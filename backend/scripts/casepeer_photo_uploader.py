#!/usr/bin/env python
"""
Production-ready Casepeer Photo Uploader Script
Uploads photos from a single zip file to a case's storage folder.
Designed to handle 2000+ cases with proper logging and analytics.
"""

import csv
import os
import re
import sys
import zipfile
from datetime import datetime
from io import BytesIO

import django

# ==================== CONFIGURATION ====================
# Update these settings for each run

# Zip file configuration
ZIP_FILE_NAME = "1189642-CASE-C95A10C1_photos.zip"  # Change this for each case
ZIP_FILE_DIRECTORY = "/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/casepeer_photos/Casepeer Photos/Photo_Data_Brumely"

# Folder configuration
CASEPEER_FOLDER_NAME = "casepeer_photos_3"  # Change this as needed

# Logging configuration
LOG_FILE_PATH = "casepeer_upload_log.csv"  # CSV file for analytics
ENABLE_DEBUG_LOGGING = True  # Set to False for production runs

# ==================== SETUP ====================

# Setup Django
sys.path.append("/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from case_management.models import Case
from storage_service.services.factory import StorageServiceFactory, StorageType

# ==================== LOGGING SETUP ====================


def setup_logging():
    """Initialize CSV logging file with headers if it doesn't exist"""
    if not os.path.exists(LOG_FILE_PATH):
        with open(LOG_FILE_PATH, "w", newline="") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(
                [
                    "timestamp",
                    "zip_filename",
                    "case_id",
                    "case_name",
                    "storage_root_folder_id",
                    "casepeer_folder_id",
                    "zip_file_count",
                    "files_uploaded",
                    "files_failed",
                    "upload_success",
                    "error_message",
                    "execution_time_seconds",
                ]
            )


def log_result(
    zip_filename,
    case_id,
    case_name,
    storage_root_folder_id,
    casepeer_folder_id,
    zip_file_count,
    files_uploaded,
    files_failed,
    upload_success,
    error_message,
    execution_time,
):
    """Log the upload result to CSV file"""
    with open(LOG_FILE_PATH, "a", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(
            [
                datetime.now().isoformat(),
                zip_filename,
                case_id,
                case_name,
                storage_root_folder_id,
                casepeer_folder_id,
                zip_file_count,
                files_uploaded,
                files_failed,
                upload_success,
                error_message,
                execution_time,
            ]
        )


def debug_print(message):
    """Print debug messages only if debug logging is enabled"""
    if ENABLE_DEBUG_LOGGING:
        print(f"[DEBUG] {message}")


# ==================== UTILITY FUNCTIONS ====================


def extract_case_id_from_zip(zip_filename):
    """Extract case ID from zip filename using regex"""
    # Pattern: 1189637-CASE-C716B7C4_photos.zip -> CASE-C716B7C4
    pattern = r"\d+-(.+?)_photos\.zip"
    match = re.search(pattern, zip_filename)
    if match:
        return match.group(1)
    else:
        raise ValueError(f"Could not extract case ID from filename: {zip_filename}")


def get_zip_file_info(zip_path):
    """Get information about files in the zip"""
    try:
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            file_list = [f for f in zip_ref.namelist() if not f.endswith("/")]
            return file_list
    except Exception as e:
        raise Exception(f"Error reading zip file {zip_path}: {str(e)}")


# ==================== MAIN UPLOAD FUNCTION ====================


def upload_casepeer_photos():
    """Main function to upload photos from zip file to case storage"""
    start_time = datetime.now()

    # Initialize variables for logging
    case_id = None
    case_name = ""
    storage_root_folder_id = ""
    casepeer_folder_id = ""
    zip_file_count = 0
    files_uploaded = 0
    files_failed = 0
    upload_success = False
    error_message = ""

    try:
        print("=== Casepeer Photo Uploader ===")
        print(f"Zip File: {ZIP_FILE_NAME}")
        print(f"Folder Name: {CASEPEER_FOLDER_NAME}")
        print(f"Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Step 1: Extract case ID from zip filename
        try:
            case_id = extract_case_id_from_zip(ZIP_FILE_NAME)
            print(f"✓ Extracted Case ID: {case_id}")
        except ValueError as e:
            error_message = str(e)
            print(f"✗ {error_message}")
            return

        # Step 2: Get the case
        try:
            case = Case.objects.get(id=case_id)
            case_name = case.name
            storage_root_folder_id = case.storage_root_folder_id or ""
            print(f"✓ Found case: {case_name}")
            debug_print(f"Storage Root: {storage_root_folder_id}")
            debug_print(f"Storage Type: {case.storage_type}")
        except Case.DoesNotExist:
            error_message = f"Case {case_id} not found"
            print(f"✗ {error_message}")
            return

        if not case.storage_root_folder_id:
            error_message = f"Case {case_id} does not have storage structure"
            print(f"✗ {error_message}")
            return

        # Step 3: Initialize storage service
        try:
            storage_service = StorageServiceFactory.get_storage_service(StorageType.S3, org_id=1)
            print("✓ Storage service initialized")
        except Exception as e:
            error_message = f"Failed to initialize storage service: {str(e)}"
            print(f"✗ {error_message}")
            return

        # Step 4: Create or find casepeer folder
        try:
            contents = storage_service.get_folder_contents(case.storage_root_folder_id)
            casepeer_folder = None

            debug_print(f"Root folder contents: {len(contents)} items")
            for item in contents:
                if item.get("name") == CASEPEER_FOLDER_NAME and "folder" in item.get("type", ""):
                    casepeer_folder = item
                    break

            if casepeer_folder:
                casepeer_folder_id = casepeer_folder["id"]
                print(f"✓ Found existing folder: {CASEPEER_FOLDER_NAME}")
            else:
                print(f"Creating folder: {CASEPEER_FOLDER_NAME}")
                folder_result = storage_service.create_folder(
                    parent_folder_id=case.storage_root_folder_id, folder_name=CASEPEER_FOLDER_NAME
                )
                casepeer_folder_id = folder_result["id"]
                print(f"✓ Created folder: {CASEPEER_FOLDER_NAME}")

            debug_print(f"Casepeer folder ID: {casepeer_folder_id}")

        except Exception as e:
            error_message = f"Error with casepeer folder: {str(e)}"
            print(f"✗ {error_message}")
            return

        # Step 5: Process zip file
        zip_path = os.path.join(ZIP_FILE_DIRECTORY, ZIP_FILE_NAME)

        if not os.path.exists(zip_path):
            error_message = f"Zip file not found: {zip_path}"
            print(f"✗ {error_message}")
            return

        try:
            file_list = get_zip_file_info(zip_path)
            zip_file_count = len(file_list)
            print(f"✓ Zip file contains {zip_file_count} files")

            if zip_file_count == 0:
                print("✓ Zip file is empty, nothing to upload")
                upload_success = True
                return

        except Exception as e:
            error_message = f"Error reading zip file: {str(e)}"
            print(f"✗ {error_message}")
            return

        # Step 6: Upload files
        print(f"Uploading {zip_file_count} files...")

        try:
            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                for i, file_path in enumerate(file_list, 1):
                    filename = os.path.basename(file_path)
                    if not filename:
                        continue

                    print(f"  [{i}/{zip_file_count}] {filename}...", end=" ")

                    try:
                        # Extract file to memory
                        file_data = zip_ref.read(file_path)
                        file_obj = BytesIO(file_data)

                        # Check file size (limit to 15MB)
                        file_size = len(file_data)
                        if file_size > 15 * 1024 * 1024:  # 15MB
                            print(f"SKIPPED (too large: {file_size / (1024 * 1024):.1f}MB)")
                            files_failed += 1
                            continue

                        # Upload to storage service
                        upload_result = storage_service.upload_file(
                            folder_id=casepeer_folder_id, file_name=filename, file_content=file_obj
                        )
                        print("✓")
                        files_uploaded += 1

                    except Exception as upload_error:
                        print(f"✗ ({str(upload_error)})")
                        files_failed += 1

                upload_success = True
                print(f"✓ Upload completed: {files_uploaded} uploaded, {files_failed} failed")

        except Exception as e:
            error_message = f"Error during upload: {str(e)}"
            print(f"✗ {error_message}")
            return

    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"
        print(f"✗ {error_message}")

    finally:
        # Calculate execution time
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # Log results
        log_result(
            ZIP_FILE_NAME,
            case_id,
            case_name,
            storage_root_folder_id,
            casepeer_folder_id,
            zip_file_count,
            files_uploaded,
            files_failed,
            upload_success,
            error_message,
            execution_time,
        )

        print(f"Completed in {execution_time:.2f} seconds")
        print(f"Results logged to: {LOG_FILE_PATH}")


# ==================== MAIN EXECUTION ====================

if __name__ == "__main__":
    print("Starting Casepeer Photo Uploader...")

    # Setup logging
    setup_logging()

    # Run the upload
    upload_casepeer_photos()
