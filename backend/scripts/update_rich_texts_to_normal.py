#!/usr/bin/env python
"""
Script to process rich text fields and convert them to plain text using BeautifulSoup.

This script processes the following models and fields:
1. CaseNote.content (case_management/models.py)
2. CaseIncidentDetails.incident_description (case_management/v2/models.py)
3. CaseIncidentDetails.critical_conditions (case_management/v2/models.py)
4. TwilioMessage.body (users/models.py)
5. Employer.description (case_management/v2/models.py)
6. MedicalTreatment fields (case_management/v2/models.py)

Usage:
    python scripts/update_rich_texts_to_normal.py --all --organization-id 1
    python scripts/update_rich_texts_to_normal.py --model CaseNote --organization-id 1
    python scripts/update_rich_texts_to_normal.py --dry-run --all --organization-id 1
"""

import argparse
import json
import os
import re
import sys

import django
from bs4 import BeautifulSoup
from tqdm import tqdm

# Add the parent directory to sys.path to allow imports from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Use the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from case_management.models import CaseNote
from case_management.v2.models import CaseIncidentDetails, Employer, EmployerWorkersCompensation
from users.models import TwilioMessage


def extract_text_from_html(html_content):
    """
    Extract plain text from HTML content using BeautifulSoup.

    Args:
        html_content (str): HTML content to process

    Returns:
        str: Plain text extracted from HTML
    """
    if not html_content:
        return ""

    # Check if content is already plain text
    if not re.search(r"<[^>]+>", html_content):
        return html_content

    # Process with BeautifulSoup
    try:
        soup = BeautifulSoup(html_content, "html.parser")
        return soup.get_text(separator=" ")
    except Exception as e:
        print(f"Error processing HTML content: {e}")
        return html_content


def extract_text_from_json(json_content):
    """
    Extract text from JSON structured content (like Slate editor format).

    Args:
        json_content (str): JSON content to process

    Returns:
        str: Plain text extracted from JSON
    """
    if not json_content:
        return ""

    try:
        # Try to parse as JSON
        data = json.loads(json_content)

        # Handle Slate editor format (array of paragraph objects)
        if isinstance(data, list):
            text_parts = []

            for item in data:
                if isinstance(item, dict) and "children" in item:
                    paragraph_text = []
                    for child in item.get("children", []):
                        if isinstance(child, dict) and "text" in child:
                            paragraph_text.append(child["text"])
                    text_parts.append(" ".join(paragraph_text))

            return "\n".join(text_parts)

        # If it's not in the expected format, convert to string and extract text
        return extract_text_from_html(str(data))

    except json.JSONDecodeError:
        # If it's not valid JSON, treat as HTML
        return extract_text_from_html(json_content)


def process_content(content):
    """
    Process content that could be HTML, JSON, or plain text.

    Args:
        content (str): Content to process

    Returns:
        str: Plain text extracted from content
    """
    if not content:
        return ""

    # Try to process as JSON first
    try:
        json.loads(content)
        return extract_text_from_json(content) or ""
    except (json.JSONDecodeError, TypeError):
        # If not valid JSON, process as HTML
        return extract_text_from_html(content) or ""


def process_case_notes(dry_run=False, organization_id=None):
    """Process CaseNote.content field"""
    notes = CaseNote.objects.filter(case__organization__id=organization_id)
    updated_count = 0

    print(f"Processing {notes.count()} CaseNote records...")

    for note in tqdm(notes):
        if not note.content:
            continue

        original_content = note.content or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if (not dry_run) and (processed_content) and len(str(processed_content).replace(" ", "")) > 2:
                note.content = processed_content
                note.save(update_fields=["content"])
                updated_count += 1
            else:
                updated_count += 1

    return updated_count


def process_incident_details(dry_run=False, organization_id=None):
    """Process CaseIncidentDetails incident_description and critical_conditions fields"""
    details = CaseIncidentDetails.objects.filter(case__organization__id=organization_id)
    updated_count = 0

    print(f"Processing {details.count()} CaseIncidentDetails records...")

    for detail in tqdm(details):
        updated = False

        # Process incident_description
        if detail.incident_description:
            original_content = detail.incident_description
            processed_content = process_content(original_content)

            if processed_content != original_content:
                if (not dry_run) and (processed_content) and len(str(processed_content).replace(" ", "")) > 2:
                    detail.incident_description = processed_content
                    updated = True
                updated_count += 1

        # Process critical_conditions
        if detail.critical_conditions:
            original_content = detail.critical_conditions
            processed_content = process_content(original_content)

            if processed_content != original_content:
                if not dry_run:
                    detail.critical_conditions = processed_content
                    updated = True
                updated_count += 1

        if updated and not dry_run:
            detail.save(update_fields=["incident_description", "critical_conditions"])

    return updated_count


def process_twilio_messages(dry_run=False, organization_id=None):
    """Process TwilioMessage.body field"""
    messages = TwilioMessage.objects.filter(organization__id=organization_id)
    updated_count = 0

    print(f"Processing {messages.count()} TwilioMessage records...")

    for message in tqdm(messages):
        if not message.body:
            continue

        original_content = message.body
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if (not dry_run) and (processed_content) and len(str(processed_content).replace(" ", "")) > 2:
                message.body = processed_content
                message.save(update_fields=["body"])
                updated_count += 1
            else:
                updated_count += 1

    return updated_count


def process_employer_descriptions(dry_run=False, organization_id=None):
    """Process Employer.description field"""
    employers = Employer.objects.filter(case__organization__id=organization_id)
    updated_count = 0

    print(f"Processing {employers.count()} Employer records...")

    for employer in tqdm(employers):
        if not employer.description:
            continue

        original_content = employer.description
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if (not dry_run) and (processed_content) and len(str(processed_content).replace(" ", "")) > 2:
                employer.description = processed_content
                employer.save(update_fields=["description"])
                updated_count += 1
            else:
                updated_count += 1

    return updated_count


def process_employement_note(dry_run=False, organization_id=None):
    """Process Employer.description field"""
    employers = Employer.objects.filter(case__organization__id=organization_id)
    updated_count = 0

    print(f"Processing {employers.count()} Employment Note records...")

    for employer in tqdm(employers):
        workers_compensation = EmployerWorkersCompensation.objects.filter(employer=employer).first()
        if not workers_compensation:
            continue

        original_content = workers_compensation.note
        processed_content = process_content(original_content)

        print(f"{original_content} \n\n->\n\n {processed_content}")

        if processed_content != original_content:
            if (not dry_run) and (processed_content) and len(str(processed_content).replace(" ", "")) > 2:
                workers_compensation.note = processed_content
                workers_compensation.save(update_fields=["note"])
                updated_count += 1
            else:
                updated_count += 1

    return updated_count


def main():
    parser = argparse.ArgumentParser(description="Process rich text fields to plain text")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--all", action="store_true", help="Process all models")
    group.add_argument(
        "--model",
        choices=["CaseNote", "CaseIncidentDetails", "TwilioMessage", "Employer"],
        help="Process specific model",
    )
    parser.add_argument("--dry-run", action="store_true", help="Dry run (don't save changes)")
    parser.add_argument("--organization_id", type=int, required=True, help="Organization ID to process")

    args = parser.parse_args()

    if args.dry_run:
        print("DRY RUN MODE - No changes will be saved")

    total_updated = 0
    organization_id = args.organization_id

    # if args.all or args.model == "CaseNote":
    #     updated = process_case_notes(dry_run=args.dry_run, organization_id=organization_id)
    #     print(f"{'Would update' if args.dry_run else 'Updated'} {updated} CaseNote records")
    #     total_updated += updated

    # if args.all or args.model == "CaseIncidentDetails":
    #     updated = process_incident_details(dry_run=args.dry_run, organization_id=organization_id)
    #     print(f"{'Would update' if args.dry_run else 'Updated'} {updated} CaseIncidentDetails records")
    #     total_updated += updated

    # if args.all or args.model == "TwilioMessage":
    #     updated = process_twilio_messages(dry_run=args.dry_run, organization_id=organization_id)
    #     print(f"{'Would update' if args.dry_run else 'Updated'} {updated} TwilioMessage records")
    #     total_updated += updated

    # if args.all or args.model == "Employer":
    #     updated = process_employer_descriptions(dry_run=args.dry_run, organization_id=organization_id)
    #     print(f"{'Would update' if args.dry_run else 'Updated'} {updated} Employer records")
    #     total_updated += updated

    if args.all or args.model == "EmploymentNote":
        updated = process_employement_note(dry_run=args.dry_run, organization_id=organization_id)
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} EmploymentNote records")
        total_updated += updated

    print(f"\nTotal: {'Would update' if args.dry_run else 'Updated'} {total_updated} records")


if __name__ == "__main__":
    main()
