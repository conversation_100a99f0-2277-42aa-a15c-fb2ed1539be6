#!/usr/bin/env python
"""
<PERSON>ript to process rich text fields and convert them to CKEditor-compatible HTML for Client and Defendant related models.

This script:
1. Converts JSON-formatted rich text content (from Slate editor) to plain text with <br/> tags
2. Converts existing \n characters to <br/> tags for CKEditor compatibility
3. Processes the following models and fields:

Client related:
1. CaseNote.content (Index Details: Notes)
2. ClientInsurance.claim_note (Insurance Company: Claim Notes)
3. ClientPropertyDamage.note (Property Damage: Notes)
4. LawFirmContact.note (Law Firm: Notes)
5. UserCard.content (User Cards: Content)

Defendant related:
1. CaseDefendant.description (Personal Information: Description)
2. DefendantInsurance.claim_note (Insurance Company: Claim Notes)
3. DefendantPropertyDamage.note (Property Damage: Notes)
4. LawFirmContact.note (Law Firm: Notes - same as client)

Usage:
    python scripts/update_rich_texts_to_normal_v2.py --all --organization-id 12
    python scripts/update_rich_texts_to_normal_v2.py --model CaseNote --organization-id 12
    python scripts/update_rich_texts_to_normal_v2.py --dry-run --all --organization-id 12
    python scripts/update_rich_texts_to_normal_v2.py --test-case-id CASE-EB272858 --organization-id 12
"""

import argparse
import json
import os
import re
import sys

import django
from bs4 import BeautifulSoup
from tqdm import tqdm

# Add the parent directory to sys.path to allow imports from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Use the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from case_management.cards.models import UserCard
from case_management.models import CaseNote
from case_management.v2.models import (
    CaseDefendant,
    ClientInsurance,
    ClientPropertyDamage,
    DefendantInsurance,
    DefendantPropertyDamage,
    LawFirmContact,
)


def extract_text_from_html(html_content):
    """
    Extract plain text from HTML content using BeautifulSoup.

    Args:
        html_content (str): HTML content to process

    Returns:
        str: Plain text extracted from HTML
    """
    if not html_content:
        return ""

    # Check if content is already plain text
    if not re.search(r"<[^>]+>", html_content):
        return html_content

    # Process with BeautifulSoup
    try:
        soup = BeautifulSoup(html_content, "html.parser")
        return soup.get_text(separator=" ")
    except Exception as e:
        print(f"Error processing HTML content: {e}")
        return html_content


def extract_text_from_json(json_content):
    """
    Extract text from JSON structured content (like Slate editor format).

    Args:
        json_content (str): JSON content to process

    Returns:
        str: Plain text extracted from JSON
    """
    if not json_content:
        return ""

    try:
        # Try to parse as JSON
        data = json.loads(json_content)

        # Handle Slate editor format (array of paragraph objects)
        if isinstance(data, list):
            text_parts = []

            for item in data:
                if isinstance(item, dict) and "children" in item:
                    paragraph_text = []
                    for child in item.get("children", []):
                        if isinstance(child, dict) and "text" in child:
                            paragraph_text.append(child["text"])
                    text_parts.append(" ".join(paragraph_text))

            return "<br/>".join(text_parts)

        # If it's not in the expected format, convert to string and extract text
        return extract_text_from_html(str(data))

    except json.JSONDecodeError:
        # If it's not valid JSON, treat as HTML
        return extract_text_from_html(json_content)


def process_content(content):
    """
    Process content that could be HTML, JSON, or plain text.
    Also converts existing \n characters to <br/> tags for CKEditor compatibility.

    Args:
        content (str): Content to process

    Returns:
        str: Plain text extracted from content with <br/> tags
    """
    if not content:
        return ""

    # Try to process as JSON first
    try:
        json.loads(content)
        processed = extract_text_from_json(content) or ""
    except (json.JSONDecodeError, TypeError):
        # If not valid JSON, process as HTML or treat as plain text
        processed = extract_text_from_html(content) or content

    # Convert any existing \n characters to <br/> tags for CKEditor compatibility
    if processed and "\n" in processed:
        processed = processed.replace("\n", "<br/>")

    return processed


def process_case_notes(dry_run=False, organization_id=None, test_case_id=None):
    """Process CaseNote.content field"""
    notes = CaseNote.objects.filter(case__organization__id=organization_id)

    if test_case_id:
        notes = notes.filter(case__id=test_case_id)

    updated_count = 0

    print(f"Processing {notes.count()} CaseNote records...")

    for note in tqdm(notes):
        if not note.content:
            continue

        original_content = note.content or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"CaseNote ID {note.id} - Case: {note.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                note.content = processed_content
                note.save(update_fields=["content"])
                updated_count += 1

    return updated_count


def process_client_insurance_claim_notes(dry_run=False, organization_id=None, test_case_id=None):
    """Process ClientInsurance.claim_note field"""
    insurances = ClientInsurance.objects.filter(case__organization__id=organization_id)

    if test_case_id:
        insurances = insurances.filter(case__id=test_case_id)

    updated_count = 0

    print(f"Processing {insurances.count()} ClientInsurance records...")

    for insurance in tqdm(insurances):
        if not insurance.claim_note:
            continue

        original_content = insurance.claim_note or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"ClientInsurance ID {insurance.id} - Case: {insurance.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                insurance.claim_note = processed_content
                insurance.save(update_fields=["claim_note"])
                updated_count += 1

    return updated_count


def process_client_property_damage_notes(dry_run=False, organization_id=None, test_case_id=None):
    """Process ClientPropertyDamage.note field"""
    property_damages = ClientPropertyDamage.objects.filter(case__organization__id=organization_id)

    if test_case_id:
        property_damages = property_damages.filter(case__id=test_case_id)

    updated_count = 0

    print(f"Processing {property_damages.count()} ClientPropertyDamage records...")

    for prop_damage in tqdm(property_damages):
        if not prop_damage.note:
            continue

        original_content = prop_damage.note or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"ClientPropertyDamage ID {prop_damage.id} - Case: {prop_damage.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                prop_damage.note = processed_content
                prop_damage.save(update_fields=["note"])
                updated_count += 1

    return updated_count


def process_defendant_descriptions(dry_run=False, organization_id=None, test_case_id=None):
    """Process CaseDefendant.description field"""
    defendants = CaseDefendant.objects.filter(case__organization__id=organization_id)

    if test_case_id:
        defendants = defendants.filter(case__id=test_case_id)

    updated_count = 0

    print(f"Processing {defendants.count()} CaseDefendant records...")

    for defendant in tqdm(defendants):
        if not defendant.description:
            continue

        original_content = defendant.description or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"CaseDefendant ID {defendant.id} - Case: {defendant.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                defendant.description = processed_content
                defendant.save(update_fields=["description"])
                updated_count += 1

    return updated_count


def process_defendant_insurance_claim_notes(dry_run=False, organization_id=None, test_case_id=None):
    """Process DefendantInsurance.claim_note field"""
    insurances = DefendantInsurance.objects.filter(defendant__case__organization__id=organization_id)

    if test_case_id:
        insurances = insurances.filter(defendant__case__id=test_case_id)

    updated_count = 0

    print(f"Processing {insurances.count()} DefendantInsurance records...")

    for insurance in tqdm(insurances):
        if not insurance.claim_note:
            continue

        original_content = insurance.claim_note or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"DefendantInsurance ID {insurance.id} - Case: {insurance.defendant.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                insurance.claim_note = processed_content
                insurance.save(update_fields=["claim_note"])
                updated_count += 1

    return updated_count


def process_defendant_property_damage_notes(dry_run=False, organization_id=None, test_case_id=None):
    """Process DefendantPropertyDamage.note field"""
    property_damages = DefendantPropertyDamage.objects.filter(defendant__case__organization__id=organization_id)

    if test_case_id:
        property_damages = property_damages.filter(defendant__case__id=test_case_id)

    updated_count = 0

    print(f"Processing {property_damages.count()} DefendantPropertyDamage records...")

    for prop_damage in tqdm(property_damages):
        if not prop_damage.note:
            continue

        original_content = prop_damage.note or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"DefendantPropertyDamage ID {prop_damage.id} - Case: {prop_damage.defendant.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                prop_damage.note = processed_content
                prop_damage.save(update_fields=["note"])
                updated_count += 1

    return updated_count


def process_law_firm_notes(dry_run=False, organization_id=None, test_case_id=None):
    """Process LawFirmContact.note field"""
    law_firms = LawFirmContact.objects.filter(organization__id=organization_id)

    # For test case, we can't directly filter by case since LawFirmContact is organization-level
    # But we can still process all law firms for the organization
    updated_count = 0

    print(f"Processing {law_firms.count()} LawFirmContact records...")

    for law_firm in tqdm(law_firms):
        if not law_firm.note:
            continue

        original_content = law_firm.note or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"LawFirmContact ID {law_firm.id} - Organization: {law_firm.organization.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                law_firm.note = processed_content
                law_firm.save(update_fields=["note"])
                updated_count += 1

    return updated_count


def process_user_cards(dry_run=False, organization_id=None, test_case_id=None):
    """Process UserCard.content field"""
    user_cards = UserCard.objects.filter(case__organization__id=organization_id)

    if test_case_id:
        user_cards = user_cards.filter(case__id=test_case_id)

    updated_count = 0

    print(f"Processing {user_cards.count()} UserCard records...")

    for user_card in tqdm(user_cards):
        if not user_card.content:
            continue

        original_content = user_card.content or ""
        processed_content = process_content(original_content)

        if processed_content != original_content:
            if dry_run:
                print(f"UserCard ID {user_card.id} - Case: {user_card.case.id}")
                print(f"Original: {original_content[:100]}...")
                print(f"Processed: {processed_content[:100]}...")
                print("-" * 50)
                updated_count += 1
            elif processed_content and len(str(processed_content).replace(" ", "")) > 2:
                user_card.content = processed_content
                user_card.save(update_fields=["content"])
                updated_count += 1

    return updated_count


def test_content_processing():
    """Test the content processing with sample data"""
    # Test 1: JSON rich text content
    test_content_json = '[{"type":"paragraph","children":[{"text":"ALPHA LAW //1P: NO INSURANCE// 3P: 100/300 "}]},{"type":"paragraph","children":[{"text":""}]},{"type":"paragraph","children":[{"text":"Client was going to work early in the morning and it was very icy out, clients car started to slide and his door came open, he then put his hazards on and pulled over to the shoulder in the carpool lane. He was at a full stop and the defendant rear ended him. He made no effort to slow down before colliding with our client."}]}]'

    print("Testing JSON rich text content processing...")
    print(f"Original content: {test_content_json}")
    print("-" * 50)

    processed_content_json = process_content(test_content_json)
    print(f"Processed content: {processed_content_json}")
    print("-" * 50)

    expected_result_json = "ALPHA LAW //1P: NO INSURANCE// 3P: 100/300 <br/><br/>Client was going to work early in the morning and it was very icy out, clients car started to slide and his door came open, he then put his hazards on and pulled over to the shoulder in the carpool lane. He was at a full stop and the defendant rear ended him. He made no effort to slow down before colliding with our client."

    print(f"Expected result: {expected_result_json}")
    print("-" * 50)

    if processed_content_json.strip() == expected_result_json.strip():
        print("✅ Test 1 PASSED: JSON content processing works correctly!")
    else:
        print("❌ Test 1 FAILED: JSON content processing did not produce expected result")
        print(f"Length difference: {len(processed_content_json)} vs {len(expected_result_json)}")

    print("\n" + "=" * 80 + "\n")

    # Test 2: Plain text with \n characters (already processed content)
    test_content_newlines = "ALPHA LAW //1P: NO INSURANCE// 3P: 100/300\n\nClient was going to work early in the morning and it was very icy out, clients car started to slide and his door came open, he then put his hazards on and pulled over to the shoulder in the carpool lane. He was at a full stop and the defendant rear ended him. He made no effort to slow down before colliding with our client."

    print("Testing plain text with \\n characters processing...")
    print(f"Original content: {test_content_newlines}")
    print("-" * 50)

    processed_content_newlines = process_content(test_content_newlines)
    print(f"Processed content: {processed_content_newlines}")
    print("-" * 50)

    expected_result_newlines = "ALPHA LAW //1P: NO INSURANCE// 3P: 100/300<br/><br/>Client was going to work early in the morning and it was very icy out, clients car started to slide and his door came open, he then put his hazards on and pulled over to the shoulder in the carpool lane. He was at a full stop and the defendant rear ended him. He made no effort to slow down before colliding with our client."

    print(f"Expected result: {expected_result_newlines}")
    print("-" * 50)

    if processed_content_newlines.strip() == expected_result_newlines.strip():
        print("✅ Test 2 PASSED: Newline conversion works correctly!")
    else:
        print("❌ Test 2 FAILED: Newline conversion did not produce expected result")
        print(f"Length difference: {len(processed_content_newlines)} vs {len(expected_result_newlines)}")

    print("\n" + "=" * 80 + "\n")
    print("Both tests completed!")


def main():
    parser = argparse.ArgumentParser(
        description="Process rich text fields to plain text for Client and Defendant models"
    )
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--all", action="store_true", help="Process all models")
    group.add_argument(
        "--model",
        choices=[
            "CaseNote",
            "ClientInsurance",
            "ClientPropertyDamage",
            "CaseDefendant",
            "DefendantInsurance",
            "DefendantPropertyDamage",
            "LawFirmContact",
            "UserCard",
            "Test",
        ],
        help="Process specific model",
    )
    parser.add_argument("--dry-run", action="store_true", help="Dry run (don't save changes)")
    parser.add_argument("--organization-id", type=int, required=True, help="Organization ID to process")
    parser.add_argument("--test-case-id", type=str, help="Test with specific case ID (e.g., CASE-EB272858)")

    args = parser.parse_args()

    if args.model == "Test":
        test_content_processing()
        return

    if args.dry_run:
        print("DRY RUN MODE - No changes will be saved")

    total_updated = 0
    organization_id = args.organization_id
    test_case_id = args.test_case_id

    if test_case_id:
        print(f"Testing with case ID: {test_case_id}")

    if args.all or args.model == "CaseNote":
        updated = process_case_notes(dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id)
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} CaseNote records")
        total_updated += updated

    if args.all or args.model == "ClientInsurance":
        updated = process_client_insurance_claim_notes(
            dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id
        )
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} ClientInsurance records")
        total_updated += updated

    if args.all or args.model == "ClientPropertyDamage":
        updated = process_client_property_damage_notes(
            dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id
        )
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} ClientPropertyDamage records")
        total_updated += updated

    if args.all or args.model == "CaseDefendant":
        updated = process_defendant_descriptions(
            dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id
        )
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} CaseDefendant records")
        total_updated += updated

    if args.all or args.model == "DefendantInsurance":
        updated = process_defendant_insurance_claim_notes(
            dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id
        )
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} DefendantInsurance records")
        total_updated += updated

    if args.all or args.model == "DefendantPropertyDamage":
        updated = process_defendant_property_damage_notes(
            dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id
        )
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} DefendantPropertyDamage records")
        total_updated += updated

    if args.all or args.model == "LawFirmContact":
        updated = process_law_firm_notes(
            dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id
        )
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} LawFirmContact records")
        total_updated += updated

    if args.all or args.model == "UserCard":
        updated = process_user_cards(dry_run=args.dry_run, organization_id=organization_id, test_case_id=test_case_id)
        print(f"{'Would update' if args.dry_run else 'Updated'} {updated} UserCard records")
        total_updated += updated

    print(f"\nTotal: {'Would update' if args.dry_run else 'Updated'} {total_updated} records")


if __name__ == "__main__":
    main()
