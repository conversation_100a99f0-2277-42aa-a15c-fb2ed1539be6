#!/usr/bin/env python3
import logging
import os
import sys
from pathlib import Path

import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(), logging.FileHandler("upload_templates.log")],
)
logger = logging.getLogger(__name__)

# Global variables
AUTH_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._Y0olPh2yBihPtSSuKsZldN9Kw0ZqRLbuwF-orHzxoc"  # Replace with your actual auth token
API_URL = "https://main-backend.alphalaw.io/api/v1/organization-management/templates/"
TEMPLATE_DIR = "/Users/<USER>/Desktop/Work/Others/Foresight-Labs/templates/Templates"


# # Global variables
# AUTH_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gc7yyslbojJBPdWGQm6BXABtotljH-1pvPBHxXw_Bg4"  # Replace with your actual auth token
# API_URL = "http://127.0.0.1:8080/api/v1/organization-management/templates/"

# Mapping of folder names to context_types
FOLDER_CONTEXT_MAPPING = {
    "Client": "client",
    "Client Insurance": "client_insurance",
    "Health Insurance": "client_health_insurance",
    "Defendant": "defendant",
    "Defendant Insurance": "defendant_insurance",
    "Health Provider": "health_provider",
    "Other Party": "other_party",
    "Witness": "witness",
    "Litigation": "litigation",
}


def upload_template(file_path, context_type="client"):
    """Upload a template file to the API endpoint."""
    file_name = os.path.basename(file_path)
    template_name = os.path.splitext(file_name)[0]  # Remove extension

    # template_name might be of the format Lien_Reduction_letter_cje4EFd, remove underscores and capitalize the first letter of each word
    template_name = " ".join(word.capitalize() for word in template_name.split("_"))

    # Prepare the multipart form data
    files = {
        "file": (
            file_name,
            open(file_path, "rb"),
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
    }
    data = {
        "name": template_name,
        "description": "",
        "context_type": context_type,
        "use_in_case": "true",
        "use_in_lead": "false",
    }

    headers = {"Authorization": AUTH_TOKEN}

    try:
        response = requests.post(API_URL, headers=headers, files=files, data=data)
        if response.status_code == 201 or response.status_code == 200:
            logger.info(f"Successfully uploaded: {file_name} with context_type: {context_type}")
            return True
        else:
            logger.error(f"Failed to upload {file_name}. Response: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        logger.error(f"Exception when uploading {file_name}: {str(e)}")
        return False
    finally:
        # Close the file
        files["file"][1].close()


def find_and_upload_templates(template_dir):
    """Recursively find and upload all .docx files in the template directory."""
    template_dir = Path(template_dir)

    # Count for statistics
    total_files = 0
    successful_uploads = 0
    failed_uploads = 0

    # Walk through all subdirectories
    for root, _, files in os.walk(template_dir):
        # Get the relative folder path from the template_dir
        rel_path = os.path.relpath(root, template_dir)
        if rel_path == ".":
            # Skip the root directory itself
            continue

        # Determine the context_type based on the folder name
        folder_name = rel_path.split(os.sep)[0]  # Get the top-level folder name
        context_type = FOLDER_CONTEXT_MAPPING.get(folder_name, "client")  # Default to "client"

        # Process all .docx files in this directory
        for file in files:
            if file.endswith(".docx"):
                total_files += 1
                file_path = os.path.join(root, file)

                logger.info(f"Uploading {file} with context_type: {context_type}")

                result = upload_template(
                    file_path,
                    context_type=context_type,
                )

                if result:
                    successful_uploads += 1
                else:
                    failed_uploads += 1

    # Print summary
    logger.info("Upload Summary:")
    logger.info(f"  Total files processed: {total_files}")
    logger.info(f"  Successfully uploaded: {successful_uploads}")
    logger.info(f"  Failed uploads: {failed_uploads}")


def main():
    # Check if template directory exists
    if not os.path.isdir(TEMPLATE_DIR):
        logger.error(f"Template directory does not exist: {TEMPLATE_DIR}")
        sys.exit(1)

    logger.info(f"Starting template upload from directory: {TEMPLATE_DIR}")
    find_and_upload_templates(TEMPLATE_DIR)
    logger.info("Upload process completed.")


if __name__ == "__main__":
    main()
