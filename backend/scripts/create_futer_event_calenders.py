#!/usr/bin/env python
import logging
import os
import sys
import time
from datetime import datetime
from zoneinfo import ZoneInfo

import django
from bs4 import BeautifulSoup

# Add the parent directory to sys.path to allow imports from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Use the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

# Import necessary models
from case_management.v2.models import CaseCourtDetails, CaseEvent, CaseWorkers
from users.models import NylasIntegration, User
from users.nylas_integration.config import nylas_client

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("create_future_event_calendars.log"),
    ],
)
logger = logging.getLogger(__name__)


def get_case_workers_for_calendar(case):
    """
    Get the relevant case workers for calendar events.
    Returns a list of User objects for the specified roles.
    """
    workers = []
    try:
        case_workers = CaseWorkers.objects.get(case=case)

        # Add the specified roles if they exist
        if case_workers.primary_contact:
            workers.append(case_workers.primary_contact)

        if case_workers.lead_attorney:
            workers.append(case_workers.lead_attorney)

        if case_workers.supervising_attorney:
            workers.append(case_workers.supervising_attorney)

        if case_workers.litigation_attorney:
            workers.append(case_workers.litigation_attorney)

        if case_workers.litigation_assistant:
            workers.append(case_workers.litigation_assistant)

    except CaseWorkers.DoesNotExist:
        logger.warning(f"No case workers found for case {case.id}")

    return workers


def get_event_title_for_case_event(case_event):
    # Format incident date if it exists
    incident_date_str = ""
    if (
        hasattr(case_event.case, "incident_details")
        and case_event.case.incident_details
        and case_event.case.incident_details.incident_date
    ):
        incident_date = case_event.case.incident_details.incident_date
        incident_date_str = incident_date.strftime("%m/%d/%Y")

    court_details = CaseCourtDetails.objects.get(case=case_event.case)
    if court_details and hasattr(court_details, "case_name"):
        title = f"{case_event.event_type} - {court_details.case_name}"
    else:
        title = f"{case_event.event_type} : {case_event.case.client_basic_details.first_name}, {case_event.case.client_basic_details.last_name} - {incident_date_str}"

    return title


def create_calendar_event(case_event, integration, workers):
    """
    Create a calendar event for a case event using Nylas API.

    Args:
        case_event: CaseEvent object
        integration: NylasIntegration object
        workers: List of User objects

    Returns:
        Created event data or None if failed
    """
    try:
        # Default to primary calendar
        calendar_id = "primary"

        # Convert date and times to datetime objects
        date_str = str(case_event.date)
        start_time_str = str(case_event.start_time) if case_event.start_time else "09:00:00"
        end_time_str = str(case_event.end_time) if case_event.end_time else "10:00:00"

        # Default timezone
        tz = "America/Los_Angeles"

        # Parse datetime strings
        try:
            start_datetime = datetime.strptime(f"{date_str} {start_time_str}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                start_datetime = datetime.strptime(f"{date_str} {start_time_str}", "%Y-%m-%d %H:%M")
            except ValueError:
                start_datetime = datetime.strptime(f"{date_str} {start_time_str}", "%Y-%m-%d %H")

        try:
            end_datetime = datetime.strptime(f"{date_str} {end_time_str}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                end_datetime = datetime.strptime(f"{date_str} {end_time_str}", "%Y-%m-%d %H:%M")
            except ValueError:
                end_datetime = datetime.strptime(f"{date_str} {end_time_str}", "%Y-%m-%d %H")

        # Make timezone aware
        start_aware = datetime.combine(case_event.date, start_datetime.time()).replace(tzinfo=ZoneInfo(tz))
        end_aware = datetime.combine(case_event.date, end_datetime.time()).replace(tzinfo=ZoneInfo(tz))

        # Convert to timestamps
        start_time_timestamp = int(start_aware.timestamp())
        end_time_timestamp = int(end_aware.timestamp())

        # Create participants list from workers
        participants = [
            {
                "email": worker.email,
                "name": f"{worker.first_name} {worker.last_name}".strip(),
                "status": "noreply",  # Default status for new invites
            }
            for worker in workers
        ]

        # Add event creator as a participant with "yes" status
        participants.append(
            {
                "email": integration.email,
                "name": integration.user.get_full_name() or integration.user.email,
                "status": "yes",
            }
        )

        # Create event using Nylas API
        title = get_event_title_for_case_event(case_event)
        formatted_description = BeautifulSoup(case_event.description or "", "html.parser").get_text(separator=" ")
        event = nylas_client.events.create(
            integration.grant_id,  # First positional argument
            query_params={"calendar_id": calendar_id},
            request_body={
                "calendar_id": calendar_id,
                "when": {
                    "start_time": start_time_timestamp,
                    "end_time": end_time_timestamp,
                },
                "title": title,
                "description": formatted_description,
                "location": case_event.address or "",
                "participants": participants,
            },
        ).data

        return event

    except Exception as e:
        logger.error(f"Error creating calendar event: {str(e)}")
        return None


def update_calendar_event(case_event, integration, workers):
    """
    Update a calendar event for a case event using Nylas API.

    Args:
        case_event: CaseEvent object
        integration: NylasIntegration object
        workers: List of User objects

    Returns:
        Updated event data or None if failed
    """
    try:
        # Default to primary calendar
        calendar_id = "primary"

        # Convert date and times to datetime objects
        date_str = str(case_event.date)
        start_time_str = str(case_event.start_time) if case_event.start_time else "09:00:00"
        end_time_str = str(case_event.end_time) if case_event.end_time else "10:00:00"

        # Default timezone
        tz = "America/Los_Angeles"

        # Parse datetime strings
        try:
            start_datetime = datetime.strptime(f"{date_str} {start_time_str}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                start_datetime = datetime.strptime(f"{date_str} {start_time_str}", "%Y-%m-%d %H:%M")
            except ValueError:
                start_datetime = datetime.strptime(f"{date_str} {start_time_str}", "%Y-%m-%d %H")

        try:
            end_datetime = datetime.strptime(f"{date_str} {end_time_str}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                end_datetime = datetime.strptime(f"{date_str} {end_time_str}", "%Y-%m-%d %H:%M")
            except ValueError:
                end_datetime = datetime.strptime(f"{date_str} {end_time_str}", "%Y-%m-%d %H")

        # Make timezone aware
        start_aware = datetime.combine(case_event.date, start_datetime.time()).replace(tzinfo=ZoneInfo(tz))
        end_aware = datetime.combine(case_event.date, end_datetime.time()).replace(tzinfo=ZoneInfo(tz))

        # Convert to timestamps
        start_time_timestamp = int(start_aware.timestamp())
        end_time_timestamp = int(end_aware.timestamp())

        # Create participants list from workers
        participants = [
            {
                "email": worker.email,
                "name": f"{worker.first_name} {worker.last_name}".strip(),
                "status": "noreply",  # Default status for new invites
            }
            for worker in workers
        ]

        # Add event creator as a participant with "yes" status
        participants.append(
            {
                "email": integration.email,
                "name": integration.user.get_full_name() or integration.user.email,
                "status": "yes",
            }
        )

        # Create event using Nylas API
        title = get_event_title_for_case_event(case_event)
        formatted_description = BeautifulSoup(case_event.description or "", "html.parser").get_text(separator=" ")
        event = nylas_client.events.update(
            integration.grant_id,
            case_event.current_calendar_event_id,  # First positional argument
            query_params={"calendar_id": calendar_id},
            request_body={
                "calendar_id": calendar_id,
                "when": {
                    "start_time": start_time_timestamp,
                    "end_time": end_time_timestamp,
                },
                "title": title,
                "description": formatted_description or "",
                "location": case_event.address or "",
                "participants": participants,
            },
        ).data

        return event

    except Exception as e:
        logger.error(f"Error creating calendar event: {str(e)}")
        return None


def update_case_event_with_calendar_details(case_event, calendar_event, integration):
    """
    Update the case event with calendar event details.

    Args:
        case_event: CaseEvent object
        calendar_event: Calendar event data from Nylas
        integration: NylasIntegration object
    """
    try:
        # Update case event with calendar tracking details
        # Initialize or update previous_calendar_events
        if case_event.previous_calendar_events is None:
            case_event.previous_calendar_events = []
        else:
            case_event.previous_calendar_events.append(case_event.current_calendar_event_id)

        case_event.current_calendar_event_id = calendar_event.id
        case_event.calendar_id = "primary"  # Default to primary calendar
        case_event.nylas_grant_id = integration.grant_id

        case_event.save()

        logger.info(f"Updated case event {case_event.id} with calendar event {calendar_event.id}")

    except Exception as e:
        logger.error(f"Error updating case event with calendar details: {str(e)}")


def process_future_events(
    organization_id,
    dry_run=False,
):
    """
    Process all future case events that don't have calendar events.

    Args:
        admin_user_email: Email of admin user to use for creating calendar events
        dry_run: If True, don't actually create events
    """
    # Get current date
    # Get current date or use manual input if provided
    manual_date = ""
    if manual_date:
        try:
            today = datetime.strptime(manual_date, "%m %d %y").date()
        except ValueError:
            logger.error(f"Invalid manual date format: {manual_date}. Using current date.")
            today = datetime.now().date()
    else:
        today = datetime.now().date()

    # Find all future case events without calendar events
    future_events = CaseEvent.objects.filter(case__organization__id=organization_id, date__gte=today).select_related(
        "case"
    )

    # case_ids = ["CASE-79AB5C42"]
    # future_events = future_events.filter(case__id__in=case_ids)

    logger.info(f"Found {future_events.count()} future events without calendar events")

    if dry_run:
        logger.info("DRY RUN MODE - No events will be created")
        try:
            # count dict ingtegrations founds, all events, not found
            count_integration_dict = {
                "integration_found": 0,
                "no_integration_found": 0,
                "total_events": future_events.count(),
            }
            for case_event in future_events:
                # Get first available case worker with Nylas integration based on priority:
                # 1. Litigation Attorney
                # 2. Lead Attorney
                # 3. Supervising Attorney
                # 4. Primary Contact
                case_workers = CaseWorkers.objects.get(case=case_event.case)
                priority_workers = [
                    case_workers.litigation_attorney,
                    case_workers.lead_attorney,
                    case_workers.supervising_attorney,
                    case_workers.primary_contact,
                ]
                admin_user = None
                for worker in priority_workers:
                    if worker and NylasIntegration.objects.filter(user=worker).exists():
                        admin_user = worker
                        break
                if not admin_user:
                    logger.error("No case workers with Nylas integration found")
                    count_integration_dict["no_integration_found"] += 1
                    continue
                count_integration_dict["integration_found"] += 1
                logger.info(f"Using admin user {admin_user.email} for creating calendar events")

                # print dict count
                logger.info(f"Count: {count_integration_dict}")
                return
        except (User.DoesNotExist, NylasIntegration.DoesNotExist):
            return

    # Process each future event
    events_processed = 0
    events_created = 0
    events_updated = 0

    for case_event in future_events:
        events_processed += 1

        # Get case workers
        workers = get_case_workers_for_calendar(case_event.case)

        case_workers = CaseWorkers.objects.get(case=case_event.case)
        priority_workers = [
            case_workers.litigation_attorney,
            case_workers.lead_attorney,
            case_workers.supervising_attorney,
            case_workers.primary_contact,
        ]
        admin_user = None
        for worker in priority_workers:
            if worker and NylasIntegration.objects.filter(user=worker).exists():
                admin_user = worker
                break

        if admin_user:
            try:
                admin_integration = NylasIntegration.objects.get(user=admin_user)
                logger.info(f"Using integration for {admin_user.email}")
            except NylasIntegration.DoesNotExist:
                logger.error(f"No Nylas integration found for admin user {admin_user.email}")
                continue
            integration = admin_integration
        else:
            logger.error("No case workers with Nylas integration found")
            continue

        # If still no integration, skip this event
        if not integration:
            logger.warning(f"No Nylas integration found for case event {case_event.id}, skipping")
            continue

        # Create calendar event
        if not dry_run:
            calendar_event = None
            if not case_event.current_calendar_event_id:
                calendar_event = create_calendar_event(case_event, integration, workers)
                if calendar_event:
                    # Update case event with calendar details
                    update_case_event_with_calendar_details(case_event, calendar_event, integration)
                    events_created += 1

                    # Add workers to case event
                    worker_ids = [worker.id for worker in workers]
                    case_event.workers.set(worker_ids)

                    logger.info(f"Created calendar event for case event {case_event.id}")
                else:
                    logger.error(f"Failed to create calendar event for case event {case_event.id}")
            else:
                update_calendar_event(case_event, integration, workers)
                events_updated += 1
                logger.info(f"Updated calendar event for case event {case_event.id}")
            time.sleep(0.5)

        else:
            logger.info(f"Would create calendar event for case event {case_event.id} with {len(workers)} workers")

        # Log progress every 10 events
        if events_processed % 10 == 0:
            logger.info(f"Processed {events_processed}/{future_events.count()} events")

    logger.info(f"Completed processing {events_processed} events")
    logger.info(f"Created {events_created} calendar events")
    logger.info(f"Updated {events_updated} calendar events")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Create calendar events for future case events")
    parser.add_argument("--orgnization_id", help="Organization ID to process")
    parser.add_argument(
        "--dry-run", action="store_true", help="Don't actually create events, just log what would be done"
    )

    args = parser.parse_args()

    process_future_events(organization_id=args.orgnization_id, dry_run=args.dry_run)
