import re

from django.core.management.base import <PERSON>Command, CommandError
from django.db.models import Q
from tqdm import tqdm

from case_management.v2.models import (
    AdjusterContact,
    CaseDefendant,
    CasePartyContact,
    ClerkContact,
    ClientContactDetails,
    CourtContact,
    Employer,
    ExpertWitnessContact,
    InsuranceCompany,
    JudgeContact,
    LawFirmContact,
    LienHolder,
    LoanCompanyContact,
    MediatorContact,
    MedicalProviderContact,
    OrganizationAttorneyContact,
    OrgCostContact,
    SubrogationCompany,
    SubrogationContact,
)
from users.models import Organization


def format_phone_number(phone_str):
    """
    Format phone numbers to XXX-XXX-XXXX format

    Examples:
    (************* -> ************
    ************ -> ************
    ************ -> ************
    """
    if not phone_str or not isinstance(phone_str, str):
        return phone_str

    # Extract digits only
    digits = re.sub(r"\D", "", phone_str)

    # If we have a 10-digit number, format it as XXX-XXX-XXXX
    if len(digits) == 10:
        return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
    # For 11-digit numbers starting with 1 (common US format), remove the 1 and format
    elif len(digits) == 11 and digits[0] == "1":
        return f"{digits[1:4]}-{digits[4:7]}-{digits[7:]}"
    else:
        # Return original if not a standard format
        return phone_str


class PhoneNumberFormatter:
    """Class to handle updating phone numbers across all relevant models"""

    def __init__(self, organization_id, dry_run=False, detail=1):
        self.organization = Organization.objects.get(id=organization_id)
        self.dry_run = dry_run
        self.detail = detail

        # Define models and their phone-related fields
        self.models_config = [
            {
                "model": ClientContactDetails,
                "organization_filter": lambda m: Q(case__organization=self.organization),
                "fields": [
                    "phone_number_1",
                    "phone_number_2",
                    "phone_number_3",
                    "key_contact_phone",
                    "spouse_phone",
                    "emergency_contact_phone",
                ],
            },
            {
                "model": InsuranceCompany,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": AdjusterContact,
                "organization_filter": lambda m: Q(insurance_company__organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": OrganizationAttorneyContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": LawFirmContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": CaseDefendant,
                "organization_filter": lambda m: Q(case__organization=self.organization),
                "fields": ["phone", "cell", "fax", "work_phone"],
            },
            {
                "model": CasePartyContact,
                "organization_filter": lambda m: Q(case__organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": MedicalProviderContact,
                "organization_filter": lambda m: Q(medical_provider__organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": LienHolder,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": SubrogationContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": SubrogationCompany,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": Employer,
                "organization_filter": lambda m: Q(case__organization=self.organization),
                "fields": ["phone", "fax"],
            },
            {
                "model": OrgCostContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": CourtContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": JudgeContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax", "department_phone"],
            },
            {
                "model": ClerkContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": MediatorContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": ExpertWitnessContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
            {
                "model": LoanCompanyContact,
                "organization_filter": lambda m: Q(organization=self.organization),
                "fields": ["phone", "cell", "fax"],
            },
        ]

    def process_model(self, model_config):
        """Process a single model and update its phone number fields"""
        model = model_config["model"]
        filter_func = model_config["organization_filter"]
        fields = model_config["fields"]

        # Get all objects for this organization
        objects = model.objects.filter(filter_func(model))

        # Track changes for dry run reporting
        changes = []
        count = 0

        with tqdm(total=objects.count(), desc=f"Updating {model.__name__}") as pbar:
            for obj in objects:
                updated = False
                object_changes = {"id": obj.id, "fields": {}}

                for field in fields:
                    # Skip phone_ext fields and None values
                    if field.endswith("_ext") or not hasattr(obj, field):
                        continue

                    old_value = getattr(obj, field)
                    if old_value:
                        new_value = format_phone_number(old_value)
                        if new_value != old_value:
                            # Store change for reporting
                            object_changes["fields"][field] = {"old": old_value, "new": new_value}

                            if not self.dry_run:
                                # Only actually change the value if not in dry run mode
                                setattr(obj, field, new_value)
                                updated = True

                # Add to changes list if any fields would be changed
                if object_changes["fields"]:
                    try:
                        # Try to get a descriptive field for better reporting
                        if hasattr(obj, "name"):
                            object_changes["display"] = obj.name
                        elif hasattr(obj, "company"):
                            object_changes["display"] = obj.company
                        elif hasattr(obj, "first_name") and hasattr(obj, "last_name"):
                            object_changes["display"] = f"{obj.first_name} {obj.last_name}"
                        # Add this record as changed
                        changes.append(object_changes)
                    except:
                        # Fallback if any error occurs getting descriptive fields
                        object_changes["display"] = f"Object ID: {obj.id}"
                        changes.append(object_changes)

                    count += 1

                    # Only save if not in dry run mode and changes were made
                    if updated and not self.dry_run:
                        obj.save()

                pbar.update(1)

        return count, changes

    def update_all_models(self):
        """Process all models and update phone numbers"""
        total_updated = 0
        all_changes = {}

        for model_config in self.models_config:
            model_name = model_config["model"].__name__
            print(f"\nProcessing {model_name}...")

            count, changes = self.process_model(model_config)
            all_changes[model_name] = changes

            if self.dry_run:
                print(f"Would update {count} {model_name} objects")

                # Print detailed changes if in verbose mode
                if self.detail > 1 and changes:
                    print(f"\nDetailed changes for {model_name}:")
                    for change in changes:
                        print(f"  - {change.get('display', f'ID: {change["id"]}')}:")
                        for field, values in change["fields"].items():
                            print(f"      {field}: '{values['old']}' → '{values['new']}'")
            else:
                print(f"Updated {count} {model_name} objects")

            total_updated += count

        return total_updated, all_changes


class Command(BaseCommand):
    help = "Update phone numbers to standard format (XXX-XXX-XXXX) across all relevant models"

    def add_arguments(self, parser):
        parser.add_argument("--organization_id", type=int, required=True, help="Organization ID to process")
        parser.add_argument("--dry-run", action="store_true", help="Preview changes without saving to database")
        parser.add_argument(
            "--detail",
            type=int,
            default=1,
            choices=[0, 1, 2, 3],
            help="Detail level: 0=minimal output, 1=normal output, 2=verbose output, 3=very verbose output",
        )

    def handle(self, *args, **options):
        organization_id = options["organization_id"]
        dry_run = options["dry_run"]
        detail = options["detail"]

        try:
            formatter = PhoneNumberFormatter(organization_id, dry_run, detail)
            total_updated, all_changes = formatter.update_all_models()

            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Dry run completed. Would update {total_updated} phone numbers for organization {organization_id}"
                    )
                )

                # Output a summary of changes by model
                if detail == 1:
                    for model_name, changes in all_changes.items():
                        if changes:
                            self.stdout.write(f"{model_name}: {len(changes)} records would be updated")

                # Very verbose output - write JSON for programmatic analysis
                if detail > 2:
                    import json

                    self.stdout.write("\nDetailed changes in JSON format:")
                    self.stdout.write(json.dumps(all_changes, indent=2))
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully updated {total_updated} phone numbers for organization {organization_id}"
                    )
                )

        except Organization.DoesNotExist:
            raise CommandError(f"Organization with ID {organization_id} does not exist")
        except Exception as e:
            raise CommandError(f"Error updating phone numbers: {str(e)}")
