#!/bin/bash
# cleanup_docker.sh - Clean up Docker containers, networks, and volumes for AlphaLaw

echo "===== CLEANING UP DOCKER RESOURCES ====="

# Stop and remove all containers
echo "Stopping and removing all containers..."
docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml down

# Remove all volumes
echo "Removing all volumes..."
docker volume rm alphalaw_static_volume alphalaw_media_volume alphalaw_log_volume \
                alphalaw_celery_worker_volume alphalaw_celery_beat_volume \
                alphalaw_flower_volume 2>/dev/null || true

# Clean up the Redis data directory
echo "Cleaning up Redis data directory..."
rm -rf docker_data/redis/* 2>/dev/null || true

# List remaining volumes
echo ""
echo "Remaining volumes:"
docker volume ls

# List networks
echo ""
echo "Networks:"
docker network ls

# List containers
echo ""
echo "Containers:"
docker ps -a

echo ""
echo "===== CLEANUP COMPLETED ====="
echo "You can now restart the services with: ./start_services.sh prod"
