version: '3.8'

x-django-base: &django-base
  profiles:
    - prod
  build: .
  volumes:
    - .:/app
    - static_volume:/app/staticfiles
    - media_volume:/app/media
    - log_volume:/app/logs
  env_file:
    - .env
  depends_on:
    redis:
      condition: service_healthy
  networks:
    - alphalaw_network
  environment:
    - REDIS_HOST=redis
    - CELERY_BROKER_URL=redis://redis:6379/0
    - CELERY_RESULT_BACKEND=redis://redis:6379/0

services:
  redis:
    profiles:
      - prod
    image: redis:latest
    environment:
      - REDIS_MEMORY_LIMIT=2g
    command: redis-server --maxmemory 2gb --maxmemory-policy volatile-lru --appendonly no --save ""
    deploy:
      resources:
        limits:
          memory: 4g
          cpus: '0.75'
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    # In production, we don't need persistent Redis data
    volumes: []
    networks:
      - alphalaw_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  nginx:
    profiles:
      - prod
    build:
      context: .
      dockerfile: nginx/Dockerfile
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      django1:
        condition: service_started
      django2:
        condition: service_started
      django3:
        condition: service_started
      django4:
        condition: service_started
      django5:
        condition: service_started
      django6:
        condition: service_started
      django7:
        condition: service_started
      django8:
        condition: service_started
      django9:
        condition: service_started
      django10:
        condition: service_started
      django11:
        condition: service_started
      django12:
        condition: service_started
      django13:
        condition: service_started
      django14:
        condition: service_started
      django15:
        condition: service_started
    networks:
      - alphalaw_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/"]
      interval: 10s
      timeout: 5s
      retries: 3

  django1:
    <<: *django-base
    command: >
      sh -c "apt-get update && apt-get install -y curl &&
             python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django2:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django3:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django4:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django5:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django6:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django7:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django8:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django9:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django10:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django11:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django12:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django13:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django14:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  django15:
    <<: *django-base
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             exec gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-1} --threads ${GUNICORN_THREADS:-2} --timeout 120 --log-level debug django_backend_alphalaw.wsgi:application"

  celery_worker:
    profiles:
      - prod
    build: .
    command: celery -A django_backend_alphalaw worker --loglevel=info --concurrency=${CELERY_CONCURRENCY:-1} --prefetch-multiplier=${CELERY_PREFETCH_MULTIPLIER:-1} --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD:-100} -Q high_priority,medium_priority,low_priority,geocoding,celery
    volumes:
      - .:/app
      - log_volume:/app/logs
      - celery_worker_volume:/app/celery
      - media_volume:/app/media
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
      django1:
        condition: service_started
    networks:
      - alphalaw_network
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_TASK_TRACK_STARTED=True
      - CELERY_TASK_TIME_LIMIT=30
      - CELERY_RESULT_EXPIRES=86400
    deploy:
      resources:
        limits:
          memory: 4g
          cpus: '1.5'

  celery_beat:
    profiles:
      - prod
    build: .
    command: celery -A django_backend_alphalaw beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - .:/app
      - log_volume:/app/logs
      - celery_beat_volume:/app/celerybeat
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
      django1:
        condition: service_started
    networks:
      - alphalaw_network
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=django_backend_alphalaw.settings
    deploy:
      resources:
        limits:
          memory: 2g
          cpus: '0.25'

  flower:
    profiles:
      - prod
    build: .
    command: celery -A django_backend_alphalaw flower --url_prefix=/flower --port=5555 --persistent=True --db=/app/flower/flower.db --basic_auth=${FLOWER_USER}:${FLOWER_PASSWORD}
    ports:
      - "5555:5555"
    volumes:
      - .:/app
      - flower_volume:/app/flower
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
      celery_worker:
        condition: service_started
      django1:
        condition: service_started
    networks:
      - alphalaw_network
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_PERSISTENT=True
      - FLOWER_DB=/app/flower/flower.db
      - FLOWER_STATE_SAVE_INTERVAL=10000
      - FLOWER_BASIC_AUTH=${FLOWER_USER}:${FLOWER_PASSWORD}
      - FLOWER_URL_PREFIX=/flower
    deploy:
      resources:
        limits:
          memory: 2g
          cpus: '0.25'

  django_websocket:
    profiles:
      - prod
    build: .
    command: >
      sh -c "daphne -b 0.0.0.0 -p 8081 django_backend_alphalaw.asgi:application"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - log_volume:/app/logs
    ports:
      - "8081:8081"
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
      django1:
        condition: service_started
    networks:
      - alphalaw_network
    environment:
      - REDIS_HOST=redis
      - DJANGO_SETTINGS_MODULE=django_backend_alphalaw.settings
    deploy:
      resources:
        limits:
          memory: 4g
          cpus: '0.5'

volumes:
  static_volume:
  media_volume:
  log_volume:
  celery_worker_volume:
  celery_beat_volume:
  flower_volume:

networks:
  alphalaw_network:
    driver: bridge
