# Task Management System

This document explains the task management system in the AlphaLaw platform, including the filtering logic for different views and the API endpoints.

## Task Filtering Logic

The task management system provides different views of tasks based on the user's role and the selected view type. The following view types are available:

### View Types

1. **My Tasks (`my_tasks`)**
   - Shows tasks where the user is either assigned OR tagged
   - This is the default view
   - Use case: "Show me all tasks I need to work on or am involved with"

2. **Tagged Tasks (`tagged`)**
   - Shows ONLY tasks where the user is tagged but NOT assigned
   - Excludes tasks where the user is the assignee
   - Use case: "Show me tasks where I'm mentioned/tagged but not directly responsible"

3. **Created Tasks (`created`)**
   - Shows ONLY tasks created by the user
   - Use case: "Show me tasks I've created for others"

4. **All Tasks (`all`)**
   - Shows tasks where the user is assigned, tagged, OR created
   - Provides a comprehensive view of all tasks the user is involved with
   - Use case: "Show me all tasks I'm involved with in any way"

### Additional Filtering

In addition to the view type, the following filters can be applied:

- **Status**: Filter by task status (pending, in_progress, completed, reopened)
- **Priority**: Filter by task priority (low, medium, high, urgent, critical)
- **Due Date**: Filter by due date range
- **Exclude Closed Cases**: Exclude tasks from closed cases

### User Role Considerations

- **Admin Users**: Can see all tasks in their organization when using the `all` view
- **Regular Users**: Can only see tasks they have access to based on the view type

## API Endpoints

### Categorized Dashboard

```
GET /api/v1/task-management/tasks/categorized_dashboard/
```

This endpoint returns tasks categorized by timeframe:
- **Overdue**: Tasks with due dates in the past
- **Today**: Tasks due today
- **This Week**: Tasks due within the next 7 days
- **Later**: Tasks due beyond 7 days
- **No Due Date**: Tasks without a due date

#### Query Parameters

- `view`: The view type (my_tasks, tagged, created, all)
- `status`: Task status filter (can be comma-separated for multiple statuses)
- `priority`: Task priority filter
- `exclude_closed_cases`: Whether to exclude tasks from closed cases (true/false)

#### Response Format

```json
{
  "overdue": [...],
  "today": [...],
  "this_week": [...],
  "later": [...],
  "no_due_date": [...],
  "summary": {
    "total": 10,
    "pending": 5,
    "in_progress": 3,
    "completed": 1,
    "reopened": 1
  }
}
```

## Implementation Details

The task filtering logic is implemented in two main components:

1. **TaskViewSetV2.categorized_dashboard**: Sets up the view type filters
2. **TaskService.get_tasks_for_user**: Applies the filters to the database query

The filtering process follows these steps:

1. Apply organization filter
2. Apply case filter (if provided)
3. Apply exclude_closed_cases filter (if true)
4. Apply additional filters (status, priority, etc.)
5. Apply view type-specific filters
6. Apply user role-specific filters
7. Return the filtered tasks

## Best Practices

- Always include the `view` parameter to specify which tasks to show
- Use `exclude_closed_cases=true` to hide tasks from closed cases
- Use comma-separated values for `status` to filter by multiple statuses
- Check the `summary` object in the response for counts of tasks by status
