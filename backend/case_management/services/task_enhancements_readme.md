# Task Management Enhancements

This document outlines the enhancements made to the task management system to improve user experience and performance.

## Overview

The task management system has been enhanced with the following features:

1. **Categorized Dashboard**: Tasks are now categorized by timeframe (overdue, today, this week, later, no due date) with priority-based sorting within each category.
2. **Quick Status Updates**: A simplified API for quickly changing task status with minimal overhead.
3. **Bulk Task Updates**: Ability to update multiple tasks at once for improved efficiency.
4. **View Filtering**: Support for filtering tasks by view type (my tasks, tagged tasks, created tasks, all tasks).
5. **Performance Optimizations**: Improved database queries with proper select_related and prefetch_related usage.

## New API Endpoints

### 1. Categorized Dashboard

**Endpoint**: `GET /api/v1/task-management/tasks/categorized_dashboard/`

**Query Parameters**:
- `view`: Filter by view type (my_tasks, tagged, created, all)
- `status`: Filter by task status
- `priority`: Filter by task priority
- `exclude_closed_cases`: Exclude tasks from closed cases (true/false)

**Response**:
```json
{
  "overdue": [...],
  "today": [...],
  "this_week": [...],
  "later": [...],
  "no_due_date": [...],
  "summary": {
    "total": 25,
    "pending": 10,
    "in_progress": 8,
    "completed": 5,
    "reopened": 2
  }
}
```

### 2. Quick Status Update

**Endpoint**: `PATCH /api/v1/task-management/tasks/{task_id}/quick_status/`

**Request Body**:
```json
{
  "status": "completed"
}
```

**Response**: Updated task object

### 3. Bulk Task Update

**Endpoint**: `POST /api/v1/task-management/tasks/bulk_update/`

**Request Body**:
```json
{
  "task_ids": [1, 2, 3, 4],
  "updates": {
    "status": "completed",
    "priority": "high"
  }
}
```

**Response**:
```json
{
  "detail": "Updated 4 of 4 tasks",
  "updated_count": 4,
  "total_count": 4
}
```

## Service Layer Enhancements

### TaskService

The TaskService class has been enhanced with the following methods:

1. **get_categorized_tasks**: Get tasks categorized by timeframe with priority-based sorting.
2. **quick_update_status**: Quickly update a task's status with minimal validation.
3. **bulk_update_tasks**: Update multiple tasks with the same changes.

## Frontend Integration

To integrate these enhancements with the frontend, the following changes are recommended:

### 1. Update Task Dashboard Component

```typescript
// Example React Query hook for categorized dashboard
export const useCategorizedTasks = (
  view: 'my_tasks' | 'tagged' | 'created' | 'all' = 'my_tasks',
  filters: TaskFilters = {}
) => {
  return useQuery(
    ['categorizedTasks', view, filters],
    () =>
      api.get('/api/v1/task-management/tasks/categorized_dashboard/', {
        params: {
          view,
          ...filters
        }
      }),
    {
      select: (response) => response.data
    }
  );
};
```

### 2. Add Quick Status Toggle Component

```typescript
// Example React component for quick status toggle
const QuickStatusToggle = ({ task, onStatusChange }) => {
  const quickUpdateMutation = useMutation(
    (newStatus: string) =>
      api.patch(`/api/v1/task-management/tasks/${task.id}/quick_status/`, {
        status: newStatus
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['categorizedTasks']);
        onStatusChange && onStatusChange();
      }
    }
  );

  const handleStatusChange = (newStatus: string) => {
    quickUpdateMutation.mutate(newStatus);
  };

  return (
    <StatusDropdown
      currentStatus={task.status}
      onChange={handleStatusChange}
      isLoading={quickUpdateMutation.isLoading}
    />
  );
};
```

### 3. Implement Bulk Actions

```typescript
// Example React component for bulk actions
const BulkActions = ({ selectedTaskIds }) => {
  const bulkUpdateMutation = useMutation(
    (updates: any) =>
      api.post('/api/v1/task-management/tasks/bulk_update/', {
        task_ids: selectedTaskIds,
        updates
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['categorizedTasks']);
      }
    }
  );

  const handleBulkComplete = () => {
    bulkUpdateMutation.mutate({ status: 'completed' });
  };

  const handleBulkInProgress = () => {
    bulkUpdateMutation.mutate({ status: 'in_progress' });
  };

  return (
    <div className="bulk-actions">
      <Button
        onClick={handleBulkComplete}
        disabled={selectedTaskIds.length === 0 || bulkUpdateMutation.isLoading}
      >
        Mark Selected as Complete
      </Button>
      <Button
        onClick={handleBulkInProgress}
        disabled={selectedTaskIds.length === 0 || bulkUpdateMutation.isLoading}
      >
        Mark Selected as In Progress
      </Button>
    </div>
  );
};
```

## Implementation Notes

1. The new endpoints are available under the `/api/v1/task-management/` prefix to separate them from the case management API.
2. The existing task endpoints in the case management API continue to work as before.
3. The new endpoints are designed to be more efficient and provide a better user experience.
4. The service layer has been enhanced to support the new features while maintaining the existing functionality.

## Next Steps

1. Update the frontend to use the new endpoints.
2. Add visual indicators for task priority and due dates.
3. Implement the quick status toggle component.
4. Add support for bulk actions in the task dashboard.
5. Update the task creation form to use templates for common tasks.
