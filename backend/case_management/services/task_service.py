import logging
from datetime import datetime, timedelta
from typing import Dict, List

from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from users.models import User

from case_management.models import Case, Task

logger = logging.getLogger(__name__)


class TaskService:
    """
    Service class for handling task-related business logic.
    This centralizes task operations to ensure consistent behavior across different views.
    """

    @staticmethod
    def get_tasks_for_user(user: User, organization_id: int = None, case_id: str = None, **filters) -> List[Task]:
        """
        Get tasks for a specific user based on their role and permissions.

        Args:
            user: The user to get tasks for
            organization_id: Optional organization ID to filter by
            case_id: Optional case ID to filter by
            filters: Additional filters to apply

        Returns:
            List of Task objects
        """
        logger.info(f"get_tasks_for_user called with filters: {filters}")

        if not organization_id:
            organization = user.organizations.first()
            if not organization:
                logger.warning(f"No organization found for user {user.id}")
                return []
            organization_id = organization.id

        # Base query with organization filter
        query = Task.objects.filter(case__organization_id=organization_id)
        logger.info(f"Base query count: {query.count()}")

        # Apply case filter if provided
        if case_id:
            query = query.filter(case_id=case_id)
            logger.info(f"After case filter count: {query.count()}")

        # Handle exclude_closed_cases filter separately
        exclude_closed_cases = filters.pop("exclude_closed_cases", False)
        if exclude_closed_cases:
            from kpi_and_reports.models import KPIType

            query = query.exclude(
                case__organization_status__kpi_type__in=[
                    KPIType.case_closed,
                    KPIType.case_rejected,
                    KPIType.case_dropped,
                    KPIType.subbed_out,
                    KPIType.pending_drop,
                ]
            )
            logger.info(f"After exclude_closed_cases filter count: {query.count()}")

        # Handle special view type filters
        view_type = filters.pop("view_type", None)
        view_user = filters.pop("view_user", None)
        exclude_assigned = filters.pop("exclude_assigned", None)

        # Apply additional filters
        for key, value in filters.items():
            if value is not None:
                # Handle comma-separated status values
                if key == "status" and "," in str(value):
                    status_values = [s.strip() for s in value.split(",")]
                    logger.info(f"Applying status filter with multiple values: {status_values}")
                    query = query.filter(**{f"{key}__in": status_values})
                else:
                    logger.info(f"Applying filter {key}={value}")
                    query = query.filter(**{key: value})
                logger.info(f"After applying {key} filter, count: {query.count()}")

        # Handle exclude_assigned filter (for tagged view)
        if exclude_assigned:
            logger.info(f"Excluding tasks assigned to user {exclude_assigned.id}")
            query = query.exclude(assigned_to=exclude_assigned)
            logger.info(f"After excluding assigned tasks, count: {query.count()}")

        # For admin users with no view_type, return all tasks in their organization
        if user.role == "admin" and not view_type:
            logger.info(f"User {user.id} is admin with no view_type, returning all tasks: {query.count()}")
            return query

        # Handle view type filtering
        if view_type and view_user:
            logger.info(f"Applying view_type filter: {view_type} for user {view_user.id}")

            if view_type == "my_tasks":
                # For my_tasks, show tasks where user is assigned OR tagged
                filtered_query = query.filter(Q(assigned_to=view_user) | Q(tagged_users=view_user)).distinct()
                logger.info(f"After my_tasks filter, count: {filtered_query.count()}")
                return filtered_query

            elif view_type == "all":
                # For all tasks, show tasks where user is assigned, tagged, or created
                filtered_query = query.filter(
                    Q(assigned_to=view_user) | Q(tagged_users=view_user) | Q(created_by=view_user)
                ).distinct()
                logger.info(f"After all tasks filter, count: {filtered_query.count()}")
                return filtered_query

        # Default filtering for regular users
        # Only return tasks they have access to
        filtered_query = query.filter(
            Q(assigned_to=user)  # User is assigned to the task
            | Q(tagged_users=user)  # User is tagged in the task
            | Q(created_by=user)  # User created the task
            | Q(case__created_by=user)  # User created the case
            | Q(case__assigned_users=user)  # User is assigned to the case
        ).distinct()

        logger.info(f"User {user.id} using default filtering, returning filtered tasks: {filtered_query.count()}")
        return filtered_query

    @staticmethod
    def get_dashboard_tasks(user: User, **filters) -> Dict[str, List[Task]]:
        """
        Get tasks for the user dashboard, organized by today, upcoming, and overdue.

        Args:
            user: The user to get tasks for
            filters: Additional filters to apply

        Returns:
            Dictionary with today, upcoming, and overdue tasks
        """
        organization = user.organizations.first()
        if not organization:
            return {"today": [], "upcoming": [], "overdue": []}

        # Get base queryset
        base_queryset = TaskService.get_tasks_for_user(user, organization_id=organization.id, **filters)

        # Define time periods
        today = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow = today + timedelta(days=1)

        # Filter by time periods
        overdue_tasks = base_queryset.filter(due_date__lt=today, status__in=["pending", "in_progress", "reopened"])
        today_tasks = base_queryset.filter(due_date__gte=today, due_date__lt=tomorrow)
        upcoming_tasks = base_queryset.filter(due_date__gte=tomorrow)

        # Priority order for sorting
        priority_order = {
            "critical": 0,
            "urgent": 1,
            "high": 2,
            "regular": 3,
            "medium": 4,
            "low": 5,
        }

        # Sort tasks by priority and due date
        overdue_tasks = sorted(
            overdue_tasks, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max)
        )
        today_tasks = sorted(today_tasks, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max))
        upcoming_tasks = sorted(
            upcoming_tasks, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max)
        )

        return {"overdue": overdue_tasks, "today": today_tasks, "upcoming": upcoming_tasks}

    @staticmethod
    def get_categorized_tasks(user: User, organization_id: int = None, **filters) -> Dict[str, List[Task]]:
        """
        Get tasks categorized by timeframe with priority-based sorting.

        Args:
            user: The user to get tasks for
            organization_id: Optional organization ID to filter by
            filters: Additional filters to apply

        Returns:
            Dict with categories: overdue, today, this_week, later, no_due_date
        """
        logger.info(f"get_categorized_tasks called with filters: {filters}")

        organization = user.organizations.first()
        if not organization:
            logger.warning(f"No organization found for user {user.id}")
            return {"overdue": [], "today": [], "this_week": [], "later": [], "no_due_date": []}
        organization_id = organization.id

        # Get base queryset
        tasks = TaskService.get_tasks_for_user(user, organization_id=organization_id, **filters)

        # Log the count of tasks returned
        task_count = tasks.count()
        logger.info(f"get_tasks_for_user returned {task_count} tasks for user {user.id}")

        # Define time periods
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start.replace(hour=23, minute=59, second=59)
        week_end = today_end + timedelta(days=6)

        # Categorize tasks
        overdue = tasks.filter(due_date__lt=today_start, status__in=["pending", "in_progress", "reopened"])
        today = tasks.filter(due_date__gte=today_start, due_date__lte=today_end)
        this_week = tasks.filter(due_date__gt=today_end, due_date__lte=week_end)
        later = tasks.filter(due_date__gt=week_end)
        no_due_date = tasks.filter(due_date__isnull=True)

        # Priority order for sorting
        priority_order = {
            "critical": 0,
            "urgent": 1,
            "high": 2,
            "regular": 3,
            "medium": 4,
            "low": 5,
        }

        # Apply sorting
        overdue = sorted(overdue, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max))
        today = sorted(today, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max))
        this_week = sorted(this_week, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max))
        later = sorted(later, key=lambda x: (priority_order.get(x.priority, 6), x.due_date or datetime.max))
        no_due_date = sorted(no_due_date, key=lambda x: priority_order.get(x.priority, 6))

        return {"overdue": overdue, "today": today, "this_week": this_week, "later": later, "no_due_date": no_due_date}

    @staticmethod
    def create_task(
        case: Case,
        title: str,
        created_by: User,
        description: str = "",
        assigned_to: User = None,
        tagged_user_ids: List[int] = None,
        due_date: datetime = None,
        priority: str = "medium",
        task_for: str = "Case",
        defendant_id: int = None,
        **kwargs,
    ) -> Task:
        """
        Create a new task with validation.

        Args:
            case: The case to create the task for
            title: Task title
            created_by: User creating the task
            description: Task description
            assigned_to: User assigned to the task
            tagged_user_ids: List of user IDs to tag
            due_date: Task due date
            priority: Task priority
            task_for: What the task is for (Case, Negotiation, etc.)
            defendant_id: Optional defendant ID for negotiation tasks
            kwargs: Additional task fields

        Returns:
            The created Task object
        """
        with transaction.atomic():
            # Create the task
            task = Task(
                case=case,
                title=title,
                description=description,
                assigned_to=assigned_to,
                due_date=due_date,
                priority=priority,
                task_for=task_for,
                created_by=created_by,
                **kwargs,
            )

            # Set defendant if provided
            if defendant_id and task_for in ["Negotiation", "Negotiations_UIM"]:
                from case_management.v2.models import CaseDefendant

                try:
                    defendant = CaseDefendant.objects.get(id=defendant_id, case=case)
                    task.defendant = defendant
                except CaseDefendant.DoesNotExist:
                    logger.warning(f"Defendant {defendant_id} not found for case {case.id}")

            # Save the task
            task.save()

            # Add tagged users
            if tagged_user_ids:
                tagged_users = User.objects.filter(id__in=tagged_user_ids)
                task.tagged_users.set(tagged_users)

            return task

    @staticmethod
    def update_task(task: Task, user: User, **update_data) -> Task:
        """
        Update a task with validation.

        Args:
            task: The task to update
            user: User performing the update
            update_data: Fields to update

        Returns:
            The updated Task object
        """
        with transaction.atomic():
            # Handle tagged users separately
            tagged_user_ids = update_data.pop("tagged_user_ids", None)

            # Update task fields
            for field, value in update_data.items():
                if hasattr(task, field):
                    setattr(task, field, value)

            # Save the task
            task.save()

            # Update tagged users if provided
            if tagged_user_ids is not None:
                tagged_users = User.objects.filter(id__in=tagged_user_ids)
                task.tagged_users.set(tagged_users)

            return task

    @staticmethod
    def mark_task_complete(task: Task, user: User) -> Task:
        """
        Mark a task as completed.

        Args:
            task: The task to mark as completed
            user: User performing the action

        Returns:
            The updated Task object
        """
        return TaskService.update_task(task, user, status="completed")

    @staticmethod
    def mark_task_in_progress(task: Task, user: User) -> Task:
        """
        Mark a task as in progress.

        Args:
            task: The task to mark as in progress
            user: User performing the action

        Returns:
            The updated Task object
        """
        return TaskService.update_task(task, user, status="in_progress")

    @staticmethod
    def reopen_task(task: Task, user: User) -> Task:
        """
        Reopen a completed task.

        Args:
            task: The task to reopen
            user: User performing the action

        Returns:
            The updated Task object
        """
        return TaskService.update_task(task, user, status="reopened")

    @staticmethod
    def quick_update_status(task: Task, user: User, new_status: str) -> Task:
        """
        Quickly update a task's status with minimal validation.

        Args:
            task: The task to update
            user: User performing the update
            new_status: New status value

        Returns:
            The updated Task object
        """
        # Validate the status transition
        if new_status not in task.VALID_STATUS_TRANSITIONS.get(task.status, []):
            raise ValueError(f"Invalid status transition from {task.status} to {new_status}")

        # Update the task
        return TaskService.update_task(task, user, status=new_status)

    @staticmethod
    def bulk_update_tasks(tasks, user: User, **updates) -> List[Task]:
        """
        Update multiple tasks with the same changes.

        Args:
            tasks: List of tasks to update
            user: User performing the update
            updates: Fields to update

        Returns:
            List of updated Task objects
        """
        updated_tasks = []
        for task in tasks:
            try:
                updated_task = TaskService.update_task(task, user, **updates)
                updated_tasks.append(updated_task)
            except Exception as e:
                logger.error(f"Error updating task {task.id}: {str(e)}")

        return updated_tasks
