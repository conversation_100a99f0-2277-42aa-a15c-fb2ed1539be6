# Task Management Business Logic

## Overview
This document outlines the business logic for the task management system in the case management module. Tasks are used to track work items, assignments, and follow-ups at the case level.

## Task Model
The Task model has the following key attributes:
- **case**: ForeignKey to Case
- **title**: Task title
- **description**: Task description
- **status**: Task status (pending, in_progress, completed, reopened)
- **assigned_to**: User assigned to the task
- **tagged_users**: Users tagged in the task
- **priority**: Task priority (low, medium, regular, high, urgent, critical, cancelled)
- **due_date**: When the task is due
- **task_for**: What the task is related to (Case, Negotiation, Negotiations_UIM)
- **defendant**: Optional defendant for negotiation tasks
- **created_by**: User who created the task
- **created_at**: When the task was created
- **updated_at**: When the task was last updated
- **is_overdue**: Whether the task is past its due date
- **overwritten_by_admin**: Whether an admin has marked this overdue task as no longer needed

## Status Transitions
Tasks follow a specific workflow with valid status transitions:
- **pending** → in_progress, completed, reopened
- **in_progress** → completed, reopened, pending
- **completed** → reopened, pending, in_progress
- **reopened** → in_progress, pending, completed

## Access Control
Tasks have the following access control rules:
1. **Admin users** can access all tasks in their organization
2. **Regular users** can access tasks if:
   - They created the task
   - They are assigned to the task
   - They are tagged in the task
   - They created the case
   - They are assigned to the case

## Required API Endpoints

### 1. Dashboard Tasks API
- **Endpoint**: `GET /api/v1/case-management/tasks/dashboard/`
- **Purpose**: Get tasks for the user's dashboard, organized by today, upcoming, and overdue
- **Filters**:
  - `status`: Filter by task status
  - `priority`: Filter by task priority
  - `exclude_closed_cases`: Exclude tasks from closed cases
- **Response**: Tasks grouped by timeframe (today, upcoming, overdue)

### 2. Categorized Dashboard Tasks API (New)
- **Endpoint**: `GET /api/v1/case-management/v2/tasks/categorized_dashboard/`
- **Purpose**: Get tasks categorized by timeframe with priority-based sorting
- **Filters**:
  - `status`: Filter by task status
  - `priority`: Filter by task priority
  - `exclude_closed_cases`: Exclude tasks from closed cases
  - `view`: Filter by view type (my_tasks, tagged, created, all)
- **Response**: Tasks grouped by timeframe (overdue, today, this_week, later, no_due_date) with summary counts

### 3. Case Tasks API
- **Endpoint**: `GET /api/v1/case-management/cases/{case_id}/tasks/`
- **Purpose**: Get all tasks for a specific case
- **Filters**:
  - `status`: Filter by task status
  - `priority`: Filter by task priority
  - `task_for`: Filter by what the task is for (Case, Negotiation, etc.)
  - `defendant_id`: Filter by defendant (for negotiation tasks)
- **Response**: Paginated list of tasks

### 4. Create Task API
- **Endpoint**: `POST /api/v1/case-management/cases/{case_id}/tasks/`
- **Purpose**: Create a new task for a case
- **Request Body**:
  - `title`: Task title (required)
  - `description`: Task description
  - `assigned_to_id`: User ID to assign the task to
  - `tagged_user_ids`: List of user IDs to tag
  - `due_date`: Task due date
  - `priority`: Task priority
  - `task_for`: What the task is for (Case, Negotiation, etc.)
  - `defendant_id`: Defendant ID (required for negotiation tasks)
- **Response**: Created task details

### 5. Update Task API
- **Endpoint**: `PUT /api/v1/case-management/tasks/{task_id}/`
- **Purpose**: Update an existing task
- **Request Body**: Same fields as Create Task API
- **Response**: Updated task details

### 6. Task Status Transition API
- **Endpoint**: `POST /api/v1/case-management/tasks/{task_id}/transition/`
- **Purpose**: Change the status of a task with proper validation
- **Request Body**:
  - `status`: New status (must be a valid transition)
- **Response**: Updated task details

### 7. Quick Status Update API (New)
- **Endpoint**: `PATCH /api/v1/case-management/v2/tasks/{task_id}/quick_status/`
- **Purpose**: Quickly update a task's status with minimal validation
- **Request Body**:
  - `status`: New status (must be a valid transition)
- **Response**: Updated task details

### 8. Bulk Task Update API
- **Endpoint**: `POST /api/v1/case-management/v2/tasks/bulk_update/`
- **Purpose**: Update multiple tasks at once
- **Request Body**:
  - `task_ids`: List of task IDs to update
  - `updates`: Fields to update (status, assigned_to_id, etc.)
- **Response**: Summary of updates

### 9. Task Assignment API
- **Endpoint**: `POST /api/v1/case-management/tasks/{task_id}/assign/`
- **Purpose**: Assign a task to a user
- **Request Body**:
  - `user_id`: User ID to assign the task to
- **Response**: Updated task details

### 10. Task Tagging API
- **Endpoint**: `POST /api/v1/case-management/tasks/{task_id}/tag-users/`
- **Purpose**: Tag users in a task
- **Request Body**:
  - `user_ids`: List of user IDs to tag
- **Response**: Updated task details

## Business Rules

### Task Creation
1. The user creating the task must have access to the case
2. The assigned user must belong to the same organization as the case
3. Tagged users must belong to the same organization as the case
4. For negotiation tasks, a defendant must be specified
5. The defendant must belong to the same case

### Task Updates
1. The user updating the task must have access to the task
2. Status transitions must follow the valid transition rules
3. The assigned user must belong to the same organization as the case
4. Tagged users must belong to the same organization as the case

### Task Deletion
1. The user deleting the task must have access to the task
2. Admins can delete any task in their organization
3. Regular users can only delete tasks they created

### Notifications
1. When a task is created, notify the assigned user and tagged users
2. When a task's status changes, notify the assigned user and tagged users
3. When a task is assigned to a new user, notify the new assignee
4. When users are tagged in a task, notify the newly tagged users

## Implementation Considerations

### Performance Optimization
1. Use select_related and prefetch_related to optimize database queries
2. Implement pagination for task listings
3. Use caching for frequently accessed data

### Security
1. Enforce organization-level isolation for all task operations
2. Validate user permissions for each operation
3. Sanitize and validate all user inputs

### Monitoring
1. Log all task operations for audit purposes
2. Track task completion rates and overdue tasks for reporting
3. Monitor API performance and error rates