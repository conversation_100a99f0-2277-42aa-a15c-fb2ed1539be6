# Generated by Django 5.1.3 on 2025-05-19 10:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('case_management_v2', '0149_alter_caseincidentdetails_incident_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='caseincidentdetails',
            name='requested_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='caseincidentdetails',
            name='requested_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incident_docs_requested', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='caseincidentdetails',
            name='received_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='caseincidentdetails',
            name='received_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incident_docs_received', to=settings.AUTH_USER_MODEL),
        ),
    ] 