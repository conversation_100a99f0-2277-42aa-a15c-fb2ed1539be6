# Generated by Django 5.1.3 on 2025-05-10 10:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('case_management_v2', '0161_casediscovery_event_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='treatmentprovider',
            name='accepted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='treatmentprovider',
            name='accepted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='accepted_treatments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='treatmentprovider',
            name='accepted_date',
            field=models.DateField(blank=True, null=True),
        ),
    ]
