"""
Tests for auto-completion of negotiation deadline tasks.
"""

from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from unittest.mock import patch

from case_management.models import Task
from case_management.v2.models import (
    Case,
    CaseDefendant,
    CaseNegotiation,
    CaseNegotiationUIM,
    ClientInsurance,
)
from case_management.v2.services.negotiation_service import NegotiationService
from django.test import TestCase
from django.utils import timezone
from users.models import Organization, User


class NegotiationTaskCompletionTest(TestCase):
    """Test auto-completion of negotiation deadline tasks."""

    @classmethod
    def setUpClass(cls):
        """Set up class-level test data."""
        # Patch the signals to avoid UserTag and other signal-related errors
        cls.patcher1 = patch("case_management.signals.create_default_user_tags")
        cls.patcher2 = patch("organization_management.signals.create_default_statuses_and_checklists")
        cls.mock_create_default_user_tags = cls.patcher1.start()
        cls.mock_create_default_statuses_and_checklists = cls.patcher2.start()
        super().setUpClass()

    @classmethod
    def tearDownClass(cls):
        """Clean up after tests."""
        cls.patcher1.stop()
        cls.patcher2.stop()
        super().tearDownClass()

    def setUp(self):
        """Set up test data."""
        # Create organization
        self.organization = Organization.objects.create(name="Test Organization")

        # Create users
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password",
            first_name="Test",
            last_name="User",
        )
        # Add user to organization
        self.organization.users.add(self.user)

        # Create case
        self.case = Case.objects.create(
            case_name="Test Case",
            organization=self.organization,
            created_by=self.user,
        )

        # Create defendant
        self.defendant = CaseDefendant.objects.create(
            case=self.case,
            defendant_type="individual",  # Required field
            first_name="John",
            last_name="Doe",
        )

        # Create client insurance
        self.client_insurance = ClientInsurance.objects.create(
            case=self.case,
            policy_number="12345",
            um_uim="UIM",  # Using um_uim field instead of insurance_type
        )

        # Create negotiation deadline task
        self.negotiation_task = Task.objects.create(
            case=self.case,
            title=f"{self.case.case_name}: Negotiation deadline approaching for defendant {self.defendant.first_name} {self.defendant.last_name}",
            description="Test description",
            status="pending",
            priority="high",
            task_for="Negotiation",
            due_date=timezone.now().date() + timedelta(days=2),
            created_by=self.user,
        )

        # Create UIM negotiation deadline task
        self.uim_negotiation_task = Task.objects.create(
            case=self.case,
            title=f"{self.case.case_name}: UIM negotiation deadline approaching for case {self.case.id}",
            description="Test description",
            status="pending",
            priority="high",
            task_for="Negotiations_UIM",
            due_date=timezone.now().date() + timedelta(days=2),
            created_by=self.user,
        )

    def test_complete_negotiation_deadline_tasks(self):
        """Test the complete_negotiation_deadline_tasks method."""
        # Verify tasks are pending
        self.assertEqual(self.negotiation_task.status, "pending")
        self.assertEqual(self.uim_negotiation_task.status, "pending")

        # Complete third-party negotiation tasks
        count = NegotiationService.complete_negotiation_deadline_tasks(self.case, is_uim=False)
        self.assertEqual(count, 1)

        # Refresh from database
        self.negotiation_task.refresh_from_db()
        self.uim_negotiation_task.refresh_from_db()

        # Verify third-party task is completed but UIM task is still pending
        self.assertEqual(self.negotiation_task.status, "completed")
        self.assertEqual(self.uim_negotiation_task.status, "pending")

        # Complete UIM negotiation tasks
        count = NegotiationService.complete_negotiation_deadline_tasks(self.case, is_uim=True)
        self.assertEqual(count, 1)

        # Refresh from database
        self.uim_negotiation_task.refresh_from_db()

        # Verify UIM task is now completed
        self.assertEqual(self.uim_negotiation_task.status, "completed")

    def test_accept_offer_completes_tasks(self):
        """Test that accepting an offer completes negotiation deadline tasks."""
        # Create a demand
        demand = CaseNegotiation.objects.create(
            defendant=self.defendant,
            type="INITIAL_DEMAND",
            amount=Decimal("10000.00"),
            status="SENT",
        )

        # Create an offer
        offer = CaseNegotiation.objects.create(
            defendant=self.defendant,
            type="OFFER",
            amount=Decimal("8000.00"),
            status="RECEIVED",
            previous_offer=demand,
        )

        # Verify task is pending
        self.assertEqual(self.negotiation_task.status, "pending")

        # Accept the offer
        NegotiationService.accept_offer(offer, self.user)

        # Refresh from database
        self.negotiation_task.refresh_from_db()

        # Verify task is completed
        self.assertEqual(self.negotiation_task.status, "completed")

    def test_accept_uim_offer_completes_tasks(self):
        """Test that accepting a UIM offer completes UIM negotiation deadline tasks."""
        # Create a UIM demand
        demand = CaseNegotiationUIM.objects.create(
            client_insurance=self.client_insurance,
            type="INITIAL_DEMAND",
            amount=Decimal("10000.00"),
            status="SENT",
        )

        # Create a UIM offer
        offer = CaseNegotiationUIM.objects.create(
            client_insurance=self.client_insurance,
            type="OFFER",
            amount=Decimal("8000.00"),
            status="RECEIVED",
            previous_offer=demand,
        )

        # Verify task is pending
        self.assertEqual(self.uim_negotiation_task.status, "pending")

        # Accept the offer
        NegotiationService.accept_offer(offer, self.user)

        # Refresh from database
        self.uim_negotiation_task.refresh_from_db()

        # Verify task is completed
        self.assertEqual(self.uim_negotiation_task.status, "completed")

    def test_create_demand_completes_tasks(self):
        """Test that creating a new demand completes negotiation deadline tasks."""
        # Verify task is pending
        self.assertEqual(self.negotiation_task.status, "pending")

        # Create a new demand
        NegotiationService.create_demand(
            case=self.case,
            amount=Decimal("12000.00"),
            status="SENT",
            defendant_id=self.defendant.id,
        )

        # Refresh from database
        self.negotiation_task.refresh_from_db()

        # Verify task is completed
        self.assertEqual(self.negotiation_task.status, "completed")

    def test_create_counter_offer_completes_tasks(self):
        """Test that creating a counter offer completes negotiation deadline tasks."""
        # Create a demand
        demand = CaseNegotiation.objects.create(
            defendant=self.defendant,
            type="INITIAL_DEMAND",
            amount=Decimal("10000.00"),
            status="SENT",
        )

        # Verify task is pending
        self.assertEqual(self.negotiation_task.status, "pending")

        # Create a counter offer
        NegotiationService.create_offer(
            previous_negotiation=demand,
            amount=Decimal("9000.00"),
            status="SENT",  # SENT means it's a counter offer
        )

        # Refresh from database
        self.negotiation_task.refresh_from_db()

        # Verify task is completed
        self.assertEqual(self.negotiation_task.status, "completed")
