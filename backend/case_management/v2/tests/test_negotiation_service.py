"""
Tests for the negotiation service.
"""

from unittest.mock import MagicMock, patch

from case_management.v2.services.negotiation_service import NegotiationService
from django.test import TestCase


class NegotiationServiceTest(TestCase):
    """Test the negotiation service."""

    @patch("case_management.models.Task.objects.filter")
    def test_complete_negotiation_deadline_tasks(self, mock_filter):
        """Test the complete_negotiation_deadline_tasks method."""
        # Set up the mock chain
        mock_tasks = MagicMock()
        mock_filter.return_value = mock_tasks
        mock_tasks.count.return_value = 1

        # Mock the tasks that will be completed

        # Mock the active_tasks queryset to return a list with one task
        mock_task = MagicMock()
        mock_task.id = 1
        mock_task.title = "Test Task"
        mock_tasks.__iter__.return_value = [mock_task]

        # Call the method with a mock case
        mock_case = MagicMock()
        mock_case.id = "CASE-12345"

        # Call the method
        result = NegotiationService.complete_negotiation_deadline_tasks(mock_case, is_uim=False)

        # Verify the result
        self.assertEqual(result, 1)

        # Verify the filter was called with the correct arguments
        mock_filter.assert_called_once_with(
            case=mock_case,
            task_for="Negotiation",
            title__contains="Negotiation Deadline Approaching",
            status__in=["pending", "in_progress", "reopened"],
        )

        # Verify the task was saved with status="completed"
        self.assertEqual(mock_task.status, "completed")
        self.assertEqual(mock_task.save.call_count, 1)

    @patch("case_management.models.Task.objects.filter")
    def test_complete_uim_negotiation_deadline_tasks(self, mock_filter):
        """Test the complete_negotiation_deadline_tasks method with UIM."""
        # Set up the mock chain
        mock_tasks = MagicMock()
        mock_filter.return_value = mock_tasks
        mock_tasks.count.return_value = 1

        # Mock the tasks that will be completed

        # Mock the active_tasks queryset to return a list with one task
        mock_task = MagicMock()
        mock_task.id = 1
        mock_task.title = "Test Task"
        mock_tasks.__iter__.return_value = [mock_task]

        # Call the method with a mock case
        mock_case = MagicMock()
        mock_case.id = "CASE-12345"

        # Call the method
        result = NegotiationService.complete_negotiation_deadline_tasks(mock_case, is_uim=True)

        # Verify the result
        self.assertEqual(result, 1)

        # Verify the filter was called with the correct arguments
        mock_filter.assert_called_once_with(
            case=mock_case,
            task_for="Negotiations_UIM",
            title__contains="UIM Negotiation Deadline Approaching",
            status__in=["pending", "in_progress", "reopened"],
        )

        # Verify the task was saved with status="completed"
        self.assertEqual(mock_task.status, "completed")
        self.assertEqual(mock_task.save.call_count, 1)
