import logging

from django.db.models import Q
from django.http import Http404
from django_filters import rest_framework as filters
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from users.models import User
from utils.utils import LoggerMixin

from case_management.models import Task
from case_management.serializers import TaskSerializer
from case_management.services.task_service import TaskService

logger = logging.getLogger(__name__)


class TaskPagination(PageNumberPagination):
    """
    Custom pagination class for tasks.
    Provides larger page size for task listings.
    """

    page_size = 100
    page_size_query_param = "page_size"
    max_page_size = 200

    def get_page_size(self, request):
        """Support both page_size and pageSize query parameters"""
        if "pageSize" in request.query_params:
            try:
                return min(int(request.query_params["pageSize"]), self.max_page_size)
            except (KeyError, ValueError):
                pass
        return super().get_page_size(request)


class TaskFilter(filters.FilterSet):
    """Filter for Task model"""

    status = filters.MultipleChoiceFilter(choices=Task.STATUS_CHOICES)
    priority = filters.ChoiceFilter(choices=Task.PRIORITY_CHOICES)
    due_date = filters.DateTimeFilter()
    due_date_after = filters.DateTimeFilter(field_name="due_date", lookup_expr="gte")
    due_date_before = filters.DateTimeFilter(field_name="due_date", lookup_expr="lte")
    created_at_after = filters.DateTimeFilter(field_name="created_at", lookup_expr="gte")
    created_at_before = filters.DateTimeFilter(field_name="created_at", lookup_expr="lte")
    is_overdue = filters.BooleanFilter()
    overwritten_by_admin = filters.BooleanFilter()
    created_by = filters.NumberFilter()
    tagged_users = filters.NumberFilter(field_name="tagged_users")
    overwritten_by = filters.NumberFilter()
    task_for = filters.ChoiceFilter(choices=Task.TASK_FOR_CHOICES)
    defendant = filters.NumberFilter()

    class Meta:
        model = Task
        fields = [
            "case",
            "status",
            "priority",
            "due_date",
            "assigned_to",
            "is_overdue",
            "overwritten_by_admin",
            "created_by",
            "tagged_users",
            "overwritten_by",
            "task_for",
            "defendant",
        ]


class TaskViewSetV2(LoggerMixin, viewsets.ModelViewSet):
    """
    Enhanced ViewSet for managing tasks with improved business logic.
    Uses TaskService for centralized business logic.
    """

    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = TaskFilter
    pagination_class = TaskPagination
    search_fields = ["title", "description"]
    ordering_fields = ["created_at", "updated_at", "due_date", "priority"]
    ordering = ["-created_at"]

    def get_queryset(self):
        """Get tasks based on user permissions and filters"""
        user = self.request.user
        organization = user.organizations.first()

        if not organization:
            return Task.objects.none()

        # Get case_id from URL if this is a nested route
        case_id = self.kwargs.get("case_id")

        # Get additional filters from query params
        filters = {}
        for param in ["status", "priority", "is_overdue", "task_for", "defendant"]:
            if self.request.query_params.get(param):
                filters[param] = self.request.query_params.get(param)

        # Check if we should exclude closed cases
        exclude_closed_cases = self.request.query_params.get("exclude_closed_cases", "").lower() == "true"
        if exclude_closed_cases:
            filters["exclude_closed_cases"] = True

        # Check if this is for dashboard view
        for_dashboard = self.request.query_params.get("for_dashboard", "").lower() == "true"

        # Use TaskService to get filtered tasks
        queryset = TaskService.get_tasks_for_user(
            user=user, organization_id=organization.id, case_id=case_id, **filters
        )

        # Optimize with select_related and prefetch_related
        queryset = queryset.select_related("case", "assigned_to", "created_by", "defendant")
        queryset = queryset.prefetch_related("tagged_users")

        # Filter for dashboard if requested
        if for_dashboard and user.role != "admin":
            queryset = queryset.filter(Q(assigned_to=user) | Q(tagged_users=user)).distinct()

        return queryset

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """
        Get tasks for the user's dashboard, organized by today, upcoming, and overdue.
        """
        try:
            user = request.user

            # Get filters from query params
            filters = {}
            for param in ["status", "priority", "task_for", "defendant"]:
                if request.query_params.get(param):
                    filters[param] = request.query_params.get(param)

            # Check if we should exclude closed cases
            exclude_closed_cases = request.query_params.get("exclude_closed_cases", "").lower() == "true"
            if exclude_closed_cases:
                filters["exclude_closed_cases"] = True

            # Get dashboard tasks using TaskService
            tasks_by_timeframe = TaskService.get_dashboard_tasks(user, **filters)

            # Serialize the results
            overdue_serializer = self.get_serializer(tasks_by_timeframe["overdue"], many=True)
            today_serializer = self.get_serializer(tasks_by_timeframe["today"], many=True)
            upcoming_serializer = self.get_serializer(tasks_by_timeframe["upcoming"], many=True)

            return Response(
                {
                    "overdue_tasks": overdue_serializer.data,
                    "todays_tasks": today_serializer.data,
                    "upcoming_tasks": upcoming_serializer.data,
                }
            )
        except Exception as e:
            self.logger.error(f"Error in dashboard tasks: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error retrieving dashboard tasks", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def categorized_dashboard(self, request):
        """
        Get tasks categorized by timeframe with priority-based sorting.
        """
        try:
            user = request.user

            # Get filters from query params
            filters = {}
            for param in ["status", "priority", "task_for", "defendant"]:
                if request.query_params.get(param):
                    filters[param] = request.query_params.get(param)

            # Log the filters for debugging
            self.logger.info(f"Categorized dashboard filters: {filters}")

            # Get view type
            view_type = request.query_params.get("view", "my_tasks")
            self.logger.info(f"View type: {view_type}")

            # Log all query parameters for debugging
            self.logger.info(f"All query parameters: {dict(request.query_params)}")

            # Set view-specific filters
            if view_type == "my_tasks":
                # For my_tasks, show tasks where user is assigned OR tagged
                self.logger.info(f"Setting my_tasks filter for user {user.id}")
                # We'll handle this with a custom filter in get_tasks_for_user
                filters["view_type"] = "my_tasks"
                filters["view_user"] = user
            elif view_type == "tagged":
                # For tagged tasks, only show tasks where user is tagged (not assigned)
                self.logger.info(f"Setting tagged_users filter for user {user.id}")
                filters["tagged_users"] = user
                # Exclude tasks where user is assigned
                filters["exclude_assigned"] = user
            elif view_type == "created":
                # For created tasks, only show tasks created by the user
                self.logger.info(f"Setting created_by filter for user {user.id}")
                filters["created_by"] = user
            elif view_type == "all":
                # For all tasks, show tasks where user is assigned, tagged, or created
                self.logger.info(f"Setting all tasks filter for user {user.id}")
                filters["view_type"] = "all"
                filters["view_user"] = user
            else:
                self.logger.info(f"Unknown view type: {view_type}, defaulting to my_tasks")
                filters["view_type"] = "my_tasks"
                filters["view_user"] = user

            # Check if we should exclude closed cases
            exclude_closed_cases = request.query_params.get("exclude_closed_cases", "").lower() == "true"
            if exclude_closed_cases:
                filters["exclude_closed_cases"] = True

            # Get categorized tasks
            task_categories = TaskService.get_categorized_tasks(user, **filters)

            # Serialize each category
            response_data = {}
            for category, tasks in task_categories.items():
                response_data[category] = self.get_serializer(tasks, many=True).data

            # Add summary counts
            all_tasks = []
            for tasks in task_categories.values():
                all_tasks.extend(tasks)

            summary = {
                "total": len(all_tasks),
                "pending": len([t for t in all_tasks if t.status == "pending"]),
                "in_progress": len([t for t in all_tasks if t.status == "in_progress"]),
                "completed": len([t for t in all_tasks if t.status == "completed"]),
                "reopened": len([t for t in all_tasks if t.status == "reopened"]),
            }

            response_data["summary"] = summary

            return Response(response_data)
        except Exception as e:
            self.logger.error(f"Error in categorized dashboard: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error retrieving dashboard tasks", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["patch"])
    def quick_status(self, request, pk=None):
        """
        Quickly update a task's status.
        """
        try:
            task = self.get_object()
            new_status = request.data.get("status")

            if not new_status:
                return Response({"detail": "Status is required"}, status=status.HTTP_400_BAD_REQUEST)

            # Update the task status
            try:
                updated_task = TaskService.quick_update_status(task, request.user, new_status)
                serializer = self.get_serializer(updated_task)

                return Response(serializer.data)
            except ValueError as e:
                return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Http404:
            return Response({"detail": "Task not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            self.logger.error(f"Error in quick status update: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error updating task status", "error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def transition(self, request, pk=None):
        """
        Transition a task to a new status with validation.
        """
        try:
            task = self.get_object()
            new_status = request.data.get("status")

            if not new_status:
                return Response({"detail": "Status is required"}, status=status.HTTP_400_BAD_REQUEST)

            # Validate the status transition
            original_status = task.status
            valid_transitions = task.VALID_STATUS_TRANSITIONS.get(original_status, [])

            if new_status not in valid_transitions:
                return Response(
                    {
                        "detail": f"Invalid status transition. From '{original_status}' you can only transition to: {', '.join(valid_transitions)}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update the task status
            updated_task = TaskService.update_task(task, request.user, status=new_status)
            serializer = self.get_serializer(updated_task)

            return Response(serializer.data)
        except Http404:
            return Response({"detail": "Task not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            self.logger.error(f"Error in task transition: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error transitioning task", "error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def assign(self, request, pk=None):
        """
        Assign a task to a user.
        """
        try:
            task = self.get_object()
            user_id = request.data.get("user_id")

            if not user_id:
                return Response({"detail": "User ID is required"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response({"detail": "User not found"}, status=status.HTTP_404_NOT_FOUND)

            # Validate that user belongs to the same organization as the case
            if not user.organizations.filter(id=task.case.organization.id).exists():
                return Response(
                    {"detail": f"User {user.email} must belong to the same organization as the case"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update the task
            updated_task = TaskService.update_task(task, request.user, assigned_to=user)
            serializer = self.get_serializer(updated_task)

            return Response(serializer.data)
        except Http404:
            return Response({"detail": "Task not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            self.logger.error(f"Error in task assignment: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error assigning task", "error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def tag_users(self, request, pk=None):
        """
        Tag users in a task.
        """
        try:
            task = self.get_object()
            user_ids = request.data.get("user_ids", [])

            if not user_ids:
                return Response({"detail": "User IDs are required"}, status=status.HTTP_400_BAD_REQUEST)

            # Get users
            users = User.objects.filter(id__in=user_ids)

            # Validate that users belong to the same organization as the case
            org_id = task.case.organization.id
            valid_org_user_ids = User.objects.filter(organizations=org_id).values_list("id", flat=True)

            invalid_users = set(user_ids) - set(valid_org_user_ids)
            if invalid_users:
                invalid_users = User.objects.filter(id__in=invalid_users)
                return Response(
                    {
                        "detail": f"Users {', '.join([u.email for u in invalid_users])} must belong to the same organization as the case"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update the task
            task.tagged_users.set(users)
            serializer = self.get_serializer(task)

            return Response(serializer.data)
        except Http404:
            return Response({"detail": "Task not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            self.logger.error(f"Error in tagging users: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error tagging users", "error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def bulk_update(self, request):
        """
        Update multiple tasks at once.
        """
        try:
            task_ids = request.data.get("task_ids", [])
            updates = request.data.get("updates", {})

            if not task_ids:
                return Response({"detail": "Task IDs are required"}, status=status.HTTP_400_BAD_REQUEST)

            if not updates:
                return Response({"detail": "Updates are required"}, status=status.HTTP_400_BAD_REQUEST)

            # Get tasks
            user = request.user
            organization = user.organizations.first()

            if not organization:
                return Response({"detail": "User must belong to an organization"}, status=status.HTTP_400_BAD_REQUEST)

            # Get tasks that the user has access to
            tasks = TaskService.get_tasks_for_user(user=user, organization_id=organization.id).filter(id__in=task_ids)

            # Update tasks using the bulk update service
            updated_tasks = TaskService.bulk_update_tasks(tasks, user, **updates)

            return Response(
                {
                    "detail": f"Updated {len(updated_tasks)} of {len(task_ids)} tasks",
                    "updated_count": len(updated_tasks),
                    "total_count": len(task_ids),
                }
            )
        except Exception as e:
            self.logger.error(f"Error in bulk task update: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error updating tasks", "error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
