from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .task import TaskViewSetV2

# Create a router for the enhanced task views
task_router = DefaultRouter()
task_router.register(r"tasks", TaskViewSetV2, basename="task-v2")

# Create a nested router for case-specific tasks
case_task_router = DefaultRouter()
case_task_router.register(r"tasks", TaskViewSetV2, basename="case-task-v2")

urlpatterns = [
    # Include the task router
    path("", include(task_router.urls)),
    
    # Include the case-specific task router
    path("cases/<str:case_id>/", include(case_task_router.urls)),
]
