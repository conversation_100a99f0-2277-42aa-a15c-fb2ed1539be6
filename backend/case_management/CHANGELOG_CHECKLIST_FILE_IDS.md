# Checklist File IDs Feature

## Overview
This feature adds the ability to associate file IDs with checklist items in the case management system. This allows users to track which files are related to specific checklist items, making it easier to find and reference relevant documents.

## Changes Made
1. Added a new `file_ids` <PERSON><PERSON><PERSON>ield to the `CaseChecklist` model
2. Updated the `CaseChecklistManagementSerializer` to include the new field
3. Updated the API endpoints to handle the new field:
   - Updated the `update_checklist_item` action in `CaseViewSet`
   - Updated the `checklists` action in `CaseViewSet` to include file_ids in the response
   - Updated the `toggle_completion` action in `CaseChecklistManagementViewSet` to handle file_ids

## API Usage Examples

### Update a checklist item with file IDs

```bash
# Update a checklist item with file IDs
curl -X POST \
  "http://localhost:8000/api/v1/case-management/cases/CASE-12345/update_checklist_item/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "checklist_item_id": 123,
    "is_completed": true,
    "file_ids": ["file-id-1", "file-id-2", "file-id-3"]
  }'
```

### Toggle completion status with file IDs

```bash
# Toggle completion status with file IDs
curl -X POST \
  "http://localhost:8000/api/v1/case-management/cases/CASE-12345/case-checklists/123/toggle_completion/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "file_ids": ["file-id-1", "file-id-2", "file-id-3"]
  }'
```

## Frontend Implementation Notes

When implementing this feature in the frontend, you'll need to:

1. Update the checklist item component to display associated files
2. Add UI for selecting files to associate with a checklist item
3. Update the API calls to include file_ids when updating checklist items

Example TypeScript interface for the checklist item:

```typescript
interface ChecklistItem {
  id: number;
  name: string;
  description: string;
  is_completed: boolean;
  completed_at: string | null;
  completed_by: string | null;
  organization_status: number;
  file_ids: string[]; // Array of file IDs
}
```

Example React component for displaying associated files:

```tsx
import React from 'react';
import { FileIcon, Download } from 'lucide-react';

interface ChecklistFileListProps {
  fileIds: string[];
  onViewFile: (fileId: string) => void;
}

const ChecklistFileList: React.FC<ChecklistFileListProps> = ({ fileIds, onViewFile }) => {
  if (!fileIds || fileIds.length === 0) {
    return null;
  }

  return (
    <div className="mt-2">
      <h4 className="text-sm font-medium">Associated Files:</h4>
      <ul className="mt-1 space-y-1">
        {fileIds.map((fileId) => (
          <li key={fileId} className="flex items-center text-sm">
            <FileIcon className="h-4 w-4 mr-2" />
            <span className="flex-1 truncate">{fileId}</span>
            <button
              onClick={() => onViewFile(fileId)}
              className="ml-2 text-blue-500 hover:text-blue-700"
            >
              <Download className="h-4 w-4" />
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ChecklistFileList;
```
