import logging

from django.db.models import Q

from case_management.models import Case

logger = logging.getLogger(__name__)


class CaseSearchService:
    """
    Service class for handling case searches with improved performance and reliability.
    This separates the search logic from the view for better maintainability.
    """

    def __init__(self, organization):
        self.organization = organization
        self.logger = logging.getLogger(__name__)

    def search_cases(self, search_query):
        """
        Search for cases matching the given query across multiple fields.
        Returns a queryset of matching cases.
        """
        if not search_query:
            return Case.objects.filter(organization=self.organization)

        self.logger.info(f"Searching for cases with query: '{search_query}'")

        # Split the search query into parts for full name search
        search_query = search_query.strip()
        name_parts = search_query.split()

        # Create separate querysets for each search type
        results = []

        # 1. Search in client names
        # it is working properly, comment out before pushing to production
        results.append(self._search_client_names(search_query, name_parts))

        # 2. Search in defendant names
        results.append(self._search_defendant_names(search_query, name_parts))

        # # 3. Search in case name
        # results.append(self._search_case_name(search_query))

        # 4. Search in insurances
        results.append(self._search_insurances(search_query))

        # 5. Search in health insurances
        results.append(self._search_health_insurances(search_query))

        # 6. Search in other fields
        results.append(self._search_other_fields(search_query))

        # Combine all results by collecting case IDs
        case_ids = set()
        for queryset in results:
            for case in queryset:
                case_ids.add(case.id)

        self.logger.info(f"Found {len(case_ids)} unique cases matching the search query")

        # Return a queryset with all the matching cases
        if case_ids:
            return Case.objects.filter(id__in=case_ids)
        else:
            return Case.objects.none()

    def _search_client_names(self, search_query, name_parts):
        """Search in client names."""
        client_filter = Q()

        # Basic search in first name and last name
        client_filter |= Q(client_basic_details__first_name__icontains=search_query)
        client_filter |= Q(client_basic_details__last_name__icontains=search_query)

        # Full name search if we have multiple parts
        if len(name_parts) >= 2:
            for i in range(len(name_parts)):
                first_part = name_parts[i]
                for j in range(len(name_parts)):
                    if i != j:
                        last_part = name_parts[j]
                        client_filter |= Q(client_basic_details__first_name__icontains=first_part) & Q(
                            client_basic_details__last_name__icontains=last_part
                        )

        results = Case.objects.filter(organization=self.organization).filter(client_filter).distinct()
        self.logger.info(f"Client name search found {results.count()} cases")
        return results

    def _search_defendant_names(self, search_query, name_parts):
        """Search in defendant names."""
        defendant_filter = Q()

        # Basic search in first name and last name
        defendant_filter |= Q(v2_defendants__first_name__icontains=search_query)
        defendant_filter |= Q(v2_defendants__last_name__icontains=search_query)

        # Full name search if we have multiple parts
        if len(name_parts) >= 2:
            for i in range(len(name_parts)):
                first_part = name_parts[i]
                for j in range(len(name_parts)):
                    if i != j:
                        last_part = name_parts[j]
                        defendant_filter |= Q(v2_defendants__first_name__icontains=first_part) & Q(
                            v2_defendants__last_name__icontains=last_part
                        )

        results = Case.objects.filter(organization=self.organization).filter(defendant_filter).distinct()
        self.logger.info(f"Defendant name search found {results.count()} cases")
        return results

    def _search_case_name(self, search_query):
        """Search in case name."""
        results = Case.objects.filter(organization=self.organization, case_name__icontains=search_query).distinct()
        self.logger.info(f"Case name search found {results.count()} cases")
        return results

    def _search_insurances(self, search_query):
        """Search in all insurance fields (client and defendant)."""
        # First, try to search client insurance fields separately
        try:
            # Search in client insurance fields - only use fields we know exist
            client_insurance_filter = (
                Q(insurances__claim_number__icontains=search_query)
                | Q(insurances__policy_number__icontains=search_query)
                | Q(insurances__medpay_claim_number__icontains=search_query)
            )

            # Execute client insurance query
            client_results = (
                Case.objects.filter(organization=self.organization).filter(client_insurance_filter).distinct()
            )

            self.logger.info(f"Client insurance search found {client_results.count()} cases")
        except Exception as e:
            self.logger.error(f"Error in client insurance search: {str(e)}")
            client_results = Case.objects.none()

        # No insurance company name search
        company_results = Case.objects.none()

        # Next, try to search defendant insurance fields separately
        try:
            # Search in defendant insurance fields - only use fields we know exist
            defendant_insurance_filter = Q(v2_defendants__insurances__claim_number__icontains=search_query) | Q(
                v2_defendants__insurances__policy_number__icontains=search_query
            )

            # Execute defendant insurance query
            defendant_results = (
                Case.objects.filter(organization=self.organization).filter(defendant_insurance_filter).distinct()
            )

            self.logger.info(f"Defendant insurance search found {defendant_results.count()} cases")
        except Exception as e:
            self.logger.error(f"Error in defendant insurance search: {str(e)}")
            defendant_results = Case.objects.none()

        # Combine all results by collecting case IDs
        case_ids = set()

        # Add IDs from client insurance results
        for case in client_results:
            case_ids.add(case.id)

        # Add IDs from insurance company results
        for case in company_results:
            case_ids.add(case.id)

        # Add IDs from defendant insurance results
        for case in defendant_results:
            case_ids.add(case.id)

        # Create a new queryset with all the collected IDs
        if case_ids:
            results = Case.objects.filter(id__in=case_ids)
        else:
            results = Case.objects.none()

        self.logger.info(f"Combined insurance search found {results.count()} cases")

        return results

    def _search_health_insurances(self, search_query):
        """Search in health insurance fields."""
        results = Case.objects.filter(
            organization=self.organization, health_insurances__file_number__icontains=search_query
        ).distinct()
        self.logger.info(f"Health insurance search found {results.count()} cases")
        return results

    def _search_other_fields(self, search_query):
        """Search in other miscellaneous fields."""
        other_filter = (
            # Court details
            Q(court_details__case_number__icontains=search_query)
            |
            # Phone numbers
            Q(client_contact_details__phone_number_1__icontains=search_query)
            | Q(client_contact_details__phone_number_2__icontains=search_query)
            | Q(client_contact_details__phone_number_3__icontains=search_query)
            | Q(v2_defendants__phone__icontains=search_query)
            | Q(v2_defendants__cell__icontains=search_query)
            | Q(v2_defendants__work_phone__icontains=search_query)
            |
            # Incident details
            Q(incident_details__report_number__icontains=search_query)
            |
            # Events
            Q(events__title__icontains=search_query)
            | Q(events__event_type__icontains=search_query)
            | Q(events__description__icontains=search_query)
        )
        results = Case.objects.filter(organization=self.organization).filter(other_filter).distinct()
        self.logger.info(f"Other fields search found {results.count()} cases")
        return results
