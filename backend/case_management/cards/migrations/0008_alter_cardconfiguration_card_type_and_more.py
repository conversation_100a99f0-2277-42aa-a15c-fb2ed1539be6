# Generated by Django 5.1.3 on 2025-05-20 10:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('case_management_cards', '0007_alter_cardconfiguration_card_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cardconfiguration',
            name='card_type',
            field=models.CharField(choices=[('CASE_AGE', 'Case Age'), ('CLIENT_AGE', 'Client Age'), ('PENDING_STATUTE', 'Pending Statute'), ('THIRD_PARTY_POLICY_LIMIT', 'Third Party Policy Limits'), ('ATTORNEY_LIENS', 'Attorney Liens'), ('MISC_LIENS', 'Miscellaneous Liens'), ('POLICE_REPORT_STATUS', 'Police Report Status'), ('PROPERTY_DAMAGE', 'Property Damage'), ('LOST_WAGES', 'Lost Wages'), ('MEDICAL_TREATMENT', 'Medical Treatment'), ('HEALTH_INSURANCE_AND_PIP', 'Health Insurance and PIP'), ('CASE_COST', 'Case Cost')], max_length=50),
        ),
        migrations.AlterField(
            model_name='systemcard',
            name='card_type',
            field=models.CharField(choices=[('CASE_AGE', 'Case Age'), ('CLIENT_AGE', 'Client Age'), ('PENDING_STATUTE', 'Pending Statute'), ('THIRD_PARTY_POLICY_LIMIT', 'Third Party Policy Limits'), ('ATTORNEY_LIENS', 'Attorney Liens'), ('MISC_LIENS', 'Miscellaneous Liens'), ('POLICE_REPORT_STATUS', 'Police Report Status'), ('PROPERTY_DAMAGE', 'Property Damage'), ('LOST_WAGES', 'Lost Wages'), ('MEDICAL_TREATMENT', 'Medical Treatment'), ('HEALTH_INSURANCE_AND_PIP', 'Health Insurance and PIP'), ('CASE_COST', 'Case Cost')], max_length=50),
        ),
    ]
