from decimal import Decimal
from typing import TypedDict
from django.db.models import Sum
import logging

from case_management.v2.models import Case, CaseCost

logger = logging.getLogger(__name__)

class CaseCostData(TypedDict):
    pending_amount: Decimal
    paid_amount: Decimal
    total_amount: Decimal
    cost_count: int


def calculate_case_cost(case: Case) -> CaseCostData:
    """
    Calculate the pending and paid amounts for a case.
    
    Args:
        case: The case instance to calculate costs for
        
    Returns:
        Dict containing pending_amount, paid_amount, total_amount and cost_count
    """
    # Get all non-void case costs for this case
    case_costs = CaseCost.objects.filter(case=case, is_void=False)
    
    # Debug: Print all case costs
    logger.info(f"Calculating costs for case {case.id}")
    logger.info(f"Total case costs found: {case_costs.count()}")
    
    # List all case costs for debugging
    for cost in case_costs:
        logger.info(f"Cost ID: {cost.id}, Amount: {cost.amount}, Status: {cost.status}")
    
    # Calculate totals using aggregate
    # Pending includes both PENDING and APPROVED statuses
    pending_costs = case_costs.filter(status__in=['PENDING', 'APPROVED'])
    paid_costs = case_costs.filter(status='PAID')
    
    logger.info(f"Pending costs count: {pending_costs.count()}")
    logger.info(f"Paid costs count: {paid_costs.count()}")
    
    pending_amount = pending_costs.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    paid_amount = paid_costs.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    logger.info(f"Calculated pending amount: {pending_amount}")
    logger.info(f"Calculated paid amount: {paid_amount}")
    
    total_amount = pending_amount + paid_amount
    logger.info(f"Total amount: {total_amount}")
    
    return CaseCostData(
        pending_amount=pending_amount,
        paid_amount=paid_amount,
        total_amount=total_amount,
        cost_count=case_costs.count()
    ) 