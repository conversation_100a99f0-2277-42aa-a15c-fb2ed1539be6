# Generated by Django 5.1.3 on 2025-05-05 09:23

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('case_management', '0058_add_performance_indexes'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_organiz_cd597d_idx',
            old_name='case_org_idx',
        ),
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_is_mult_bc696c_idx',
            old_name='case_multiple_client_idx',
        ),
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_created_0c2c9f_idx',
            old_name='case_created_by_idx',
        ),
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_organiz_5a5852_idx',
            old_name='case_org_created_idx',
        ),
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_organiz_aab3f0_idx',
            old_name='case_org_status_idx',
        ),
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_organiz_fcbde5_idx',
            old_name='case_org_multiple_idx',
        ),
        migrations.RenameIndex(
            model_name='case',
            new_name='case_manage_organiz_5e4958_idx',
            old_name='case_org_created_by_idx',
        ),
        migrations.RenameIndex(
            model_name='casenote',
            new_name='case_manage_created_2ef84e_idx',
            old_name='casenote_created_at_idx',
        ),
        migrations.RenameIndex(
            model_name='casenote',
            new_name='case_manage_created_268a44_idx',
            old_name='casenote_created_by_idx',
        ),
        migrations.RenameIndex(
            model_name='casenote',
            new_name='case_manage_note_fo_dd17bb_idx',
            old_name='casenote_note_for_idx',
        ),
        migrations.RenameIndex(
            model_name='casenote',
            new_name='case_manage_case_id_20bac7_idx',
            old_name='casenote_case_note_for_idx',
        ),
        migrations.RenameIndex(
            model_name='casenote',
            new_name='case_manage_case_id_c7d2ca_idx',
            old_name='casenote_case_defendant_idx',
        ),
        migrations.RenameIndex(
            model_name='casenote',
            new_name='case_manage_case_id_165ed4_idx',
            old_name='casenote_case_note_def_idx',
        ),
    ]
