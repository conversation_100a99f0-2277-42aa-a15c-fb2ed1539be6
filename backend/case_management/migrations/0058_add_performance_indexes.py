from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("case_management", "0057_case_accident_recreation_video_url"),
    ]

    operations = [
        # Add indexes to Case model
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["organization"], name="case_org_idx"),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["is_multiple_client_involved"], name="case_multiple_client_idx"),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["created_by"], name="case_created_by_idx"),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["organization", "created_at"], name="case_org_created_idx"),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["organization", "status"], name="case_org_status_idx"),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["organization", "is_multiple_client_involved"], name="case_org_multiple_idx"),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(fields=["organization", "created_by"], name="case_org_created_by_idx"),
        ),
        # Add indexes to CaseNote model
        migrations.AddIndex(
            model_name="casenote",
            index=models.Index(fields=["created_at"], name="casenote_created_at_idx"),
        ),
        migrations.AddIndex(
            model_name="casenote",
            index=models.Index(fields=["created_by"], name="casenote_created_by_idx"),
        ),
        migrations.AddIndex(
            model_name="casenote",
            index=models.Index(fields=["note_for"], name="casenote_note_for_idx"),
        ),
        migrations.AddIndex(
            model_name="casenote",
            index=models.Index(fields=["case", "note_for"], name="casenote_case_note_for_idx"),
        ),
        migrations.AddIndex(
            model_name="casenote",
            index=models.Index(fields=["case", "defendant"], name="casenote_case_defendant_idx"),
        ),
        migrations.AddIndex(
            model_name="casenote",
            index=models.Index(fields=["case", "note_for", "defendant"], name="casenote_case_note_def_idx"),
        ),
    ]
