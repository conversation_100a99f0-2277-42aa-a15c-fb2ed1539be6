#!/bin/bash
# stop_celery_prod.sh - Stop Celery services in production

# Set environment to production
export COMPOSE_FILE="docker-compose.yml:docker-compose.prod.yml"

echo "===== STOPPING CELERY SERVICES IN PRODUCTION ====="

# Stop Celery services
echo "Stopping celery_worker..."
docker-compose stop celery_worker
echo "Stopping celery_beat..."
docker-compose stop celery_beat
echo "Stopping flower..."
docker-compose stop flower

# Remove Celery containers to ensure they don't restart
echo "Removing celery_worker container..."
docker-compose rm -f celery_worker
echo "Removing celery_beat container..."
docker-compose rm -f celery_beat
echo "Removing flower container..."
docker-compose rm -f flower

echo "===== CELERY SERVICES STOPPED ====="

# Show running services
echo ""
echo "Running services:"
docker-compose ps

echo ""
echo "IMPORTANT: Celery services have been stopped and removed."
echo "To restart the application without Celery, use: ./start_services_no_celery.sh"
