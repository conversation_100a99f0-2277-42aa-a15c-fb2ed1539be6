import logging

from django.core.management.base import BaseCommand

from alarm_service.tasks.check_discovery_deadlines import check_discovery_deadlines
from alarm_service.tasks.check_minor_fee_agreements import check_minor_fee_agreements
from alarm_service.tasks.check_negotiation_deadlines import check_negotiation_deadlines
from alarm_service.tasks.check_statute_limitations import check_statute_limitations
from alarm_service.tasks.check_uim_negotiation_deadlines import check_uim_negotiation_deadlines
from alarm_service.tasks.check_upcoming_events import check_upcoming_events

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Run all alarm checks to test active case filtering"

    def add_arguments(self, parser):
        parser.add_argument(
            "--check",
            type=str,
            help="Specify a specific check to run (statute, discovery, minor, negotiation, uim, events)",
        )

    def handle(self, *args, **options):
        specific_check = options.get("check")

        if specific_check:
            self.run_specific_check(specific_check)
        else:
            self.run_all_checks()

    def run_specific_check(self, check_name):
        check_name = check_name.lower()

        if check_name == "statute":
            self.stdout.write("Running statute of limitations check...")
            result = check_statute_limitations()
            self.stdout.write(f"Created {result} statute of limitations alarms")

        elif check_name == "discovery":
            self.stdout.write("Running discovery deadlines check...")
            result = check_discovery_deadlines()
            self.stdout.write(f"Created {result} discovery deadline alarms")

        elif check_name == "minor":
            self.stdout.write("Running minor fee agreements check...")
            try:
                result = check_minor_fee_agreements()
                self.stdout.write(f"Created {result} minor fee agreement tasks")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error in minor fee agreements check: {str(e)}"))

        elif check_name == "negotiation":
            self.stdout.write("Running negotiation deadlines check...")
            result = check_negotiation_deadlines()
            self.stdout.write(f"Created {result} negotiation deadline tasks")

        elif check_name == "uim":
            self.stdout.write("Running UIM negotiation deadlines check...")
            result = check_uim_negotiation_deadlines()
            self.stdout.write(f"Created {result} UIM negotiation deadline tasks")

        elif check_name == "events":
            self.stdout.write("Running upcoming events check...")
            result = check_upcoming_events()
            self.stdout.write(f"Created {result} upcoming event alarms")

        else:
            self.stdout.write(
                self.style.ERROR(
                    f"Unknown check: {check_name}. Valid options are: statute, discovery, minor, negotiation, uim, events"
                )
            )

    def run_all_checks(self):
        self.stdout.write("Running all alarm checks...")

        # Run statute of limitations check
        self.stdout.write("Running statute of limitations check...")
        statute_result = check_statute_limitations()
        self.stdout.write(f"Created {statute_result} statute of limitations alarms")

        # Run discovery deadlines check
        self.stdout.write("Running discovery deadlines check...")
        discovery_result = check_discovery_deadlines()
        self.stdout.write(f"Created {discovery_result} discovery deadline alarms")

        # Run minor fee agreements check
        self.stdout.write("Running minor fee agreements check...")
        try:
            minor_result = check_minor_fee_agreements()
            self.stdout.write(f"Created {minor_result} minor fee agreement tasks")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error in minor fee agreements check: {str(e)}"))
            minor_result = 0

        # Run negotiation deadlines check
        self.stdout.write("Running negotiation deadlines check...")
        negotiation_result = check_negotiation_deadlines()
        self.stdout.write(f"Created {negotiation_result} negotiation deadline tasks")

        # Run UIM negotiation deadlines check
        self.stdout.write("Running UIM negotiation deadlines check...")
        uim_result = check_uim_negotiation_deadlines()
        self.stdout.write(f"Created {uim_result} UIM negotiation deadline tasks")

        # Run upcoming events check
        self.stdout.write("Running upcoming events check...")
        events_result = check_upcoming_events()
        self.stdout.write(f"Created {events_result} upcoming event alarms")

        # Summary
        # Handle None values by converting to 0
        statute_result = statute_result or 0
        discovery_result = discovery_result or 0
        minor_result = minor_result or 0
        negotiation_result = negotiation_result or 0
        uim_result = uim_result or 0
        events_result = events_result or 0

        total = statute_result + discovery_result + minor_result + negotiation_result + uim_result + events_result
        self.stdout.write(self.style.SUCCESS(f"All checks completed. Created {total} total alarms/tasks."))
