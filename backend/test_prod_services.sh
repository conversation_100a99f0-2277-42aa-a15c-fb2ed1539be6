#!/bin/bash
# test_prod_services.sh - Test the production services startup

# Set the environment to production
ENV="prod"

# Source the environment setup script
source ./setup_env.sh $ENV

echo "===== TESTING PRODUCTION SERVICES STARTUP ====="

# Stop and remove all containers
echo "Stopping and removing all containers..."
docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml down

# Clean up any existing containers to avoid conflicts
docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml rm -f

# Start all services with the prod profile
echo "Starting all production services with --profile prod..."
docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml --profile prod up -d

# Show running services
echo ""
echo "Running services:"
docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml ps

# Check if all expected services are running
echo ""
echo "Checking if all expected services are running..."
EXPECTED_SERVICES=("redis" "django1" "django2" "django3" "django4" "django5" "nginx" "celery_worker" "celery_beat" "flower" "django_websocket")
ALL_RUNNING=true

for service in "${EXPECTED_SERVICES[@]}"; do
    if ! docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml ps | grep -q "$service.*Up"; then
        echo "ERROR: Service $service is not running!"
        ALL_RUNNING=false
    else
        echo "Service $service is running."
    fi
done

if [ "$ALL_RUNNING" = true ]; then
    echo ""
    echo "SUCCESS: All expected services are running!"
else
    echo ""
    echo "ERROR: Some services failed to start. Check the logs for more details."
    echo "You can check the logs with: docker-compose -p alphalaw -f docker-compose.yml -f docker-compose.prod.yml logs"
fi

echo ""
echo "===== TEST COMPLETED ====="
