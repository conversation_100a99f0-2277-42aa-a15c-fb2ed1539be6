# Generated by Django 5.1.3 on 2025-05-04 19:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketing_management', '0024_alter_partnercontact_profile_photo'),
        ('users', '0038_organization_onedrive_personal_access_token_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='engagementtype',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='engagement_types', to='users.organization'),
        ),
        migrations.AddField(
            model_name='marketinggoal',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='marketing_goals', to='users.organization'),
        ),
    ]
