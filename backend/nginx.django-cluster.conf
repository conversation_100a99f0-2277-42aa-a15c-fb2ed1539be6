# Django Cluster Configuration
# This configuration handles load balancing across multiple Django instances
# Used in production environment with docker-compose.prod.yml

upstream django_cluster {
    server django1:8080;
    server django2:8080;
    server django3:8080;
    server django4:8080;
    server django5:8080;
    server django6:8080;
    server django7:8080;
    server django8:8080;
    server django9:8080;
    server django10:8080;
    server django11:8080;
    server django12:8080;
    server django13:8080;
    server django14:8080;
    server django15:8080;
}

server {
    listen 8080;
    server_name localhost main-backend.alphalaw.io;

    # Get the forwarded protocol from the external Nginx
    set $forwarded_proto $http_x_forwarded_proto;
    if ($http_x_forwarded_proto = '') {
        set $forwarded_proto $scheme;
    }

    # Main application proxy
    location / {
        proxy_pass http://django_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $forwarded_proto;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Handle trailing slashes to prevent unnecessary redirects
        proxy_redirect off;
    }

    # API endpoints - prevent trailing slash redirects
    location /api/ {
        proxy_pass http://django_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $forwarded_proto;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Prevent redirects
        proxy_redirect off;
    }

    # Static files
    location /static/ {
        alias /app/staticfiles/;
        expires max;
        add_header Cache-Control public;
        access_log off;
        include /etc/nginx/mime.types;
        try_files $uri $uri/ =404;
    }

    # Media files
    location /media/ {
        alias /app/media/;
        expires max;
        add_header Cache-Control public;
        access_log off;
        include /etc/nginx/mime.types;
        try_files $uri $uri/ =404;
    }

    # Health check endpoint
    location = /health/ {
        add_header Content-Type text/plain;
        return 200 'OK';
        access_log off;
    }

    # Forward health check to Django if needed
    location /health/ {
        proxy_pass http://django_cluster/health/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $forwarded_proto;
        access_log off;
    }

    client_max_body_size 100M;
}