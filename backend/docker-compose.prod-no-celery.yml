version: '3.8'

# Production environment configuration (4 cores, 16GB RAM) without Celery services
services:
  django:
    environment:
      - GUNICORN_WORKERS=3
      - GUNICORN_THREADS=6
      - DJANGO_ENV=production-blf
    deploy:
      resources:
        limits:
          memory: 6g
          cpus: '1.5'

  redis:
    environment:
      - REDIS_MEMORY_LIMIT=2g
    # Use direct command line without loading the config file
    command: redis-server --maxmemory 2gb --maxmemory-policy volatile-lru --appendonly no --save ""
    deploy:
      resources:
        limits:
          memory: 2g
          cpus: '0.75'
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    # Override the volumes from base docker-compose.yml to remove persistent storage
    volumes: []

  django_websocket:
    deploy:
      resources:
        limits:
          memory: 2g
          cpus: '0.25'
