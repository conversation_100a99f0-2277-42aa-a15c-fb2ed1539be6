DJANGO_ENV=
DEBUG=True
# Database Configuration (SQLite)
# Note: SQLite configuration is handled in settings.py

# Database Configuration (PostgreSQL)
DB_USER=
DB_PASSWORD=
DB_HOST=
DB_PORT=
DB_NAME=

# Frontend Configuration
FRONTEND_DOMAIN=


AWS_REGION_NAME=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
SECRET_KEY=
AWS_STORAGE_BUCKET_NAME=

AWS_SES_SMTP_USER=XXXXXXXXXXXXXXXXXX
AWS_SES_SMTP_PASSWORD=XXXXXXXXXXXXXXXXXX
AWS_SES_REGION_ENDPOINT=email-smtp.us-east-1.amazonaws.com
AWS_SES_SENDER_EMAIL=<EMAIL>
BREVO_API_KEY=XXXXXXXXXXXXXXXXXX
BREVO_SMTP_SERVER=smtp-relay.brevo.com
BREVO_LOGIN=XXXXXXXXXXXXXXXXXX
BREVO_PORT=587
BREVO_SENDER_EMAIL=<EMAIL>
BREVO_SENDER_NAME=AlphaLaw

SLACK_BOT_TOKEN=

NYLAS_CLIENT_ID=
NYLAS_API_KEY=
NYLAS_API_URI=
NYLAS_CALLBACK_URI=


# Twilio Configuration
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=

# Note: Organization-specific phone numbers should be stored in OrganizationProfile.twilio_phone_number field
# Example: +***********


AI_BACKEND_URL=
AI_BACKEND_S3_DOCUMENT_INDEXING_URL=
AI_BACKEND_AUTH_TOKEN=


ONEDRIVE_CLIENT_ID=
ONEDRIVE_CLIENT_SECRET=
ONEDRIVE_REDIRECT_URI=
ONEDRIVE_AUTHORITY=



# DocumentSO Configuration
DOCUMENSO_API_KEY=
DOCUMENSO_API_URL=

SENTRY_DSN=

FLOWER_USER=admin
FLOWER_PASSWORD=alphalaw_secure_2024


REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

CONFLICT_CHECK_INTERVAL=60
CONFLICT_RESOLUTION_INTERVAL=60


MISTRAL_API_KEY=3Gd6f6z0CVmsPOW8WObjjp9hI4qs8rxU

AWS_TEXTRACT_BUCKET_NAME=
AWS_TEXTRACT_ACCESS_KEY_ID=
AWS_TEXTRACT_SECRET_ACCESS_KEY=
AWS_TEXTRACT_REGION_NAME=



PINECONE_API_KEY=
OPENAI_API_KEY=
ANTHROPIC_KEY=
ANTHROPIC_KEY_BACKUP=
OPENAI_API_KEY_BACKUP=
ANTHROPIC_KEY_BACKUP=

ALARM_CHECK_MINUTE=14
ALARM_CHECK_HOUR=3

# System Email Parser Configuration
SYSTEM_EMAIL_PARSER=
SYSTEM_EMAIL_PARSER_ENABLED=

GOOGLE_MAPS_API_KEY=

# Other services configured for dev environment