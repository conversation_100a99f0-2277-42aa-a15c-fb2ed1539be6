# Redis Performance Optimization for Production

This document outlines the changes made to optimize Redis performance in production by disabling persistence.

## Changes Made

### 1. Updated Redis Service in Production (`docker-compose.prod.yml`)

```yaml
redis:
  environment:
    - REDIS_MEMORY_LIMIT=2g
  # Use direct command line without loading the config file
  command: redis-server --maxmemory 2gb --maxmemory-policy volatile-lru --appendonly no --save ""
  deploy:
    resources:
      limits:
        memory: 2g
        cpus: '0.75'
  ulimits:
    nofile:
      soft: 65536
      hard: 65536
  # Override the volumes from base docker-compose.yml to remove persistent storage
  volumes: []
```

## Why These Changes Help

1. **Disabled Persistence**: Redis no longer writes to disk for persistence, eliminating disk I/O bottlenecks.
2. **Memory-Only Storage**: By removing all volume mounts, Redis operates entirely in memory.
3. **Optimized Memory Policy**: Changed from `allkeys-lru` to `volatile-lru` to only evict keys with TTL.
4. **Increased File Descriptors**: Higher limits for file descriptors to handle more connections.

## Important Notes

- **Data Loss on Restart**: With persistence disabled, all data in Redis will be lost when the container restarts.
- **Acceptable Use Cases**: This configuration is appropriate for:
  - Session storage
  - Caching
  - Temporary data
  - Task queues where tasks can be safely requeued
- **Not Suitable For**: Any data that must be preserved across restarts.

## Monitoring Redis Performance

Monitor Redis performance with these commands:

```bash
# Check network stats
docker-compose exec redis redis-cli info | grep network

# Check client connections
docker-compose exec redis redis-cli info | grep connected_clients

# Check memory usage
docker-compose exec redis redis-cli info | grep used_memory_human

# Check slow operations
docker-compose exec redis redis-cli slowlog get 10

# Use the monitoring script
./monitor_redis.sh
```

## Reverting Changes

If persistence is needed, revert these changes by:

1. Restoring the volumes section in `docker-compose.prod.yml`:
   ```yaml
   volumes:
     - redis_data:/data
     - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
   ```

2. Changing the Redis command to re-enable persistence:
   ```
   command: redis-server --appendonly yes --save 900 1 --save 300 10 --save 60 10000
   ```
