import os
import django
import json
import requests
from django.conf import settings

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from users.models import User, NylasIntegration
from users.nylas_integration.config import NYLAS_CONFIG

# Get the system user
system_user = User.objects.get(email=settings.SYSTEM_EMAIL_PARSER)
print(f"Found system user: {system_user.email}")

# Get the Nylas integration for the system user
integration = NylasIntegration.objects.get(user=system_user)
print(f"Found Nylas integration for system user: {integration.email} (Grant ID: {integration.grant_id})")

# Set up API request
api_key = NYLAS_CONFIG.get("api_key")
api_uri = NYLAS_CONFIG.get("api_uri", "https://api.us.nylas.com")

# Get messages with subject containing 250072346
messages_url = f"{api_uri}/v3/grants/{integration.grant_id}/messages"
headers = {
    "Accept": "application/json, application/gzip",
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

params = {
    "limit": 10,
    "subject": "250072346"
}

print(f"Fetching messages with subject containing 250072346...")
response = requests.get(messages_url, headers=headers, params=params)

if response.status_code == 200:
    message_data = response.json()
    messages = message_data.get("data", [])
    print(f"Found {len(messages)} messages with subject containing 250072346")
    
    for message in messages:
        message_id = message.get("id")
        subject = message.get("subject")
        print(f"\nMessage ID: {message_id}")
        print(f"Subject: {subject}")
        
        # Fetch detailed message with headers
        detailed_message_url = f"{api_uri}/v3/grants/{integration.grant_id}/messages/{message_id}/"
        detailed_params = {"fields": "include_headers"}
        
        detailed_response = requests.get(detailed_message_url, headers=headers, params=detailed_params)
        
        if detailed_response.status_code == 200:
            detailed_message = detailed_response.json()
            if "data" in detailed_message and "headers" in detailed_message["data"]:
                message_headers = detailed_message["data"]["headers"]
                print(f"Headers type: {type(message_headers)}")
                print(f"Headers: {json.dumps(message_headers, indent=2)}")
                
                # Look for Envelope-To header
                for header in message_headers:
                    if isinstance(header, dict) and "name" in header and "value" in header:
                        if header["name"] == "Envelope-To" or header["name"] == "Envelope-to":
                            print(f"Found Envelope-To header: {header['value']}")
                            if "@bcc.alphalaw.io" in header["value"].lower():
                                import re
                                routing_match = re.search(r"([a-zA-Z0-9]+)@bcc\.alphalaw\.io", header["value"], re.IGNORECASE)
                                if routing_match:
                                    routing_id = routing_match.group(1)
                                    case_id = f"CASE-{routing_id.upper()}"
                                    print(f"Extracted case ID: {case_id}")
            else:
                print("No headers found in detailed message")
        else:
            print(f"Failed to fetch detailed message: {detailed_response.status_code}")
else:
    print(f"Failed to fetch messages: {response.status_code}")
