#!/usr/bin/env python
import os

import django

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

# Now import Django-related modules
from django.contrib.auth import get_user_model
from django.db import transaction

from users.models import Organization

# Get the User model properly using Django's get_user_model
User = get_user_model()

ORG_ID = 11  # <-- CHANGE THIS TO YOUR TARGET ORG ID

user_data = [
    ("<PERSON>", "<EMAIL>", "ali@1234"),
    ("<PERSON>", "<EMAIL>", "ann@1234"),
    ("<PERSON> Mascorro", "<EMAIL>", "ash@1234"),
    ("Chels<PERSON>", "<EMAIL>", "che@1234"),
    ("<PERSON>", "<EMAIL>", "dan@1234"),
    ("<PERSON><PERSON>", "<EMAIL>", "deg@1234"),
    ("<PERSON>", "<EMAIL>", "dia@1234"),
    ("Dyords Tiglao", "<EMAIL>", "dyo@1234"),
    ("Hans Chavez", "<EMAIL>", "han@1234"),
    ("Isaac Nair", "<EMAIL>", "isa@1234"),
    ("Jaeda Matheson", "<EMAIL>", "jae@1234"),
    ("Jake Dawe", "<EMAIL>", "jak@1234"),
    ("Joshua Brumley", "<EMAIL>", "jos@1234"),
    ("Karla Escorcia", "<EMAIL>", "kar@1234"),
    ("Kimberlee Remec", "<EMAIL>", "kim@1234"),
    ("Marcos Banuelos", "<EMAIL>", "mar@1234"),
    ("Prerana Chand", "<EMAIL>", "pre@1234"),
    ("Jamie Hudspeth", "<EMAIL>", "jam@1234"),
    ("Simone Jensen", "<EMAIL>", "sim@1234"),
    ("Sofia Biberfeld", "<EMAIL>", "sof@1234"),
    ("Taylor Bolle", "<EMAIL>", "tay@1234"),
    ("Adel Sadik", "<EMAIL>", "ade@1234"),
    ("Jora Singh", "<EMAIL>", "jor@1234"),
    ("Gelila Ayenew", "<EMAIL>", "gel@1234"),
    ("Chyna DeBoer", "<EMAIL>", "chy@1234"),
    ("Jackson Hudgins", "<EMAIL>", "jac@1234"),
    ("Gabriella Scalera", "<EMAIL>", "gab@1234"),
    ("Dmitry Spiridonov", "<EMAIL>", "dmi@1234"),
    ("Noah Spyra", "<EMAIL>", "noa@1234"),
    ("Jasmin Striedinger", "<EMAIL>", "jas@1234"),
    ("Andrea Vasquez", "<EMAIL>", "and@1234"),
    ("Esther Aguilera", "<EMAIL>", "est@1234"),
    ("Icee Caspe", "<EMAIL>", "ice@1234"),
    ("Jaizlyn Carino", "<EMAIL>", "jai@1234"),
    ("Jorge Hernandez", "<EMAIL>", "jor@1234"),
    ("Samm Alyson", "<EMAIL>", "sam@1234"),
    ("Irene Guzman", "<EMAIL>", "ire@1234"),
    ("Shyla Loto", "<EMAIL>", "shy@1234"),
]

admin_names = {"Joshua", "Diana", "Jake", "Dan"}

try:
    # Get the organization
    org = Organization.objects.get(id=ORG_ID)
    print(f"Found organization: {org.name} (ID: {org.id})")

    # Process each user
    for name, email, password in user_data:
        try:
            with transaction.atomic():
                first_name = name.split()[0]
                last_name = " ".join(name.split()[1:]) if len(name.split()) > 1 else ""
                role = "admin" if first_name in admin_names else "general"

                # Normalize email to lowercase
                email = email.lower()

                try:
                    # Try to get existing user
                    user = User.objects.get(email=email)
                    print(f"User already exists: {name} ({email}) - updating")

                    # Update user fields
                    user.name = name
                    user.first_name = first_name
                    user.last_name = last_name
                    user.role = role

                    # Set password properly
                    user.set_password(password)
                    user.save()

                    # Remove from all organizations
                    user.organizations.clear()
                    print(f"Removed {name} from all organizations")

                except User.DoesNotExist:
                    # Create new user
                    print(f"Creating new user: {name} ({email}) as {role}")
                    user = User.objects.create(
                        username=email,
                        email=email,
                        name=name,
                        first_name=first_name,
                        last_name=last_name,
                        role=role,
                        is_active=True,
                        verified=True,
                    )
                    # Set password properly
                    user.set_password(password)
                    user.save()

                # Add to the specified organization
                user.organizations.add(org)
                print(f"Added {name} ({email}) to org {org.id}")

        except Exception as e:
            print(f"Error processing user {name} ({email}): {str(e)}")
            continue

except Organization.DoesNotExist:
    print(f"Organization with ID {ORG_ID} not found")
except Exception as e:
    print(f"Unexpected error: {str(e)}")
