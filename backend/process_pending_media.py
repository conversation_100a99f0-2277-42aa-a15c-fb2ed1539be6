#!/usr/bin/env python
import os
import sys

import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from users.models import TwilioMessage
from users.twilio_integration.tasks import process_twilio_media_attachments


def process_pending_media():
    """
    Process all pending Twilio media attachments for organization 12
    """
    # Get all pending media messages for organization 12
    pending_messages = TwilioMessage.objects.filter(media_processing_status="pending", organization_id=12)

    count = pending_messages.count()
    print(f"Found {count} pending media messages for organization 12")

    # Process each message
    for i, message in enumerate(pending_messages, 1):
        print(f"Processing message {i}/{count}: {message.id} (SID: {message.message_sid})")
        # Trigger the task synchronously for immediate processing
        process_twilio_media_attachments(message.id)

    print("Completed processing pending media messages")

    # Check final status
    status_counts = (
        TwilioMessage.objects.filter(organization_id=12)
        .values("media_processing_status")
        .annotate(count=django.db.models.Count("id"))
    )

    print("Updated media processing status statistics:")
    print(status_counts)


if __name__ == "__main__":
    process_pending_media()
