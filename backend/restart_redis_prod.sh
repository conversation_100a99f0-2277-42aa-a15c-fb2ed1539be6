#!/bin/bash
# restart_redis_prod.sh - Restart Redis with optimized production settings

# Set environment to production
export COMPOSE_FILE="docker-compose.yml:docker-compose.prod.yml"

echo "Stopping Redis..."
docker-compose stop redis

echo "Removing Redis container..."
docker-compose rm -f redis

echo "Starting Redis with optimized production settings (persistence disabled)..."
docker-compose up -d redis

# Check if Redis started successfully
if ! docker-compose ps redis | grep -q "Up"; then
    echo "ERROR: Redis failed to start!"
    echo "Checking logs:"
    docker-compose logs redis
    exit 1
fi

echo "Redis restarted successfully with persistence disabled."
echo "IMPORTANT: All previous Redis data has been cleared."
echo "See REDIS_PERFORMANCE.md for details on the optimizations."

# Wait a moment for Redis to initialize
sleep 2

# Show Redis info
echo ""
echo "Redis Info:"
docker-compose exec redis redis-cli info | grep -E "used_memory_human|connected_clients|total_connections_received|total_commands_processed" || echo "Could not connect to Redis"

# Monitor Redis network activity
echo ""
echo "Redis Network Activity:"
docker-compose exec redis redis-cli info | grep -E "total_net_input_bytes|total_net_output_bytes" || echo "Could not connect to Redis"

echo ""
echo "To monitor Redis performance continuously, run: ./monitor_redis.sh"
