#!/bin/bash
# monitor_redis.sh - Monitor Redis performance metrics

# Check if interval parameter is provided
INTERVAL=${1:-5}

echo "=== Redis Performance Monitor ==="
echo "Monitoring Redis every $INTERVAL seconds. Press Ctrl+C to stop."
echo "========================================"

while true; do
    echo ""
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Redis Stats"
    echo "----------------------------------------"
    
    # Get Redis info
    echo "MEMORY USAGE:"
    docker-compose exec -T redis redis-cli info memory | grep -E "used_memory_human|used_memory_peak_human|mem_fragmentation_ratio"
    
    echo ""
    echo "CLIENTS:"
    docker-compose exec -T redis redis-cli info clients | grep -E "connected_clients|blocked_clients|tracking_clients"
    
    echo ""
    echo "NETWORK STATS:"
    docker-compose exec -T redis redis-cli info stats | grep -E "total_net_input_bytes|total_net_output_bytes|instantaneous_input_kbps|instantaneous_output_kbps"
    
    echo ""
    echo "COMMAND STATS:"
    docker-compose exec -T redis redis-cli info stats | grep -E "total_commands_processed|instantaneous_ops_per_sec|rejected_connections"
    
    echo ""
    echo "KEYSPACE INFO:"
    docker-compose exec -T redis redis-cli info keyspace
    
    # Get slow log if any
    SLOW_COUNT=$(docker-compose exec -T redis redis-cli slowlog len)
    if [ "$SLOW_COUNT" -gt "0" ]; then
        echo ""
        echo "SLOW OPERATIONS (last 5):"
        docker-compose exec -T redis redis-cli slowlog get 5
    fi
    
    # Wait for the next interval
    sleep $INTERVAL
done
