version: '3.8'

services:
  django:
    environment:
      - GUNICORN_WORKERS=1
      - GUNICORN_THREADS=2
      - DJANGO_ENV=local
    deploy:
      resources:
        limits:
          memory: 1g
          cpus: '1'

  celery_worker:
    environment:
      - CELERY_CONCURRENCY=2
      - CELERY_PREFETCH_MULTIPLIER=1
      - CELERY_MAX_TASKS_PER_CHILD=100
    deploy:
      resources:
        limits:
          memory: 1g
          cpus: '1'

  celery_beat:
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.1'

  flower:
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.1'

  redis:
    environment:
      - REDIS_MEMORY_LIMIT=512m
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'

  django_websocket:
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
