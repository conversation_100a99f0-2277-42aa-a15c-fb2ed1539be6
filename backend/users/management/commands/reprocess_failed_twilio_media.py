import logging
from django.core.management.base import BaseCommand
from users.models import TwilioMessage
from users.twilio_integration.tasks import process_twilio_media_attachments

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Reprocess failed Twilio media attachments'

    def add_arguments(self, parser):
        parser.add_argument(
            '--status',
            type=str,
            default='failed',
            help='Media processing status to filter by (failed, pending, partially_completed)',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=None,
            help='Limit the number of messages to process',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Only show what would be processed without actually processing',
        )
        parser.add_argument(
            '--message-id',
            type=int,
            help='Process a specific message ID',
        )

    def handle(self, *args, **options):
        status = options['status']
        limit = options['limit']
        dry_run = options['dry_run']
        message_id = options['message_id']

        if message_id:
            # Process a specific message
            try:
                message = TwilioMessage.objects.get(id=message_id)
                self.stdout.write(f"Found message {message.id} (SID: {message.message_sid})")
                
                if dry_run:
                    self.stdout.write(self.style.SUCCESS(f"Would reprocess message {message.id} (dry run)"))
                else:
                    self.stdout.write(f"Reprocessing message {message.id}...")
                    # Reset status to pending
                    message.media_processing_status = 'pending'
                    message.save(update_fields=['media_processing_status'])
                    # Queue task
                    process_twilio_media_attachments.delay(message.id)
                    self.stdout.write(self.style.SUCCESS(f"Requeued message {message.id} for processing"))
            except TwilioMessage.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"Message with ID {message_id} not found"))
            return

        # Process messages by status
        messages = TwilioMessage.objects.filter(
            media_processing_status=status,
            media_urls__isnull=False,
        ).exclude(
            media_urls=[]
        ).order_by('-created_at')
        
        if limit:
            messages = messages[:limit]
        
        count = messages.count()
        self.stdout.write(f"Found {count} messages with status '{status}'")
        
        if count == 0:
            return
        
        if dry_run:
            for message in messages:
                self.stdout.write(f"Would reprocess message {message.id} (SID: {message.message_sid})")
            self.stdout.write(self.style.SUCCESS(f"Dry run completed. {count} messages would be reprocessed."))
            return
        
        for i, message in enumerate(messages, 1):
            self.stdout.write(f"[{i}/{count}] Reprocessing message {message.id} (SID: {message.message_sid})...")
            # Reset status to pending
            message.media_processing_status = 'pending'
            message.save(update_fields=['media_processing_status'])
            # Queue task
            process_twilio_media_attachments.delay(message.id)
        
        self.stdout.write(self.style.SUCCESS(f"Successfully requeued {count} messages for processing"))
