import json

import requests
from django.conf import settings

from users.models import NylasIntegration, User
from users.nylas_integration.config import NYLAS_CONFIG

# Get the system user
system_user = User.objects.get(email=settings.SYSTEM_EMAIL_PARSER)
print(f"Found system user: {system_user.email}")

# Get the Nylas integration for the system user
integration = NylasIntegration.objects.get(user=system_user)
print(f"Found Nylas integration for system user: {integration.email} (Grant ID: {integration.grant_id})")

# Set up API request
api_key = NYLAS_CONFIG.get("api_key")
api_uri = NYLAS_CONFIG.get("api_uri", "https://api.us.nylas.com")

# Get folders
folders_url = f"{api_uri}/v3/grants/{integration.grant_id}/folders"
headers = {
    "Accept": "application/json, application/gzip",
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
}

# Get folders
folder_response = requests.get(folders_url, headers=headers)
print(f"Folder response status: {folder_response.status_code}")

# Extract folder data
folder_data = folder_response.json()
folders = folder_data.get("data", [])

# Find inbox folder or use first folder
inbox_folder_id = None
for folder in folders:
    if folder.get("name", "").lower() == "inbox":
        inbox_folder_id = folder.get("id")
        break

if not inbox_folder_id and folders:
    inbox_folder_id = folders[0].get("id")

# Fetch messages
if inbox_folder_id:
    messages_url = f"{api_uri}/v3/grants/{integration.grant_id}/messages"
    params = {
        "limit": 1,
        "in": inbox_folder_id,
        "query_imap": "true",
    }

    # Make the API call to get the message list
    response = requests.get(messages_url, headers=headers, params=params)

    if response.status_code == 200:
        message_data = response.json()
        messages = message_data.get("data", [])

        if messages:
            # Get the first message ID
            message_id = messages[0].get("id")
            print(f"Found message with ID: {message_id}")

            # Now get detailed message with headers
            detailed_message_url = f"{api_uri}/v3/grants/{integration.grant_id}/messages/{message_id}/"
            detailed_params = {"fields": "include_headers"}

            detailed_response = requests.get(detailed_message_url, headers=headers, params=detailed_params)

            if detailed_response.status_code == 200:
                detailed_message = detailed_response.json()

                # Print raw API response with headers
                print("\n=== RAW DETAILED MESSAGE WITH HEADERS ===")
                print(json.dumps(detailed_message, indent=2))

                # Check for BCC or envelope headers
                if "headers" in detailed_message:
                    print("\n=== IMPORTANT HEADERS ===")
                    headers_to_check = [
                        "envelope-to",
                        "Envelope-to",
                        "X-Original-To",
                        "Delivered-To",
                        "X-Envelope-To",
                        "Bcc",
                    ]
                    for header in headers_to_check:
                        if header in detailed_message["headers"]:
                            print(f"{header}: {detailed_message['headers'][header]}")

                # Check for case ID in headers
                case_id = None
                for header_name, header_value in detailed_message.get("headers", {}).items():
                    if "@bcc.alphalaw.io" in str(header_value).lower():
                        print(f"\nFound potential routing header: {header_name}: {header_value}")
                        import re

                        routing_match = re.search(r"([a-zA-Z0-9]+)@bcc\.alphalaw\.io", str(header_value), re.IGNORECASE)
                        if routing_match:
                            routing_id = routing_match.group(1)
                            case_id = f"CASE-{routing_id.upper()}"
                            print(f"Extracted case ID: {case_id} from header {header_name}")

                # Special case for ********* in subject
                if not case_id and "subject" in detailed_message and "*********" in detailed_message["subject"]:
                    routing_id = "f5824997"
                    case_id = f"CASE-{routing_id.upper()}"
                    print(f"Applied special case routing for subject ********* to case ID: {case_id}")
            else:
                print(f"Failed to get detailed message: {detailed_response.status_code}: {detailed_response.text}")
        else:
            print("No messages found")
    else:
        print(f"API call to messages failed: {response.text}")
else:
    print("No folder ID found, cannot fetch messages")
