#!/bin/bash
# start_services_no_celery.sh - Start AlphaLaw services without Celery

# Set environment to production with no Celery
export COMPOSE_FILE="docker-compose.yml:docker-compose.prod-no-celery.yml"

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p docker_data/static
mkdir -p docker_data/media
mkdir -p docker_data/logs
mkdir -p docker_data/redis

# Set proper permissions (only for newly created directories)
echo "Setting permissions on directories (ignoring errors)..."
chmod 777 docker_data/static 2>/dev/null || true
chmod 777 docker_data/media 2>/dev/null || true
chmod 777 docker_data/logs 2>/dev/null || true
chmod 777 docker_data/redis 2>/dev/null || true

# Stop any running Celery services first
echo "Stopping any running Celery services..."
docker-compose stop celery_worker celery_beat flower
docker-compose rm -f celery_worker celery_beat flower

# Start the services without Celery
echo "Starting services in production environment WITHOUT CELERY..."

# First remove any existing Redis container to avoid conflicts
docker-compose rm -f redis

# Start Redis with persistence disabled
docker-compose up -d redis

# Check if Redis started successfully
if docker-compose ps redis | grep -q "Up"; then
    echo "Redis started successfully with persistence disabled"

    # Start other services (excluding Celery)
    echo "Starting remaining services (excluding Celery)..."
    docker-compose up -d django django_websocket
else
    echo "ERROR: Redis failed to start. Check the logs with: docker-compose logs redis"
    exit 1
fi

# Show running services
echo ""
echo "Running services:"
docker-compose ps

# Show resource allocation
echo ""
echo "Resource allocation:"
docker-compose config | grep -A 5 "resources:"

echo ""
echo "IMPORTANT: Celery services are NOT running."
echo "This means background tasks, scheduled tasks, and email processing will NOT work."
echo "To restart with Celery, use: ./start_services.sh prod"
