# Generated by Django 5.1.3 on 2025-05-14 09:09

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("case_management", "0061_casenote_pinned"),
        ("lead_management", "0029_lead_referred_by"),
        ("users", "0038_organization_onedrive_personal_access_token_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="lead",
            name="lead_manage_lead_so_6c1134_idx",
        ),
        migrations.RenameField(
            model_name="lead",
            old_name="lead_source",
            new_name="source_type",
        ),
        migrations.RenameField(
            model_name="lead",
            old_name="lead_source_detail",
            new_name="source_detail",
        ),
        migrations.AddIndex(
            model_name="lead",
            index=models.Index(fields=["source_type"], name="lead_manage_source__afe0a5_idx"),
        ),
    ]
