# Generated by Django 5.1.3 on 2025-05-14 09:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lead_management', '0030_remove_lead_lead_manage_lead_so_6c1134_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='lead',
            name='emergency_contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='lead',
            name='emergency_contact_first_name',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='lead',
            name='emergency_contact_last_name',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='lead',
            name='emergency_contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='lead',
            name='emergency_contact_relationship',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
    ]
