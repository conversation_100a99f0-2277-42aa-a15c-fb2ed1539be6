import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { ImagePlus, Send } from "lucide-react";
import { Input } from "@/components/ui/input";

interface Contact {
  id: string;
  name: string;
  phone: string;
}

interface Message {
  id: string;
  text: string;
  images?: string[];
  timestamp: Date;
  sender: "user" | "contact";
}

interface MessageViewProps {
  contacts: Contact[];
  caseId: string;
}

export function MessageView({ contacts, caseId }: MessageViewProps) {
  console.log(contacts);
  console.log(caseId);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  console.log(selectedContact);
  console.log(setSelectedContact);
  const [messageText, setMessageText] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [images, setImages] = useState<File[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!messageText.trim() && images.length === 0) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      images:
        images.length > 0
          ? images.map((img) => URL.createObjectURL(img))
          : undefined,
      timestamp: new Date(),
      sender: "user",
    };

    setMessages([...messages, newMessage]);
    setMessageText("");
    setImages([]);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImages([...images, ...files]);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="flex flex-col gap-4 p-4 min-h-full">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.sender === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.sender === "user"
                      ? "bg-green-600 text-white"
                      : "bg-gray-100"
                  }`}
                >
                  <p className="whitespace-pre-wrap break-words">
                    {message.text}
                  </p>
                  {message.images && (
                    <div className="mt-2 grid grid-cols-2 gap-2">
                      {message.images.map((img, index) => (
                        <img
                          key={index}
                          src={img}
                          alt="Attached"
                          className="rounded-lg max-w-full h-auto"
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </div>

      <div className="flex flex-col gap-2 p-4 border-t bg-white">
        {images.length > 0 && (
          <ScrollArea className="w-full whitespace-nowrap pb-2" type="always">
            <div className="flex gap-2">
              {images.map((img, index) => (
                <div
                  key={index}
                  className="relative inline-block flex-shrink-0"
                >
                  <img
                    src={URL.createObjectURL(img)}
                    alt="Preview"
                    className="h-16 w-16 object-cover rounded-md border"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="h-5 w-5 absolute -top-2 -right-2 rounded-full p-0"
                    onClick={() =>
                      setImages(images.filter((_, i) => i !== index))
                    }
                  >
                    ×
                  </Button>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}

        <div className="flex gap-2 items-end">
          <Input
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            id="image-upload"
            onChange={handleImageUpload}
          />
          <Button
            variant="outline"
            size="icon"
            onClick={() => document.getElementById("image-upload")?.click()}
            className="flex-shrink-0"
          >
            <ImagePlus className="h-4 w-4" />
          </Button>
          <Textarea
            value={messageText}
            onChange={(e) => setMessageText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 min-h-[40px] max-h-[120px] resize-none"
            rows={1}
          />
          <Button
            size="icon"
            onClick={handleSendMessage}
            className="flex-shrink-0 bg-green-600 hover:bg-green-700"
          >
            <Send className="h-4 w-4 text-white" />
          </Button>
        </div>
      </div>
    </div>
  );
}
