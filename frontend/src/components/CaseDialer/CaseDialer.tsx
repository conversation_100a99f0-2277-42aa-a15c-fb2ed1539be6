"use client";

import { Phone, MessageSquare } from "lucide-react";
import { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { CallView } from "./CallView";
import { useClientContactDetailsQuery } from "@/services/case-management/clientDetailService";
import { useToast } from "@/hooks/use-toast";
import twilioService from "@/services/twilioIntegrationService";
import { Textarea } from "@/components/ui/textarea";

interface CaseDialerProps {
  caseId: string;
}

// Add these utility functions at the top of the file, after imports
const formatPhoneForDisplay = (phone: string) => {
  // Strip all non-numeric characters
  const cleaned = phone.replace(/\D/g, "");

  // Format as (XXX) XXX-XXXX if 10 digits
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
      6
    )}`;
  }
  // Format as +X (XXX) XXX-XXXX if 11 digits starting with 1
  if (cleaned.length === 11 && cleaned.startsWith("1")) {
    return `+${cleaned[0]} (${cleaned.slice(1, 4)}) ${cleaned.slice(
      4,
      7
    )}-${cleaned.slice(7)}`;
  }
  // Return original if not matching expected formats
  return phone;
};

const formatPhoneForTwilio = (phone: string) => {
  // Strip all non-numeric characters
  const cleaned = phone.replace(/\D/g, "");

  // If it's a 10-digit number, add +1
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  }
  // If it's already 11 digits starting with 1, add +
  if (cleaned.length === 11 && cleaned.startsWith("1")) {
    return `+${cleaned}`;
  }
  // If it already starts with +, return as is
  if (phone.startsWith("+")) {
    return phone;
  }
  // Default case: assume US number and add +1
  return `+1${cleaned}`;
};

export function CaseDialer({ caseId }: CaseDialerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<
    (typeof contacts)[0] | null
  >(null);
  const [message, setMessage] = useState("");
  const { toast } = useToast();
  const smsMutation = twilioService.useSendSMS();

  // get client contact details from useClientContactDetailsQuery
  const { data: clientContactDetails } = useClientContactDetailsQuery(caseId);

  // Modify the client contact creation to format the phone number
  const clientContact = clientContactDetails
    ? {
        id: clientContactDetails.id.toString(),
        name: "Client",
        type: "Client",
        phone: formatPhoneForDisplay(
          clientContactDetails.phone_number_1 ||
            clientContactDetails.phone_number_2 ||
            clientContactDetails.phone_number_3 ||
            ""
        ),
        rawPhone:
          clientContactDetails.phone_number_1 ||
          clientContactDetails.phone_number_2 ||
          clientContactDetails.phone_number_3 ||
          "",
      }
    : null;

  // Update mock contacts to include both display and raw phone numbers
  const contacts = [
    ...(clientContact ? [clientContact] : []),
    {
      id: "2",
      name: "Mary Adjuster",
      type: "Adjuster",
      phone: "(*************",
      rawPhone: "**********",
    },
    {
      id: "3",
      name: "Bob Mobile",
      type: "Mobile",
      phone: "(*************",
      rawPhone: "**********",
    },
    {
      id: "4",
      name: "Alice Provider",
      type: "Provider",
      phone: "(*************",
      rawPhone: "**********",
    },
  ];

  const handleSendMessage = async () => {
    if (!selectedContact?.rawPhone || !message) {
      toast({
        title: "Error",
        description: "Please provide both contact and message",
        variant: "destructive",
      });
      return;
    }

    try {
      await smsMutation.mutateAsync({
        to_number: formatPhoneForTwilio(selectedContact.rawPhone),
        message,
      });
      setMessage("");
      toast({
        title: "Success",
        description: "Message sent successfully",
      });
    } catch (error) {
      console.error("Error sending SMS:", error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <div className="fixed bottom-0 right-0 p-6 group">
        <div className="relative">
          <div className="absolute bottom-0 right-0 w-24 h-24 rounded-full bg-transparent" />
          <Button
            onClick={() => setIsOpen(true)}
            className="absolute bottom-0 right-0 h-14 w-14 rounded-full shadow-lg 
                     bg-green-600 hover:bg-green-700
                     transform translate-y-1/2 translate-x-1/2
                     group-hover:translate-y-0 group-hover:translate-x-0
                     transition-all duration-300 ease-in-out"
            size="icon"
          >
            <Phone className="h-6 w-6 text-white" />
          </Button>
        </div>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          className="sm:max-w-[900px] p-0"
          aria-description="Case Dialer"
          title="Case Dialer"
        >
          <div className="flex h-[600px]">
            {/* Contacts Sidebar - Keep this section for both tabs */}
            <div className="w-[300px] border-r flex flex-col">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold">Case Contacts</h2>
              </div>
              <ScrollArea className="flex-1">
                <div className="p-4 space-y-2">
                  {Object.entries(
                    contacts.reduce((acc, contact) => {
                      if (!acc[contact.type]) acc[contact.type] = [];
                      acc[contact.type].push(contact);
                      return acc;
                    }, {} as Record<string, typeof contacts>)
                  ).map(([type, contacts]) => (
                    <div key={type}>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">
                        {type}
                      </h3>
                      {contacts.map((contact) => (
                        <div
                          key={contact.id}
                          className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 cursor-pointer mb-2"
                          onClick={() => {
                            if (contact.phone) {
                              setSelectedContact(contact);
                            } else {
                              toast({
                                title: "Error",
                                description:
                                  "No phone number available for this contact",
                                variant: "destructive",
                              });
                            }
                          }}
                        >
                          <div>
                            <p className="font-medium">{contact.name}</p>
                            <p className="text-sm text-gray-500">
                              {contact.phone}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <Tabs defaultValue="message" className="h-full flex flex-col">
                <div className="p-4 border-b">
                  <TabsList className="grid w-48 grid-cols-2">
                    <TabsTrigger value="message">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Message
                    </TabsTrigger>
                    <TabsTrigger value="call">
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="call" className="flex-1 mt-0">
                  <CallView contacts={contacts} />
                </TabsContent>

                <TabsContent value="message" className="flex-1 mt-0">
                  {selectedContact ? (
                    <div className="p-4 space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium">
                          Messaging {selectedContact.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {selectedContact.phone}
                        </p>
                      </div>
                      <Textarea
                        placeholder="Type your message..."
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        className="min-h-[200px]"
                      />
                      <Button
                        className="w-full"
                        onClick={handleSendMessage}
                        disabled={smsMutation.isPending}
                      >
                        {smsMutation.isPending ? "Sending..." : "Send Message"}
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      Select a contact to start messaging
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
