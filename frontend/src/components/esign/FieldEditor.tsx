"use client";

import { useState } from "react";
import { 
  use<PERSON><PERSON><PERSON><PERSON>, 
  Recipient, 
  FieldType,
  CreateFieldRequest 
} from "@/services/eSignService";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

interface FieldEditorProps {
  documentId: number;
  recipients: Recipient[];
}

const FIELD_TYPES: { 
  value: FieldType; 
  label: string;
  defaultSize: { width: number; height: number };
  requiresLabel: boolean;
  defaultMeta?: {
    required?: boolean;
    fontSize?: number;
    textAlign?: 'left' | 'center' | 'right';
  };
}[] = [
  { 
    value: "SIGNATURE", 
    label: "Signature",
    defaultSize: { width: 200, height: 50 },
    requiresLabel: false,
  },
  { 
    value: "INITIALS", 
    label: "Initials",
    defaultSize: { width: 100, height: 50 },
    requiresLabel: false,
  },
  { 
    value: "NAME", 
    label: "Full Name",
    defaultSize: { width: 200, height: 50 },
    requiresLabel: true,
    defaultMeta: {
      required: true,
      fontSize: 12,
      textAlign: "left"
    }
  },
  { 
    value: "EMAIL", 
    label: "Email",
    defaultSize: { width: 200, height: 50 },
    requiresLabel: true,
    defaultMeta: {
      required: true,
      fontSize: 12,
      textAlign: "left"
    }
  },
  { 
    value: "DATE", 
    label: "Date",
    defaultSize: { width: 200, height: 50 },
    requiresLabel: true,
    defaultMeta: {
      required: true,
      fontSize: 12,
      textAlign: "left"
    }
  },
  { 
    value: "TEXT", 
    label: "Text",
    defaultSize: { width: 200, height: 50 },
    requiresLabel: true,
    defaultMeta: {
      fontSize: 12,
      textAlign: "left"
    }
  },
  { 
    value: "CHECKBOX", 
    label: "Checkbox",
    defaultSize: { width: 50, height: 50 },
    requiresLabel: true,
    defaultMeta: {
      required: true
    }
  },
];

export function FieldEditor({ documentId, recipients }: FieldEditorProps) {
  const [selectedRecipient, setSelectedRecipient] = useState<number | null>(null);
  const [selectedType, setSelectedType] = useState<FieldType>("SIGNATURE");
  const [position, setPosition] = useState({
    pageNumber: 1,
    x: 100,
    y: 100,
    width: 200,
    height: 50,
  });
  const [label, setLabel] = useState("");
  const { toast } = useToast();

  // const { data: document } = useDocument(documentId);
  const createField = useCreateField();
  // const deleteField = useDeleteField();

  // Update position when field type changes
  const handleFieldTypeChange = (type: FieldType) => {
    const fieldType = FIELD_TYPES.find(t => t.value === type);
    if (fieldType) {
      setPosition(prev => ({
        ...prev,
        width: fieldType.defaultSize.width,
        height: fieldType.defaultSize.height,
      }));
      setSelectedType(type);
    }
  };

  const handleAddField = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRecipient || !selectedType) return;

    const fieldType = FIELD_TYPES.find(t => t.value === selectedType);
    if (!fieldType) return;

    try {
      const fieldData: CreateFieldRequest = {
        recipientId: selectedRecipient,
        type: selectedType,
        pageNumber: position.pageNumber,
        pageX: position.x,
        pageY: position.y,
        pageWidth: position.width,
        pageHeight: position.height,
        ...(fieldType.requiresLabel && {
          fieldMeta: {
            ...fieldType.defaultMeta,
            label: label || fieldType.label,
          }
        }),
        ...(!fieldType.requiresLabel && {
          fieldMeta: {} // Empty object for SIGNATURE and INITIALS
        })
      };

      await createField.mutateAsync({
        documentId,
        data: fieldData,
      });

      toast({
        title: "Success",
        description: "Field added successfully",
      });

      // Reset label if it was used
      if (fieldType.requiresLabel) {
        setLabel("");
      }
    } catch (err) {
      // Error handling is done in the mutation
      console.error(err);
    }
  };

  if (!recipients.length) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center text-muted-foreground">
            Add recipients before adding signature fields
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add Signature Field</CardTitle>
          <CardDescription>
            Add and position signature fields for recipients
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleAddField} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="recipient">Recipient</Label>
                <Select
                  value={selectedRecipient?.toString() || ""}
                  onValueChange={(value) => setSelectedRecipient(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select recipient" />
                  </SelectTrigger>
                  <SelectContent>
                    {recipients.map((recipient) => (
                      <SelectItem
                        key={recipient.id}
                        value={recipient.id.toString()}
                      >
                        {recipient.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Field Type</Label>
                <Select
                  value={selectedType}
                  onValueChange={handleFieldTypeChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select field type" />
                  </SelectTrigger>
                  <SelectContent>
                    {FIELD_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {FIELD_TYPES.find(t => t.value === selectedType)?.requiresLabel && (
              <div className="space-y-2">
                <Label htmlFor="label">Field Label</Label>
                <Input
                  id="label"
                  value={label}
                  onChange={(e) => setLabel(e.target.value)}
                  placeholder={`Enter label (default: ${FIELD_TYPES.find(t => t.value === selectedType)?.label})`}
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="page">Page Number</Label>
                <Input
                  id="page"
                  type="number"
                  min={1}
                  value={position.pageNumber}
                  onChange={(e) =>
                    setPosition({ ...position, pageNumber: parseInt(e.target.value) })
                  }
                />
              </div>

              <div className="space-y-2">
                <Label>Position</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="X"
                    type="number"
                    value={position.x}
                    onChange={(e) =>
                      setPosition({ ...position, x: parseInt(e.target.value) })
                    }
                  />
                  <Input
                    placeholder="Y"
                    type="number"
                    value={position.y}
                    onChange={(e) =>
                      setPosition({ ...position, y: parseInt(e.target.value) })
                    }
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Size</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="Width"
                    type="number"
                    value={position.width}
                    onChange={(e) =>
                      setPosition({ ...position, width: parseInt(e.target.value) })
                    }
                  />
                  <Input
                    placeholder="Height"
                    type="number"
                    value={position.height}
                    onChange={(e) =>
                      setPosition({ ...position, height: parseInt(e.target.value) })
                    }
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={createField.isPending || !selectedRecipient}
              >
                Add Field
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* TODO: Add field preview and positioning interface */}
      {/* This would typically involve a PDF viewer component with drag-and-drop functionality */}
      <Card>
        <CardHeader>
          <CardTitle>Field Preview</CardTitle>
          <CardDescription>
            Preview and adjust field positions on the document
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[600px] bg-muted rounded-lg flex items-center justify-center">
            PDF Preview and Field Positioning Interface
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 