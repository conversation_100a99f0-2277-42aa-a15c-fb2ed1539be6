"use client";

import { useState } from "react";
import { useDocument } from "@/services/eSignService";
import { RecipientForm } from "@/components/esign/RecipientForm";
import { FieldEditor } from "@/components/esign/FieldEditor";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { UserPlus, FileSignature } from "lucide-react";

interface DocumentEditorProps {
  documentId: number;
}

export function DocumentEditor({ documentId }: DocumentEditorProps) {
  const [activeTab, setActiveTab] = useState("recipients");
  const { data: document, isLoading } = useDocument(documentId);

  if (isLoading) {
    return <div>Loading document...</div>;
  }

  if (!document) {
    return <div>Document not found</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">{document.title}</h2>
              <p className="text-muted-foreground">
                Document ID: {document.id}
              </p>
            </div>
            {document.status === "draft" && (
              <Button asChild>
                <a href={`/esign/send/${document.id}`}>
                  Continue to Send
                </a>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="recipients">
            <UserPlus className="h-4 w-4 mr-2" />
            Recipients
          </TabsTrigger>
          <TabsTrigger value="fields">
            <FileSignature className="h-4 w-4 mr-2" />
            Signature Fields
          </TabsTrigger>
        </TabsList>

        <TabsContent value="recipients" className="mt-6">
          <RecipientForm documentId={documentId} />
        </TabsContent>

        <TabsContent value="fields" className="mt-6">
          <FieldEditor 
            documentId={documentId}
            recipients={document.recipients || []}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
} 