"use client";

import { useDocument, useSendDocument, useResendDocument } from "@/services/eSignService";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, SendIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

interface DocumentSenderProps {
  documentId: number;
}

export function DocumentSender({ documentId }: DocumentSenderProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { data: document, isLoading } = useDocument(documentId);
  const sendDocument = useSendDocument();
  const resendDocument = useResendDocument();

  const handleSend = async () => {
    try {
      await sendDocument.mutateAsync(documentId);
      toast({
        title: "Success",
        description: "Document sent successfully",
      });
      router.push("/esign");
    } catch (err) {
      // Error handling is done in the mutation
      console.error(err);
    }
  };

  const handleResend = async (recipientIds: number[]) => {
    try {
      await resendDocument.mutateAsync({ documentId, recipientIds });
      toast({
        title: "Success",
        description: "Document resent successfully",
      });
    } catch (err) {
      // Error handling is done in the mutation
      console.error(err);
    }
  };

  if (isLoading) {
    return <div>Loading document...</div>;
  }

  if (!document) {
    return <div>Document not found</div>;
  }

  const hasRecipients = document.recipients && document.recipients.length > 0;
  const hasFields = document.fields && document.fields.length > 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Document Review</CardTitle>
          <CardDescription>
            Review document details before sending
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">Document Details</h3>
              <p className="text-muted-foreground">{document.title}</p>
              <p className="text-sm text-muted-foreground">
                ID: {document.id}
              </p>
            </div>

            <div>
              <h3 className="font-medium mb-2">Requirements</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  {hasRecipients ? (
                    <CheckCircle className="h-5 w-5 text-success" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-destructive" />
                  )}
                  <span>Recipients Added</span>
                </div>
                <div className="flex items-center space-x-2">
                  {hasFields ? (
                    <CheckCircle className="h-5 w-5 text-success" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-destructive" />
                  )}
                  <span>Signature Fields Added</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {hasRecipients && (
        <Card>
          <CardHeader>
            <CardTitle>Recipients</CardTitle>
            <CardDescription>
              Review recipient details and signing order
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {document.recipients?.map((recipient) => (
                  <TableRow key={recipient.id}>
                    <TableCell>{recipient.signing_order}</TableCell>
                    <TableCell>{recipient.name}</TableCell>
                    <TableCell>{recipient.email}</TableCell>
                    <TableCell>
                      <Badge variant={
                        recipient.status === "signed" ? "success" :
                        recipient.status === "declined" ? "destructive" :
                        "secondary"
                      }>
                        {recipient.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {recipient.status === "pending" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleResend([recipient.id])}
                          disabled={resendDocument.isPending}
                        >
                          Resend
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => router.push(`/esign/edit/${documentId}`)}
        >
          Back to Edit
        </Button>
        <Button
          onClick={handleSend}
          disabled={
            sendDocument.isPending ||
            !hasRecipients ||
            !hasFields ||
            document.status !== "draft"
          }
        >
          <SendIcon className="h-4 w-4 mr-2" />
          Send Document
        </Button>
      </div>
    </div>
  );
} 