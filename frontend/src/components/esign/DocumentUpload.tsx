"use client";

import { useState, useCallback } from "react";
import { useCreateDocument } from "@/services/eSignService";
import { useDropzone } from "react-dropzone";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { FileIcon, UploadIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface DocumentUploadProps {
  onSuccess: () => void;
}

export function DocumentUpload({ onSuccess }: DocumentUploadProps) {
  const [title, setTitle] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const createDocument = useCreateDocument();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const uploadedFile = acceptedFiles[0];
    if (uploadedFile) {
      setFile(uploadedFile);
      // Use filename without extension as default title
      setTitle(uploadedFile.name.replace(/\.[^/.]+$/, ""));
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || !title) return;

    try {
      await createDocument.mutateAsync({
        file,
        title,
      });
      onSuccess();
    } catch (err) {
      // Error handling is done in the mutation
      console.error(err);
    }
  };

  return (
    <div className="space-y-6">
      <DialogHeader>
        <DialogTitle>Upload Document for Signing</DialogTitle>
      </DialogHeader>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="title">Document Title</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter document title"
            required
          />
        </div>

        <div 
          {...getRootProps()} 
          className={cn(
            "border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors",
            isDragActive ? "border-primary bg-primary/10" : "border-muted",
            "hover:border-primary hover:bg-primary/5"
          )}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center space-y-4 text-center">
            {file ? (
              <>
                <FileIcon className="h-10 w-10 text-primary" />
                <div>
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </>
            ) : (
              <>
                <UploadIcon className="h-10 w-10 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    {isDragActive ? "Drop the file here" : "Drag & drop your PDF here"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    or click to select a file
                  </p>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button
            type="submit"
            disabled={!file || !title || createDocument.isPending}
          >
            {createDocument.isPending ? "Uploading..." : "Upload Document"}
          </Button>
        </div>
      </form>
    </div>
  );
} 