import { DocumentStatus, useDocumentsQuery } from "@/services/eSignService";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileIcon, PencilIcon, SendIcon, TrashIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";

interface DocumentListProps {
  filter: "all" | DocumentStatus;
}

export function DocumentList({ filter }: DocumentListProps) {
  const { data: documentsData, isLoading } = useDocumentsQuery(filter === "all" ? undefined : filter);

  const getStatusBadge = (status: DocumentStatus) => {
    const variants: Record<DocumentStatus, "default" | "destructive" | "success" | "warning" | "outline" | "secondary"> = {
      draft: "secondary",
      pending: "warning",
      completed: "success",
      voided: "destructive"
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  if (isLoading) {
    return <div>Loading documents...</div>;
  }

  if (!documentsData?.results.length) {
    return (
      <div className="text-center py-12">
        <FileIcon className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No documents found</h3>
        <p className="text-muted-foreground">
          {filter === "all"
            ? "Upload a document to get started"
            : `No ${filter} documents found`}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Document</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Recipients</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documentsData.results.map((doc) => (
            <TableRow key={doc.id}>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <FileIcon className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{doc.title}</div>
                    <div className="text-sm text-muted-foreground">
                      ID: {doc.id}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>{getStatusBadge(doc.status)}</TableCell>
              <TableCell>
                {doc.recipients?.length || 0} recipient(s)
              </TableCell>
              <TableCell>
                {doc.updated_at ? formatDistanceToNow(new Date(doc.updated_at), { addSuffix: true }) : "—"}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  {doc.status === "draft" && (
                    <>
                      <Link href={`/esign/edit/${doc.id}`}>
                        <Button size="sm" variant="outline">
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button size="sm" variant="outline">
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                  {doc.status === "draft" && (
                    <Link href={`/esign/send/${doc.id}`}>
                      <Button size="sm">
                        <SendIcon className="h-4 w-4 mr-1" />
                        Send
                      </Button>
                    </Link>
                  )}
                  {doc.status !== "draft" && (
                    <Link href={`/esign/view/${doc.id}`}>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </Link>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {documentsData.count > 0 && (
        <div className="text-sm text-muted-foreground text-center">
          Showing {documentsData.results.length} of {documentsData.count} documents
        </div>
      )}
    </div>
  );
} 