"use client";

import { useState } from "react";
import { useDocument, useCreateRecipient, useUpdateRecipient, useDeleteRecipient, Recipient } from "@/services/eSignService";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { PencilIcon, TrashIcon, UserPlus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface RecipientFormProps {
  documentId: number;
}

export function RecipientForm({ documentId }: RecipientFormProps) {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecipient, setEditingRecipient] = useState<Recipient | null>(null);
  const { toast } = useToast();
  
  const { data: document, isLoading } = useDocument(documentId);
  const createRecipient = useCreateRecipient();
  const updateRecipient = useUpdateRecipient();
  const deleteRecipient = useDeleteRecipient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !name) return;

    try {
      if (isEditing && editingRecipient) {
        await updateRecipient.mutateAsync({
          id: editingRecipient.id,
          data: {
            name,
            signing_order: editingRecipient.signing_order
          }
        });
        setIsEditing(false);
        setEditingRecipient(null);
      } else {
        await createRecipient.mutateAsync({
          document: documentId,
          email,
          name,
          signing_order: (document?.recipients?.length || 0) + 1,
        });
      }
      setEmail("");
      setName("");
      toast({
        title: "Success",
        description: `Recipient ${isEditing ? 'updated' : 'added'} successfully`,
      });
    } catch (err) {
      // Error handling is done in the mutation
      console.error(err);
    }
  };

  const handleEdit = (recipient: Recipient) => {
    setIsEditing(true);
    setEditingRecipient(recipient);
    setName(recipient.name);
    setEmail(recipient.email);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingRecipient(null);
    setName("");
    setEmail("");
  };

  const handleDelete = async (recipientId: number) => {
    try {
      await deleteRecipient.mutateAsync(recipientId);
      toast({
        title: "Success",
        description: "Recipient removed successfully",
      });
    } catch (err) {
      // Error handling is done in the mutation
      console.error(err);
    }
  };

  if (isLoading) {
    return <div>Loading recipients...</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add Recipient</CardTitle>
          <CardDescription>
            Add people who need to sign this document
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter recipient's name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter recipient's email"
                  required
                  disabled={isEditing}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              {isEditing && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                disabled={createRecipient.isPending || updateRecipient.isPending}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                {isEditing ? 'Update' : 'Add'} Recipient
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {document?.recipients && document.recipients.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>Recipients</CardTitle>
            <CardDescription>
              Manage document recipients and signing order
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {document.recipients.map((recipient) => (
                  <TableRow key={recipient.id}>
                    <TableCell>{recipient.signing_order}</TableCell>
                    <TableCell>{recipient.name}</TableCell>
                    <TableCell>{recipient.email}</TableCell>
                    <TableCell>
                      <Badge variant={
                        recipient.status === "signed" ? "success" :
                        recipient.status === "declined" ? "destructive" :
                        "secondary"
                      }>
                        {recipient.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(recipient)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(recipient.id)}
                          disabled={deleteRecipient.isPending}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="py-12">
            <div className="text-center text-muted-foreground">
              No recipients added yet
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 