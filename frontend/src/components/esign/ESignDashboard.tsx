"use client";

import { useState } from "react";
import { DocumentUpload } from "@/components/esign/DocumentUpload";
import { DocumentList } from "@/components/esign/DocumentList";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";

export function ESignDashboard() {
  const [isUploadOpen, setIsUploadOpen] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Tabs defaultValue="all" className="w-full">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="all">All Documents</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="draft">Drafts</TabsTrigger>
            </TabsList>
            
            <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DocumentUpload onSuccess={() => setIsUploadOpen(false)} />
              </DialogContent>
            </Dialog>
          </div>

          <TabsContent value="all" className="mt-0">
            <DocumentList filter="all" />
          </TabsContent>
          <TabsContent value="pending" className="mt-0">
            <DocumentList filter="pending" />
          </TabsContent>
          <TabsContent value="completed" className="mt-0">
            <DocumentList filter="completed" />
          </TabsContent>
          <TabsContent value="draft" className="mt-0">
            <DocumentList filter="draft" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 