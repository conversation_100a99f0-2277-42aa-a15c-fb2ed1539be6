"use client";

import React, { useState, useRef, useEffect, KeyboardEvent, ChangeEvent } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  Phone,
  MessageSquare,
  PhoneCall,
  // PhoneOff,
  // Mic,
  // MicOff,
  // Volume2,
  // VolumeX,
  ImagePlus,
  Send,
  X
} from "lucide-react";
import twilioService from "@/services/twilioIntegrationService";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { formatPhoneNumber } from "../ui/phone-link";

interface LeadDialerChatProps {
  leadId: string;
}

interface PhoneOption {
  number: string;
  type?: string;
  preferred?: boolean;
}

declare global {
  interface Window {
    RCAdapter?: {
      setMinimized: (minimized: boolean) => void;
      config: (config: {
        closeAfterCallEnd?: boolean;
        enablePopup?: boolean;
        minimized?: boolean;
        clickToDialInSearchResult?: boolean;
        enableMinimize?: boolean;
        hideCallTab?: boolean;
      }) => void;
      clickToCall: (phoneNumber: string) => void;
    };
  }
}

const loadRingCentralScript = () => {
  const script = document.createElement("script");
  script.src =
    "https://apps.ringcentral.com/integration/ringcentral-embeddable/latest/adapter.js";
  script.async = true;
  document.body.appendChild(script);
};

export function LeadDialerChat({ leadId }: LeadDialerChatProps) {
  const selectedLead = useSelector(
    (state: RootState) => state.leads.selectedLead
  );
  console.log(leadId, "leadId leadDialerChat");
  const [message, setMessage] = useState("");
  const [selectedPhoneIndex, setSelectedPhoneIndex] = useState<number>(0);
  const [phoneNumber, setPhoneNumber] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [images, setImages] = useState<File[]>([]);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // Twilio service hooks
  const smsMutation = twilioService.useSendSMS();
  const {
    data: conversationHistory,
    isLoading: isLoadingHistory,
    error: historyError,
  } = twilioService.useGetConversationHistory(phoneNumber, {
    lead_id: leadId,
  });

  // Add this effect to reset the last message timestamp when changing conversations
  useEffect(() => {
    if (phoneNumber) {
      twilioService.resetConversationState();
    }
  }, [phoneNumber]);

  useEffect(() => {
    scrollToBottom();
  }, [conversationHistory]);

  useEffect(() => {
    scrollToBottom();

    const interval = setInterval(() => {
      const container = chatContainerRef.current;
      if (container) {
        const isScrolledToBottom =
          container.scrollHeight - container.scrollTop <=
          container.clientHeight + 100;

        if (isScrolledToBottom) {
          scrollToBottom();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (selectedLead) {
      const numbers = getAvailablePhoneNumbers();
      if (numbers.length > 0 && selectedPhoneIndex < numbers.length) {
        setPhoneNumber(numbers[selectedPhoneIndex].number);
      } else if (selectedPhoneIndex === numbers.length) {
        setPhoneNumber(""); // Clear phone number for manual entry
      }
    }
  }, [selectedLead, selectedPhoneIndex]);

  useEffect(() => {
    if (leadId) {
      console.log("Fetching chat history for lead:", leadId);
    }
  }, [leadId]);

  useEffect(() => {
    loadRingCentralScript();

    const configureRingCentral = () => {
      if (window.RCAdapter && window.RCAdapter.config) {
        window.RCAdapter.config({
          closeAfterCallEnd: true,
          enablePopup: true,
          minimized: true,
          clickToDialInSearchResult: true,
          enableMinimize: true,
          hideCallTab: true,
        });
      }
    };

    const interval = setInterval(() => {
      if (window.RCAdapter) {
        configureRingCentral();
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = async () => {
    // Allow sending if there's a message OR images
    if (!message.trim() && images.length === 0) return;
    if (!phoneNumber.trim()) {
      return;
    }

    try {
      await smsMutation.mutateAsync({
        to_number: phoneNumber,
        message: message.trim(),
        media_files: images,
        lead_id: leadId,
      });
      setMessage("");
      setImages([]);
      // Reset file input value
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      scrollToBottom();
    } catch (err) {
      console.error("Failed to send message:", err);
    }
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      // Only send if there's a message OR images
      if ((images.length > 0) && phoneNumber.trim() && !smsMutation.isPending) {
        handleSendMessage();
      }
    }
  };

  const handleCall = () => {
    if (window.RCAdapter && phoneNumber) {
      window.RCAdapter.setMinimized(false);
      window.RCAdapter?.clickToCall(phoneNumber);
    }
  };

  const getAvailablePhoneNumbers = (): PhoneOption[] => {
    if (!selectedLead) return [];

    const numbers: PhoneOption[] = [];

    // Add client phone numbers
    if (selectedLead.phone) {
      numbers.push({
        number: formatPhoneNumber(selectedLead.phone),
        type: "Client Primary",
        preferred: true,
      });
    }

    // if (selectedLead.phone_2) {
    //   numbers.push({
    //     number: formatPhoneNumber(selectedLead.phone_2),
    //     type: "Client Secondary",
    //     preferred: false,
    //   });
    // }

    // Add key party phone numbers
    // if (selectedLead.key_party_phone) {
    //   numbers.push({
    //     number: formatPhoneNumber(selectedLead.key_party_phone),
    //     type: "Key Party Primary",
    //     preferred: false,
    //   });
    // }

    // if (selectedLead.key_party_phone_2) {
    //   numbers.push({
    //     number: formatPhoneNumber(selectedLead.key_party_phone_2),
    //     type: "Key Party Secondary",
    //     preferred: false,
    //   });
    // }

    return numbers;
  };

  const phoneNumberSelector = (
    <div className="p-4 border-b">
      {getAvailablePhoneNumbers().length > 0 ? (
        <div className="space-y-2">
          <Select
            value={String(selectedPhoneIndex)}
            onValueChange={(value) => {
              if (value === "manual") {
                setSelectedPhoneIndex(getAvailablePhoneNumbers().length);
                setPhoneNumber(""); // Clear the phone number when switching to manual
              } else {
                setSelectedPhoneIndex(Number(value));
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a phone number" />
            </SelectTrigger>
            <SelectContent>
              {getAvailablePhoneNumbers().map(
                ({ number, type, preferred }, index) => (
                  <SelectItem key={number} value={String(index)}>
                    <div className="flex items-center justify-between w-full">
                      <span className="font-medium">{number}</span>
                      <span className="text-sm text-muted-foreground">
                        {type}
                        {preferred && " ★"}
                      </span>
                    </div>
                  </SelectItem>
                )
              )}
              <SelectItem value="manual">
                <span className="text-sm text-muted-foreground">
                  Dial another number...
                </span>
              </SelectItem>
            </SelectContent>
          </Select>
          {selectedPhoneIndex === getAvailablePhoneNumbers().length && (
            <Input
              type="tel"
              placeholder="Enter phone number"
              value={phoneNumber}
              onChange={(e) => {
                const input = e.target.value.replace(/\D/g, "");
                setPhoneNumber(formatPhoneNumber(input));
              }}
              className="w-full"
            />
          )}
        </div>
      ) : (
        <Input
          type="tel"
          placeholder="Enter phone number"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(formatPhoneNumber(e.target.value))}
        />
      )}
    </div>
  );

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const fileArray = Array.from(files);
      setImages(prev => [...prev, ...fileArray]);
    }
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  return (
    <div className="h-full flex flex-col">
      {/* Image Preview Dialog */}
      <Dialog 
        open={!!previewImage} 
        onOpenChange={(open) => {
          if (!open) setPreviewImage(null);
        }}
      >
        <DialogContent 
          className="max-w-[80vw] max-h-[80vh] p-0 bg-transparent border-none shadow-none"
        >
          {previewImage && (
            <div 
              className="relative w-full h-full flex items-center justify-center"
              onClick={() => setPreviewImage(null)}
            >
              <img 
                src={previewImage} 
                alt="Preview" 
                className="max-w-[75vw] max-h-[75vh] object-contain rounded-lg shadow-xl cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Tabs defaultValue="messages" className="h-full flex flex-col">
        <div className="border-b flex-none">
          <TabsList className="w-full flex">
            <TabsTrigger
              value="messages"
              className="flex-1 flex items-center justify-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              <span>Messages</span>
            </TabsTrigger>
            <TabsTrigger
              value="call"
              className="flex-1 flex items-center justify-center gap-2"
            >
              <Phone className="h-4 w-4" />
              <span>Call</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="call" className="flex-1 h-full">
          <div className="flex flex-col h-full">
            {phoneNumberSelector}

            <div className="flex-1 flex flex-col items-center justify-center p-4 space-y-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold">
                  {phoneNumber || "Select a number to call"}
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Use RingCentral to manage your call
                </p>
              </div>

              <div className="flex items-center justify-center gap-4">
                <Button
                  size="icon"
                  className="h-20 w-20 rounded-full bg-green-500 hover:bg-green-600 text-white"
                  onClick={handleCall}
                  disabled={!phoneNumber.trim()}
                >
                  <PhoneCall className="h-10 w-10" />
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent
          value="messages"
          className="flex-1 flex flex-col h-full overflow-hidden"
        >
          {phoneNumberSelector}

          <div
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0"
          >
            {isLoadingHistory ? (
              <div className="text-center text-gray-500">
                Loading messages...
              </div>
            ) : historyError ? (
              <div className="text-center text-red-500">
                Failed to load messages
              </div>
            ) : conversationHistory?.length === 0 ? (
              <div className="text-center text-gray-500">No messages yet</div>
            ) : (
              conversationHistory?.map((msg) => (
                <div
                  key={msg.message_sid}
                  className={`max-w-[80%] ${
                    msg.message_type === "incoming" ? "ml-0" : "ml-auto"
                  }`}
                >
                  <div
                    className={`p-3 rounded-lg ${
                      msg.message_type === "incoming"
                        ? "bg-gray-100"
                        : "bg-blue-100"
                    }`}
                  >
                    <p className="text-sm whitespace-pre-wrap break-words">
                      {msg.body}
                    </p>
                    {msg.media_urls && msg.media_urls.length > 0 && (
                      <div className="mt-2 grid grid-cols-2 gap-2">
                        {msg.media_urls.map((media, index) => (
                          <div 
                            key={index}
                            className="relative cursor-pointer"
                            onClick={() => setPreviewImage(media.presigned_url)}
                          >
                            <img
                              src={media.presigned_url}
                              alt="Attached media"
                              className="rounded-lg w-full h-auto object-cover hover:opacity-90 transition-opacity"
                              loading="lazy"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = "/placeholder-image.png";
                                target.onerror = null;
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {format(new Date(msg.created_at), "MMM d, HH:mm")}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="flex-none border-t bg-white p-4">
            {images.length > 0 && (
              <div className="mb-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-500">
                    {images.length} {images.length === 1 ? "image" : "images"} selected
                  </span>
                  {images.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 text-xs text-red-500 hover:text-red-700 p-0"
                      onClick={() => setImages([])}
                    >
                      Clear all
                    </Button>
                  )}
                </div>
                <ScrollArea className="w-full whitespace-nowrap" type="always">
                  <div className="flex gap-2">
                    {images.map((img, index) => (
                      <div
                        key={index}
                        className="relative inline-block flex-shrink-0"
                      >
                        <img
                          src={URL.createObjectURL(img)}
                          alt="Preview"
                          className="h-16 w-16 object-cover rounded-md border cursor-pointer"
                          onClick={() => setPreviewImage(URL.createObjectURL(img))}
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="h-5 w-5 absolute -top-2 -right-2 rounded-full p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeImage(index);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
            
            <div className="flex gap-2">
              <input
                type="file"
                multiple
                accept="image/*"
                className="hidden"
                id="lead-image-upload"
                ref={fileInputRef}
                onChange={handleImageUpload}
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => document.getElementById("lead-image-upload")?.click()}
                className="flex-shrink-0"
                disabled={!phoneNumber.trim()}
              >
                <ImagePlus className="h-4 w-4" />
              </Button>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={phoneNumber.trim() ? "Type your message..." : "Add phone number to send message"}
                className="flex-1 min-h-[44px] max-h-[120px] resize-none"
                disabled={!phoneNumber.trim()}
              />
              <Button
                onClick={handleSendMessage}
                disabled={
                  smsMutation.isPending ||
                  (!message.trim() && images.length === 0) ||
                  !phoneNumber.trim()
                }
                className="flex-shrink-0"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
