import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
// import { Settlement } from "./Settlement";
import { SettlementAdvance } from "./SettlementAdvance";
import Negotiation from "./Negotiation";
import NewSettlement from "./NewSettlement";
import UIMNegotiation from "./UIMNegotiation";
import { useNegotiationsQuery } from "@/services/negotiationService";
import { NegotiationType } from "@/type/negotiationTypes";
import { useMemo, useState, useEffect, useCallback } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { ClientTrust } from "./ClientTrust";
import { useDefendantsListQuery } from "@/services/case-management/defendantService";
import UIMSettlement from "./UIMSettlement";

interface SettlementAndNegotiationProps {
  caseId: string;
}

// Component to handle negotiations for a single defendant
const DefendantNegotiations = ({
  caseId,
  defendantId,
  onHasAcceptedOffer
}: {
  caseId: string;
  defendantId: number;
  onHasAcceptedOffer: (defendantId: number, hasAccepted: boolean) => void;
}) => {
  const { data: negotiations } = useNegotiationsQuery(caseId, defendantId);

  useEffect(() => {
    if (!negotiations) return;
    const hasAccepted = negotiations.some(n => n.type === NegotiationType.ACCEPTED_OFFER);
    onHasAcceptedOffer(defendantId, hasAccepted);
  }, [negotiations, defendantId, onHasAcceptedOffer]);

  return null;
};

// Custom hook to handle negotiations for all defendants
const useDefendantsNegotiations = (caseId: string) => {
  const { data: defendants } = useDefendantsListQuery(caseId);
  const [acceptedOffers, setAcceptedOffers] = useState<Record<number, boolean>>({});

  const hasAcceptedOffer = useMemo(() => {
    if (!defendants?.length) return false;
    return Object.values(acceptedOffers).some(Boolean);
  }, [defendants, acceptedOffers]);

  return {
    hasAcceptedOffer,
    defendants,
    setAcceptedOffers
  };
};

export function SettlementAndNegotiation({ caseId }: SettlementAndNegotiationProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const initialTab = searchParams?.get("settlementTab") || "settlement";
  const { defendants, setAcceptedOffers } = useDefendantsNegotiations(caseId);

  const handleTabChange = (value: string) => {
    const newParams = new URLSearchParams(searchParams?.toString() || "");
    const mainTab = searchParams?.get("mainTab");
    newParams.delete("overviewTab");
    newParams.delete("preLitigationTab");
    newParams.delete("litigationTab");
    if (mainTab) newParams.set("mainTab", mainTab);
    newParams.set("settlementTab", value);
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });
  };

  const handleAcceptedOffer = useCallback((defendantId: number, hasAccepted: boolean) => {
    setAcceptedOffers(prev => ({
      ...prev,
      [defendantId]: hasAccepted
    }));
  }, [setAcceptedOffers]);

  return (
    <div className="p-4">
      {/* Render DefendantNegotiations components for each defendant */}
      {defendants?.map(defendant => (
        <DefendantNegotiations
          key={defendant.id}
          caseId={caseId}
          defendantId={defendant.id}
          onHasAcceptedOffer={handleAcceptedOffer}
        />
      ))}

      <Tabs
        defaultValue={initialTab}
        className="w-full"
        onValueChange={handleTabChange}
      >
        <TabsList className="grid w-full" style={{ gridTemplateColumns: "repeat(6, 1fr)" }} >
          {/* gridTemplateColumns: hasAcceptedOffer ? "repeat(5, 1fr)" : "repeat(4, 1fr)" }}> */}
          <TabsTrigger value="negotiation">Third Party Negotiation</TabsTrigger>
          {/* {hasAcceptedOffer && <TabsTrigger value="uim">UIM</TabsTrigger>} */}
          <TabsTrigger value="uim">UIM Negotiation</TabsTrigger>
          <TabsTrigger value="settlement">Third Party Settlement Memo</TabsTrigger>
          <TabsTrigger value="uim-settlement">UIM Settlement Memo</TabsTrigger>
          <TabsTrigger value="settlement-advance">Settlement Advance</TabsTrigger>
          <TabsTrigger value="client-trust">Client Trust</TabsTrigger>
        </TabsList>

        <TabsContent value="negotiation">
          <Negotiation caseId={caseId} />
        </TabsContent>

        {/* {hasAcceptedOffer && ( */}
        <TabsContent value="uim">
          <UIMNegotiation caseId={caseId} />
        </TabsContent>
        {/* )} */}

        <TabsContent value="settlement">
          <NewSettlement />
        </TabsContent>

        <TabsContent value="settlement-advance">
          <SettlementAdvance caseId={caseId} />
        </TabsContent>

        <TabsContent value="uim-settlement">
          <UIMSettlement />
        </TabsContent>

        <TabsContent value="client-trust">
          <ClientTrust caseId={caseId} />
        </TabsContent>

      </Tabs>
    </div>
  );
} 