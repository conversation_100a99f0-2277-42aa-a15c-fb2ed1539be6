import { CaseContentItem, FolderContentItem } from "@/services/case-management/newDocumentManagement";

// New file for types
export interface FileManagerProps {
  caseId: string;
}

export interface UploadingFile {
  file: File;
  progress: number;
  error?: string;
}

export interface FolderHistoryItem {
  id: string;
  name: string;
}

export interface DeleteConfirmation {
  type: 'file' | 'folder';
  item: FolderContentItem | CaseContentItem;
} 