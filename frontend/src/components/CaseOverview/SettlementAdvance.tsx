// src/components/CaseOverview/SettlementAdvance.tsx
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogPortal,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { AddEditLoanCompany } from "./AddEditLoanCompany";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import {
  useSettlementAdvancesQuery,
  useCreateSettlementAdvanceMutation,
  useUpdateSettlementAdvanceMutation,
  useLoanCompaniesQuery,
  useDeleteSettlementAdvanceMutation
} from '@/services/case-management/settlementService';
import { LoanCompany } from '@/type/case-management/settlementTypes';
import type { SettlementAdvance as SettlementAdvanceType } from '@/type/case-management/settlementTypes';
import { Edit, Plus, Trash2 } from "lucide-react";
import { DollarSign, Building2, Calendar, Percent, FileText, LucideIcon } from 'lucide-react';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { CurrencyDisplay } from "@/components/ui/currency-display";
import { KPICard } from "@/components/ui/kpi-card";
import { Wallet, Calculator } from "lucide-react";

// Form validation schema
const formSchema = z.object({
  company: z.string().min(1, { message: "Company is required" }),
  amount: z.string().min(1, { message: "Amount is required" }),
  handling_fee: z.string().optional(),
  rate_per_year: z.string().optional(),
  interest: z.string().optional(),
  total_owed: z.string().min(1, { message: "Total owed is required" }),
  note: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface SettlementAdvanceProps {
  caseId: string;
}

const calculateTotals = (advances: SettlementAdvanceType[] | undefined) => {
  if (!advances) return { totalAmount: 0, totalInterest: 0, totalOwed: 0 };

  return advances.reduce((acc, advance) => ({
    totalAmount: acc.totalAmount + parseFloat(advance.amount || '0'),
    totalInterest: acc.totalInterest + parseFloat(advance.interest || '0'),
    totalOwed: acc.totalOwed + parseFloat(advance.total_owed || '0'),
  }), { totalAmount: 0, totalInterest: 0, totalOwed: 0 });
};

interface AdvanceInfoFieldProps {
  icon: LucideIcon;
  label: string;
  value: string | React.ReactNode;
  className?: string;
}

const AdvanceInfoField: React.FC<AdvanceInfoFieldProps> = ({ icon: Icon, label, value }) => (
  <div className="flex items-start gap-3">
    <Icon className="h-5 w-5 text-gray-500" />
    <div>
      <span className="text-gray-500 text-sm">{label}</span>
      <p className="text-sm font-medium text-[#060216]">
        {value}
      </p>
    </div>
  </div>
);

export function SettlementAdvance({ caseId }: SettlementAdvanceProps) {
  const [open, setOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<LoanCompany | null>(null);
  const [selectedAdvance, setSelectedAdvance] = useState<SettlementAdvanceType | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [advanceToDelete, setAdvanceToDelete] = useState<SettlementAdvanceType | null>(null);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

  // Query and mutation hooks
  const { data: advances, isLoading: isLoadingAdvances } = useSettlementAdvancesQuery(caseId);
  const { data: loanCompaniesData, isLoading: isLoadingCompanies } = useLoanCompaniesQuery();
  const createAdvanceMutation = useCreateSettlementAdvanceMutation(caseId);
  const updateAdvanceMutation = useUpdateSettlementAdvanceMutation(
    caseId,
    selectedAdvance?.id.toString() || ''
  );
  const deleteAdvanceMutation = useDeleteSettlementAdvanceMutation(
    caseId,
    advanceToDelete?.id.toString() || ''
  );

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      company: "",
      amount: "",
      handling_fee: "",
      rate_per_year: "",
      interest: "",
      total_owed: "",
      note: "",
    },
  });

  // Update form when editing
  useEffect(() => {
    if (selectedAdvance) {
      form.reset({
        company: selectedAdvance.company.id.toString(),
        amount: selectedAdvance.amount,
        handling_fee: selectedAdvance.handling_fee || "",
        rate_per_year: selectedAdvance.rate_per_year || "",
        interest: selectedAdvance.interest || "",
        total_owed: selectedAdvance.total_owed,
        note: selectedAdvance.note || "",
      });
      setSelectedCompany(selectedAdvance.company);
    } else {
      form.reset({
        company: "",
        amount: "",
        handling_fee: "",
        rate_per_year: "",
        interest: "",
        total_owed: "",
        note: "",
      });
      setSelectedCompany(null);
    }
  }, [selectedAdvance, form]);

  const onSubmit = async (data: FormValues) => {
    try {
      if (selectedAdvance) {
        await updateAdvanceMutation.mutateAsync({
          company: data.company,
          amount: data.amount,
          handling_fee: data.handling_fee,
          rate_per_year: data.rate_per_year,
          interest: data.interest,
          total_owed: data.total_owed,
          note: data.note,
        });
      } else {
        await createAdvanceMutation.mutateAsync({
          company: data.company,
          amount: data.amount,
          handling_fee: data.handling_fee,
          rate_per_year: data.rate_per_year,
          interest: data.interest,
          total_owed: data.total_owed,
          note: data.note,
        });
      }
      form.reset();
      setOpen(false);
    } catch (error) {
      console.error('Failed to save settlement advance:', error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset();
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleDelete = async (advance: SettlementAdvanceType) => {
    setAdvanceToDelete(advance);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (advanceToDelete) {
      try {
        await deleteAdvanceMutation.mutateAsync();
        setDeleteDialogOpen(false);
        setAdvanceToDelete(null);
      } catch (error) {
        console.error('Failed to delete settlement advance:', error);
      }
    }
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <div className="p-4">
      <div className="flex justify-end items-end">
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <Button
            variant="link"
            className="text-green-600"
          >
            {selectedAdvance ? <Edit className="h-4 w-4 mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
            {selectedAdvance ? "Edit Settlement Advance" : "Add Settlement Advance"}
          </Button>
        </DialogTrigger>
        <DialogPortal>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>
                {selectedAdvance ? "EDIT SETTLEMENT ADVANCE" : "ADD SETTLEMENT ADVANCE"}
              </DialogTitle>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>company *</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          const company = loanCompaniesData?.find(
                            (c) => c.id.toString() === value
                          );
                          setSelectedCompany(company || null);
                        }}
                        value={field.value}
                        disabled={isLoadingCompanies}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select company" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loanCompaniesData?.map((company: LoanCompany) => (
                            <SelectItem key={company.id} value={company.id.toString()}>
                              {company.company}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-4 mb-2">
                  <AddEditLoanCompany isEdit={false}>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-20"
                    >
                      ADD
                    </Button>
                  </AddEditLoanCompany>
                  <AddEditLoanCompany
                    isEdit={true}
                    selectedLoanCompany={selectedCompany}
                  >
                    <Button
                      type="button"
                      variant="outline"
                      className="w-20"
                      disabled={!selectedCompany}
                    >
                      EDIT
                    </Button>
                  </AddEditLoanCompany>
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>amount *</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="handling_fee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>handling fee</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="rate_per_year"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>% rate / year</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="interest"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>interest</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="total_owed"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>total owed *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>note</FormLabel>
                      <FormControl>
                        <Textarea {...field} className="h-32" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelClick}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {selectedAdvance ? "Update" : "Create"}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </DialogPortal>
      </Dialog>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-6">
        <KPICard
          icon={DollarSign}
          title="ADVANCED"
          metrics={{
            primary: {
              type: 'monetary',
              value: calculateTotals(advances)?.totalAmount
            }
          }}
          isCurrency={true}
        />

        <KPICard
          icon={Calculator}
          title="INTEREST"
          metrics={{
            primary: {
              type: 'monetary',
              value: calculateTotals(advances)?.totalInterest
            }
          }}
          isCurrency={true}
        />

        <KPICard
          icon={Wallet}
          title="TOTAL OWED"
          metrics={{
            primary: {
              type: 'monetary',
              value: calculateTotals(advances)?.totalOwed
            }
          }}
          isCurrency={true}
        />
      </div>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        text="Settlement Advance"
      />

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />

      <div className="grid gap-4 mt-4">
        {isLoadingAdvances ? (
          <div>Loading advances...</div>
        ) : (
          advances?.map((advance: SettlementAdvanceType) => (
            <Card key={advance.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Building2 className="h-5 w-5 text-gray-500" />
                      <h3 className="text-lg font-semibold text-[#060216]">
                        {advance.company.company}
                      </h3>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedAdvance(advance);
                          setOpen(true);
                        }}
                        className="hover:bg-gray-100"
                      >
                        <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(advance)}
                        className="hover:bg-gray-100"
                      >
                        <Trash2 className="h-4 w-4 text-red-600 cursor-pointer" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <AdvanceInfoField
                      icon={DollarSign}
                      label="Amount"
                      value={<CurrencyDisplay amount={advance.amount} />}
                    />
                    <AdvanceInfoField
                      icon={DollarSign}
                      label="Handling Fee"
                      value={<CurrencyDisplay amount={advance.handling_fee || 0} />}
                    />
                    <AdvanceInfoField
                      icon={Percent}
                      label="Rate per Year"
                      value={advance.rate_per_year ? `${advance.rate_per_year}%` : '—'}
                    />
                    <AdvanceInfoField
                      icon={DollarSign}
                      label="Interest"
                      value={<CurrencyDisplay amount={advance.interest || 0} />}
                    />
                    <AdvanceInfoField
                      icon={DollarSign}
                      label="Total Owed"
                      value={<CurrencyDisplay amount={advance.total_owed} />}
                      className="text-green-600 font-semibold"
                    />
                    <AdvanceInfoField
                      icon={Calendar}
                      label="Date"
                      value={new Date(advance.created_at).toLocaleDateString()}
                    />
                    {advance.note && (
                      <div className="col-span-3">
                        <AdvanceInfoField
                          icon={FileText}
                          label="Note"
                          value={advance.note}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}