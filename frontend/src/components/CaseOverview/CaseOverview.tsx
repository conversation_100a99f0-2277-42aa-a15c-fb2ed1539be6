import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CaseDetail } from "./components/CaseDetail";
import { IncidentDetails } from "./components/IncidentDetails";
// import { MiscLiens } from "./MiscLiens";
// import { Discovery } from "./Discovery";
import { CaseCosts } from "./components/CaseCosts";
import FileManager from "./components/FileManager";
import { Worker } from "./components/Worker";
import { useSearchParams, useRouter } from "next/navigation";
import { CheckList } from "./components/CheckList";
import { useClientBasicDetailsQuery } from "@/services/case-management/clientDetailService";
import EmploymentPageDetails from "./components/EmploymentPageDetails";
import DefendantDetails from "./DefendantDetails";
import WitnessDetails from "./components/WitnessDetails";
import PlaintiffDetails from "./components/PlaintiffDetails";
import { MedicalTreatmentDetails } from "./components/MedicalTreatmentDetails";
import HealthBillsDetails from "./components/HealthBillsDetails";
import LiensDetails from "./components/LiensDetails";
import ClientDetails from "./components/ClientDetails";
// import { ImpactAssessment } from "./components/ImpactAssessment";
import Mails from "./components/Mails";
// import { ClientTrust } from "./ClientTrust";
import { useTreatmentProvidersQuery } from "@/services/case-management/medicalTreatmentService";
import { useIncidentDetailsQuery } from "@/services/incidentService";
import { usePinnedFileQuery } from "@/services/case-management/newDocumentManagement";
import { useLinkedCasesQuery } from "@/services/case-management/linkCaseService";
import { useCombinedCardsQuery } from "@/services/case-management/cardService";

interface CaseOverviewProps {
  caseId: string;
}

export function CaseOverview({ caseId }: CaseOverviewProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const initialTab = searchParams?.get("overviewTab") || "Case Detail";
  const isEdit = searchParams?.get("isEdit") === "true";
  const [activeTab, setActiveTab] = useState(initialTab);

  // Initialize all queries
  const { data: basicDetails, refetch: refetchBasicDetails } = useClientBasicDetailsQuery(caseId);
  const { refetch: refetchTreatmentProviders } = useTreatmentProvidersQuery(caseId);
  const { refetch: refetchIncidentDetails } = useIncidentDetailsQuery(caseId);
  const { refetch: refetchPinnedFile } = usePinnedFileQuery(caseId);
  const { refetch: refetchLinkedCases } = useLinkedCasesQuery(caseId);
  const { refetch: refetchCombinedCards } = useCombinedCardsQuery(caseId);

  // Prefetch    data for the active tab
  const prefetchTabData = async (tabName: string) => {
    switch (tabName) {
      case "Case Detail":
        await Promise.all([
          refetchCombinedCards(),
          refetchIncidentDetails(),
          refetchPinnedFile(),
          refetchLinkedCases(),
        ]);
        break;
      case "Client":
        await refetchBasicDetails();
        break;
      case "Medical Treatment":
        await refetchTreatmentProviders();
        break;
      case "Incident":
        await refetchIncidentDetails();
        break;
      case "Media/Documents":
        await refetchPinnedFile();
        break;
      // Add other cases as needed
    }
  };

  // Initial data fetch when component mounts
  useEffect(() => {
    prefetchTabData(initialTab);
  }, [caseId]);

  const handleTabChange = (newTab: string) => {
    console.log("[CaseOverview] handleTabChange called with newTab:", newTab);
    console.log("[CaseOverview] Current search params:", Object.fromEntries(searchParams ? searchParams.entries() : []));

    const newParams = new URLSearchParams(searchParams ? searchParams.toString() : "");
    // Only preserve mainTab and overviewTab
    const mainTab = searchParams?.get("mainTab");
    newParams.delete("preLitigationTab");
    newParams.delete("litigationTab");
    newParams.delete("settlementTab");
    if (mainTab) newParams.set("mainTab", mainTab);
    newParams.set("overviewTab", newTab);

    console.log("[CaseOverview] Setting new URL params:", newParams.toString());
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });

    console.log("[CaseOverview] Setting active tab to:", newTab);
    setActiveTab(newTab);
    prefetchTabData(newTab);
  };

  useEffect(() => {
    console.log("[CaseOverview] URL params changed:", Object.fromEntries(searchParams ? searchParams.entries() : []));
    const overviewTab = searchParams?.get("overviewTab");
    if (overviewTab) {
      console.log("[CaseOverview] Found overviewTab in URL:", overviewTab);
      setActiveTab(overviewTab);
      prefetchTabData(overviewTab);
    }
  }, [searchParams]);

  useEffect(() => {
    console.log("[CaseOverview] Active tab changed to:", activeTab);
  }, [activeTab]);

  const tabs = [
    {
      name: "Case Detail",
      component: <CaseDetail caseId={caseId} setActiveTab={handleTabChange} />,
    },
    { name: "Checklist", component: <CheckList caseId={caseId} /> },
    { name: "Client", component: <ClientDetails caseId={caseId} /> },
    { name: "Defendants", component: <DefendantDetails caseId={caseId} /> },
    {
      name: "Incident",
      component: <IncidentDetails caseId={caseId} isEdit={isEdit} />,
    },
    { name: "Witness", component: <WitnessDetails caseId={caseId} /> },
    { name: "Other Parties", component: <PlaintiffDetails caseId={caseId} /> },
    {
      name: "Medical Treatment",
      component: <MedicalTreatmentDetails caseId={caseId} />,
    },
    { name: "Health Insurance", component: <HealthBillsDetails caseId={caseId} /> },
    ...(basicDetails?.employed
      ? [{ name: "Employment", component: <EmploymentPageDetails caseId={caseId} /> }]
      : []),
    { name: "Liens", component: <LiensDetails caseId={caseId} /> },
    { name: "Case Costs", component: <CaseCosts caseId={caseId} /> },
    // { name: "Client Trust", component: <ClientTrust caseId={caseId} />  },
    { name: "Media/Documents", component: <FileManager caseId={caseId} /> },
    // { name: "Impact Assessment", component: <ImpactAssessment caseId={caseId} /> },
    { name: "Emails", component: <Mails caseId={caseId} /> },
    { name: "Workers", component: <Worker caseId={caseId} /> },
  ];

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey) {
        if (event.key === "ArrowRight" || event.key === "ArrowLeft") {
          event.preventDefault();
          const currentIndex = tabs.findIndex((tab) => tab.name === activeTab);
          let newIndex;

          if (event.key === "ArrowRight") {
            newIndex = currentIndex === tabs.length - 1 ? 0 : currentIndex + 1;
          } else {
            newIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
          }

          handleTabChange(tabs[newIndex].name);
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [activeTab, tabs]);

  return (
    <div className="w-full flex flex-col items-start gap-6 p-3 rounded-[10px] bg-white">
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="relative w-full">
          <div className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white to-transparent pointer-events-none" />
          <div className="absolute left-0 top-0 h-full w-8 bg-gradient-to-r from-white to-transparent pointer-events-none" />
          <TabsList className="w-full">
            {tabs.map((tab) => (
              <TabsTrigger key={tab.name} value={tab.name}>
                {tab.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
        <div className="w-full h-[calc(100vh-235px)] overflow-y-auto scroll-smooth scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 mt-6">
          {tabs.map((tab) => (
            <TabsContent key={tab.name} value={tab.name} className="px-3">
              {tab.component}
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  );
}
