import React from "react";
import { Card, CardContent } from "@/components/ui/card";

export function Discovery() {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Discovery</h2>
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Discovery Type</label>
                  <select className="w-full mt-1 p-2 border rounded">
                    <option value="">Select type</option>
                    <option value="interrogatories">Interrogatories</option>
                    <option value="deposition">Deposition</option>
                    <option value="document_request">Document Request</option>
                    <option value="admission_request">
                      Request for Admission
                    </option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Due Date</label>
                  <input
                    type="date"
                    className="w-full mt-1 p-2 border rounded"
                  />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Status</label>
                <select className="w-full mt-1 p-2 border rounded">
                  <option value="">Select status</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="overdue">Overdue</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Notes</label>
                <textarea
                  className="w-full mt-1 p-2 border rounded"
                  rows={3}
                  placeholder="Enter discovery notes"
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
