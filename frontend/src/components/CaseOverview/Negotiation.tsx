import React, { useState, useEffect, useMemo, Fragment } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { DollarSign, Calendar, AlertCircle, ChevronDown } from 'lucide-react';
import { useDefendantInsuranceListQuery, useDefendantsListQuery } from '@/services/case-management/defendantService';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { NegotiationProps } from '@/type/negotiationTypes';
import { CreateDemandDialog } from '@/components/CaseOverview/CreateDemandDialog';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useNegotiationsQuery, useDefendantAdjustersQuery, useDeleteNegotiationMutation } from '@/services/negotiationService';
import { NegotiationType, NegotiationRead } from '@/type/negotiationTypes';
import { useUpdateNegotiationMutation } from '@/services/negotiationService';
import { useSettlementCalculationQuery, useUpdateSettlementCalculationMutation } from '@/services/case-management/settlementService';
import { CaseNotes } from '../CaseNotes/CaseNotes';
import { CaseTasks } from '../CaseTasks/CaseTasks';
import { formatDateForApi, formatDateForDisplay } from '@/utils/dateUtils';
import { DeleteConfirmationDialog } from '@/components/common/DeleteConfirmationDialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useQueryClient } from '@tanstack/react-query';
import {
  useCreateClientTrustMutation,
  useClientTrustQuery,
  useUpdateClientTrustMutation,
  CLIENT_TRUST_KEYS
} from "@/services/case-management/clientTrustService";
import {
  ClientTrustEntryType,
  DepositType,
  ClientTrust
} from "@/type/case-management/clientTrustTypes";
import { toast } from '@/hooks/use-toast';
import AllDefendantsView from './AllDefendantsView';
import { NegotiationTable, NegotiationTableType } from './components/negotiation/Table';
import { ParentOfferDemand } from './components/negotiation/parentOfferDemand';
import NegotiationCards from './components/negotiation/negotiationcards';
import KeyInformation, { AdjusterInfo } from './components/negotiation/KeyInformation';
import FeePercentageSelect from './components/negotiation/FeePercentageSelect';

interface OrganizedNegotiation {
  parent: NegotiationRead;
  children: NegotiationRead[];
}

/**
 * The main Negotiation component for managing defendant negotiations
 */
const Negotiation = ({ caseId }: NegotiationProps): JSX.Element => {
  const queryClient = useQueryClient();
  const createClientTrust = useCreateClientTrustMutation(queryClient);
  const updateClientTrust = useUpdateClientTrustMutation(queryClient);
  const { data: clientTrustData } = useClientTrustQuery(caseId);

  const { data: defendants, isLoading: isDefendantsLoading } = useDefendantsListQuery(caseId);
  const [selectedDefendant, setSelectedDefendant] = useState<number | null>(null);
  const [editDemand, setEditDemand] = useState<NegotiationRead | null>(null);

  const { data: negotiations, refetch } = useNegotiationsQuery(caseId, selectedDefendant);
  const { data: calculations } = useSettlementCalculationQuery(caseId);

  const { data: insuranceList, refetch: refetchInsuranceList } = useDefendantInsuranceListQuery(
    caseId,
    (selectedDefendant || '').toString(),
    { enabled: !!selectedDefendant }
  );

  // Add adjuster query at the top level
  const { data: adjusters, isLoading: isAdjustersLoading } = useDefendantAdjustersQuery(
    caseId,
    selectedDefendant?.toString() || '',
    true,
    { enabled: !!selectedDefendant }
  );

  // Add updateSettlementCalculation mutation
  const updateSettlementCalculation = useUpdateSettlementCalculationMutation(caseId);

  // Refetch negotiations when defendant changes
  useEffect(() => {
    if (selectedDefendant) {
      refetch();
    }
  }, [selectedDefendant, refetch]);

  const [currentNegotiationId, setCurrentNegotiationId] = useState<number>(0);
  const updateNegotiationMutation = useUpdateNegotiationMutation(caseId, currentNegotiationId);

  const handleAcceptOffer = async (negotiationId: number) => {
    if (!selectedDefendant) return;
    setCurrentNegotiationId(negotiationId);

    try {
      // First update the negotiation status - only send necessary fields
      if (updateNegotiationMutation && negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          type: NegotiationType.ACCEPTED_OFFER
        });
      }

    } catch (error) {
      console.error('Error accepting offer:', error);
    }
  };

  const handleDeacceptOffer = async (negotiationId: number) => {
    if (!selectedDefendant) return;
    setCurrentNegotiationId(negotiationId);
    if (updateNegotiationMutation && negotiationId !== 0) {
      await updateNegotiationMutation.mutateAsync({
        type: NegotiationType.INITIAL_DEMAND
      });
    }
  };

  const latestAdjusterId = useMemo(() => {
    if (!negotiations?.length) return null;
    return negotiations[0]?.adjuster?.id || null;
  }, [negotiations]);

  useEffect(() => {
    if (selectedDefendant) {
      refetch();
      refetchInsuranceList();
    }
  }, [selectedDefendant, refetch, refetchInsuranceList]);

  useEffect(() => {
    if (selectedDefendant === undefined && defendants && defendants.length > 0) {
      setSelectedDefendant(defendants[0].id);
    }
  }, [defendants, selectedDefendant]);

  const [expandedDays, setExpandedDays] = useState<string[]>([]);

  useEffect(() => {
    if (negotiations) {
      // setExpandedDays(negotiations.map(n => n.id.toString()));
      // Expand all negotiations by default
      const allNegotiationIds = negotiations.map(n => n.id.toString());
      setExpandedDays(allNegotiationIds);
    }
  }, [negotiations]);

  // Helper function to organize negotiations into parent-child structure
  const organizeNegotiations = (negotiations: NegotiationRead[] | undefined): OrganizedNegotiation[] => {
    if (!negotiations) return [];

    // Get parent negotiations (where previous_offer is null)
    const parentNegotiations = negotiations.filter(item => item.previous_offer === null);

    // Sort parents by created_at in descending order (newest first)
    return parentNegotiations.map(parent => ({
      parent,
      // Get all children where previous_offer matches parent ID
      children: negotiations
        .filter(item => item.previous_offer === parent.id)
        // Sort children by created_at in ascending order (oldest first)
        .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
    }))
      // Sort parents by created_at in descending order (newest first)
      .sort((a, b) => new Date(b.parent.created_at).getTime() - new Date(a.parent.created_at).getTime());
  };

  // Use the organized negotiations instead of the previous grouping logic
  const organizedNegotiations = useMemo(() => {
    return organizeNegotiations(negotiations);
  }, [negotiations]);

  // Add this new useEffect to make all sections expanded by default
  useEffect(() => {
    if (organizedNegotiations.length > 0) {
      setExpandedDays(organizedNegotiations.map(item => item.parent.id.toString()));
    }
  }, [organizedNegotiations]);

  const toggleDay = (date: string) => {
    setExpandedDays(prev =>
      prev.includes(date)
        ? prev.filter(d => d !== date)
        : [...prev, date]
    );
  };

  const hasAcceptedOffer = useMemo(() => {
    return negotiations?.some(n => n.type === NegotiationType.ACCEPTED_OFFER) ?? false;
  }, [negotiations]);

  // const acceptedOfferId = useMemo(() => {
  //   return negotiations?.find(n => n.type === NegotiationType.ACCEPTED_OFFER)?.id ?? null;
  // }, [negotiations]);

  const hasArchivedDemand = useMemo(() => {
    return negotiations?.some(n => n.is_archived) ?? false;
  }, [negotiations]);

  const quickStats = useMemo(() => {
    if (!negotiations?.length) return {
      latestOffer: null,
      demandAmount: null,
      nextDeadline: null,
      acceptedOffer: null,
      totalPolicyLimit: null
    };

    // Filter negotiations with non-null previous_offer and sort by created_at for latest offer
    const sortedNegotiations = [...negotiations]
      .filter(n => n.previous_offer !== null)
      .sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

    // Latest offer is now the most recent negotiation amount from filtered negotiations
    const latestOffer = sortedNegotiations[0]?.amount || null;

    // Find latest demand by filtering negotiations with null previous_offer and sorting by created_at
    const sortedDemands = [...negotiations]
      .filter(n => n.previous_offer === null)
      .sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

    const latestDemandPerDefendant = new Map();
    sortedDemands.forEach(demand => {
      if (demand.defendant?.id && demand.amount) {
        if (!latestDemandPerDefendant.has(demand.defendant.id) ||
          new Date(demand.created_at) > new Date(latestDemandPerDefendant.get(demand.defendant.id).created_at)) {
          latestDemandPerDefendant.set(demand.defendant.id, demand);
        }
      }
    });

    const totalDemandAmount = Array.from(latestDemandPerDefendant.values())
      .reduce((sum, demand) => Number(sum) + (Number(demand.amount) || 0), 0);

    const latestDemand = sortedDemands[0]?.amount || null;

    const allSortedNegotiations = [...negotiations].sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
    const nextDeadline = allSortedNegotiations.find(n => n.response_deadline)?.response_deadline;

    const acceptedOffer = negotiations.find(n => n.type === NegotiationType.ACCEPTED_OFFER)?.amount || null;

    return {
      latestOffer,
      demandAmount: selectedDefendant ? latestDemand : totalDemandAmount,
      nextDeadline,
      acceptedOffer,
      totalPolicyLimit: null
    };
  }, [negotiations, selectedDefendant]);

  const handleEditComplete = () => {
    setEditDemand(null);
    setTimeout(() => {
      refetch();
    }, 100);
  };

  const [showEditDialog, setShowEditDialog] = useState(false);

  const handleFeePercentageChange = (value: string) => {
    // Only call the API if the value is valid
    if (value && !isNaN(parseFloat(value))) {
      // Format to exactly 2 decimal places
      const formattedValue = parseFloat(value).toFixed(2);
      updateSettlementCalculation.mutate({
        fees_percentage: formattedValue,
      });
    }
  };

  const [currentDeleteId, setCurrentDeleteId] = useState<number>(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const deleteNegotiationMutation = useDeleteNegotiationMutation(caseId, currentDeleteId);

  const handleDelete = async (negotiationId: number) => {
    setCurrentDeleteId(negotiationId);
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    await deleteNegotiationMutation.mutateAsync();
    setShowDeleteDialog(false);
  };

  const handleArchiveToggle = async (negotiationId: number, isArchived: boolean) => {
    if (!selectedDefendant) return;
    setCurrentNegotiationId(negotiationId);
    if (updateNegotiationMutation && negotiationId !== 0) {
      await updateNegotiationMutation.mutateAsync({
        is_archived: isArchived
      });
    }
  };

  const handleSettlementCheck = async (negotiation: NegotiationRead) => {
    console.log('negotiation --->', negotiation);
    if (!negotiation.amount || !negotiation.insurance_company?.name) {
      toast({
        title: 'Error',
        description: 'Negotiation amount or insurance company name is missing',
        variant: 'destructive',
      });
      return;
    }

    try {
      await createClientTrust.mutateAsync({
        caseId,
        trustData: {
          check_number: "",
          amount: negotiation.amount.toString(),
          memo: `Settlement payment from ${negotiation.insurance_company.name}`,
          issuer_payee: negotiation.insurance_company.name,
          deposit_date: formatDateForApi(new Date()),
          client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
          deposit_type: DepositType.THIRD_PARTY,
          negotiation_id: negotiation.id
        }
      });
      toast({
        title: 'Success',
        description: 'Settlement check created successfully',
      });
    } catch (error) {
      console.error('Error creating settlement check:', error);
    }
  };

  const checkSettlementStatus = (negotiation: NegotiationRead): ClientTrust | null => {
    if (!clientTrustData || !negotiation.insurance_company?.name) return null;

    const matchingTrust = clientTrustData.find(trust =>
      trust.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT &&
      trust.issuer_payee === negotiation.insurance_company?.name &&
      trust.amount === negotiation.amount?.toString() &&
      trust.memo?.includes(negotiation.insurance_company?.name) &&
      !trust.void
    );

    return matchingTrust || null;
  };

  const handleVoidSettlement = async (negotiation: NegotiationRead) => {
    const matchingTrust = checkSettlementStatus(negotiation);
    if (!matchingTrust?.id) return;

    try {
      await updateClientTrust.mutateAsync({
        caseId,
        id: matchingTrust.id,
        trustData: {
          ...matchingTrust,
          void: true
        }
      });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(caseId) });
    } catch (error) {
      console.error('Error voiding settlement:', error);
      toast({
        title: 'Error',
        description: 'Failed to void settlement check',
        variant: 'destructive',
      });
    }
  };

  // Set the negotiation type based on the context
  const negotiationType: NegotiationTableType = 'third_party';

  if (selectedDefendant === null) {
    return (
      <AllDefendantsView
        caseId={caseId}
        onSelectDefendant={(defendantId) => setSelectedDefendant(defendantId)}
      />
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-3 sm:p-4 space-y-4">
      {/* Enhanced Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 bg-gradient-to-r from-slate-50 to-gray-50 p-3 sm:p-4 rounded-lg shadow-sm">
        <div className="flex md:flex-row justify-between items-start md:items-center gap-3">
          <h1 className="text-xl sm:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-slate-900 to-slate-700">
            Case Negotiations
          </h1>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="h-9 min-w-[180px] justify-between bg-white hover:bg-slate-50 border-slate-200 text-sm"
              >
                {isDefendantsLoading ? (
                  <LoadingSpinner />
                ) : (
                  <>
                    {selectedDefendant ?
                      defendants?.find(d => d.id === selectedDefendant)?.first_name + ' ' +
                      defendants?.find(d => d.id === selectedDefendant)?.last_name :
                      "All Defendants"}
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px]">
              <DropdownMenuItem
                onClick={() => setSelectedDefendant(null)}
                className="cursor-pointer hover:bg-slate-50 text-sm"
              >
                All Defendants
              </DropdownMenuItem>
              {defendants?.map((defendant) => (
                <DropdownMenuItem
                  key={defendant.id}
                  onClick={() => setSelectedDefendant(defendant.id)}
                  className="cursor-pointer hover:bg-slate-50 text-sm"
                >
                  {`${defendant.first_name} ${defendant.last_name}`}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span>
                  <FeePercentageSelect
                    value={
                      calculations?.fees_percentage &&
                        parseFloat(calculations.fees_percentage) > 0
                        ? calculations.fees_percentage
                        : "33.33"
                    }
                    onChange={handleFeePercentageChange}
                    disabled={hasAcceptedOffer}
                  />
                </span>
              </TooltipTrigger>
              {hasAcceptedOffer && (
                <TooltipContent>
                  <p>Unaccept the offer to modify attorney fee</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span>
                  <CreateDemandDialog
                    key={editDemand ? `edit-${editDemand.id}` : 'create-new'}
                    caseId={caseId}
                    defendantId={selectedDefendant}
                    adjusterId={latestAdjusterId}
                    disabled={!selectedDefendant || Boolean(hasAcceptedOffer)}
                    negotiations={negotiations}
                    editDemand={editDemand}
                    onEditComplete={handleEditComplete}
                    onClose={() => setEditDemand(null)}
                  />
                </span>
              </TooltipTrigger>
              {hasAcceptedOffer && (
                <TooltipContent>
                  <p>Unaccept the offer to create new demand</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Redesigned Quick Stats using KPICard */}
      <NegotiationCards
        data={[
          {
            icon: DollarSign,
            title: "Current Demand",
            metrics: {
              primary: {
                type: 'monetary',
                value: quickStats.demandAmount || 0
              }
            },
            bgColor: "bg-blue-50"
          },
          {
            icon: DollarSign,
            title: hasAcceptedOffer ? "Accepted Offer" : "Latest Offer",
            metrics: {
              primary: {
                type: 'monetary',
                value: hasAcceptedOffer ? (quickStats.acceptedOffer || 0) : (quickStats.latestOffer || 0)
              }
            },
            bgColor: hasAcceptedOffer ? "bg-orange-50" : "bg-emerald-50"
          },
          {
            icon: AlertCircle,
            title: "Policy Limit",
            metrics: {
              primary: {
                type: 'number',
                value: !selectedDefendant ? 0 :
                  !insuranceList?.length ? 0 :
                    insuranceList[0]?.policy_limits ? insuranceList[0].policy_limits : 0
              }
            },
            bgColor: "bg-amber-50"
          },
          {
            icon: Calendar,
            title: "Next Deadline",
            metrics: {
              primary: {
                type: 'date',
                value: quickStats.nextDeadline ? formatDateForDisplay(quickStats.nextDeadline) : "",
                unit: 'days'
              }
            },
            bgColor: "bg-rose-50"
          }
        ]}
      />

      {/* Enhanced Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="bg-slate-100 p-1 rounded-lg">
          <TabsTrigger value="overview" className="data-[state=active]:bg-white rounded-md text-sm">
            Overview
          </TabsTrigger>
          <TabsTrigger value="strategy" className="data-[state=active]:bg-white rounded-md text-sm">
            Strategy
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <Card className="col-span-2 border-none shadow-sm">
              <CardContent className="p-0">
                <div className="space-y-0">
                  {organizedNegotiations.map(({ parent, children }: { parent: NegotiationRead; children: NegotiationRead[] }) => {
                    const negotiationId = parent.id.toString();
                    const isAcceptedOffer = parent.type === NegotiationType.ACCEPTED_OFFER;
                    const hasAcceptedChild = children.some(child => child.type === NegotiationType.ACCEPTED_OFFER);
                    // const parentId = negotiationId;

                    // Find defendant type and insurance company for this negotiation
                    const defendantId = typeof parent.defendant === 'object' ? parent.defendant?.id : parent.defendant;
                    const matchingDefendant = defendants?.find(d => d.id === defendantId);
                    const defendantType = matchingDefendant ? `${matchingDefendant.defendant_type}` : '';
                    const insuranceCompany = typeof parent.insurance_company === 'string'
                      ? parent.insurance_company
                      : parent.insurance_company?.name || '';

                    // Find adjuster name
                    const adjusterName = parent.adjuster
                      ? `${parent.adjuster.first_name} ${parent.adjuster.last_name}`
                      : "";

                    // Build header string with only available values
                    const headerParts = [];

                    if (insuranceCompany) {
                      headerParts.push(insuranceCompany);
                    }

                    if (defendantType) {
                      headerParts.push(defendantType);
                    }

                    if (adjusterName) {
                      headerParts.push(adjusterName);
                    }

                    if (parent.amount) {
                      headerParts.push(`Amount $${Number(parent.amount).toLocaleString()}`);
                    }

                    if (parent.date_sent) {
                      headerParts.push(`${parent.type === NegotiationType.INITIAL_DEMAND ? "Demand" : "Offer"} ${formatDateForDisplay(parent.date_sent)}`);
                    }

                    if (parent.response_deadline) {
                      headerParts.push(`exp ${formatDateForDisplay(parent.response_deadline)}`);
                    }

                    if (calculations?.fees_percentage) {
                      const feePercentage = calculations.fees_percentage === '0.00' ? '33.33' : Number(calculations.fees_percentage).toFixed(2);
                      headerParts.push(`Attorney Fee ${feePercentage}%`);
                    }

                    const headerString = headerParts.join(' | ');

                    return (
                      <Fragment key={parent.id}>
                        <ParentOfferDemand<NegotiationRead>
                          parent={parent}
                          isExpanded={expandedDays.includes(negotiationId)}
                          toggleExpand={() => toggleDay(negotiationId)}
                          headerString={headerString}
                          caseId={caseId}
                          selectedDefendant={selectedDefendant}
                          hasAcceptedOffer={hasAcceptedOffer}
                          hasAcceptedChild={hasAcceptedChild}
                          isAcceptedOffer={isAcceptedOffer}
                          onDelete={handleDelete}
                          onAcceptOffer={handleAcceptOffer}
                          onDeacceptOffer={handleDeacceptOffer}
                          onSettlementCheck={handleSettlementCheck}
                          onEditDemand={(negotiation) => {
                            setEditDemand(negotiation);
                            setShowEditDialog(true);
                          }}
                          onArchiveToggle={handleArchiveToggle}
                          checkSettlementStatus={checkSettlementStatus}
                          handleVoidSettlement={handleVoidSettlement}
                          insuranceCompany={parent.insurance_company}
                        />

                        {expandedDays.includes(negotiationId) && (
                          <NegotiationTable<NegotiationRead>
                            negotiations={children}
                            parentId={parent.id}
                            caseId={caseId}
                            selectedDefendant={selectedDefendant}
                            onAcceptOffer={handleAcceptOffer}
                            onDeacceptOffer={handleDeacceptOffer}
                            hasAcceptedOffer={hasAcceptedOffer}
                            onDelete={handleDelete}
                            parentIsArchived={parent.is_archived}
                            insuranceList={insuranceList}
                            negotiationType={negotiationType}
                            parentInsuranceCompany={parent.insurance_company}
                            acceptedOfferId={negotiations?.find(n => n.type === NegotiationType.ACCEPTED_OFFER)?.id}
                            checkSettlementStatus={checkSettlementStatus}
                            handleVoidSettlement={handleVoidSettlement}
                          />
                        )}
                      </Fragment>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <KeyInformation
              negotiationType="third_party"
              adjusters={adjusters as unknown as AdjusterInfo[]}
              isLoading={isAdjustersLoading}
              policyLimit={selectedDefendant && insuranceList?.length ? insuranceList[0]?.policy_limits : null}
            />
          </div>
        </TabsContent>

        {/* Strategy Tab Content */}
        <TabsContent value="strategy">
          <div className="mt-4">
            {selectedDefendant ? (
              <Tabs defaultValue="tasks" className="w-full">
                <TabsList className="bg-slate-100 p-1 rounded-lg grid grid-cols-2">
                  <TabsTrigger value="tasks" className="data-[state=active]:bg-white rounded-md text-sm">
                    Tasks
                  </TabsTrigger>
                  <TabsTrigger value="notes" className="data-[state=active]:bg-white rounded-md text-sm">
                    Notes
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="tasks">
                  <CaseTasks
                    caseId={caseId}
                    taskFor="Negotiation"
                    defendantId={selectedDefendant?.toString()}
                  />
                </TabsContent>

                <TabsContent value="notes">
                  <CaseNotes
                    caseId={caseId}
                    noteFor="Negotiation"
                    defendantId={selectedDefendant?.toString()}
                  />
                </TabsContent>
              </Tabs>
            ) : (
              <div className="text-center py-8 bg-slate-50 rounded-lg">
                <p className="text-gray-500 text-sm">Please select a defendant to view tasks and notes.</p>
                <p className="text-xs text-gray-400 mt-1">Tasks and notes are specific to each defendant.</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {editDemand && showEditDialog && (
        <CreateDemandDialog
          key={`edit-${editDemand.id}-${showEditDialog}`}
          caseId={caseId}
          defendantId={selectedDefendant}
          adjusterId={latestAdjusterId}
          disabled={!selectedDefendant || Boolean(hasAcceptedOffer) || hasArchivedDemand}
          negotiations={negotiations}
          editDemand={editDemand}
          onEditComplete={() => {
            handleEditComplete();
            setShowEditDialog(false);
          }}
          onClose={() => {
            setShowEditDialog(false);
            setEditDemand(null);
          }}
        />
      )}

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleConfirmDelete}
        text="Negotiation"
      />
    </div>
  );
};

export default Negotiation;