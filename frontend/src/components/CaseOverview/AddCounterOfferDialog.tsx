import React, { FC, useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { Form, FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import {
  useCreateNegotiationMutation,
  useCreateUIMNegotiationMutation,
  useUpdateNegotiationMutation,
  useUpdateUIMNegotiationMutation
} from '@/services/negotiationService';
import {
  NegotiationStatus,
  NegotiationType,
  NegotiationRead,
  NegotiationUIMRead,
  NegotiationUIMWrite,
  NegotiationWrite
} from '@/type/negotiationTypes';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { format, parse } from "date-fns";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { RequiredLabel } from '../ui/required-label';
import { Edit } from 'lucide-react';
import { formatDateForInput, formatDateForApi } from '@/utils/dateUtils';
import { InsuranceCompany } from '@/type/case-management/orgTypes';
import { nullToUndefined } from '@/utils/typeUtils';
import { AdjusterContact } from '@/type/case-management/orgTypes';

interface AddCounterOfferDialogProps {
  caseId: string;
  defendantId: number | null;
  previousOfferId: number;
  initialData?: NegotiationRead | NegotiationUIMRead;
  isUIM?: boolean;
  isEdit?: boolean;
  clientInsuranceId?: number | null;
  onEditComplete?: () => void;
  disabled?: boolean;
  insuranceCompany?: InsuranceCompany | { id: number | string; name: string; };
  adjuster?: AdjusterContact | null;
}

const counterOfferSchema = z.object({
  amount: z.string().min(1, "Amount is required"),
  notes: z.string().optional(),
  response_deadline: z.string().min(1, "Response deadline is required"),
  date_sent: z.string().min(1, "Date sent is required"),
});

type CounterOfferFormData = z.infer<typeof counterOfferSchema>;

export const AddCounterOfferDialog: FC<AddCounterOfferDialogProps> = ({
  caseId,
  defendantId,
  previousOfferId,
  initialData,
  isEdit = false,
  isUIM = false,
  clientInsuranceId = null,
  onEditComplete,
  disabled = false,
  insuranceCompany,
  adjuster
}): JSX.Element => {
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const createMutation = useCreateNegotiationMutation(caseId);
  const createUIMNegotiationMutation = useCreateUIMNegotiationMutation(caseId);
  const updateMutation = useUpdateNegotiationMutation(caseId, initialData?.id ?? 0);
  const updateUIMNegotiationMutation = useUpdateUIMNegotiationMutation(caseId, initialData?.id ?? 0);
  const form = useForm<CounterOfferFormData>({
    resolver: zodResolver(counterOfferSchema),
    defaultValues: {
      amount: initialData && isEdit ? String(initialData.amount) : "",
      notes: initialData && isEdit ? initialData.notes || "" : "",
      response_deadline: initialData && isEdit ? initialData.response_deadline || "" : "",
      date_sent: initialData && isEdit ? initialData.date_sent || formatDateForApi(new Date()) : formatDateForApi(new Date()),
    }
  });

  console.log(`☄️☄️ previousOfferId ----------------------->☄️☄️`, previousOfferId);

  useEffect(() => {
    if (initialData && isEdit) {
      form.setValue('amount', String(initialData.amount));
      form.setValue('notes', initialData.notes || "");
      form.setValue('response_deadline', initialData.response_deadline || "");
      form.setValue('date_sent', initialData.date_sent || formatDateForApi(new Date()));
    }
  }, [initialData, form, isEdit]);

  // Debug log for previousOfferId changes
  useEffect(() => {
    console.log('Previous Offer ID changed:', {
      previousOfferId,
      timestamp: new Date().toISOString(),
    });
  }, [previousOfferId]);

  const handleOpenChange = (newOpen: boolean) => {
    if (disabled) return;

    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset({
      amount: "",
      notes: "",
      response_deadline: formatDateForApi(new Date()),
      date_sent: formatDateForApi(new Date())
    });
    if (onEditComplete) {
      setShowUnsavedAlert(false);
      onEditComplete();
    }
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const onSubmit = async (data: CounterOfferFormData) => {
    try {
      if (isUIM) {
        if (!clientInsuranceId && !initialData?.client_insurance?.id) {
          throw new Error('Client insurance is required for UIM negotiations');
        }

        // Handle both string and number IDs for insurance company
        const getInsuranceCompanyId = (company: InsuranceCompany | { id: number | string; name: string; } | null | undefined): number => {
          if (!company) return 0;
          
          const id = nullToUndefined(company)?.id;
          if (!id) {
            throw new Error('Insurance company ID is required');
          }
          
          // Convert string ID to number if needed
          return typeof id === 'string' ? Number(id) : id;
        };
        
        console.log("initialData  ---> uim", initialData);
        const insuranceCompanyId = getInsuranceCompanyId(insuranceCompany || initialData?.insurance_company);
        
        if (!insuranceCompanyId) {
          throw new Error('Valid insurance company ID is required');
        }

        const getDateValue = (fieldName: keyof CounterOfferFormData, value: string | undefined, originalValue: string | null | undefined) => {
          if (isEdit && initialData && !form.formState.dirtyFields[fieldName]) {
            return originalValue || undefined;
          }
          return value || undefined;
        };

        const baseData = {
          amount: data.amount,
          type: NegotiationType.COUNTER_OFFER,
          notes: data.notes || "",
          status: NegotiationStatus.SENT,
          response_deadline: getDateValue('response_deadline', data.response_deadline, initialData?.response_deadline),
          date_sent: getDateValue('date_sent', data.date_sent, initialData?.date_sent),
          client_insurance: clientInsuranceId || initialData?.client_insurance?.id,
          previous_offer: previousOfferId,
          adjuster: adjuster?.id,
          insurance_company: insuranceCompanyId,
          settlement_terms: nullToUndefined(initialData?.settlement_terms),
          payment_terms: nullToUndefined(initialData?.payment_terms),
          conditions: nullToUndefined(initialData?.conditions),
          release_terms: nullToUndefined(initialData?.release_terms)
        } as const;
        console.log(`☄️☄️ baseData ----------------------->☄️☄️`, baseData);
        
        // Modified filtering logic to handle date fields properly
        const filteredEntries = Object.entries(baseData).filter(([key, value]) => {
          // Always include required fields and date fields
          if (key === 'amount' || key === 'type' || key === 'status' || key === 'insurance_company') return true;
          // Include date fields if they have a value
          if ((key === 'response_deadline' || key === 'date_sent') && value) return true;
          // Include other fields if they have a non-empty value
          return value !== undefined && value !== "" && value !== null;
        });

        console.log(`☄️☄️ filteredEntries ----------------------->☄️☄️`, filteredEntries);
        
        // Create the cleaned data with required properties
        const cleanedData: NegotiationUIMWrite = {
          amount: data.amount,
          type: NegotiationType.COUNTER_OFFER,
          status: NegotiationStatus.SENT,
          client_insurance: isEdit 
            ? (clientInsuranceId ?? initialData?.client_insurance?.id) as number 
            : (clientInsuranceId ?? initialData?.client_insurance?.id ?? 0) as number,
          insurance_company: insuranceCompanyId,
          ...Object.fromEntries(filteredEntries.filter(([key]) => 
            !['amount', 'type', 'status', 'insurance_company'].includes(key)
          ))
        };
        
        console.log(`☄️☄️ cleanedData ----------------------->☄️☄️`, cleanedData);
        console.log(`Client insurance ID details:`, {
          clientInsuranceId,
          initialDataId: initialData?.client_insurance?.id,
          finalValue: cleanedData.client_insurance,
          isEdit
        });
        if (isEdit && initialData && updateUIMNegotiationMutation) {
          await updateUIMNegotiationMutation.mutateAsync(cleanedData);
          handleClose();
        } else {
          await createUIMNegotiationMutation.mutateAsync(cleanedData);
          handleClose();
        }
      } else {
        if (!defendantId) {
          throw new Error('Defendant is required for negotiations');
        }

        // Handle both string and number IDs for insurance company
        const getInsuranceCompanyId = (company: InsuranceCompany | { id: number | string; name: string; } | null | undefined): number => {
          if (!company) return 0;
          
          const id = nullToUndefined(company)?.id;
          if (!id) {
            throw new Error('Insurance company ID is required');
          }
          
          // Convert string ID to number if needed
          return typeof id === 'string' ? Number(id) : id;
        };

        const insuranceCompanyId = getInsuranceCompanyId(insuranceCompany || initialData?.insurance_company);
        
        if (!insuranceCompanyId) {
          throw new Error('Valid insurance company ID is required');
        }

        const getDateValue = (fieldName: keyof CounterOfferFormData, value: string | undefined, originalValue: string | null | undefined) => {
          if (isEdit && initialData && !form.formState.dirtyFields[fieldName]) {
            return originalValue || undefined;
          }
          return value || undefined;
        };

        const baseData = {
          amount: data.amount,
          type: NegotiationType.COUNTER_OFFER,
          notes: data.notes || "",
          status: NegotiationStatus.SENT,
          response_deadline: getDateValue('response_deadline', data.response_deadline, initialData?.response_deadline),
          date_sent: getDateValue('date_sent', data.date_sent, initialData?.date_sent),
          defendant: defendantId || undefined,
          previous_offer: previousOfferId,
          adjuster: adjuster?.id,
          insurance_company: insuranceCompanyId,
          settlement_terms: nullToUndefined(initialData?.settlement_terms),
          payment_terms: nullToUndefined(initialData?.payment_terms),
          conditions: nullToUndefined(initialData?.conditions),
          release_terms: nullToUndefined(initialData?.release_terms)
        } as const;
        console.log(`☄️☄️ baseData ----------------------->☄️☄️`, baseData);
        
        // Modified filtering logic to handle date fields properly
        const filteredEntries = Object.entries(baseData).filter(([key, value]) => {
          // Always include required fields and date fields
          if (key === 'amount' || key === 'type' || key === 'status' || key === 'insurance_company') return true;
          // Include date fields if they have a value
          if ((key === 'response_deadline' || key === 'date_sent') && value) return true;
          // Include other fields if they have a non-empty value
          return value !== undefined && value !== "" && value !== null;
        });

        console.log(`☄️☄️ filteredEntries ----------------------->☄️☄️`, filteredEntries);
        
        // Create the cleaned data with required properties
        const requiredFields = {
          amount: data.amount,
          type: NegotiationType.COUNTER_OFFER,
          status: NegotiationStatus.SENT,
          insurance_company: insuranceCompanyId,
        } as const;
        
        const optionalFields = Object.fromEntries(
          filteredEntries.filter(([key]) => 
            !['amount', 'type', 'status', 'insurance_company'].includes(key)
          )
        );

        const cleanedData: NegotiationWrite = {
          ...requiredFields,
          ...optionalFields
        };
        
        console.log(`☄️☄️ cleanedData ----------------------->☄️☄️`, cleanedData);
        if (isEdit && initialData && updateMutation) {
          const updateData = {
            ...cleanedData,
            defendant: defendantId || undefined // Ensure null is converted to undefined
          };
          await updateMutation.mutateAsync(updateData);
          handleClose();
        } else {
          await createMutation.mutateAsync(cleanedData);
          handleClose();
        }
      }
    } catch (error) {
      // console.error('Error creating/updating counter offer:', error);
      console.error(error, 'Error details:', {
        initialData,
        isUIM,
        defendantId,
        clientInsuranceId,
        previousOfferId,
        insuranceCompany: insuranceCompany ? {
          id: insuranceCompany.id,
          name: insuranceCompany.name,
          type: typeof insuranceCompany.id
        } : 'No insurance company'
      });
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEdit ? (
            <Edit className={`h-4 w-4 ${disabled ? 'text-gray-400' : 'text-green-600'}`} />
          ) : (
            <div className={`border-green-600 text-green-600 hover:bg-green-50 hover:text-green-600 text-xs p-2 border rounded-md ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
              Offer
            </div>
          )}
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEdit ? 'Edit Offer' : 'Add Insurance Offer'}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Amount</RequiredLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        placeholder="Enter amount"
                        required
                      />
                    </FormControl>
                    {form.formState.errors.amount && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.amount.message}
                      </p>
                    )}
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="response_deadline"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Response Deadline </RequiredLabel>
                    <FormControl>
                      <CustomDateInput
                        value={field.value ? formatDateForInput(field.value) : ""}
                        onChange={(value: string) => {
                          // Parse the string value (dd-MM-yyyy) into a Date object
                          try {
                            const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                            if (!isNaN(parsedDate.getTime())) {
                              // Pass the Date object to form
                              field.onChange(format(parsedDate, "yyyy-MM-dd"));
                            }
                          } catch (error) {
                            console.error("Error parsing date:", error);
                          }
                        }}
                        error={!!form.formState.errors.response_deadline}
                        minDate={new Date()}
                        onError={(message: string) => {
                          form.setError("response_deadline", {
                            type: "manual",
                            message
                          });
                        }}
                      />
                    </FormControl>
                    {form.formState.errors.response_deadline && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.response_deadline.message}
                      </p>
                    )}
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="date_sent"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Sent Date</RequiredLabel>
                    <FormControl>
                      <CustomDateInput
                        value={field.value ? formatDateForInput(field.value) : ""}
                        onChange={(value: string) => {
                          try {
                            const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                            if (!isNaN(parsedDate.getTime())) {
                              field.onChange(format(parsedDate, "yyyy-MM-dd"));
                            }
                          } catch (error) {
                            console.error("Error parsing date:", error);
                          }
                        }}
                        error={!!form.formState.errors.date_sent}
                        maxDate={new Date()}
                        onError={(message: string) => {
                          form.setError("date_sent", {
                            type: "manual",
                            message
                          });
                        }}
                      />
                    </FormControl>
                    {form.formState.errors.date_sent && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.date_sent.message}
                      </p>
                    )}
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter notes"
                      />
                    </FormControl>
                    {form.formState.errors.notes && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.notes.message}
                      </p>
                    )}
                  </FormItem>
                )}
              />
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createMutation.isPending}
                >
                  {createMutation.isPending ? 'Saving...' : isEdit ? 'Save Changes' : 'Add Insurance Offer'}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleClose}
        onCancel={() => setShowUnsavedAlert(false)}
      />
    </>
  );
}; 