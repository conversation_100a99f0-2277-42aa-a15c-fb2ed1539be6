"use client";

import {
  Plus,
  Edit,
  Trash2,
  Repeat,
  ChevronUp,
  ChevronDown,
  Loader2,
  UserRound,
  Shield,
  FileText
} from "lucide-react";
import { CreateUpdateDefendant } from "@/app/defendant/components/CreateUpdateDefendant";
import {
  useDefendantDetailQuery,
  useDefendantPropertyDamageListQuery,
  useDefendantsListQuery,
  useDeleteDefendantMutation,
  useSyncDefendantMutation
} from "@/services/case-management/defendantService";
import { useLinkedCasesQuery } from "@/services/case-management/linkCaseService";
import { Skeleton } from "@/components/ui/skeleton";
import DefendantInsuranceDetails from "@/app/defendant/components/DefendantInsuranceDetails";
import DefendantInsuranceDetailsEditDialog from "@/app/defendant/components/DefendantInsuranceDetailsEditDialog";
import { AddEditDefendantPropertyDamage } from "@/app/defendant/components/AddEditDefendantPropertyDamage";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DefendantListItem } from "@/type/case-management/defendantTypes";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { MouseEvent, ReactNode, useState } from "react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { InfoFieldGroup } from "../ui/InfoField";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { CaseBasicDetails } from "@/type/linkCaseType";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Card, CardContent } from "../ui/card";
import { Badge } from "../ui/badge";
import LegalRepresentation from "@/app/client-details/components/LegalRepresentation";
import AddEditInsuranceLegalRepresentation from "@/app/client-details/components/AddEditInsuranceLegalRepresentation";

interface DefendantPageProps {
  caseId: string;
  defendantId?: string;
}

interface DamageField {
  label: string;
  value: ReactNode | string | null;
}

function PropertyDamageSection({
  caseId,
  defendantId,
}: {
  caseId: string;
  defendantId: string;
}) {
  const { data: propertyDamages, isLoading } =
    useDefendantPropertyDamageListQuery(caseId, defendantId);

  if (isLoading) {
    return <Skeleton className="w-full h-[200px]" />;
  }

  if (!propertyDamages) {
    return null;
  }

  const damageFields: DamageField[] = [
    {
      label: "Property Damage Type",
      value: propertyDamages.damage || "—",
    },
    {
      label: "Frame Damage",
      value: propertyDamages.frame_damage ? "Yes" : "—",
    },
    {
      label: "Total Loss",
      value: propertyDamages.total_loss ? "Yes" : "—",
    },
    {
      label: "Registered Owner",
      value: propertyDamages.registered_owner || "—",
    },
    {
      label: "Auto Body Shop",
      value: propertyDamages.auto_body_shop || "—",
    },
    {
      label: "Estimate",
      value: propertyDamages.estimate ? `$${propertyDamages.estimate}` : "—",
    },
    {
      label: "Final",
      value: propertyDamages.final ? `$${propertyDamages.final}` : "—",
    },
    {
      label: "Make",
      value: propertyDamages.make || "—",
    },
    {
      label: "Model",
      value: propertyDamages.model || "—",
    },
    {
      label: "Color",
      value: propertyDamages.color || "—",
    },
    {
      label: "Year",
      value: propertyDamages.year || "—",
    },
    {
      label: "Plate #",
      value: propertyDamages.plate || "—",
    },
    {
      label: "VIN #",
      value: propertyDamages.vin || "—",
    },
    {
      label: "Mileage",
      value: propertyDamages.mileage || "—",
    },
  ];

  if (propertyDamages.note) {
    damageFields.push({
      label: "Note",
      value: propertyDamages.note ? <RichTextViewer data={propertyDamages.note} /> : "—"
    });
  }

  return (
    <Card className="border-none p-0">
      <div className="flex items-center justify-between gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
        <div className="flex items-center gap-2">
          <FileText className="w-5 h-5 text-[#060216]" />
          <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
            Property Damage
          </h2>
        </div>
        <AddEditDefendantPropertyDamage
          isEdit={true}
          caseId={caseId}
          defendantId={defendantId}
          propertyDamage={propertyDamages}
        >
          <Button
            variant="link"
          >
            <Edit className="h-4 w-4 text-green-600" />
          </Button>
        </AddEditDefendantPropertyDamage>
      </div>
      <CardContent> 
        <InfoFieldGroup
          fields={damageFields.map(field => ({
            label: field.label,
            value: field.value
          }))}
        />
      </CardContent>
    </Card>
  );
}

function DefendantCard({ caseId, defendant }: { caseId: string; defendant: DefendantListItem }) {
  const defendantId = defendant.id.toString();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const deleteDefendant = useDeleteDefendantMutation(caseId);
  const syncDefendant = useSyncDefendantMutation(caseId);
  const { data: linkedCases } = useLinkedCasesQuery(caseId);
  const { toast } = useToast();

  const allLinkedCases: CaseBasicDetails[] = [
    ...(linkedCases?.direct_cases || []),
    ...(linkedCases?.indirect_cases || [])
  ];

  const handleSync = async () => {
    try {
      if (!allLinkedCases.length) {
        toast({
          title: "Error",
          description: "No linked cases found to sync with",
          variant: "destructive",
        });
        return;
      }

      // Get the actual case IDs
      const caseIds = allLinkedCases.map((c: CaseBasicDetails) => c.id);

      if (caseIds.length === 0) {
        toast({
          title: "Error",
          description: "Could not get valid case IDs for syncing",
          variant: "destructive",
        });
        return;
      }

      await syncDefendant.mutateAsync({
        source_defendant_id: Number(defendantId),
        target_case_ids: caseIds,
        sync_options: {
          sync_insurances: true,
          sync_property_damage: true,
          sync_legal_representation: true,
          sync_adjusters: true
        }
      });
    } catch (error) {
      console.error("Error syncing defendant:", error);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteDefendant.mutateAsync(defendantId);
    } catch (error) {
      console.error("Error deleting defendant:", error);
    }
  };

  const {
    data: defendantDetails,
    isLoading: isDefendantLoading,
    error,
  } = useDefendantDetailQuery(caseId, defendantId);

  const { data: propertyDamage } = useDefendantPropertyDamageListQuery(
    caseId,
    defendantId
  );

  if (isDefendantLoading) {
    return <Skeleton className="w-full h-[500px]" />;
  }

  if (error || !defendantDetails) {
    return (
      <div className="p-8 text-red-500">Error loading defendant details</div>
    );
  }

  const toggleExpand = (e: MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <Card className="relative">
      <CardContent className="p-0">
        {/* Header Section - Always visible */}
        <div className="flex justify-between items-start p-6" onClick={toggleExpand}>
          <div className="flex items-center gap-3 cursor-pointer">
            <div>
              <h3 className="text-lg font-semibold text-[#060216]">
                {defendantDetails.first_name} {defendantDetails.last_name}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="capitalize text-xs bg-blue-50 text-blue-600">
                  {defendantDetails.defendant_type.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(" ")}
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleExpand}
              className="hover:bg-gray-100 h-8 w-8 rounded-full"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </Button>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className={`${(!allLinkedCases.length || syncDefendant.isPending) ? 'cursor-not-allowed' : ''}`}>
                    <Button
                      variant="outline"
                      className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed w-full p-0"
                      onClick={handleSync}
                      disabled={syncDefendant.isPending || !allLinkedCases.length}
                    >
                      {syncDefendant.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Repeat className="h-4 w-4 mr-2" />
                      )}
                    </Button>
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  {!allLinkedCases.length
                    ? "No linked cases available to sync with. Please link cases first."
                    : "Sync defendant data to linked cases"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DefendantInsuranceDetailsEditDialog
              isEdit={false}
              caseId={caseId}
              defendantId={defendantId}
            />
            {!propertyDamage && (
              <AddEditDefendantPropertyDamage
                isEdit={false}
                caseId={caseId}
                defendantId={defendantId}
              >
                <Button
                  variant="link"
                  className="text-green-600"
                >
                  <Plus className="h-4 w-4" />
                  Property Damage
                </Button>
              </AddEditDefendantPropertyDamage>
            )}
            <CreateUpdateDefendant
              isEditing={true}
              caseId={caseId}
              formData={defendantDetails}
            />
            <Button
              variant="ghost"
              size="icon"
              className="hover:bg-red-50 hover:text-red-600"
              onClick={() => setDeleteDialogOpen(true)}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
            <ManageEmailTemplate
              caseId={caseId}
              defendantId={defendantId}
              contextType={"defendant" as TemplateContextType}
              className="h-8 w-8"
            />
          </div>
        </div>

        {/* Expandable Content */}
        {isExpanded && (
          <>
            {/* Personal Information */}
            <div className="px-6 pb-6">
              <div className="space-y-6">
                <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
                  <UserRound className="w-5 h-5 text-[#060216]" />
                  <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                    Personal Information
                  </h2>
                </div>

                <InfoFieldGroup
                  fields={[
                    {
                      label: "Type",
                      value: defendantDetails.defendant_type.split("_").map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(" ")
                    },
                    {
                      label: "Company/Entity",
                      value: defendantDetails.company_entity
                    },
                    {
                      label: "Description",
                      value: defendantDetails.description ? <RichTextViewer data={defendantDetails.description} /> : null
                    },
                    {
                      label: "First Name",
                      value: defendantDetails.first_name
                    },
                    {
                      label: "Middle Name",
                      value: defendantDetails.middle_name
                    },
                    {
                      label: "Last Name",
                      value: defendantDetails.last_name
                    },
                    {
                      label: "Registered Agent",
                      value: defendantDetails.registered_agent
                    },
                    {
                      label: "Gender",
                      value: defendantDetails.gender ? defendantDetails.gender.toLowerCase() : null
                    },
                    {
                      label: "SSN",
                      value: defendantDetails.ssn
                    },
                    {
                      label: "Date of Birth",
                      value: defendantDetails.date_of_birth,
                      isDate: true
                    },
                    {
                      label: "Driver's License",
                      value: defendantDetails.driver_license_type ? `${defendantDetails.driver_license_type} - ${defendantDetails.driver_license}` : null
                    },
                    {
                      label: "Language",
                      value: defendantDetails.language ? defendantDetails.language.toLowerCase() : null
                    },
                    {
                      label: "Race",
                      value: defendantDetails.race ? defendantDetails.race.toLowerCase() : null
                    }
                  ]}
                />
              </div>

              <Separator className="w-full my-5" />

              {/* Contact Information */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
                  <UserRound className="w-5 h-5 text-[#060216]" />
                  <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                    Contact Information
                  </h2>
                </div>

                <InfoFieldGroup
                  caseId={caseId}
                  fields={[
                    {
                      label: "Phone",
                      value: defendantDetails.phone + (defendantDetails.phone_ext ? ` ext. ${defendantDetails.phone_ext}` : ""),
                      isPhone: true
                    },
                    {
                      label: "Cell",
                      value: defendantDetails.cell,
                      isPhone: true
                    },
                    {
                      label: "Email",
                      value: defendantDetails.email,
                      isMail: true
                    },
                    {
                      label: "Fax",
                      value: defendantDetails.fax,
                      isPhone: true
                    },
                    {
                      label: "Address",
                      value: defendantDetails.street1 ? (
                        <>
                          {defendantDetails.street1}
                          {defendantDetails.street2 && `, ${defendantDetails.street2}`}
                          {defendantDetails.city && `, ${defendantDetails.city}`}
                          {defendantDetails.state && `, ${defendantDetails.state}`}
                          {defendantDetails.zip_code && ` ${defendantDetails.zip_code}`}
                        </>
                      ) : null,
                      className: "col-span-2"
                    }
                  ]}
                />
              </div>

              <Separator className="w-full my-5" />

              {/* Insurance Details Section */}
              <div>
                <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
                  <Shield className="w-5 h-5 text-[#060216]" />
                  <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                    Insurance Details
                  </h2>
                </div>
                <DefendantInsuranceDetails caseId={caseId} defendantId={defendantId} />
              </div>

              <Separator className="w-full my-5" />

              <div>
                <div className="flex items-center justify-between gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
                  <div className="flex items-center gap-2">
                    <FileText className="w-5 h-5 text-[#060216]" />
                    <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                      Legal Representation
                    </h2>
                  </div>
                  <AddEditInsuranceLegalRepresentation
                    caseId={caseId}
                    insuranceId={""}
                    defendantId={defendantId}
                  />
                </div>
                <div className="text-[#060216]">
                  <LegalRepresentation
                    caseId={caseId}
                    insuranceId={""}
                    defendantId={defendantId}
                  />
                </div>
              </div>

              <Separator className="w-full my-5" />

              {/* Property Damage Section */}
              <div>
                {/* <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200">
                  <FileText className="w-5 h-5 text-[#060216]" />
                  <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                    Property Damage
                  </h2>
                </div> */}
                <PropertyDamageSection caseId={caseId} defendantId={defendantId} />
              </div>
            </div>
          </>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDelete}
        text="Defendant"
      />
    </Card>
  );
}

function DefendantDetails({ caseId, defendantId }: DefendantPageProps) {
  console.log("defendantId", defendantId);
  // First fetch all defendants for the case
  const { data: defendants, isLoading: isDefendantsLoading } = useDefendantsListQuery(caseId);

  if (isDefendantsLoading) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex justify-end">
          <CreateUpdateDefendant isEditing={false} caseId={caseId} />
        </div>
        <Skeleton className="w-full h-[500px]" />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          Defendants
        </h2>
        <CreateUpdateDefendant isEditing={false} caseId={caseId} />
      </div>
      {defendants && defendants.length > 0 ? (
        defendants.map((defendant) => (
          <DefendantCard
            key={defendant.id}
            caseId={caseId}
            defendant={defendant}
          />
        ))
      ) : (
        <div className="p-8 text-gray-500 flex items-center justify-center">No defendants found</div>
      )}
    </div>
  );
}

export default DefendantDetails;
