import React from "react";
import { Card, CardContent } from "@/components/ui/card";

export function MiscLiens() {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Miscellaneous Liens</h2>
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Lienholder Name</label>
                  <input
                    type="text"
                    className="w-full mt-1 p-2 border rounded"
                    placeholder="Enter lienholder name"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Lien Amount</label>
                  <input
                    type="number"
                    className="w-full mt-1 p-2 border rounded"
                    placeholder="Enter lien amount"
                  />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Lien Type</label>
                <select className="w-full mt-1 p-2 border rounded">
                  <option value="">Select lien type</option>
                  <option value="medical">Medical</option>
                  <option value="government">Government</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Notes</label>
                <textarea
                  className="w-full mt-1 p-2 border rounded"
                  rows={3}
                  placeholder="Enter additional notes"
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
