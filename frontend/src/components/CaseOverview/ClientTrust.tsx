"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Trash2, Edit } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useQueryClient } from '@tanstack/react-query';
import {
  useCreateClientTrustMutation,
  useClientTrustSummaryQuery,
  use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  useUpdateClient<PERSON>rustMutation,
  useDeleteClientTrustMutation
} from "@/services/case-management/clientTrustService";
import { useMedicalProvidersQuery } from "@/services/orgAPIs";
import { useCaseCostsQuery } from "@/services/case-management/costService";
import { CostFor, CostStatus } from "@/type/case-management/costTypes";
import {
  ClientTrust as ClientTrustType,
  ClientTrustEntryType,
  TRUST_ENTRY_DISPLAY_VALUES
} from "@/type/case-management/clientTrustTypes";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { formatDateForDisplay, formatDateForInput, formatDateForApi } from "@/utils/dateUtils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Plus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CurrencyDisplay } from "@/components/ui/currency-display";
import { RequiredLabel } from "../ui/required-label";
import { cn } from "@/lib/utils";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

const formSchema = z.object({
  check_number: z.string().optional(),
  amount: z.string().min(1, "Amount is required"),
  memo: z.string().optional(),
  deposit_date: z.string().min(1, "Date is required"),
  demand_type: z.string().optional(),
  client_trust_entry_type: z.nativeEnum(ClientTrustEntryType),
  medical_provider_id: z.number().nullable().optional(),
  issuer_payee: z.string().optional(),
  void: z.boolean().optional(),
});

type FormData = z.infer<typeof formSchema> & {
  void?: boolean;
};

const FORM_TITLES = TRUST_ENTRY_DISPLAY_VALUES;

export const DEFAULT_NOTES: Record<ClientTrustEntryType, string> = {
  [ClientTrustEntryType.CLIENT_PAYMENT]: "Payment issued to client for settlement funds",
  [ClientTrustEntryType.FEE_REIMBURSEMENT]: "Fee reimbursement for legal services rendered",
  [ClientTrustEntryType.TRUST_REIMBURSEMENT]: "Trust account reimbursement",
  [ClientTrustEntryType.SETTLEMENT_PAYMENT]: "Settlement check received",
  [ClientTrustEntryType.ATTORNEY_PAYMENT]: "Payment issued to attorney for legal services",
  [ClientTrustEntryType.OTHER_CHECK]: "Other check issued",
  [ClientTrustEntryType.OTHER_DEPOSIT]: "Other deposit received",
  [ClientTrustEntryType.PROVIDER_PAYMENT]: "Provider check received",
  [ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST]: "Payment for Brumley Law Firm costs",
  [ClientTrustEntryType.REFERRED_FEE]: "Referred fee payment",
  [ClientTrustEntryType.MEDPAY_DEPOSIT]: "MEDPAY DEPOSIT"
};

export const DEFAULT_ISSUER_PAYEE: Record<ClientTrustEntryType, string> = {
  [ClientTrustEntryType.CLIENT_PAYMENT]: "Client",
  [ClientTrustEntryType.FEE_REIMBURSEMENT]: "Brumley Law Firm",
  [ClientTrustEntryType.TRUST_REIMBURSEMENT]: "Brumley Law Firm",
  [ClientTrustEntryType.SETTLEMENT_PAYMENT]: "Insurance Company",
  [ClientTrustEntryType.ATTORNEY_PAYMENT]: "Attorney",
  [ClientTrustEntryType.OTHER_CHECK]: "Other",
  [ClientTrustEntryType.OTHER_DEPOSIT]: "Other",
  [ClientTrustEntryType.PROVIDER_PAYMENT]: "Provider",
  [ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST]: "Brumley Law Firm",
  [ClientTrustEntryType.REFERRED_FEE]: "Brumley Law Firm",
  [ClientTrustEntryType.MEDPAY_DEPOSIT]: "MEDPAY DEPOSIT"
};
  
interface ClientTrustProps {
  caseId: string;
}

type ExtendedClientTrust = ClientTrustType & {
  id?: number;
  balance?: string;
  void?: boolean;
};

export function ClientTrust({ caseId }: ClientTrustProps) {
  const queryClient = useQueryClient();
  const { data: trustSummary } = useClientTrustSummaryQuery(caseId);
  const { data: trustTransactions, isLoading: isTransactionsLoading } = useClientTrustQuery(caseId);
  const { data: caseCosts } = useCaseCostsQuery(caseId, CostFor.CASE_COST);
  const { data: litigationCosts } = useCaseCostsQuery(caseId, CostFor.LITIGATION_COST);
  const [activeForm, setActiveForm] = useState<ClientTrustEntryType | null>(null);
  const [mutationErrors, setMutationErrors] = useState<{ [key: string]: string[] }>({});
  const [selectedTransaction, setSelectedTransaction] = useState<ExtendedClientTrust | null>(null);
  const [selectedEntryType, setSelectedEntryType] = useState<ClientTrustEntryType>(ClientTrustEntryType.SETTLEMENT_PAYMENT);
  const [searchParams] = useState({
    name: "",
    specialty: "",
    city: "",
    page: 1,
    page_size: 10
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [transactionToDelete, setTransactionToDelete] = useState<ExtendedClientTrust | null>(null);

  const { data: providersData } = useMedicalProvidersQuery(searchParams);

  const createClientTrust = useCreateClientTrustMutation(queryClient);
  const updateClientTrust = useUpdateClientTrustMutation(queryClient);
  const deleteClientTrust = useDeleteClientTrustMutation(queryClient);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      check_number: "",
      amount: "",
      memo: "",
      deposit_date: "",
      client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
      issuer_payee: "",
    },
  });

  console.log(mutationErrors);

  const onSubmit = async (values: FormData) => {
    try {
      setMutationErrors({});

      const isValid = await form.trigger();
      if (!isValid) {
        return;
      }

      // Determine if this is a deposit based on the entry type
      const isDeposit = values.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT ||
        values.client_trust_entry_type === ClientTrustEntryType.FEE_REIMBURSEMENT ||
        values.client_trust_entry_type === ClientTrustEntryType.OTHER_DEPOSIT;

      // For non-deposit types, amount should be negative (debit)
      let amount = values.amount;
      if (!isDeposit && !amount.startsWith("-")) {
        amount = "-" + amount;
      }

      const formData = {
        check_number: values.check_number,
        amount: amount,
        memo: values.memo,
        issuer_payee: values.issuer_payee || undefined,
        deposit_date: values.deposit_date || undefined,
        client_trust_entry_type: values.client_trust_entry_type,
        medical_provider_id: values.medical_provider_id || null,
      };

      if (selectedTransaction) {
        // Update existing transaction
        await updateClientTrust.mutateAsync({
          caseId,
          id: selectedTransaction.id as number,
          trustData: formData
        });
      } else {
        // Create new transaction
        await createClientTrust.mutateAsync({
          caseId,
          trustData: formData
        }); 
      }

      form.reset();
      setActiveForm(null);
      setSelectedTransaction(null);
    } catch (error) {
      console.error('Error submitting form:', error);
      if (error instanceof Error) {
        setMutationErrors({ form: [error.message] });
      }
    }
  };

  const handleOpenForm = (formType: ClientTrustEntryType) => {
    form.reset({
      check_number: "",
      amount: "",
      memo: DEFAULT_NOTES[formType],
      deposit_date: "",
      client_trust_entry_type: formType,
      issuer_payee: DEFAULT_ISSUER_PAYEE[formType],
    });
    setActiveForm(formType);
  };

  const handleEdit = (transaction: ExtendedClientTrust) => {
    setSelectedTransaction(transaction);

    // Remove negative sign from amount for display in form
    let amount = transaction.amount;
    if (amount.startsWith("-")) {
      amount = amount.substring(1);
    }

    form.reset({
      check_number: transaction.check_number || "",
      amount: amount,
      memo: transaction.memo || "",
      deposit_date: transaction.deposit_date || "",
      client_trust_entry_type: transaction.client_trust_entry_type || ClientTrustEntryType.SETTLEMENT_PAYMENT,
      issuer_payee: transaction.issuer_payee || "",
    });

    // Set the appropriate form type based on the transaction
    if (transaction.client_trust_entry_type) {
      setActiveForm(transaction.client_trust_entry_type);
    } else {
      // Fallback for legacy data
      if (transaction.issuer_payee === "Client") {
        setActiveForm(ClientTrustEntryType.CLIENT_PAYMENT);
      } else if (transaction.is_deposit === true) {
        setActiveForm(ClientTrustEntryType.SETTLEMENT_PAYMENT);
      } else {
        setActiveForm(ClientTrustEntryType.FEE_REIMBURSEMENT);
      }
    }
  };

  const handleDelete = async (transaction: ExtendedClientTrust) => {
    setTransactionToDelete(transaction);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (transactionToDelete) {
      try {
        await deleteClientTrust.mutateAsync({
          caseId,
          id: transactionToDelete.id as number
        });
      } catch (error) {
        console.error("Error deleting transaction:", error);
      }
    }
    setDeleteDialogOpen(false);
    setTransactionToDelete(null);
  };

  const handleVoidToggle = async (transaction: ExtendedClientTrust) => {
    try {
      await updateClientTrust.mutateAsync({
        caseId,
        id: transaction.id as number,
        trustData: {
          ...transaction,
          void: !transaction.void
        }
      });
    } catch (error) {
      console.error("Error toggling void status:", error);
    }
  };

  // Get the entry type display name
  const getEntryTypeDisplayName = (transaction: ExtendedClientTrust) => {
    if (transaction.client_trust_entry_type) {
      return FORM_TITLES[transaction.client_trust_entry_type];
    }

    // Fallback for legacy data
    if (transaction.is_deposit === true) {
      return "Deposit";
    } else if (transaction.issuer_payee === "Client") {
      return "Client Check";
    } else {
      return "Fee Reimbursement";
    }
  };

  // Combine all transactions
  const combinedTransactions: ExtendedClientTrust[] = [...(trustTransactions || [])];

  // litigationCosts?.results?.forEach((cost) => {
  //   if (cost.status === CostStatus.PAID || cost.status === CostStatus.APPROVED) {
  //     const transaction: ExtendedClientTrust = {
  //       id: cost.id,
  //       amount: `${cost.amount}`,
  //       deposit_date: cost.created_at,
  //       memo: `Litigation Cost: ${cost.memo || 'No description'}`,
  //       issuer_payee: cost.cost_contact?.company_name || 'Litigation Cost',
  //       client_trust_entry_type: ClientTrustEntryType.OTHER_CHECK,
  //       check_number: cost.check_number || '',
  //       is_deposit: false,
  //       void: cost.void
  //     };
  //     combinedTransactions.push(transaction);
  //   }
  // });
  const sortedTransactions = combinedTransactions.sort((a, b) => {
    const dateA = new Date(a.deposit_date || '').getTime();
    const dateB = new Date(b.deposit_date || '').getTime();
    return dateA - dateB;
  });

  // Calculate running balance
  let runningBalance = 0;
  sortedTransactions.forEach((transaction) => {
    if (!transaction.void && transaction.client_trust_entry_type !== ClientTrustEntryType.REFERRED_FEE) {
      runningBalance += Number(transaction.amount);
      transaction.balance = runningBalance.toString();
    } else {
      transaction.balance = runningBalance.toString();
    }
  });

  // Sort back to descending order (newest first) for display
  const displayedTransactions = sortedTransactions.reverse();

  return (
    <div className="w-full max-w-7xl mx-auto space-y-4">
      {/* Compact Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 bg-gradient-to-r from-slate-50 to-gray-50 p-4 rounded-lg shadow-sm">
        <div className="space-y-2 w-full flex justify-between items-center">
          <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-slate-900 to-slate-700">
            Client Trust
          </h1>
          <div className="flex flex-wrap gap-2 justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="bg-white hover:bg-slate-50 border-slate-200 shadow-sm flex gap-1 h-8 text-xs">
                  {FORM_TITLES[selectedEntryType]}
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[280px]">
                <div className="p-2">
                  <div className="mb-3">
                    <h3 className="font-semibold text-sm text-green-700 mb-2 px-2">Credits/Inflows (+)</h3>
                    {/* <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.SETTLEMENT_PAYMENT)}
                      className="cursor-pointer"
                    >
                      <span>SETTLEMENT PAYMENT <span className="text-green-600">(+)</span></span>
                    </DropdownMenuItem> */}
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.FEE_REIMBURSEMENT)}
                      className="cursor-pointer"
                    >
                      <span>FEE REIMBURSEMENT <span className="text-green-600">(+)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.OTHER_DEPOSIT)}
                      className="cursor-pointer"
                    >
                      <span>OTHER DEPOSIT <span className="text-green-600">(+)</span></span>
                    </DropdownMenuItem>
                  </div>

                  <div>
                    <h3 className="font-semibold text-sm text-red-700 mb-2 px-2">Debits/Outflows (-)</h3>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.CLIENT_PAYMENT)}
                      className="cursor-pointer"
                    >
                      <span>CLIENT PAYMENT <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.TRUST_REIMBURSEMENT)}
                      className="cursor-pointer"
                    >
                      <span>TRUST REIMBURSEMENT <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.ATTORNEY_PAYMENT)}
                      className="cursor-pointer"
                    >
                      <span>ATTORNEY PAYMENT <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.OTHER_CHECK)}
                      className="cursor-pointer"
                    >
                      <span>OTHER CHECK <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.PROVIDER_PAYMENT)}
                      className="cursor-pointer"
                    >
                      <span>PROVIDER PAYMENT <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST)}
                      className="cursor-pointer"
                    >
                      <span>BRUMLEY LAW FIRM COST <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSelectedEntryType(ClientTrustEntryType.REFERRED_FEE)}
                      className="cursor-pointer"
                    >
                      <span>REFERRED FEE <span className="text-red-600">(-)</span></span>
                    </DropdownMenuItem>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex gap-1 h-8 text-xs"
              onClick={() => handleOpenForm(selectedEntryType)}
            >
              <Plus className="h-3 w-3" />
              Create
            </Button>
          </div>
        </div>
      </div>

      {/* Shared Form Dialog */}
      <Dialog open={activeForm !== null} onOpenChange={(open) => !open && setActiveForm(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-base">{activeForm ? FORM_TITLES[activeForm] : ""}</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="check_number"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm">
                      Check Number
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter check number" className="h-8 text-sm" />
                    </FormControl>
                    <FormMessage className="text-red-500 text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="issuer_payee"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm flex gap-1">
                      Issuer/Payee
                    </FormLabel>
                    <FormControl>
                      <Input 
                        {...field} 
                        value={field.value || ''} 
                        placeholder="Enter issuer or payee name" 
                        className="h-8 text-sm" 
                      />
                    </FormControl>
                    <FormMessage className="text-red-500 text-xs" />
                  </FormItem>
                )}
              />

              {activeForm === ClientTrustEntryType.PROVIDER_PAYMENT && (
                <FormField
                  control={form.control}
                  name="medical_provider_id"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm flex gap-1">
                        Medical Provider
                      </FormLabel>
                      <Select
                        value={field.value?.toString() || ""}
                        onValueChange={(value) => field.onChange(value ? Number(value) : null)}
                      >
                        <FormControl>
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select a provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent searchable>
                          {providersData?.results?.map((provider) => (
                            <SelectItem key={provider.id} value={provider.id.toString()}>
                              {provider.company}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-red-500 text-xs" />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm flex gap-1">
                      Amount
                      <span className="text-gray-600">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter amount (e.g. 100.00)" className="h-8 text-sm" />
                    </FormControl>
                    <FormMessage className="text-red-500 text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="memo"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm">Memo</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter memo"
                        className="text-sm min-h-[60px] resize-none"
                      />
                    </FormControl>
                    <FormMessage className="text-red-500 text-xs" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="deposit_date"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <RequiredLabel className="text-sm flex gap-1">
                      Date
                    </RequiredLabel>
                    <FormControl>
                      <CustomDateInput
                        value={formatDateForInput(field.value)}
                        onChange={(value: string) => {
                          field.onChange(formatDateForApi(value));
                        }}
                        displayFormat={formatDateForDisplay}
                        error={!!form.formState.errors.deposit_date}
                        maxDate={new Date()}
                        onError={(message: string) => {
                          form.setError("deposit_date", {
                            type: "manual",
                            message,
                          });
                        }}
                        required
                      />
                    </FormControl>
                    <FormMessage className="text-red-500 text-xs" />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  className="h-8 text-xs"
                  onClick={() => {
                    setActiveForm(null);
                    setSelectedTransaction(null);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" className="h-8 text-xs">
                  {selectedTransaction ? "Update" : "Submit"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Trust Account Summary - Compact version */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <Card className="border border-emerald-500 shadow-sm overflow-hidden">
          <CardHeader className="pb-1 pt-2 px-3 bg-emerald-50">
            <CardTitle className="text-xs font-semibold text-emerald-700">DEPOSITS</CardTitle>
          </CardHeader>
          <CardContent className="p-3">
            <p className="text-lg font-bold text-emerald-600">
              <CurrencyDisplay amount={trustSummary ? trustSummary.total_deposits : 0} />
            </p>
          </CardContent>
        </Card>

        <Card className="border border-red-500 shadow-sm overflow-hidden">
          <CardHeader className="pb-1 pt-2 px-3 bg-red-50">
            <CardTitle className="text-xs font-semibold text-red-700">DEBITS</CardTitle>
          </CardHeader>
          <CardContent className="p-3">
            <p className="text-lg font-bold text-red-600">
              <CurrencyDisplay amount={trustSummary ? trustSummary.total_debits : 0} negative={true} />
            </p>
          </CardContent>
        </Card>

        <Card className="border border-blue-500 shadow-sm overflow-hidden">
          <CardHeader className="pb-1 pt-2 px-3 bg-blue-50">
            <CardTitle className="text-xs font-semibold text-blue-700">CURRENT BALANCE</CardTitle>
          </CardHeader>
          <CardContent className="p-3">
            <p className="text-lg font-bold text-blue-600">
              <CurrencyDisplay amount={runningBalance} className="text-blue-600" />
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Client Trust Balance Sheet */}
      <Card className="shadow-sm overflow-hidden">
        <CardHeader className="py-2 px-4 bg-gray-100">
          <CardTitle className="text-sm font-medium">CLIENT TRUST BALANCE SHEET</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="text-xs py-2 text-center">Void</TableHead>
                  <TableHead className="text-xs py-2 text-center">Date</TableHead>
                  <TableHead className="text-xs py-2 text-center">Check #</TableHead>
                  <TableHead className="text-xs py-2 text-center">Issuer / Payee</TableHead>
                  <TableHead className="text-xs py-2 text-center">Type</TableHead>
                  <TableHead className="text-xs py-2 text-center">Memo</TableHead>
                  <TableHead className="text-xs py-2 text-center">Amount</TableHead>
                  <TableHead className="text-xs py-2 text-center">Balance</TableHead>
                  {displayedTransactions.some(t => t.void) && (
                    <TableHead className="text-xs py-2 text-center">Status</TableHead>
                  )}
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isTransactionsLoading ? (
                  <TableRow>
                    <TableCell colSpan={displayedTransactions.some(t => t.void) ? 10 : 9} className="text-center py-3 text-sm">Loading transactions...</TableCell>
                  </TableRow>
                ) : combinedTransactions.length > 0 ? (
                  displayedTransactions
                    .sort((a: ExtendedClientTrust, b: ExtendedClientTrust): number => {
                      // First separate SETTLEMENT_PAYMENT from other types
                      // const aIsSettlement = a.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT;
                      // const bIsSettlement = b.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT;

                      // If one is settlement and other isn't, settlement comes first
                      // if (aIsSettlement && !bIsSettlement) return -1;
                      // if (!aIsSettlement && bIsSettlement) return 1;

                      // For entries of the same type (both settlement or both non-settlement)
                      // Sort by date (newest first)
                      const dateA = new Date(a.deposit_date || '').getTime();
                      const dateB = new Date(b.deposit_date || '').getTime();
                      return dateB - dateA;
                    })
                    .filter(transaction => {
                      // Show all SETTLEMENT_PAYMENT entries
                      if (transaction.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT) {
                        return true;
                      }
                      // For other types, only exclude OTHER_CHECK without additional checks
                      if (transaction.client_trust_entry_type === ClientTrustEntryType.OTHER_CHECK) {
                        return true;
                      }
                      return true;
                    })
                    .map((transaction) => {
                      return (
                        <TableRow
                          key={transaction.id}
                          className={cn(
                            "text-xs",
                            transaction.void && "text-red-500 bg-red-50"
                          )}
                        >
                          <TableCell className="py-1.5">
                            <input
                              type="checkbox"
                              checked={transaction.void}
                              onChange={() => handleVoidToggle(transaction)}
                              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            />
                          </TableCell>
                          <TableCell className="py-1.5">{formatDateForDisplay(transaction.deposit_date || '')}</TableCell>
                          <TableCell className="py-1.5">{transaction.check_number}</TableCell>
                          <TableCell className="py-1.5">{transaction.issuer_payee}</TableCell>
                          <TableCell className="py-1.5">{getEntryTypeDisplayName(transaction)}</TableCell>
                          <TableCell className="py-1.5 max-w-[150px] truncate">{transaction.memo || "—"}</TableCell>
                          <TableCell className={cn(
                            "py-1.5 text-right font-medium",
                            transaction.void && "text-red-500"
                          )}>
                            <CurrencyDisplay amount={transaction.amount}/>
                          </TableCell>
                          <TableCell className="py-1.5 text-right font-medium">
                            <CurrencyDisplay amount={transaction.balance || "0"} />
                          </TableCell>
                          {displayedTransactions.some(t => t.void) && (
                            <TableCell className="py-1.5 text-center font-medium">
                              {transaction.void ? "VOID" : ""}
                            </TableCell>
                          )}
                          <TableCell className="py-1.5">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="link"
                                className="text-green-600 p-0"
                                onClick={() => handleEdit(transaction)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="link"
                                className="text-red-600 p-0"
                                onClick={() => handleDelete(transaction)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                ) : (
                  <TableRow>
                    <TableCell colSpan={displayedTransactions.some(t => t.void) ? 10 : 9} className="text-center py-3 text-sm">No transactions found</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          {trustSummary && Number(trustSummary.current_balance) > 0 && (
            <div className="flex justify-end p-2">
              <div className="bg-gray-50 px-4 py-2 rounded text-sm">
                <p className="font-medium">
                  REMAINING IN TRUST: <span className="text-blue-600 font-bold">
                    <CurrencyDisplay 
                      amount={
                        displayedTransactions
                          .filter(transaction => 
                            !transaction.void && 
                            transaction.client_trust_entry_type !== ClientTrustEntryType.REFERRED_FEE
                          )
                          .reduce((sum, transaction) => sum + Number(transaction.amount), 0)
                      }
                    />
                  </span>
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fees Earned Sheet */}
      <Card className="shadow-sm overflow-hidden">
        <CardHeader className="py-2 px-4 bg-gray-100">
          <CardTitle className="text-sm font-medium">FEES EARNED SHEET</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="text-xs py-2">Void</TableHead>
                  <TableHead className="text-xs py-2">Date</TableHead>
                  <TableHead className="text-xs py-2">Check #</TableHead>
                  <TableHead className="text-xs py-2">Issuer / Payee</TableHead>
                  <TableHead className="text-xs py-2">Type</TableHead>
                  <TableHead className="text-xs py-2">Memo</TableHead>
                  <TableHead className="text-xs py-2 text-right">Amount</TableHead>
                  <TableHead className="text-xs py-2 text-right">Balance</TableHead>
                  {displayedTransactions.some(t => t.void) && (
                    <TableHead className="text-xs py-2 text-center">Status</TableHead>
                  )}
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isTransactionsLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-3 text-sm">Loading transactions...</TableCell>
                  </TableRow>
                ) : displayedTransactions.filter(transaction =>
                (transaction.client_trust_entry_type === ClientTrustEntryType.ATTORNEY_PAYMENT ||
                  transaction.client_trust_entry_type === ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST ||
                  transaction.client_trust_entry_type === ClientTrustEntryType.REFERRED_FEE)
                ).length > 0 ? (
                  displayedTransactions
                    .filter(transaction =>
                    (transaction.client_trust_entry_type === ClientTrustEntryType.ATTORNEY_PAYMENT ||
                      transaction.client_trust_entry_type === ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST ||
                      transaction.client_trust_entry_type === ClientTrustEntryType.REFERRED_FEE)
                    )
                    .map(transaction => (
                      <TableRow
                        key={transaction.id}
                        className={cn(
                          "text-xs",
                          transaction.void && "text-red-500 bg-red-50"
                        )}
                      >
                        <TableCell className="py-1.5">
                          <input
                            type="checkbox"
                            checked={transaction.void}
                            onChange={() => handleVoidToggle(transaction)}
                            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                        </TableCell>
                        <TableCell className="py-1.5">{formatDateForDisplay(transaction.deposit_date || '')}</TableCell>
                        <TableCell className="py-1.5">{transaction.check_number}</TableCell>
                        <TableCell className="py-1.5">{transaction.issuer_payee}</TableCell>
                        <TableCell className="py-1.5">{getEntryTypeDisplayName(transaction)}</TableCell>
                        <TableCell className="py-1.5 max-w-[150px] truncate">{transaction.memo || "—"}</TableCell>
                        <TableCell className="py-1.5 text-right font-medium text-green-600">
                          <CurrencyDisplay amount={Number(transaction.amount)*-1} className="text-green-600" />
                        </TableCell>
                        <TableCell className="py-1.5 text-right font-medium">
                          <CurrencyDisplay amount={transaction.balance || "0"} className="text-green-600" />
                        </TableCell>
                        {displayedTransactions.some(t => t.void) && (
                          <TableCell className="py-1.5 text-center font-medium">
                            {transaction.void ? "VOID" : ""}
                          </TableCell>
                        )}
                        <TableCell className="py-1.5">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="link"
                              className="text-green-600 p-0"
                              onClick={() => handleEdit(transaction)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="link"
                              className="text-red-600 p-0"
                              onClick={() => handleDelete(transaction)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-3 text-sm">No fee transactions found</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="flex justify-end p-2">
            <div className="bg-gray-50 px-4 py-2 rounded text-sm">
              <p className="font-medium">
                FEES <span className="text-red-600 font-bold">
                  <CurrencyDisplay
                    amount={
                      displayedTransactions
                        .filter(transaction =>
                          (transaction.client_trust_entry_type === ClientTrustEntryType.ATTORNEY_PAYMENT ||
                            transaction.client_trust_entry_type === ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST) &&
                          !transaction.void
                        )
                        .reduce((sum, transaction) => sum + Number(transaction.amount), 0)*-1
                    }
                  />
                </span>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brumley Law Firm Cost Sheet */}
      <Card className="shadow-sm overflow-hidden">
        <CardHeader className="py-2 px-4 bg-gray-100">
          <CardTitle className="text-sm font-medium">BRUMLEY LAW FIRM COST SHEET</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="text-xs py-2">Date</TableHead>
                  <TableHead className="text-xs py-2">Check #</TableHead>
                  <TableHead className="text-xs py-2">Issuer / Payee</TableHead>
                  <TableHead className="text-xs py-2">Type</TableHead>
                  <TableHead className="text-xs py-2">Memo</TableHead>
                  <TableHead className="text-xs py-2 text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isTransactionsLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-3 text-sm">Loading transactions...</TableCell>
                  </TableRow>
                ) : ((caseCosts?.results?.length || 0) > 0 || (litigationCosts?.results?.length || 0) > 0) ? (
                  [
                    ...(caseCosts?.results || [])
                      .filter(cost => cost.status === CostStatus.PAID) // || cost.status === CostStatus.APPROVED
                      .map(cost => ({
                        id: cost.id,
                        amount: `${cost.amount}`,
                        deposit_date: cost.created_at,
                        memo: cost.memo ? `Case Cost: ${cost.memo}` : 'No description',
                        issuer_payee: cost.cost_contact?.company_name || 'Case Cost',
                        client_trust_entry_type: ClientTrustEntryType.OTHER_CHECK,
                        check_number: cost.check_number || '',
                        balance: '0',
                        is_deposit: false,
                        costType: 'Case Cost'
                      })),
                    ...(litigationCosts?.results || [])
                      .filter(cost => cost.status === CostStatus.PAID) // || cost.status === CostStatus.APPROVED)
                      .map(cost => ({
                        id: cost.id,
                        amount: `${cost.amount}`,
                        deposit_date: cost.created_at,
                        memo: cost.memo ? `Litigation Cost: ${cost.memo}` : 'No description',
                        issuer_payee: cost.cost_contact?.company_name || 'Litigation Cost',
                        client_trust_entry_type: ClientTrustEntryType.OTHER_CHECK,
                        check_number: cost.check_number || '',
                        balance: '0',
                        is_deposit: false,
                        costType: 'Litigation Cost'
                      }))
                  ]
                    .sort((a, b) => {
                      const dateA = new Date(a.deposit_date || '').getTime();
                      const dateB = new Date(b.deposit_date || '').getTime();
                      return dateB - dateA;
                    })
                    .map(transaction => (
                      <TableRow key={transaction.id} className="text-xs">
                        <TableCell className="py-1.5">{formatDateForDisplay(transaction.deposit_date || '')}</TableCell>
                        <TableCell className="py-1.5">{transaction.check_number}</TableCell>
                        <TableCell className="py-1.5">{transaction.issuer_payee}</TableCell>
                        <TableCell className="py-1.5">{transaction.costType}</TableCell>
                        <TableCell className="py-1.5 max-w-[150px] truncate">{transaction.memo || "—"}</TableCell>
                        <TableCell className="py-1.5 text-right font-medium text-red-600">
                          <CurrencyDisplay amount={transaction.amount} negative={false} />
                        </TableCell>
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-3 text-sm">No costs found</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex justify-end p-2">
            <div className="bg-gray-50 px-4 py-2 rounded text-sm">
              <p className="font-medium">
                TOTAL COSTS <span className="text-red-600 font-bold">
                  <CurrencyDisplay
                    amount={
                      (((caseCosts?.results || [])
                        .filter(cost =>
                          cost.status === CostStatus.PAID && !cost.void
                        )
                        .reduce((sum, cost) => sum + Number(cost.amount), 0)) +
                      ((litigationCosts?.results || [])
                        .filter(cost =>
                          cost.status === CostStatus.PAID && !cost.void
                        )
                        .reduce((sum, cost) => sum + Number(cost.amount), 0))) * 1
                    }
                  />
                </span>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        text="Transaction"
      />
    </div>
  );
}