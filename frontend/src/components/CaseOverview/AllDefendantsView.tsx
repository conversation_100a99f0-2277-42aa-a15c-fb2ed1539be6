import React, { FC, useState, useEffect, useMemo, Fragment } from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useDefendantsListQuery } from '@/services/case-management/defendantService';
import { useNegotiationsQuery, useDeleteNegotiationMutation, useUpdateNegotiationMutation } from '@/services/negotiationService';
import { useSettlementCalculationQuery, useUpdateSettlementCalculationMutation } from '@/services/case-management/settlementService';
import {
  useCreateClientTrustMutation,
  useClientTrustQuery,
  useUpdateClientTrustMutation,
  CLIENT_TRUST_KEYS
} from "@/services/case-management/clientTrustService";
import { ClientTrustEntryType, ClientTrust, DepositType } from "@/type/case-management/clientTrustTypes";
import { toast } from '@/hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronDown, DollarSign, AlertCircle } from 'lucide-react';
import { NegotiationRead, NegotiationType } from '@/type/negotiationTypes';
import { Button } from '../ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '../ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DefendantListItem } from '@/type/case-management/defendantTypes';
import { CreateDemandDialog } from '@/components/CaseOverview/CreateDemandDialog';
import { DeleteConfirmationDialog } from '@/components/common/DeleteConfirmationDialog';
import { useQueryClient } from '@tanstack/react-query';
import { formatDateForApi } from '@/utils/dateUtils';
import { NegotiationTable, SettlementCheckDialog } from './components/negotiation/Table';
import { ParentOfferDemand } from './components/negotiation/parentOfferDemand';
import NegotiationCards from './components/negotiation/negotiationcards';
import FeePercentageSelect from './components/negotiation/FeePercentageSelect';

const formatDate = (dateString: string) => {
  const hasTime = dateString.includes('T') || dateString.includes(' ');
  const dateToFormat = hasTime ? dateString : `${dateString}T00:00:00`;
  return new Date(dateToFormat).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

interface OrganizedNegotiation {
  parent: NegotiationRead;
  children: NegotiationRead[];
}

interface DefendantGroup {
  defendantId: number;
  defendantName: string;
  defendantType: string;
  negotiations: OrganizedNegotiation[];
}

interface AllDefendantsViewProps {
  caseId: string;
  onSelectDefendant: (defendantId: number | null) => void;
}

const AllDefendantsView: FC<AllDefendantsViewProps> = ({ caseId, onSelectDefendant }) => {
  const [expandedDemands, setExpandedDemands] = useState<number[]>([]);
  const [selectedDefendantId, setSelectedDefendantId] = useState<string | null>(null);
  const [editDemand, setEditDemand] = useState<NegotiationRead | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentDeleteId, setCurrentDeleteId] = useState<number>(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentNegotiationId, setCurrentNegotiationId] = useState<number>(0);
  const updateNegotiationMutation = useUpdateNegotiationMutation(caseId, currentNegotiationId);
  const [showSettlementDialog, setShowSettlementDialog] = useState(false);
  const [selectedNegotiation, setSelectedNegotiation] = useState<NegotiationRead | null>(null);
  
  const { data: defendants, isLoading: isDefendantsLoading } = useDefendantsListQuery(caseId);
  const { data: calculations } = useSettlementCalculationQuery(caseId);
  const { data: negotiations, refetch } = useNegotiationsQuery(caseId, null);
  const { data: clientTrustData } = useClientTrustQuery(caseId);
  
  const updateSettlementCalculation = useUpdateSettlementCalculationMutation(caseId);
  const deleteNegotiationMutation = useDeleteNegotiationMutation(caseId, currentDeleteId);
  const queryClient = useQueryClient();
  const createClientTrust = useCreateClientTrustMutation(queryClient);
  const updateClientTrust = useUpdateClientTrustMutation(queryClient);

  useEffect(() => {
    if (selectedDefendantId) {
      onSelectDefendant(parseInt(selectedDefendantId));
    }
  }, [selectedDefendantId, onSelectDefendant]);

  const hasAcceptedOfferForDefendant = (defendantId: number) => {
    return negotiations?.some(n =>
      n.type === NegotiationType.ACCEPTED_OFFER &&
      n.defendant?.id === defendantId
    ) ?? false;
  };

  const getAcceptedOfferIdForDefendant = (defendantId: number): number | null => {
    const acceptedOffer = negotiations?.find(n =>
      n.type === NegotiationType.ACCEPTED_OFFER &&
      n.defendant?.id === defendantId
    );
    return acceptedOffer?.id || null;
  };

  const handleFeePercentageChange = (value: string) => {
    if (value && !isNaN(parseFloat(value))) {
      const formattedValue = parseFloat(value).toFixed(2);
      updateSettlementCalculation.mutate({
        fees_percentage: formattedValue,
      });
    }
  };

  const handleDelete = async (id: number) => {
    setCurrentDeleteId(id);
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    try {
      await deleteNegotiationMutation.mutateAsync();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting negotiation:', error);
    }
  };

  const handleAcceptOffer = async (negotiationId: number) => {
    setCurrentNegotiationId(negotiationId);

    try {
      if (negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          type: NegotiationType.ACCEPTED_OFFER,
          defendant: selectedDefendantId ? parseInt(selectedDefendantId) : undefined
        });
      }
    } catch (error) {
      console.error('Error accepting offer:', error);
      toast({
        title: 'Error',
        description: 'Failed to accept offer',
        variant: 'destructive',
      });
    }
  };

  const handleDeacceptOffer = async (negotiationId: number) => {
    setCurrentNegotiationId(negotiationId);

    try {
      if (negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          type: NegotiationType.INITIAL_DEMAND,
          defendant: selectedDefendantId ? parseInt(selectedDefendantId) : undefined
        });
        toast({
          title: 'Success',
          description: 'Offer unaccepted successfully',
        });
      }
    } catch (error) {
      console.error('Error deaccepting offer:', error);
      toast({
        title: 'Error',
        description: 'Failed to unaccept offer',
        variant: 'destructive',
      });
    }
  };

  const handleSettlementCheck = async (negotiation: NegotiationRead) => {
    if (!negotiation.amount || !negotiation.insurance_company?.name) {
      toast({
        title: 'Error',
        description: 'Negotiation amount or insurance company name is missing',
        variant: 'destructive',
      });
      return;
    }

    setSelectedNegotiation(negotiation);
    setShowSettlementDialog(true);
  };

  const handleSettlementSubmit = async (checkNumber: string) => {
    if (!selectedNegotiation) return;

    try {
      await createClientTrust.mutateAsync({
        caseId,
        trustData: {
          check_number: checkNumber,
          amount: selectedNegotiation.amount.toString(),
          memo: `Settlement payment from ${selectedNegotiation.insurance_company?.name}`,
          issuer_payee: selectedNegotiation.insurance_company?.name || '',
          deposit_date: formatDateForApi(new Date()),
          client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
          deposit_type: DepositType.THIRD_PARTY,
          negotiation_id: selectedNegotiation.id
        }
      });
      toast({
        title: 'Success',
        description: 'Settlement check created successfully',
      });
      setShowSettlementDialog(false);
      setSelectedNegotiation(null);
    } catch (error) {
      console.error('Error creating settlement check:', error);
      toast({
        title: 'Error',
        description: 'Failed to create settlement check',
        variant: 'destructive',
      });
    }
  };

  const checkSettlementStatus = (negotiation: NegotiationRead): ClientTrust | null => {
    console.log("clientTrustData ---> all defendants view", clientTrustData, negotiation);
    if (!clientTrustData || !negotiation.insurance_company?.name) return null;

    const settlementPayments = clientTrustData.filter(trust =>
      trust.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT &&
      !trust.void
    );

    const matchingTrust = settlementPayments.find(trust => {
      const amountMatches = trust.amount === negotiation.amount?.toString();
      const companyMatches = trust.issuer_payee === negotiation.insurance_company?.name;
      const memoMatches = trust.memo?.includes(negotiation.insurance_company?.name || '');

      return amountMatches && companyMatches && memoMatches;
    });

    if (!matchingTrust || !matchingTrust.id) return null;

    // Convert ClientTrustRow to ClientTrust
    const amount = matchingTrust.amount !== undefined && matchingTrust.amount !== null 
      ? String(matchingTrust.amount) 
      : "";

    return {
      id: Number(matchingTrust.id),
      check_number: matchingTrust.check_number || "",
      issuer_payee: matchingTrust.issuer_payee,
      memo: matchingTrust.memo || "",
      amount,
      deposit_date: matchingTrust.deposit_date || "",
      client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
      void: false,
      created_at: matchingTrust.created_at || "",
      updated_at: matchingTrust.updated_at || ""
    };
  };

  const handleVoidSettlement = async (negotiation: NegotiationRead) => {
    console.log("negotiation", negotiation);
    const matchingTrust = checkSettlementStatus(negotiation);
    if (!matchingTrust?.id) return;

    try {
      await updateClientTrust.mutateAsync({
        caseId,
        id: matchingTrust.id,
        trustData: {
          ...matchingTrust,
          void: true,
          negotiation_id: negotiation.id
        },
      });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(caseId) });
      toast({
        title: 'Success',
        description: 'Settlement check voided successfully',
      });
    } catch (error) {
      console.error('Error voiding settlement:', error);
      toast({
        title: 'Error',
        description: 'Failed to void settlement check',
        variant: 'destructive',
      });
    }
  };

  const handleArchiveToggle = async (negotiationId: number, isArchived: boolean) => {
    console.log("negotiationId -----------> ", negotiationId, isArchived);
    try {
      setCurrentNegotiationId(negotiationId);
      console.log("updateNegotiationMutation", updateNegotiationMutation);
      if (updateNegotiationMutation && negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          is_archived: isArchived
        });

        await refetch();

        toast({
          title: 'Success',
          description: isArchived ? 'Negotiation archived successfully' : 'Negotiation unarchived successfully',
        });
      }
    } catch (error) {
      console.error('Error toggling archive status:', error);
      toast({
        title: 'Error',
        description: `Failed to ${isArchived ? 'archive' : 'unarchive'} negotiation. Please try again.`,
        variant: 'destructive',
      });

      await refetch();
    }
  };

  const handleEditComplete = () => {
    setEditDemand(null);
    setShowEditDialog(false);
    setTimeout(() => {
      refetch();
    }, 100);
  };

  const organizeNegotiationsByDefendant = (
    negotiations: NegotiationRead[] | undefined,
    defendants: DefendantListItem[] | undefined
  ): DefendantGroup[] => {
    if (!negotiations || !defendants) return [];

    const defendantGroups: DefendantGroup[] = defendants.map(defendant => ({
      defendantId: defendant.id,
      defendantName: `${defendant.first_name} ${defendant.last_name}`,
      defendantType: defendant.defendant_type || 'Unknown Type',
      negotiations: []
    }));

    const parentNegotiations = negotiations.filter(item => item.previous_offer === null);

    parentNegotiations.forEach(parent => {
      const defendantGroup = defendantGroups.find(group =>
        group.defendantId === parent.defendant?.id
      );

      if (defendantGroup) {
        const children = negotiations
          .filter(item => item.previous_offer === parent.id)
          .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

        defendantGroup.negotiations.push({
          parent,
          children
        });
      }
    });

    defendantGroups.forEach(group => {
      group.negotiations.sort((a, b) =>
        new Date(b.parent.created_at).getTime() - new Date(a.parent.created_at).getTime()
      );
    });

    return defendantGroups.filter(group => group.negotiations.length > 0);
  };

  const organizedDefendantGroups = useMemo(() => {
    return organizeNegotiationsByDefendant(negotiations, defendants);
  }, [negotiations, defendants]);

  useEffect(() => {
    if (negotiations) {
      // Get all parent negotiation IDs
      const parentNegotiationIds = negotiations
        .filter(item => item.previous_offer === null)
        .map(item => item.id);
      
      // Set all parent negotiations to be expanded by default
      setExpandedDemands(parentNegotiationIds);
    }
  }, [negotiations]);

  const toggleDemand = (negotiationId: number) => {
    setExpandedDemands(prev => {
      const newState = prev.includes(negotiationId)
        ? prev.filter(d => d !== negotiationId)
        : [...prev, negotiationId];
      return newState;
    });
  };

  const quickStats = useMemo(() => {
    if (!negotiations?.length) return {
      latestOffer: null,
      demandAmount: null,
      nextDeadline: null,
      acceptedOffer: null,
      totalPolicyLimit: null
    };

    const sortedNegotiations = [...negotiations]
      .filter(n => n.previous_offer !== null)
      .sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

    const latestOffer = sortedNegotiations[0]?.amount || null;

    const sortedDemands = [...negotiations]
      .filter(n => n.previous_offer === null)
      .sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

    const latestDemandPerDefendant = new Map();
    sortedDemands.forEach(demand => {
      if (demand.defendant?.id && demand.amount) {
        if (!latestDemandPerDefendant.has(demand.defendant.id) ||
          new Date(demand.created_at) > new Date(latestDemandPerDefendant.get(demand.defendant.id).created_at)) {
          latestDemandPerDefendant.set(demand.defendant.id, demand);
        }
      }
    });

    const totalDemandAmount = Array.from(latestDemandPerDefendant.values())
      .reduce((sum, demand) => Number(sum) + (Number(demand.amount) || 0), 0);

    const allSortedNegotiations = [...negotiations].sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
    const nextDeadline = allSortedNegotiations.find(n => n.response_deadline)?.response_deadline;

    const acceptedOffer = selectedDefendantId
      ? negotiations.find(n =>
        n.type === NegotiationType.ACCEPTED_OFFER &&
        n.defendant?.id === parseInt(selectedDefendantId)
      )?.amount
      : null;

    return {
      latestOffer,
      demandAmount: totalDemandAmount,
      nextDeadline,
      acceptedOffer,
      totalPolicyLimit: null
    };
  }, [negotiations, selectedDefendantId]);

  const totalPolicyLimit = useMemo(() => {
    const policyLimits = defendants?.map(defendant => {
      console.log("defendant", defendant);
      return 0;
    }) || [];
    return policyLimits.reduce((sum, limit) => sum + limit, 0);
  }, [defendants]);

  const renderDefendantSection = (defendant: DefendantListItem) => {
    const defendantGroup = organizedDefendantGroups.find(group => group.defendantId === defendant.id);
    const hasAcceptedOfferForThisDefendant = hasAcceptedOfferForDefendant(defendant.id);

    return (
      <div key={defendant.id} className="bg-slate-50 border-b">
        <div className="p-3">
          <div className="flex justify-between items-center">
            <span className="font-semibold text-slate-700">
              {`${defendant.first_name} ${defendant.last_name}`}
            </span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span>
                    <CreateDemandDialog
                      caseId={caseId}
                      defendantId={defendant.id}
                      adjusterId={null}
                      disabled={hasAcceptedOfferForThisDefendant}
                      negotiations={negotiations?.filter(n => n.defendant?.id === defendant.id)}
                    />
                  </span>
                </TooltipTrigger>
                {hasAcceptedOfferForThisDefendant && (
                  <TooltipContent>
                    <p>Cannot create new demand while there is an accepted offer for this defendant</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="bg-white">
          {defendantGroup && defendantGroup.negotiations.length > 0 ? (
            defendantGroup.negotiations.map(({ parent, children }) => {
              const isAcceptedOffer = parent.type === NegotiationType.ACCEPTED_OFFER;
              const hasAcceptedChild = children.some(child => child.type === NegotiationType.ACCEPTED_OFFER);
              console.log((hasAcceptedOfferForThisDefendant || parent.is_archived || (hasAcceptedChild && !isAcceptedOffer)), `☄️☄️ rowDisabled ----------------------->☄️☄️`, parent.id, parent.amount, "!isAcceptedOffer", !isAcceptedOffer, "hasAcceptedOfferForThisDefendant", hasAcceptedOfferForThisDefendant, "parent.is_archived", parent.is_archived, "hasAcceptedChild && !isAcceptedOffer", hasAcceptedChild && !isAcceptedOffer);
              const parentInsuranceCompany = parent.insurance_company ||
                (parent.adjuster?.insurance_company_name ? {
                  id: parent.adjuster.insurance_company,
                  name: parent.adjuster.insurance_company_name
                } : undefined);

              const headerParts = [];

              if (parent.adjuster?.insurance_company_name) {
                headerParts.push(parent.adjuster.insurance_company_name);
              } else if (parent.insurance_company?.name) {
                headerParts.push(parent.insurance_company.name);
              }

              if (parent.adjuster) {
                headerParts.push(`${parent.adjuster.first_name} ${parent.adjuster.last_name}`);
              }

              if (parent.amount) {
                headerParts.push(`$${Number(parent.amount).toLocaleString()}`);
              }

              if (parent.date_sent) {
                headerParts.push(`${parent.type === NegotiationType.INITIAL_DEMAND ? "Demand" : "Offer"} ${formatDate(parent.date_sent)}`);
              }

              if (parent.response_deadline) {
                headerParts.push(`exp ${formatDate(parent.response_deadline)}`);
              }
              
              if (calculations?.fees_percentage) {
                const feePercentage = calculations.fees_percentage === '0.00' ? '33.33' : Number(calculations.fees_percentage).toFixed(2);
                headerParts.push(`Attorney Fee ${feePercentage}%`);
              }

              const headerString = headerParts.join(' | ');

              return (
                <Fragment key={parent.id}>
                  <ParentOfferDemand<NegotiationRead>
                    parent={parent}
                    isExpanded={expandedDemands.includes(parent.id)}
                    toggleExpand={() => toggleDemand(parent.id)}
                    headerString={headerString}
                    caseId={caseId}
                    selectedDefendant={defendant.id}
                    hasAcceptedOffer={hasAcceptedOfferForThisDefendant}
                    hasAcceptedChild={hasAcceptedChild}
                    isAcceptedOffer={isAcceptedOffer}
                    onDelete={handleDelete}
                    onAcceptOffer={handleAcceptOffer}
                    onDeacceptOffer={handleDeacceptOffer}
                    onSettlementCheck={handleSettlementCheck}
                    onEditDemand={(negotiation) => {
                      setEditDemand(negotiation);
                      setShowEditDialog(true);
                    }}
                    onArchiveToggle={handleArchiveToggle}
                    checkSettlementStatus={checkSettlementStatus}
                    handleVoidSettlement={handleVoidSettlement}
                    insuranceCompany={parentInsuranceCompany}
                  />

                  {expandedDemands.includes(parent.id) && (
                    <div>
                      <NegotiationTable<NegotiationRead>
                        negotiations={children}
                        parentId={parent.id}
                        caseId={caseId}
                        selectedDefendant={defendant.id}
                        onAcceptOffer={handleAcceptOffer}
                        onDeacceptOffer={handleDeacceptOffer}
                        hasAcceptedOffer={hasAcceptedOfferForThisDefendant}
                        onDelete={handleDelete}
                        parentIsArchived={parent.is_archived}
                        negotiationType="third_party"
                        parentInsuranceCompany={parentInsuranceCompany}
                        acceptedOfferId={getAcceptedOfferIdForDefendant(defendant.id)}
                        checkSettlementStatus={checkSettlementStatus}
                        handleVoidSettlement={handleVoidSettlement}
                      />
                    </div>
                  )}
                </Fragment>
              );
            })
          ) : (
            <div className="text-center py-8 bg-slate-50">
              <p className="text-gray-500 text-sm">No demands or offers found for this defendant.</p>
              <p className="text-xs text-gray-400 mt-1">Click the &quot;Create Demand&quot; button above to get started.</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-3 sm:p-4 space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 bg-gradient-to-r from-slate-50 to-gray-50 p-3 sm:p-4 rounded-lg shadow-sm">
        <div className="flex md:flex-row justify-between items-start md:items-center gap-3">
          <h1 className="text-xl sm:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-slate-900 to-slate-700">
            Case Negotiations
          </h1>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="h-9 min-w-[180px] justify-between bg-white hover:bg-slate-50 border-slate-200 text-sm"
              >
                {isDefendantsLoading ? (
                  <LoadingSpinner />
                ) : (
                  <>
                    {selectedDefendantId ?
                      defendants?.find(d => d.id === parseInt(selectedDefendantId))?.first_name + ' ' +
                      defendants?.find(d => d.id === parseInt(selectedDefendantId))?.last_name :
                      "All Defendants"}
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px]">
              <DropdownMenuItem
                onClick={() => {
                  setSelectedDefendantId(null);
                  onSelectDefendant(null);
                }}
                className="cursor-pointer hover:bg-slate-50 text-sm"
              >
                All Defendants
              </DropdownMenuItem>
              {defendants?.map((defendant) => (
                <DropdownMenuItem
                  key={defendant.id}
                  onClick={() => {
                    setSelectedDefendantId(defendant.id.toString());
                    onSelectDefendant(defendant.id);
                  }}
                  className="cursor-pointer hover:bg-slate-50 text-sm"
                >
                  {`${defendant.first_name} ${defendant.last_name}`}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span>
                  <FeePercentageSelect
                    value={
                      calculations?.fees_percentage &&
                        parseFloat(calculations.fees_percentage) > 0
                        ? calculations.fees_percentage
                        : "33.33"
                    }
                    onChange={handleFeePercentageChange}
                    disabled={hasAcceptedOfferForDefendant(parseInt(selectedDefendantId || '0'))}
                  />
                </span>
              </TooltipTrigger>
              {hasAcceptedOfferForDefendant(parseInt(selectedDefendantId || '0')) && (
                <TooltipContent>
                  <p>Unaccept the offer to modify attorney fee</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <NegotiationCards 
        data={[
          {
            icon: DollarSign,
            title: "Total Current Demands",
            metrics: {
              primary: {
                type: 'monetary',
                value: quickStats.demandAmount || 0
              }
            },
            bgColor: "bg-blue-50"
          },
          {
            icon: DollarSign,
            title: hasAcceptedOfferForDefendant(parseInt(selectedDefendantId || '0')) ? "Total Accepted Offer" : "Total Offer",
            metrics: {
              primary: {
                type: 'monetary',
                value: hasAcceptedOfferForDefendant(parseInt(selectedDefendantId || '0')) ? (quickStats.acceptedOffer || 0) : (quickStats.latestOffer || 0)
              }
            },
            bgColor: hasAcceptedOfferForDefendant(parseInt(selectedDefendantId || '0')) ? "bg-orange-50" : "bg-emerald-50"
          },
          {
            icon: AlertCircle,
            title: "Total Policy Limits",
            metrics: {
              primary: {
                type: 'monetary',
                value: totalPolicyLimit
              }
            },
            bgColor: "bg-amber-50"
          }
        ]}
        mobileColumns={2}
        mediumColumns={3}
      />

      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-slate-800">Defendants</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {isDefendantsLoading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="divide-y divide-slate-200">
              {defendants?.map(defendant => renderDefendantSection(defendant))}
              {(!defendants || defendants.length === 0) && (
                <div className="text-center py-8 bg-slate-50">
                  <p className="text-gray-500 text-sm">No defendants found.</p>
                  <p className="text-xs text-gray-400 mt-1">Add defendants to create demands.</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {editDemand && showEditDialog && (
        <CreateDemandDialog
          key={`edit-${editDemand.id}-${showEditDialog}`}
          caseId={caseId}
          defendantId={editDemand.defendant?.id || null}
          adjusterId={editDemand.adjuster?.id || null}
          disabled={editDemand.defendant?.id ? hasAcceptedOfferForDefendant(editDemand.defendant.id) : false}
          negotiations={negotiations}
          editDemand={editDemand}
          onEditComplete={() => {
            handleEditComplete();
            setShowEditDialog(false);
          }}
          onClose={() => {
            setShowEditDialog(false);
            setEditDemand(null);
          }}
        />
      )}

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleConfirmDelete}
        text="Negotiation"
      />

      <SettlementCheckDialog
        isOpen={showSettlementDialog}
        onClose={() => {
          setShowSettlementDialog(false);
          setSelectedNegotiation(null);
        }}
        onSubmit={handleSettlementSubmit}
        negotiation={selectedNegotiation}
      />
    </div>
  );
};

export default AllDefendantsView; 