import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { UploadingFile } from '../types/fileManager.types';
import { useFileUploadMutation, useFileUploadFromUrlMutation } from '@/services/case-management/newDocumentManagement';

export const useFileUpload = (folderId: string | null, onSuccess: () => void) => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const { toast } = useToast();
  const fileUpload = useFileUploadMutation();
  const fileUploadFromUrl = useFileUploadFromUrlMutation();

  const updateUploadProgress = useCallback((file: File, progress: number) => {
    setUploadingFiles(prev => 
      prev.map(f => f.file === file ? { ...f, progress } : f)
    );
  }, []);

  const handleUploadError = useCallback((file: File, error: string) => {
    setUploadingFiles(prev => 
      prev.map(f => f.file === file ? { ...f, error } : f)
    );
    toast({
      title: "Upload Failed",
      description: `Failed to upload ${file.name}: ${error}`,
      variant: "destructive",
    });
  }, [toast]);

  const handleUpload = useCallback(async (file: File) => {
    if (!folderId) {
      throw new Error('No folder ID available for upload');
    }

    try {
      await fileUpload.mutateAsync({
        folder_id: folderId,
        file: file
      });
      onSuccess();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      throw new Error(errorMessage);
    }
  }, [folderId, fileUpload, onSuccess]);

  return {
    uploadingFiles,
    setUploadingFiles,
    updateUploadProgress,
    handleUploadError,
    handleUpload,
    fileUploadFromUrl
  };
}; 