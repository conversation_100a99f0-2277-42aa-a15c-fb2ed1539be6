import React, { FC, useState, useEffect, useMemo, Fragment } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, DollarSign, Calendar } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatDateForDisplay, formatDateForApi } from '@/utils/dateUtils';
import { NegotiationUIMRead, NegotiationType } from '@/type/negotiationTypes';
import { useUIMNegotiationsQuery, useUpdateUIMNegotiationMutation, useDeleteUIMNegotiationMutation } from '@/services/negotiationService';
import { useClientInsuranceListQuery } from '@/services/case-management/clientDetailService';
import { ClientInsurance } from '@/type/case-management/clientDetailTypes';
import { useSettlementCalculationQuery } from '@/services/case-management/settlementService';
import { CreateDemandDialog } from './CreateDemandDialog';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { DeleteConfirmationDialog } from '@/components/common/DeleteConfirmationDialog';
import { useQueryClient } from '@tanstack/react-query';
import {
  CLIENT_TRUST_KEYS,
  useCreateClientTrustMutation,
  useUpdateClientTrustMutation,
} from "@/services/case-management/clientTrustService";
import {
  ClientTrustEntryType,
  DepositType,
  ClientTrust
} from "@/type/case-management/clientTrustTypes";
import { useClientTrustQuery } from '@/services/case-management/caseAnalyticsService';
import { CaseReportFilters, ClientTrustRow } from '@/type/case-management/caseAnalyticsTypes';
import { toast } from '@/hooks/use-toast';
import NegotiationCards from './components/negotiation/negotiationcards';
import { NegotiationTable } from './components/negotiation/Table';
import { ParentOfferDemand } from './components/negotiation/parentOfferDemand';

// Define local interfaces
interface AllUIMViewProps {
  caseId: string;
  onSelectInsurance: (insuranceId: number | null) => void;
}

interface OrganizedNegotiation {
  parent: NegotiationUIMRead;
  children: NegotiationUIMRead[];
}

interface InsuranceGroup {
  insuranceId: number;
  insuranceName: string;
  policyLimit: number | null;
  negotiations: OrganizedNegotiation[];
}

// Type for client insurance has been removed and imported instead

const AllUIMView: FC<AllUIMViewProps> = ({ caseId, onSelectInsurance }) => {
  const { data: clientInsuranceList } = useClientInsuranceListQuery(caseId);
  const [expandedNegotiations, setExpandedNegotiations] = useState<number[]>([]);
  const { data: calculations } = useSettlementCalculationQuery(caseId);
//   const updateSettlementCalculation = useUpdateSettlementCalculationMutation(caseId);
  const { data: UIMNegotiations, refetch: refetchUIMNegotiations } = useUIMNegotiationsQuery(caseId, null);
  const [selectedInsuranceId, setSelectedInsuranceId] = useState<number | null>(null);
  const [editDemand, setEditDemand] = useState<NegotiationUIMRead | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentDeleteId, setCurrentDeleteId] = useState<number>(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const queryClient = useQueryClient();
  const createClientTrust = useCreateClientTrustMutation(queryClient);
  const updateClientTrust = useUpdateClientTrustMutation(queryClient);
  const { data: clientTrustData, refetch: refetchClientTrustData } = useClientTrustQuery({ case_id: caseId } as CaseReportFilters);
  const [currentNegotiationId, setCurrentNegotiationId] = useState<number>(0);
  const updateNegotiationMutation = useUpdateUIMNegotiationMutation(caseId, currentNegotiationId);
  const deleteUIMNegotiationMutation = useDeleteUIMNegotiationMutation(caseId, currentDeleteId.toString());

  useEffect(() => {
    if (selectedInsuranceId) {
      onSelectInsurance(selectedInsuranceId);
    }
  }, [selectedInsuranceId, onSelectInsurance]);

  // Get the latest UIM adjuster ID
  const latestUIMadjusterId = useMemo(() => {
    if (!UIMNegotiations?.length) return null;
    return UIMNegotiations[0]?.adjuster?.id || null;
  }, [UIMNegotiations]);

  const hasAcceptedOfferForInsurance = (insuranceId: number) => {
    return UIMNegotiations?.some(n =>
      n.type === NegotiationType.ACCEPTED_OFFER &&
      n.client_insurance?.id === insuranceId
    ) ?? false;
  };

  const handleUIMDelete = async (id: number) => {
    setCurrentDeleteId(id);
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    await deleteUIMNegotiationMutation.mutateAsync();
    setShowDeleteDialog(false);
    refetchUIMNegotiations();
  };

  const handleUIMAcceptOffer = async (negotiationId: number) => {
    try {
      setCurrentNegotiationId(negotiationId);
      if (updateNegotiationMutation && negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          type: NegotiationType.ACCEPTED_OFFER
        });

        // Find the negotiation that was just accepted
        const acceptedNegotiation = UIMNegotiations?.find(n => n.id === negotiationId);
        if (acceptedNegotiation?.amount && acceptedNegotiation?.client_insurance?.insurance_company?.name) {
          // Create client trust entry for the accepted offer
          await createClientTrust.mutateAsync({
            caseId,
            trustData: {
              check_number: "",
              amount: acceptedNegotiation.amount.toString(),
              memo: `UIM Settlement payment from ${acceptedNegotiation.client_insurance.insurance_company.name}`,
              issuer_payee: acceptedNegotiation.client_insurance.insurance_company.name,
              deposit_date: formatDateForApi(new Date()),
              client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
              deposit_type: DepositType.UIM
            }
          });
        }

        // Refetch data after successful update
        await refetchUIMNegotiations();
        toast({
          title: 'Success',
          description: 'Offer accepted successfully',
        });
      }
    } catch (error) {
      console.error('Error accepting offer:', error);
      toast({
        title: 'Error',
        description: 'Failed to accept offer',
        variant: 'destructive',
      });
    }
  };

  const handleUIMDeacceptOffer = async (negotiationId: number) => {
    try {
      setCurrentNegotiationId(negotiationId);
      if (updateNegotiationMutation && negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          type: NegotiationType.INITIAL_DEMAND
        });
        await refetchUIMNegotiations();
        toast({
          title: 'Success',
          description: 'Offer unaccepted successfully',
        });
      }
    } catch (error) {
      console.error('Error deaccepting offer:', error);
      toast({
        title: 'Error',
        description: 'Failed to unaccept offer',
        variant: 'destructive',
      });
    }
  };

  const handleCombinedSettlementCheck = async (negotiation: NegotiationUIMRead) => {
    try {
      if (!negotiation.amount || !negotiation.client_insurance?.insurance_company?.name) {
        toast({
          title: 'Error',
          description: 'Negotiation amount or insurance company name is missing',
          variant: 'destructive',
        });
        return;
      }

      await createClientTrust.mutateAsync({
        caseId,
        trustData: {
          check_number: "",
          amount: negotiation.amount.toString(),
          memo: `UIM Settlement payment from ${negotiation.client_insurance.insurance_company.name}`,
          issuer_payee: negotiation.client_insurance.insurance_company.name,
          deposit_date: formatDateForApi(new Date()),
          client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
          deposit_type: DepositType.UIM,
          negotiation_id: negotiation.id
        }
      });
      toast({
        title: 'Success',
        description: 'Settlement check created successfully',
      });
    } catch (error) {
      console.error('Error creating settlement check:', error);
      toast({
        title: 'Error',
        description: 'Failed to create settlement check',
        variant: 'destructive',
      });
    }
  };

  const handleVoidSettlement = async (negotiation: NegotiationUIMRead) => {
    const matchingTrust = checkSettlementStatus(negotiation);
    if (!matchingTrust?.id) return;

    try {
      await updateClientTrust.mutateAsync({
        caseId,
        id: matchingTrust.id,
        trustData: {
          check_number: matchingTrust.check_number,
          issuer_payee: matchingTrust.issuer_payee,
          memo: matchingTrust.memo,
          amount: matchingTrust.amount,
          deposit_date: matchingTrust.deposit_date || "",
          client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
          void: true
        }
      });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(caseId) });
      toast({
        title: 'Success',
        description: 'Settlement check voided successfully',
      });
    } catch (error) {
      console.error('Error voiding settlement:', error);
      toast({
        title: 'Error',
        description: 'Failed to void settlement check',
        variant: 'destructive',
      });
    }
  };

  const checkSettlementStatus = (negotiation: NegotiationUIMRead): ClientTrust | null => {
    if (!clientTrustData?.results || !negotiation.client_insurance?.insurance_company?.name || !negotiation.amount) return null;

    const settlementPayments = clientTrustData.results.filter((trust: ClientTrustRow) =>
      trust.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT &&
      !trust.void
    );
    
    const negotiationAmount = parseFloat(negotiation.amount);
    if (isNaN(negotiationAmount)) return null;

    const matchingTrust = settlementPayments.find(trust =>
      trust.issuer_payee === negotiation.client_insurance?.insurance_company?.name &&
      trust.amount === negotiationAmount &&
      trust.memo?.includes(negotiation.client_insurance?.insurance_company?.name) &&
      !trust.void
    );

    if (!matchingTrust || !matchingTrust.id) return null;

    // Convert ClientTrustRow to ClientTrust
    const amount = matchingTrust.amount !== undefined && matchingTrust.amount !== null 
      ? String(matchingTrust.amount) 
      : "";

    const created_at = matchingTrust.created_at !== undefined && matchingTrust.created_at !== null
      ? String(matchingTrust.created_at)
      : "";

    const updated_at = matchingTrust.updated_at !== undefined && matchingTrust.updated_at !== null
      ? String(matchingTrust.updated_at)
      : "";

    return {
      id: Number(matchingTrust.id),
      check_number: matchingTrust.check_number || "",
      issuer_payee: matchingTrust.issuer_payee,
      memo: matchingTrust.memo || "",
      amount,
      deposit_date: matchingTrust.deposit_date || "",
      client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
      void: false,
      created_at,
      updated_at
    };
  };

  const handleEditComplete = () => {
    setEditDemand(null);
    setTimeout(() => {
      refetchUIMNegotiations();
    }, 100);
  };

  const toggleDemand = (negotiationId: number) => {
    setExpandedNegotiations(prev =>
      prev.includes(negotiationId)
        ? prev.filter(id => id !== negotiationId)
        : [...prev, negotiationId]
    );
  };

  // Helper function to organize negotiations by insurance
  const organizeNegotiationsByInsurance = (
    negotiations: NegotiationUIMRead[] | undefined,
    insuranceList: ClientInsurance[] | undefined
  ): InsuranceGroup[] => {
    if (!negotiations || !insuranceList) return [];

    // Create initial groups for each insurance
    const insuranceGroups: InsuranceGroup[] = insuranceList.map(insurance => ({
      insuranceId: insurance.id,
      insuranceName: insurance.insurance_company?.name || "Unknown Insurance",
      policyLimit: insurance.um_uim ? Number(insurance.um_uim) : null,
      negotiations: []
    }));

    insuranceList.forEach(insurance => {
      const insuranceNegotiations = negotiations.filter(n => 
        n.client_insurance?.id === insurance.id
      );

      // Find parent negotiations (initial demands)
      const parentNegotiations = insuranceNegotiations.filter(n => n.previous_offer === null);

      // Create organized negotiations
      const organizedNegotiations: OrganizedNegotiation[] = parentNegotiations.map(parent => {
        const children = insuranceNegotiations.filter(n => n.previous_offer === parent.id);
        return {
          parent,
          children: children.sort((a, b) => 
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          )
        };
      }).sort((a, b) => 
        new Date(b.parent.created_at).getTime() - new Date(a.parent.created_at).getTime()
      );

      if (organizedNegotiations.length > 0 || insurance) {
        insuranceGroups.push({
          insuranceId: insurance.id,
          insuranceName: insurance.insurance_company?.name || "Unknown Insurance",
          policyLimit: insurance.um_uim ? Number(insurance.um_uim) : null,
          negotiations: organizedNegotiations
        });
      }
    });

    return insuranceGroups;
  };

  // Organize negotiations by insurance
  const organizedInsuranceGroups = useMemo(() => {
    return organizeNegotiationsByInsurance(UIMNegotiations, clientInsuranceList);
  }, [UIMNegotiations, clientInsuranceList]);

  // Now useEffect after organizedInsuranceGroups is defined
  useEffect(() => {
    if (clientInsuranceList && clientInsuranceList.length > 0) {
      // Auto-expand the first insurance company's negotiations
      // const firstInsurance = clientInsuranceList[0];
      // const firstInsuranceGroup = organizedInsuranceGroups.find(group => group.insuranceId === firstInsurance.id);
      // if (firstInsuranceGroup && firstInsuranceGroup.negotiations.length > 0) {
      //   setExpandedNegotiations([firstInsuranceGroup.negotiations[0].parent.id]);
      // }
      // Expand all negotiations for all insurance companies by default
      const allNegotiationIds = organizedInsuranceGroups
        .flatMap(group => group.negotiations)
        .map(negotiation => negotiation.parent.id);
      setExpandedNegotiations(allNegotiationIds);
    }
  }, [clientInsuranceList, organizedInsuranceGroups]);

  // Calculate quick stats
  const quickStats = useMemo(() => {
    if (!UIMNegotiations?.length) return {
      demandAmount: 0,
      latestOffer: 0,
      acceptedOffer: 0,
      nextDeadline: null
    };

    // Calculate total demand amount from all insurances
    const initialDemands = UIMNegotiations.filter(n => n.type === NegotiationType.INITIAL_DEMAND);
    const totalDemandAmount = initialDemands.reduce((sum, demand) => sum + (Number(demand.amount) || 0), 0);

    // Find latest offer
    const latestOffer = [...UIMNegotiations]
      .filter(n => n.type !== NegotiationType.INITIAL_DEMAND && n.type !== NegotiationType.ACCEPTED_OFFER)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

    // Find accepted offers
    const acceptedOffers = UIMNegotiations.filter(n => n.type === NegotiationType.ACCEPTED_OFFER);
    const totalAcceptedAmount = acceptedOffers.reduce((sum, offer) => sum + (Number(offer.amount) || 0), 0);

    // Find next deadline
    const upcomingDeadlines = UIMNegotiations
      .filter(n => n.response_deadline && new Date(n.response_deadline) >= new Date())
      .sort((a, b) => new Date(a.response_deadline!).getTime() - new Date(b.response_deadline!).getTime());

    return {
      demandAmount: totalDemandAmount,
      latestOffer: latestOffer?.amount || 0,
      acceptedOffer: totalAcceptedAmount,
      nextDeadline: upcomingDeadlines.length > 0 ? upcomingDeadlines[0].response_deadline : null
    };
  }, [UIMNegotiations]);

  const handleArchiveToggle = async (negotiationId: number, isArchived: boolean) => {
    try {
      setCurrentNegotiationId(negotiationId);
      if (updateNegotiationMutation && negotiationId !== 0) {
        await updateNegotiationMutation.mutateAsync({
          is_archived: isArchived
        });
        await refetchUIMNegotiations();
        toast({
          title: 'Success',
          description: isArchived ? 'Negotiation archived successfully' : 'Negotiation unarchived successfully',
        });
      }
    } catch (error) {
      console.error('Error archiving/unarchiving negotiation:', error);
      toast({
        title: 'Error',
        description: 'Failed to update negotiation',
        variant: 'destructive',
      });
    }
  };

  // Function to refetch all necessary data when a demand is created or updated
  const refetchAllData = async () => {
    try {
      await Promise.all([
        refetchUIMNegotiations(),
        refetchClientTrustData?.(),
        queryClient.invalidateQueries({ queryKey: ['settlement-calculation', caseId] })
      ]);
    } catch (error) {
      console.error('Error refetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh data',
        variant: 'destructive',
      });
    }
  };

  // Render UIM insurance section with consistent approach as AllDefendantsView
  const renderInsuranceSection = (insurance: ClientInsurance) => {
    const insuranceGroup = organizedInsuranceGroups.find(group => group.insuranceId === insurance.id);
    const hasAcceptedOfferForThisInsurance = hasAcceptedOfferForInsurance(insurance.id);
    const acceptedOfferId = hasAcceptedOfferForThisInsurance ? 
      UIMNegotiations?.find(n => 
        n.type === NegotiationType.ACCEPTED_OFFER && 
        n.client_insurance?.id === insurance.id
      )?.id || null : null;

    return (
      <div key={insurance.id} className="bg-slate-50 border-b">
        <div className="p-3">
          <div className="flex justify-between items-center">
            <div className="flex flex-col">
              <span className="font-semibold text-slate-700">
                {insurance.insurance_company?.name || "Unknown Insurance"}
              </span>
              <span className="text-xs text-slate-500">
                Policy Limit: {insurance.um_uim ? `$${insurance.um_uim.toLocaleString()}` : 'Not specified'}
              </span>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span>
                    <CreateDemandDialog
                      caseId={caseId}
                      adjusterId={latestUIMadjusterId}
                      disabled={hasAcceptedOfferForThisInsurance || false}
                      negotiations={UIMNegotiations}
                      clientInsuranceId={insurance.id}
                      isUIM={true}
                      onEditComplete={refetchAllData}
                    />
                  </span>
                </TooltipTrigger>
                {hasAcceptedOfferForThisInsurance && (
                  <TooltipContent>
                    <p>Cannot create new demand while there is an accepted offer for this insurance</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="bg-white">
          {insuranceGroup && insuranceGroup.negotiations.length > 0 ? (
            insuranceGroup.negotiations.map(({ parent, children }) => {
              const isAcceptedOffer = parent.type === NegotiationType.ACCEPTED_OFFER;
              const hasAcceptedChild = children.some(child => child.type === NegotiationType.ACCEPTED_OFFER);
              
              // Create header string
              const headerParts = [];
              if (insurance.insurance_company?.name) {
                headerParts.push(insurance.insurance_company.name);
              }
              
              if (parent.adjuster) {
                headerParts.push(`${parent.adjuster.first_name} ${parent.adjuster.last_name}`);
              }
              
              if (parent.amount) {
                headerParts.push(`Amount $${Number(parent.amount).toLocaleString()}`);
              }
              
              if (parent.date_sent) {
                headerParts.push(`${parent.type === NegotiationType.INITIAL_DEMAND ? "Demand" : "Offer"} ${formatDateForDisplay(parent.date_sent)}`);
              }
              
              if (parent.response_deadline) {
                headerParts.push(`exp ${formatDateForDisplay(parent.response_deadline)}`);
              }
              
              if (calculations?.fees_percentage) {
                const feePercentage = calculations.fees_percentage === '0.00' ? '33.33' : Number(calculations.fees_percentage).toFixed(2);
                headerParts.push(`Attorney Fee ${feePercentage}%`);
              }
              
              const headerString = headerParts.join(' | ');

              return (
                <Fragment key={parent.id}>
                  <ParentOfferDemand<NegotiationUIMRead>
                    parent={parent}
                    isExpanded={expandedNegotiations.includes(parent.id)}
                    toggleExpand={() => toggleDemand(parent.id)}
                    headerString={headerString}
                    caseId={caseId}
                    selectedDefendant={null}
                    hasAcceptedOffer={hasAcceptedOfferForThisInsurance}
                    hasAcceptedChild={hasAcceptedChild}
                    isAcceptedOffer={isAcceptedOffer}
                    onDelete={handleUIMDelete}
                    onAcceptOffer={handleUIMAcceptOffer}
                    onDeacceptOffer={handleUIMDeacceptOffer}
                    onSettlementCheck={handleCombinedSettlementCheck}
                    onEditDemand={(negotiation) => {
                      setEditDemand(negotiation as NegotiationUIMRead);
                      setShowEditDialog(true);
                    }}
                    onArchiveToggle={handleArchiveToggle}
                    checkSettlementStatus={checkSettlementStatus}
                    handleVoidSettlement={handleVoidSettlement}
                    insuranceCompany={parent.client_insurance?.insurance_company}
                  />

                  {expandedNegotiations.includes(parent.id) && (
                    <div className="pl-6 pr-3 pb-3">
                      <NegotiationTable<NegotiationUIMRead>
                        negotiations={UIMNegotiations?.filter(n => n.client_insurance?.id === insurance.id) || []}
                        parentId={parent.id}
                        caseId={caseId}
                        selectedDefendant={null}
                        onAcceptOffer={handleUIMAcceptOffer}
                        onDeacceptOffer={handleUIMDeacceptOffer}
                        hasAcceptedOffer={hasAcceptedOfferForThisInsurance}
                        onDelete={handleUIMDelete}
                        parentIsArchived={parent.is_archived}
                        negotiationType="uim"
                        parentInsuranceCompany={parent.client_insurance?.insurance_company}
                        acceptedOfferId={acceptedOfferId}
                        checkSettlementStatus={checkSettlementStatus}
                        handleVoidSettlement={handleVoidSettlement}
                      />
                    </div>
                  )}
                </Fragment>
              );
            })
          ) : (
            <div className="text-center py-8 bg-slate-50">
              <p className="text-gray-500 text-sm">No UIM negotiations found for this insurance.</p>
              <p className="text-xs text-gray-400 mt-1">Click the &quot;Create Demand&quot; button to get started.</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-3 sm:p-4 space-y-4">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 bg-gradient-to-r from-slate-50 to-gray-50 p-3 sm:p-4 rounded-lg shadow-sm">
        <div className="flex md:flex-row justify-between items-start md:items-center gap-3">
          <h1 className="text-xl sm:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-slate-900 to-slate-700">
            UIM Negotiations
          </h1>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="h-9 min-w-[180px] justify-between bg-white hover:bg-slate-50 border-slate-200 text-sm"
              >
                {!clientInsuranceList ? (
                  <LoadingSpinner />
                ) : (
                  <>
                    {selectedInsuranceId ?
                      clientInsuranceList?.find(i => i.id === selectedInsuranceId)?.insurance_company?.name || "Select Insurance" :
                      "All Insurances"}
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px]">
              <DropdownMenuItem
                onClick={() => {
                  setSelectedInsuranceId(null);
                  onSelectInsurance(null);
                }}
                className="cursor-pointer hover:bg-slate-50 text-sm"
              >
                All Insurances
              </DropdownMenuItem>
              {clientInsuranceList?.map((insurance) => (
                <DropdownMenuItem
                  key={insurance.id}
                  onClick={() => setSelectedInsuranceId(insurance.id)}
                  className="cursor-pointer hover:bg-slate-50 text-sm"
                >
                  {insurance.insurance_company?.name || "Unknown Insurance"}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Stats Section */}
      <NegotiationCards 
        data={[
          {
            icon: DollarSign,
            title: "Total Current Demands",
            metrics: {
              primary: {
                type: 'monetary',
                value: quickStats.demandAmount || 0
              }
            },
            bgColor: "bg-blue-50"
          },
          {
            icon: DollarSign,
            title: "Total Accepted Offers",
            metrics: {
              primary: {
                type: 'monetary',
                value: quickStats.acceptedOffer || 0
              }
            },
            bgColor: "bg-orange-50"
          },
          {
            icon: DollarSign,
            title: "Latest Offer",
            metrics: {
              primary: {
                type: 'monetary',
                value: quickStats.latestOffer || 0
              }
            },
            bgColor: "bg-emerald-50"
          },
          {
            icon: Calendar,
            title: "Next Deadline",
            metrics: {
              primary: {
                type: 'date',
                value: quickStats.nextDeadline ? formatDateForDisplay(quickStats.nextDeadline) : '',
                unit: 'days'
              }
            },
            bgColor: "bg-rose-50"
          }
        ]}
      />

      {/* UIM Insurance List */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-slate-800">UIM Negotiations</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y divide-slate-200">
            {clientInsuranceList?.map(insurance => renderInsuranceSection(insurance))}
            {(!clientInsuranceList || clientInsuranceList.length === 0) && (
              <div className="text-center py-8 bg-slate-50">
                <p className="text-gray-500 text-sm">No client insurance found.</p>
                <p className="text-xs text-gray-400 mt-1">Add client insurance to create UIM demands.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {editDemand && showEditDialog && (
        <CreateDemandDialog
          key={`edit-${editDemand.id}-${showEditDialog}`}
          caseId={caseId}
          adjusterId={latestUIMadjusterId}
          disabled={false}
          negotiations={UIMNegotiations}
          editDemand={editDemand}
          onEditComplete={() => {
            handleEditComplete();
            refetchAllData();
            setShowEditDialog(false);
          }}
          onClose={() => {
            setShowEditDialog(false);
            setEditDemand(null);
          }}
          isUIM={true}
          clientInsuranceId={editDemand.client_insurance?.id}
        />
      )}

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleConfirmDelete}
        text="UIM Negotiation"
      />
    </div>
  );
};

export default AllUIMView; 