import { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, CheckCircle, Upload, Clock, Cpu, Send, History } from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { DocumentFile } from '@/type/doocument';
import { useRouter } from 'next/navigation';
import { useDemandGenerationRequest, useSubmitForDemandGeneration, useSubmitForReview } from '@/services/caseService';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useCategorizeFilesBackground } from '@/services/recordAIAnalysisService';
import { useCaseDocumentsCategories } from '@/services/documentService';
import { USStates, USStatesLabels } from '@/constants/commont';
import {
    Di<PERSON>,
    Dialog<PERSON>ontent,
    Di<PERSON>Header,
    DialogTitle,
    Di<PERSON>Footer,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Timeline, TimelineItem } from '@/components/ui/timeline';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

interface DemandGenerationProps {
    caseId: string;
}

export function DemandGeneration({ caseId }: DemandGenerationProps) {
    const router = useRouter();
    const [documents, setDocuments] = useState<DocumentFile[]>([]);
    const { data: demandGenerationRequest, isLoading } = useDemandGenerationRequest(caseId);
    const submitDemandGeneration = useSubmitForDemandGeneration();
    const submitForReview = useSubmitForReview();
    const { allDocuments, selectedCaseDetail } = useSelector((state: RootState) => state.caseView);
    const categorizeFilesBackground = useCategorizeFilesBackground();
    const { data: documentCategories } = useCaseDocumentsCategories(caseId);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [reviewComment, setReviewComment] = useState('');
    const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    
    

    useEffect(() => {
        if (allDocuments) {
            setDocuments(allDocuments.filter((file: DocumentFile) => file.is_part_of_demand_package));
        }
    }, [allDocuments]);

    console.log(allDocuments);

    const navigateToDocuments = () => {
        router.push(`/dashboard/case/${caseId}/setup?step=6`);
    };

    const submitForDemandGeneration = () => {
        if (demandGenerationRequest?.id) {
            submitDemandGeneration.mutate(demandGenerationRequest.id, {
                onSuccess: () => {
                    const stateAbbreviation = selectedCaseDetail?.metadata?.state_of_incident as USStates;
                    const metadata = {
                        state_of_incident: stateAbbreviation ? USStatesLabels[stateAbbreviation] : null,
                        case_type: selectedCaseDetail?.metadata?.case_type,
                      };
                    categorizeFilesBackground.mutate({
                        case_id: caseId,
                        all_files_names: documents.map(doc => doc.file_name),
                        documentsWithCategories: documentCategories || [],
                        accident_date: selectedCaseDetail?.metadata?.date_of_incident || '' ,
                        state_of_incident: metadata?.state_of_incident || '',
                        case_type: metadata?.case_type || ''
                    });
                }
            });
        }
    };

    const handleSubmitForReview = () => {
        if (demandGenerationRequest?.id) {
            submitForReview.mutate({
                id: demandGenerationRequest.id,
                reviewText: reviewComment
            });
            setIsDialogOpen(false);
            setReviewComment('');
        }
    };

    const handleReviewHistoryClick = () => {
        setIsHistoryDialogOpen(true);
    };

    const parseReviewHistory = (historyText: string) => {
        const regex = /----\s*\((.*?)\)\s*by\s*(.*?)(?:\s*\[ADMIN\])?\s*----\s*([\s\S]*?)(?=----|\s*$)/g;
        const matches = [];
        let match;

        while ((match = regex.exec(historyText)) !== null) {
            matches.push({
                created_at: match[1],
                reviewer: match[2],
                review_text: match[3].trim()
            });
        }

        return matches;
    };

    const handleOpenChange = (newOpen: boolean) => {
        if (!newOpen && reviewComment.trim()) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsDialogOpen(true);
            }
        }
    };

    const handleClose = () => {
        setIsDialogOpen(false);
        setReviewComment('');
    };

    const handleCancelClick = () => {
        if (reviewComment.trim()) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    return (
        <div className="space-y-8 p-8 h-[calc(100vh-310px)] overflow-y-auto">
            {/* Document Status Section */}
            <Card>
                <CardContent className="flex items-start gap-4 p-0">
                    {documents.length === 0 ? (
                        <Alert variant="default" className="w-full border-0 shadow-none">
                            <AlertTriangle className="h-5 w-5" />
                            <AlertDescription className="flex items-center justify-between">
                                <span>No documents found in demand folders. Please upload relevant documents to proceed.</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={navigateToDocuments}
                                    className="gap-2"
                                >
                                    <Upload className="h-4 w-4" />
                                    Upload Documents
                                </Button>
                            </AlertDescription>
                        </Alert>
                    ) : (
                        <Alert variant="default" className="w-full border-0 shadow-none">
                            <h2>Document Status</h2>
                            <AlertDescription className="flex items-center">
                                {documents.length} document(s) ready for demand generation
                            </AlertDescription>
                        </Alert>
                    )}
                </CardContent>
            </Card>

            {/* Generation Timeline */}
            <Card>
                <CardHeader>
                    <CardTitle>Generation Progress</CardTitle>
                </CardHeader>
                <CardContent>
                    {isLoading ? (
                        <div className="flex justify-center items-center p-4">
                            <LoadingSpinner />
                        </div>
                    ) : (
                        <Timeline>
                            <TimelineItem
                                title="Initial Generation"
                                description="Submit documents for demand letter generation"
                                icon={demandGenerationRequest?.is_submitted_for_demand_generation ? <CheckCircle className="h-5 w-5" /> : <Send className="h-5 w-5" />}
                                isActive={!demandGenerationRequest?.is_submitted_for_demand_generation}
                                isCompleted={demandGenerationRequest?.is_submitted_for_demand_generation}
                            >
                                {!demandGenerationRequest?.is_submitted_for_demand_generation && (
                                    <Button 
                                        disabled={documents.length === 0}
                                        onClick={submitForDemandGeneration}
                                        className="gap-1 bg-white border-green-500 border text-green-500 hover:bg-green-50 text-xs px-2 py-1 h-7"
                                    >
                                        <Send className="h-3 w-3 text-green-500" />
                                        Generate Demand
                                    </Button>
                                )}
                            </TimelineItem>
                            <TimelineItem
                                title="Initial Generation"
                                description="Review generated demand letter and submit for processing"
                                icon={demandGenerationRequest?.demand_generation_completed ? <CheckCircle className="h-5 w-5" /> : <Clock className="h-5 w-5" />}
                                isActive={demandGenerationRequest?.is_submitted_for_demand_generation && !demandGenerationRequest?.demand_generation_completed}
                                isCompleted={demandGenerationRequest?.demand_generation_completed}
                            >
                                {!demandGenerationRequest?.demand_generation_completed && demandGenerationRequest?.is_submitted_for_demand_generation && (
                                    <div 
                                        className="inline-flex items-center justify-center gap-1 px-2 py-1 text-xs bg-white border-blue-500 border text-[#060216]-500 hover:bg-blue-50 rounded-md cursor-not-allowed opacity-50 h-7"
                                    >
                                        <Cpu className="h-3 w-3 text-[#060216]-500" />
                                        Processing
                                    </div>
                                )}
                            </TimelineItem>
                            <TimelineItem
                                title="Initial Generation"
                                description="Review generated demand letter and submit for processing"
                                icon={demandGenerationRequest?.review_completed ? <CheckCircle className="h-5 w-5" /> : <Send className="h-5 w-5" />}
                                isActive={demandGenerationRequest?.demand_generation_completed && !demandGenerationRequest?.review_completed}
                                isCompleted={demandGenerationRequest?.review_completed}
                            >
                                {!demandGenerationRequest?.review_completed && demandGenerationRequest?.demand_generation_completed && (
                                    <>
                                        <div className="flex gap-1">
                                            {demandGenerationRequest?.review_text_history && demandGenerationRequest?.review_text_history.length > 0 && 
                                                <>
                                                    <Button 
                                                        onClick={handleReviewHistoryClick}
                                                        className="gap-1 bg-white border-gray-500 border text-gray-500 hover:bg-gray-50 text-xs px-2 py-1 h-7"
                                                        variant="secondary"
                                                    >
                                                        <History className="h-3 w-3" />
                                                        Review History
                                                    </Button>

                                                    <Dialog open={isHistoryDialogOpen} onOpenChange={setIsHistoryDialogOpen}>
                                                        <DialogContent>
                                                            <DialogHeader>
                                                                <DialogTitle>Review History</DialogTitle>
                                                            </DialogHeader>
                                                            <div className="py-4 space-y-4">
                                                                {parseReviewHistory(demandGenerationRequest.review_text_history).map((review, index) => (
                                                                    <div key={index} className="border rounded-lg p-4">
                                                                        <div className="text-sm text-gray-500 mb-2">
                                                                            <span>{review.created_at}</span>
                                                                            <span className="mx-2">•</span>
                                                                            <span>{review.reviewer}</span>
                                                                        </div>
                                                                        <div className="text-sm">
                                                                            {review.review_text}
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                            <DialogFooter>
                                                                <Button onClick={() => setIsHistoryDialogOpen(false)}>
                                                                    Close
                                                                </Button>
                                                            </DialogFooter>
                                                        </DialogContent>
                                                    </Dialog>
                                                </>
                                            }
                                            <Button 
                                                onClick={() => setIsDialogOpen(true)}
                                                className="gap-1 bg-white border-green-500 border text-green-500 hover:bg-green-50 text-xs px-2 py-1 h-7"
                                            >
                                                <Send className="h-3 w-3 text-green-500" />
                                                Submit for Review Again
                                            </Button>
                                        </div>

                                        <Dialog open={isDialogOpen} onOpenChange={handleOpenChange}>
                                            <DialogContent>
                                                <DialogHeader>
                                                    <DialogTitle>Submit for Review</DialogTitle>
                                                </DialogHeader>
                                                <div className="py-4">
                                                    <Textarea
                                                        placeholder="Enter your review comments..."
                                                        value={reviewComment}
                                                        onChange={(e) => setReviewComment(e.target.value)}
                                                        className="min-h-[100px] focus:outline-none focus-visible:outline-none focus:border-none focus:ring-0"
                                                    />
                                                </div>
                                                <DialogFooter>
                                                    <Button variant="outline" onClick={handleCancelClick}>
                                                        Cancel
                                                    </Button>
                                                    <Button onClick={handleSubmitForReview}>
                                                        Submit
                                                    </Button>
                                                </DialogFooter>
                                            </DialogContent>
                                        </Dialog>
                                    </>
                                )}
                            </TimelineItem>
                        </Timeline>
                    )}
                </CardContent>
            </Card>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleClose}
                onCancel={handleClose}
            />
        </div>
    );
} 