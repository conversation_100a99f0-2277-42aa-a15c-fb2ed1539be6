'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON>T<PERSON><PERSON>,
    <PERSON><PERSON>Footer,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useState, useEffect } from "react";
import { USStatesLabels } from '@/constants/commont';
import { PhoneNumberInput } from "@/components/ui/phone-number-input";
import {
    useCreateLoanCompanyMutation,
    useUpdateLoanCompanyMutation
} from '@/services/case-management/settlementService';
import {
    <PERSON>anCompany,
    LoanCompanyCreateRequest,
    LoanCompanyUpdateRequest
} from '@/type/case-management/settlementTypes';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import RichTextEditor from "@/components/ui/RichTextEditor";

const loanCompanyFormSchema = z.object({
    company: z.string().min(1, "Company name is required"),
    payee: z.string().optional(),
    street1: z.string().optional(),
    street2: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip: z.string()
        .refine((val) => !val || /^\d{5}(-\d{4})?$/.test(val), {
            message: "If provided, ZIP code must be in format: 12345 or 12345-6789"
        })
        .optional(),
    phone: z.string()
        .refine((val) => !val || /^\d{3}-\d{3}-\d{4}$/.test(val), {
            message: "If provided, phone must be in format: ************"
        })
        .optional(),
    phoneExt: z.string()
        .refine((val) => !val || /^\d{0,6}$/.test(val), {
            message: "If provided, extension must be up to 6 digits"
        })
        .optional(),
    cell: z.string()
        .refine((val) => !val || /^\d{3}-\d{3}-\d{4}$/.test(val), {
            message: "If provided, cell phone must be in format: ************"
        })
        .optional(),
    email: z.string()
        .optional()
        .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
            message: "If provided, email must be valid (e.g., <EMAIL>)"
        }),
    fax: z.string()
        .refine((val) => !val || /^\d{3}-\d{3}-\d{4}$/.test(val), {
            message: "If provided, fax must be in format: ************"
        })
        .optional(),
    taxId: z.string()
        .refine((val) => !val || /^\d{2}-\d{7}$|^\d{3}-\d{2}-\d{4}$/.test(val), {
            message: "If provided, Tax ID must be in format: 12-3456789 or SSN: ***********"
        })
        .optional(),
    website: z.string()
        .refine((val) => !val || /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/.test(val), {
            message: "If provided, website must be a valid domain (e.g., example.com)"
        })
        .optional(),
    note: z.string()
        .optional(),
});

type LoanCompanyFormValues = z.infer<typeof loanCompanyFormSchema>;

interface AddEditLoanCompanyProps {
    isEdit: boolean;
    children: React.ReactNode;
    selectedLoanCompany?: LoanCompany | null;
}

export function AddEditLoanCompany({
    isEdit = false,
    children,
    selectedLoanCompany
}: AddEditLoanCompanyProps) {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = form.formState.isDirty;
        if (!newOpen && isDirty && !form.formState.isSubmitting) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(true);
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        form.reset();
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const form = useForm<LoanCompanyFormValues>({
        resolver: zodResolver(loanCompanyFormSchema),
        defaultValues: {
            company: "",
            payee: "",
            street1: "",
            street2: "",
            city: "",
            state: "",
            zip: "",
            phone: "",
            phoneExt: "",
            cell: "",
            email: "",
            fax: "",
            taxId: "",
            website: "",
            note: "",
        },
    });

    useEffect(() => {
        if (selectedLoanCompany && isEdit) {
            form.reset({
                company: selectedLoanCompany.company,
                payee: selectedLoanCompany.payee || "",
                street1: selectedLoanCompany.street1 || "",
                street2: selectedLoanCompany.street2 || "",
                city: selectedLoanCompany.city || "",
                state: selectedLoanCompany.state || "",
                zip: selectedLoanCompany.zip_code || "",
                phone: selectedLoanCompany.phone || "",
                cell: selectedLoanCompany.cell || "",
                email: selectedLoanCompany.email || "",
                fax: selectedLoanCompany.fax || "",
                taxId: selectedLoanCompany.tax_id || "",
                website: selectedLoanCompany.website || "",
                note: selectedLoanCompany.note || "",
            });
        } else if (!isEdit) {
            form.reset({
                company: "",
                payee: "",
                street1: "",
                street2: "",
                city: "",
                state: "",
                zip: "",
                phone: "",
                cell: "",
                email: "",
                fax: "",
                taxId: "",
                website: "",
                note: "",
            });
        }
    }, [selectedLoanCompany, isEdit, form]);

    async function onSubmit(values: LoanCompanyFormValues) {
        try {
            if (isEdit && selectedLoanCompany) {
                await handleUpdate({
                    company: values.company,
                    payee: values.payee || undefined,
                    street1: values.street1 || undefined,
                    street2: values.street2 || undefined,
                    city: values.city || undefined,
                    state: values.state || undefined,
                    zip_code: values.zip || undefined,
                    phone: values.phone || undefined,
                    cell: values.cell || undefined,
                    email: values.email || undefined,
                    fax: values.fax || undefined,
                    tax_id: values.taxId || undefined,
                    website: values.website || undefined,
                    note: values.note || undefined,
                });
            } else {
                await handleCreate({
                    company: values.company,
                    payee: values.payee || undefined,
                    street1: values.street1 || undefined,
                    street2: values.street2 || undefined,
                    city: values.city || undefined,
                    state: values.state || undefined,
                    zip_code: values.zip || undefined,
                    phone: values.phone || undefined,
                    cell: values.cell || undefined,
                    email: values.email || undefined,
                    fax: values.fax || undefined,
                    tax_id: values.taxId || undefined,
                    website: values.website || undefined,
                    note: values.note || undefined,
                });
            }

            setOpen(false);
            form.reset();
        } catch (error) {
            console.error(`Failed to ${isEdit ? 'update' : 'create'} loan company:`, error);
        }
    }

    const createLoanCompany = useCreateLoanCompanyMutation();
    const updateLoanCompany = useUpdateLoanCompanyMutation(selectedLoanCompany?.id || 0);

    const handleCreate = async (data: LoanCompanyCreateRequest) => {
        await createLoanCompany.mutateAsync(data);
    };

    const handleUpdate = async (data: LoanCompanyUpdateRequest) => {
        if (!selectedLoanCompany?.id) {
            throw new Error('Loan company ID is required for update');
        }
        await updateLoanCompany.mutateAsync(data);
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {children}
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? `Edit Loan Company` : 'Add New Loan Company'}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="max-h-[80vh] overflow-y-auto pr-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                {/* Company Name */}
                                <FormField
                                    control={form.control}
                                    name="company"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Company *</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Payee */}
                                <FormField
                                    control={form.control}
                                    name="payee"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Payee</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Address Fields */}
                                <div className="space-y-4">
                                    <FormField
                                        control={form.control}
                                        name="street1"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Street 1</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="street2"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Street 2</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <div className="grid grid-cols-3 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="city"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>City</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="state"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>State</FormLabel>
                                                    <Select 
                                                        value={field.value || ""} 
                                                        onValueChange={field.onChange}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select state" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            {field.value && (
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    className="mb-2 w-full justify-center text-sm"
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        field.onChange("");
                                                                    }}
                                                                >
                                                                    Clear Selection
                                                                </Button>
                                                            )}
                                                            {Object.entries(USStatesLabels).map(([value, label]) => (
                                                                <SelectItem key={value} value={value}>
                                                                    {label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="zip"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>ZIP</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                </div>

                                {/* Contact Fields */}
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-4">
                                        <div className="flex gap-2">
                                            <FormField
                                                control={form.control}
                                                name="phone"
                                                render={({ field }) => (
                                                    <FormItem className="flex-1">
                                                        <FormLabel>Phone</FormLabel>
                                                        <FormControl>
                                                            <PhoneNumberInput
                                                                {...field}
                                                                value={field.value || ''}
                                                                placeholder="************"
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                            <FormField
                                                control={form.control}
                                                name="phoneExt"
                                                render={({ field }) => (
                                                    <FormItem className="w-20">
                                                        <FormLabel>Ext</FormLabel>
                                                        <FormControl>
                                                            <Input {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        <FormField
                                            control={form.control}
                                            name="cell"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Cell</FormLabel>
                                                    <FormControl>
                                                        <PhoneNumberInput
                                                            {...field}
                                                            value={field.value || ''}
                                                            placeholder="************"
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="email"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Email</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} type="email" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <div className="space-y-4">
                                        <FormField
                                            control={form.control}
                                            name="fax"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Fax</FormLabel>
                                                    <FormControl>
                                                        <PhoneNumberInput
                                                            {...field}
                                                            value={field.value || ''}
                                                            placeholder="************"
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="taxId"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Tax ID / SSN</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="website"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Website</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                </div>

                                {/* Note Field */}
                                <FormField
                                    control={form.control}
                                    name="note"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Note</FormLabel>
                                            <FormControl>
                                                <RichTextEditor
                                                    value={field.value || ""}
                                                    onChange={(value) => {
                                                        field.onChange(value);
                                                    }}
                                                    className="min-h-[100px]"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <DialogFooter>
                                <Button type="button" onClick={(e) => {
                                    form.handleSubmit((values) => onSubmit(values))(e);
                                }}>
                                    {isEdit ? 'Save Changes' : 'Add Loan Company'}
                                </Button>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancelClick}
                                >
                                    Cancel
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleCancelClick}
                onCancel={handleCancelClick}
            />
        </>
    );
} 