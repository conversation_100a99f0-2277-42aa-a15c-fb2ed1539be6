import {
  ExternalLink,
  Shield,
  Building2,
  User,
  LucideIcon,
  UserCog,
  FileText,
  Hash,
  Mail,
  Phone,
  Smartphone,
  Printer,
  MapPin,
  Calendar,
  Users,
  Languages,
  BadgeInfo,
  Car,
  Trash,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { CreateUpdateDefendant } from "@/app/defendant/components/CreateUpdateDefendant";
import {
  useDefendantsListQuery,
  useDeleteDefendantMutation,
  useDeleteDefendantInsuranceMutation,
} from "@/services/case-management/defendantService";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { AddressLink } from "@/components/gMap/address-link";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { useToast } from "@/hooks/use-toast";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { useState } from "react";
import { formatDateForDisplay } from "@/utils/dateUtils";

interface InfoFieldProps {
  icon: LucideIcon | (() => JSX.Element);
  label: string;
  value: string | React.ReactNode;
}

interface InsuranceType {
  id: number;
  insurance_company: {
    id: number;
    name: string;
  };
  claim_number: string | null;
  policy_number: string | null;
  policy_type: string | null;
  liability_status: string | null;
  coverage_status: string | null;
  claim_note: string | null;
  umbrella_policy?: boolean | null;
}

interface InsuranceCardProps {
  insurance: InsuranceType;
}

interface CardHeaderProps {
  title: string;
  status?: {
    label: string;
    isActive?: boolean;
  };
  caseId: string;
  templateConfig: {
    contextType: TemplateContextType;
    insuranceId?: string | undefined;
    defendantId?: string;
  };
  actions?: React.ReactNode;
  onDelete?: () => void;
}

const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  status,
  caseId,
  templateConfig,
  actions,
  onDelete,
}) => (
  <div className="flex items-center justify-between">
    <h5 className="font-medium text-[#060216]">{title}</h5>
    <div className="flex items-center gap-2">
      {status && (
        <span
          className={cn(
            "px-2 py-0.5 rounded-full text-xs font-medium",
            status.isActive
              ? "bg-green-100 text-green-700"
              : "bg-gray-100 text-gray-600"
          )}
        >
          {status.label}
        </span>
      )}
      {actions}
      {onDelete && (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 hover:bg-red-50 text-red-600"
          onClick={onDelete}
        >
          <Trash className="h-4 w-4" />
        </Button>
      )}
      <ManageEmailTemplate
        caseId={caseId}
        defendantId={templateConfig.defendantId}
        defendantInsuranceId={templateConfig.insuranceId}
        contextType={templateConfig.contextType}
      />
    </div>
  </div>
);

const InfoField: React.FC<InfoFieldProps> = ({ icon: Icon, label, value }) => (
  <div>
    <div className="flex items-center gap-2 mb-1">
      {typeof Icon === "function" && !("render" in Icon) ? (
        <Icon />
      ) : (
        <Icon className="h-4 w-4 text-[#060216]/50" />
      )}
      <label className="text-sm text-[#060216]/50">{label}</label>
    </div>
    <p className="text-sm text-[#060216]">{value}</p>
  </div>
);

const InsuranceCard: React.FC<
  InsuranceCardProps & { caseId: string; defendantId: string }
> = ({ insurance, caseId, defendantId }) => {
  const { toast } = useToast();
  const deleteInsurance = useDeleteDefendantInsuranceMutation(
    caseId,
    defendantId
  );
  const { refetch: refetchDefendants } = useDefendantsListQuery(caseId);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteInsurance.mutateAsync(insurance.id.toString());
      await refetchDefendants();
      toast({
        title: "Success",
        description: "Insurance record deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting insurance record:", error);
      toast({
        title: "Error",
        description: "Failed to delete insurance record",
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  const insuranceFields = [
    {
      icon: Hash,
      label: "claim",
      value: insurance.claim_number || "—",
    },
    {
      icon: Hash,
      label: "policy",
      value: insurance.policy_number || "—",
    },
    {
      icon: FileText,
      label: "policy type",
      value: insurance.policy_type || "—",
    },
    {
      icon: Shield,
      label: "liability status",
      value: insurance.liability_status || "—",
    },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 space-y-4">
      <CardHeader
        title={insurance.insurance_company?.name || "—"}
        status={
          insurance.coverage_status
            ? {
                label: insurance.coverage_status,
                isActive: insurance.coverage_status === "Active",
              }
            : undefined
        }
        caseId={caseId}
        templateConfig={{
          contextType: "defendant_insurance",
          defendantId: defendantId,
          insuranceId: insurance.id.toString(),
        }}
        onDelete={() => setDeleteDialogOpen(true)}
      />
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {insuranceFields.map((field, index) => (
          <InfoField
            key={index}
            icon={field.icon}
            label={field.label}
            value={field.value}
          />
        ))}
      </div>
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        text="Defendant Insurance"
        onConfirm={handleDelete}
      />
    </div>
  );
};

export function DefendantDetail({ caseId }: { caseId: string }) {
  const router = useRouter();
  const {
    data: defendantList,
    isLoading,
    error,
  } = useDefendantsListQuery(caseId);
  const deleteDefendant = useDeleteDefendantMutation(caseId);
  const [deleteDefendantDialogOpen, setDeleteDefendantDialogOpen] =
    useState(false);
  const [selectedDefendantId, setSelectedDefendantId] = useState<number | null>(
    null
  );

  if (isLoading) {
    return <Skeleton className="w-full h-48" />;
  }

  if (error) {
    return <div>Error loading defendants</div>;
  }

  const handleDelete = async () => {
    try {
      if (selectedDefendantId) {
        await deleteDefendant.mutateAsync(selectedDefendantId.toString());
      }
    } catch (error) {
      console.error("Error deleting defendant:", error);
    } finally {
      setDeleteDefendantDialogOpen(false);
    }
  };

  return (
    <div className="p-8 flex flex-col gap-6 bg-[#f5f5f5]/0">
      {/* Header with Add Button */}
      <div className="flex justify-between items-center">
        <div className="flex flex-row items-center gap-3">
          <User className="h-5 w-5 text-[#060216]" />
          <h3 className="text-[22px] font-bold font-Manrope text-[#060216]">
            Defendants
          </h3>
        </div>
        <CreateUpdateDefendant isEditing={false} caseId={caseId} />
      </div>

      {/* Defendants List */}
      <div className="flex flex-col gap-6">
        {defendantList?.map((defendant) => (
          <div
            key={defendant.id}
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 space-y-6"
          >
            {/* Defendant Header */}
            <div className="flex items-center justify-between">
              <h5 className="font-medium text-[#060216]">
                {defendant.defendant_type === "COMPANY"
                  ? defendant.company_entity || "Unnamed Company"
                  : `${defendant.first_name || ""} ${
                      defendant.last_name || ""
                    }`.trim() || "—"}
              </h5>
              <div className="flex items-center gap-2">
                <CreateUpdateDefendant
                  isEditing={true}
                  caseId={caseId}
                  formData={defendant}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={() => {
                    setSelectedDefendantId(defendant.id);
                    setDeleteDefendantDialogOpen(true);
                  }}
                >
                  <Trash className="h-4 w-4" />
                </Button>
                <ManageEmailTemplate 
                  caseId={caseId}
                  defendantId={defendant.id.toString()}
                  contextType={"defendant" as TemplateContextType}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    router.push(`/defendant/${caseId}/${defendant.id}`)
                  }
                >
                  <ExternalLink className="h-4 w-4 text-gray-600" />
                </Button>
              </div>
            </div>

            {/* Personal Information */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <InfoField
                icon={UserCog}
                label="type"
                value={defendant.defendant_type
                  .split("_")
                  .join(" ")
                  .toLowerCase()}
              />
              {defendant.registered_agent && (
                <InfoField
                  icon={User}
                  label="registered agent"
                  value={defendant.registered_agent}
                />
              )}
              {defendant.ssn && (
                <InfoField icon={Hash} label="ssn" value={defendant.ssn} />
              )}
              {defendant.company_entity && (
                <InfoField
                  icon={Building2}
                  label="company/entity"
                  value={defendant.company_entity}
                />
              )}
              {defendant.date_of_birth && (
                <InfoField
                  icon={Calendar}
                  label="date of birth"
                  value={
                    defendant.date_of_birth
                      ? formatDateForDisplay(defendant.date_of_birth)
                      : "-"
                  }
                />
              )}
              {defendant.gender && (
                <InfoField
                  icon={Users}
                  label="gender"
                  value={defendant.gender.toLowerCase()}
                />
              )}
              {defendant.language && (
                <InfoField
                  icon={Languages}
                  label="language"
                  value={defendant.language.toLowerCase()}
                />
              )}
              {defendant.race && (
                <InfoField
                  icon={BadgeInfo}
                  label="race"
                  value={defendant.race.toLowerCase()}
                />
              )}
              {defendant.drivers_license && (
                <InfoField
                  icon={Car}
                  label="driver's license"
                  value={`${defendant.drivers_license.state} - ${defendant.drivers_license.number}`}
                />
              )}
              {defendant.email && (
                <InfoField
                  icon={Mail}
                  label="email"
                  value={
                    <a
                      href={`mailto:${defendant.email}`}
                      className="text-[#060216]-600 hover:underline"
                    >
                      {defendant.email}
                    </a>
                  }
                />
              )}
              {defendant.phone && (
                <InfoField
                  icon={Phone}
                  label="phone"
                  value={
                    <a
                      href={`tel:${defendant.phone.replace(/\D/g, "")}`}
                      className="text-[#060216]-600 hover:underline"
                    >
                      {defendant.phone}
                      {defendant.phone_ext
                        ? ` ext. ${defendant.phone_ext}`
                        : ""}
                    </a>
                  }
                />
              )}
              {defendant.cell && (
                <InfoField
                  icon={Smartphone}
                  label="cell"
                  value={
                    <a
                      href={`tel:${defendant.cell.replace(/\D/g, "")}`}
                      className="text-[#060216]-600 hover:underline"
                    >
                      {defendant.cell}
                    </a>
                  }
                />
              )}
              {defendant.fax && (
                <InfoField icon={Printer} label="fax" value={defendant.fax} />
              )}
              {(defendant.street1 || defendant.city) && (
                <InfoField
                  icon={() => (
                    <AddressLink
                      address={{
                        street1: defendant.street1 || undefined,
                        street2: defendant.street2 || undefined,
                        city: defendant.city || undefined,
                        state: defendant.state || undefined,
                        zip_code: defendant.zip_code || undefined,
                      }}
                    >
                      <MapPin className="h-4 w-4 text-[#060216]/50 hover:text-[#1C7B35]" />
                    </AddressLink>
                  )}
                  label="address"
                  value={`${defendant.street1}${
                    defendant.street2 ? `, ${defendant.street2}` : ""
                  }, ${defendant.city}, ${defendant.state} ${
                    defendant.zip_code
                  }`}
                />
              )}
            </div>

            {/* Insurance Information */}
            {defendant.insurances.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-[#060216]" />
                  <h4 className="text-[22px] font-bold font-Manrope text-[#060216]">
                    Insurance Details
                  </h4>
                </div>
                <div className="space-y-4">
                  {defendant.insurances.map((insurance) => (
                    <InsuranceCard
                      key={insurance.id}
                      insurance={insurance}
                      caseId={caseId}
                      defendantId={defendant.id.toString()}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}

        {(!defendantList || defendantList.length === 0) && (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-gray-400" />
              <p className="text-sm text-[#060216]">
                No defendants found. Click the &quot;Add Defendant&quot; button
                to add one.
              </p>
            </div>
          </div>
        )}
      </div>

      <DeleteConfirmationDialog
        open={deleteDefendantDialogOpen}
        onOpenChange={setDeleteDefendantDialogOpen}
        text="Defendant"
        onConfirm={handleDelete}
      />
    </div>
  );
}
