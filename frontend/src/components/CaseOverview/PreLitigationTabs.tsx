import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { DemandValidation } from "@/components/CaseOverview/demandValidation";
import { Button } from "@/components/ui/button";
import { Download, Save, Cloud, Loader2, Maximize2, Minimize2 } from "lucide-react";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useUpdateDocumentData, useCreateDocument, useSaveDocInS3, useDocDataSections } from '@/services/reasoningAIService';
import { setDocumentData, setLoading, setError } from '@/store/slices/reasoningAISlice';
import { useCaseDocumentsCategories } from '@/services/documentService';
import { DemandReview } from "@/components/CaseOverview/DemandReview";
import { DemandGeneration } from "@/components/CaseOverview/DemandGeneration";
import Negotiation from "./Negotiation";
import { AntiColossus } from "./antiColossus";
import { useSearchParams, useRouter } from "next/navigation";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

export function PreLitigationTabs({ caseId }: { caseId: string }) {
  const dispatch = useDispatch();
  const [isDownloading, setIsDownloading] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [fileName, setFileName] = useState(caseId);
  const [downloadFileName, setDownloadFileName] = useState(caseId);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [pendingDialog, setPendingDialog] = useState<'save' | 'download' | null>(null);
  const [pendingFileName, setPendingFileName] = useState('');
console.log(`☄️☄️ pendingFileName ----------------------->☄️☄️`, pendingFileName);
  const { toast } = useToast();
  const user = useSelector((state: RootState) => state.auth.user);
  const documentData = useSelector((state: RootState) => state.reasoningAI.documentData);

  const { mutate: updateDocumentData, isPending: isSaving } = useUpdateDocumentData();
  const { mutate: createDocument } = useCreateDocument();
  const { mutate: saveToS3, isPending: isSavingToS3 } = useSaveDocInS3();
  const { data: docSections, isLoading, error } = useDocDataSections(caseId);
  const { data: documentCategories } = useCaseDocumentsCategories(caseId);

  const searchParams = useSearchParams();
  const router = useRouter();
  const initialTab = searchParams?.get("preLitigationTab") || "demand-validation";
  const initialInnerTab = searchParams?.get("demandTab") || "generate";

  useEffect(() => {
    dispatch(setLoading(isLoading));

    if (docSections) {
      dispatch(setDocumentData(docSections));
    }

    if (error) {
      dispatch(setError((error as Error).message));
      dispatch(setDocumentData(null));
    }
  }, [docSections, isLoading, error, dispatch]);

  const handleSaveProgress = () => {
    if (documentData) {
      updateDocumentData({ caseId, documentData });
    }
  };

  const handleDownload = async (customFileName: string) => {
    if (!documentData || isDownloading || !documentCategories) return;

    setIsDownloading(true);

    try {
      updateDocumentData({ caseId, documentData });

      createDocument(
        {
          caseId,
          organization: user?.organization?.name || '',
          fileName: customFileName,
          documentWithCategories: documentCategories
        },
        {
          onSuccess: (blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${customFileName}.docx`;
            link.click();
            window.URL.revokeObjectURL(url);

            toast({
              title: 'Document downloaded successfully',
            });
            setShowDownloadModal(false);
          },
          onError: (error) => {
            console.error('Error creating document:', error);
            toast({
              title: 'Error creating document',
              description: 'Please try again.',
              variant: 'destructive'
            });
          },
          onSettled: () => {
            setIsDownloading(false);
          }
        }
      );
    } catch (error) {
      console.error('Error downloading document:', error);
      setIsDownloading(false);
    }
  };

  const handleSaveToCloud = () => {
    if (!fileName) return;

    saveToS3(
      {
        caseId,
        saveGeneratedDocInS3: true,
        fileName,
        organization: user?.organization?.name || '',
        documentWithCategories: documentCategories || []
      },
      {
        onSuccess: () => {
          toast({
            title: 'Document saved to cloud successfully',
            description: `File saved as: ${fileName}`,
          });
          setShowSaveModal(false);
        },
        onError: () => {
          toast({
            title: 'Error saving to cloud',
            description: 'Please try again.',
            variant: 'destructive'
          });
        }
      }
    );
  };

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleOpenChange = (dialog: 'save' | 'download', newOpen: boolean) => {
    const isDirty = (dialog === 'save' && fileName !== caseId) || 
                   (dialog === 'download' && downloadFileName !== caseId);
    
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
      setPendingDialog(dialog);
    } else {
      if (!newOpen) {
        handleClose(dialog);
      } else {
        if (dialog === 'save') {
          setShowSaveModal(true);
          setPendingFileName(fileName);
        } else {
          setShowDownloadModal(true);
          setPendingFileName(downloadFileName);
        }
      }
    }
  };

  const handleClose = (dialog: 'save' | 'download') => {
    if (dialog === 'save') {
      setShowSaveModal(false);
      setFileName(caseId);
    } else {
      setShowDownloadModal(false);
      setDownloadFileName(caseId);
    }
    setPendingDialog(null);
    setPendingFileName('');
  };

  const handleCancelClick = (dialog: 'save' | 'download') => {
    const isDirty = (dialog === 'save' && fileName !== caseId) || 
                   (dialog === 'download' && downloadFileName !== caseId);
    if (isDirty) {
      setShowUnsavedAlert(true);
      setPendingDialog(dialog);
    } else {
      handleClose(dialog);
    }
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    if (pendingDialog) {
      handleClose(pendingDialog);
    }
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleTabChange = (value: string) => {
    if (!searchParams) return;
    const newParams = new URLSearchParams(searchParams.toString());
    // Only preserve mainTab and preLitigationTab
    const mainTab = searchParams.get("mainTab");
    newParams.delete("overviewTab");
    newParams.delete("litigationTab");
    newParams.delete("settlementTab");
    if (mainTab) newParams.set("mainTab", mainTab);
    newParams.set("preLitigationTab", value);
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });
  };

  return (
    <div className="w-full">
      <Tabs 
        defaultValue={initialTab} 
        className="w-full"
        onValueChange={handleTabChange}
      >
        <TabsList>
          <TabsTrigger value="demand-validation">
            Demand Letter Validation
          </TabsTrigger>
          <TabsTrigger value="demand-generator">Demand Generator</TabsTrigger>
          {/* <TabsTrigger value="anti-colossus">Anti Colossus</TabsTrigger>*/}
          <TabsTrigger value="negotiations">Negotiations</TabsTrigger> 
          <TabsTrigger value="anti-colossus">Anti Colossus</TabsTrigger>
        </TabsList>

        <TabsContent value="demand-validation">
          <DemandValidation caseId={caseId} />
        </TabsContent>

        <TabsContent value="demand-generator">
          <div className="h-full">
            <h1 className="text-xl font-semibold mb-4">Demand Generator</h1>
            <h4 className="text-sm text-gray-500 mb-2">Generate and review demands for your case</h4>

            <Tabs 
              defaultValue={initialInnerTab} 
              className="w-full"
              onValueChange={(value) => handleTabChange(value)}
            >
              <TabsList className="grid w-full grid-cols-2 bg-white">
                <TabsTrigger value="generate" className="bg-white border-b-2 border-transparent data-[state=active]:bg-green-50 data-[state=active]:border-green-500 data-[state=active]:shadow-none rounded-b-none">Request Generation</TabsTrigger>
                <TabsTrigger value="review" className="bg-white border-b-2 border-transparent data-[state=active]:bg-green-50 data-[state=active]:border-green-500 data-[state=active]:shadow-none rounded-b-none">View Demand Letter</TabsTrigger>
              </TabsList>
              <TabsContent value="generate">
                <DemandGeneration caseId={caseId} />
              </TabsContent>
              <TabsContent value="review" className={isFullscreen ? "fixed inset-0 bg-white z-50 p-4" : ""}>
                <div className="flex justify-between items-center mb-4">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleFullscreen}
                    className="rounded-full"
                  >
                    {isFullscreen ? (
                      <Minimize2 className="h-4 w-4" />
                    ) : (
                      <Maximize2 className="h-4 w-4" />
                    )}
                  </Button>
                  <div className="flex gap-3">
                    <Button
                      variant="default"
                      className="flex items-center gap-2 bg-gray-500 text-white hover:bg-gray-600 rounded-full"
                      onClick={handleSaveProgress}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      <span>{isSaving ? 'Saving...' : 'Save progress'}</span>
                    </Button>
                    <Button
                      variant="default"
                      className="flex items-center gap-2 bg-blue-500 text-white hover:bg-blue-600 rounded-full"
                      onClick={() => setShowSaveModal(true)}
                    >
                      <Cloud className="h-4 w-4" />
                      <span>Save to cloud</span>
                    </Button>
                    <Button
                      variant="default"
                      className="flex items-center gap-2 bg-green-500 text-white hover:bg-green-600 rounded-full"
                      onClick={() => setShowDownloadModal(true)}
                      disabled={isDownloading}
                    >
                      {isDownloading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Download className="h-4 w-4" />
                      )}
                      <span>{isDownloading ? 'Downloading...' : 'Download'}</span>
                    </Button>
                  </div>
                </div>
                {!documentData ? (
                  <div className="flex flex-col items-center justify-center h-[400px] text-gray-500">
                    <p className="text-lg font-medium">No data available</p>
                    <p className="text-sm">Please run analysis first in the Request Generation tab</p>
                  </div>
                ) : (
                  <DemandReview isFullscreen={isFullscreen} />
                )}
              </TabsContent>
            </Tabs>
          </div>
        </TabsContent>
        {/* <TabsContent value="anti-colossus">
          <AntiColossus caseId={caseId} />
        </TabsContent>*/}
        <TabsContent value="negotiations">
          <Negotiation caseId={caseId} />
        </TabsContent> 
        <TabsContent value="anti-colossus">
          <AntiColossus caseId={caseId} />
        </TabsContent>
      </Tabs>

      <Dialog open={showSaveModal} onOpenChange={(open) => handleOpenChange('save', open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Document to Cloud</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              placeholder="Enter file name"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleCancelClick('save')}>
              Cancel
            </Button>
            <Button onClick={handleSaveToCloud} disabled={isSavingToS3 || !fileName}>
              {isSavingToS3 ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showDownloadModal} onOpenChange={(open) => handleOpenChange('download', open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Download Document</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={downloadFileName}
              onChange={(e) => setDownloadFileName(e.target.value)}
              placeholder="Enter file name"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleCancelClick('download')}>
              Cancel
            </Button>
            <Button
              onClick={() => handleDownload(downloadFileName)}
              disabled={isDownloading || !downloadFileName}
            >
              {isDownloading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Downloading...
                </>
              ) : (
                'Download'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </div>
  );
}
