import { useFolderFiles } from '@/services/documentService';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button"
import { useState } from 'react';
import { useAntiColossusQuestions } from '@/services/caseService';
import { FileList } from './FileList';
import { useDispatch } from 'react-redux';
import { addMessage } from '@/store/slices/caseChatSlice';

export function AntiColossus({ caseId }: { caseId: string }) {
    const router = useRouter();
    const { data: files, isLoading, error } = useFolderFiles(caseId, 'DEMAND_LETTER');

    const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const antiColossusQuestions = useAntiColossusQuestions();
    const dispatch = useDispatch();

    const isFormValid = (): boolean => {
        return !!(
            selectedFiles.length > 0 &&
            !isSubmitting
        );
    };

    const handleUpload = () => {
        router.push(`/dashboard/case/${caseId}/setup?step=6`);
    };

    const handleGenerateQuestions = async () => {
        setIsSubmitting(true);
        try {
            const response = await antiColossusQuestions.mutateAsync({
                case_id: caseId,
                demand_letter_filenames: selectedFiles.join(';'),
                user_prompt: ''
            });
            
            dispatch(addMessage({
                caseId,
                message: {
                    role: 'assistant',
                    content: response,
                    contextFiles: []
                }
            }));
            
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleFound = () => {
        if (error || !files || files.length === 0) {
            return (
                <div className="flex justify-center items-center  bg-[#F7FAFC] rounded-xl border-2 border-dashed border-[#E2E8F0] m-1">
                    <div className="text-center p-3">
                        <i className="fas fa-file-upload text-[#A0AEC0] mb-4"></i>
                        <h3 className="text-lg text-[#2D3748] font-semibold mb-2">Missing Demand Letters</h3>
                        <p className="text-[#4A5568] text-md mb-2">No demand letters found in this case.</p>
                        <p className="text-[#718096] text-sm mb-6">Please upload demand letters to generate discovery questions.</p>
                        <button
                            onClick={handleUpload}
                            className="inline-flex items-center gap-2 px-6 py-3 bg-[#4299E1] text-white rounded-lg text-sm font-medium hover:bg-[#3182CE] transition-all duration-200"
                        >
                            <i className="fas fa-upload text-base"></i>
                            Upload Documents
                        </button>
                    </div>
                </div>
            );
        }

        return (
            <FileList 
                files={files}
                selectedFiles={selectedFiles}
                setSelectedFiles={setSelectedFiles}
            />
        );
    };

    if (isLoading) {
        return (
            <div className="p-2 flex justify-center">
                <LoadingSpinner />
            </div>
        );
    }

    return (
        <div className="p-2">
            <h2 className="text-xl font-semibold">Anti-Colossus Workflow</h2>
            <div className="bg-white py-4">
                <h4 className="text-lg font-semibold mb-2">Select Demand Letter</h4>
                {handleFound()}
                <hr className="my-4 border-t border-gray-200" />
                <Button
                    className={'w-full'}
                    variant="secondary"
                    disabled={!isFormValid()}
                    onClick={handleGenerateQuestions}
                >
                    {isSubmitting ? (
                        <LoadingSpinner className="mr-2 h-4 w-4" />
                    ) : (
                        'Generate Questions'
                    )}
                </Button>
            </div>
        </div>
    );
}
