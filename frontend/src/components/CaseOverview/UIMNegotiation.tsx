import React, { useState, useEffect, useMemo, useCallback, Fragment, FC } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { DollarSign, Calendar, ChevronDown, AlertCircle } from 'lucide-react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { CreateDemandDialog } from '@/components/CaseOverview/CreateDemandDialog';
import { useUIMNegotiationsQuery, useUpdateUIMNegotiationMutation, useDeleteUIMNegotiationMutation } from '@/services/negotiationService';
import { NegotiationType, NegotiationUIMRead } from '@/type/negotiationTypes';
import { useClientInsuranceListQuery } from '@/services/case-management/clientDetailService';
import { useSettlementCalculationQuery, useUpdateSettlementCalculationMutation } from '@/services/case-management/settlementService';
import { CaseTasks } from '../CaseTasks/CaseTasks';
import { CaseNotes } from '../CaseNotes/CaseNotes';
import { formatDateForDisplay, formatDateForApi } from '@/utils/dateUtils';
import { DeleteConfirmationDialog } from '@/components/common/DeleteConfirmationDialog';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useQueryClient } from '@tanstack/react-query';
import {
    CLIENT_TRUST_KEYS,
    useCreateClientTrustMutation,
    useUpdateClientTrustMutation,
} from "@/services/case-management/clientTrustService";
import {
    ClientTrustEntryType,
    DepositType,
    ClientTrust
} from "@/type/case-management/clientTrustTypes";
import { toast } from '@/hooks/use-toast';
import { useClientTrustQuery } from '@/services/case-management/caseAnalyticsService';
import { CaseReportFilters, ClientTrustRow } from '@/type/case-management/caseAnalyticsTypes';
import { NegotiationTable, NegotiationTableType } from './components/negotiation/Table';
import { ParentOfferDemand } from './components/negotiation/parentOfferDemand';
import NegotiationCards from './components/negotiation/negotiationcards';
import KeyInformation, { AdjusterInfo, InsuranceCompanyInfo } from './components/negotiation/KeyInformation';
import FeePercentageSelect from './components/negotiation/FeePercentageSelect';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Define local interfaces
interface NegotiationProps {
    caseId: string;
}

interface OrganizedNegotiation {
    parent: NegotiationUIMRead;
    children: NegotiationUIMRead[];
}

// Add form schema for settlement check dialog
const settlementCheckSchema = z.object({
    check_number: z.string().optional(),
});

type SettlementCheckFormData = z.infer<typeof settlementCheckSchema>;

// Add SettlementCheckDialog component
const SettlementCheckDialog = ({
    isOpen,
    onClose,
    onSubmit,
    negotiation,
}: {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (checkNumber: string) => void;
    negotiation: NegotiationUIMRead | null;
}) => {
    const form = useForm<SettlementCheckFormData>({
        resolver: zodResolver(settlementCheckSchema),
        defaultValues: {
            check_number: "",
        },
    });

    const handleSubmit = (values: SettlementCheckFormData) => {
        onSubmit(values.check_number || "");
        form.reset();
    };

    if (!negotiation) return null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Create UIM Settlement Check</DialogTitle>
                </DialogHeader>
                <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="check_number">Check Number (Optional)</Label>
                        <Input
                            id="check_number"
                            {...form.register("check_number")}
                            placeholder="Enter check number"
                        />
                    </div>
                    <div className="space-y-2">
                        <Label>Amount</Label>
                        <div className="text-sm text-gray-500">
                            ${Number(negotiation.amount).toLocaleString()}
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label>Insurance Company</Label>
                        <div className="text-sm text-gray-500">
                            {negotiation.client_insurance?.insurance_company?.name}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="button" variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button type="submit">Create Settlement Check</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
};

const UIMNegotiation: FC<NegotiationProps> = ({ caseId }) => {
    const queryClient = useQueryClient();
    const createClientTrust = useCreateClientTrustMutation(queryClient);
    const { data: clientTrustData } = useClientTrustQuery({ case_id: caseId } as CaseReportFilters);
    const updateClientTrust = useUpdateClientTrustMutation(queryClient);

    const { data: clientInsuranceList } = useClientInsuranceListQuery(caseId);
    const [selectedClientInsurance, setSelectedClientInsurance] = useState<number | null>(null);
    const [editDemand, setEditDemand] = useState<NegotiationUIMRead | null>(null);
    const [currentNegotiationId, setCurrentNegotiationId] = useState<number>(0);
    const [currentDeleteId, setCurrentDeleteId] = useState<number>(0);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [showSettlementDialog, setShowSettlementDialog] = useState(false);
    const [selectedNegotiation, setSelectedNegotiation] = useState<NegotiationUIMRead | null>(null);

    const { data: UIMNegotiations, refetch } = useUIMNegotiationsQuery(
        caseId,
        selectedClientInsurance
    );

    // Add useEffect to set first insurance as selected
    useEffect(() => {
        if (clientInsuranceList && clientInsuranceList.length > 0 && !selectedClientInsurance) {
            setSelectedClientInsurance(clientInsuranceList[0].id);
        }
    }, [clientInsuranceList, selectedClientInsurance]);

    const deleteUIMNegotiationMutation = useDeleteUIMNegotiationMutation(caseId, currentDeleteId.toString());

    // Get the adjuster ID from the latest negotiation
    const latestAdjusterId = useMemo(() => {
        if (!UIMNegotiations?.length) return null;
        return UIMNegotiations[0]?.adjuster?.id || null;
    }, [UIMNegotiations]);

    // Refetch negotiations when insurance changes
    useEffect(() => {
        if (selectedClientInsurance) {
            refetch();
        }
    }, [selectedClientInsurance, refetch]);

    // useEffect(() => {
    //     if (!selectedClientInsurance && clientInsuranceList && clientInsuranceList.length > 0) {
    //         console.log(clientInsuranceList[0].id);
    //         setSelectedClientInsurance(clientInsuranceList[0].id);
    //     }
    // }, [clientInsuranceList, selectedClientInsurance]);

    const updateNegotiationMutation = useUpdateUIMNegotiationMutation(caseId, currentNegotiationId);

    // Function to check if there's an accepted offer for a specific insurance company
    const hasAcceptedOfferForInsurance = useCallback((insuranceId: number | null) => {
        if (!insuranceId) return false;
        return UIMNegotiations?.some(n =>
            n.type === NegotiationType.ACCEPTED_OFFER &&
            n.client_insurance?.id === insuranceId
        ) ?? false;
    }, [UIMNegotiations]);

    // Check if the currently selected insurance has an accepted offer
    const hasAcceptedOfferForSelectedInsurance = useMemo(() => {
        return hasAcceptedOfferForInsurance(selectedClientInsurance);
    }, [hasAcceptedOfferForInsurance, selectedClientInsurance]);

    const handleAcceptOffer = async (negotiationId: number) => {
        try {
            setCurrentNegotiationId(negotiationId);
            if (updateNegotiationMutation && negotiationId !== 0) {
                await updateNegotiationMutation.mutateAsync({
                    type: NegotiationType.ACCEPTED_OFFER
                });

                // Find the negotiation that was just accepted
                const acceptedNegotiation = UIMNegotiations?.find(n => n.id === negotiationId);
                if (acceptedNegotiation?.amount && acceptedNegotiation?.client_insurance?.insurance_company?.name) {
                    // Create client trust entry for the accepted offer
                    await createClientTrust.mutateAsync({
                        caseId,
                        trustData: {
                            check_number: "",
                            amount: acceptedNegotiation.amount.toString(),
                            memo: `UIM Settlement payment from ${acceptedNegotiation.client_insurance.insurance_company.name}`,
                            issuer_payee: acceptedNegotiation.client_insurance.insurance_company.name,
                            deposit_date: formatDateForApi(new Date()),
                            client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
                            deposit_type: DepositType.UIM,
                            negotiation_id: acceptedNegotiation.id
                        }
                    });
                }

                // Refetch data after successful update
                await refetch();
                toast({
                    title: 'Success',
                    description: 'Offer accepted successfully',
                });
            }
        } catch (error) {
            console.error('Error accepting offer:', error);
            toast({
                title: 'Error',
                description: 'Failed to accept offer',
                variant: 'destructive',
            });
        }
    };

    const handleDeacceptOffer = async (negotiationId: number) => {
        try {
            setCurrentNegotiationId(negotiationId);
            if (updateNegotiationMutation && negotiationId !== 0) {
                await updateNegotiationMutation.mutateAsync({
                    type: NegotiationType.INITIAL_DEMAND
                });
                // Refetch data after successful update
                await refetch();
                toast({
                    title: 'Success',
                    description: 'Offer unaccepted successfully',
                });
            }
        } catch (error) {
            console.error('Error deaccepting offer:', error);
            toast({
                title: 'Error',
                description: 'Failed to unaccept offer',
                variant: 'destructive',
            });
        }
    };

    const latestAdjuster = useMemo(() => {
        if (!UIMNegotiations?.length) return null;
        return UIMNegotiations[0]?.adjuster;
    }, [UIMNegotiations]);

    // For collapsible sections
    const [expandedDays, setExpandedDays] = useState<string[]>([]);

    const toggleDay = (id: string) => {
        setExpandedDays(prev =>
            prev.includes(id)
                ? prev.filter(d => d !== id)
                : [...prev, id]
        );
    };

    // Handle edit complete with refetch
    const handleEditComplete = () => {
        setEditDemand(null);
        setTimeout(() => {
            refetch();
        }, 100);
    };

    // Add a showEditDialog state
    const [showEditDialog, setShowEditDialog] = useState(false);

    // Helper function to organize negotiations into parent-child structure
    const organizeNegotiations = (negotiations: NegotiationUIMRead[] | undefined): OrganizedNegotiation[] => {
        if (!negotiations) return [];

        const parentNegotiations = negotiations.filter(item => item.previous_offer === null);

        return parentNegotiations.map(parent => {
            const children = negotiations.filter(item => item.previous_offer === parent.id);
            return {
                parent,
                children: children.sort((a, b) =>
                    new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
                )
            };
        }).sort((a, b) =>
            new Date(b.parent.created_at).getTime() - new Date(a.parent.created_at).getTime()
        );
    };

    // Use the organized negotiations instead of the previous grouping logic
    const organizedNegotiations = useMemo(() => {
        return organizeNegotiations(UIMNegotiations);
    }, [UIMNegotiations]);

    // Update the useEffect to use organizedNegotiations
    useEffect(() => {
        if (organizedNegotiations.length > 0) {
            // Expand all negotiations by default
            const allNegotiationIds = organizedNegotiations.map(item => item.parent.id.toString());
            setExpandedDays(allNegotiationIds);
        }
    }, [organizedNegotiations]);

    // Calculate quick stats for the UI
    const quickStats = useMemo(() => {
        const filteredNegotiations = UIMNegotiations?.filter(n =>
            n.client_insurance?.id === selectedClientInsurance
        ) || [];

        const initialDemand = filteredNegotiations.find(n => n.type === NegotiationType.INITIAL_DEMAND);
        const latestOffer = [...filteredNegotiations]
            .filter(n => n.type !== NegotiationType.INITIAL_DEMAND && n.type !== NegotiationType.ACCEPTED_OFFER)
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
        const acceptedOffer = filteredNegotiations.find(n => n.type === NegotiationType.ACCEPTED_OFFER);

        const upcomingDeadlines = filteredNegotiations
            .filter(n => n.response_deadline && new Date(n.response_deadline) >= new Date())
            .sort((a, b) => new Date(a.response_deadline!).getTime() - new Date(b.response_deadline!).getTime());

        return {
            demandAmount: initialDemand?.amount || 0,
            latestOffer: latestOffer?.amount || 0,
            acceptedOffer: acceptedOffer?.amount || 0,
            nextDeadline: upcomingDeadlines.length > 0 ? upcomingDeadlines[0].response_deadline : null
        };
    }, [UIMNegotiations, selectedClientInsurance]);

    const selectedInsuranceDetails = useMemo(() => {
        if (!clientInsuranceList || !selectedClientInsurance) return null;
        return clientInsuranceList.find(insurance => insurance.id === selectedClientInsurance);
    }, [clientInsuranceList, selectedClientInsurance]);

    console.log(selectedInsuranceDetails);

    const handleDelete = async (negotiationId: number) => {
        setCurrentDeleteId(negotiationId);
        setShowDeleteDialog(true);
    };

    const handleConfirmDelete = async () => {
        await deleteUIMNegotiationMutation.mutateAsync();
        setShowDeleteDialog(false);
        refetch();
    };

    const { data: calculations } = useSettlementCalculationQuery(caseId);
    const updateSettlementCalculation = useUpdateSettlementCalculationMutation(caseId);

    const handleFeePercentageChange = (value: string) => {
        // Only call the API if the value is valid
        if (value && !isNaN(parseFloat(value))) {
            // Format to exactly 2 decimal places
            const formattedValue = parseFloat(value).toFixed(2);
            updateSettlementCalculation.mutate({
                fees_percentage: formattedValue,
            });
        }
    };

    const hasArchivedDemand = useMemo(() => {
        return UIMNegotiations?.some(n => n.is_archived) ?? false;
    }, [UIMNegotiations]);

    const handleArchiveToggle = async (negotiationId: number, isArchived: boolean) => {
        try {
            setCurrentNegotiationId(negotiationId);
            if (updateNegotiationMutation && negotiationId !== 0) {
                await updateNegotiationMutation.mutateAsync({
                    is_archived: isArchived
                });
                await refetch();
            }
        } catch (error) {
            console.error('Error archiving demand:', error);
        }
    };

    const handleSettlementCheck = async (negotiation: NegotiationUIMRead) => {
        console.log("negotiation ---> uim", negotiation);
        if (!negotiation.amount || !negotiation?.insurance_company?.name) {
            toast({
                title: 'Error',
                description: 'Negotiation amount or insurance company name is missing',
                variant: 'destructive',
            });
            return;
        }

        setSelectedNegotiation(negotiation);
        setShowSettlementDialog(true);
    };

    const handleSettlementSubmit = async (checkNumber: string) => {
        if (!selectedNegotiation) return;

        try {
            await createClientTrust.mutateAsync({
                caseId,
                trustData: {
                    check_number: checkNumber,
                    amount: selectedNegotiation.amount.toString(),
                    memo: `UIM Settlement payment from ${selectedNegotiation.client_insurance.insurance_company.name}`,
                    issuer_payee: selectedNegotiation.insurance_company.name,
                    deposit_date: formatDateForApi(new Date()),
                    client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
                    deposit_type: DepositType.UIM,
                    negotiation_id: selectedNegotiation.id
                }
            });
            toast({
                title: 'Success',
                description: 'Settlement check created successfully',
            });
            setShowSettlementDialog(false);
            setSelectedNegotiation(null);
        } catch (error) {
            console.error('Error creating settlement check:', error);
            toast({
                title: 'Error',
                description: 'Failed to create settlement check',
                variant: 'destructive',
            });
        }
    };

    const checkSettlementStatus = (negotiation: NegotiationUIMRead): ClientTrust | null => {
        console.log("clientTrustData ---> uim", clientTrustData, negotiation);
        if (!clientTrustData || !negotiation.client_insurance?.insurance_company?.name || !negotiation.amount) return null;

        const settlementPayments = clientTrustData.results.filter((trust: ClientTrustRow) =>
            trust.client_trust_entry_type === ClientTrustEntryType.SETTLEMENT_PAYMENT &&
            !trust.void
        );
        const negotiationAmount = parseFloat(negotiation.amount);
        if (isNaN(negotiationAmount)) return null;

        const matchingTrust = settlementPayments.find(trust =>
            trust.issuer_payee === negotiation.client_insurance?.insurance_company?.name &&
            trust.amount === negotiationAmount &&
            trust.memo?.includes(negotiation.client_insurance?.insurance_company?.name) &&
            !trust.void
        );

        if (!matchingTrust) return null;

        // Convert ClientTrustRow to ClientTrust
        const amount = typeof matchingTrust.amount === 'number'
            ? matchingTrust.amount.toString()
            : String(matchingTrust.amount);

        return {
            id: parseInt(matchingTrust.id),
            check_number: matchingTrust.check_number || "",
            issuer_payee: matchingTrust.issuer_payee,
            memo: matchingTrust.memo || "",
            amount,
            deposit_date: matchingTrust.deposit_date || "",
            client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
            void: false,
            created_at: matchingTrust.created_at || ""
            // updated_at: matchingTrust.updated_at || ""
        };
    };

    const handleVoidSettlement = async (negotiation: NegotiationUIMRead) => {
        const matchingTrust = checkSettlementStatus(negotiation);
        if (!matchingTrust?.id) return;

        try {
            await updateClientTrust.mutateAsync({
                caseId,
                id: matchingTrust.id,
                trustData: {
                    check_number: matchingTrust.check_number,
                    issuer_payee: matchingTrust.issuer_payee,
                    memo: matchingTrust.memo,
                    amount: matchingTrust.amount,
                    deposit_date: matchingTrust.deposit_date,
                    client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
                    void: true
                }
            });
            queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(caseId) });
            toast({
                title: 'Success',
                description: 'Settlement check voided successfully',
            });
        } catch (error) {
            console.error('Error voiding settlement:', error);
            toast({
                title: 'Error',
                description: 'Failed to void settlement check',
                variant: 'destructive',
            });
        }
    };

    // Set the negotiation type to 'uim'
    const negotiationType: NegotiationTableType = 'uim';

    // If no insurance is selected, show the AllUIMView
    // if (selectedClientInsurance === null) {
    //     return (
    //         <AllUIMView
    //             caseId={caseId}
    //             onSelectInsurance={(insuranceId) => setSelectedClientInsurance(insuranceId)}
    //         />
    //     );
    // }

    return (
        <div className="w-full max-w-7xl mx-auto p-3 sm:p-4 space-y-4">
            {/* Header Section */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 bg-gradient-to-r from-slate-50 to-gray-50 p-3 sm:p-4 rounded-lg shadow-sm">
                <div className="flex flex-row justify-between items-center w-full md:w-auto gap-4">
                    <div className="flex flex-col">
                        <h1 className="text-xl sm:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-slate-900 to-slate-700">
                            UIM Negotiations
                        </h1>
                        <p className="text-slate-500 text-xs mt-0.5">Underinsured Motorist Coverage</p>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="outline"
                                className="h-9 min-w-[180px] justify-between bg-white hover:bg-slate-50 border-slate-200 text-sm"
                            >
                                {!clientInsuranceList ? (
                                    <LoadingSpinner />
                                ) : (
                                    <>
                                        {selectedClientInsurance && clientInsuranceList ?
                                            clientInsuranceList.find(i => i.id === selectedClientInsurance)?.insurance_company?.name || "Select Insurance"
                                            : "Select Insurance"}
                                        <ChevronDown className="h-4 w-4 opacity-50" />
                                    </>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[180px]">
                            {/* <DropdownMenuItem
                                onClick={() => setSelectedClientInsurance(null)}
                                className="cursor-pointer hover:bg-slate-50 text-sm"
                            >
                                All Insurances
                            </DropdownMenuItem> */}
                            {clientInsuranceList?.map((insurance) => (
                                <DropdownMenuItem
                                    key={insurance.id}
                                    onClick={() => setSelectedClientInsurance(insurance.id)}
                                    className="cursor-pointer hover:bg-slate-50 text-sm"
                                >
                                    {insurance.insurance_company?.name || "Unknown Insurance"}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
                <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span>
                                    <FeePercentageSelect
                                        value={
                                            calculations?.fees_percentage &&
                                                parseFloat(calculations.fees_percentage) > 0
                                                ? calculations.fees_percentage
                                                : "33.330000"
                                        }
                                        onChange={handleFeePercentageChange}
                                        disabled={hasAcceptedOfferForSelectedInsurance}
                                    />
                                </span>
                            </TooltipTrigger>
                            {hasAcceptedOfferForSelectedInsurance && (
                                <TooltipContent>
                                    <p>Unaccept the offer to modify attorney fee</p>
                                </TooltipContent>
                            )}
                        </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span>
                                    <CreateDemandDialog
                                        caseId={caseId}
                                        adjusterId={latestAdjusterId}
                                        disabled={!selectedClientInsurance || hasAcceptedOfferForSelectedInsurance}
                                        negotiations={UIMNegotiations}
                                        clientInsuranceId={selectedClientInsurance}
                                        isUIM={true}
                                        editDemand={editDemand}
                                        onEditComplete={handleEditComplete}
                                    />
                                </span>
                            </TooltipTrigger>
                            {hasAcceptedOfferForSelectedInsurance && (
                                <TooltipContent>
                                    <p>Unaccept offer to create new demand</p>
                                </TooltipContent>
                            )}
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>

            {/* Quick Stats */}
            <NegotiationCards
                data={[
                    {
                        icon: DollarSign,
                        title: "Current Demand",
                        metrics: {
                            primary: {
                                type: 'monetary',
                                value: quickStats.demandAmount || 0
                            }
                        },
                        bgColor: "bg-blue-50"
                    },
                    {
                        icon: DollarSign,
                        title: hasAcceptedOfferForSelectedInsurance ? "Accepted Offer" : "Latest Offer",
                        metrics: {
                            primary: {
                                type: 'monetary',
                                value: hasAcceptedOfferForSelectedInsurance ? quickStats.acceptedOffer || 0 : quickStats.latestOffer || 0
                            }
                        },
                        bgColor: hasAcceptedOfferForSelectedInsurance ? "bg-orange-50" : "bg-emerald-50"
                    },
                    {
                        icon: AlertCircle,
                        title: "Policy Limit",
                        metrics: {
                            primary: {
                                type: 'number',
                                value: !selectedClientInsurance ? 0 :
                                    !selectedInsuranceDetails ? 0 :
                                        selectedInsuranceDetails?.um_uim || 0
                            }
                        },
                        bgColor: "bg-amber-50"
                    },
                    {
                        icon: Calendar,
                        title: "Next Deadline",
                        metrics: {
                            primary: {
                                type: 'date',
                                value: quickStats.nextDeadline ? formatDateForDisplay(quickStats.nextDeadline) : '',
                                unit: 'days'
                            }
                        },
                        bgColor: "bg-rose-50"
                    }
                ]}
            />

            {/* Tabs Section */}
            <Tabs defaultValue="overview" className="w-full">
                <TabsList className="bg-slate-100 p-1 rounded-lg">
                    <TabsTrigger value="overview" className="data-[state=active]:bg-white rounded-md text-sm">
                        Overview
                    </TabsTrigger>
                    <TabsTrigger value="strategy" className="data-[state=active]:bg-white rounded-md text-sm">
                        Strategy
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="overview">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        {/* Negotiation Timeline */}
                        <Card className="col-span-2 border-none shadow-sm h-auto flex flex-col">
                            <CardContent className="p-0 flex-grow flex flex-col">
                                <div className="space-y-0">
                                    {organizedNegotiations.filter(item => item.parent.client_insurance.id === selectedClientInsurance).map(({ parent }) => {
                                        const negotiation = parent;
                                        const negotiationId = parent.id.toString();
                                        const isAcceptedOffer = negotiation.type === NegotiationType.ACCEPTED_OFFER;

                                        const adjusterName = negotiation.adjuster
                                            ? `${negotiation.adjuster.first_name} ${negotiation.adjuster.last_name}`
                                            : "";

                                        const insuranceCompanyName = selectedInsuranceDetails?.insurance_company?.name || "";

                                        const headerParts = [];

                                        if (insuranceCompanyName) {
                                            headerParts.push(insuranceCompanyName);
                                        }

                                        if (adjusterName) {
                                            headerParts.push(adjusterName);
                                        }

                                        if (negotiation.amount) {
                                            headerParts.push(`Amount $${Number(negotiation.amount).toLocaleString()}`);
                                        }

                                        if (negotiation.date_sent) {
                                            headerParts.push(`${negotiation.type === NegotiationType.INITIAL_DEMAND ? "Demand" : "Offer"} ${formatDateForDisplay(negotiation.date_sent)}`);
                                        }

                                        if (negotiation.response_deadline) {
                                            headerParts.push(`exp ${formatDateForDisplay(negotiation.response_deadline)}`);
                                        }

                                        if (calculations?.fees_percentage) {
                                            const feePercentage = calculations.fees_percentage === '0.00' ? '33.33' : Number(calculations.fees_percentage).toFixed(2);
                                            headerParts.push(`Attorney Fee ${feePercentage}%`);
                                        }

                                        const headerString = headerParts.join(' | ');

                                        return (
                                            <Fragment key={parent.id}>
                                                <ParentOfferDemand<NegotiationUIMRead>
                                                    parent={parent}
                                                    isExpanded={expandedDays.includes(negotiationId)}
                                                    toggleExpand={() => toggleDay(negotiationId)}
                                                    headerString={headerString}
                                                    caseId={caseId}
                                                    selectedDefendant={null}
                                                    hasAcceptedOffer={hasAcceptedOfferForInsurance(negotiation.client_insurance.id)}
                                                    isAcceptedOffer={isAcceptedOffer}
                                                    onDelete={handleDelete}
                                                    onAcceptOffer={handleAcceptOffer}
                                                    onDeacceptOffer={handleDeacceptOffer}
                                                    onSettlementCheck={handleSettlementCheck}
                                                    onEditDemand={(negotiation) => {
                                                        setEditDemand(negotiation);
                                                        setShowEditDialog(true);
                                                    }}
                                                    onArchiveToggle={handleArchiveToggle}
                                                    checkSettlementStatus={checkSettlementStatus}
                                                    handleVoidSettlement={handleVoidSettlement}
                                                    insuranceCompany={negotiation.insurance_company}
                                                />

                                                {expandedDays.includes(negotiationId) && (
                                                    <div>
                                                        <NegotiationTable<NegotiationUIMRead>
                                                            negotiations={UIMNegotiations || []}
                                                            parentId={negotiation.id}
                                                            caseId={caseId}
                                                            selectedDefendant={null}
                                                            onAcceptOffer={handleAcceptOffer}
                                                            onDeacceptOffer={handleDeacceptOffer}
                                                            hasAcceptedOffer={hasAcceptedOfferForInsurance(negotiation.client_insurance.id)}
                                                            onDelete={handleDelete}
                                                            parentIsArchived={negotiation.is_archived}
                                                            negotiationType={negotiationType}
                                                            parentInsuranceCompany={negotiation.insurance_company}
                                                            acceptedOfferId={UIMNegotiations?.find(n =>
                                                                n.type === NegotiationType.ACCEPTED_OFFER &&
                                                                n.client_insurance?.id === negotiation.client_insurance.id
                                                            )?.id}
                                                            checkSettlementStatus={checkSettlementStatus}
                                                            handleVoidSettlement={handleVoidSettlement}
                                                        />
                                                    </div>
                                                )}
                                            </Fragment>
                                        );
                                    })}
                                    {(!UIMNegotiations || UIMNegotiations.length === 0) && (
                                        <div className="text-center py-8 bg-white rounded-lg flex-grow">
                                            <p className="text-gray-500 text-sm">No UIM negotiations found.</p>
                                            <p className="text-xs text-gray-400 mt-1">Create a new demand to get started.</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Replace the Key Information card with the new KeyInformation component */}
                        <KeyInformation
                            negotiationType="uim"
                            selectedAdjuster={latestAdjuster as unknown as AdjusterInfo}
                            insuranceCompany={selectedInsuranceDetails?.insurance_company as unknown as InsuranceCompanyInfo}
                            policyLimit={selectedInsuranceDetails?.um_uim}
                        />
                    </div>
                </TabsContent>

                {/* Strategy Tab Content */}
                <TabsContent value="strategy">
                    <div className="mt-4">
                        <Tabs defaultValue="tasks" className="w-full">
                            <TabsList className="bg-slate-100 p-1 rounded-lg grid grid-cols-2">
                                <TabsTrigger value="tasks" className="data-[state=active]:bg-white rounded-md text-sm">
                                    Tasks
                                </TabsTrigger>
                                <TabsTrigger value="notes" className="data-[state=active]:bg-white rounded-md text-sm">
                                    Notes
                                </TabsTrigger>
                            </TabsList>

                            <TabsContent value="tasks">
                                <CaseTasks caseId={caseId} taskFor="Negotiations_UIM" />
                            </TabsContent>

                            <TabsContent value="notes">
                                <CaseNotes caseId={caseId} noteFor="Negotiations_UIM" />
                            </TabsContent>
                        </Tabs>
                    </div>
                </TabsContent>
            </Tabs>

            {/* Add this at the end of the component, before the closing div */}
            {editDemand && showEditDialog && (
                <CreateDemandDialog
                    key={`edit-${editDemand.id}-${showEditDialog}`}
                    caseId={caseId}
                    adjusterId={latestAdjusterId}
                    disabled={!selectedClientInsurance || (editDemand && hasAcceptedOfferForInsurance(editDemand.client_insurance.id)) || hasArchivedDemand}
                    negotiations={UIMNegotiations}
                    clientInsuranceId={selectedClientInsurance}
                    isUIM={true}
                    editDemand={editDemand}
                    onEditComplete={() => {
                        handleEditComplete();
                        setShowEditDialog(false);
                    }}
                    onClose={() => {
                        setShowEditDialog(false);
                        setEditDemand(null);
                    }}
                />
            )}

            {/* Add DeleteConfirmationDialog */}
            <DeleteConfirmationDialog
                open={showDeleteDialog}
                onOpenChange={setShowDeleteDialog}
                onConfirm={handleConfirmDelete}
                text="UIM Negotiation"
            />

            <SettlementCheckDialog
                isOpen={showSettlementDialog}
                onClose={() => {
                    setShowSettlementDialog(false);
                    setSelectedNegotiation(null);
                }}
                onSubmit={handleSettlementSubmit}
                negotiation={selectedNegotiation}
            />
        </div>
    );
};

export default UIMNegotiation;