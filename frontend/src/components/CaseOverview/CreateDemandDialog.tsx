import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { NegotiationType, NegotiationStatus, NegotiationWrite, NegotiationRead, NegotiationUIMRead, NegotiationUIMWrite } from "@/type/negotiationTypes";
import { useCreateNegotiationMutation, useCreateUIMNegotiationMutation, useDefendantAdjustersQuery, useUpdateNegotiationMutation, useUpdateUIMNegotiationMutation } from "@/services/negotiationService";
import { useInsuranceAdjusterDetailsQuery, useClientInsuranceListQuery } from '@/services/case-management/clientDetailService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormField, FormItem, FormControl, FormLabel, FormMessage } from "@/components/ui/form";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { AdjusterContact } from '@/type/case-management/commonTypes';
import { InsuranceAdjusterDetails, AdjusterDetail } from '@/type/case-management/insuranceTypes';
import { RequiredLabel } from '../ui/required-label';
import { formatDateForApi, formatDateForInput } from '@/utils/dateUtils';
import { useDefendantInsuranceListQuery } from "@/services/case-management/defendantService";
import { Plus } from 'lucide-react';

interface CreateDemandDialogProps {
  caseId: string;
  defendantId?: number | null;
  clientInsuranceId?: number | null;
  adjusterId: number | null;
  disabled: boolean;
  negotiations: NegotiationRead[] | NegotiationUIMRead[] | undefined;
  isUIM?: boolean;
  editDemand?: NegotiationRead | NegotiationUIMRead | null;
  onEditComplete?: () => void;
  onClose?: () => void;
}

const negotiationSchema = z.object({
  amount: z.string().min(1, "Amount is required"),
  type: z.nativeEnum(NegotiationType),
  date_sent: z.string().min(1, "Date sent is required"),
  response_deadline: z.string().min(1, "Response deadline is required"),
  response_received_date: z.string().optional(),
  extension_date: z.string().optional(),
  notes: z.string().optional(),
  settlement_terms: z.string().optional(),
  payment_terms: z.string().optional(),
  conditions: z.string().optional(),
  release_terms: z.string().optional(),
  adjuster: z.number().optional(),
  insurance_company: z.string().superRefine((val, ctx) => {
    if (!val) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Insurance company is required for Initial Demand"
      });
    }
  })
});

type NegotiationFormData = z.infer<typeof negotiationSchema>;

type AdjusterWithRoles = AdjusterDetail & { roles?: string[] };

const isAdjusterWithRoles = (adjuster: AdjusterContact | AdjusterWithRoles): adjuster is AdjusterWithRoles => {
  return 'roles' in adjuster;
};

export function CreateDemandDialog({ caseId, defendantId, clientInsuranceId, adjusterId, disabled, negotiations = [], isUIM = false, editDemand = null, onEditComplete, onClose }: CreateDemandDialogProps) {
  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const createMutation = useCreateNegotiationMutation(caseId);
  const createUIMMutation = useCreateUIMNegotiationMutation(caseId);
  const updateMutation = useUpdateNegotiationMutation(caseId, editDemand?.id || 0);
  const updateUIMutation = useUpdateUIMNegotiationMutation(caseId, editDemand?.id || 0);
  console.log(clientInsuranceId)

  // Track if we need to call onEditComplete after mutation finishes
  const [shouldCallEditComplete, setShouldCallEditComplete] = useState(false);

  const { data: defendantAdjusters, isLoading: isLoadingDefendantAdjusters } = useDefendantAdjustersQuery(
    caseId,
    defendantId?.toString(),
    true,
    {
      enabled: open && !isUIM && defendantId !== null && defendantId !== undefined
    }
  );

  // Add debugging for defendant adjusters
  useEffect(() => {
    if (defendantAdjusters && !isUIM) {
      console.log('Defendant Adjusters loaded:', defendantAdjusters.length);
      console.log('Adjuster Insurance Companies:', defendantAdjusters.map(adj => adj.insurance_company));
    }
  }, [defendantAdjusters, isUIM]);

  const [selectedClientInsurance, setSelectedClientInsurance] = useState<number | null>(null)

  const { data: clientInsuranceAdjusters, isLoading: isLoadingClientAdjusters, refetch: refetchAdjuster } = useInsuranceAdjusterDetailsQuery(
    caseId,
    selectedClientInsurance?.toString() || '',
    {
      // Only enable the query if we have a valid selectedClientInsurance
      enabled: open && isUIM && selectedClientInsurance !== null && selectedClientInsurance !== undefined && selectedClientInsurance > 0
    }
  );

  const { data: defendantInsurances, isLoading: isLoadingDefendantInsurances } = useDefendantInsuranceListQuery(
    caseId,
    defendantId?.toString() || '',
    {
      enabled: open && !isUIM && defendantId !== null && defendantId !== undefined
    }
  );

  const { data: clientInsuranceList, isLoading: isLoadingClientInsurances } = useClientInsuranceListQuery(
    caseId,
    {
      enabled: open && isUIM
    }
  );

  useEffect(() => { refetchAdjuster() }, [selectedClientInsurance])

  const getUniqueAdjusters = (clientInsuranceAdjusters: InsuranceAdjusterDetails | undefined | null): (AdjusterDetail & { roles?: string[] })[] => {
    if (!clientInsuranceAdjusters) return [];

    const adjusterMap = new Map<number, AdjusterDetail & { roles?: string[] }>();

    const addAdjuster = (adjuster: AdjusterDetail | null, role: string) => {
      if (!adjuster?.id) return;

      const key = adjuster.id;
      if (adjusterMap.has(key)) {
        const existing = adjusterMap.get(key)!;
        existing.roles = [...(existing.roles || []), role];
      } else {
        adjusterMap.set(key, {
          ...adjuster,
          roles: [role]
        });
      }
    };

    // Add all adjusters with their roles
    addAdjuster(clientInsuranceAdjusters.bodily_injury, 'Bodily Injury');
    addAdjuster(clientInsuranceAdjusters.bi_supervisor, 'BI Supervisor');
    addAdjuster(clientInsuranceAdjusters.medpay_pip, 'Medpay/PIP');
    addAdjuster(clientInsuranceAdjusters.medpay_pip_supervisor, 'Medpay Supervisor');
    addAdjuster(clientInsuranceAdjusters.property_damage, 'Property Damage');
    addAdjuster(clientInsuranceAdjusters.pd_supervisor, 'PD Supervisor');

    return Array.from(adjusterMap.values());
  };

  // Define a type for insurance company entries
  type InsuranceCompanyEntry = {
    id: number | undefined;
    name: string;
    claim_number: string;
    insurance_id: number;
    index: number;
  };

  const insuranceCompanies = useMemo(() => {
    let companies: InsuranceCompanyEntry[] = [];

    if (isUIM && clientInsuranceList) {
      // For UIM negotiations, use client insurance list
      companies = clientInsuranceList
        .filter(insurance => insurance && insurance.insurance_company) // Ensure insurance and insurance_company exist
        .map((insurance, index) => ({
          id: insurance.insurance_company?.id,
          name: insurance.insurance_company?.name || 'Unknown Insurance',
          claim_number: insurance.claim_number || 'No Claim Number',
          insurance_id: insurance.id,
          index: index
        }));
    } else if (!isUIM && defendantInsurances) {
      // For third-party negotiations, use defendant insurance list
      companies = defendantInsurances
        .filter(insurance => insurance && insurance.insurance_company) // Ensure insurance and insurance_company exist
        .map((insurance, index) => ({
          id: insurance.insurance_company?.id,
          name: insurance.insurance_company?.name || 'Unknown Insurance',
          claim_number: insurance.claim_number || 'No Claim Number',
          insurance_id: insurance.id,
          index: index
        }));
    }

    // Sort companies by name
    return companies.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
  }, [isUIM, clientInsuranceList, defendantInsurances]);

  const [selectedInsuranceId, setSelectedInsuranceId] = useState<string | null>(null);

  const filteredAdjusters = useMemo(() => {
    if (!selectedInsuranceId) return [];

    if (isUIM) {
      // For UIM, get adjusters for the selected client insurance
      const adjusters = getUniqueAdjusters(clientInsuranceAdjusters);
      console.log('UIM Adjusters:', adjusters);
      return adjusters;
    } else {
      // For normal demands, get adjusters for the selected defendant insurance
      // Find the selected insurance company from the insuranceCompanies array
      const selectedInsurance = insuranceCompanies.find(
        insurance => insurance.index.toString() === selectedInsuranceId
      );

      if (!selectedInsurance || !selectedInsurance.id) {
        console.log('No insurance company found for index:', selectedInsuranceId);
        return [];
      }

      console.log('Selected insurance company ID:', selectedInsurance.id);
      console.log('All defendant adjusters:', defendantAdjusters);

      // Filter adjusters by the actual insurance company ID
      const filteredList = (defendantAdjusters || []).filter(adjuster => {
        const match = adjuster.insurance_company?.toString() === selectedInsurance.id?.toString();
        console.log(
          `Adjuster ${adjuster.id} (${adjuster.first_name} ${adjuster.last_name}) - ` +
          `Insurance Company: ${adjuster.insurance_company} - Match: ${match}`
        );
        return match;
      });

      console.log('Filtered defendant adjusters:', filteredList);
      return filteredList;
    }
  }, [selectedInsuranceId, isUIM, clientInsuranceAdjusters, defendantAdjusters, insuranceCompanies]);

  const handleInsuranceChange = (value: string) => {
    setSelectedInsuranceId(value);

    // Find the selected insurance company
    const insurance = insuranceCompanies.find(item => item.index === Number(value));

    // Debug insurance selection
    console.log('Selected insurance index:', value);
    console.log('Found insurance:', insurance);
    console.log('All insurance companies:', insuranceCompanies);

    if (insurance) {
      // For UIM negotiations, set the client insurance ID
      if (isUIM && insurance.insurance_id) {
        setSelectedClientInsurance(insurance.insurance_id);
      } else {
        // For non-UIM, we don't need client insurance
        setSelectedClientInsurance(null);
      }

      // Set the insurance company ID in the form
      if (insurance.id) {
        form.setValue('insurance_company', insurance.id.toString());
        console.log('Set insurance_company in form to:', insurance.id.toString());
      }
    } else {
      // If no insurance is found, reset the client insurance
      setSelectedClientInsurance(null);
      form.setValue('insurance_company', '');
      console.log('No insurance found for index:', value);
    }

    // Reset adjuster when insurance changes
    form.setValue('adjuster', undefined);
  };

  const adjusters = useMemo(() => {
    if (isUIM) {
      return getUniqueAdjusters(clientInsuranceAdjusters);
    }
    return (defendantAdjusters || []) as AdjusterContact[];
  }, [isUIM, clientInsuranceAdjusters, defendantAdjusters]);
  const isLoadingAdjusters = isUIM ? isLoadingClientAdjusters : isLoadingDefendantAdjusters;

  const sortedAdjusters = useMemo(() => {
    return filteredAdjusters.sort((a, b) => {
      const nameA = `${a.first_name || ''} ${a.last_name || ''}`.toLowerCase();
      const nameB = `${b.first_name || ''} ${b.last_name || ''}`.toLowerCase();
      return nameA.localeCompare(nameB);
    });
  }, [filteredAdjusters]);

  const isLoadingInsurances = isUIM ? isLoadingClientInsurances : isLoadingDefendantInsurances;

  const form = useForm<NegotiationFormData>({
    resolver: zodResolver(negotiationSchema),
    defaultValues: {
      amount: "",
      type: NegotiationType.INITIAL_DEMAND,
      date_sent: formatDateForApi(new Date()),
      response_deadline: formatDateForApi(new Date()),
      response_received_date: undefined,
      extension_date: formatDateForApi(new Date()),
      notes: "",
      settlement_terms: "",
      payment_terms: "",
      conditions: "",
      release_terms: "",
      adjuster: undefined,
      insurance_company: ""
    }
  });

  // Convert NegotiationType to display text
  const getNegotiationTypeDisplay = (type: NegotiationType): string => {
    switch (type) {
      case NegotiationType.COUNTER_OFFER:
        return "COUNTER DEMAND";
      case NegotiationType.INITIAL_DEMAND:
        return "INITIAL DEMAND";
      default:
        return "INITIAL DEMAND";
    }
  };

  useEffect(() => {
    if (editDemand) {
      form.setValue('type', editDemand.type as NegotiationType);
      return;
    }

    const hasExistingNegotiations = negotiations?.some(n => {
      if (isUIM) {
        return 'client_insurance' in n && n.client_insurance === clientInsuranceId;
      } else {
        return 'defendant' in n && n.defendant?.id === defendantId;
      }
    });

    form.setValue('type', hasExistingNegotiations ? NegotiationType.COUNTER_OFFER : NegotiationType.INITIAL_DEMAND);
  }, [negotiations, defendantId, clientInsuranceId, isUIM, editDemand, form]);

  useEffect(() => {
    if (editDemand) {
      setOpen(true);
    } else {
      // When editDemand is cleared, ensure dialog is closed
      setOpen(false);
    }
  }, [editDemand]);

  console.log("test changes");

  useEffect(() => {
    form.setValue('type', editDemand ? NegotiationType.COUNTER_DEMAND : NegotiationType.INITIAL_DEMAND);
  }, [editDemand, form]);

  useEffect(() => {
    if (adjusters && adjusterId) {
      form.setValue('adjuster', adjusterId);
    }

    if (editDemand) {
      form.setValue('amount', editDemand.amount ? editDemand.amount.toString() : "");
      form.setValue('type', editDemand.type as NegotiationType);
      form.setValue('date_sent', editDemand.date_sent || formatDateForApi(new Date()));
      form.setValue('response_deadline', editDemand.response_deadline || formatDateForApi(new Date()));
      form.setValue('response_received_date', editDemand.response_received_date || "");
      form.setValue('extension_date', editDemand.extension_date || "");
      form.setValue('notes', editDemand.notes || "");
      form.setValue('settlement_terms', editDemand.settlement_terms || "");
      form.setValue('payment_terms', editDemand.payment_terms || "");
      form.setValue('conditions', editDemand.conditions || "");
      form.setValue('release_terms', editDemand.release_terms || "");

      if (editDemand.insurance_company) {
        const insuranceId = typeof editDemand.insurance_company === 'number'
          ? editDemand.insurance_company
          : editDemand.insurance_company.id;

        form.setValue('insurance_company', insuranceId.toString());

        const insurance = insuranceCompanies.find(
          item => item.id === insuranceId
        );
        if (insurance) {
          setSelectedInsuranceId(insurance.index.toString());
          if (isUIM) {
            setSelectedClientInsurance(insurance.insurance_id);
          }
        }
      }

      if (editDemand.adjuster?.id) {
        form.setValue('adjuster', editDemand.adjuster.id);
      }
    }
  }, [adjusters, adjusterId, form, editDemand, insuranceCompanies, isUIM]);

  // Call onEditComplete when updateMutation is done
  useEffect(() => {
    if (shouldCallEditComplete &&
      (updateMutation.status === 'success' || updateUIMutation.status === 'success')) {
      // First call onEditComplete
      if (onEditComplete) {
        onEditComplete();
      }

      // Then close dialog and reset form
      setOpen(false);
      form.reset();
      setShouldCallEditComplete(false);
    }
  }, [shouldCallEditComplete, updateMutation.status, updateUIMutation.status, onEditComplete, form]);

  const handleAdjusterChange = (value: string) => {
    // Convert string value to number for form
    const numericValue = parseInt(value, 10);
    if (!isNaN(numericValue)) {
      form.setValue('adjuster', numericValue);
    }
  };

  const determineNegotiationStatus = () => {
    if (!negotiations || negotiations.length === 0) {
      return NegotiationStatus.SENT;
    }

    const lastNegotiation = negotiations[0];

    if (lastNegotiation.status === NegotiationStatus.RECEIVED) {
      return NegotiationStatus.SENT;
    }
    return NegotiationStatus.RECEIVED;
  };

  const onSubmit = async (data: NegotiationFormData) => {
    // Add validation check before proceeding with submission
    if (!data.insurance_company) {
      form.setError("insurance_company", {
        type: "manual",
        message: "Insurance company is required for Initial Demand"
      });
      return;
    }

    // Validate date formats before proceeding
    const validateDate = (dateStr: string | undefined, fieldName: string): string | undefined => {
      if (!dateStr) return undefined;
      try {
        // Try to format the date - if it fails, it's invalid
        const formattedDate = formatDateForApi(dateStr);
        // if (!formattedDate) throw new Error('Invalid date format');
        return formattedDate;
      } catch (error) {
        form.setError(fieldName as keyof NegotiationFormData, {
          type: "manual",
          message: "Invalid date format"
        });
        return undefined;
      }
    };

    // Validate all dates
    const validatedDates = {
      date_sent: validateDate(data.date_sent, 'date_sent'),
      response_deadline: validateDate(data.response_deadline, 'response_deadline'),
      response_received_date: data.response_received_date ? validateDate(data.response_received_date, 'response_received_date') : undefined,
      extension_date: data.extension_date ? validateDate(data.extension_date, 'extension_date') : undefined
    };

    // Check if any date validation failed
    if (form.formState.errors.date_sent || form.formState.errors.response_deadline ||
        form.formState.errors.response_received_date || form.formState.errors.extension_date) {
      return;
    }

    // Get the dirty fields to only update what changed
    const dirtyFields = Object.keys(form.formState.dirtyFields);
    console.log('Dirty fields:', dirtyFields);

    // Start with the original data if editing
    const basePayload = {
      ...data,
      insurance_company: data.insurance_company ? parseInt(data.insurance_company) : undefined,
      // Use validated dates, falling back to existing values if not changed
      date_sent: validatedDates.date_sent || (editDemand?.date_sent ? formatDateForApi(editDemand.date_sent) : formatDateForApi(new Date())),
      response_deadline: validatedDates.response_deadline || (editDemand?.response_deadline ? formatDateForApi(editDemand.response_deadline) : formatDateForApi(new Date())),
      response_received_date: validatedDates.response_received_date,
      extension_date: validatedDates.extension_date,
      status: determineNegotiationStatus()
    };

    console.log('Form Data:', {
      dirtyFields,
      date_sent: data.date_sent,
      response_deadline: data.response_deadline,
      formatted_date_sent: validatedDates.date_sent,
      formatted_response_deadline: validatedDates.response_deadline
    });

    // When editing, only include fields that were actually changed
    const filteredPayload = Object.entries(basePayload).filter(([key, value]) => {
      // Always include required fields
      if (['amount', 'type', 'status', 'insurance_company'].includes(key)) return true;
      // For date fields, include if they're dirty or required
      if (['date_sent', 'response_deadline'].includes(key)) {
        return !editDemand || dirtyFields.includes(key);
      }
      // For other fields, include if they have a value and either they're dirty or we're creating new
      return value !== undefined && value !== "" && value !== null && (!editDemand || dirtyFields.includes(key));
    });

    // Create base payload with required fields
    const cleanedPayload = {
      amount: data.amount,
      type: data.type,
      status: determineNegotiationStatus(),
      insurance_company: parseInt(data.insurance_company),
      ...Object.fromEntries(filteredPayload.filter(([key]) => 
        !['amount', 'type', 'status', 'insurance_company'].includes(key)
      ))
    };

    console.log('Cleaned Payload:', cleanedPayload);

    try {
      if (editDemand) {
        // Update existing demand
        if (isUIM) {
          // Update UIM demand
          if (!selectedClientInsurance) {
            throw new Error('Client insurance is required for UIM negotiations');
          }

          const updateUIMPayload: NegotiationUIMWrite = {
            ...cleanedPayload,
            client_insurance: selectedClientInsurance,
            // Ensure required fields are properly typed
            amount: cleanedPayload.amount,
            type: cleanedPayload.type,
            status: cleanedPayload.status,
            insurance_company: cleanedPayload.insurance_company
          };

          await updateUIMutation.mutateAsync(updateUIMPayload);
          setShouldCallEditComplete(true);
        } else {
          // Update normal demand
          type UpdatePayload = Omit<NegotiationWrite, 'defendant'> & {
            defendant?: number;
          };

          const updatePayload: UpdatePayload = {
            ...cleanedPayload,
            // Ensure required fields are properly typed
            amount: cleanedPayload.amount,
            type: cleanedPayload.type,
            status: cleanedPayload.status,
            insurance_company: cleanedPayload.insurance_company,
            // Only include defendant if it's a valid number
            ...(typeof defendantId === 'number' ? { defendant: defendantId } : {})
          };

          await updateMutation.mutateAsync(updatePayload);
          setShouldCallEditComplete(true);
        }
      } else if (isUIM) {
        // Create new UIM demand
        if (!selectedClientInsurance) {
          throw new Error('Client insurance is required for UIM negotiations');
        }
        
        const createUIMPayload: NegotiationUIMWrite = {
          ...cleanedPayload,
          client_insurance: selectedClientInsurance,
          // Ensure required fields are properly typed
          amount: cleanedPayload.amount,
          type: cleanedPayload.type,
          status: cleanedPayload.status,
          insurance_company: cleanedPayload.insurance_company
        };

        await createUIMMutation.mutateAsync(createUIMPayload);
        handleClose();
      } else {
        // Create new demand
        if (!defendantId) {
          throw new Error('Defendant is required for liability negotiations');
        }

        const createPayload: NegotiationWrite = {
          ...cleanedPayload,
          defendant: defendantId,
          // Ensure required fields are properly typed
          amount: cleanedPayload.amount,
          type: cleanedPayload.type,
          status: cleanedPayload.status,
          insurance_company: cleanedPayload.insurance_company
        };

        await createMutation.mutateAsync(createPayload);
        handleClose();
      }
    } catch (error) {
      console.error("Error submitting demand:", error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setOpen(true);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset();
    setSelectedInsuranceId(null);
    setSelectedClientInsurance(null);

    // Call the onClose prop when dialog closes
    if (onClose) {
      onClose();
    }
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        {!editDemand && (
          <DialogTrigger asChild>
            <Button
              variant="link"
              disabled={disabled}
              className="text-green-600"
            >
              <Plus className="w-4 h-4 mr-2" />
              Demand
            </Button>
          </DialogTrigger>
        )}
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{editDemand ? "Edit Demand" : "Create New Demand"}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="max-h-[80vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => {
                      // Ensure we always have a valid type, even after form reset
                      const currentType = field.value ||
                        (negotiations && negotiations.length > 0 ?
                          NegotiationType.COUNTER_OFFER :
                          NegotiationType.INITIAL_DEMAND);

                      return (
                        <FormItem>
                          <RequiredLabel>Negotiation Type</RequiredLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                if (!value) {
                                  field.onChange(currentType);
                                }
                              }}
                              value={currentType}
                              disabled={true}
                            >
                              <SelectTrigger>
                                <SelectValue>
                                  {getNegotiationTypeDisplay(currentType)}
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem
                                  value={currentType}
                                >
                                  {getNegotiationTypeDisplay(currentType)}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Amount</RequiredLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter amount"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="space-y-2">
                    <label>Notes</label>
                    <Textarea
                      {...form.register("notes")}
                      placeholder="Enter any additional notes"
                      className="h-24"
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="insurance_company"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Insurance Company</RequiredLabel>
                        <Select
                          onValueChange={handleInsuranceChange}
                          value={selectedInsuranceId || ""}
                          defaultValue={selectedInsuranceId || ""}
                        >
                          <FormControl>
                            <SelectTrigger className={!field.value ? "border-red-500" : ""}>
                              <SelectValue placeholder={isLoadingInsurances ? "Loading..." : "Select company"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent searchable>
                            {isLoadingInsurances ? (
                              <SelectItem value="loading">Loading...</SelectItem>
                            ) : insuranceCompanies.map((insurance) => (
                              <SelectItem key={insurance.id} value={insurance.index.toString()}>
                                {insurance.name} - {insurance.claim_number}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-red-500" />
                      </FormItem>
                    )}
                  />

                  {selectedInsuranceId && (
                    <FormField
                      control={form.control}
                      name="adjuster"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Adjuster</FormLabel>
                          <Select
                            onValueChange={handleAdjusterChange}
                            value={field.value?.toString() || ""}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={isLoadingAdjusters ? "Loading..." : "Select adjuster"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {isLoadingAdjusters ? (
                                <SelectItem value="loading">Loading adjusters...</SelectItem>
                              ) : sortedAdjusters.length > 0 ? (
                                sortedAdjusters.map((adjuster) => (
                                  <SelectItem
                                    key={adjuster.id || 'no-id'}
                                    value={(adjuster.id || '').toString()}
                                  >
                                    {`${adjuster.first_name || ''} ${adjuster.last_name || ''}`}
                                    {isAdjusterWithRoles(adjuster) && adjuster.roles ?
                                      ` (${adjuster.roles.join(', ')})` : ''}
                                  </SelectItem>
                                ))
                              ) : (
                                <SelectItem disabled value="no-adjusters">
                                  No adjusters found for this insurance company
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="date_sent"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Sent Date</RequiredLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value && typeof field.value === 'string' ? formatDateForInput(field.value) : ""}
                            onChange={(value) => {
                              field.onChange(value);
                            }}
                            error={!!form.formState.errors.date_sent}
                            maxDate={new Date()}
                            onError={(message) => {
                              form.setError("date_sent", {
                                type: "manual",
                                message
                              });
                            }}
                          />
                        </FormControl>
                        {form.formState.errors.date_sent && (
                          <p className="text-sm text-red-500 mt-1">
                            {form.formState.errors.date_sent.message}
                          </p>
                        )}
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="response_deadline"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel className="text-sm pb-1">Response Deadline </RequiredLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value && typeof field.value === 'string' ? formatDateForInput(field.value) : ""}
                            onChange={(value) => {
                              field.onChange(value);
                            }}
                            error={!!form.formState.errors.response_deadline}
                            minDate={new Date()}
                            onError={(message) => {
                              form.setError("response_deadline", {
                                type: "manual",
                                message
                              });
                            }}
                          />
                        </FormControl>
                        {form.formState.errors.response_deadline && (
                          <p className="text-sm text-red-500 mt-1">
                            {form.formState.errors.response_deadline.message}
                          </p>
                        )}
                      </FormItem>
                    )}
                  />

                  {/* Response Received Date Field - Currently Disabled
                  <FormField
                    control={form.control}
                    name="response_received_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Response Received</FormLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value ? format(new Date(field.value), "dd-MM-yyyy") : ""}
                            onChange={(value) => {
                              const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                              if (!isNaN(parsedDate.getTime())) {
                                field.onChange(format(parsedDate, "yyyy-MM-dd"));
                              }
                            }}
                            error={!!form.formState.errors.response_received_date}
                            maxDate={new Date()}
                            onError={(message) => {
                              form.setError("response_received_date", {
                                type: "manual",
                                message
                              });
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  */}

                  {/* Extension Date Field - Currently Disabled
                  <FormField
                    control={form.control}
                    name="extension_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Extension Date</FormLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value ? format(new Date(field.value), "dd-MM-yyyy") : ""}
                            onChange={(value) => {
                              const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                              if (!isNaN(parsedDate.getTime())) {
                                field.onChange(format(parsedDate, "yyyy-MM-dd"));
                              }
                            }}
                            error={!!form.formState.errors.extension_date}
                            minDate={new Date()}
                            onError={(message) => {
                              form.setError("extension_date", {
                                type: "manual",
                                message
                              });
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  */}
                </div>

                <div className="grid grid-cols-2 gap-4 mt-4">


                  {/* Settlement Terms Field - Currently Disabled
                  <div className="space-y-2">
                    <label>Settlement Terms</label>
                    <Textarea
                      {...form.register("settlement_terms")}
                      placeholder="Enter settlement terms"
                      className="h-24"
                    />
                  </div>
                  */}

                  {/* Payment Terms Field - Currently Disabled
                  <div className="space-y-2">
                    <label>Payment Terms</label>
                    <Textarea
                      {...form.register("payment_terms")}
                      placeholder="Enter payment terms"
                      className="h-24"
                    />
                  </div>
                  */}

                  {/* Conditions Field - Currently Disabled
                  <div className="space-y-2">
                    <label>Conditions</label>
                    <Textarea
                      {...form.register("conditions")}
                      placeholder="Enter conditions"
                      className="h-24"
                    />
                  </div>
                  */}

                  {/* Release Terms Field - Currently Disabled
                  <div className="space-y-2 col-span-2">
                    <label>Release Terms</label>
                    <Textarea
                      {...form.register("release_terms")}
                      placeholder="Enter release terms"
                      className="h-24"
                    />
                  </div>
                  */}
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>
                <Button type="submit">{editDemand ? "Update Demand" : "Create Demand"}</Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={() => {
          form.reset();
          setOpen(false);
          if (onClose) {
            onClose();
            setShowUnsavedAlert(false);
          }
        }}
        onCancel={() => {
          setShowUnsavedAlert(false);
        }}
      />
    </>
  );
}