import React, { useState, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Trash2, PlusSquare, DollarSign, Clock, FileUp, FileText } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { AddEditCost } from "./AddEditCost";
import { ContactType } from "@/type/case-management/orgTypes";
import {
  useCaseCostsQuery,
  useDeleteCaseCostMutation,
  useUpdateCaseCostMutation,
} from "@/services/case-management/costService";
import { useAutoUploadMutation } from "@/services/case-management/documentManagementService";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import {
  CostFor,
  CostPriority,
  PRIORITY_CHOICES,
  STATUS_CHOICES,
} from "@/type/case-management/costTypes";
import { CostStatus } from "@/type/case-management/costTypes";
import { CaseCostUpdateRequest } from "@/type/case-management/costTypes";
import { Label } from "@/components/ui/label";
import { formatDateForDisplay } from "@/utils/dateUtils";
import { CurrencyDisplay } from "@/components/ui/currency-display";
import { KPICard } from "@/components/ui/kpi-card";
import { Input } from "@/components/ui/input";
import { CopyNumber } from "@/components/ui/copy-number";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface CaseCostsProps {
  caseId: string;
  costFor?: CostFor;
}

function DeleteCostButton({
  caseId,
  costId,
  costFor = CostFor.CASE_COST,
}: {
  caseId: string;
  costId: string;
  costFor?: CostFor;
}) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const deleteCost = useDeleteCaseCostMutation(caseId, costId, costFor);

  const handleDelete = () => {
    deleteCost.mutate();
  };

  return (
    <>
      <Button
        onClick={() => setIsDeleteDialogOpen(true)}
        variant="link"
        title="Delete cost"
      >
        <Trash2 className="h-4 w-4 text-red-600" />
      </Button>
      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDelete}
        text="Cost"
      />
    </>
  );
}

export function CaseCosts({ caseId, costFor = CostFor.CASE_COST }: CaseCostsProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchParams, setSearchParams] = useState({
    page: 1,
    page_size: 10,
    search: ""
  });

  const { data: costsData, isLoading } = useCaseCostsQuery(caseId, costFor, searchParams);
  const autoUpload = useAutoUploadMutation();
  const { toast } = useToast();
  const updateCostMutation = useUpdateCaseCostMutation();

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchParams(prev => ({ ...prev, search: searchTerm, page: 1 }));
      setCurrentPage(1);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    setSearchParams(prev => ({ ...prev, page }));
  }, []);

  const handlePageSizeChange = useCallback((value: string) => {
    const newPageSize = Number(value);
    setPageSize(newPageSize);
    setCurrentPage(1);
    setSearchParams(prev => ({ ...prev, page_size: newPageSize, page: 1 }));
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleFileUpload = async (file: File) => {
    try {
      await autoUpload.mutateAsync({
        file,
        document_type: "CASE_COST_DOCUMENT",
        level: "case",
        case_id: caseId,
        description: `Cost document - ${file.name}`,
      });

      toast({
        title: "Success",
        description: "Cost document uploaded successfully",
        variant: "default",
      });
    } catch (error) {
      console.error("Upload error:", error);
      toast({
        title: "Error",
        description: "Failed to upload cost document",
        variant: "destructive",
      });
    }
  };

  const handleStatusChange = async ({
    id,
    status,
  }: {
    id: string;
    status: string;
  }) => {
    try {
      const costToUpdate = costsData?.results.find(
        (c) => c.id?.toString() === id
      );
      if (costToUpdate) {
        const updateData: CaseCostUpdateRequest = {
          status: status as CostStatus,
          cost_for: costFor,
        };
        await updateCostMutation.mutateAsync({
          caseId,
          costId: id,
          data: updateData,
          costFor,
        });
      }
    } catch (error) {
      console.error("Failed to update status:", error);
    }
  };

  const handlePriorityChange = async ({
    id,
    priority,
  }: {
    id: string;
    priority: string;
  }) => {
    try {
      const costToUpdate = costsData?.results.find(
        (c) => c.id?.toString() === id
      );
      if (costToUpdate) {
        const updateData: CaseCostUpdateRequest = {
          priority: priority as CostPriority,
          cost_for: costFor,
        };
        await updateCostMutation.mutateAsync({
          caseId,
          costId: id,
          data: updateData,
          costFor,
        });
      }
    } catch (error) {
      console.error("Failed to update priority:", error);
    }
  };

  // Calculate totals from real data
  const totalCosts = costsData?.results?.length || 0;
  const totalPaid =
    costsData?.results?.reduce(
      (sum, cost) => (cost.status === CostStatus.PAID ? sum + Number(cost.amount) : sum),
      0
    ) || 0;
  const pending =  costsData?.results?.reduce(
    (sum, cost) => (cost.status === CostStatus.PENDING ? sum + Number(cost.amount) : sum),
    0
  ) || 0;
  const totalPending = costsData?.results?.filter(cost => cost.status === CostStatus.PENDING).length || 0;

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Cost Overview</h2>
        <div className="flex items-center">
          <AddEditCost
            isEdit={false}
            onSuccess={() => { }}
            costFor={costFor}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="link" className="gap-2 text-green-600">
                <FileText className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  const input = document.createElement("input");
                  input.type = "file";
                  input.accept = ".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png";
                  input.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file) handleFileUpload(file);
                  };
                  input.click();
                }}
              >
                <FileUp className="mr-2 h-4 w-4" />
                Upload Cost Document
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="grid grid-cols-4 gap-4">
        <KPICard
          icon={PlusSquare}
          title="Total Cost"
          metrics={{
            primary: {
              type: 'count',
              value: totalCosts
            }
          }}
        />
        <KPICard
          icon={DollarSign}
          title="Total Paid"
          metrics={{
            primary: {
              type: 'monetary',
              value: totalPaid
            }
          }}
          isCurrency={true}
        />
        <KPICard
          icon={Clock}
          title="Pending"
          metrics={{
            primary: {
              type: 'monetary',
              value: pending
            }
          }}
          isCurrency={true}
        />
         <KPICard
          icon={PlusSquare}
          title="Total Pending"
          metrics={{
            primary: {
              type: 'count',
              value: totalPending
            }
          }}
        />
      </div>

      {/* Action Bar */}
      <div className="flex items-end mt-4 mb-4 justify-end">
        <Input
          type="search"
          placeholder="Search"
          className="rounded-full bg-white flex items-center w-[300px]"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </div>

      {/* Table Section */}
      <div className="bg-white rounded-lg border shadow-sm">
        {costsData?.results && costsData.results.length > 0 ? (
          <>
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-medium text-center">Payee</TableHead>
                  <TableHead className="font-medium text-center">Status</TableHead>
                  <TableHead className="font-medium text-center">Priority</TableHead>
                  <TableHead className="font-medium text-center">Memo</TableHead>
                  <TableHead className="font-medium text-center">Invoice</TableHead>
                  <TableHead className="font-medium text-center">Requested</TableHead>
                  <TableHead className="font-medium text-center">Paid</TableHead>
                  <TableHead className="font-medium text-center">Paid By</TableHead>
                  <TableHead className="font-medium text-center">Amount</TableHead>
                  <TableHead className="font-medium text-center">Void/Refund</TableHead>
                  <TableHead className="font-medium"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {costsData.results.map((cost) => {
                  return (
                    <TableRow key={cost.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">
                        <div>{cost.cost_contact?.full_display_name}</div>
                      </TableCell>
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Label
                              className={cn("px-2 py-1 rounded-md cursor-pointer", {
                                "bg-green-50 text-green-800 ring-1 ring-green-600/20":
                                  cost.status === CostStatus.PAID,
                                "bg-gray-50 text-gray-800 ring-1 ring-gray-600/20":
                                  cost.status === CostStatus.CANCELLED,
                              })}
                            >
                              {cost.status}
                            </Label>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            {STATUS_CHOICES.map(({ value, label }) => (
                              <DropdownMenuItem
                                key={value}
                                onSelect={() =>
                                  handleStatusChange({
                                    id: cost.id?.toString() || "",
                                    status: value,
                                  })
                                }
                              >
                                {label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Label
                              className={cn("px-2 py-1 rounded-md cursor-pointer", {
                                "bg-gray-50 text-gray-800 ring-1 ring-gray-600/20":
                                  cost.priority === "REGULAR",
                                "bg-red-50 text-red-800 ring-1 ring-red-600/20":
                                  cost.priority === "URGENT",
                                "bg-orange-50 text-orange-800 ring-1 ring-orange-600/20":
                                  cost.priority === "HIGH",
                              })}
                            >
                              {cost.priority}
                            </Label>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            {PRIORITY_CHOICES.map(({ value, label }) => (
                              <DropdownMenuItem
                                key={value}
                                onSelect={() =>
                                  handlePriorityChange({
                                    id: cost.id?.toString() || "",
                                    priority: value,
                                  })
                                }
                              >
                                {label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                      <TableCell>{cost.memo}</TableCell>
                      <TableCell>{cost.invoice_number ? <CopyNumber value={cost.invoice_number} /> : cost.invoice_number} </TableCell>
                      <TableCell>
                        {cost.requested_date && (
                          <div>
                            <div>{formatDateForDisplay(cost.requested_date)}</div>
                            <div className="text-sm text-gray-500">{cost.request_created_by}</div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {cost.paid_date && (
                          <div>
                            <div>{formatDateForDisplay(cost.paid_date)}</div>
                            <div className="text-sm text-gray-500">{cost.paid_created_by}</div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {cost.payment_type === "CREDIT_CARD" ? (
                          <div>
                            Credit Card{" "}
                            {cost.credit_card_reference
                              ? `(${cost.credit_card_reference})`
                              : ""}
                          </div>
                        ) : cost.payment_type === "CHECK" ? (
                          <div>
                            Check {cost.check_number ? `#${cost.check_number}` : ""}
                          </div>
                        ) : (
                          ""
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        <CurrencyDisplay amount={cost.amount} />
                      </TableCell>
                      <TableCell>
                        {cost.void && (
                          <div className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-800 ring-1 ring-inset ring-red-600/20">
                            Void
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <AddEditCost
                            isEdit={true}
                            selectedCost={{
                              id: cost.id?.toString() || "",
                              contactType:
                                cost.cost_contact?.contact_type ||
                                ContactType.GENERAL,
                              contact: cost.cost_contact?.id?.toString() || "",
                              amount: Number(cost.amount),
                              memo: cost.memo,
                              invoice: cost.invoice_number,
                              priority:
                                (cost.priority?.toLowerCase() as
                                  | "regular"
                                  | "urgent"
                                  | "high") || "regular",
                              payment_type: cost.payment_type,
                              check_number: cost.check_number,
                              credit_card_reference: cost.credit_card_reference,
                              paid_date: cost.paid_date,
                              requested_date: cost.requested_date,
                              is_void: cost.void,
                              cost_contact: cost.cost_contact,
                              cost_for: costFor,
                            }}
                            onSuccess={() => { }}
                            costFor={costFor}
                          />
                          <DeleteCostButton
                            caseId={caseId}
                            costId={cost.id?.toString() || ""}
                            costFor={costFor}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>

            <div className="flex items-center justify-between py-4 px-4">
              <div className="flex-1 max-w-[400px]">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600 whitespace-nowrap">Items per page:</span>
                  <Select
                    value={pageSize.toString()}
                    onValueChange={handlePageSizeChange}
                  >
                    <SelectTrigger className="w-[70px]">
                      <SelectValue placeholder={pageSize} />
                    </SelectTrigger>
                    <SelectContent>
                      {[10, 20, 25, 100].map((size) => (
                        <SelectItem key={size} value={size.toString()}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600 whitespace-nowrap">
                    {`${((currentPage - 1) * pageSize) + 1}-${Math.min(currentPage * pageSize, costsData?.count || 0)} of ${costsData?.count || 0}`}
                  </span>
                </div>
              </div>

              <div className="flex-1 max-w-[400px]">
                <div className="flex justify-end">
                  <Pagination>
                    <PaginationContent className="flex justify-end">
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(currentPage - 1)}
                          className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        />
                      </PaginationItem>

                      {[...Array(Math.ceil((costsData?.count || 0) / pageSize))].map((_, index) => {
                        const page = index + 1;
                        if (
                          page === 1 ||
                          page === Math.ceil((costsData?.count || 0) / pageSize) ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        } else if (
                          page === currentPage - 2 ||
                          page === currentPage + 2
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationEllipsis />
                            </PaginationItem>
                          );
                        }
                        return null;
                      })}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(currentPage + 1)}
                          className={currentPage === Math.ceil((costsData?.count || 0) / pageSize) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="text-gray-500 text-center py-4">No case costs</div>
        )}
      </div>
    </div>
  );
}
