import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  useUpdateIncidentDetailsMutation,
  useCreateIncidentDetailsMutation,
} from "@/services/incidentService";
import { CaseIncidentDetails, DocRequestStatus } from "@/type/incidentTypes";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format, parse, parseISO, addYears } from "date-fns";
import {
  FileText,
  AlertTriangle,
  MapPin,
  Shield,
  Stethoscope,
  Plus,
  Trash2,
  Calendar,
} from "lucide-react";
import {
  INCIDENT_TYPE_CHOICES,
  MEDICAL_STATUS_CHOICES,
} from "@/type/lead/leadTypes";
import { IncidentDetailsUpdateRequest } from "@/type/incidentTypes";
import { BODY_PARTS } from "@/constants/bodyParts";
import { USStatesLabels } from "@/constants/commont";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import RichTextEditor from "@/components/ui/RichTextEditor";
import { toast } from "@/hooks/use-toast";

const ICON_CLASSES = "h-5 w-5 text-gray-500";

const DOC_REQUEST_STATUS_DISPLAY: Record<DocRequestStatus, string> = {
  [DocRequestStatus.RECEIVED]: "Received",
  [DocRequestStatus.REQUESTED]: "Requested",
  [DocRequestStatus.DOES_NOT_EXIST]: "Does Not Exist",
  [DocRequestStatus.NOT_YET_REQUESTED]: "Not Yet Requested",
};

// Add statute of limitations mapping by state for personal injury cases
const STATE_STATUTES: Record<string, number> = {
  AL: 2,
  AK: 2,
  AZ: 2,
  AR: 3,
  CA: 2,
  CO: 2,
  CT: 2,
  DE: 2,
  FL: 4,
  GA: 2,
  HI: 2,
  ID: 2,
  IL: 2,
  IN: 2,
  IA: 2,
  KS: 2,
  KY: 1,
  LA: 1,
  ME: 6,
  MD: 3,
  MA: 3,
  MI: 3,
  MN: 2,
  MS: 3,
  MO: 5,
  MT: 3,
  NE: 4,
  NV: 2,
  NH: 3,
  NJ: 2,
  NM: 3,
  NY: 3,
  NC: 3,
  ND: 6,
  OH: 2,
  OK: 2,
  OR: 2,
  PA: 2,
  RI: 3,
  SC: 3,
  SD: 3,
  TN: 1,
  TX: 2,
  UT: 4,
  VT: 3,
  VA: 2,
  WA: 3,
  WV: 2,
  WI: 3,
  WY: 4,
  DC: 3,
  // ... add other states
  // This is a simplified example - you should add all states with their correct statutes
};

interface EditIncidentDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  incidentDetails?: CaseIncidentDetails;
  caseId: string;
  isEdit: boolean;
}

interface AffectedArea {
  part: string;
  description: string;
}

export default function EditIncidentDetailsDialog({
  isEdit,
  isOpen,
  onClose,
  incidentDetails,
  caseId,
}: EditIncidentDetailsDialogProps) {
  const form = useForm<IncidentDetailsUpdateRequest>({
    defaultValues: {
      report_number: incidentDetails?.report_number || "",
      incident_date: incidentDetails?.incident_date || "",
      incident_type: incidentDetails?.incident_type || "",
      street1: incidentDetails?.street1 || "",
      street2: incidentDetails?.street2 || "",
      city: incidentDetails?.city || "",
      state: incidentDetails?.state || "",
      zip_code: incidentDetails?.zip_code || "",
      insurance_status: incidentDetails?.insurance_status || false,
      incident_description: incidentDetails?.incident_description || "",
      weather_conditions: incidentDetails?.weather_conditions || "",
      police_report_details: incidentDetails?.police_report_details || "",
      witness_information: incidentDetails?.witness_information || "",
      previous_attorney: incidentDetails?.previous_attorney || "",
      medical_status: incidentDetails?.medical_status,
      basic_facts: incidentDetails?.basic_facts || "",
      conflicts_checked: incidentDetails?.conflicts_checked || false,
      injury_type: incidentDetails?.injury_type || "",
      injury_location: incidentDetails?.injury_location || "",
      affected_areas: incidentDetails?.affected_areas || {},
      surgery_required: incidentDetails?.surgery_required || false,
      critical_conditions: incidentDetails?.critical_conditions || "",
      liability_assessment: incidentDetails?.liability_assessment || "",
      estimated_value: incidentDetails?.estimated_value || "",
      statute_of_limitations: incidentDetails?.statute_of_limitations || "",
      doc_request_status:
        incidentDetails?.doc_request_status ||
        DocRequestStatus.NOT_YET_REQUESTED,
      doc_request_notes: incidentDetails?.doc_request_notes || "",
    },
  });

  const updateMutation = useUpdateIncidentDetailsMutation(caseId);
  const createMutation = useCreateIncidentDetailsMutation(caseId);
  const [date, setDate] = useState<Date | undefined>(
    incidentDetails?.incident_date
      ? parseISO(incidentDetails.incident_date)
      : undefined
  );

  const [statuteDate, setStatuteDate] = useState<Date | undefined>(
    incidentDetails?.statute_of_limitations
      ? parseISO(incidentDetails.statute_of_limitations)
      : undefined
  );

  console.log(statuteDate, date);

  const [affectedAreas, setAffectedAreas] = useState<AffectedArea[]>(() => {
    if (incidentDetails?.affected_areas) {
      return Object.entries(incidentDetails.affected_areas).map(
        ([part, description]) => ({
          part,
          description: description as string,
        })
      );
    }
    return [];
  });

  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [formState, setFormState] = useState<IncidentDetailsUpdateRequest | null>(null);

  const handleAreaChange = (
    index: number,
    field: "part" | "description",
    value: string
  ) => {
    const newAreas = [...affectedAreas];
    newAreas[index][field] = value;
    setAffectedAreas(newAreas);

    const areasObject = newAreas.reduce((acc, area) => {
      if (area.part) {
        acc[area.part] = area.description || "";
      }
      return acc;
    }, {} as Record<string, string>);

    form.setValue("affected_areas", areasObject, { shouldValidate: true });
  };

  const handleAddArea = () => {
    if (
      affectedAreas.length === 0 ||
      (affectedAreas[affectedAreas.length - 1].part &&
        affectedAreas[affectedAreas.length - 1].description)
    ) {
      const newAreas = [...affectedAreas, { part: "", description: "" }];
      setAffectedAreas(newAreas);
    }
  };

  const handleRemoveArea = (index: number) => {
    const newAreas = affectedAreas.filter((_, i) => i !== index);
    setAffectedAreas(newAreas);

    // Convert remaining areas to object format for form state
    const areasObject = newAreas.reduce((acc, area) => {
      if (area.part) {
        acc[area.part] = area.description || "";
      }
      return acc;
    }, {} as Record<string, string>);

    form.setValue("affected_areas", areasObject, { shouldValidate: true });
  };

  const isPartSelected = (part: string, currentIndex: number) => {
    return affectedAreas.some(
      (area, index) => index !== currentIndex && area.part === part
    );
  };

  // Add function to calculate statute of limitations
  const calculateStatuteOfLimitations = (incidentDate: Date, state: string) => {
    const yearsToAdd = STATE_STATUTES[state] || 2; // Default to 2 years if state not found
    return addYears(incidentDate, yearsToAdd);
  };

  // Add validation for statute date
  const validateStatuteDate = (statuteDate: Date, incidentDate: Date | undefined) => {
    if (!incidentDate) return true;
    return statuteDate >= incidentDate;
  };

  // Update date change handler to auto-calculate statute
  const handleDateChange = (value: string) => {
    try {
      const parsedDate = parse(value, "dd-MM-yyyy", new Date());
      if (!isNaN(parsedDate.getTime())) {
        form.setValue("incident_date", format(parsedDate, "yyyy-MM-dd"));
        setDate(parsedDate);

        // Auto-calculate statute if state is selected
        const currentState = form.getValues("state");
        if (currentState) {
          const statuteDate = calculateStatuteOfLimitations(
            parsedDate,
            currentState
          );
          form.setValue(
            "statute_of_limitations",
            format(statuteDate, "yyyy-MM-dd")
          );
          setStatuteDate(statuteDate);
        }
      }
    } catch (error) {
      console.error("Error parsing date:", error);
    }
  };

  // Update state change handler to recalculate statute
  const handleStateChange = (newState: string) => {
    form.setValue("state", newState, { shouldDirty: true });
    const currentDate = date;
    if (currentDate && newState) {
      const statuteDate = calculateStatuteOfLimitations(currentDate, newState);
      form.setValue(
        "statute_of_limitations",
        format(statuteDate, "yyyy-MM-dd")
      );
      setStatuteDate(statuteDate);
    }
  };

  const onSubmit = async (data: IncidentDetailsUpdateRequest) => {
    try {
      // Validate statute date is not before incident date
      if (data.statute_of_limitations && data.incident_date) {
        const statuteDate = parseISO(data.statute_of_limitations);
        const incidentDate = parseISO(data.incident_date);
        
        if (!validateStatuteDate(statuteDate, incidentDate)) {
          toast({
            title: "Invalid Date",
            description: "Statute of limitations date cannot be before the incident date",
            variant: "destructive"
          });
          return;
        }
      }

      const affectedAreasObject = affectedAreas.reduce(
        (acc, { part, description }) => {
          if (part) {
            acc[part] = description || "";
          }
          return acc;
        },
        {} as Record<string, string>
      );

      // Create base payload without date fields first
      const basePayload = {
        ...data,
        affected_areas: affectedAreasObject,
        street1: data.street1,
        street2: data.street2,
        city: data.city,
        state: data.state,
        zip_code: data.zip_code,
      };

      // Add date fields separately to handle type issues
      const payload = {
        ...basePayload,
        incident_date: data.incident_date || undefined,
        statute_of_limitations: data.statute_of_limitations || undefined,
      };

      if (incidentDetails) {
        await updateMutation.mutateAsync(payload);
      } else {
        await createMutation.mutateAsync({
          ...payload,
          case: caseId,
          insurance_status: payload.insurance_status ?? false,
          conflicts_checked: payload.conflicts_checked ?? false,
          surgery_required: payload.surgery_required ?? false,
        });
      }
      onClose();
    } catch (error) {
      console.error("Failed to save incident details:", error);
    }
  };

  // Reset form when dialog is opened/closed
  useEffect(() => {
    if (isOpen) {
      // Reset to initial values when dialog opens
      form.reset({
        report_number: incidentDetails?.report_number || "",
        incident_date: incidentDetails?.incident_date || "",
        incident_type: incidentDetails?.incident_type || "",
        street1: incidentDetails?.street1 || "",
        street2: incidentDetails?.street2 || "",
        city: incidentDetails?.city || "",
        state: incidentDetails?.state || "",
        zip_code: incidentDetails?.zip_code || "",
        insurance_status: incidentDetails?.insurance_status || false,
        incident_description: incidentDetails?.incident_description || "",
        weather_conditions: incidentDetails?.weather_conditions || "",
        police_report_details: incidentDetails?.police_report_details || "",
        witness_information: incidentDetails?.witness_information || "",
        previous_attorney: incidentDetails?.previous_attorney || "",
        medical_status: incidentDetails?.medical_status,
        basic_facts: incidentDetails?.basic_facts || "",
        conflicts_checked: incidentDetails?.conflicts_checked || false,
        injury_type: incidentDetails?.injury_type || "",
        injury_location: incidentDetails?.injury_location || "",
        affected_areas: incidentDetails?.affected_areas || {},
        surgery_required: incidentDetails?.surgery_required || false,
        critical_conditions: incidentDetails?.critical_conditions || "",
        liability_assessment: incidentDetails?.liability_assessment || "",
        estimated_value: incidentDetails?.estimated_value || "",
        statute_of_limitations: incidentDetails?.statute_of_limitations || "",
        doc_request_status:
          incidentDetails?.doc_request_status ||
          DocRequestStatus.NOT_YET_REQUESTED,
        doc_request_notes: incidentDetails?.doc_request_notes || "",
      });

      // Reset dates
      setDate(
        incidentDetails?.incident_date
          ? parseISO(incidentDetails.incident_date)
          : undefined
      );
      setStatuteDate(
        incidentDetails?.statute_of_limitations
          ? parseISO(incidentDetails.statute_of_limitations)
          : undefined
      );

      // Reset affected areas
      setAffectedAreas(
        incidentDetails?.affected_areas
          ? Object.entries(incidentDetails.affected_areas).map(
            ([part, description]) => ({
              part,
              description: description as string,
            })
          )
          : []
      );
    }
  }, [isOpen, incidentDetails, form]);

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && formState !== null) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      }
    }
  };

  const handleClose = () => {
    form.reset();
    setFormState(null);
    setDate(
      incidentDetails?.incident_date
        ? parseISO(incidentDetails.incident_date)
        : undefined
    );
    setStatuteDate(
      incidentDetails?.statute_of_limitations
        ? parseISO(incidentDetails.statute_of_limitations)
        : undefined
    );
    onClose();
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  // Watch for form changes
  useEffect(() => {
    const subscription = form.watch((value) => {
      const hasChanges = JSON.stringify(value) !== JSON.stringify(incidentDetails);
      if (hasChanges) {
        setFormState(value as IncidentDetailsUpdateRequest);
      } else {
        setFormState(null);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, incidentDetails]);

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleOpenChange} modal={true}>
        <DialogContent
          className="max-w-6xl bg-white"
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="text-2xl font-semibold">
              {isEdit ? "Edit Incident Details" : "Add Incident Details"}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="max-h-[80vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {/* Basic Details Section */}
                <div className="border-b pb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
                    <FileText className={ICON_CLASSES} />
                    Basic Details
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    {/* Date of Loss */}
                    <div className="flex items-start gap-3">
                      <Calendar className={ICON_CLASSES} />
                      <div className="space-y-1 flex-1">
                        <FormField
                          control={form.control}
                          name="incident_date"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Date of Loss</FormLabel>
                              <FormControl>
                                <CustomDateInput
                                  value={
                                    field.value
                                      ? format(
                                        parseISO(field.value),
                                        "dd-MM-yyyy"
                                      )
                                      : ""
                                  }
                                  onChange={handleDateChange}
                                  error={false}
                                  maxDate={new Date()}
                                  onError={(message: string) => {
                                    console.error(message);
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Report Number */}
                    <div className="flex items-start gap-3">
                      <FileText className={ICON_CLASSES} />
                      <div className="space-y-1 flex-1">
                        <FormField
                          control={form.control}
                          name="report_number"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Report Number</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder="Enter Report Number"
                                  className="bg-gray-50"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Statute of Limitations */}
                    <div className="flex items-start gap-3">
                      <Calendar className={ICON_CLASSES} />
                      <div className="space-y-1 flex-1">
                        <FormField
                          control={form.control}
                          name="statute_of_limitations"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Statute of Limitations</FormLabel>
                              <FormControl>
                                <CustomDateInput
                                  value={
                                    field.value
                                      ? format(
                                        parseISO(field.value),
                                        "dd-MM-yyyy"
                                      )
                                      : ""
                                  }
                                  onChange={(value) => {
                                    try {
                                      const parsedDate = parse(value, "dd-MM-yyyy", new Date());
                                      if (!isNaN(parsedDate.getTime())) {
                                        // Validate that statute date is not before incident date
                                        const incidentDate = date;
                                        if (incidentDate && !validateStatuteDate(parsedDate, incidentDate)) {
                                          toast({
                                            title: "Invalid Date",
                                            description: "Statute of limitations date cannot be before the incident date",
                                            variant: "destructive"
                                          });
                                          return;
                                        }
                                        form.setValue("statute_of_limitations", format(parsedDate, "yyyy-MM-dd"));
                                        setStatuteDate(parsedDate);
                                      }
                                    } catch (error) {
                                      console.error("Error parsing statute date:", error);
                                    }
                                  }}
                                  error={false}
                                  onError={(message: string) => {
                                    console.error(message);
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                          
                    {/* Estimated Value - moved here */}
                    <FormField
                      control={form.control}
                      name="estimated_value"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Estimated Value
                          </FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-2">
                              <AlertTriangle className={ICON_CLASSES} />
                              <Input
                                {...field}
                                type="number"
                                placeholder="Enter Estimated Value"
                                className="bg-gray-50"
                              />
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {/* Address Fields */}
                    <div className="col-span-3 grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="street1"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Street Address 1
                            </FormLabel>
                            <FormControl>
                              <div className="flex items-center gap-2">
                                <MapPin className={ICON_CLASSES} />
                                <Input
                                  {...field}
                                  placeholder="Enter Street Address 1"
                                  className="bg-gray-50"
                                />
                              </div>
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="street2"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Street Address 2
                            </FormLabel>
                            <FormControl>
                              <div className="flex items-center gap-2">
                                <MapPin className={ICON_CLASSES} />
                                <Input
                                  {...field}
                                  placeholder="Enter Street Address 2"
                                  className="bg-gray-50"
                                />
                              </div>
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">City</FormLabel>
                            <FormControl>
                              <div className="flex items-center gap-2">
                                <MapPin className={ICON_CLASSES} />
                                <Input
                                  {...field}
                                  placeholder="Enter City"
                                  className="bg-gray-50"
                                />
                              </div>
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">State</FormLabel>
                            <Select
                              onValueChange={handleStateChange}
                              value={field.value}
                            >
                              <FormControl>
                                <div className="flex items-center gap-2">
                                  <MapPin className={ICON_CLASSES} />
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select State" />
                                  </SelectTrigger>
                                </div>
                              </FormControl>
                              <SelectContent>
                                {(field.value) && (
                                  <Button
                                    variant="ghost"
                                    className="mt-2 w-full justify-center text-sm"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      form.setValue("state", "", { shouldDirty: true });
                                      handleStateChange("");
                                    }}
                                  >
                                    Clear Selection
                                  </Button>
                                )}
                                {Object.entries(USStatesLabels).map(
                                  ([value, label]) => (
                                    <SelectItem key={value} value={value}>
                                      {label}
                                    </SelectItem>
                                  )
                                )}
                              </SelectContent>
                            </Select>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="zip_code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Zip Code
                            </FormLabel>
                            <FormControl>
                              <div className="flex items-center gap-2">
                                <MapPin className={ICON_CLASSES} />
                                <Input
                                  {...field}
                                  placeholder="Enter Zip Code"
                                  className="bg-gray-50"
                                />
                              </div>
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Incident Type */}
                    <FormField
                      control={form.control}
                      name="incident_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Incident Type
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <div className="flex items-center gap-2">
                                <AlertTriangle className={ICON_CLASSES} />
                                <SelectTrigger>
                                  <SelectValue placeholder="Select Type" />
                                </SelectTrigger>
                              </div>
                            </FormControl>
                            <SelectContent>
                              {INCIDENT_TYPE_CHOICES.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    {/* Insurance Status */}
                    <FormField
                      control={form.control}
                      name="insurance_status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Insurance Status
                          </FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-2">
                              <Shield className={ICON_CLASSES} />
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                                <Label>Insured</Label>
                              </div>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Description and Reports Section */}
                <div className="border-b py-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
                    <FileText className="h-5 w-5 text-primary" />
                    Description & Reports
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="col-span-2">
                      <FormField
                        control={form.control}
                        name="incident_description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Description
                            </FormLabel>
                            <FormControl>
                              <RichTextEditor
                                value={field.value || ""}
                                onChange={(value) => {
                                  field.onChange(value);
                                }}
                                placeholder="Enter Description"
                                className="resize-none bg-gray-50"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* <FormField
                      control={form.control}
                      name="weather_conditions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Weather Conditions
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Weather Conditions"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    /> */}
                    <FormField
                      control={form.control}
                      name="police_report_details"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Police Report Details
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Police Report Details"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="critical_conditions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Critical Conditions
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Critical Conditions"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <div className="flex items-start gap-3">
                      <FileText className={ICON_CLASSES} />
                      <div className="space-y-1 flex-1">
                        <FormField
                          control={form.control}
                          name="doc_request_status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-600">
                                Police Report Request Status
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <div className="flex items-center gap-2">
                                      <FileText className={ICON_CLASSES} />
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select Status" />
                                      </SelectTrigger>
                                    </div>
                                  </FormControl>
                                  <SelectContent>
                                    {Object.entries(
                                      DOC_REQUEST_STATUS_DISPLAY
                                    ).map(([status, display]) => (
                                      <SelectItem key={status} value={status}>
                                        {display}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* <FormField
                      control={form.control}
                      name="doc_request_notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Document Request Notes
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Document Request Notes"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    /> */}
                  </div>
                </div>

                {/* Medical Information Section */}
                <div className="border-b py-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
                    <Stethoscope className="h-5 w-5 text-primary" />
                    Medical Information
                  </h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="previous_attorney"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Previous Attorney
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter Previous Attorney"
                                className="bg-gray-50"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <div className="flex items-start gap-2">
                        <Stethoscope className="h-5 w-5 text-primary" />
                        <div className="space-y-1 flex-1">
                          <FormField
                            control={form.control}
                            name="medical_status"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-gray-600">
                                  Treatment Status
                                </FormLabel>
                                <FormControl>
                                  <Select
                                    onValueChange={field.onChange}
                                    value={field.value}
                                  >
                                    <FormControl>
                                      <div className="flex items-center gap-2">
                                        <Stethoscope className={ICON_CLASSES} />
                                        <SelectTrigger>
                                          <SelectValue placeholder="Select Status" />
                                        </SelectTrigger>
                                      </div>
                                    </FormControl>
                                    <SelectContent>
                                      {MEDICAL_STATUS_CHOICES.map((status) => (
                                        <SelectItem
                                          key={status.value}
                                          value={status.value}
                                        >
                                          {status.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      <FormField
                        control={form.control}
                        name="injury_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Injury Type
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter Injury Type"
                                className="bg-gray-50"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="injury_location"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-gray-600">
                              Injury Location
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter Injury Location"
                                className="bg-gray-50"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Injury Assessment Section */}
                    <div className="pt-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-medium flex items-center gap-2 text-gray-800">
                          <AlertTriangle className="h-5 w-5 text-primary" />
                          Injury Assessment
                        </h4>
                        <Button
                          variant="outline"
                          size="sm"
                          type="button"
                          onClick={handleAddArea}
                          className="flex items-center gap-2 hover:bg-primary/10"
                        >
                          <Plus className="h-4 w-4" />
                          Add Area
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {affectedAreas.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">
                            No affected areas added. Click &quot;Add Area&quot; to
                            add one.
                          </div>
                        ) : (
                          affectedAreas.map((area, index) => (
                            <div
                              key={index}
                              className="flex gap-3 items-start bg-gray-50 p-3 rounded-md"
                            >
                              <Select
                                value={area.part}
                                onValueChange={(value) =>
                                  handleAreaChange(index, "part", value)
                                }
                              >
                                <FormControl>
                                  <div className="flex items-center gap-2">
                                    <FileText className={ICON_CLASSES} />
                                    <SelectTrigger className="w-[200px]">
                                      <SelectValue placeholder="Select body part" />
                                    </SelectTrigger>
                                  </div>
                                </FormControl>
                                <SelectContent>
                                  {BODY_PARTS.map((part) => (
                                    <SelectItem
                                      key={part.value}
                                      value={part.value}
                                      disabled={isPartSelected(part.value, index)}
                                    >
                                      {part.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>

                              <Input
                                value={area.description}
                                onChange={(e) =>
                                  handleAreaChange(
                                    index,
                                    "description",
                                    e.target.value
                                  )
                                }
                                placeholder="Describe the injury"
                                className="bg-gray-50"
                              />

                              <Button
                                variant="ghost"
                                size="icon"
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleRemoveArea(index);
                                }}
                                className="hover:bg-red-50 hover:text-red-600"
                              >
                                <Trash2 className="h-4 w-4 text-red-600" />
                              </Button>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Legal Information Section */}
                {/* <div className="py-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
                    <Shield className="h-5 w-5 text-primary" />
                    Legal Information
                  </h3>
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="basic_facts"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Basic Facts
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Basic Facts"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="flex items-start gap-3">
                      <AlertTriangle className={ICON_CLASSES} />
                      <div className="space-y-1 flex-1">
                        <FormField
                          control={form.control}
                          name="conflicts_checked"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-600">
                                Conflicts Checked
                              </FormLabel>
                              <FormControl>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                  <Label>Yes</Label>
                                </div>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="critical_conditions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Critical Conditions
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Critical Conditions"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="flex items-start gap-3">
                      <AlertTriangle className={ICON_CLASSES} />
                      <div className="space-y-1 flex-1">
                        <FormField
                          control={form.control}
                          name="surgery_required"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-600">
                                Surgery Required
                              </FormLabel>
                              <FormControl>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                  <Label>Yes</Label>
                                </div>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="liability_assessment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-600">
                            Liability Assessment
                          </FormLabel>
                          <FormControl>
                            <RichTextEditor
                              value={field.value||"" }
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              placeholder="Enter Liability Assessment"
                              className="resize-none bg-gray-50"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div> */}
              </div> 

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {isEdit ? "Update" : "Create"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
}
