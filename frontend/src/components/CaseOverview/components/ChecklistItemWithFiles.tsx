"use client";

import React, { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Paperclip, ChevronRight, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { FileSelector } from "./FileSelector";
import { ChecklistItemFiles } from "./ChecklistItemFiles";
import { useQueryClient } from "@tanstack/react-query";
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

interface ChecklistItemWithFilesProps {
  item: {
    id: number;
    name: string;
    description?: string;
    is_completed: boolean;
    file_ids?: string[];
  };
  caseId: string;
  onToggleComplete: (id: number, isCompleted: boolean) => void;
}

export function ChecklistItemWithFiles({ item, caseId, onToggleComplete }: ChecklistItemWithFilesProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isFileSelectorOpen, setIsFileSelectorOpen] = useState(false);

  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  const handleToggleComplete = () => {
    onToggleComplete(item.id, !item.is_completed);
  };

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleOpenFileSelector = () => {
    setIsFileSelectorOpen(true);
  };

  const handleCloseFileSelector = () => {
    setIsFileSelectorOpen(false);
  };

  const handleSelectFiles = async (selectedFileIds: string[]) => {
    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_DJANGO_URL}/case-management/cases/${caseId}/update_checklist_item/`,
        {
          checklist_item_id: item.id,
          is_completed: item.is_completed,
          file_ids: selectedFileIds
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });

      toast({
        title: "Files associated",
        description: `${selectedFileIds.length} file(s) associated with checklist item`,
      });
    } catch (error) {
      console.error('Error updating checklist item:', error);
      toast({
        title: "Error",
        description: "Failed to associate files with checklist item",
        variant: "destructive",
      });
    }
  };

  const handleRemoveFile = async (fileId: string) => {
    const updatedFileIds = item.file_ids?.filter(id => id !== fileId) || [];

    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_DJANGO_URL}/case-management/cases/${caseId}/update_checklist_item/`,
        {
          checklist_item_id: item.id,
          is_completed: item.is_completed,
          file_ids: updatedFileIds
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });

      toast({
        title: "File removed",
        description: "File removed from checklist item",
      });
    } catch (error) {
      console.error('Error updating checklist item:', error);
      toast({
        title: "Error",
        description: "Failed to remove file from checklist item",
        variant: "destructive",
      });
    }
  };

  const hasFiles = item.file_ids && item.file_ids.length > 0;

  return (
    <div className="border rounded-md mb-2 overflow-hidden">
      <div
        className={cn(
          "flex items-center p-3 cursor-pointer hover:bg-gray-50",
          item.is_completed && "bg-green-50 hover:bg-green-50"
        )}
        onClick={handleToggleExpand}
      >
        <div className="mr-3">
          {isExpanded ? (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronRight className="h-4 w-4 text-gray-500" />
          )}
        </div>

        <Checkbox
          checked={item.is_completed}
          onCheckedChange={handleToggleComplete}
          onClick={(e) => e.stopPropagation()}
          className="mr-3"
        />

        <div className="flex-1 flex justify-between items-start">
          <div>
            <div className={cn(
              "font-medium",
              item.is_completed && "line-through text-gray-500"
            )}>
              {item.name}
            </div>
            {hasFiles && (
              <div className="text-xs text-blue-600 mt-1 flex items-center">
                <Paperclip className="h-3 w-3 mr-1" />
                {item.file_ids?.length} file{item.file_ids?.length !== 1 ? 's' : ''} attached
              </div>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleOpenFileSelector();
            }}
            className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 h-auto"
          >
            <Paperclip className="h-3 w-3 mr-1" />
            {hasFiles ? `${item.file_ids?.length} file${item.file_ids?.length !== 1 ? 's' : ''}` : 'Attach Files'}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-3 pt-0 border-t">
          {item.description && (
            <div className="text-sm text-gray-600 mb-3 mt-2">
              {item.description}
            </div>
          )}

          {hasFiles && (
            <ChecklistItemFiles
              fileIds={item.file_ids || []}
              onRemoveFile={handleRemoveFile}
            />
          )}
        </div>
      )}

      <FileSelector
        caseId={caseId}
        isOpen={isFileSelectorOpen}
        onClose={handleCloseFileSelector}
        onSelect={handleSelectFiles}
        initialSelectedFileIds={item.file_ids || []}
      />
    </div>
  );
}
