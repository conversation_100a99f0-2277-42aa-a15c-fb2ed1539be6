import React, { FC } from "react";
import { USStatesLabels } from "@/constants/commont";
import RichTextViewer from "@/components/ui/RichTextViewer";

interface CaseIncidentSummaryProps {
  incidentDate?: string;
  incidentType?: string;
  incidentLocation?: string;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  incidentDescription?: string;
  affectedAreas?: Record<string, string>;
}

const  CaseIncidentSummary: FC<CaseIncidentSummaryProps> = ({
  street1,
  street2,
  city,
  state,
  zip_code,
  incidentDescription = "—",
  affectedAreas = {},
}) => {
  const formatAddress = () => {
    const parts = [
      street1,
      street2,
      city,
      state ? USStatesLabels[state as keyof typeof USStatesLabels] : undefined,
      zip_code,
    ].filter(Boolean);

    return parts.length > 0 ? parts.join(", ") : "—";
  };
  console.log(formatAddress(),"formatAddress");
  const formatAffectedAreas = (areas: Record<string, string>) => {
    return Object.entries(areas)
      .map(([area, details]) => `${area}: ${details}`)
      .join(", ") || "—";
  };

  return (
    <div className="p-6 bg-white rounded-lg border border-gray-100 w-full">
      {/* Information Grid */}
      <div className="grid grid-cols-1 gap-6 mb-6 w-full">
        {/* Incident Description */}
        <div>
          <div className="text-[#060216]/50 text-xs mb-1">Incident Description</div>
          <div className="text-sm font-semibold text-[#060216] w-full">
            {incidentDescription ? <RichTextViewer data={incidentDescription} /> : "—"}
          </div>
        </div>

        {/* Injury Assessment */}
        <div>
          <div className="text-[#060216]/50 text-xs mb-1">Injury Assessment - Affected Areas</div>
          <div className="text-sm font-semibold text-[#060216]">
            {formatAffectedAreas(affectedAreas)}
          </div>
        </div>
      </div>

      {/* Address Section */}
      {/* <div>
        <div className="text-[#060216]/50 text-xs mb-1">
          <AddressLink
            address={{
              street1: street1 || undefined,
              street2: street2 || undefined,
              city: city || undefined,
              state: state || undefined,
              zip_code: zip_code || undefined,
            }}
            label="Address"
          />
        </div>
        <div className="text-sm font-semibold text-[#060216]">
          {formatAddress()}
        </div>
      </div> */}
    </div>
  );
};

export default CaseIncidentSummary; 