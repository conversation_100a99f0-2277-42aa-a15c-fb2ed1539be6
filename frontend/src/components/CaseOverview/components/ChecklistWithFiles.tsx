"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ChecklistItemWithFiles } from "./ChecklistItemWithFiles";
import { useQueryClient } from "@tanstack/react-query";
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

interface ChecklistItem {
  id: number;
  name: string;
  description?: string;
  is_completed: boolean;
  file_ids?: string[];
}

interface ChecklistWithFilesProps {
  caseId: string;
  requiredItems: ChecklistItem[];
  optionalItems: ChecklistItem[];
  title?: string;
}

export function ChecklistWithFiles({
  caseId,
  requiredItems,
  optionalItems,
  title = "Checklist"
}: ChecklistWithFilesProps) {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  // Create a single function to handle all checklist item updates
  const handleToggleComplete = async (itemId: number, isCompleted: boolean) => {
    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_DJANGO_URL}/case-management/cases/${caseId}/update_checklist_item/`,
        {
          checklist_item_id: itemId,
          is_completed: isCompleted
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });

      toast({
        title: isCompleted ? "Item completed" : "Item uncompleted",
        description: `Successfully ${isCompleted ? "completed" : "uncompleted"} the checklist item`,
      });
    } catch (error) {
      console.error('Error updating checklist item:', error);
      toast({
        title: "Error",
        description: "Failed to update checklist item",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {requiredItems.length > 0 && (
          <div className="mb-4">
            <h3 className="text-sm font-medium mb-2">Required Items</h3>
            <div className="space-y-2">
              {requiredItems.map((item) => (
                <ChecklistItemWithFiles
                  key={item.id}
                  item={item}
                  caseId={caseId}
                  onToggleComplete={handleToggleComplete}
                />
              ))}
            </div>
          </div>
        )}

        {optionalItems.length > 0 && (
          <div>
            <h3 className="text-sm font-medium mb-2">Optional Items</h3>
            <div className="space-y-2">
              {optionalItems.map((item) => (
                <ChecklistItemWithFiles
                  key={item.id}
                  item={item}
                  caseId={caseId}
                  onToggleComplete={handleToggleComplete}
                />
              ))}
            </div>
          </div>
        )}

        {requiredItems.length === 0 && optionalItems.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            No checklist items available
          </div>
        )}
      </CardContent>
    </Card>
  );
}
