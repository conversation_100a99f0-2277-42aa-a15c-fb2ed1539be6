'use client';

import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    Dialog<PERSON>ontent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Search, Plus, Minus, Edit } from "lucide-react";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { useCases } from "@/services/caseService";
import { useLeadsQuery } from "@/services/leads/leadService";
import { useCreateCaseLinkMutation, useLinkedCasesQuery, useRemoveCaseLinkMutation } from "@/services/case-management/linkCaseService";
import { useLinkedLeadsQuery, useRemoveLeadLinkMutation, useLinkExistingLeadMutation } from "@/services/leads/linkLeadService";
import { format } from "date-fns";
import { RelationshipType, CaseBasicDetails } from '@/type/linkCaseType';

const formSchema = z.object({
    search: z.string(),
});

type EntityType = 'case' | 'lead';

interface SelectLinkCaseOrLeadProps {
    entityId: string;
    entityType: EntityType;
    onClose?: () => void;
    className?: string;
}

export default function SelectLinkCaseOrLead({ entityId, entityType, onClose, className }: SelectLinkCaseOrLeadProps) {
    const [listOpen, setListOpen] = useState(false);
    const [searchParams, setSearchParams] = useState({
        search: "",
        pageSize: 10,
        page: 1
    });
    // const [quickRemoveOpen, setQuickRemoveOpen] = useState(false);

    // Case-specific hooks
    const { data: casesData, isLoading: isLoadingCases } = useCases(searchParams, entityType === 'case');
    const { data: linkedCasesData } = useLinkedCasesQuery(entityType === 'case' ? entityId : '');
    const createCaseLink = useCreateCaseLinkMutation(entityType === 'case' ? entityId : '');
    const removeCaseLink = useRemoveCaseLinkMutation(entityType === 'case' ? entityId : '');

    // Lead-specific hooks - moved outside conditional
    const { data: leadsData, isLoading: isLoadingLeads } = useLeadsQuery(searchParams);
    const { data: linkedLeadsData } = useLinkedLeadsQuery(entityType === 'lead' ? entityId : '');
    const createLeadLink = useLinkExistingLeadMutation(entityType === 'lead' ? entityId : '');
    const removeLeadLink = useRemoveLeadLinkMutation(entityType === 'lead' ? entityId : '');

    const isLoading = entityType === 'case' ? isLoadingCases : isLoadingLeads;

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            search: "",
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        setSearchParams(prev => ({
            ...prev,
            search: values.search
        }));
    }

    useEffect(() => {
        const timer = setTimeout(() => {
            if (searchParams.search) {
                form.handleSubmit(onSubmit)();
            }
        }, 500);

        return () => clearTimeout(timer);
    }, [searchParams.search]);

    const handleLinkCase = async (targetCaseId: string) => {
        try {
            await createCaseLink.mutateAsync({
                target_case: targetCaseId,
                relationship_type: RelationshipType.RELATED
            });
            setListOpen(false);
            onClose?.();
        } catch (error) {
            console.error("Error linking case:", error);
        }
    };

    const handleLinkLead = async (targetLeadId: string) => {
        try {
            await createLeadLink.mutateAsync({
                target_lead_id: targetLeadId
            });
            setListOpen(false);
            onClose?.();
        } catch (error) {
            console.error("Error linking lead:", error);
        }
    };

    const handleRemoveCaseLink = async (targetCaseId: string) => {
        try {
            await removeCaseLink.mutateAsync(targetCaseId);
            setListOpen(false);
            onClose?.();
        } catch (error) {
            console.error("Error removing case link:", error);
        }
    };

    const handleRemoveLeadLink = async (targetLeadId: string) => {
        try {
            await removeLeadLink.mutateAsync(targetLeadId);
            setListOpen(false);
            onClose?.();
        } catch (error) {
            console.error("Error removing lead link:", error);
        }
    };

    const isCaseLinked = (caseId: string) => {
        console.debug('Checking if case is linked:', {
            caseId,
            directCases: linkedCasesData?.direct_cases || [],
            indirectCases: linkedCasesData?.indirect_cases || []
        });
        const isDirectLinked = linkedCasesData?.direct_cases?.some((linkedCase: CaseBasicDetails) => linkedCase.id === caseId);
        const isIndirectLinked = linkedCasesData?.indirect_cases?.some((linkedCase: CaseBasicDetails) => linkedCase.id === caseId);

        console.debug('Link check result:', { isDirectLinked, isIndirectLinked });
        return isDirectLinked || isIndirectLinked;
    };

    const isLeadLinked = (leadId: string) => {
        const hasLinkedLeads = Array.isArray(linkedLeadsData) && linkedLeadsData.length > 0;

        if (!hasLinkedLeads) return false;

        const leadIdStr = String(leadId);
        const isLinked = linkedLeadsData.some(linkedLead => String(linkedLead.id) === leadIdStr);

        return isLinked;
    };

    const renderEntityTable = () => {
        if (entityType === 'case') {
            // Case table headers   
            const headers = [
                { key: 'case_name', label: 'Case Name', width: 'w-[300px]' },
                { key: 'client', label: 'Client' },
                { key: 'created_by', label: 'Created By' },
                { key: 'date_of_loss', label: 'Date of Loss' },
                { key: 'status', label: 'Status' },
                { key: 'actions', label: '', width: 'w-[100px]' }
            ];

            return (
                <Table>
                    <TableHeader className="sticky top-0 bg-white z-10">
                        <TableRow className="bg-[#060216]/5">
                            {headers.map(header => (
                                <TableHead
                                    key={header.key}
                                    className={`${header.width || ''} text-[#060216]/50`}
                                >
                                    {header.label}
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {casesData?.results
                            ?.filter(c => c.id.toString() !== entityId)
                            ?.sort((a, b) => {
                                const isALinked = isCaseLinked(a.id.toString());
                                const isBLinked = isCaseLinked(b.id.toString());
                                if (isALinked && !isBLinked) return -1;
                                if (!isALinked && isBLinked) return 1;
                                return 0;
                            })
                            .map((caseItem) => (
                                <TableRow
                                    key={caseItem.id}
                                    className={`cursor-pointer hover:bg-[#060216]/5 ${isCaseLinked(caseItem.id.toString()) ? 'bg-gray-50' : ''
                                        }`}
                                >
                                    <TableCell className="font-medium text-[#060216]">
                                        {caseItem.case_name}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {caseItem.name_of_client}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {caseItem.created_by?.name || "—"}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {caseItem.accident_date ?
                                            format(new Date(caseItem.accident_date), 'MM/dd/yyyy')
                                            : "—"}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {caseItem.organization_status_details?.display_name}
                                    </TableCell>
                                    <TableCell>
                                        {isCaseLinked(caseItem.id.toString()) ? (
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleRemoveCaseLink(caseItem.id.toString())}
                                            >
                                                <Minus className="h-4 w-4 text-red-500" />
                                            </Button>
                                        ) : (
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleLinkCase(caseItem.id.toString())}
                                            >
                                                <Plus className="h-4 w-4 text-green-500" />
                                            </Button>
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
            );
        } else {
            // Lead table headers
            const headers = [
                { key: 'name', label: 'Name', width: 'w-[200px]' },
                { key: 'email', label: 'Email' },
                { key: 'phone', label: 'Phone' },
                { key: 'incident_date', label: 'Incident Date' },
                { key: 'status', label: 'Status' },
                { key: 'actions', label: '', width: 'w-[100px]' }
            ];

            return (
                <Table>
                    <TableHeader className="sticky top-0 bg-white z-10">
                        <TableRow className="bg-[#060216]/5">
                            {headers.map(header => (
                                <TableHead
                                    key={header.key}
                                    className={`${header.width || ''} text-[#060216]/50`}
                                >
                                    {header.label}
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {leadsData?.results
                            ?.filter(lead => lead.id.toString() !== entityId)
                            ?.sort((a, b) => {
                                const isALinked = isLeadLinked(a.id.toString());
                                const isBLinked = isLeadLinked(b.id.toString());
                                if (isALinked && !isBLinked) return -1;
                                if (!isALinked && isBLinked) return 1;
                                return 0;
                            })
                            .map((lead) => (
                                <TableRow
                                    key={lead.id}
                                    className={`cursor-pointer hover:bg-[#060216]/5 ${isLeadLinked(lead.id.toString()) ? 'bg-gray-50' : ''
                                        }`}
                                >
                                    <TableCell className="font-medium text-[#060216]">
                                        {`${lead.first_name} ${lead.last_name}`}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {lead.email || "—"}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {lead.phone || "—"}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {lead.incident_date ?
                                            format(new Date(lead.incident_date), 'MM/dd/yyyy')
                                            : "—"}
                                    </TableCell>
                                    <TableCell className="text-[#060216]">
                                        {lead.status}
                                    </TableCell>
                                    <TableCell>
                                        {isLeadLinked(lead.id.toString()) ? (
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleRemoveLeadLink(lead.id.toString())}
                                            >
                                                <Minus className="h-4 w-4 text-red-500" />
                                            </Button>
                                        ) : (
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleLinkLead(lead.id.toString())}
                                            >
                                                <Plus className="h-4 w-4 text-green-500" />
                                            </Button>
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
            );
        }
    };

    return (
        <>
            <Dialog open={listOpen} onOpenChange={setListOpen}>
                <div className="flex items-center gap-1">
                    {/* Minus button for quickly removing links */}
                    {/* {((entityType === 'case' && (linkedCasesData?.direct_cases?.length || 0) > 0) || 
                      (entityType === 'lead' && (linkedLeadsData?.length || 0) > 0)) && (
                        <div className="relative group">
                            <Button
                                variant="outline"
                                className={`bg-transparent text-[#060216]-600 font-Manrope p-0 border-none`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    // Show a dropdown of linked entities to remove
                                    setQuickRemoveOpen(prev => !prev);
                                }}
                            >
                                <Minus className="h-4 w-4 text-red-500" />
                            </Button>
                            {quickRemoveOpen && (
                                <div className="absolute z-50 mt-1 bg-white rounded-md shadow-lg py-1 w-48 right-0">
                                    <div className="px-2 py-1 text-xs font-semibold text-gray-500">
                                        {entityType === 'case' ? 'Remove Linked Case' : 'Remove Linked Lead'}
                                    </div>
                                    {entityType === 'case' ? (
                                        // List linked cases
                                        <>
                                            {linkedCasesData?.direct_cases?.map((linkedCase) => (
                                                <div 
                                                    key={linkedCase.id}
                                                    className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                                                    onClick={() => {
                                                        handleRemoveCaseLink(linkedCase.id);
                                                        setQuickRemoveOpen(false);
                                                    }}
                                                >
                                                    <span className="truncate">{linkedCase.case_name}</span>
                                                    <Minus className="h-3 w-3 text-red-500" />
                                                </div>
                                            ))}
                                            {linkedCasesData?.indirect_cases?.map((linkedCase) => (
                                                <div 
                                                    key={linkedCase.id}
                                                    className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                                                    onClick={() => {
                                                        handleRemoveCaseLink(linkedCase.id);
                                                        setQuickRemoveOpen(false);
                                                    }}
                                                >
                                                    <span className="truncate">{linkedCase.case_name}</span>
                                                    <Minus className="h-3 w-3 text-red-500" />
                                                </div>
                                            ))}
                                        </>
                                    ) : (
                                        // List linked leads
                                        <>
                                            {linkedLeadsData?.map((linkedLead) => (
                                                <div 
                                                    key={linkedLead.id}
                                                    className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                                                    onClick={() => {
                                                        handleRemoveLeadLink(linkedLead.id);
                                                        setQuickRemoveOpen(false);
                                                    }}
                                                >
                                                    <span className="truncate">{`${linkedLead.first_name} ${linkedLead.last_name}`}</span>
                                                    <Minus className="h-3 w-3 text-red-500" />
                                                </div>
                                            ))}
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                    )} */}

                    {/* Edit button to open dialog */}
                    <Button
                        variant="outline"
                        className={`bg-transparent text-[#060216]-600 font-Manrope p-0 border-none ${className || ''}`}
                        onClick={() => setListOpen(true)}
                    >
                        <Edit className="h-4 w-4 text-green-500" />
                    </Button>
                </div>
                <DialogContent className="max-w-4xl">
                    <DialogHeader>
                        <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
                            {entityType === 'case' ? 'Link Case' : 'Link Lead'}
                        </DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="grid grid-cols-1 gap-4">
                                <FormField
                                    control={form.control}
                                    name="search"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <div className="flex items-center">
                                                    <Search className="w-4 h-4 inline-block mr-2" />
                                                    <Input
                                                        {...field}
                                                        className="bg-white"
                                                        placeholder={
                                                            entityType === 'case'
                                                                ? "Search by case name, client name, or date"
                                                                : "Search by lead name, email, or phone"
                                                        }
                                                        onChange={(e) => {
                                                            field.onChange(e);
                                                            setSearchParams(prev => ({
                                                                ...prev,
                                                                search: e.target.value
                                                            }));
                                                        }}
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                                {isLoading ? (
                                    <div className="flex justify-center items-center py-8">
                                        <Loader2 className="h-8 w-8 animate-spin text-[#060216]/50" />
                                    </div>
                                ) : (
                                    renderEntityTable()
                                )}
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="outline"
                                    type="button"
                                    onClick={() => setListOpen(false)}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>
        </>
    );
} 