"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AddressLink } from "@/components/gMap/address-link";
import React from 'react';
import {
  FileText,
  DollarSign,
  Wallet,
  Building2,
  Trash2,
  Info as InfoIcon,
  ChevronDown,
  ChevronUp,
  Phone,
  Mail,
  Hash,
  ClipboardList,
  AlertCircle,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import InsuranceForm from "@/app/case/health-insurance/components/AddEditInsurance";
import { useHealthInsurancesQuery, useDeleteHealthInsuranceMutation } from "@/services/case-management/healthInsuranceService";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { useState, MouseEvent, useEffect } from "react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
// import { PhoneLink } from "@/components/ui/phone-link";
import { KPICard } from "@/components/ui/kpi-card";
// import { MailLink } from "@/components/ui/mail-link";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import { Separator } from "@/components/ui/separator";

function HealthInsurance({ caseId }: { caseId: string }) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedInsuranceId, setSelectedInsuranceId] = useState<string>("");
  const [expandedInsuranceIds, setExpandedInsuranceIds] = useState<Set<string>>(new Set<string>());
  const deleteInsurance = useDeleteHealthInsuranceMutation();

  const { data: healthInsurances, isLoading } =
    useHealthInsurancesQuery(caseId);

  // Set all insurance items as expanded by default
  useEffect(() => {
    if (healthInsurances && healthInsurances.length > 0) {
      const allIds = new Set<string>(
        healthInsurances
          .map(insurance => insurance.id?.toString() || "")
          .filter(id => id !== "")
      );
      setExpandedInsuranceIds(allIds);
    }
  }, [healthInsurances]);

  const statistics = {
    totalLiens:
      healthInsurances?.reduce(
        (sum, insurance) =>
          sum + (parseFloat(insurance.total_lien || "0") || 0),
        0
      ) || 0,
    adjustedLiens:
      healthInsurances?.reduce(
        (sum, insurance) =>
          sum + (parseFloat(insurance.adjusted_lien || "0") || 0),
        0
      ) || 0,
  };

  const handleDelete = (insuranceId: string) => {
    setSelectedInsuranceId(insuranceId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    deleteInsurance.mutate({ caseId, insuranceId: selectedInsuranceId });
    setDeleteDialogOpen(false);
  };

  const toggleExpand = (insuranceId: string) => {
    setExpandedInsuranceIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(insuranceId)) {
        newSet.delete(insuranceId);
      } else {
        newSet.add(insuranceId);
      }
      return newSet;
    });
  };

  return (
    <div className="flex flex-col gap-8 bg-[#f5f5f5]/0">
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          Health Insurance
        </h2>
        <div className="flex items-center gap-4">
          <InsuranceForm isEdit={false} caseId={caseId} />
          <Button variant="link" className="gap-2 text-green-600">
            <FileText className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* KPI Section */}
      <div className="grid grid-cols-4 gap-4">
        <KPICard
          icon={DollarSign}
          title="TOTAL LIENS"
          metrics={{
            primary: {
              type: 'monetary',
              value: statistics.totalLiens
            }
          }}
          isCurrency={true}
        />

        <KPICard
          icon={Wallet}
          title="ADJUSTED LIENS"
          metrics={{
            primary: {
              type: 'monetary',
              value: statistics.adjustedLiens
            }
          }}
          isCurrency={true}
        />
      </div>

      {/* Insurance Details Section */}
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <div className="flex flex-col gap-4">
          {healthInsurances?.map((insurance) => {
            const insuranceId = insurance.id?.toString() || "";
            const isExpanded = expandedInsuranceIds.has(insuranceId);

            return (
              <Card
                key={insuranceId}
                className="mb-4"
              >
                <CardContent className="p-0">
                  {/* Insurance Company Header - Clickable */}
                  <div
                    className="p-6 border-b border-gray-100 cursor-pointer hover:bg-gray-50/50 transition-colors"
                    onClick={(e: MouseEvent<HTMLDivElement>) => {
                      e.preventDefault();
                      toggleExpand(insuranceId);
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-2">
                        <Building2 className="w-4 h-4" />
                        <h3 className="text-lg font-semibold">{insurance.insurance_company?.name}</h3>
                      </div>
                      <div className="flex gap-2 justify-end items-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleExpand(insuranceId);
                          }}
                        >
                          {isExpanded ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                        <div onClick={e => e.stopPropagation()}>
                          <InsuranceForm
                            isEdit={true}
                            caseId={caseId}
                            insuranceId={insuranceId}
                            defaultValues={{
                              noInsurance: insurance.no_insurance,
                              insuranceCompany: insurance.insurance_company?.id?.toString(),
                              subrogationCompany: insurance.subrogation_company?.id?.toString(),
                              insured: insurance.insured || "",
                              groupNumber: insurance.group_number || "",
                              memberNumber: insurance.member_number || "",
                              policyNumber: insurance.policy_number || "",
                              representative: insurance.representative?.id?.toString(),
                              totalLien: insurance.total_lien || "",
                              adjustedLien: insurance.adjusted_lien || "",
                              fileNumber: insurance.file_number || "",
                              planType: insurance.plan_type || "",
                              erisa: insurance.erisa || "",
                              medicare: insurance.medicare || "",
                              note: insurance.note || "",
                              final_lien: insurance.final_lien || false,
                              final_lien_date: insurance.final_lien_date || "",
                              final_amount: insurance.final_amount || "",
                            }}
                          />
                        </div>
                        <div onClick={e => e.stopPropagation()}>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(insuranceId)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <div onClick={e => e.stopPropagation()}>
                          <ManageEmailTemplate
                            caseId={caseId}
                            contextType={"client_health_insurance" as TemplateContextType}
                            healthInsuranceId={insuranceId}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Collapsible Content */}
                  {isExpanded && (
                    <div className="p-6">
                      {insurance.no_insurance ? (
                        <div className="bg-orange-50 p-4 rounded-lg">
                          <div className="flex items-center gap-3 text-orange-700">
                            <InfoIcon className="w-5 h-5" />
                            <p className="text-sm">
                              This record indicates the client has no health insurance. No additional insurance details are required.
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-8">
                          {/* Insurance Company */}
                          {/* <div className="space-y-1">
                            <span className="text-xs text-gray-500">Insurance Company</span>
                            <p className="text-sm font-semibold">{insurance.insurance_company?.name || "—"}</p>
                          </div> */}

                          {/* Contact Information */}
                          <div className="space-y-4">
                            <h3 className="text-md font-bold">Contact Information</h3>
                            <InfoFieldGroup
                              // columns={4}
                              gap="gap-x-5 gap-y-2"
                              caseId={caseId}
                              fields={[
                                {
                                  icon: Phone,
                                  label: "Primary Phone",
                                  value: insurance.insurance_company?.phone || "",
                                  isPhone: true
                                },
                                // {
                                //   icon: Phone,
                                //   label: "Secondary Phone",
                                //   value: insurance.insurance_company?.cell || "",
                                //   isPhone: true
                                // },
                                {
                                  icon: Phone,
                                  label: "Fax",
                                  value: insurance.insurance_company?.fax || "",
                                  isPhone: true
                                },
                                {
                                  icon: Mail,
                                  label: "Email",
                                  value: insurance.insurance_company?.email || "",
                                  isMail: true
                                },
                                {
                                  label: "Address",
                                  value: <AddressLink
                                    address={{
                                      street1: insurance.insurance_company?.street1,
                                      street2: insurance.insurance_company?.street2,
                                      city: insurance.insurance_company?.city,
                                      state: insurance.insurance_company?.state,
                                      zip_code: insurance.insurance_company?.zip_code
                                    }}
                                  />
                                },
                                {
                                  icon: Hash,
                                  label: "Group Number",
                                  value: insurance.group_number || "",
                                  isNumber: true
                                },
                                {
                                  icon: Hash,
                                  label: "Member Number",
                                  value: insurance.member_number || "",
                                  isNumber: true
                                },
                                {
                                  icon: FileText,
                                  label: "File Number",
                                  value: insurance.file_number || "",
                                  isNumber: true
                                },
                                {
                                  icon: DollarSign,
                                  label: "Total Lien",
                                  value: insurance.total_lien || "",
                                  isCurrency: true
                                },
                                {
                                  icon: DollarSign,
                                  label: "Adjusted Lien",
                                  value: insurance.adjusted_lien || "",
                                  isCurrency: true
                                },
                                // {
                                //   icon: ClipboardList,
                                //   label: "Notes",
                                //   value: insurance.note ? <RichTextViewer data={insurance.note} /> : ""
                                // },
                                {
                                  icon: AlertCircle,
                                  label: "ERISA",
                                  value: insurance.erisa
                                },
                                {
                                  icon: AlertCircle,
                                  label: "Medicare",
                                  value: insurance.medicare
                                }
                              ]}
                            />
                            {insurance.note && (
                              <div className="p-2 bg-gray-50 rounded-lg border border-gray-100">
                                <p className="text-xs text-gray-500 mb-1">Notes</p>
                                <div className="text-sm font-semibold">
                                  <RichTextViewer data={insurance.note} />
                                </div>
                              </div>
                            )}
                          </div>

                          <Separator />
                          {/* Subrogation Information Section */}
                          {(insurance.subrogation_company || insurance.representative) && (
                            <div className="pt-2">
                              <div className="flex items-center gap-2 mb-4">
                                <Building2 className="w-4 h-4" />
                                <h3 className="text-md font-bold">Subrogation Information</h3>
                              </div>

                              {insurance.subrogation_company && (
                                <>
                                  <InfoFieldGroup
                                    caseId={caseId}
                                    fields={[
                                      {
                                        label: "Company Name",
                                        value: insurance.subrogation_company.company || ""
                                      },
                                      {
                                        label: "Phone",
                                        value: insurance.subrogation_company.phone || "",
                                        isPhone: true
                                      },
                                      {
                                        label: "Fax",
                                        value: insurance.subrogation_company.fax || "",
                                        isPhone: true
                                      },
                                      {
                                        label: "Cell",
                                        value: insurance.subrogation_company.cell || "",
                                        isPhone: true
                                      },
                                      {
                                        label: "Email",
                                        value: insurance.subrogation_company.email || "",
                                        isMail: true
                                      }
                                    ]}
                                  />
                                </>
                              )}

                              {insurance.representative && (
                                <>
                                  <InfoFieldGroup
                                    caseId={caseId}
                                    fields={[
                                      {
                                        label: "Representative",
                                        value: `${insurance.representative.first_name} ${insurance.representative.last_name}` || ""
                                      },
                                      {
                                        icon: Phone,
                                        label: "Phone",
                                        value: insurance.representative.phone || "",
                                        isPhone: true
                                      },
                                      {
                                        icon: Mail,
                                        label: "Email",
                                        value: insurance.representative.email || "",
                                        isMail: true
                                      }
                                    ]}
                                  />
                                </>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        text="Health Insurance"
      />
    </div>
  );
}

export default function HealthBillsDetails({ caseId }: { caseId: string }) {
  return <HealthInsurance caseId={caseId} />;
}
