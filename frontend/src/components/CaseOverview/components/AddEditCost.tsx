import React, { ChangeE<PERSON>, useEffect, use<PERSON>emo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Edit } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import AddEditCostContact from "./AddEditCostContact";
import { useCostContactsQuery } from "@/services/orgAPIs";
import {
  ContactType,
  CONTACT_TYPE_CHOICES,
  OrgCostContact
} from "@/type/case-management/orgTypes";
import {
  useCreateCaseCostMutation,
  useUpdateCaseCostMutation,
} from "@/services/case-management/costService";
import {
  CaseCostCreateRequest,
  CostPriority,
  PRIORITY_CHOICES,
  PaymentType,
  CostFor,
} from "@/type/case-management/costTypes";
import { useParams } from "next/navigation";
import { Checkbox } from "@/components/ui/checkbox";
import { formatDateForInput, formatDateForApi } from "@/utils/dateUtils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { useQueryClient } from "@tanstack/react-query";
import { RequiredLabel } from "@/components/ui/required-label";

interface AddEditCostProps {
  isEdit?: boolean;
  selectedCost?: {
    id: string;
    contactType: ContactType;
    contact: string;
    amount: number;
    memo?: string;
    invoice?: string;
    priority: "regular" | "urgent" | "high";
    payment_type?: PaymentType;
    check_number?: string;
    credit_card_reference?: string;
    paid_date?: string;
    requested_date?: string;
    is_void?: boolean;
    cost_contact?: OrgCostContact;
    cost_for?: CostFor;
  };
  onSuccess?: () => void;
  defaultContactType?: ContactType;
  costFor?: CostFor;
}

const costFormSchema = z.object({
  contactType: z.nativeEnum(ContactType, {
    required_error: "Please select a contact type",
  }),
  contact: z.string({
    required_error: "Please select a contact",
  }),
  amount: z.number({
    required_error: "Please enter an amount",
  }),
  memo: z.string().optional().transform(val => val?.trim() || undefined),
  invoice: z.string().optional().transform(val => val?.trim() || undefined),
  priority: z.enum(["regular", "urgent", "high"], {
    required_error: "Please select a priority",
  }),
  payment_type: z.union([z.nativeEnum(PaymentType), z.null(), z.undefined()]).transform(val => val || undefined),
  check_number: z.string().optional().transform(val => val?.trim() || undefined),
  credit_card_reference: z.string().optional().superRefine((val, ctx) => {
    const paymentType = ((ctx as unknown) as { parent: { payment_type?: PaymentType | null } }).parent?.payment_type;

    if (paymentType === PaymentType.CREDIT_CARD) {
      if (!val?.trim() || !/^\d{4}$/.test(val)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Last 4 digits are required for credit card payments",
        });
      }
    }
    return val?.trim() || undefined;
  }),
  paid_date: z.string().optional().transform(val => val?.trim() || undefined),
  requested_date: z.string().optional().transform(val => val?.trim() || undefined),
  is_void: z.boolean().default(false),
});

type CostFormValues = z.infer<typeof costFormSchema>;

export function AddEditCost({
  isEdit = false,
  selectedCost,
  onSuccess,
  defaultContactType,
  costFor = CostFor.CASE_COST,
}: AddEditCostProps) {
  type Params = {
    caseId: string;
  };
  
  const params = useParams() as Params;
  const caseId = params.caseId;
  
  if (!caseId) {
    throw new Error('Case ID is required but not found in URL parameters');
  }

  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [costContactsLastUpdated, setCostContactsLastUpdated] = useState<number>(Date.now());
  const pageSize = 10;
  const queryClient = useQueryClient();

  const createMutation = useCreateCaseCostMutation(caseId, costFor);
  const updateMutation = useUpdateCaseCostMutation();

  const form = useForm<CostFormValues>({
    resolver: zodResolver(costFormSchema),
    defaultValues: {
      contactType: defaultContactType || undefined,
      contact: undefined,
      amount: undefined,
      priority: "regular",
      memo: "",
      invoice: "",
      payment_type: undefined,
      check_number: "",
      credit_card_reference: "",
      paid_date: "",
      requested_date: "",
      is_void: false,
    },
    mode: "onChange",
  });

  const [selectedContactObj, setSelectedContactObj] = useState<OrgCostContact | null>(selectedCost?.cost_contact || null);

  // Reset form to initial values based on selectedCost
  const resetFormToInitialValues = () => {
    form.reset({
      contactType: selectedCost?.contactType || defaultContactType || undefined,
      contact: selectedCost?.contact,
      amount: selectedCost?.amount,
      priority: selectedCost?.priority || "regular",
      memo: selectedCost?.memo || "",
      invoice: selectedCost?.invoice || "",
      payment_type: selectedCost?.payment_type,
      check_number: selectedCost?.check_number || "",
      credit_card_reference: selectedCost?.credit_card_reference || "",
      paid_date: selectedCost?.paid_date
        ? formatDateForInput(selectedCost.paid_date)
        : "",
      requested_date: selectedCost?.requested_date
        ? formatDateForInput(selectedCost.requested_date)
        : "",
      is_void: selectedCost?.is_void || false,
    });
  };

  useEffect(() => {
    if (selectedCost) {
      resetFormToInitialValues();
    }
  }, [selectedCost]);

  useEffect(() => {
    if (selectedCost?.cost_contact) {
      setSelectedContactObj(selectedCost.cost_contact);
    }
  }, [selectedCost]);

  const contactType = form.watch("contactType");

  // Reset page when contact type or search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [contactType, searchTerm]);

  const { data: costContactsResponse, isLoading: isLoadingContacts } =
    useCostContactsQuery({
      contact_type: contactType,
      search: searchTerm || undefined,
      page: currentPage,
      page_size: pageSize
    });

  const filteredContacts: OrgCostContact[] = costContactsResponse?.results || [];
  const totalContactsCount = costContactsResponse?.count || 0;
  const totalPages = Math.ceil(totalContactsCount / pageSize);

  // Create a list of dropdown options including the selected contact if it exists and not already in the filtered contacts
  const contactOptions = useMemo(() => {
    if (!selectedContactObj) return filteredContacts;
    
    // Check if the selected contact is already in the filtered results
    const selectedContactExists = filteredContacts.some(
      contact => String(contact.id) === String(selectedContactObj.id)
    );
    
    // If not, add it to the options
    if (!selectedContactExists) {
      return [selectedContactObj, ...filteredContacts];
    }
    
    return filteredContacts;
  }, [filteredContacts, selectedContactObj]);

  const paymentType = form.watch("payment_type");

  useEffect(() => {
    if (paymentType === PaymentType.CREDIT_CARD) {
      form.trigger("credit_card_reference");
    }
  }, [paymentType, form]);

  // Function to handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Search input handler with debounce
  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  async function onSubmit(data: CostFormValues) {
    try {
      // Create base cost data with required fields
      const costData: Partial<CaseCostCreateRequest> = {
        contact: Number(data.contact),
        amount: data.amount.toString(),
        priority: data.priority.toUpperCase() as CostPriority,
        is_void: data.is_void,
        cost_for: costFor,
      };

      // Add optional fields only if they have non-empty values
      if (data.memo?.trim()) costData.memo = data.memo.trim();
      if (data.invoice?.trim()) costData.invoice_number = data.invoice.trim();

      // Handle payment related fields
      if (data.payment_type) {
        costData.payment_type = data.payment_type;

        if (data.payment_type === PaymentType.CREDIT_CARD && data.credit_card_reference?.trim()) {
          costData.credit_card_reference = data.credit_card_reference.trim();
        }

        if (data.payment_type === PaymentType.CHECK && data.check_number?.trim()) {
          costData.check_number = data.check_number.trim();
        }
      }

      // Handle dates only if they have values
      if (data.paid_date?.trim()) {
        costData.paid_date = formatDateForApi(data.paid_date.trim());
      }
      if (data.requested_date?.trim()) {
        costData.requested_date = formatDateForApi(data.requested_date.trim());
      }

      if (isEdit && selectedCost) {
        await updateMutation.mutateAsync({
          caseId,
          costId: selectedCost.id,
          data: costData,
          costFor,
        });
      } else {
        await createMutation.mutateAsync(costData as CaseCostCreateRequest);
      }
      setOpen(false);
      resetFormToInitialValues();
      onSuccess?.();
    } catch (error) {
      console.error("Failed to save cost:", error);
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    // If opening the dialog, reset form to initial values
    if (newOpen) {
      resetFormToInitialValues();
      setOpen(true);
      return;
    }

    // If closing the dialog
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const handleClose = () => {
    setOpen(false);
    resetFormToInitialValues();
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {isEdit && selectedCost ? (
            <Button
              variant="link"
              size="sm"
            >
              <Edit className="h-4 w-4 text-green-600" />
            </Button>
          ) : (
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4" />
              Cost
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-[600px] flex flex-col overflow-hidden">
          <DialogHeader>
            <DialogTitle>{isEdit ? "Edit Cost" : "Add Cost"}</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
              <div className="flex-1 max-h-[75vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <div className="grid gap-6">
                  <FormField
                    control={form.control}
                    name="contactType"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Contact Type</RequiredLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            form.setValue("contact", "");
                          }}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select contact type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {CONTACT_TYPE_CHOICES.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contact"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Contact</RequiredLabel>
                        <div className="space-y-2">
                          {contactType && (
                            <div className="mb-2">
                              <Input 
                                placeholder="Search contacts..." 
                                value={searchTerm}
                                onChange={handleSearchChange}
                                className="mb-2"
                              />
                            </div>
                          )}
                          <Select 
                            onValueChange={field.onChange} 
                            value={field.value}
                            onOpenChange={(open) => {
                              if (open && contactType) {
                                queryClient.invalidateQueries({ 
                                  queryKey: ['costContacts', { contact_type: contactType }] 
                                });
                                setCostContactsLastUpdated(Date.now());
                              }
                            }}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={
                                    isLoadingContacts
                                      ? "Loading contacts..."
                                      : "Select contact"
                                  }
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="max-h-[200px]">
                              {contactOptions.length > 0 ? (
                                contactOptions.map((contact: OrgCostContact) => (
                                  <SelectItem
                                    key={`${contact.id}-${costContactsLastUpdated}`}
                                    value={String(contact.id) || ""}
                                    className="hover:bg-blue-50"
                                  >
                                    {contact.full_display_name || "Unnamed Contact"}
                                  </SelectItem>
                                ))
                              ) : (
                                <div className="relative flex items-center justify-center py-2 px-2 text-sm text-gray-500">
                                  {contactType
                                    ? searchTerm 
                                      ? `No contacts found for "${searchTerm}"`
                                      : `No contacts found for ${CONTACT_TYPE_CHOICES.find(
                                        (type) => type.value === contactType
                                      )?.label || contactType}`
                                    : "Please select a contact type first"}
                                </div>
                              )}
                            </SelectContent>
                          </Select>

                          {/* Pagination controls */}
                          {totalContactsCount > pageSize && (
                            <div className="flex items-center justify-between pt-2 text-sm">
                              <span className="text-gray-500">
                                Showing {Math.min((currentPage - 1) * pageSize + 1, totalContactsCount)} to {Math.min(currentPage * pageSize, totalContactsCount)} of {totalContactsCount}
                              </span>
                              <div className="flex space-x-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handlePageChange(currentPage - 1)}
                                  disabled={currentPage === 1}
                                  className="h-7 w-7 p-0"
                                >
                                  &lt;
                                </Button>
                                {[...Array(Math.min(totalPages, 3))].map((_, idx) => {
                                  const pageNum = idx + 1 + Math.max(0, currentPage - 2);
                                  if (pageNum > totalPages) return null;
                                  
                                  return (
                                    <Button
                                      key={pageNum}
                                      variant={currentPage === pageNum ? "default" : "outline"}
                                      size="sm"
                                      onClick={() => handlePageChange(pageNum)}
                                      className="h-7 w-7 p-0"
                                    >
                                      {pageNum}
                                    </Button>
                                  );
                                })}
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handlePageChange(currentPage + 1)}
                                  disabled={currentPage === totalPages}
                                  className="h-7 w-7 p-0"
                                >
                                  &gt;
                                </Button>
                              </div>
                            </div>
                          )}

                          <div className="flex gap-2">
                            <AddEditCostContact
                              isEdit={false}
                              onSuccess={async () => {
                                if (contactType) {
                                  await queryClient.invalidateQueries({ 
                                    queryKey: ['costContacts', { contact_type: contactType }] 
                                  });
                                  
                                  // Update timestamp to force re-render of select items
                                  setCostContactsLastUpdated(Date.now());
                                  
                                  // Clear the selection to encourage selecting the new contact
                                  form.setValue("contact", "");
                                }
                              }}
                            />
                            {field.value &&
                              contactOptions.some(
                                (c: OrgCostContact) => String(c.id) === field.value
                              ) && (
                                <AddEditCostContact
                                  key={field.value}
                                  isEdit={true}
                                  selectedContact={contactOptions.find(
                                    (c: OrgCostContact) => String(c.id) === field.value
                                  )}
                                  onSuccess={async () => {
                                    if (contactType) {
                                      await queryClient.invalidateQueries({ 
                                        queryKey: ['costContacts', { contact_type: contactType }] 
                                      });
                                      
                                      // Update timestamp to force re-render of select items
                                      setCostContactsLastUpdated(Date.now());
                                    }
                                  }}
                                />
                              )}
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Amount</RequiredLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="memo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Memo</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="payment_type"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Paid By</FormLabel>
                        <FormControl>
                          <RadioGroup
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value);
                              if (value !== PaymentType.CREDIT_CARD) {
                                form.setValue("credit_card_reference", "");
                              }
                            }}
                            className="space-y-4"
                          >
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value={PaymentType.CHECK} />
                                <FormLabel className="font-normal">Check</FormLabel>
                              </div>
                              {field.value === PaymentType.CHECK && (
                                <FormField
                                  control={form.control}
                                  name="check_number"
                                  render={({ field: checkField }) => (
                                    <FormControl>
                                      <Input
                                        {...checkField}
                                        className="max-w-[400px]"
                                        placeholder="Enter check number"
                                      />
                                    </FormControl>
                                  )}
                                />
                              )}
                            </div>

                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value={PaymentType.CREDIT_CARD} />
                                <FormLabel className="font-normal">
                                  Credit Card
                                </FormLabel>
                              </div>
                              {field.value === PaymentType.CREDIT_CARD && (
                                <FormField
                                  control={form.control}
                                  name="credit_card_reference"
                                  render={({ field: creditField }) => (
                                    <FormItem>
                                      <FormLabel className="text-sm">
                                        Last 4 digits of credit card
                                        <span className="text-red-500">
                                          *
                                        </span>
                                      </FormLabel>
                                      <FormControl>
                                        <Input
                                          {...creditField}
                                          className={`max-w-[400px] ${form.formState.errors
                                              .credit_card_reference
                                              ? "border-destructive focus-visible:ring-destructive"
                                              : ""
                                            }`}
                                          maxLength={4}
                                          pattern="\d{4}"
                                          placeholder="Enter last 4 digits"
                                          onChange={(e) => {
                                            const value = e.target.value
                                              .replace(/\D/g, "")
                                              .slice(0, 4);
                                            creditField.onChange(value);
                                            form.trigger("credit_card_reference");
                                          }}
                                          onBlur={() => {
                                            creditField.onBlur();
                                            form.trigger("credit_card_reference");
                                          }}
                                        />
                                      </FormControl>
                                      <FormMessage className="text-destructive text-sm" />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="invoice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Priority</RequiredLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Regular" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {PRIORITY_CHOICES.map((priority) => (
                                <SelectItem
                                  key={priority.value}
                                  value={priority.value.toLowerCase()}
                                >
                                  {priority.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="is_void"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Void/Refund</FormLabel>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paid_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Paid Date</FormLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value}
                            onChange={(value) => {
                              field.onChange(value);
                            }}
                            error={!!form.formState.errors.paid_date}
                            onError={(message) => {
                              form.setError("paid_date", {
                                type: "manual",
                                message: message,
                              });
                            }}
                            maxDate={new Date()}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requested_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Requested Date</FormLabel>
                        <FormControl>
                          <CustomDateInput
                            value={field.value}
                            onChange={(value) => {
                              field.onChange(value);
                            }}
                            error={!!form.formState.errors.requested_date}
                            onError={(message) => {
                              form.setError("requested_date", {
                                type: "manual",
                                message: message,
                              });
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="flex-none border-t mt-4">
                <div className="flex justify-end gap-3 py-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelClick}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      createMutation.isPending ||
                      updateMutation?.isPending
                    }
                  >
                    {isEdit ? "Save Changes" : "Add Cost"}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
}
