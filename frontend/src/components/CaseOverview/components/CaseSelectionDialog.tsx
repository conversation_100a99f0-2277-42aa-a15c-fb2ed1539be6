'use client';

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    Di<PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { useCases } from "@/services/caseService";
import { format } from "date-fns";
import { CaseResponse } from "@/type/dashboard";
interface CaseSelectionDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onCaseSelect: (caseItem: CaseResponse) => void;
    selectedCaseId?: string;
}

export default function CaseSelectionDialog({ 
    open, 
    onOpenChange, 
    onCaseSelect,
    selectedCaseId 
}: CaseSelectionDialogProps) {
    const [searchParams, setSearchParams] = useState({
        search: "",
        pageSize: 10,
        page: 1
    });

    const { data: casesData, isLoading } = useCases(searchParams, true);

    const handleSearch = (value: string) => {
        setSearchParams(prev => ({
            ...prev,
            search: value,
            page: 1
        }));
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
                        Select Case
                    </DialogTitle>
                </DialogHeader>
                <div className="space-y-6">
                    <div className="flex items-center">
                        <Search className="w-4 h-4 inline-block mr-2" />
                        <Input
                            className="bg-white"
                            placeholder="Search by case name, client name, or date"
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>

                    <div className="max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                        <Table>
                            <TableHeader className="sticky top-0 bg-white z-10">
                                <TableRow className="bg-[#060216]/5">
                                    <TableHead className="w-[300px] text-[#060216]/50">Case Name</TableHead>
                                    <TableHead className="text-[#060216]/50">Status</TableHead>
                                    <TableHead className="text-[#060216]/50">Date of Loss</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {casesData?.results?.map((caseItem) => (
                                    <TableRow
                                        key={caseItem.id}
                                        className={`cursor-pointer hover:bg-[#060216]/5 ${
                                            selectedCaseId === caseItem.id ? 'bg-[#060216]/10' : ''
                                        }`}
                                        onClick={() => onCaseSelect(caseItem)}
                                    >
                                        <TableCell className="font-medium text-[#060216]">
                                            {caseItem.case_name}
                                        </TableCell>
                                        <TableCell className="text-[#060216]">
                                            {caseItem.organization_status_details?.display_name}
                                        </TableCell>
                                        <TableCell className="text-[#060216]">
                                            {caseItem.accident_date ?
                                                format(new Date(caseItem.accident_date), 'MM/dd/yyyy')
                                                : "—"}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>

                    <div className="flex justify-end space-x-2">
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                        >
                            Cancel
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
} 