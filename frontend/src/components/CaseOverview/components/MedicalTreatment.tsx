"use client";

import React, { Fragment, useState, use<PERSON>emo, MouseEvent } from "react";
import {
  Hospital,
  ExternalLink,
  FileText,
  Calendar,
  DollarSign,
  Phone,
  Mail,
  Globe,
  UserCircle,
  Trash,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useRouter } from "next/navigation";
import SelectProver from "@/app/case/medical-treatment/components/SelectProver";
import {
  useTreatmentProvidersQuery,
  useDeleteTreatmentProviderMutation,
} from "@/services/case-management/medicalTreatmentService";
import { RecordStatus } from "@/type/case-management/medicalTreatmentTypes";
import { cn } from "@/lib/utils";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { formatDateForDisplay } from "@/utils/dateUtils";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { InfoFieldGroup } from "@/components/ui/InfoField";

interface MedicalTreatmentProps {
  caseId: string;
}

export function MedicalTreatment({ caseId }: MedicalTreatmentProps) {
  const { data: treatments = [] } = useTreatmentProvidersQuery(caseId);
  const deleteMutation = useDeleteTreatmentProviderMutation();
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState<string>("");

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalProviders = treatments.length;
    const originalBills = treatments.reduce(
      (sum, t) => sum + parseFloat(t.original_bill || "0"),
      0
    );
    const paidOwedBills = treatments.reduce(
      (sum, t) => sum + parseFloat(t.paid_plus_owed || "0"),
      0
    );
    const recsProgress =
      treatments.length > 0
        ? (treatments.filter((t) => t.records_status === RecordStatus.RECEIVED)
          .length /
          treatments.length) *
        100
        : 0;

    return {
      totalProviders,
      originalBills,
      paidOwedBills,
      recsProgress,
    };
  }, [treatments]);

  const handleCardClick = () => {
    router.push(`/case/medical-treatment/${caseId}`);
  };

  const handleDelete = async (providerId: string, e: MouseEvent) => {
    e.stopPropagation();
    setSelectedProviderId(providerId);
    setDeleteDialogOpen(true);
  };

  return (
    <div className="w-full mt-4 flex flex-col gap-8">
      {/* Statistics Section */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm text-[#060216]/50">Total Providers</h3>
            <p className="text-2xl font-bold text-[#060216]">
              {statistics.totalProviders}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm text-[#060216]/50">Original Bills</h3>
            <p className="text-2xl font-bold text-[#060216]">
              $
              {statistics.originalBills.toLocaleString("en-US", {
                minimumFractionDigits: 2,
              })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm text-[#060216]/50">Paid + Owed Bills</h3>
            <p className="text-2xl font-bold text-[#060216]">
              $
              {statistics.paidOwedBills.toLocaleString("en-US", {
                minimumFractionDigits: 2,
              })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm text-[#060216]/50">Recs / Bills In</h3>
            <Progress
              value={statistics.recsProgress}
              className="mt-2 [&>div]:bg-[#060216]"
            />
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col gap-4">
        <SelectProver caseId={caseId} />

        {treatments.map((treatment) => (
          <Card
            key={
              typeof treatment.id === "number"
                ? treatment.id
                : (treatment.id as { id: number }).id
            }
            className="hover:shadow-lg transition-shadow"
          >
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header with provider name and status */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Hospital className="h-5 w-5 text-[#060216]" />
                    <h3 className="text-[22px] font-bold font-Manrope text-[#060216]">
                      {typeof treatment.medical_provider === "number"
                        ? treatment.medical_provider
                        : (treatment.medical_provider as { company: string })
                          .company}
                    </h3>
                  </div>
                  <div className="flex items-center gap-3">
                    <span
                      className={cn(
                        "px-2 py-0.5 rounded-full text-xs font-medium",
                        treatment.treatment_status === "TREATING"
                          ? "bg-orange-100 text-orange-700"
                          : treatment.treatment_status === "COMPLETE"
                            ? "bg-green-100 text-green-700"
                            : "bg-gray-100 text-[#060216]"
                      )}
                    >
                      {treatment.treatment_status}
                    </span>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) =>
                        handleDelete(
                          typeof treatment.id === "number"
                            ? treatment.id.toString()
                            : (treatment.id as { id: number }).id.toString(),
                          e
                        )
                      }
                      className="hover:bg-gray-100"
                    >
                      <Trash className="h-4 w-4 text-red-500" />
                    </Button>
                    <ManageEmailTemplate
                      caseId={caseId}
                      healthProviderId={typeof treatment.id === "number" ? treatment.id.toString() : (treatment.id as { id: number }).id.toString()}
                      contextType={"health_provider" as TemplateContextType}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCardClick();
                      }}
                      className="hover:bg-gray-100"
                    >
                      <ExternalLink className="h-4 w-4 text-[#060216]/50" />
                    </Button>
                  </div>
                </div>

                {/* Treatment Details Grid */}
                <div className="grid grid-cols-3 gap-4 bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <InfoFieldGroup
                    fields={[
                      {
                        icon: FileText,
                        label: "records status",
                        value: treatment.records_status || "—",
                      },
                      {
                        icon: Calendar,
                        label: "first visit",
                        value: treatment.first_visit
                          ? formatDateForDisplay(treatment.first_visit)
                          : "—",
                      },
                      {
                        icon: Calendar,
                        label: "last visit",
                        value: treatment.last_visit
                          ? formatDateForDisplay(treatment.last_visit)
                          : "—",
                      },
                      {
                        icon: DollarSign,
                        label: "original bill",
                        value: Number(treatment.original_bill || 0).toLocaleString(
                          "en-US",
                          {
                            style: "currency",
                            currency: "USD",
                          }
                        ),
                      },
                      {
                        icon: DollarSign,
                        label: "still owed",
                        value: Number(treatment.still_owed || 0).toLocaleString(
                          "en-US",
                          {
                            style: "currency",
                            currency: "USD",
                          }
                        ),
                      },
                      {
                        icon: DollarSign,
                        label: "insurance paid",
                        value: Number(treatment.insurance_paid || 0).toLocaleString(
                          "en-US",
                          {
                            style: "currency",
                            currency: "USD",
                          }
                        ),
                      },
                    ]}
                  />

                  {typeof treatment?.lien_holder === "object" && (
                    <>
                      <InfoFieldGroup
                        fields={[
                          {
                            icon: Phone,
                            label: "phone",
                            value: treatment?.lien_holder?.phone || "—",
                          },
                          {
                            icon: Phone,
                            label: "phone",
                            value: treatment?.lien_holder?.phone || "—",
                          },
                          {
                            icon: Phone,
                            label: "cell",
                            value: treatment?.lien_holder?.cell || "—",
                          },
                          {
                            icon: Phone,
                            label: "fax",
                            value: treatment?.lien_holder?.fax || "—",
                          },
                          {
                            icon: Mail,
                            label: "email",
                            value: treatment?.lien_holder?.email || "—",
                          },
                          {
                            icon: Globe,
                            label: "website",
                            value: treatment?.lien_holder?.website || "—",
                          }
                        ]}
                      />
                    </>
                  )}
                  {/* Show contacts if available */}
                  {typeof treatment.medical_provider === "object" &&
                    "contacts" in treatment.medical_provider &&
                    treatment.medical_provider.contacts?.map((contact) => (
                      <Fragment key={contact.id}>
                        <InfoFieldGroup
                          caseId={caseId}
                          fields={[
                            {
                              icon: UserCircle,
                              label: `${contact.contact_type} Contact`,
                              value: contact?.name || "—",
                            },
                            {
                              icon: Phone,
                              label: `${contact.contact_type} Phone`,
                              value: contact?.phone || "—",
                            },
                            {
                              icon: Mail,
                              label: `${contact.contact_type} Email`,
                              value: contact?.email || "—",
                              isMail: true,
                            },
                          ]}
                        />
                      </Fragment>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        text="Treatment Provider"
        onConfirm={async () => {
          try {
            await deleteMutation.mutate({
              caseId,
              providerId: selectedProviderId,
            });
          } catch (error) {
            console.error("Failed to delete treatment provider:", error);
          }
          setDeleteDialogOpen(false);
        }}
      />
    </div >
  );
}
