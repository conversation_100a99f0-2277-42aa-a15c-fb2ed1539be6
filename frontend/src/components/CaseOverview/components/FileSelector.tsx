"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, File as FileIcon, Folder, Loader2, ArrowLeft } from "lucide-react";
import { useCaseFolderContentsQuery, useFolderContentsQuery, CaseContentItem, FolderContentItem } from "@/services/case-management/newDocumentManagement";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface FileSelectorProps {
  caseId: string;
  isOpen: boolean;
  onClose: () => void;
  onSelect: (selectedFileIds: string[]) => void;
  initialSelectedFileIds?: string[];
}

export function FileSelector({ caseId, isO<PERSON>, onClose, onSelect, initialSelectedFileIds = [] }: FileSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>(initialSelectedFileIds);
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<Array<{ id: string; name: string }>>([]);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());

  const { data: caseContents, isLoading: isLoadingCaseContents } = useCaseFolderContentsQuery(caseId);
  const { data: folderContents, isLoading: isLoadingFolderContents } = useFolderContentsQuery(
    currentFolderId || "",
    { enabled: !!currentFolderId }
  );

  const { toast } = useToast();

  // Reset selected files when the dialog opens with new initialSelectedFileIds
  useEffect(() => {
    if (isOpen) {
      setSelectedFileIds(initialSelectedFileIds);
      setCurrentFolderId(null);
      setFolderPath([]);
      setExpandedFolders(new Set());
    }
  }, [isOpen, initialSelectedFileIds]);

  // Get current contents based on whether we're in a folder or at root
  const currentContents = currentFolderId ? folderContents : caseContents?.contents;
  const isLoading = currentFolderId ? isLoadingFolderContents : isLoadingCaseContents;

  // Filter items based on search query and type
  const filteredItems = React.useMemo(() => {
    if (!currentContents) return { files: [], folders: [] };

    const searchLower = searchQuery.toLowerCase();
    const filtered = searchQuery
      ? currentContents.filter(item => item.name.toLowerCase().includes(searchLower))
      : currentContents;

    return {
      files: filtered.filter(item => item.type === "file"),
      folders: filtered.filter(item => item.type === "folder")
    };
  }, [currentContents, searchQuery]);

  const handleToggleFile = (fileId: string) => {
    setSelectedFileIds(prev => {
      if (prev.includes(fileId)) {
        return prev.filter(id => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  };

  const handleSelectAll = () => {
    const currentFiles = filteredItems.files;
    const allFileIds = currentFiles.map(file => file.id);

    // Check if all files in the current view are selected
    const allSelected = allFileIds.every(id => selectedFileIds.includes(id));

    if (allSelected) {
      // Deselect all files in the current view
      setSelectedFileIds(prev => prev.filter(id => !allFileIds.includes(id)));
    } else {
      // Select all files in the current view
      const newSelectedIds = [...selectedFileIds];
      allFileIds.forEach(id => {
        if (!newSelectedIds.includes(id)) {
          newSelectedIds.push(id);
        }
      });
      setSelectedFileIds(newSelectedIds);
    }
  };

  const handleOpenFolder = (folder: CaseContentItem | FolderContentItem) => {
    setCurrentFolderId(folder.id);
    setFolderPath(prev => [...prev, { id: folder.id, name: folder.name }]);
    setSearchQuery("");
  };

  const handleNavigateBack = () => {
    if (folderPath.length === 0) return;

    const newPath = [...folderPath];
    newPath.pop(); // Remove the last folder

    if (newPath.length === 0) {
      setCurrentFolderId(null);
    } else {
      setCurrentFolderId(newPath[newPath.length - 1].id);
    }

    setFolderPath(newPath);
    setSearchQuery("");
  };

  const handleSave = () => {
    onSelect(selectedFileIds);
    onClose();

    toast({
      title: "Files associated",
      description: `${selectedFileIds.length} file(s) associated with checklist item`,
    });
  };

  // Render a folder item
  const renderFolderItem = (folder: CaseContentItem | FolderContentItem, level: number = 0) => {
    const isExpanded = expandedFolders.has(folder.id);
    const paddingLeft = level * 12;

    return (
      <div key={folder.id} className="space-y-1">
        <div
          className={cn(
            "flex items-center p-2 rounded-md hover:bg-accent transition-colors cursor-pointer",
            isExpanded && "bg-accent/50"
          )}
          style={{ paddingLeft: `${paddingLeft + 8}px` }}
          onClick={() => handleOpenFolder(folder)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Folder className="h-4 w-4 text-blue-500 flex-shrink-0" />
            <span className="text-sm truncate">{folder.name}</span>
          </div>
        </div>
      </div>
    );
  };

  // Render a file item
  const renderFileItem = (file: CaseContentItem | FolderContentItem) => {
    return (
      <div
        key={file.id}
        className="flex items-center p-2 rounded-md hover:bg-accent transition-colors"
      >
        <Checkbox
          id={`file-${file.id}`}
          checked={selectedFileIds.includes(file.id)}
          onCheckedChange={() => handleToggleFile(file.id)}
        />
        <FileIcon className="ml-2 h-4 w-4 text-muted-foreground" />
        <label
          htmlFor={`file-${file.id}`}
          className="ml-2 text-sm truncate cursor-pointer flex-1"
        >
          {file.name}
        </label>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] h-[80vh] flex flex-col p-6">
        <DialogHeader className="flex-shrink-0 px-0 pb-4">
          <DialogTitle>Select Files for Checklist Item</DialogTitle>
        </DialogHeader>

        <div className="relative mb-4 flex-shrink-0">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Breadcrumb navigation */}
        {folderPath.length > 0 && (
          <div className="flex items-center mb-2 text-sm flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2"
              onClick={handleNavigateBack}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <div className="flex items-center overflow-x-auto whitespace-nowrap ml-2">
              <span
                className="text-blue-500 cursor-pointer hover:underline"
                onClick={() => {
                  setCurrentFolderId(null);
                  setFolderPath([]);
                }}
              >
                Root
              </span>
              {folderPath.map((folder, index) => (
                <React.Fragment key={folder.id}>
                  <span className="mx-1">/</span>
                  <span
                    className={cn(
                      "cursor-pointer hover:underline",
                      index === folderPath.length - 1 ? "font-medium" : "text-blue-500"
                    )}
                    onClick={() => {
                      if (index === folderPath.length - 1) return;
                      setCurrentFolderId(folder.id);
                      setFolderPath(folderPath.slice(0, index + 1));
                    }}
                  >
                    {folder.name}
                  </span>
                </React.Fragment>
              ))}
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="flex items-center mb-2 flex-shrink-0">
              <Checkbox
                id="select-all"
                checked={
                  filteredItems.files.length > 0 &&
                  filteredItems.files.every(file => selectedFileIds.includes(file.id))
                }
                onCheckedChange={handleSelectAll}
              />
              <label htmlFor="select-all" className="ml-2 text-sm font-medium">
                Select All Files ({filteredItems.files.length})
              </label>
              <span className="ml-auto text-sm text-muted-foreground">
                {selectedFileIds.length} selected
              </span>
            </div>

            <div className="flex-1 min-h-0 overflow-auto border rounded-md">
              {filteredItems.folders.length === 0 && filteredItems.files.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  {searchQuery ? "No items match your search" : "No items available"}
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {/* Render folders first */}
                  {filteredItems.folders.map(folder => renderFolderItem(folder))}

                  {/* Render files */}
                  {filteredItems.files.map(file => renderFileItem(file))}
                </div>
              )}
            </div>
          </>
        )}

        <DialogFooter className="mt-4 flex-shrink-0 px-0 pt-2">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave} disabled={isLoading}>
            Associate Files ({selectedFileIds.length})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
