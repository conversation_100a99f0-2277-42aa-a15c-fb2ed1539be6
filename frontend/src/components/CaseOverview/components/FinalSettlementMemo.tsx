import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { CurrencyDisplay } from "@/components/ui/currency-display";
import RichTextViewer from "@/components/ui/RichTextViewer";

/**
 * Props for a single memo section.
 */
export interface MemoSection {
  /** Section title, e.g., 'RECOVERY', 'DEDUCT AND RETAIN TO PAY OTHERS' */
  title: string;
  /**
   * List of line items for the section.
   * Each item has a label/description and an amount (number or string).
   */
  items: { label: string; amount: number | string }[];
  /**
   * Optional: Show a bolded total at the end of the section.
   */
  totalLabel?: string;
  totalAmount?: number | string;
}

/**
 * Props for the FinalSettlementMemo component.
 */
export interface FinalSettlementMemoProps {
  /** Client name */
  clientName: string;
  /** Date of loss or memo date */
  date: string;
  /** List of sections to render in order */
  sections: MemoSection[];
  /** Net amount due to client */
  netToClient: number | string;
  /** Optional: Footer note */
  footerNote?: string;
  /** Optional: Custom memo title */
  memoTitle?: string;
}

/**
 * Reusable, styled memo preview component for settlement memos.
 *
 * @param props FinalSettlementMemoProps
 */
const FinalSettlementMemo: React.FC<FinalSettlementMemoProps> = ({
  clientName,
  date,
  sections,
  netToClient,
  footerNote,
  memoTitle = "FINAL SETTLEMENT MEMORANDUM",
}) => {
  return (
    <Card className="w-full max-w-2xl mx-auto p-8 shadow-none bg-white">
      <div className="text-center font-medium text-base mb-2">Brumley Law Firm, PLLC</div>
      <CardHeader className="pb-2 px-0">
        <div className="text-sm">
          <p className="font-medium inline-block mr-2">Client Name:</p>
          <span className="font-normal">{clientName}</span>
        </div>
        <div className="text-sm mb-4">
          <p className="font-medium inline-block mr-2">DOL:</p>
          <span className="font-normal">{date}</span>
        </div>
        <CardTitle className="text-center font-bold text-base mt-6 mb-4 tracking-wide">
          {memoTitle}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-8 px-0">
        {sections.map((section, idx) => (
          <div key={section.title + idx} className="mb-2">
            <p className="font-bold text-sm mb-1">{section.title}</p>
            <div className="p-2">
              {section.items.map((item, i) => (
                <div key={i} className="flex justify-between mb-1">
                  <span>{item.label ? <RichTextViewer data={item.label} /> : item.label}</span>
                  <span className="text-right min-w-[100px]">
                    <CurrencyDisplay amount={item.amount} />
                  </span>
                </div>
              ))}
              {section.totalLabel && (
                <div className="flex justify-between font-bold border-t border-black pt-1 mt-2 text-base">
                  <span>{section.totalLabel}</span>
                  <span className="text-right min-w-[100px]">
                    <CurrencyDisplay amount={section.totalAmount ?? 0} />
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
        <div className="border-t-2 border-black pt-4 pb-2">
          <div className="flex justify-between font-bold text-base">
            <span>Net Amount Due to Client:</span>
            <span className="text-right min-w-[100px]">
              <CurrencyDisplay amount={netToClient ?? 0} />
            </span>
          </div>
        </div>
        {footerNote && (
          <div className="mt-6">
            <p className="text-sm text-left">{footerNote}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FinalSettlementMemo; 