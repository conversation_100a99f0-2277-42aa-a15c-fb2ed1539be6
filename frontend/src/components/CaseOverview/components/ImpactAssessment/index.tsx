"use client";

import React, { useState, ChangeEvent } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  useImpactAssessmentQuery, 
  useCreateImpactAssessmentMutation, 
  useUpdateImpactAssessmentMutation 
} from "@/services/case-management/ImpactAssessmentService";
import { ImpactAssessmentFormData } from "@/type/case-management/impactAssessmentTypes";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { AlertCircle, Save, Edit, Activity } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ImpactAssessmentProps {
  caseId: string;
}

export function ImpactAssessment({ caseId }: ImpactAssessmentProps) {
  const [activeTab, setActiveTab] = useState("economic-impact");
  const [isEditing, setIsEditing] = useState(false);
  
  const { data: impactAssessment, isLoading, isError } = useImpactAssessmentQuery(caseId);
  
  const createMutation = useCreateImpactAssessmentMutation(caseId);
  const updateMutation = useUpdateImpactAssessmentMutation(caseId);
  
  const [formData, setFormData] = useState<ImpactAssessmentFormData>({
    loss_of_income: impactAssessment?.loss_of_income || "",
    out_of_pocket_expenses: impactAssessment?.out_of_pocket_expenses || "",
    household_services: impactAssessment?.household_services || "",
    additional_facts: impactAssessment?.additional_facts || "",
    impact_on_recreation: impactAssessment?.impact_on_recreation || "",
    impact_on_entertainment: impactAssessment?.impact_on_entertainment || "",
    impact_on_home_activities: impactAssessment?.impact_on_home_activities || "",
    impact_on_social_time: impactAssessment?.impact_on_social_time || "",
    impact_on_self_care: impactAssessment?.impact_on_self_care || "",
    additional_impact: impactAssessment?.additional_impact || ""
  });
  
  // Update form data when impact assessment data is loaded
  React.useEffect(() => {
    if (impactAssessment) {
      setFormData({
        loss_of_income: impactAssessment.loss_of_income || "",
        out_of_pocket_expenses: impactAssessment.out_of_pocket_expenses || "",
        household_services: impactAssessment.household_services || "",
        additional_facts: impactAssessment.additional_facts || "",
        impact_on_recreation: impactAssessment.impact_on_recreation || "",
        impact_on_entertainment: impactAssessment.impact_on_entertainment || "",
        impact_on_home_activities: impactAssessment.impact_on_home_activities || "",
        impact_on_social_time: impactAssessment.impact_on_social_time || "",
        impact_on_self_care: impactAssessment.impact_on_self_care || "",
        additional_impact: impactAssessment.additional_impact || ""
      });
    }
  }, [impactAssessment]);
  
  const handleInputChange = (field: keyof ImpactAssessmentFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleSave = async () => {
    try {
      if (impactAssessment) {
        await updateMutation.mutateAsync(formData);
      } else {
        await createMutation.mutateAsync(formData);
      }
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to save impact assessment:", error);
    }
  };

  // Helper function to display values properly based on their type
  const displayValue = (value: string | number | undefined, isEconomic: boolean = false): React.ReactNode => {
    if (value === undefined || value === null || value === "") {
      return <p className="text-gray-500 italic">No information provided</p>;
    }
    
    // For economic fields, format as currency if it's a number
    if (isEconomic && (typeof value === 'number' || !isNaN(Number(value)))) {
      return <p className="font-medium">${Number(value).toFixed(2)}</p>;
    }
    
    // For text content, display as is
    return <p>{value.toString()}</p>;
  };
  
  if (isLoading) {
    return <div className="p-4">Loading impact assessment data...</div>;
  }
  
  if (isError) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load impact assessment data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Impact Assessment
        </h2>
        <Button 
          variant={isEditing ? "default" : "outline"} 
          onClick={() => isEditing ? handleSave() : setIsEditing(true)}
        >
          {isEditing ? (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save
            </>
          ) : (
            <>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </>
          )}
        </Button>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="economic-impact">Economic Impact</TabsTrigger>
          <TabsTrigger value="quality-of-life">Quality of Life Impact</TabsTrigger>
        </TabsList>
        
        <TabsContent value="economic-impact" className="space-y-4 mt-4">
          <Card>
            <CardContent className="pt-6">
              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="loss_of_income">Loss of Income</Label>
                    <Textarea
                      id="loss_of_income"
                      value={formData.loss_of_income}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("loss_of_income", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="out_of_pocket_expenses">Out of Pocket Expenses</Label>
                    <Textarea
                      id="out_of_pocket_expenses"
                      value={formData.out_of_pocket_expenses}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("out_of_pocket_expenses", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="household_services">Household Services</Label>
                    <Textarea
                      id="household_services"
                      value={formData.household_services}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("household_services", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="additional_facts">Additional Facts</Label>
                    <Textarea
                      id="additional_facts"
                      value={formData.additional_facts}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("additional_facts", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Loss of Income</h3>
                    {displayValue(formData.loss_of_income, true)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Out of Pocket Expenses</h3>
                    {displayValue(formData.out_of_pocket_expenses, true)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Household Services</h3>
                    {displayValue(formData.household_services, true)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Additional Facts</h3>
                    {displayValue(formData.additional_facts)}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="quality-of-life" className="space-y-4 mt-4">
          <Card>
            <CardContent className="pt-6">
              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="impact_on_recreation">Impact on Recreation</Label>
                    <Textarea
                      id="impact_on_recreation"
                      value={formData.impact_on_recreation}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("impact_on_recreation", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="impact_on_entertainment">Impact on Entertainment</Label>
                    <Textarea
                      id="impact_on_entertainment"
                      value={formData.impact_on_entertainment}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("impact_on_entertainment", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="impact_on_home_activities">Impact on Home Activities</Label>
                    <Textarea
                      id="impact_on_home_activities"
                      value={formData.impact_on_home_activities}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("impact_on_home_activities", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="impact_on_social_time">Impact on Social Time</Label>
                    <Textarea
                      id="impact_on_social_time"
                      value={formData.impact_on_social_time}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("impact_on_social_time", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="impact_on_self_care">Impact on Self Care</Label>
                    <Textarea
                      id="impact_on_self_care"
                      value={formData.impact_on_self_care}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("impact_on_self_care", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="additional_impact">Additional Impact</Label>
                    <Textarea
                      id="additional_impact"
                      value={formData.additional_impact}
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => handleInputChange("additional_impact", e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Impact on Recreation</h3>
                    {displayValue(formData.impact_on_recreation)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Impact on Entertainment</h3>
                    {displayValue(formData.impact_on_entertainment)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Impact on Home Activities</h3>
                    {displayValue(formData.impact_on_home_activities)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Impact on Social Time</h3>
                    {displayValue(formData.impact_on_social_time)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Impact on Self Care</h3>
                    {displayValue(formData.impact_on_self_care)}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Additional Impact</h3>
                    {displayValue(formData.additional_impact)}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 