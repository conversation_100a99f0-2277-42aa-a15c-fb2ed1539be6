import React, { useState, useCallback, useEffect, DragEvent, FC, ChangeEvent, Fragment } from 'react';
import {
  Folder,
  File as FileIcon,
  Upload,
  Plus,
  ArrowLeft,
  Loader2,
  Pin,
  PinOff,
  X,
  ExternalLink,
  MessageSquare,
  Trash2,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from "@/components/ui/progress";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
} from '@/components/ui/context-menu';
import {
  useCreateFolderMutation,
  useCaseFolderContentsQuery,
  useFolderContentsQuery,
  type CaseContentItem,
  type FolderContentItem,
  type CreateFolderRequest,
  useFileUploadMutation,
  usePinFileMutation,
  usePinnedFileQuery,
  useFileDownloadMutation,
  useFileViewLinkQuery,
  useFileUploadFromUrlMutation,
  useRenameFileMutation,
  type FileViewLinkResponse,
  useDeleteFolderMutation,
  useDeleteFileMutation,
} from '@/services/case-management/newDocumentManagement';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { FileManagerProps } from '../types/fileManager.types';
import { DragOverlay } from './DragOverlay';
import { Document } from '@/type/doocument';
import { useDocumentContext } from '@/contexts/DocumentContext';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import axios from 'axios';
import { useProcessFileMutation, useProcessedFilesQuery, type ProcessedFile, useDeleteProcessedFileMutation } from '@/services/ai-service/chat-with-case.service';
import Image from 'next/image';

// Define BASE_URL
const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/storage';

interface UploadingFile {
  file: File;
  progress: number;
  error?: string;
}

// Add isImageFile helper function
const isImageFile = (fileName: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  const lowerFileName = fileName.toLowerCase();
  return imageExtensions.some(ext => lowerFileName.endsWith(ext));
};

// Add these helper functions at the top of the file, after imports
const getFileNameAndExtension = (filename: string) => {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) return { name: filename, extension: '' };
  return {
    name: filename.substring(0, lastDotIndex),
    extension: filename.substring(lastDotIndex)
  };
};

// Add a new interface for image previews
interface ImagePreview {
  fileId: string;
  previewUrl: string;
}

// Add this after other interfaces
interface ImagePreviewDialogProps {
  imageUrl?: string;
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
}

const ImagePreviewDialog: FC<ImagePreviewDialogProps> = ({
  imageUrl,
  isOpen,
  onClose,
  fileName
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="relative max-w-3xl max-h-[80vh] bg-white rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">{fileName}</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="relative">
          {imageUrl ? (
            <div className="relative w-full h-[70vh]">
              <Image
                src={imageUrl}
                alt={fileName}
                fill
                className="object-contain"
                unoptimized // Since we're using external URLs
                priority
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-40">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Add this helper function near other helper functions
const isSupportedForChat = (fileName: string): boolean => {
  const supportedExtensions = ['.docx', '.pdf', '.doc', '.jpeg', '.jpg', '.png'];
  const lowerFileName = fileName.toLowerCase();
  return supportedExtensions.some(ext => lowerFileName.endsWith(ext));
};

// Add new interface for the confirmation dialog
interface ChatWithCaseDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  fileName: string;
  fileType: string;
  isProcessing?: boolean;
}

// Add new ChatWithCaseDialog component
const ChatWithCaseDialog: FC<ChatWithCaseDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  fileType,
  isProcessing = false
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add to Case Chat</DialogTitle>
          <DialogDescription>
            This will index the file content for chat interactions.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col space-y-4 py-4">
          <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
            <FileIcon className="h-8 w-8 text-blue-500" />
            <div className="flex-1">
              <p className="font-medium">{fileName}</p>
              <p className="text-sm text-gray-500 uppercase">{fileType}</p>
            </div>
          </div>
          <div className="flex items-start space-x-2">
            <div className="p-2 bg-blue-50 rounded-full">
              <MessageSquare className="h-4 w-4 text-blue-500" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">Enable Chat Interactions</p>
              <p className="text-sm text-gray-500">
                Once indexed, you can ask questions about this document&apos;s content in the case chat.
              </p>
            </div>
          </div>
        </div>
        <DialogFooter className="flex space-x-2 sm:justify-end">
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button 
            onClick={onConfirm} 
            className="bg-blue-600 hover:bg-blue-700"
            disabled={isProcessing}
          >
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Add to Chat
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Add this helper function near other helpers
const getStatusColor = (status: string) => {
  switch (status?.toUpperCase()) {
    case 'COMPLETED':
      return 'bg-green-500';
    case 'PROCESSING':
      return 'bg-yellow-500';
    case 'FAILED':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const FileManager: FC<FileManagerProps> = ({ caseId }) => {
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [deleteConfirmation, setDeleteConfirmation] = useState<{ type: 'file' | 'folder', item: FolderContentItem | CaseContentItem } | null>(null);
  const [selectedFile, setSelectedFile] = useState<FolderContentItem | CaseContentItem | null>(null);
  const [folderHistory, setFolderHistory] = useState<{ id: string, name: string }[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [rootFolderId, setRootFolderId] = useState<string | null>(null);
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [fileToRename, setFileToRename] = useState<FolderContentItem | CaseContentItem | null>(null);
  const [fileExtension, setFileExtension] = useState('');
  // Add new state for image previews
  const [imagePreviews, setImagePreviews] = useState<ImagePreview[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  // Replace the single query with two conditional queries
  const {
    data: caseContents,
    isLoading: isLoadingCaseContents,
    isFetching: isFetchingCaseContents,
    refetch: refetchCaseContents
  } = useCaseFolderContentsQuery(caseId);

  const {
    data: folderContents,
    isLoading: isLoadingFolderContents,
    isFetching: isFetchingFolderContents,
    refetch: refetchFolderContents
  } = useFolderContentsQuery(currentFolderId || caseContents?.root_folder?.id || '', {
    enabled: !!currentFolderId
  });

  // Determine which data to use based on current folder
  const contents = currentFolderId ?
    (folderContents || []) :
    (caseContents?.contents || []);

  const folders = contents.filter(item => item.type === 'folder');
  const files = contents.filter(item => item.type === 'file');

  // Store root folder id when it's received
  useEffect(() => {
    if (caseContents?.root_folder?.id) {
      console.log('Setting root folder ID:', caseContents.root_folder.id);
      setRootFolderId(caseContents.root_folder.id);
    }
  }, [caseContents?.root_folder]);

  // Add new hooks for file operations
  const fileDownload = useFileDownloadMutation();
  const { refetch: refetchViewLink } = useFileViewLinkQuery(
    selectedFile?.id?.toString()?.trim() || '',
    60  // 60 minutes expiration
  );

  // Create a ref to track which files we've already requested previews for
  const requestedPreviews = React.useRef<Set<string>>(new Set());
  // Add ref to track previous folder ID
  const previousFolderIdRef = React.useRef<string | null>(null);

  // Add a function to fetch image preview URLs
  const fetchImagePreview = useCallback(async (fileId: string) => {
    // Skip if we've already requested this preview
    if (requestedPreviews.current.has(fileId)) {
      return;
    }
    
    // Mark this file as requested
    requestedPreviews.current.add(fileId);
    
    try {
      const { data } = await axios.get<FileViewLinkResponse>(
        `${BASE_URL}/file/${fileId}/view-link/`,
        {
          params: { expiration_minutes: 60 },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      
      if (data?.view_link) {
        setImagePreviews((prev: ImagePreview[]) => {
          // Add the new preview
          return [...prev, { fileId, previewUrl: data.view_link }];
        });
      }
    } catch (error) {
      console.error('Error fetching image preview:', error);
      // Remove from requested set on error so we can try again
      requestedPreviews.current.delete(fileId);
    }
  }, [accessToken]);

  // Helper function to get image preview URL
  const getImagePreviewUrl = useCallback((fileId: string | undefined): string | undefined => {
    if (!fileId) return undefined;
    return imagePreviews.find((p: ImagePreview) => p.fileId === fileId)?.previewUrl;
  }, [imagePreviews]);

  // Single useEffect for handling image previews
  useEffect(() => {
    // Check if folder has actually changed
    if (currentFolderId !== previousFolderIdRef.current) {
      // Clear previews and requested previews only when folder changes
      setImagePreviews([]);
      requestedPreviews.current.clear();
      previousFolderIdRef.current = currentFolderId;
      return; // Exit early if we're just clearing
    }
    
    // Skip if no files
    if (!files.length) return;
    
    // Only fetch previews for image files that haven't been requested yet
    const imageFiles = files.filter(file => 
      isImageFile(file.name) && 
      file.id && 
      !requestedPreviews.current.has(file.id.toString())
    );
    
    // Limit to a reasonable number to avoid too many requests
    const filesToFetch = imageFiles.slice(0, 20);
    
    // Fetch previews for all image files
    filesToFetch.forEach(file => {
      if (file.id) {
        fetchImagePreview(file.id.toString());
      }
    });
  }, [currentFolderId, files, fetchImagePreview]);

  // Mutations
  const createFolder = useCreateFolderMutation();
  const deleteFolder = useDeleteFolderMutation(
    deleteConfirmation?.type === 'folder' && deleteConfirmation.item.id ? deleteConfirmation.item.id.toString() : ''
  );
  const deleteFile = useDeleteFileMutation(
    deleteConfirmation?.type === 'file' && deleteConfirmation.item.id ? deleteConfirmation.item.id.toString() : ''
  );
  const fileUpload = useFileUploadMutation();
  const pinFile = usePinFileMutation();
  const { data: pinnedFile } = usePinnedFileQuery(caseId);
  const fileUploadFromUrl = useFileUploadFromUrlMutation();
  const renameFile = useRenameFileMutation(fileToRename?.id?.toString() || '', accessToken || '');

  // Add state for drag overlay
  const [isDraggingImage, setIsDraggingImage] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);
  const { setSelectedDocuments, setActiveDocumentId, selectedDocuments } =
    useDocumentContext();

  // Add these new state variables after other state declarations
  const [previewImage, setPreviewImage] = useState<{
    url?: string;
    isOpen: boolean;
    fileName: string;
  }>({
    isOpen: false,
    fileName: '',
  });

  // Add this with other state declarations at the top
  const [showChatDialog, setShowChatDialog] = useState<string | null>(null); // Will store file ID when dialog should be shown

  // Add the process file mutation
  const processFile = useProcessFileMutation();

  // Add the processed files query
  const { data: processedFiles } = useProcessedFilesQuery(caseId);
  
  // Add helper function to get file status
  const getFileStatus = useCallback((fileId: string): ProcessedFile | undefined => {
    return processedFiles?.data?.files.find(
      (f) => f.file_id === fileId
    );
  }, [processedFiles]);

  // Update the handleAddToChat function
  const handleAddToChat = async (fileId: string) => {
    try {
      const response = await processFile.mutateAsync({
        file_id: fileId,
        case_id: caseId
      });

      // Invalidate the processed files query to trigger a refetch
      queryClient.invalidateQueries({
        queryKey: ['processedFiles', caseId]
      });

      toast({
        title: "Success",
        description: response.message || "File processing started",
      });
      setShowChatDialog(null);

      // Optionally start polling for status
      // You can use the returned task_id with your useOcrStatusQuery
    } catch (error) {
      console.error('Error adding file to case chat:', error);
      toast({
        title: "Error",
        description: "Failed to add file to case chat",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {

    const fetchViewLink = async () => {
      try {
        const { data } = await refetchViewLink();
        if (data?.view_link && selectedFile) {
          const newDocument: Document = {
            id: selectedFile.id,
            name: selectedFile.name,
            url: data.view_link,
            content_type: 'file',
          };

          console.log("newDocument", newDocument);

          const exists = selectedDocuments.some(
            (doc: Document) => doc.id === selectedFile.id
          );

          if (!exists) {
            setSelectedDocuments([...selectedDocuments, newDocument]);
          }
          setActiveDocumentId(selectedFile.id);

          // Reset selectedFile after processing
          setSelectedFile(null);
        // } else {
        //   console.log(`☄️☄️ error at view link in fetchViewLink ----------------------->☄️☄️`);
          // toast({
          //   title: "Error",
          //   description: "Could not generate view link",
          //   variant: "destructive",
          // });
        } 
      } catch (error) {
        console.error('Error viewing file:', error);
        toast({
          title: "Error",
          description: "Failed to view file in fetchViewLink",
          variant: "destructive",
        });
      }
    };
    if (!!selectedFile?.id) {
      fetchViewLink();
    }
  }, [selectedFile, selectedDocuments, setSelectedDocuments, setActiveDocumentId]);

  const updateUploadProgress = (file: File, progress: number) => {
    setUploadingFiles(prev =>
      prev.map(f =>
        f.file === file ? { ...f, progress } : f
      )
    );
  };

  const handleUploadError = (file: File, error: string) => {
    setUploadingFiles(prev =>
      prev.map(f =>
        f.file === file ? { ...f, error } : f
      )
    );
    toast({
      title: "Upload Failed",
      description: `Failed to upload ${file.name}: ${error}`,
      variant: "destructive",
    });
  };

  const handleCreateFolder = async () => {
    if (newFolderName.trim()) {
      try {
        // Get the parent folder ID - either current folder or root folder
        const parentFolderId = currentFolderId || caseContents?.root_folder?.id || null;

        // Create the payload according to the CreateFolderRequest interface
        const payload: CreateFolderRequest = {
          folder_name: newFolderName,
          parent_id: parentFolderId
        };

        await createFolder.mutateAsync(payload);

        // Clear form and close dialog
        setNewFolderName('');
        setShowNewFolderDialog(false);
        refetchCaseContents();
        refetchFolderContents();

        // Refresh the appropriate query based on current location
        if (currentFolderId) {
          queryClient.invalidateQueries({
            queryKey: ['folderContents', currentFolderId]
          });
        } else {
          queryClient.invalidateQueries({
            queryKey: ['caseContents', caseId]
          });
        }

      } catch (error) {
        console.error('Error creating folder:', error);
        toast({
          title: "Error",
          description: "Failed to create folder",
          variant: "destructive",
        });
      }
    }
  };

  const handleUpload = async (fileOrUrl: File | string) => {
    try {
      // Get the target folder ID - either current folder or root folder
      const targetFolderId = currentFolderId || caseContents?.root_folder?.id;

      if (!targetFolderId) {
        toast({
          title: "Error",
          description: 'No folder ID available for upload',
          variant: "destructive"
        });
        return;
      }

      if (typeof fileOrUrl === 'string') {
        // Handle URL upload
        console.log('Starting URL upload', {
          url: fileOrUrl,
          folderId: targetFolderId,
        });

        await fileUploadFromUrl.mutateAsync({
          folder_id: targetFolderId,
          file_url: fileOrUrl
        });
      } else {
        // Handle file upload
        console.log('Starting file upload', {
          file: fileOrUrl.name,
          folderId: targetFolderId,
        });

        await fileUpload.mutateAsync({
          folder_id: targetFolderId,
          file: fileOrUrl
        });
      }

      refetchCaseContents();
      refetchFolderContents();

    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  // Replace dropzone handlers with native drag and drop handlers
  const handleDragOver = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);

    const types = Array.from(e.dataTransfer.types);
    if (types.includes('text/uri-list') || types.includes('text/html')) {
      setIsDraggingImage(true);
    }
  }, []);

  const handleDragLeave = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragActive(false);
    setIsDraggingImage(false);
  }, []);

  const handleDrop = useCallback(async (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    setIsDraggingImage(false);

    // Ensure we have a root folder ID if we're in the root
    if (!currentFolderId && !caseContents?.root_folder?.id) {
      toast({
        title: "Error",
        description: "Root folder not available. Please try again.",
        variant: "destructive"
      });
      return;
    }

    // Handle dropped image elements
    if (e.dataTransfer.getData('text/html')) {
      const html = e.dataTransfer.getData('text/html');
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      const imgElement = tempDiv.querySelector('img');

      if (imgElement?.src) {
        try {
          await handleUpload(imgElement.src);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed';
          console.error('Image URL upload error:', errorMessage);
          toast({
            title: "Error",
            description: `Failed to upload image from URL: ${errorMessage}`,
            variant: "destructive"
          });
        }
        return;
      }
    }

    // Handle dropped files
    const files = Array.from(e.dataTransfer.files);
    const acceptedFiles = files.filter(file => file.size <= 100 * 1024 * 1024); // 100MB limit

    const newUploadingFiles = acceptedFiles.map(file => ({
      file,
      progress: 0,
    }));
    setUploadingFiles(prev => [...prev, ...newUploadingFiles]);

    for (const file of acceptedFiles) {
      try {
        const interval = setInterval(() => {
          updateUploadProgress(file, Math.random() * 100);
        }, 200);

        await handleUpload(file);

        clearInterval(interval);
        updateUploadProgress(file, 100);

        setTimeout(() => {
          setUploadingFiles(prev => prev.filter(f => f.file !== file));
        }, 1000);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';
        console.error('File upload error:', errorMessage);
        handleUploadError(file, errorMessage);
      }
    }
  }, [caseId, currentFolderId, caseContents?.root_folder?.id, handleUpload]);

  // Add this function before renderDropZone
  const getDropzoneText = () => {
    if (!currentFolderId && (!rootFolderId || isLoadingCaseContents || isFetchingCaseContents)) {
      return "Loading folder structure...";
    }
    return "Drag & drop files here, or click to select files";
  };

  // Replace dropzone UI with native drag and drop
  const renderDropZone = () => (
    <div
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => document.getElementById('fileInput')?.click()}
      className={`
        relative border-2 border-dashed rounded-lg p-8 mb-4 transition-colors cursor-pointer
        ${isDragActive || isDraggingImage ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
        ${(!currentFolderId && (!rootFolderId || isLoadingCaseContents || isFetchingCaseContents)) ? 'opacity-50' : ''}
      `}
    >
      <input
        id="fileInput"
        type="file"
        multiple
        onChange={handleFileSelect}
        className="hidden"
        accept="image/*,.pdf,.doc,.docx,.txt,video/*,.mov,audio/*"
      />
      <div className="text-center">
        <Upload className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2">
          {getDropzoneText()}
          {currentFolderId && ` (uploading to ${getCurrentFolderName()})`}
        </p>
        <p className="text-sm text-gray-500">
          Drag files or images from your computer or from the page
        </p>
        <p className="text-sm text-gray-500">Maximum file size: 100MB</p>
        {!currentFolderId && (!rootFolderId || isLoadingCaseContents || isFetchingCaseContents) && (
          <div className="mt-2 flex items-center justify-center">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span className="text-sm text-gray-500">Loading folder structure...</span>
          </div>
        )}
      </div>

      {isDragActive || isDraggingImage ? (
        <DragOverlay
          isDragActive={isDragActive}
          isDraggingImage={isDraggingImage}
          uploadingFiles={uploadingFiles}
        />
      ) : null}
    </div>
  );

  const handleDelete = async () => {
    if (!deleteConfirmation) return;

    console.log('Starting deletion process', {
      type: deleteConfirmation.type,
      itemId: deleteConfirmation.item.id,
      currentFolderId
    });

    try {
      if (deleteConfirmation.type === 'folder') {
        console.log('Deleting folder');
        await deleteFolder.mutateAsync();
        console.log('Folder deletion completed');

        // If we're in the deleted folder, go back first
        if (currentFolderId === deleteConfirmation.item.id.toString()) {
          console.log('Navigating back from deleted folder');
          goBack();
        }
      } else {
        console.log('Deleting file');
        await deleteFile.mutateAsync();
        console.log('File deletion completed');
      }

      // Clear the confirmation state first
      setDeleteConfirmation(null);

      // Then refresh the contents
      console.log('Refreshing folder contents', { caseId, currentFolderId });
      if (currentFolderId) {
        queryClient.invalidateQueries({
          queryKey: ['folderContents', currentFolderId]
        });
      } else {
        queryClient.invalidateQueries({
          queryKey: ['caseContents', caseId]
        });
      }

    } catch (error) {
      console.error('Error in deletion process:', error);
      toast({
        title: "Error",
        description: "Failed to delete item",
        variant: "destructive",
      });
      // Make sure to clear the confirmation even on error
      setDeleteConfirmation(null);
    }
  };

  const navigateToFolder = (folder: CaseContentItem | FolderContentItem) => {
    setFolderHistory(prev => [...prev, { id: folder.id.toString(), name: folder.name }]);
    setCurrentFolderId(folder.id.toString());

    // No need to invalidate query as the folder contents will be fetched automatically
    // when currentFolderId changes
  };

  const goBack = () => {
    console.log('Going back from current folder', { currentFolderId, folderHistory });

    setFolderHistory(prev => {
      const newHistory = [...prev];
      newHistory.pop();
      console.log('New folder history', newHistory);
      return newHistory;
    });

    // Update currentFolderId after history update
    const newCurrentFolder = folderHistory.length > 1
      ? folderHistory[folderHistory.length - 2].id
      : null;

    console.log('Setting new current folder', newCurrentFolder);
    setCurrentFolderId(newCurrentFolder);
  };

  const handleFileClick = async (file: FolderContentItem | CaseContentItem) => {
    // For image files, show preview dialog
    if (isImageFile(file.name)) {
      const fileId = file.id?.toString();
      if (!fileId || fileId.trim() === '' || !accessToken) {
        toast({
          title: "Error",
          description: "Invalid file ID or missing authentication",
          variant: "destructive",
        });
        return;
      }

      try {
        // Set the selected file first
        setSelectedFile(file);
        
        // Use the file ID directly with the view link query
        const response = await axios.get<FileViewLinkResponse>(
          `${BASE_URL}/file/${fileId}/view-link/`,
          {
            params: { expiration_minutes: 60 },
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );

        if (response.data?.view_link) {
          setPreviewImage({
            url: response.data.view_link,
            isOpen: true,
            fileName: file.name
          });
        }
      } catch (error) {
        console.error('Error getting view link:', error);
        toast({
          title: "Error",
          description: "Failed to load image preview",
          variant: "destructive",
        });
      } finally {
        // Reset selected file
        setSelectedFile(null);
      }
    }
  };

  const handleFileDownload = async (file: FolderContentItem | CaseContentItem) => {
    try {
      await fileDownload.mutateAsync(file.id.toString());
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const getCurrentFolderName = () => {
    if (!currentFolderId) return 'Root';
    const currentFolder = folderHistory[folderHistory.length - 1];
    return currentFolder ? currentFolder.name : 'Root';
  };

  // Update loading state to consider both queries
  const isLoading = currentFolderId ?
    isLoadingFolderContents || isFetchingFolderContents :
    isLoadingCaseContents || isFetchingCaseContents;

  // Update pin/unpin handlers
  const handlePinFile = async (file: CaseContentItem | FolderContentItem) => {
    try {
      await pinFile.mutateAsync({
        case_id: caseId,
        file_id: file.id.toString(),
        folder_id: currentFolderId || caseContents?.root_folder?.id,  // Use root folder id if no current folder
        file_name: file.name
      });
    } catch (error) {
      console.error('Error pinning file:', error);
    }
  };

  const handleUnpinFile = async (file: CaseContentItem | FolderContentItem) => {
    try {
      await pinFile.mutateAsync({
        case_id: caseId,
        file_id: file.id.toString(),
        unpin: true,
        file_name: file.name
      });
    } catch (error) {
      console.error('Error unpinning file:', error);
    }
  };

  // Update the part where we set the initial file name when opening rename dialog
  const handleStartRename = (file: FolderContentItem | CaseContentItem) => {
    const { name, extension } = getFileNameAndExtension(file.name);
    setFileToRename(file);
    setNewFileName(name);
    setFileExtension(extension);
    setShowRenameDialog(true);
  };

  // Update the rename handler
  const handleRename = async () => {
    if (!fileToRename || !newFileName.trim()) return;

    try {
      const newFullName = newFileName + fileExtension;
      await renameFile.mutateAsync({
        file_id: fileToRename.id.toString(),
        new_name: newFullName
      });

      // Clear states and close dialog
      setFileToRename(null);
      setNewFileName('');
      setFileExtension('');
      setShowRenameDialog(false);

      // Refresh the contents
      if (currentFolderId) {
        queryClient.invalidateQueries({
          queryKey: ['folderContents', currentFolderId]
        });
      } else {
        queryClient.invalidateQueries({
          queryKey: ['caseContents', caseId]
        });
      }

      toast({
        title: "Success",
        description: "File renamed successfully",
      });
    } catch (error) {
      console.error('Error renaming file:', error);
      toast({
        title: "Error",
        description: "Failed to rename file",
        variant: "destructive",
      });
    }
  };

  const deleteProcessedFile = useDeleteProcessedFileMutation();

  // Add handleRemoveFromChat function before renderFileContextMenu
  const handleRemoveFromChat = async (fileId: string) => {
    try {
      await deleteProcessedFile.mutateAsync(fileId);
    } catch (error) {
      console.error('Error removing file from chat:', error);
      toast({
        title: "Error",
        description: "Failed to remove file from chat",
        variant: "destructive",
      });
    }
  };

  // Update renderFileContextMenu to include Remove from Chat option
  const renderFileContextMenu = (file: CaseContentItem | FolderContentItem) => {
    const fileId = file.id;  // file.id is already a string based on the interface
    const isPinned = fileId && pinnedFile?.pinned_files?.some(pf => pf.id === fileId);
    const canPin = isImageFile(file.name);
    const canChat = isSupportedForChat(file.name);
    
    // // Check if file is in processed files list and has a green dot (COMPLETED status)
    // const isProcessed = fileId && processedFiles?.data?.files.some(
    //   (processedFile) => processedFile.file_id === fileId && processedFile.status === 'COMPLETED'
    // );

    // Check if file is currently being processed
    const isProcessing = fileId && processedFiles?.data?.files.some(
      (processedFile) => processedFile.file_id === fileId && processedFile.status === 'PROCESSING'
    );

    // Check if file is in processed files list (any status)
    const isInProcessedFiles = fileId && processedFiles?.data?.files.some(
      (processedFile) => processedFile.file_id === fileId
    );

    return (
      <>
        <ContextMenuContent>
          <ContextMenuItem
            onClick={() => fileId && handleFileDownload(file)}
            disabled={fileDownload.isPending || !fileId}
          >
            {fileDownload.isPending ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Download
          </ContextMenuItem>
          
          {/* Chat options group */}
          {canChat && (
            <>
              <ContextMenuSeparator />
              {isInProcessedFiles ? (
                <ContextMenuItem
                  onClick={() => fileId && handleRemoveFromChat(fileId)}
                  disabled={Boolean(!fileId || deleteProcessedFile.isPending || isProcessing)}
                  className="text-red-600 focus:text-red-600"
                >
                  {deleteProcessedFile.isPending ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="mr-2 h-4 w-4" />
                  )}
                  Remove from Chat
                </ContextMenuItem>
              ) : (
                <ContextMenuItem
                  onClick={() => fileId && setShowChatDialog(fileId)}
                  disabled={!fileId}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Add to Case Chat
                </ContextMenuItem>
              )}
            </>
          )}
          
          <ContextMenuItem
            onClick={() => fileId && openInCaseCockpit(file)}
            disabled={!fileId}
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Open in Case Cockpit
          </ContextMenuItem>
          <ContextMenuItem
            onClick={() => {
              if (fileId) {
                handleStartRename(file);
              }
            }}
            disabled={!fileId}
          >
            Rename
          </ContextMenuItem>
          {canPin && (
            <>
              <ContextMenuSeparator />
              {isPinned ? (
                <ContextMenuItem onClick={() => handleUnpinFile(file)}>
                  <PinOff className="mr-2 h-4 w-4" />
                  Unpin File
                </ContextMenuItem>
              ) : (
                <ContextMenuItem
                  onClick={() => fileId && handlePinFile(file)}
                  disabled={!fileId}
                >
                  <Pin className="mr-2 h-4 w-4" />
                  Pin File
                </ContextMenuItem>
              )}
            </>
          )}
          <ContextMenuSeparator />
          <ContextMenuItem
            onClick={() => fileId && setDeleteConfirmation({ type: 'file', item: file })}
            disabled={!fileId}
            className="text-red-600 focus:text-red-600"
          >
            Delete
          </ContextMenuItem>
        </ContextMenuContent>
      </>
    );
  };

  // Add this handler function near other handlers
  const handleFileSelect = async (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const acceptedFiles = files.filter(file => file.size <= 100 * 1024 * 1024); // 100MB limit

    const newUploadingFiles = acceptedFiles.map(file => ({
      file,
      progress: 0,
    }));
    setUploadingFiles(prev => [...prev, ...newUploadingFiles]);

    for (const file of acceptedFiles) {
      try {
        const interval = setInterval(() => {
          updateUploadProgress(file, Math.random() * 100);
        }, 200);

        await handleUpload(file);

        clearInterval(interval);
        updateUploadProgress(file, 100);

        setTimeout(() => {
          setUploadingFiles(prev => prev.filter(f => f.file !== file));
        }, 1000);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';
        handleUploadError(file, errorMessage);
      }
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  // Add new function to open in case cockpit
  const openInCaseCockpit = async (file: FolderContentItem | CaseContentItem) => {
    const fileId = file.id?.toString();
    if (!fileId || fileId.trim() === '' || !accessToken) {
      toast({
        title: "Error",
        description: "Invalid file ID or missing authentication",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await axios.get<FileViewLinkResponse>(
        `${BASE_URL}/file/${fileId}/view-link/`,
        {
          params: { expiration_minutes: 60 },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );

      if (response.data?.view_link) {
        const newDocument: Document = {
          id: file.id,
          name: file.name,
          url: response.data.view_link,
          content_type: 'file',
        };

        const exists = selectedDocuments.some(
          (doc: Document) => doc.id === file.id
        );

        if (!exists) {
          setSelectedDocuments([...selectedDocuments, newDocument]);
        }
        setActiveDocumentId(file.id);
      // } else {
      //    console.log(`☄️☄️ error at view link in openInCaseCockpit ----------------------->☄️☄️`);
        // toast({
        //   title: "Error",
        //   description: "Could not generate view link",
        //   variant: "destructive",
        // });
      }
    } catch (error) {
      console.error('Error opening in case cockpit:', error);
      toast({
        title: "Error",
        description: "Failed to open in case cockpit",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="p-4">
      {/* Breadcrumb Navigation */}
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <span
            className="cursor-pointer hover:text-[#060216]-500"
            onClick={() => {
              setCurrentFolderId(null);
              setFolderHistory([]);
            }}
          >
            Root
          </span>
          {folderHistory.map((folder, index) => (
            <Fragment key={folder.id}>
              <span>/</span>
              <span
                className="cursor-pointer hover:text-[#060216]-500"
                onClick={() => {
                  setCurrentFolderId(folder.id);
                  setFolderHistory(prev => prev.slice(0, index + 1));
                }}
              >
                {folder.name}
              </span>
            </Fragment>
          ))}
        </div>
        {/* Current Folder Info */}
        <div className="text-sm text-gray-500">
          Current folder: {getCurrentFolderName()}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 mb-4">
        <Button
          variant="ghost"
          className="hover:bg-gray-100 transition-colors"
          onClick={goBack}
          disabled={folderHistory.length === 0}
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back
        </Button>
        <Button onClick={() => setShowNewFolderDialog(true)} className="bg-transparent hover:bg-blue-50 text-[#060216]-600 hover:text-[#060216]-700 border border-blue-300 shadow-sm">
          <Plus className="mr-2 h-4 w-4" />
          {currentFolderId ? 'New Subfolder' : 'New Folder'}
        </Button>
      </div>

      {/* Replace dropzone with new native drag and drop */}
      {renderDropZone()}

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="mb-4 space-y-2">
          {uploadingFiles.map(({ file, progress, error }) => (
            <div key={file.name} className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>{file.name}</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
              {error && (
                <p className="text-sm text-red-500">{error}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Files and Folders Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {folders.map((folder) => (
            <ContextMenu key={folder.id}>
              <ContextMenuTrigger>
                <TooltipProvider>
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <div
                        className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                        onDoubleClick={() => navigateToFolder(folder)}
                      >
                        <Folder className="h-8 w-8 mx-auto text-[#060216]-500" />
                        <p className="mt-2 text-center truncate text-sm">{folder.name}</p>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{folder.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ContextMenuTrigger>
              <ContextMenuContent>
                <ContextMenuItem onClick={() => setDeleteConfirmation({ type: 'folder', item: folder })}>
                  Delete
                </ContextMenuItem>
              </ContextMenuContent>
            </ContextMenu>
          ))}

          {files.map((file) => (
            <ContextMenu key={file.id}>
              <ContextMenuTrigger>
                <TooltipProvider>
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <div
                        className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer relative"
                        onClick={() => {
                          if (file && file.id) {
                            handleFileClick(file);
                          }
                        }}
                      >
                        <div className="relative">
                          {isImageFile(file.name) ? (
                            <div className="relative h-20 w-20 mx-auto overflow-hidden flex items-center justify-center border rounded">
                              {getImagePreviewUrl(file.id?.toString()) ? (
                                <div className="relative w-full h-full">
                                  <Image 
                                    src={getImagePreviewUrl(file.id?.toString()) || ''} 
                                    alt={file.name}
                                    fill
                                    className="object-cover"
                                    unoptimized // Since we're using external URLs
                                  />
                                </div>
                              ) : (
                                <div className="flex items-center justify-center h-full w-full bg-gray-50">
                                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                                </div>
                              )}
                              <FileIcon className="h-8 w-8 mx-auto text-gray-500 hidden" />
                              {/* Add a small indicator that this is an image */}
                              <div className="absolute bottom-0 right-0 bg-gray-100 rounded-tl p-0.5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                </svg>
                              </div>
                            </div>
                          ) : (
                            <FileIcon className="h-8 w-8 mx-auto text-gray-500" />
                          )}
                          
                          {/* Add status indicator */}
                          {getFileStatus(file.id?.toString() || '')?.status && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div 
                                    className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${
                                      getStatusColor(getFileStatus(file.id?.toString() || '')?.status || '')
                                    }`}
                                  />
                                </TooltipTrigger>
                                <TooltipContent>
                                  {(() => {
                                    const status = getFileStatus(file.id?.toString() || '');
                                    if (!status) return null;
                                    return (
                                      <div className="text-xs">
                                        <p>Status: {status.status}</p>
                                        {status.processing_time && (
                                          <p>Processing Time: {status.processing_time}s</p>
                                        )}
                                        {status.pages_processed > 0 && (
                                          <p>Pages Processed: {status.pages_processed}</p>
                                        )}
                                        {status.error_message && (
                                          <p className="text-red-500">Error: {status.error_message}</p>
                                        )}
                                      </div>
                                    );
                                  })()}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}

                          {pinnedFile?.pinned_files?.some(pf => pf.id === file.id?.toString()) && (
                            <Pin className="absolute -top-2 -right-2 h-4 w-4 text-blue-500" />
                          )}
                        </div>
                        <p className="mt-2 text-center truncate text-sm">{file.name}</p>
                        <p className="text-xs text-gray-500 text-center">
                          {file.size ? `${(file.size / 1024).toFixed(1)} KB` : 'Size unknown'}
                        </p>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{file.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ContextMenuTrigger>
              {renderFileContextMenu(file)}
            </ContextMenu>
          ))}
        </div>
      )}

      {/* New Folder Dialog */}
      <Dialog open={showNewFolderDialog} onOpenChange={setShowNewFolderDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              <Input
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="Folder name"
                className="mt-2"
              />
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNewFolderDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateFolder}
              disabled={createFolder.isPending}
            >
              {createFolder.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteConfirmation}
        onOpenChange={() => setDeleteConfirmation(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure?</DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-2">
              This action cannot be undone. This will permanently delete{' '}
              {deleteConfirmation?.item.name}.
            </p>
            {deleteConfirmation?.type === 'folder' && (
              <p className="text-sm text-red-500">
                Warning: This will also delete all files and subfolders within this folder.
              </p>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteConfirmation(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteFolder.isPending}
            >
              {(deleteFolder.isPending) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Update Rename Dialog */}
      <Dialog 
        open={showRenameDialog} 
        onOpenChange={(open) => {
          if (!open) {
            setShowRenameDialog(false);
            setFileToRename(null);
            setNewFileName('');
            setFileExtension('');
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename File</DialogTitle>
            <DialogDescription>
              Enter a new name for the file
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-2 mt-2">
            <div className="flex-1">
              <Input
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                placeholder="File name"
              />
            </div>
            <div className="bg-gray-100 px-3 py-2 rounded text-gray-600 min-w-[40px] text-center">
              {fileExtension}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowRenameDialog(false);
              setFileToRename(null);
              setNewFileName('');
              setFileExtension('');
            }}>
              Cancel
            </Button>
            <Button
              onClick={handleRename}
              disabled={renameFile.isPending || !newFileName.trim()}
            >
              {renameFile.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Rename
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add the ImagePreviewDialog before the other dialogs */}
      <ImagePreviewDialog
        imageUrl={previewImage.url}
        isOpen={previewImage.isOpen}
        onClose={() => setPreviewImage({ isOpen: false, fileName: '' })}
        fileName={previewImage.fileName}
      />

      {/* Add ChatWithCaseDialog outside of the renderFileContextMenu */}
      {showChatDialog && (
        <ChatWithCaseDialog
          isOpen={!!showChatDialog}
          onClose={() => setShowChatDialog(null)}
          onConfirm={() => showChatDialog && handleAddToChat(showChatDialog)}
          fileName={files.find(f => f.id?.toString() === showChatDialog)?.name || ''}
          fileType={files.find(f => f.id?.toString() === showChatDialog)?.name.split('.').pop()?.toUpperCase() || ''}
          isProcessing={processFile.isPending}
        />
      )}
    </div>
  );
};

export default FileManager;