# Checklist with Files Components

This set of components allows users to associate files with checklist items in the case management system.

## Components Overview

1. **FileSelector**: A dialog component that allows users to select files from the case folder.
2. **ChecklistItemFiles**: A component that displays the files associated with a checklist item.
3. **ChecklistItemWithFiles**: A component that displays a single checklist item with file attachment functionality.
4. **ChecklistWithFiles**: A component that displays a list of checklist items with file attachment functionality.

## How to Use

### Basic Usage

```tsx
import { ChecklistWithFilesExample } from "@/components/CaseOverview/components/ChecklistWithFilesExample";

// In your page component
function CaseDetailsPage({ params }: { params: { caseId: string } }) {
  return (
    <div>
      <h1>Case Details</h1>
      <ChecklistWithFilesExample caseId={params.caseId} />
    </div>
  );
}
```

### Advanced Usage

If you need more control over the checklist items, you can use the `ChecklistWithFiles` component directly:

```tsx
import { ChecklistWithFiles } from "@/components/CaseOverview/components/ChecklistWithFiles";

// In your component
function CustomChecklist({ caseId, items }) {
  const requiredItems = items.filter(item => item.is_required);
  const optionalItems = items.filter(item => !item.is_required);

  return (
    <ChecklistWithFiles
      caseId={caseId}
      requiredItems={requiredItems}
      optionalItems={optionalItems}
      title="Custom Checklist"
    />
  );
}
```

## API Reference

### FileSelector Props

| Prop | Type | Description |
|------|------|-------------|
| caseId | string | The ID of the case |
| isOpen | boolean | Whether the file selector dialog is open |
| onClose | () => void | Function to call when the dialog is closed |
| onSelect | (selectedFileIds: string[]) => void | Function to call when files are selected |
| initialSelectedFileIds | string[] | Optional. The initially selected file IDs |

### ChecklistItemFiles Props

| Prop | Type | Description |
|------|------|-------------|
| fileIds | string[] | The IDs of the files to display |
| onRemoveFile | (fileId: string) => void | Optional. Function to call when a file is removed |
| isReadOnly | boolean | Optional. Whether the component is read-only |

### ChecklistItemWithFiles Props

| Prop | Type | Description |
|------|------|-------------|
| item | { id: number; name: string; description?: string; is_completed: boolean; file_ids?: string[] } | The checklist item to display |
| caseId | string | The ID of the case |
| onToggleComplete | (id: number, isCompleted: boolean) => void | Function to call when the completion status is toggled |

### ChecklistWithFiles Props

| Prop | Type | Description |
|------|------|-------------|
| caseId | string | The ID of the case |
| requiredItems | ChecklistItem[] | The required checklist items |
| optionalItems | ChecklistItem[] | The optional checklist items |
| title | string | Optional. The title of the checklist |

## Backend Integration

This component suite integrates with the backend API to:

1. Fetch files from the case folder
2. Update checklist items with associated file IDs
3. View files when clicked

Make sure the backend API supports the following endpoints:

- `GET /api/v1/case-management/cases/{caseId}/folder-contents/` - Get case folder contents
- `POST /api/v1/case-management/cases/{caseId}/update_checklist_item/` - Update a checklist item
- `GET /api/v1/case-management/files/{fileId}/view/` - View a file
