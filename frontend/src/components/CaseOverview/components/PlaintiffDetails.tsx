import React, { FC, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Edit, Plus, Trash2, User, Users } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import AddEditCaseParty from "@/app/case/caseparty/components/AddEditCaseParty";
import { PartyType, PartyStatus } from "@/type/case-management/partyTypes";
import { useCasePartiesQuery, useDeleteCasePartyMutation } from "@/services/case-management/partyService";
import { CaseParty } from "@/type/case-management/partyTypes";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { KPICard } from "@/components/ui/kpi-card";
import ManageEmailTemplate from "@/components/ManageEmailTemplate";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { <PERSON><PERSON><PERSON><PERSON>, InfoFieldGroup } from "@/components/ui/InfoField";

interface PlaintiffDetailsProps {
  caseId: string;
}

const PlaintiffDetails: FC<PlaintiffDetailsProps> = ({ caseId }) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedParty, setSelectedParty] = useState<CaseParty | null>(null);

  const { data: parties = [], isLoading } = useCasePartiesQuery(caseId);
  const deletePartyMutation = useDeleteCasePartyMutation(caseId, selectedParty?.id || 0);

  if (isLoading) {
    return <div className="min-h-screen bg-gray-50 p-8">Loading...</div>;
  }

  const plaintiffs = parties.filter((party: CaseParty) => party.contact?.party_type === PartyType.PLAINTIFF);
  const otherParties = parties.filter((party: CaseParty) => party.contact?.party_type === PartyType.OTHER);

  const handleDeleteParty = (party: CaseParty) => {
    setSelectedParty(party);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedParty) {
      try {
        await deletePartyMutation.mutateAsync();
        setDeleteDialogOpen(false);
        setSelectedParty(null);
      } catch (error) {
        console.error('Error deleting party:', error);
      }
    }
  };

  return (
    <>
      <div className="flex items-end justify-end mb-6">
        <AddEditCaseParty
          caseId={caseId}
          defaultValues={{
            party_type: PartyType.PLAINTIFF,
            status: PartyStatus.PENDING_CONTACT,
            party_contact: ""
          }}
          trigger={
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4" />
              Plaintiff
            </Button>
          }
        />
        <AddEditCaseParty
          caseId={caseId}
          defaultValues={{
            party_type: PartyType.OTHER,
            status: PartyStatus.PENDING_CONTACT,
            party_contact: ""
          }}
          trigger={
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4" />
              Other Party
            </Button>
          }
        />
      </div>

      <div className="grid grid-cols-4 gap-4 mb-6">
        <KPICard
          icon={User}
          title="Plaintiffs"
          metrics={{
            primary: {
              type: 'count',
              value: plaintiffs.length
            },
          }}
        />
        <KPICard
          icon={Users}
          title="Other Parties"
          metrics={{
            primary: {
              type: 'count',
              value: otherParties.length
            },
          }}
        />
      </div>

      <Tabs defaultValue="plaintiff" className="w-full">
        <TabsList>
          <TabsTrigger value="plaintiff">Plaintiff ({plaintiffs.length})</TabsTrigger>
          <TabsTrigger value="other-parties">Other Parties ({otherParties.length})</TabsTrigger>
        </TabsList>
        <TabsContent value="plaintiff">
          {plaintiffs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No plaintiffs found.</div>
          ) : (
            <div className="space-y-6">
              {plaintiffs.map((party, index) => (
                <div key={party.id} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium">Plaintiff {index + 1}</h3>
                    <div className="flex items-center gap-2">
                      <AddEditCaseParty
                        caseId={caseId}
                        isEdit={true}
                        defaultValues={{
                          status: party.status,
                          description: party.description,
                          party_contact: party.contact?.id?.toString(),
                          law_firm: party.law_firm?.id?.toString(),
                          attorney: party.attorney?.id?.toString(),
                          party_type: party.contact.party_type,
                        }}
                        partyId={party.id.toString()}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50 p-2 h-8 w-8"
                          >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2 h-8 w-8"
                        onClick={() => handleDeleteParty(party)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600 cursor-pointer" />
                      </Button>
                      <ManageEmailTemplate
                        caseId={caseId}
                        contextType="client"
                        clientId={party.id.toString()}
                      />
                    </div>
                  </div>

                  <InfoFieldGroup
                    columns={3}
                    caseId={caseId}
                    fields={[
                      {
                        label: "Name",
                        value: `${party.contact?.first_name || ''} ${party.contact?.last_name || ''}`,
                      },
                      {
                        label: "Type",
                        value: "Plaintiff",
                      },
                      {
                        label: "Status",
                        value: party.status || '—',
                      },
                      {
                        label: "Primary Phone",
                        value: party.contact?.phone || '—',
                        isPhone: true,
                      },
                      {
                        label: "Secondary Phone",
                        value: party.contact?.cell || '—',
                        isPhone: true,
                      },
                      {
                        label: "Email",
                        value: party.contact?.email || '—',
                        isMail: true,
                      },
                    ]}
                    className="mb-5"
                  />

                  <InfoField
                    label="Address"
                    value={[
                      party.contact?.street1,
                      party.contact?.street2,
                      party.contact?.city,
                      party.contact?.state,
                      party.contact?.zip_code
                    ].filter(Boolean).join(', ') || '—'}
                    className="mb-5"
                  />

                  <div className="bg-[#F7F7F7] border border-black/[0.08] rounded-lg p-4">
                    <InfoField
                      label="Notes"
                      value={party.description ? <RichTextViewer data={party.description} /> : '—'}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
        <TabsContent value="other-parties">
          {otherParties.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No other parties found.</div>
          ) : (
            <div className="space-y-6">
              {otherParties.map((party, index) => (
                <div key={party.id} className="bg-[#F7F7F7] rounded-lg border border-[#00000014] p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium">Other Party {index + 1}</h3>
                    <div className="flex items-center gap-2">
                      <AddEditCaseParty
                        caseId={caseId}
                        isEdit={true}
                        defaultValues={{
                          status: party.status,
                          description: party.description,
                          party_contact: party.contact?.id?.toString(),
                          law_firm: party.law_firm?.id?.toString(),
                          attorney: party.attorney?.id?.toString(),
                          party_type: party.contact.party_type,
                        }}
                        partyId={party.id.toString()}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50 p-2 h-8 w-8"
                          >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2 h-8 w-8"
                        onClick={() => handleDeleteParty(party)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600 cursor-pointer" />
                      </Button>
                      <ManageEmailTemplate
                        caseId={caseId}
                        contextType="other_party"
                        other_party_id={party.id.toString()}
                      />
                    </div>
                  </div>

                  <InfoFieldGroup
                    columns={3}
                    caseId={caseId}
                    fields={[
                      {
                        label: "Name",
                        value: `${party.contact?.first_name || ''} ${party.contact?.last_name || ''}`,
                      },
                      {
                        label: "Type",
                        value: "Other Party",
                      },
                      {
                        label: "Status",
                        value: party.status || '—',
                      },
                      {
                        label: "Primary Phone",
                        value: party.contact?.phone || '—',
                        isPhone: true,
                      },
                      {
                        label: "Secondary Phone",
                        value: party.contact?.cell || '—',
                        isPhone: true,
                      },
                      {
                        label: "Email",
                        value: party.contact?.email || '—',
                        isMail: true,
                      },
                    ]}
                    className="mb-5"
                  />

                  <InfoField
                    label="Address"
                    value={[
                      party.contact?.street1,
                      party.contact?.street2,
                      party.contact?.city,
                      party.contact?.state,
                      party.contact?.zip_code
                    ].filter(Boolean).join(', ') || '—'}
                    className="mb-5"
                  />

                  <div className="bg-[#F7F7F7] border border-black/[0.08] rounded-lg p-4">
                    <InfoField
                      label="Notes"
                      value={party.description ? <RichTextViewer data={party.description} /> : '—'}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        title="Delete Party"
        description={`Are you sure you want to delete ${selectedParty?.contact?.first_name} ${selectedParty?.contact?.last_name}? This action cannot be undone.`}
      />
    </>
  );
};

export default PlaintiffDetails; 