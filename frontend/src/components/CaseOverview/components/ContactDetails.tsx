import React, { <PERSON> } from 'react';
import { User } from 'lucide-react';
import { useClientContactDetailsQuery } from '@/services/case-management/clientDetailService';
import { PhoneLink } from '@/components/ui/phone-link';
import { MailLink } from '@/components/ui/mail-link';
import { AddressLink } from '@/components/gMap/address-link';
import { USStatesLabels } from '@/constants/commont';

interface ContactDetailsProps {
    caseId: string;
}

const ContactDetails: FC<ContactDetailsProps> = ({ caseId }) => {
    const { data: contactDetails, isLoading } = useClientContactDetailsQuery(caseId);

    if (isLoading) {
        return (
            <div className="space-y-4">
                <div className="flex items-center gap-1.5">
                    <User className="h-4 w-4" />
                    <h2 className="text-[#060216] text-sm font-medium">Contact Details</h2>
                </div>
                <div className="flex items-center justify-center h-32">
                    <div className="rounded-full h-8 w-8 border-b-2 border-primary animate-spin"></div>
                </div>
            </div>
        );
    }

    const formatAddress = () => {
        const parts = [
            contactDetails?.address1_street1,
            contactDetails?.address1_street2,
            contactDetails?.address1_city,
            contactDetails?.address1_state ? USStatesLabels[contactDetails.address1_state as keyof typeof USStatesLabels] : undefined,
            contactDetails?.address1_zip,
        ].filter(Boolean);

        return parts.length > 0 ? parts.join(", ") : "—";
    };


    return (
        <div className="p-6 bg-white rounded-lg border border-gray-100 w-full">
            {/* Header */}
            {/* <div className="flex items-center gap-1.5 mb-6">
                <User className="h-4 w-4" />
                <h2 className="text-[#060216] text-sm font-medium">Contact Details</h2>
            </div> */}

            {/* Contact Information Grid */}
            <div className="grid grid-cols-3 gap-6 mb-6">
                {/* Primary Phone */}
                <div>
                    <div className="text-[#060216]/50 text-xs mb-1">Primary</div>
                    <div className="text-sm font-semibold text-[#060216]">
                        <PhoneLink phone={contactDetails?.phone_number_1} />
                    </div>
                </div>

                {/* Email */}
                <div>
                    <div className="text-[#060216]/50 text-xs mb-1">Email</div>
                    <div className="text-sm font-semibold text-[#060216]">
                        <MailLink email={contactDetails?.primary_email} caseId={caseId} />
                    </div>
                </div>
                <div>
                    <div className="text-[#060216]/50 text-xs mb-1">Address</div>
                    <div className="text-sm font-semibold text-[#060216]">
                        <AddressLink
                            address={{
                                street1: contactDetails?.address1_street1 || undefined,
                                street2: contactDetails?.address1_street2 || undefined,
                                city: contactDetails?.address1_city || undefined,
                                state: contactDetails?.address1_state || undefined,
                                zip_code: contactDetails?.address1_zip || undefined,
                            }}
                        >
                            {formatAddress()}
                        </AddressLink>
                    </div>
                </div>
            </div>

            {/* Address Section */}

        </div>
    );
};

export default ContactDetails;
