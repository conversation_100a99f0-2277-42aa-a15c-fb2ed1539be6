import React, { FC, Fragment } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { 
  useClientInsuranceListQuery, 
  useInsuranceAdjusterDetailsQuery,
  useInsuranceLegalRepresentationQuery 
} from '@/services/case-management/clientDetailService';
import RichTextViewer from '@/components/ui/RichTextViewer';
import { Separator } from "@/components/ui/separator";
import { ClientInsurance } from '@/type/case-management/clientDetailTypes';
import { AddressLink } from '@/components/gMap/address-link';
import { InfoFieldGroup } from '@/components/ui/InfoField';

interface PlaintiffInsuranceProps {
  caseId: string;
}

interface InsuranceItemProps {
  insurance: ClientInsurance;
  isLast: boolean;
  caseId: string;
}

interface LawFirmData {
  id: number;
  office_name: string;
  phone?: string | null;
  cell?: string | null;
  fax?: string | null;
  email?: string | null;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  attorneys?: {
    id: number;
    first_name: string;
    last_name: string;
    phone?: string | null;
    email?: string | null;
  }[];
}

const AdjusterInfo: FC<{ caseId: string; insuranceId: string }> = ({ caseId, insuranceId }) => {
  const { data: adjusterData } = useInsuranceAdjusterDetailsQuery(caseId, insuranceId);

  // Get the first adjuster (Bodily Injury adjuster)
  const firstAdjuster = adjusterData?.bodily_injury ||
    adjusterData?.medpay_pip ||
    adjusterData?.medpay_pip_supervisor ||
    adjusterData?.bi_supervisor ||
    adjusterData?.property_damage ||
    adjusterData?.pd_supervisor;

  return (
    <InfoFieldGroup
      caseId={caseId}
      fields={[
        {
          label: "Adjuster",
          value: firstAdjuster ? [firstAdjuster.first_name, firstAdjuster.last_name].filter(Boolean).join(' ') : '—'
        },
        {
          label: "Phone",
          value: firstAdjuster?.phone,
          isPhone: true
        },
        {
          label: "Email",
          value: firstAdjuster?.email,
          isMail: true
        }
      ]}
    />
  );
};

const LegalRepresentation: FC<{ caseId: string; insuranceId: string }> = ({ caseId, insuranceId }) => {
  const { data: legalRep } = useInsuranceLegalRepresentationQuery(
    caseId,
    insuranceId,
    { enabled: !!insuranceId }
  );

  if (!legalRep?.law_firm) return null;

  // Convert single law firm to array if needed
  const lawFirms = Array.isArray(legalRep.law_firm) 
    ? legalRep.law_firm as LawFirmData[]
    : [legalRep.law_firm] as LawFirmData[];

  if (!lawFirms.length) return null;

  return (
    <div className="mt-8 space-y-6">
      <h4 className="text-sm font-semibold text-[#060216]">Legal Representation</h4>
      {lawFirms.map((lawFirm, firmIndex) => (
        <Fragment key={lawFirm.id}>
          {firmIndex > 0 && <Separator className="my-6 bg-gray-200" />}
          <div className="space-y-6">
            <InfoFieldGroup
              caseId={caseId}
              fields={[
                {
                  label: "Law Firm",
                  value: lawFirm.office_name || '—'
                },
                {
                  label: "Phone",
                  value: lawFirm.phone,
                  isPhone: true
                },
                {
                  label: "Cell",
                  value: lawFirm.cell,
                  isPhone: true
                },
                {
                  label: "Fax",
                  value: lawFirm.fax,
                  isPhone: true
                },
                {
                  label: "Email",
                  value:lawFirm.email,
                  isMail: true
                },
                {
                  label: "Address",
                  value: <AddressLink address={{
                    street1: lawFirm.street1 || undefined,
                    street2: lawFirm.street2 || undefined,
                    city: lawFirm.city || undefined,
                    state: lawFirm.state || undefined,
                    zip_code: lawFirm.zip_code || undefined
                  }}
                  />
                }
              ]}
            />
          </div>
        </Fragment>
      ))}
    </div>
  );
};

const InsuranceItem: FC<InsuranceItemProps> = ({ insurance, isLast, caseId }) => {
  const { data: adjusterData } = useInsuranceAdjusterDetailsQuery(caseId, insurance.id.toString());
  if (insurance.no_insurance) {
    return (
      <div className="text-[#060216] text-sm font-medium p-4">
        Client has confirmed they do not have insurance
      </div>
    );
  }

  if (!insurance.insurance_company?.name) {
    return null;
  }


  // Get the first adjuster (Bodily Injury adjuster)
  const firstAdjuster = adjusterData?.bodily_injury ||
    adjusterData?.medpay_pip ||
    adjusterData?.medpay_pip_supervisor ||
    adjusterData?.bi_supervisor ||
    adjusterData?.property_damage ||
    adjusterData?.pd_supervisor;

  return (
    <Fragment>
      <InfoFieldGroup
        caseId={caseId}
        fields={[
          {
            label: "Insurance Company",
            value: insurance.insurance_company?.name || '—'
          },
          {
            label: "Company Phone",
            value: insurance.insurance_company?.phone,
            isPhone: true
          },
          {
            label: "Company Email",
            value: insurance.insurance_company?.email,
            isMail: true
          },
          {
            label: "Adjuster",
            value: firstAdjuster ? [firstAdjuster.first_name, firstAdjuster.last_name].filter(Boolean).join(' ') : '—'
          },
          {
            label: "Adjuster Phone",
            value: firstAdjuster?.phone,
            isPhone: true
          },
          {
            label: "Adjuster Email",
            value: firstAdjuster?.email,
            isMail: true
          },
          {
            label: "UM/UIM",
            value: insurance.um_uim || '—'
          },
          {
            label: "Claim ID",
            value: `${insurance.claim_number ? '#' : ''}${insurance.claim_number || '—'}`
          },
          {
            label: "Medpay",
            value: insurance.medpay || '—'
          },
          {
            label: "Policy ID",
            value: insurance.policy_number || '—'
          },
          {
            label: "1st Party Policy Limit",
            value: insurance.um_uim || '—'
          }
        ]}
      />
      {!isLast && <Separator className="my-6" />}
      {insurance.claim_note && (
        <div className="p-2 bg-gray-50 rounded-lg border border-gray-100">
          <p className="text-xs text-gray-500 mb-1">Claim Note</p>
          <div className="text-sm font-semibold">
            <RichTextViewer data={insurance.claim_note} isCopied={true} />
          </div>
        </div>
      )}
      <div className="mt-4">
        <AdjusterInfo caseId={caseId} insuranceId={insurance.id.toString()} />
      </div>
      
      <LegalRepresentation caseId={caseId} insuranceId={insurance.id.toString()} />
    </Fragment>
  );
};

const PlaintiffInsurance: FC<PlaintiffInsuranceProps> = ({ caseId }) => {
  const { data: plaintiffInsurance, isLoading } = useClientInsuranceListQuery(caseId);

  return (
    <div className="space-y-6 w-full">
      <Card className="bg-white rounded-lg shadow-sm w-full">
        <CardContent className="p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : plaintiffInsurance && plaintiffInsurance.length > 0 ? (
            <div className="animate-fadeIn">
              {plaintiffInsurance.map((insurance, index) => (
                <InsuranceItem
                  key={insurance.id}
                  insurance={insurance}
                  isLast={index === plaintiffInsurance.length - 1}
                  caseId={caseId}
                />
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-center">No plaintiff insurance found</div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PlaintiffInsurance;