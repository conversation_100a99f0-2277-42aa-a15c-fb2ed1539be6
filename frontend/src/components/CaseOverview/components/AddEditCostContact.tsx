'use client';

import React, { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useCreateCostContactMutation, useUpdateCostContactMutation } from "@/services/orgAPIs";
import { OrgCostContact, OrgCostContactCreateRequest, ContactType, CONTACT_TYPE_CHOICES } from "@/type/case-management/orgTypes";
import { USStatesLabels } from "@/constants/commont";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import RichTextEditor from "@/components/ui/RichTextEditor";

const statesList = Object.entries(USStatesLabels).map(([value, label]) => ({
    value,
    label,
}));

const formSchema = z.object({
    contact_type: z.nativeEnum(ContactType),
    company_name: z.string().min(1, "Company name is required"),
    payee: z.string().optional(),
    phone: z.string().optional(),
    phone_ext: z.string().optional(),
    cell: z.string().optional(),
    fax: z.string().optional(),
    email: z.string().email().optional().or(z.string().length(0)),
    website: z.string().optional(),
    street1: z.string().optional(),
    street2: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip_code: z.string().optional(),
    tax_id: z.string().optional(),
    note: z.string().optional(),
});

interface AddEditCostContactProps {
    isEdit?: boolean;
    selectedContact?: OrgCostContact;
    onSuccess?: () => void;
}

export default function AddEditCostContact({
    isEdit = false,
    selectedContact,
    onSuccess
}: AddEditCostContactProps) {
    const [open, setOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const createMutation = useCreateCostContactMutation();
    const updateMutation = useUpdateCostContactMutation(selectedContact?.id?.toString() || '');

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            contact_type: selectedContact?.contact_type || undefined,
            company_name: selectedContact?.company_name || '',
            payee: selectedContact?.payee || '',
            phone: selectedContact?.phone || '',
            phone_ext: selectedContact?.phone_ext || '',
            cell: selectedContact?.cell || '',
            fax: selectedContact?.fax || '',
            email: selectedContact?.email || '',
            website: selectedContact?.website || '',
            street1: selectedContact?.street1 || '',
            street2: selectedContact?.street2 || '',
            city: selectedContact?.city || '',
            state: selectedContact?.state || '',
            zip_code: selectedContact?.zip_code || '',
            tax_id: selectedContact?.tax_id || '',
            note: selectedContact?.note || "",
        }
    });

    // Reset form to initial values based on selectedContact
    const resetFormToInitialValues = useCallback(() => {
        form.reset({
            contact_type: selectedContact?.contact_type || undefined,
            company_name: selectedContact?.company_name || '',
            payee: selectedContact?.payee || '',
            phone: selectedContact?.phone || '',
            phone_ext: selectedContact?.phone_ext || '',
            cell: selectedContact?.cell || '',
            fax: selectedContact?.fax || '',
            email: selectedContact?.email || '',
            website: selectedContact?.website || '',
            street1: selectedContact?.street1 || '',
            street2: selectedContact?.street2 || '',
            city: selectedContact?.city || '',
            state: selectedContact?.state || '',
            zip_code: selectedContact?.zip_code || '',
            tax_id: selectedContact?.tax_id || '',
            note: selectedContact?.note || "",
        });
    }, [form, selectedContact]);

    useEffect(() => {
        if (selectedContact) {
            resetFormToInitialValues();
        }
    }, [selectedContact, resetFormToInitialValues]);

    const handleOpenChange = (newOpen: boolean) => {
        // If opening the dialog, reset form to initial values
        if (newOpen) {
            resetFormToInitialValues();
            setOpen(true);
            return;
        }

        // If closing the dialog
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleClose = useCallback(() => {
        setOpen(false);
        resetFormToInitialValues();
    }, [resetFormToInitialValues]);

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCancelClick = () => {
        const isDirty = form.formState.isDirty;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    async function onSubmit(values: z.infer<typeof formSchema>) {
        try {
            if (isEdit && selectedContact?.id) {
                await updateMutation.mutateAsync(values);
            } else {
                await createMutation.mutateAsync(values as OrgCostContactCreateRequest);
            }
            onSuccess?.();
            handleClose();
        } catch (error) {
            console.error('Failed to save cost contact:', error);
        }
    }

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    <Button
                        type="button"
                        variant={"outline"}
                        size="sm"
                        className={"max-w-3xl max-h-[90vh] text-green-600 border-green-600 hover:bg-green-100 hover:text-green-700"}
                    >
                        {isEdit ? "Edit" : "Add"}
                    </Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl flex flex-col overflow-hidden">
                    <DialogHeader>
                        <DialogTitle>
                            {isEdit ? "Edit Cost Contact" : "Add Cost Contact"}
                        </DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
                            <div className="flex-1 max-h-[75vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="contact_type"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Contact Type *</FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    defaultValue={field.value}
                                                    disabled={isEdit}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select contact type" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {CONTACT_TYPE_CHOICES.map((type) => (
                                                            <SelectItem key={type.value} value={type.value}>
                                                                {type.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="company_name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Company Name *</FormLabel>
                                                <FormControl>
                                                    <Input {...field} placeholder="Enter company name" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="payee"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Payee</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <div className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="phone"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Phone</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="phone_ext"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Extension</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <FormField
                                        control={form.control}
                                        name="cell"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Cell</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="fax"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Fax</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Email</FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="email" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="website"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Website</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="street1"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Street 1</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="street2"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Street 2</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <div className="grid grid-cols-3 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="city"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>City</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="state"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>State</FormLabel>
                                                    <Select 
                                                        value={field.value || ""} 
                                                        onValueChange={field.onChange}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select state" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            {field.value && (
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    className="mb-2 w-full justify-center text-sm"
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        field.onChange("");
                                                                    }}
                                                                >
                                                                    Clear Selection
                                                                </Button>
                                                            )}
                                                            {statesList.map((state) => (
                                                                <SelectItem key={state.value} value={state.value}>
                                                                    {state.label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="zip_code"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>ZIP</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <FormField
                                        control={form.control}
                                        name="tax_id"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Tax-ID / SSN</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="note"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Note</FormLabel>
                                            <FormControl>
                                                <RichTextEditor
                                                    value={field.value || ""}
                                                    onChange={(value) => {
                                                        field.onChange(value);
                                                    }}
                                                    placeholder="Enter note"
                                                    className="min-h-[100px] resize-none bg-gray-50"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <div className="flex-none border-t mt-4">
                                <div className="flex justify-end space-x-2 py-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleCancelClick}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={(e) => {
                                            form.handleSubmit((values) => onSubmit(values))(e);
                                        }}
                                        disabled={createMutation.isPending || updateMutation.isPending}>
                                        {isEdit ? "Update" : "Save"}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>

    );
} 