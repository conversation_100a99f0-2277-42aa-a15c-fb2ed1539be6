'use client';

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Search, Plus, Minus, Edit } from "lucide-react";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { useCases } from "@/services/caseService";
import { useCreateCaseLinkMutation, useLinkedCasesQuery, useRemoveCaseLinkMutation } from "@/services/case-management/linkCaseService";
import { format } from "date-fns";
import { RelationshipType, CaseBasicDetails } from '@/type/linkCaseType';

const formSchema = z.object({
    search: z.string(),
});

interface SelectCaseProps {
    caseId: string;
    onClose?: () => void;
    className?: string;
}

export default function SelectCase({ caseId, onClose, className }: SelectCaseProps) {
    const [listOpen, setListOpen] = useState(false);
    const [searchParams, setSearchParams] = useState({
        search: "",
        pageSize: 10,
        page: 1
    });

    const { data: casesData, isLoading } = useCases(searchParams, true);
    const { data: linkedCasesData } = useLinkedCasesQuery(caseId);
    const createCaseLink = useCreateCaseLinkMutation(caseId);
    const removeCaseLink = useRemoveCaseLinkMutation(caseId);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            search: "",
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        setSearchParams(prev => ({
            ...prev,
            search: values.search
        }));
    }

    useEffect(() => {
        const timer = setTimeout(() => {
            if (searchParams.search) {
                form.handleSubmit(onSubmit)();
            }
        }, 500);

        return () => clearTimeout(timer);
    }, [searchParams.search]);

    const handleLinkCase = async (targetCaseId: string) => {
        try {
            await createCaseLink.mutateAsync({
                target_case: targetCaseId,
                relationship_type: RelationshipType.RELATED
            });
            setListOpen(false);
            onClose?.();
        } catch (error) {
            console.error("Error linking case:", error);
        }
    };

    const handleRemoveLink = async (targetCaseId: string) => {
        try {
            await removeCaseLink.mutateAsync(targetCaseId);
            setListOpen(false);
            onClose?.();
        } catch (error) {
            console.error("Error removing case link:", error);
        }
    };

    const isCaseLinked = (caseId: string) => {
        return linkedCasesData?.direct_cases?.some((linkedCase: CaseBasicDetails) => linkedCase.id === caseId) || 
               linkedCasesData?.indirect_cases?.some((linkedCase: CaseBasicDetails) => linkedCase.id === caseId);
    };

    return (
        <>
            <Dialog open={listOpen} onOpenChange={setListOpen}>
                <Button
                    variant="outline"
                    className={`bg-transparent text-[#060216]-600 font-Manrope p-0 border-none ${className || ''}`}
                    onClick={() => setListOpen(true)}
                >
                    <Edit className="h-4 w-4 text-green-500" />
                </Button>
                <DialogContent className="max-w-4xl">
                    <DialogHeader>
                        <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">Link Case</DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="grid grid-cols-1 gap-4">
                                <FormField
                                    control={form.control}
                                    name="search"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <div className="flex items-center">
                                                    <Search className="w-4 h-4 inline-block mr-2" />
                                                    <Input
                                                        {...field}
                                                        className="bg-white"
                                                        placeholder="Search by case name, client name, or date"
                                                        onChange={(e) => {
                                                            field.onChange(e);
                                                            setSearchParams(prev => ({
                                                                ...prev,
                                                                search: e.target.value
                                                            }));
                                                        }}
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                                {isLoading ? (
                                    <div className="flex justify-center items-center py-8">
                                        <Loader2 className="h-8 w-8 animate-spin text-[#060216]/50" />
                                    </div>
                                ) : (
                                    <Table>
                                        <TableHeader className="sticky top-0 bg-white z-10">
                                            <TableRow className="bg-[#060216]/5">
                                                <TableHead className="w-[300px] text-[#060216]/50">Case Name</TableHead>
                                                <TableHead className="text-[#060216]/50">Client</TableHead>
                                                <TableHead className="text-[#060216]/50">Created By</TableHead>
                                                <TableHead className="text-[#060216]/50">Date of Loss</TableHead>
                                                <TableHead className="text-[#060216]/50">Status</TableHead>
                                                <TableHead className="w-[100px]"></TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {casesData?.results
                                                ?.filter(c => c.id.toString() !== caseId)
                                                ?.sort((a, b) => {
                                                    const isALinked = isCaseLinked(a.id.toString());
                                                    const isBLinked = isCaseLinked(b.id.toString());
                                                    // Sort unlinked cases first
                                                    if (!isALinked && isBLinked) return 1;
                                                    if (isALinked && !isBLinked) return -1;
                                                    return 0;
                                                })
                                                .map((caseItem) => (
                                                    <TableRow
                                                        key={caseItem.id}
                                                        className={`cursor-pointer hover:bg-[#060216]/5 ${
                                                            isCaseLinked(caseItem.id.toString()) ? 'bg-gray-50' : ''
                                                        }`}
                                                    >
                                                        <TableCell className="font-medium text-[#060216]">
                                                            {caseItem.case_name}
                                                        </TableCell>
                                                        <TableCell className="text-[#060216]">
                                                            {caseItem.name_of_client}
                                                        </TableCell>
                                                        <TableCell className="text-[#060216]">
                                                            {`${caseItem.created_by.name}`}
                                                        </TableCell>
                                                        <TableCell className="text-[#060216]">
                                                            {caseItem.accident_date ?
                                                                format(new Date(caseItem.accident_date), 'MM/dd/yyyy')
                                                                : "—"}
                                                        </TableCell>
                                                        <TableCell className="text-[#060216]">
                                                            {caseItem.status}
                                                        </TableCell>
                                                        <TableCell>
                                                            {isCaseLinked(caseItem.id.toString()) ? (
                                                                <Button
                                                                    variant="ghost"
                                                                    size="icon"
                                                                    onClick={() => handleRemoveLink(caseItem.id.toString())}
                                                                >
                                                                    <Minus className="h-4 w-4 text-red-500" />
                                                                </Button>
                                                            ) : (
                                                                <Button
                                                                    variant="ghost"
                                                                    size="icon"
                                                                    onClick={() => handleLinkCase(caseItem.id.toString())}
                                                                >
                                                                    <Plus className="h-4 w-4 text-green-500" />
                                                                </Button>
                                                            )}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                        </TableBody>
                                    </Table>
                                )}
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="outline"
                                    type="button"
                                    onClick={() => setListOpen(false)}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>
        </>
    );
} 