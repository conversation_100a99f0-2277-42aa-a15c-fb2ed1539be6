import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Edit } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useParams } from "next/navigation";
import { 
  useCreateMedPayDepositMutation, 
  useUpdateMedPayDepositMutation, 
  useTreatmentProvidersQuery 
} from "@/services/case-management/medicalTreatmentService";
import { MedPayDeposit } from "@/type/case-management/medicalTreatmentTypes";
import { TreatmentProvider } from "@/type/case-management/medicalTreatmentTypes";
import { formatDateForInput, formatDateForApi } from "@/utils/dateUtils";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { RequiredLabel } from "@/components/ui/required-label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useClientInsuranceListQuery } from "@/services/case-management/clientDetailService";
import { useDefendantsListQuery } from "@/services/case-management/defendantService";
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

interface AddEditMedPayDepositProps {
  isEdit?: boolean;
  selectedDeposit?: MedPayDeposit;
  onSuccess?: () => void;
}

// Define types for insurance objects
interface ClientInsurance {
  id: number;
  client?: {
    name: string;
  };
  insurance_company?: {
    name: string;
  };
}

interface DefendantInsuranceListItem {
  id: number;
  defendant: number;
  insurance_company: {
    id: number;
    name: string;
    phone?: string | null;
    cell?: string | null;
    fax?: string | null;
    email?: string | null;
    street1?: string | null;
    street2?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
  };
  claim_number?: string | null;
  policy_number?: string | null;
  insured?: string | null;
  liability_status?: string | null;
  coverage_status?: string | null;
  med_pay?: string | null;
  no_insurance?: boolean | null;
}

// Define type for defendants
interface DefendantListItem {
  id: number;
  name?: string;
  first_name?: string;
  last_name?: string;
  email?: string | null;
  phone?: string | null;
  date_of_birth?: string | null;
  driver_license_number?: string | null;
  driver_license_state?: string | null;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  at_fault?: boolean | null;
  liability_status?: string | null;
  notes?: string | null;
  vehicle_year?: string | null;
  vehicle_make?: string | null;
  vehicle_model?: string | null;
  vehicle_color?: string | null;
  vehicle_license_plate?: string | null;
  vehicle_license_state?: string | null;
  created_at: string;
  updated_at: string;
}

const medPayDepositFormSchema = z.object({
  insurance_type: z.enum(["CLIENT_MEDPAY", "THIRD_PARTY_MEDPAY"], {
    required_error: "Please select an MedPay type",
  }),
  client_insurance: z.string().optional().refine((val) => {
    if (val === "" || val === undefined) return true;
    return !isNaN(parseInt(val));
  }, {
    message: "Client insurance must be a valid ID",
  }),
  defendant_insurance: z.string().optional().refine((val) => {
    if (val === "" || val === undefined) return true;
    return !isNaN(parseInt(val));
  }, {
    message: "Defendant insurance must be a valid ID",
  }),
  medical_provider: z.string({
    required_error: "Please select a medical provider",
  }).refine((val) => !isNaN(parseInt(val)), {
    message: "Medical provider must be a valid ID",
  }),
  amount: z.string({
    required_error: "Please enter an amount",
  }).refine((val) => !isNaN(parseFloat(val)), {
    message: "Amount must be a valid number",
  }),
  check_number: z.string().optional(),
  medpay_request_date: z.string({
    required_error: "Please select a request date",
  }),
  medpay_deposit_date: z.string().optional(),
  note: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.insurance_type === "CLIENT_MEDPAY" && (!data.client_insurance || data.client_insurance === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Client insurance is required when insurance type is CLIENT_MEDPAY",
      path: ["client_insurance"],
    });
  }
  
  if (data.insurance_type === "THIRD_PARTY_MEDPAY" && (!data.defendant_insurance || data.defendant_insurance === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Defendant insurance is required when insurance type is THIRD_PARTY_MEDPAY",
      path: ["defendant_insurance"],
    });
  }
});

type MedPayDepositFormValues = z.infer<typeof medPayDepositFormSchema>;

export function AddEditMedPayDeposit({
  isEdit = false,
  selectedDeposit,
  onSuccess,
}: AddEditMedPayDepositProps) {
  const params = useParams() as { caseId: string };
  const caseId = params.caseId || '';
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

  if (!caseId) {
    throw new Error('Case ID is required but not found in URL parameters');
  }

  const [open, setOpen] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const [allDefendantInsurances, setAllDefendantInsurances] = useState<DefendantInsuranceListItem[]>([]);
  const [isLoadingInsurances, setIsLoadingInsurances] = useState(false);
  
  const createMutation = useCreateMedPayDepositMutation(caseId);
  const updateMutation = useUpdateMedPayDepositMutation(caseId, selectedDeposit?.id || -1);
  
  const { data: treatments = [] } = useTreatmentProvidersQuery(caseId);
  const { data: clientInsurances = [] } = useClientInsuranceListQuery(caseId, { enabled: open });
  
  // Fetch all defendants for the case
  const { data: defendants = [] } = useDefendantsListQuery(caseId);

  // Fetch all defendant insurances when dialog is opened
  useEffect(() => {
    const fetchAllInsurances = async () => {
      if (!open || !defendants.length) return;
      
      setIsLoadingInsurances(true);
      try {
        const insurancePromises = defendants.map(async (defendant) => {
          const defendantId = defendant.id.toString();
          const response = await axios.get(
            `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurances/`,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`
              }
            }
          );
          return response.data || [];
        });
        
        const results = await Promise.all(insurancePromises);
        // Flatten the array of arrays into a single array of insurances
        const allInsurances = results.flat();
        setAllDefendantInsurances(allInsurances);
      } catch (error) {
        console.error("Error fetching defendant insurances:", error);
      } finally {
        setIsLoadingInsurances(false);
      }
    };
    
    fetchAllInsurances();
  }, [open, defendants, caseId, accessToken]);

  const form = useForm<MedPayDepositFormValues>({
    resolver: zodResolver(medPayDepositFormSchema),
    defaultValues: {
      insurance_type: undefined,
      client_insurance: undefined,
      defendant_insurance: undefined,
      medical_provider: undefined,
      amount: "",
      check_number: "",
      medpay_request_date: "",
      medpay_deposit_date: "",
      note: "",
    },
    mode: "onChange",
  });

  // Reset form to initial values based on selectedDeposit
  const resetFormToInitialValues = () => {
    if (selectedDeposit) {
      form.reset({
        insurance_type: selectedDeposit.insurance_type,
        client_insurance: selectedDeposit.client_insurance?.toString(),
        defendant_insurance: selectedDeposit.defendant_insurance?.toString(),
        medical_provider: selectedDeposit.medical_provider.toString(),
        amount: selectedDeposit.amount,
        check_number: selectedDeposit.check_number || "",
        medpay_request_date: formatDateForInput(selectedDeposit.medpay_request_date),
        medpay_deposit_date: selectedDeposit.medpay_deposit_date 
          ? formatDateForInput(selectedDeposit.medpay_deposit_date) 
          : "",
        note: selectedDeposit.note || "",
      });
    } else {
      form.reset({
        insurance_type: undefined,
        client_insurance: undefined,
        defendant_insurance: undefined,
        medical_provider: undefined,
        amount: "",
        check_number: "",
        medpay_request_date: "",
        medpay_deposit_date: "",
        note: "",
      });
    }
  };

  useEffect(() => {
    resetFormToInitialValues();
  }, [selectedDeposit, open]);

  const insuranceType = form.watch("insurance_type");

  async function onSubmit(data: MedPayDepositFormValues) {
    const payload = {
      insurance_type: data.insurance_type,
      medical_provider: parseInt(data.medical_provider),
      amount: data.amount,
      check_number: data.check_number || undefined,
      medpay_request_date: formatDateForApi(data.medpay_request_date),
      medpay_deposit_date: data.medpay_deposit_date ? formatDateForApi(data.medpay_deposit_date) : undefined,
      note: data.note || undefined,
    };

    if (data.insurance_type === "CLIENT_MEDPAY" && data.client_insurance) {
      Object.assign(payload, { client_insurance: parseInt(data.client_insurance) });
    } else if (data.insurance_type === "THIRD_PARTY_MEDPAY" && data.defendant_insurance) {
      Object.assign(payload, { defendant_insurance: parseInt(data.defendant_insurance) });
    }

    if (isEdit && selectedDeposit) {
      await updateMutation!.mutateAsync(payload);
    } else {
      await createMutation.mutateAsync(payload);
    }

    setOpen(false);
    onSuccess?.();
  }

  const handleOpenChange = (newOpen: boolean) => {
    // Check if there are unsaved changes
    const isDirty = form.formState.isDirty;
    
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      setOpen(newOpen);
      if (!newOpen) {
        // Reset form when closing
        resetFormToInitialValues();
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    resetFormToInitialValues();
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    setOpen(false);
    resetFormToInitialValues();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleCancelClick = () => {
    if (form.formState.isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const getDefendantDisplayName = (defendant: DefendantListItem | undefined, defendantId: number): string => {
    if (!defendant) return `Defendant ${defendantId}`;
    
    if (defendant.first_name || defendant.last_name) {
      return `${defendant.first_name || ''} ${defendant.last_name || ''}`.trim();
    }
    
    return `Defendant ${defendantId}`;
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        {isEdit ? (
          <Button variant="link" className="p-0" onClick={() => setOpen(true)}>
            <Edit className="h-4 w-4 text-gray-500" />
          </Button>
        ) : (
          <Button variant="link" className="text-green-600" data-add-medpay-deposit onClick={() => setOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Deposit
          </Button>
        )}
        <DialogContent className="max-w-2xl">
          <DialogHeader className="border-b pb-3">
            <DialogTitle className="text-xl font-semibold text-[#060216]">{isEdit ? "Edit MedPay Deposit" : "Add MedPay Deposit"}</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col">
              <div className="flex-1 max-h-[65vh] overflow-y-auto pr-4 py-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <div className="grid gap-6">
                  {/* Insurance Type */}
                  <FormField
                    control={form.control}
                    name="insurance_type"
                    render={({ field }) => (
                      <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                        <FormLabel>
                          <RequiredLabel>MedPay Type</RequiredLabel>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            value={field.value}
                            onValueChange={field.onChange}
                            className="grid grid-cols-2 gap-4"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0 bg-white p-3 rounded-md border hover:border-gray-400 transition-colors">
                              <FormControl>
                                <RadioGroupItem value="CLIENT_MEDPAY" />
                              </FormControl>
                              <FormLabel className="font-normal">Client MedPay</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0 bg-white p-3 rounded-md border hover:border-gray-400 transition-colors">
                              <FormControl>
                                <RadioGroupItem value="THIRD_PARTY_MEDPAY" />
                              </FormControl>
                              <FormLabel className="font-normal">Third Party MedPay</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Client Insurance */}
                  {insuranceType === "CLIENT_MEDPAY" && (
                    <FormField
                      control={form.control}
                      name="client_insurance"
                      render={({ field }) => (
                        <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <FormLabel>
                            <RequiredLabel>Client Insurance</RequiredLabel>
                          </FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger className="bg-white">
                                <SelectValue placeholder="Select client insurance" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {clientInsurances.map((insurance: ClientInsurance) => (
                                <SelectItem
                                  key={insurance.id}
                                  value={insurance.id.toString()}
                                >
                                  {insurance.insurance_company?.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Defendant Insurance */}
                  {insuranceType === "THIRD_PARTY_MEDPAY" && (
                    <FormField
                      control={form.control}
                      name="defendant_insurance"
                      render={({ field }) => (
                        <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <FormLabel>
                            <RequiredLabel>Defendant Insurance</RequiredLabel>
                          </FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger className="bg-white">
                                <SelectValue placeholder={isLoadingInsurances ? "Loading insurances..." : "Select defendant insurance"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {isLoadingInsurances ? (
                                <SelectItem value="loading" disabled>Loading insurances...</SelectItem>
                              ) : allDefendantInsurances.length === 0 ? (
                                <SelectItem value="none" disabled>No defendant insurances found</SelectItem>
                              ) : (
                                allDefendantInsurances.map((insurance) => {
                                  // Find the defendant using the defendant ID
                                  const defendant = defendants.find(d => d.id === insurance.defendant);
                                  const defendantName = getDefendantDisplayName(defendant, insurance.defendant);
                                  
                                  return (
                                    <SelectItem
                                      key={insurance.id}
                                      value={insurance.id.toString()}
                                    >
                                      {defendantName} - {insurance.insurance_company.name}
                                    </SelectItem>
                                  );
                                })
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Medical Provider */}
                  <FormField
                    control={form.control}
                    name="medical_provider"
                    render={({ field }) => (
                      <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                        <FormLabel>
                          <RequiredLabel>Medical Provider</RequiredLabel>
                        </FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <SelectValue placeholder="Select medical provider" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {treatments.map((treatment: TreatmentProvider) => {
                              const provider = typeof treatment.medical_provider === 'object'
                                ? treatment.medical_provider
                                : { id: treatment.medical_provider, company: `Provider ${treatment.medical_provider}` };
                              
                              return (
                                <SelectItem
                                  key={provider.id}
                                  value={provider.id.toString()}
                                >
                                  {provider.company}
                                </SelectItem>
                              );
                            })}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    {/* Amount */}
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <FormLabel>
                            <RequiredLabel>Amount</RequiredLabel>
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                              <Input
                                placeholder="0.00"
                                {...field}
                                type="number"
                                step="0.01"
                                className="pl-8 bg-white"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Check Number */}
                    <FormField
                      control={form.control}
                      name="check_number"
                      render={({ field }) => (
                        <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <FormLabel>Check Number</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter check number"
                              {...field}
                              className="bg-white"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* Request Date */}
                    <FormField
                      control={form.control}
                      name="medpay_request_date"
                      render={({ field }) => (
                        <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <FormLabel>
                            <RequiredLabel>Request Date</RequiredLabel>
                          </FormLabel>
                          <FormControl>
                            <CustomDateInput
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Select date"
                              className="bg-white"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Deposit Date */}
                    <FormField
                      control={form.control}
                      name="medpay_deposit_date"
                      render={({ field }) => (
                        <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                          <FormLabel>Deposit Date</FormLabel>
                          <FormControl>
                            <CustomDateInput
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Select date"
                              className="bg-white"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Note */}
                  <FormField
                    control={form.control}
                    name="note"
                    render={({ field }) => (
                      <FormItem className="bg-gray-50 rounded-lg p-4 shadow-sm">
                        <FormLabel>Note</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Add notes about this MedPay deposit"
                            {...field}
                            className="bg-white min-h-[100px] resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex-none border-t mt-6 pt-4">
                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelClick}
                    className="w-24"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={createMutation.isPending || (updateMutation?.isPending ?? false)}
                    className="w-24 bg-green-600 hover:bg-green-700"
                  >
                    {createMutation.isPending || (updateMutation?.isPending ?? false)
                      ? "Saving..."
                      : isEdit
                        ? "Update"
                        : "Save"}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  );
} 