import { Badge } from "@/components/ui/badge";
import { Check, CheckCircle, Paperclip } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import React, { useEffect, useState, useMemo, FormEvent } from "react";
import { useFullCaseDetails } from "@/services/caseService";
import { useQueryClient } from "@tanstack/react-query";
import { KPIType } from "@/services/organizationService";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Plus } from "lucide-react";
import {
    useCase<PERSON>hecklistsQuery,
    CaseChecklist,
    useCreateCaseChecklistMutation,
    useUpdateCaseChecklistMutation,
    useUpdateChecklistItemMutation
} from "@/services/case-management/noteTaskService";
import {
    ContextMenu,
    ContextMenuContent,
    ContextMenuItem,
    ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { Edit } from "lucide-react";
import { useOrganizationCaseStatuses } from "@/services/organizationService";
import { formatDateForDisplay } from "@/utils/dateUtils";
import { FileSelector } from "./FileSelector";
import { ChecklistItemFiles } from "./ChecklistItemFiles";

interface CaseChecklistItem extends CaseChecklist {
    // Extending CaseChecklist with any additional fields needed for UI
    displayOrder?: number;
    completed_at?: string;
    is_applicable?: boolean;
    order?: number;  // Adding the order property
}

interface StatusGroup {
    name: string;
    items: CaseChecklistItem[];
}

interface GroupedChecklists {
    [status: string]: StatusGroup;
}

interface CheckListProps {
    caseId: string;
}

// Add KPI type selection component
type KPISelectValue = KPIType | "none";

const KPITypeSelect = ({ value, onChange }: { value?: KPIType; onChange: (value: KPIType | undefined) => void }) => {
    const [selectedValue, setSelectedValue] = useState<KPISelectValue>(() => {
        if (!value) return "none";
        return Object.values(KPIType).includes(value) ? value : "none";
    });

    const handleChange = (val: KPISelectValue) => {
        setSelectedValue(val);
        onChange(val === "none" ? undefined : val);
    };

    return (
        <div className="space-y-2">
            <Label htmlFor="kpi_type">KPI Tracking</Label>
            <input type="hidden" name="kpi_type" value={selectedValue} />
            <Select
                value={selectedValue}
                onValueChange={(val: string) => handleChange(val as KPISelectValue)}
            >
                <SelectTrigger id="kpi_type">
                    <SelectValue placeholder="Select KPI type (optional)" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {Object.values(KPIType).map((type) => (
                        <SelectItem key={type} value={type}>
                            {getKPIDisplayName(type)}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
};

// Add helper functions for KPI display
const getKPIDisplayName = (kpiType: string): string => {
    const displayNames: Record<string, string> = {
        total_cases_in_negotiation: "Cases in Negotiation",
        case_demand_ready: "Demand Ready",
        case_closed: "Cases Closed",
        cases_touched: "Cases Touched",
        treatment_period: "Treatment Period",
        avg_case_age: "Average Case Age",
        notes_entered: "Notes Entered",
        three_plus_providers_count: "3+ Providers",
        total_attorney_fees: "Attorney Fees",
        depositions_taken: "Depositions Taken",
        mediations_arb_trials: "Mediations/Trials",
        hearings: "Hearings"
    };
    return displayNames[kpiType] || kpiType.split('_').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
};

// const getKPIBadgeStyle = (kpiType: string): string => {
//     const colors: Record<string, string> = {
//         total_cases_in_negotiation: "bg-purple-100 text-purple-800",
//         case_demand_ready: "bg-blue-100 text-blue-800",
//         case_closed: "bg-green-100 text-green-800",
//         cases_touched: "bg-yellow-100 text-yellow-800",
//         treatment_period: "bg-orange-100 text-orange-800",
//         avg_case_age: "bg-red-100 text-red-800",
//         notes_entered: "bg-indigo-100 text-indigo-800",
//         three_plus_providers_count: "bg-pink-100 text-pink-800",
//         total_attorney_fees: "bg-emerald-100 text-emerald-800",
//         depositions_taken: "bg-cyan-100 text-cyan-800",
//         mediations_arb_trials: "bg-teal-100 text-teal-800",
//         hearings: "bg-violet-100 text-violet-800"
//     };
//     return colors[kpiType] || "bg-gray-100 text-gray-800";
// };

function ChecklistItem({
    item,
    onClick,
    isUpdating,
    onEdit,
    onToggleApplicable,
    caseId,
}: {
    item: CaseChecklistItem;
    onClick: () => void;
    isUpdating: boolean;
    onEdit: () => void;
    onToggleApplicable: () => void;
    caseId: string;
}) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [isFileSelectorOpen, setIsFileSelectorOpen] = useState(false);
    const updateChecklistMutation = useUpdateChecklistItemMutation(caseId, item.id);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    const hasFiles = item.file_ids && item.file_ids.length > 0;

    const handleToggleExpand = () => {
        setIsExpanded(!isExpanded);
    };

    const handleOpenFileSelector = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsFileSelectorOpen(true);
    };

    const handleCloseFileSelector = () => {
        setIsFileSelectorOpen(false);
    };

    const handleSelectFiles = (selectedFileIds: string[]) => {
        updateChecklistMutation.mutate({
            is_completed: item.is_completed,
            file_ids: selectedFileIds
        }, {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });
                toast({
                    title: "Files associated",
                    description: `${selectedFileIds.length} file(s) associated with checklist item`,
                });
            }
        });
    };

    const handleRemoveFile = (fileId: string) => {
        const updatedFileIds = item.file_ids?.filter(id => id !== fileId) || [];
        updateChecklistMutation.mutate({
            is_completed: item.is_completed,
            file_ids: updatedFileIds
        }, {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });
                toast({
                    title: "File removed",
                    description: "File removed from checklist item",
                });
            }
        });
    };

    return (
        <ContextMenu>
            <ContextMenuTrigger>
                <div
                    className={cn(
                        "group flex flex-col cursor-pointer transition-colors duration-200 rounded-lg bg-gray-50 hover:bg-gray-100 relative mb-5",
                        item.is_completed && "before:absolute before:left-[22px] before:top-[28px] before:bottom-[28px] before:w-[2px] before:bg-gray-200"
                    )}
                    onClick={handleToggleExpand}
                >
                    <div className="flex items-start justify-between p-4 pb-2">
                        <div className="flex items-start gap-3">
                            <div className="flex-shrink-0 mt-0.5 relative z-10">
                                {isUpdating ? (
                                    <div className="h-4 w-4 rounded-full border-2 border-gray-300 border-t-transparent animate-spin" />
                                ) : item.is_completed ? (
                                    <div className="h-4 w-4 rounded-full bg-green-100 flex items-center justify-center"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onClick();
                                        }}
                                    >
                                        <Check className="h-3 w-3 text-green-600" />
                                    </div>
                                ) : (
                                    <div
                                        className="h-4 w-4 rounded-full border-2 border-gray-300"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onClick();
                                        }}
                                    />
                                )}
                            </div>
                            <div className="flex-1">
                                <h3 className={cn(
                                    "text-sm font-medium leading-5",
                                    item.is_completed ? "text-gray-500" : "text-gray-900"
                                )}>
                                    {item.item_name}
                                </h3>
                                {item.description && (
                                    <p className="text-gray-500 text-sm mt-1 leading-5">
                                        {item.description}
                                    </p>
                                )}
                                {hasFiles && (
                                    <div className="text-xs text-blue-600 mt-1 flex items-center">
                                        <Paperclip className="h-3 w-3 mr-1" />
                                        {item.file_ids?.length} file{item.file_ids?.length !== 1 ? 's' : ''} attached
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="flex items-start gap-2 pt-0.5">
                            {item.kpi_type && (
                                <Badge variant="outline" className="text-green-600 bg-green-50 border-green-200 text-xs font-medium">
                                    {getKPIDisplayName(item.kpi_type)}
                                </Badge>
                            )}
                            {item.is_required && (
                                <Badge variant="outline" className="text-red-600 bg-gray-50 border-none text-xs font-medium bg-none hover:bg-gray-50">
                                    *Required
                                </Badge>
                            )}
                            <div className="flex items-center gap-2">
                                <Label htmlFor={`applicable-${item.id}`} className="text-xs text-gray-600">
                                    Applicable
                                </Label>
                                <Switch
                                    id={`applicable-${item.id}`}
                                    checked={item.is_applicable !== false}
                                    onCheckedChange={() => {
                                        onToggleApplicable();
                                    }}
                                    className="data-[state=checked]:bg-green-500"
                                />
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleOpenFileSelector(e);
                                }}
                                className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 h-auto"
                            >
                                <Paperclip className="h-3 w-3 mr-1" />
                                {hasFiles ? `${item.file_ids?.length} file${item.file_ids?.length !== 1 ? 's' : ''}` : 'Attach Files'}
                            </Button>
                        </div>
                    </div>
                    {item.is_completed && item.completed_at && (
                        <div className="flex items-center justify-between pl-12 pr-4 py-2">
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                                <span>Completed by You</span>
                                <span>•</span>
                                <span>{formatDateForDisplay(item.completed_at)}</span>
                            </div>
                            <div className="flex-1"></div>
                        </div>
                    )}

                    {isExpanded && hasFiles && (
                        <div className="pl-12 pr-4 pb-3">
                            <ChecklistItemFiles
                                fileIds={item.file_ids || []}
                                onRemoveFile={handleRemoveFile}
                            />
                        </div>
                    )}
                </div>
            </ContextMenuTrigger>
            <ContextMenuContent>
                <ContextMenuItem onClick={onEdit}>
                    <Edit className="h-4 w-4 text-green-500" />
                    Edit Item
                </ContextMenuItem>
                <ContextMenuItem onClick={onToggleApplicable}>
                    {item.is_applicable ? "Mark as Not Applicable" : "Mark as Applicable"}
                </ContextMenuItem>
                <ContextMenuItem onClick={handleOpenFileSelector}>
                    <Paperclip className="h-4 w-4 text-blue-500 mr-1" />
                    {hasFiles ? 'Manage Files' : 'Attach Files'}
                </ContextMenuItem>
            </ContextMenuContent>

            <FileSelector
                caseId={caseId}
                isOpen={isFileSelectorOpen}
                onClose={handleCloseFileSelector}
                onSelect={handleSelectFiles}
                initialSelectedFileIds={item.file_ids || []}
            />
        </ContextMenu>
    );
}

// Add ChecklistItemDialog component
interface ChecklistItemDialogProps {
    caseId: string;
    organizationStatusId: number;
    item?: CaseChecklistItem;
    onClose: () => void;
}

function ChecklistItemDialog({ caseId, organizationStatusId, item, onClose }: ChecklistItemDialogProps) {
    const [formData, setFormData] = useState({
        name: item?.item_name || "",
        description: item?.description || "",
        isRequired: item?.is_required || false,
        kpiType: item?.kpi_type ? (item.kpi_type as KPIType) : undefined,
        organizationStatus: item?.organization_status || organizationStatusId
    });

    const { data: orgStatuses } = useOrganizationCaseStatuses();
    const createMutation = useCreateCaseChecklistMutation(caseId);
    const updateMutation = useUpdateCaseChecklistMutation(caseId);
    const { toast } = useToast();

    // Reset form when item changes
    useEffect(() => {
        if (item) {
            setFormData({
                name: item.item_name,
                description: item.description || "",
                isRequired: item.is_required,
                kpiType: item.kpi_type ? (item.kpi_type as KPIType) : undefined,
                organizationStatus: item.organization_status
            });
        } else {
            setFormData(prev => ({
                ...prev,
                organizationStatus: organizationStatusId
            }));
        }
    }, [item, organizationStatusId]);

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        try {
            if (item) {
                // Update existing item
                await updateMutation.mutateAsync({
                    checklistId: item.id.toString(),
                    data: {
                        item_name: formData.name,
                        description: formData.description,
                        is_required: formData.isRequired,
                        kpi_type: formData.kpiType,
                        organization_status: formData.organizationStatus
                    }
                });
            } else {
                // Create new item
                await createMutation.mutateAsync({
                    organization_status: formData.organizationStatus,
                    item_name: formData.name,
                    description: formData.description,
                    is_required: formData.isRequired,
                    kpi_type: formData.kpiType
                });
            }
            toast({
                title: item ? "Item updated" : "Item created",
                description: `Successfully ${item ? "updated" : "created"} checklist item "${formData.name}"`,
            });
            onClose();
        } catch (error) {
            console.error("Error saving checklist item:", error);
            toast({
                title: "Error",
                description: `Failed to ${item ? "update" : "create"} checklist item`,
                variant: "destructive",
            });
        }
    };

    return (
        <DialogContent>
            <DialogHeader>
                <DialogTitle>{item ? "Edit Checklist Item" : "New Checklist Item"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="organization_status">Status</Label>
                    <Select
                        value={formData.organizationStatus.toString()}
                        onValueChange={(value) => setFormData(prev => ({
                            ...prev,
                            organizationStatus: parseInt(value)
                        }))}
                    >
                        <SelectTrigger id="organization_status">
                            <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                            {orgStatuses?.map((status) => status.id !== undefined && (
                                <SelectItem
                                    key={status.id}
                                    value={status.id.toString()}
                                >
                                    {status.name || 'Unknown Status'}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter item name"
                        required
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter description (optional)"
                    />
                </div>
                <div className="flex items-center justify-between">
                    <Label htmlFor="required">Required</Label>
                    <Switch
                        id="required"
                        checked={formData.isRequired}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRequired: checked }))}
                    />
                </div>
                <KPITypeSelect
                    value={formData.kpiType}
                    onChange={(value) => setFormData(prev => ({ ...prev, kpiType: value }))}
                />
                <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={onClose}>
                        Cancel
                    </Button>
                    <Button type="submit">
                        {item ? "Update" : "Create"}
                    </Button>
                </div>
            </form>
        </DialogContent>
    );
}

export function CheckList({ caseId }: CheckListProps) {
    const queryClient = useQueryClient();
    const { data: checklistData, isLoading: isLoadingChecklist } = useCaseChecklistsQuery(caseId);
    const { data: selectedCase } = useFullCaseDetails(caseId);
    const { data: orgStatuses } = useOrganizationCaseStatuses();
    const updateChecklistItem = useUpdateCaseChecklistMutation(caseId);
    const { toast } = useToast();
    const [updatingItems, setUpdatingItems] = useState<number[]>([]);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<CaseChecklistItem | undefined>();
    const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
    const [showOptional, setShowOptional] = useState(true);

    // Group checklist items by status
    const groupedChecklists: GroupedChecklists = useMemo(() => {
        if (!orgStatuses) return {};

        // Initialize groups with all organization statuses
        const groups = orgStatuses.reduce((acc: GroupedChecklists, status) => {
            if (status.id !== undefined) {
                acc[status.id.toString()] = {
                    name: status.name || 'Unknown Status',
                    items: []
                };
            }
            return acc;
        }, {});

        // Add checklist items to their respective status groups
        if (checklistData && Array.isArray(checklistData)) {
            checklistData.forEach((item: CaseChecklist) => {
                // Filter out optional items if showOptional is false
                if (!showOptional && !item.is_required) {
                    return;
                }
                const statusKey = item.organization_status.toString();
                if (groups[statusKey]) {
                    groups[statusKey].items.push(item as CaseChecklistItem);
                }
            });

            // Sort items by order within each group
            Object.values(groups).forEach(group => {
                group.items.sort((a, b) => {
                    // Sort by ID in ascending order
                    return a.id - b.id;
                });
            });
        }

        return groups;
    }, [checklistData, orgStatuses, showOptional]);

    // Set initial selected status from the case's status
    useEffect(() => {
        if (selectedCase?.organization_status) {
            setSelectedStatus(typeof selectedCase.organization_status === 'object'
                ? selectedCase.organization_status.id.toString()
                : selectedCase.organization_status.toString());
        }
    }, [selectedCase?.organization_status]);

    const handleChecklistItemClick = async (item: CaseChecklistItem) => {
        try {
            setUpdatingItems(prev => [...prev, item.id]);
            const newIsCompleted = !item.is_completed;

            await updateChecklistItem.mutateAsync({
                checklistId: item.id.toString(),
                data: {
                    is_completed: newIsCompleted
                }
            });

            queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });

            toast({
                title: newIsCompleted ? "Item checked" : "Item unchecked",
                description: `Successfully ${newIsCompleted ? "completed" : "uncompleted"} "${item.item_name}"`,
            });
        } catch (error) {
            console.error('Error updating checklist item:', error);
            toast({
                title: "Error",
                description: "Failed to update checklist item",
                variant: "destructive",
            });
        } finally {
            setUpdatingItems(prev => prev.filter(id => id !== item.id));
        }
    };

    const handleEditItem = (item: CaseChecklistItem) => {
        setSelectedItem(item);
        setDialogOpen(true);
    };

    const handleToggleApplicable = async (item: CaseChecklistItem) => {
        try {
            setUpdatingItems(prev => [...prev, item.id]);
            const newIsApplicable = !item.is_applicable;

            await updateChecklistItem.mutateAsync({
                checklistId: item.id.toString(),
                data: {
                    is_applicable: newIsApplicable
                }
            });

            queryClient.invalidateQueries({ queryKey: ['case-checklists', caseId] });

            toast({
                title: newIsApplicable ? "Item marked as applicable" : "Item marked as not applicable",
                description: `Successfully marked "${item.item_name}" as ${newIsApplicable ? "applicable" : "not applicable"}`,
            });
        } catch (error) {
            console.error('Error updating checklist item:', error);
            toast({
                title: "Error",
                description: "Failed to update checklist item applicability",
                variant: "destructive",
            });
        } finally {
            setUpdatingItems(prev => prev.filter(id => id !== item.id));
        }
    };

    if (isLoadingChecklist) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
        );
    }

    const selectedStatusData = selectedStatus ? groupedChecklists[selectedStatus] : null;

    return (
        <div className="h-full flex flex-col min-h-0">

            <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Case Checklist
                </h2>
                <div className="flex gap-3">
                    <Select
                        value={selectedStatus || ""}
                        onValueChange={(value) => setSelectedStatus(value)}
                    >
                        <SelectTrigger id="status-select" className="w-[200px]">
                            <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Select Status</SelectItem>
                            {orgStatuses?.map((status) => status.id !== undefined && (
                                <SelectItem key={status.id} value={status.id.toString()}>
                                    {status.name || 'Unknown Status'}
                                    <span className="ml-2 text-gray-500">
                                        ({groupedChecklists[status.id.toString()]?.items.filter(item => item.is_completed).length || 0}/
                                        {groupedChecklists[status.id.toString()]?.items.length || 0})
                                    </span>
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    <div className="flex items-center gap-2">
                        <Label htmlFor="show-optional" className="text-sm text-gray-600">
                            {showOptional ? "Hide Optional" : "Show Optional"}
                        </Label>
                        <Switch
                            id="show-optional"
                            checked={showOptional}
                            onCheckedChange={setShowOptional}
                        />
                    </div>
                    {selectedCase?.organization_status && (
                        <Dialog open={dialogOpen} onOpenChange={(open) => {
                            setDialogOpen(open);
                            if (!open) setSelectedItem(undefined);
                        }}>
                            <DialogTrigger asChild>
                                <Button variant="link" size="sm" className="text-green-600 hover:text-green-700">
                                    <Plus className="h-4 w-4 mr-1" />
                                    Add Item
                                </Button>
                            </DialogTrigger>
                            <ChecklistItemDialog
                                caseId={caseId}
                                organizationStatusId={typeof selectedCase.organization_status === 'object'
                                    ? selectedCase.organization_status.id
                                    : Number(selectedCase.organization_status)}
                                item={selectedItem}
                                onClose={() => {
                                    setDialogOpen(false);
                                    setSelectedItem(undefined);
                                }}
                            />
                        </Dialog>
                    )}
                </div>
            </div>
            <div className="flex-1 overflow-y-auto min-h-0">
                <div className="p-4 space-y-4">
                    {selectedStatus === null ? (
                        // Show all statuses
                        Object.entries(groupedChecklists).map(([statusId, group]) => (
                            <div key={statusId} className="space-y-4">
                                <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 bg-blue-500 rounded-full" />
                                    <h3 className="text-sm font-medium text-gray-700">{group.name}</h3>
                                    <Badge variant="outline" className="text-xs ml-2">
                                        {group.items.filter(item => item.is_completed).length}/{group.items.length}
                                    </Badge>
                                </div>
                                {group.items.length > 0 ? (
                                    <div className="space-y-4 gap-5 bg-white">
                                        {group.items.map(item => (
                                            <ChecklistItem
                                                key={item.id}
                                                item={item}
                                                onClick={() => handleChecklistItemClick(item)}
                                                isUpdating={updatingItems.includes(item.id)}
                                                onEdit={() => handleEditItem(item)}
                                                onToggleApplicable={() => handleToggleApplicable(item)}
                                                caseId={caseId}
                                            />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="bg-white border rounded-lg p-4 text-center text-gray-500 text-sm">
                                        No checklist items yet
                                    </div>
                                )}
                            </div>
                        ))
                    ) : (
                        // Show selected status
                        selectedStatusData && (
                            <div className="space-y-4">
                                {selectedStatusData.items.length > 0 ? (
                                    <div className="space-y-4 bg-white">
                                        {selectedStatusData.items.map(item => (
                                            <ChecklistItem
                                                key={item.id}
                                                item={item}
                                                onClick={() => handleChecklistItemClick(item)}
                                                isUpdating={updatingItems.includes(item.id)}
                                                onEdit={() => handleEditItem(item)}
                                                onToggleApplicable={() => handleToggleApplicable(item)}
                                                caseId={caseId}
                                            />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="bg-white border rounded-lg p-4 text-center text-gray-500 text-sm">
                                        No checklist items yet
                                    </div>
                                )}
                            </div>
                        )
                    )}
                </div>
            </div>
        </div>
    );
}
