import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Search, Paperclip, Reply, Mail } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import emailService, { checkIntegrationStatus } from '@/services/emailCalenderIntegrationService';
import EmailList from '@/app/mails/components/emailList';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import EmailDetail from '@/app/mails/components/emailDetail';
import { ComposeMailProvider, useComposeMail } from '@/app/mails/components/composeMail';
const EMAILS_PER_PAGE = 20;

// Compose button component that uses the useComposeMail hook
function ComposeButton() {
  const { openComposer } = useComposeMail();

  return (
    <Button onClick={() => openComposer()} className="flex items-center gap-2">
      <Mail className="h-4 w-4" />
      Compose
    </Button>
  );
}

interface MailsProps {
  caseId: string;
}

export default function Mails({ caseId }: MailsProps) {
  const { toast } = useToast();
  const user = useSelector((state: RootState) => state.auth.user);
  const [isIntegrated, setIsIntegrated] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [selectedEmailIds, setSelectedEmailIds] = useState<string[]>([]);
  const [unreadFilter, setUnreadFilter] = useState<boolean | undefined>(undefined);
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [selectedEmailId, setSelectedEmailId] = useState<string | null>(null);
  const [currentTab, setCurrentTab] = useState('inbox');
  const attachEmailMutation = emailService.useAttachEmailToCase();

  // Get labels first
  const { data: labels } = emailService.useLabels();

  const { data: selectedEmail } = emailService.useEmail(selectedEmailId || '');
  const { data: emailData, isLoading: isLoadingEmails } = emailService.useLabelMessages(
    labels?.find(l => l.name.toLowerCase() === (currentTab === 'sent' ? 'sent' : 'inbox'))?.id || '',
    {
      page,
      limit: EMAILS_PER_PAGE,
      search: searchQuery,
      unread: unreadFilter,
      start_date: startDate || undefined,
      end_date: endDate || undefined
    }
  );
  useEffect(() => {
    checkIntegrationStatus().then(status => {
      setIsIntegrated(status?.is_active || false);
      setIsLoading(false);
    });
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchQuery(searchInput);
    setPage(1);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleAttachToCase = async () => {
    try {
      if (selectedEmailIds.length === 0) {
        toast({
          title: "Error",
          description: "Please select at least one email first",
          variant: "destructive"
        });
        return;
      }

      // Attach all selected emails to the case
      await Promise.all(
        selectedEmailIds.map(emailId =>
          attachEmailMutation.mutateAsync({
            messageId: emailId,
            caseId,
            userEmail: user?.email
          })
        )
      );

      setSelectedEmailIds([]); // Clear selection after attaching
      toast({
        title: "Success",
        description: `${selectedEmailIds.length} email(s) attached to case successfully`,
      });
    } catch (error) {
      console.error("Error attaching emails to case:", error);
      toast({
        title: "Error",
        description: "Failed to attach emails to case",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-full">Loading...</div>;
  }

  if (!isIntegrated) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h1 className="text-2xl font-bold mb-4">Email Integration Required</h1>
        <p className="mb-4">Please connect your email to continue</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="bg-white p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-semibold">All Emails</h2>
            <span className="text-sm text-muted-foreground">
              {emailData?.messages?.length || 0} messages
            </span>
          </div>
          <div className="flex items-center gap-2">
            <ComposeMailProvider initialCaseId={caseId} hideButton={true}>
              <ComposeButton />
            </ComposeMailProvider>
            <Button
              variant="outline"
              onClick={handleAttachToCase}
              disabled={selectedEmailIds.length === 0}
              className="flex items-center gap-2"
            >
              <Paperclip className="h-4 w-4" />
              Attach to Case ({selectedEmailIds.length})
            </Button>
          </div>
        </div>

        <Tabs value={currentTab} onValueChange={setCurrentTab} className="mb-4">
          <TabsList>
            <TabsTrigger value="inbox" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Inbox
            </TabsTrigger>
            <TabsTrigger value="sent" className="flex items-center gap-2">
              <Reply className="h-4 w-4" />
              Sent
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="flex flex-col gap-4">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                type="text"
                placeholder="Search emails..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="pl-9"
              />
            </div>
            <Button type="submit" variant="secondary">
              Search
            </Button>
          </div>

          <div className="flex gap-4 items-center">
            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600">Unread:</label>
              <select
                className="text-sm border rounded-md p-1"
                value={unreadFilter === undefined ? '' : unreadFilter.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  setUnreadFilter(value === '' ? undefined : value === 'true');
                }}
              >
                <option value="">All</option>
                <option value="true">Unread only</option>
                <option value="false">Read only</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600">From:</label>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="text-sm"
              />
            </div>

            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600">To:</label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="text-sm"
              />
            </div>
          </div>
        </form>
      </div>

      <div className="flex-1 overflow-auto p-4">
        {isLoadingEmails ? (
          <div className="flex items-center justify-center h-full">
            Loading...
          </div>
        ) : (
          <EmailList
            emails={emailData?.messages || []}
            currentPage={page}
            limit={EMAILS_PER_PAGE}
            onPageChange={handlePageChange}
            onEmailSelect={setSelectedEmailIds}
          // onEmailClick={(emailId) => setSelectedEmailId(emailId)}
          />
        )}
      </div>

      {/* Email Detail Dialog with Email Trail */}
      <EmailDetail
        email={selectedEmail || null}
        open={!!selectedEmailId}
        onOpenChange={(open) => !open && setSelectedEmailId(null)}
      />
    </div>
  );
} 