import { Card, CardContent } from "@/components/ui/card";
import {
  ExternalLink,
  Hospital,
  User,
  Hash,
  Building2,
  DollarSign,
  FileText,
  LucideIcon,
  Mail,
  Phone,
  Trash2,
  AlertCircle,
  Info as InfoIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import {
  useHealthInsurancesQuery,
  useDeleteHealthInsuranceMutation,
} from "@/services/case-management/healthInsuranceService";
import AddEditInsurance from "@/app/case/health-insurance/components/AddEditInsurance";
import { Button } from "@/components/ui/button";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { useState } from "react";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";

interface InfoFieldProps {
  icon: LucideIcon;
  label: string;
  value: string | number;
}

const InfoField = ({ icon: Icon, label, value }: InfoFieldProps) => (
  <div>
    <span className="text-sm text-gray-500 flex items-center gap-2 mb-1">
      <Icon className="h-4 w-4 text-gray-500" />
      {label}
    </span>
    <p className="text-sm font-medium text-[#060216] ml-6">{value}</p>
  </div>
);

export function HealthBills({ caseId }: { caseId: string }) {
  const router = useRouter();
  const { data: healthInsurances, isLoading } =
    useHealthInsurancesQuery(caseId);
  const deleteInsurance = useDeleteHealthInsuranceMutation();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedInsuranceId, setSelectedInsuranceId] = useState<string>("");

  const statistics = {
    totalLiens:
      healthInsurances?.reduce(
        (sum, insurance) =>
          sum + (parseFloat(insurance.total_lien || "0") || 0),
        0
      ) || 0,
    adjustedLiens:
      healthInsurances?.reduce(
        (sum, insurance) =>
          sum + (parseFloat(insurance.adjusted_lien || "0") || 0),
        0
      ) || 0,
  };

  const handleViewDetails = () => {
    router.push(`/case/health-insurance/${caseId}`);
  };

  return (
    <div className="w-full mt-4 flex flex-col gap-8">
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm font-medium text-gray-600">TOTAL LIENS</h3>
            <p className="text-2xl font-bold text-green-500">
              $
              {statistics.totalLiens.toLocaleString("en-US", {
                minimumFractionDigits: 2,
              })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm font-medium text-gray-600">
              ADJUSTED LIENS
            </h3>
            <p className="text-2xl font-bold text-green-500">
              $
              {statistics.adjustedLiens.toLocaleString("en-US", {
                minimumFractionDigits: 2,
              })}
            </p>
          </CardContent>
        </Card>
      </div>

      <AddEditInsurance isEdit={false} caseId={caseId} />

      {/* Records Section */}
      <div className="flex flex-col gap-4">
        {isLoading ? (
          <div>Loading...</div>
        ) : (
          healthInsurances?.map((insurance, index) => (
            <Card
              key={insurance.id}
              className="hover:shadow-lg transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <div className="flex items-center gap-3">
                    {insurance.no_insurance ? (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    ) : (
                      <Hospital className="h-5 w-5 text-gray-500" />
                    )}
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold text-[#060216]">
                        {insurance.no_insurance
                          ? "No Insurance"
                          : `INSURANCE ${index + 1} - ${
                              typeof insurance.insurance_company === "object"
                                ? insurance.insurance_company?.name
                                : "No Insurance Company"
                            }`}
                      </h3>
                      {insurance.no_insurance && (
                        <p className="text-sm text-[#060216]/70">
                          Client has confirmed they do not have health insurance
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2 items-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedInsuranceId(insurance?.id?.toString() || "");
                        setDeleteDialogOpen(true);
                      }}
                      className="hover:bg-gray-100"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                    <ManageEmailTemplate 
                      caseId={caseId}
                      healthInsuranceId={insurance.id?.toString()}
                      contextType={"client_health_insurance" as TemplateContextType}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewDetails();
                      }}
                      className="hover:bg-gray-100"
                    >
                      <ExternalLink className="h-4 w-4 text-gray-500" />
                    </Button>
                  </div>
                </div>

                {!insurance.no_insurance ? (
                  <div className="grid grid-cols-2 gap-x-12 gap-y-6">
                    <div className="space-y-4">
                      <InfoField
                        icon={User}
                        label="INSURED"
                        value={insurance.insured || "—"}
                      />
                      <InfoField
                        icon={Hash}
                        label="GROUP NUMBER"
                        value={insurance.group_number || "—"}
                      />
                      <InfoField
                        icon={Hash}
                        label="MEMBER NUMBER"
                        value={insurance.member_number || "—"}
                      />
                      <InfoField
                        icon={Building2}
                        label="COMPANY"
                        value={
                          typeof insurance.insurance_company === "object"
                            ? insurance.insurance_company?.name
                            : "—"
                        }
                      />
                    </div>
                    <div className="space-y-4">
                      <InfoField
                        icon={DollarSign}
                        label="TOTAL LIEN"
                        value={insurance.total_lien || "0.00"}
                      />
                      <InfoField
                        icon={FileText}
                        label="FILE NUMBER"
                        value={insurance.file_number || "—"}
                      />
                      <InfoField
                        icon={FileText}
                        label="PLAN TYPE"
                        value={insurance.plan_type || "—"}
                      />
                      {typeof insurance.insurance_company === "object" && (
                        <>
                          <InfoField
                            icon={Mail}
                            label="COMPANY EMAIL"
                            value={insurance.insurance_company?.email || "—"}
                          />
                          <InfoField
                            icon={Phone}
                            label="COMPANY PHONE"
                            value={insurance.insurance_company?.phone || "—"}
                          />
                        </>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="bg-orange-50 p-4 rounded-lg mt-2">
                    <div className="flex items-center gap-3 text-orange-700">
                      <InfoIcon className="w-5 h-5" />
                      <p className="text-sm">
                        This record indicates the client has no health
                        insurance. No additional insurance details are required.
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        text="Health Insurance"
        onConfirm={() => {
          deleteInsurance.mutate({ caseId, insuranceId: selectedInsuranceId });
          setDeleteDialogOpen(false);
        }}
      />
    </div>
  );
}
