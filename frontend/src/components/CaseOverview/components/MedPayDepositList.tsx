import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DollarSign, PlusCircle, Trash2, ClipboardList } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { 
  useMedPayDepositsQuery, 
  useMedPayDepositSummaryQuery, 
  useDeleteMedPayDepositMutation,
  useTreatmentProvidersQuery
} from "@/services/case-management/medicalTreatmentService";
import { AddEditMedPayDeposit } from "./AddEditMedPayDeposit";
import { CurrencyDisplay } from "@/components/ui/currency-display";
import { KPICard } from "@/components/ui/kpi-card";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { formatDateForDisplay } from "@/utils/dateUtils";
import { 
  MedPayDepositStatus, 
  MedPayDepositResponse,
  MedPayDeposit,
} from "@/type/case-management/medicalTreatmentTypes";

interface MedPayDepositListProps {
  caseId: string;
  onSuccess?: () => void;
}

export function MedPayDepositList({ caseId, onSuccess, ...props }: MedPayDepositListProps) {
  const [open, setOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedDepositId, setSelectedDepositId] = useState<number | null>(null);
  
  const { data: deposits, isLoading: isLoadingDeposits, refetch } = useMedPayDepositsQuery(caseId);
  const { data: summary, isLoading: isLoadingSummary } = useMedPayDepositSummaryQuery(caseId);
  const deleteMutation = useDeleteMedPayDepositMutation();
  const { data: treatments = [] } = useTreatmentProvidersQuery(caseId);

  const handleDeleteClick = (depositId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedDepositId(depositId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedDepositId) {
      await deleteMutation.mutateAsync({ caseId, depositId: selectedDepositId });
      setDeleteDialogOpen(false);
      setSelectedDepositId(null);
      await refetch();
      if (onSuccess) onSuccess();
    }
  };

  const handleSuccess = async () => {
    await refetch();
    if (onSuccess) onSuccess();
  };

  /**
   * Helper function to get provider name from a MedPayDepositResponse object
   * @param deposit The MedPayDeposit response object that contains the provider information
   * @returns The provider company name
   */
  const getProviderName = (deposit: MedPayDepositResponse): string => {
    // If the medical_provider is an object with a company property, use it directly
    if (deposit.medical_provider && deposit.medical_provider.company) {
      return deposit.medical_provider.company;
    }
    if (!deposit.medical_provider) {
      return "-";
    }

    // Fallback to finding provider in treatments list
    const providerId = typeof deposit.medical_provider === 'object' 
      ? deposit.medical_provider.id 
      : deposit.medical_provider;
    
    const treatment = treatments.find(t => {
      const id = typeof t.medical_provider === 'object' 
        ? t.medical_provider.id 
        : t.medical_provider;
      return id === providerId;
    });

    if (!treatment) return `Provider ${providerId}`;

    return typeof treatment.medical_provider === 'object'
      ? treatment.medical_provider.company
      : `Provider ${treatment.medical_provider}`;
  };

  /**
   * Helper to prepare deposit for editing - converts complex API response to format expected by edit form
   * @param deposit The MedPayDepositResponse from the API
   * @returns MedPayDeposit object with simple ID references that the form can use
   */
  const prepareDepositForEdit = (deposit: MedPayDepositResponse): MedPayDeposit => {
    return {
      id: deposit.id,
      case: deposit.case,
      insurance_type: deposit.insurance_type,
      client_insurance: deposit.client_insurance?.id,
      defendant_insurance: deposit.defendant_insurance?.id,
      medical_provider: deposit.medical_provider ? (typeof deposit.medical_provider === 'object' 
        ? deposit.medical_provider.id 
        : deposit.medical_provider) : -1,
      amount: deposit.amount,
      check_number: deposit.check_number,
      status: deposit.status,
      medpay_request_date: deposit.medpay_request_date,
      medpay_deposit_date: deposit.medpay_deposit_date,
      client_trust_entry: deposit.client_trust_entry,
      note: deposit.note,
      created_at: deposit.created_at,
      updated_at: deposit.updated_at
    };
  };

  console.log(deposits, "deposits");

  return (
    <>
      <Button
        variant="link"
        className="text-green-600"
        onClick={() => setOpen(true)}
        {...props}
      >
        <ClipboardList className="h-4 w-4 mr-2" />
        MedPay Deposits Management
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>MedPay Deposits Management</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Summary</h3>
              <AddEditMedPayDeposit onSuccess={handleSuccess} />
            </div>

            {isLoadingSummary ? (
              <div>Loading summary...</div>
            ) : summary ? (
              <div className="grid grid-cols-4 gap-4">
                <KPICard
                  icon={DollarSign}
                  title="Total Requested"
                  metrics={{
                    primary: {
                      type: 'monetary',
                      value: summary.total_requested || 0
                    }
                  }}
                  isCurrency={true}
                />
                <KPICard
                  icon={DollarSign}
                  title="Total Paid"
                  metrics={{
                    primary: {
                      type: 'monetary',
                      value: summary.total_paid || 0
                    }
                  }}
                  isCurrency={true}
                />
                <KPICard
                  icon={DollarSign}
                  title="Pending Amount"
                  metrics={{
                    primary: {
                      type: 'monetary',
                      value: summary.pending_amount || 0
                    }
                  }}
                  isCurrency={true}
                />
                <KPICard
                  icon={PlusCircle}
                  title="Deposit Count"
                  metrics={{
                    primary: {
                      type: 'count',
                      value: summary.deposit_count || 0
                    }
                  }}
                />
              </div>
            ) : (
              <div>No summary data available</div>
            )}

            <Separator className="my-4" />

            <h3 className="text-lg font-semibold">Deposits</h3>
            
            {isLoadingDeposits ? (
              <div>Loading deposits...</div>
            ) : deposits && deposits.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-medium text-center">Insurance Type</TableHead>
                    <TableHead className="font-medium text-center">Provider</TableHead>
                    <TableHead className="font-medium text-center">Status</TableHead>
                    <TableHead className="font-medium text-center">Request Date</TableHead>
                    <TableHead className="font-medium text-center">Deposit Date</TableHead>
                    <TableHead className="font-medium text-center">Check #</TableHead>
                    <TableHead className="font-medium text-center">Amount</TableHead>
                    <TableHead className="font-medium text-center">Note</TableHead>
                    <TableHead className="font-medium text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deposits.map((deposit: MedPayDepositResponse) => (
                    <TableRow key={deposit.id} className="hover:bg-gray-50">
                      <TableCell className="text-center">
                        {deposit.insurance_type_display || 
                         (deposit.insurance_type === "CLIENT_MEDPAY" ? "Client MedPay" : "Third Party MedPay")}
                      </TableCell>
                      <TableCell className="text-center">
                        {getProviderName(deposit)}
                      </TableCell>
                      <TableCell className="text-center">
                        <span 
                          className={`px-2 py-1 rounded-md text-xs font-medium ${
                            deposit.status === MedPayDepositStatus.PAID 
                              ? "bg-green-50 text-green-800 ring-1 ring-green-600/20" 
                              : "bg-yellow-50 text-yellow-800 ring-1 ring-yellow-600/20"
                          }`}
                        >
                          {deposit.status_display || 
                           (deposit.status === MedPayDepositStatus.PAID ? "Paid" : "Invoice Raised")}
                        </span>
                      </TableCell>
                      <TableCell className="text-center">
                        {formatDateForDisplay(deposit.medpay_request_date)}
                      </TableCell>
                      <TableCell className="text-center">
                        {deposit.medpay_deposit_date 
                          ? formatDateForDisplay(deposit.medpay_deposit_date) 
                          : "—"}
                      </TableCell>
                      <TableCell className="text-center">
                        {deposit.check_number || "—"}
                      </TableCell>
                      <TableCell className="text-center">
                        <CurrencyDisplay amount={deposit.amount} />
                      </TableCell>
                      <TableCell className="text-center">
                        {deposit.note || "—"}
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex justify-center gap-2">
                          <AddEditMedPayDeposit 
                            isEdit={true} 
                            selectedDeposit={prepareDepositForEdit(deposit)} 
                            onSuccess={handleSuccess} 
                          />
                          <Button
                            variant="link"
                            className="p-0"
                            onClick={(e) => handleDeleteClick(deposit.id, e)}
                          >
                            <Trash2 className="h-4 w-4 text-red-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-gray-500 text-center py-4">No MedPay deposits found</div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        text="MedPay Deposit"
      />
    </>
  );
} 