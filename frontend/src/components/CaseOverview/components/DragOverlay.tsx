import React from 'react';
import { Upload } from 'lucide-react';
import { UploadingFile } from '../types/fileManager.types';

interface DragOverlayProps {
  isDragActive: boolean;
  isDraggingImage: boolean;
  uploadingFiles: UploadingFile[];
}

export const DragOverlay: React.FC<DragOverlayProps> = ({
  isDragActive,
  isDraggingImage,
  uploadingFiles
}) => {
  if (!isDragActive && !isDraggingImage || uploadingFiles.length > 0) {
    return null;
  }

  return (
    <div className="absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center rounded-lg">
      <div className="text-center">
        <Upload className="mx-auto h-16 w-16 text-blue-500" />
        <p className="mt-2 text-blue-600 font-medium">
          Drop here to upload
        </p>
      </div>
    </div>
  );
}; 