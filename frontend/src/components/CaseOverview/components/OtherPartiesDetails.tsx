"use client";

import { notFound } from "next/navigation";
import { Plus, Edit, Trash2, User, Users } from "lucide-react";
// import PartyCard from "@/app/case/caseparty/components/PartyCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import AddEditCaseParty from "@/app/case/caseparty/components/AddEditCaseParty";
import { useCasePartiesQuery, useCaseExpertWitnessesQuery, useDeleteCasePartyMutation } from "@/services/case-management/partyService";
import {
  CaseParty,
  PartyType,
  PartyStatus,
} from "@/type/case-management/partyTypes";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import SelectWitness from "@/app/case/caseparty/components/SelectWitness";
import ExpertWitnessList from "@/app/case/caseparty/components/ExpertWitnessList";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import React, { Fragment, useState } from "react";
import { Card } from "@/components/ui/card";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { Separator } from "@/components/ui/separator";
import { AddressLink } from "@/components/gMap/address-link";
import { PhoneLink } from "@/components/ui/phone-link";
import { MailLink } from "@/components/ui/mail-link";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { KPICard } from "@/components/ui/kpi-card";

interface CasePartyPageProps {
  caseId: string;
}

const getContextTypeAndId = (partyType: PartyType) => {
  switch (partyType) {
    case PartyType.OTHER:
      return { contextType: 'other_party' as TemplateContextType, other_party_id: '' };
    case PartyType.PLAINTIFF:
      return { contextType: 'other_plaintiff' as TemplateContextType, other_plaintiff_id: '' };
    case PartyType.WITNESS:
      return { contextType: 'witness' as TemplateContextType, witness_id: '' };
    default:
      return { contextType: 'other_party' as TemplateContextType, other_party_id: '' };
  }
};

export default function OtherParties({ caseId }: CasePartyPageProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedParty, setSelectedParty] = useState<CaseParty | null>(null);
  // const [filteredParties, setFilteredParties] = useState<CaseParty[]>([]);

  if (!caseId) {
    notFound();
  }

  const { data: parties = [], isLoading: isLoadingParties } = useCasePartiesQuery(caseId);
  const { data: expertWitnesses = [], isLoading: isLoadingExpertWitnesses } = useCaseExpertWitnessesQuery(caseId);
  const deletePartyMutation = useDeleteCasePartyMutation(caseId, selectedParty?.id || 0);

  if (isLoadingParties || isLoadingExpertWitnesses) {
    return <div className="min-h-screen bg-gray-50 p-8">Loading...</div>;
  }

  const handleDeleteParty = (party: CaseParty) => {
    setSelectedParty(party);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedParty) {
      try {
        await deletePartyMutation.mutateAsync();
        setDeleteDialogOpen(false);
        setSelectedParty(null);
      } catch (error) {
        console.error('Error deleting party:', error);
      }
    }
  };

  const plaintiffParties = parties.filter(
    (party: CaseParty) => party.contact?.party_type === PartyType.PLAINTIFF
  );
  const witnessParties = parties.filter(
    (party: CaseParty) => party.contact?.party_type === PartyType.WITNESS
  );
  const otherParties = parties.filter(
    (party: CaseParty) => party.contact?.party_type === PartyType.OTHER
  );

  const renderPartyCards = (filteredParties: CaseParty[]) => {
    if (filteredParties.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">No parties found.</div>
      );
    }

    return (
      <div className="space-y-6">
        {filteredParties.map((party, index) => {
          const { contextType, ...idProps } = getContextTypeAndId(party.contact.party_type);
          const idKey = Object.keys(idProps)[0];

          return (
            <Fragment key={party.id}>
              {index > 0 && (
                <Separator className="my-6 bg-gray-200" />
              )}
              <Card className="rounded-lg border-none">
                <div className="space-y-6">
                  {/* Header with Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <User className="h-4 w-4 text-[#060216]" />
                      <h2 className="text-[14px] font-medium text-[#060216]">
                        {party.contact?.party_type === PartyType.PLAINTIFF ? 'Plaintiff' :
                          party.contact?.party_type === PartyType.WITNESS ? 'Witness' : 'Other Party'} - {index + 1}
                      </h2>
                    </div>
                    <div className="flex items-center gap-2">
                      <AddEditCaseParty
                        caseId={caseId}
                        isEdit={true}
                        defaultValues={{
                          status: party.status,
                          description: party.description,
                          party_contact: party.contact?.id?.toString(),
                          law_firm: party.law_firm?.id?.toString(),
                          attorney: party.attorney?.id?.toString(),
                          party_type: party.contact.party_type,
                        }}
                        partyId={party.id.toString()}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50 p-2 h-8 w-8"
                          >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2 h-8 w-8"
                        onClick={() => handleDeleteParty(party)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600 cursor-pointer" />
                      </Button>
                      <ManageEmailTemplate
                        caseId={caseId}
                        contextType={contextType as TemplateContextType}
                        {...{ [idKey]: party.id.toString() }}
                      />
                    </div>
                  </div>

                  {/* First Row - Name, Type, Status */}
                  <div className="grid grid-cols-3 gap-12">
                    <div className="space-y-2">
                      <p className="text-[#06021685] text-xs font-medium">Name</p>
                      <p className="text-[#060216] text-sm font-semibold">
                        {`${party.contact?.first_name || ''} ${party.contact?.last_name || ''}`}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-[#06021685] text-xs font-medium">Type</p>
                      <p className="text-[#060216] text-sm font-semibold">
                        {party.contact?.party_type || '—'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-[#06021685] text-xs font-medium">Status</p>
                      <p className="text-[#060216] text-sm font-semibold">
                        {party.status || '—'}
                      </p>
                    </div>
                  </div>

                  {/* Second Row - Phone, Law Firm, Email */}
                  <div className="grid grid-cols-3 gap-12">
                    <div className="space-y-2">
                      <p className="text-[#06021685] text-xs font-medium">Phone</p>
                      <PhoneLink phone={party.contact?.phone} />
                    </div>
                    <div className="space-y-2">
                      <p className="text-[#06021685] text-xs font-medium">Law Firm</p>
                      <p className="text-[#060216] text-sm font-semibold">
                        {party.law_firm?.office_name || '—'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-[#06021685] text-xs font-medium">Email</p>
                      <MailLink email={party.contact?.email} caseId={caseId} />
                    </div>
                  </div>

                  {/* Third Row - Address */}
                  <div className="space-y-2">
                    <p className="text-[#06021685] text-xs font-medium">Address</p>
                    <AddressLink
                      address={{
                        street1: party.contact?.street1,
                        street2: party.contact?.street2,
                        city: party.contact?.city,
                        state: party.contact?.state,
                        zip_code: party.contact?.zip_code
                      }}
                      className="text-[#060216] text-sm font-semibold"
                    >
                      {party.contact?.street1 ?
                        `${party.contact.street1}${party.contact.street2 ? `, ${party.contact.street2}` : ''}, ${party.contact.city || ''}, ${party.contact.state || ''} ${party.contact.zip_code || ''}`
                        : '—'}
                    </AddressLink>
                  </div>

                  {/* Fourth Row - Description */}
                  <div className="bg-[#F7F7F7] rounded-lg border border-[#00000014] p-2">
                    <p className="text-[#06021685] text-xs font-medium mb-1">Description</p>
                    <div className="text-[#060216] text-sm font-semibold">
                      <RichTextViewer data={party.description || ''} />
                    </div>
                  </div>
                </div>
              </Card>
            </Fragment>
          );
        })}
      </div>
    );
  };

  return (
    <div className="min-h-screen space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          Other Parties
        </h2>
        <div className="flex">
          <SelectWitness
            caseId={caseId}
          />
          <AddEditCaseParty
            caseId={caseId}
            defaultValues={{
              party_type: PartyType.PLAINTIFF,
              status: PartyStatus.PENDING_CONTACT,
              party_contact: ""
            }}
            trigger={
              <Button
                variant="link"
                className="text-green-600"
              >
                <Plus className="h-4 w-4 stroke-[1.5] mr-2" />
                Plaintiff
              </Button>
            }
          />
          <AddEditCaseParty
            caseId={caseId}
            defaultValues={{
              party_type: PartyType.WITNESS,
              status: PartyStatus.PENDING_CONTACT,
              party_contact: ""
            }}
            trigger={
              <Button
                variant="link"
                className="text-green-600"
              >
                <Plus className="h-4 w-4 stroke-[1.5] mr-2" />
                Witness
              </Button>
            }
          />
          <AddEditCaseParty
            caseId={caseId}
            defaultValues={{
              party_type: PartyType.OTHER,
              status: PartyStatus.PENDING_CONTACT,
              party_contact: ""
            }}
            trigger={
              <Button
                variant="link"
                className="text-green-600"
              >
                <Plus className="h-4 w-4 stroke-[1.5] mr-2" />
                Other Party
              </Button>
            }
          />
        </div>
      </div>

      {/* KPI Cards Grid */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <KPICard
          icon={Users}
          title="Expert Witnesses"
          metrics={{
            primary: {
              type: 'count',
              value: expertWitnesses.length
            },
          }}
        />

        <KPICard
          icon={User}
          title="Plaintiffs"
          metrics={{
            primary: {
              type: 'count',
              value: plaintiffParties.length
            },
          }}
        />

        <KPICard
          icon={User}
          title="Witnesses"
          metrics={{
            primary: {
              type: 'count',
              value: witnessParties.length
            },
          }}
        />

        <KPICard
          icon={User}
          title="Other Parties"
          metrics={{
            primary: {
              type: 'count',
              value: otherParties.length
            },
          }}
        />
      </div>

      <Tabs defaultValue="expert-witnesses" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="expert-witnesses" className="text-sm">
            Expert Witnesses ({expertWitnesses.length})
          </TabsTrigger>
          <TabsTrigger value="plaintiffs" className="text-sm">
            Plaintiffs ({plaintiffParties.length})
          </TabsTrigger>
          <TabsTrigger value="witnesses" className="text-sm">
            Witnesses ({witnessParties.length})
          </TabsTrigger>
          <TabsTrigger value="others" className="text-sm">
            Other Parties ({otherParties.length})
          </TabsTrigger>
        </TabsList>
        <TabsContent value="expert-witnesses">
          <ExpertWitnessList caseId={caseId} />
        </TabsContent>
        <TabsContent value="plaintiffs">
          {renderPartyCards(plaintiffParties)}
        </TabsContent>
        <TabsContent value="witnesses">
          {renderPartyCards(witnessParties)}
        </TabsContent>
        <TabsContent value="others">
          {renderPartyCards(otherParties)}
        </TabsContent>
      </Tabs>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        title="Delete Party"
        description={`Are you sure you want to delete ${selectedParty?.contact?.first_name} ${selectedParty?.contact?.last_name}? This action cannot be undone.`}
      />
    </div>
  );
}
