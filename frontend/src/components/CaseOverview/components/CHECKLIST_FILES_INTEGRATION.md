# Checklist Files Integration

This document explains how the file selection functionality has been integrated into the existing checklist component.

## Overview

The checklist component now allows users to associate files with checklist items. Users can:

1. View files associated with a checklist item
2. Add new files to a checklist item
3. Remove files from a checklist item
4. Navigate through folders to find and select files

## Components Added

1. **FileSelector.tsx**: A dialog component that allows users to select files from the case folder.
   - Supports folder navigation
   - Displays breadcrumb navigation
   - Allows searching for files and folders
   - Maintains selected files across folder navigation

2. **ChecklistItemFiles.tsx**: A component that displays the files associated with a checklist item.

## Changes Made

1. **CheckList.tsx**:
   - Updated the `ChecklistItem` component to include file selection functionality
   - Added a button to open the file selector
   - Added a section to display associated files
   - Added context menu item for file management

2. **noteTaskService.ts**:
   - Updated the `CaseChecklist` interface to include the `file_ids` field
   - Updated the `CaseChecklistUpdate` interface to include the `file_ids` field
   - Added a new `useUpdateChecklistItemMutation` hook to update checklist items with file IDs

## How It Works

1. Each checklist item displays an "Attach Files" button on the right side, next to the "Applicable" toggle
2. If files are already associated with the item, the button shows the number of attached files
3. When a user clicks on the "Attach Files" button, the file selector dialog opens
4. In the file selector:
   - The user can navigate through folders by clicking on them
   - The breadcrumb navigation shows the current path and allows quick navigation
   - The user can search for files and folders
   - The user can select multiple files from any folder
   - The "Select All" option selects all files in the current folder
5. When the user saves the selection, the checklist item is updated with the selected file IDs
6. When a user clicks on a checklist item with attached files, it expands to show the list of files
7. The user can remove files from the checklist item by clicking the remove button in the expanded view

## Folder Navigation

The file selector now supports folder navigation with the following features:

1. **Folder Browsing**: Users can click on folders to navigate into them
2. **Breadcrumb Navigation**: Shows the current path and allows quick navigation to parent folders
3. **Back Button**: Allows users to navigate back to the parent folder
4. **Root Navigation**: Users can quickly return to the root folder
5. **Search Within Folders**: Search functionality works within the current folder

## API Integration

The component uses the `useUpdateChecklistItemMutation` hook to update the checklist item with the selected file IDs. The hook sends a POST request to the `/api/v1/case-management/cases/{caseId}/update_checklist_item/` endpoint with the following payload:

```json
{
  "checklist_item_id": 123,
  "is_completed": true,
  "file_ids": ["file-id-1", "file-id-2", "file-id-3"]
}
```

The component also uses the following API endpoints for folder navigation:

- `GET /api/v1/storage/case-contents/{caseId}/`: Get the contents of the case's root folder
- `GET /api/v1/storage/folder/{folderId}/contents/`: Get the contents of a specific folder

## Usage

The file selection functionality is automatically available in the existing checklist component. No additional configuration is required.

## Future Improvements

1. Add file preview functionality
2. Add drag-and-drop file upload
3. Add file type filtering
4. Add multi-folder selection
