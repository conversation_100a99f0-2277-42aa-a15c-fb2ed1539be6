"use client"

import React, { useState, useCallback, useMemo, memo, ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Briefcase,
  ChevronUp,
  ExternalLink,
  Building2,
  User,
  Phone,
  Mail,
  FileText,
  DollarSign,
  LucideIcon,
  Trash2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useAttorneyLiensQuery, useMiscLiensQuery, useDeleteAttorneyLienMutation, useDeleteMiscLienMutation } from "@/services/case-management/lienService";
import { Skeleton } from "@/components/ui/skeleton";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { CaseAttorneyLienResponse, CaseMiscellaneousLienResponse } from "@/type/case-management/lienTypes";
import RichTextViewer from "@/components/ui/RichTextViewer";

interface InfoFieldProps {
  icon: LucideIcon;
  label: string;
  value: string | number | ReactNode;
}

const InfoField = memo(({ icon: Icon, label, value }: InfoFieldProps) => (
  <div>
    <span className="text-sm text-gray-500 flex items-center gap-2 mb-1">
      <Icon className="h-4 w-4 text-gray-500" />
      {label}
    </span>
    <p className="text-sm font-medium text-[#060216]">{value || '—'}</p>
  </div>
));
InfoField.displayName = 'InfoField';

interface StatCardProps {
  title: string;
  value: string | number;
}

const StatCard = memo(({ title, value }: StatCardProps) => (
  <Card className="bg-white hover:shadow-lg transition-shadow duration-200 border border-'hsl(0 0% 89.8%)' shadow-sm">
    <CardContent className="pt-6">
      <h3 className="text-sm font-medium text-gray-600">{title}</h3>
      <p className="text-2xl font-bold text-green-500">{value}</p>
    </CardContent>
  </Card>
));
StatCard.displayName = 'StatCard';

interface LienCardProps {
  lien: CaseAttorneyLienResponse | CaseMiscellaneousLienResponse;
  type: 'attorney' | 'misc';
  onDelete: (id: string) => void;
}

const LienCard = memo(({ lien, type, onDelete }: LienCardProps) => {
  const handleDelete = useCallback(() => {
    onDelete(lien.id?.toString() || "");
  }, [lien.id, onDelete]);

  const isAttorneyLien = type === 'attorney';
  const title = isAttorneyLien
    ? (lien as CaseAttorneyLienResponse).law_firm.office_name
    : (lien as CaseMiscellaneousLienResponse).lien_name;

  return (
    <Card className="hover:shadow-lg transition-shadow w-full">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-[#060216]" />
            <h3 className="text-lg font-semibold text-[#060216]">{title}</h3>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDelete}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-x-12 gap-y-6">
          {isAttorneyLien ? (
            <>
              <InfoField
                icon={User}
                label="Attorney"
                value={
                  <>
                    {(lien as CaseAttorneyLienResponse).attorney ? (
                      <p className="text-sm text-gray-500">
                        {[(lien as CaseAttorneyLienResponse).attorney?.first_name, (lien as CaseAttorneyLienResponse).attorney?.last_name]
                          .filter(Boolean)
                          .join(" ")}
                      </p>
                    ) : '—'}
                  </>
                }
              />
              <InfoField
                icon={DollarSign}
                label="Lien Amount"
                value={`$${parseFloat((lien as CaseAttorneyLienResponse).fee_amount).toFixed(2)}`}
              />
              <InfoField
                icon={Phone}
                label="Phone"
                value={(lien as CaseAttorneyLienResponse).attorney.phone}
              />
              <InfoField
                icon={Mail}
                label="Email"
                value={(lien as CaseAttorneyLienResponse).attorney.email}
              />
              <InfoField
                icon={FileText}
                label="Note"
                value={(lien as CaseAttorneyLienResponse).note ? <RichTextViewer data={(lien as CaseAttorneyLienResponse).note || ''} /> : '—'}
              />
            </>
          ) : (
            <>
              <InfoField
                icon={Building2}
                label="Lien Holder"
                value={
                  <>
                    {(lien as CaseMiscellaneousLienResponse).lien_holder.company}
                    {((lien as CaseMiscellaneousLienResponse).lien_holder.first_name || (lien as CaseMiscellaneousLienResponse).lien_holder.last_name) && (
                      <p className="text-sm text-gray-500">
                        {[(lien as CaseMiscellaneousLienResponse).lien_holder.first_name, (lien as CaseMiscellaneousLienResponse).lien_holder.last_name]
                          .filter(Boolean)
                          .join(" ")}
                      </p>
                    )}
                  </>
                }
              />
              <InfoField
                icon={DollarSign}
                label="Lien Amount"
                value={`$${parseFloat((lien as CaseMiscellaneousLienResponse).lien_amount).toFixed(2)}`}
              />
              <InfoField
                icon={Phone}
                label="Phone"
                value={(lien as CaseMiscellaneousLienResponse).lien_holder.phone}
              />
              <InfoField
                icon={Mail}
                label="Email"
                value={(lien as CaseMiscellaneousLienResponse).lien_holder.email}
              />
              <InfoField
                icon={FileText}
                label="Description"
                value={(lien as CaseMiscellaneousLienResponse).description ? <RichTextViewer data={(lien as CaseMiscellaneousLienResponse).description || ""} /> : "—"}
              />
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
});
LienCard.displayName = 'LienCard';

export function Liens({ caseId }: { caseId: string }) {
  const router = useRouter();
  const [isAttorneyOpen, setIsAttorneyOpen] = useState(true);
  const [isMiscOpen, setIsMiscOpen] = useState(true);
  const [deleteAttorneyDialogOpen, setDeleteAttorneyDialogOpen] = useState(false);
  const [deleteMiscDialogOpen, setDeleteMiscDialogOpen] = useState(false);
  const [selectedLienId, setSelectedLienId] = useState<string>("");

  const { data: attorneyLiens = [], isLoading: isLoadingAttorneyLiens } = useAttorneyLiensQuery(caseId);
  const { data: miscLiens = [], isLoading: isLoadingMiscLiens } = useMiscLiensQuery(caseId);
  const deleteAttorneyLien = useDeleteAttorneyLienMutation(caseId, selectedLienId);
  const deleteMiscLien = useDeleteMiscLienMutation(caseId, selectedLienId);

  const handleViewDetails = useCallback(() => {
    router.push(`/case/liens/${caseId}`);
  }, [router, caseId]);

  const handleDeleteAttorneyLien = useCallback(async () => {
    try {
      await deleteAttorneyLien.mutateAsync();
      setDeleteAttorneyDialogOpen(false);
    } catch (error) {
      console.error('Failed to delete attorney lien:', error);
    }
  }, [deleteAttorneyLien]);

  const handleDeleteMiscLien = useCallback(async () => {
    try {
      await deleteMiscLien.mutateAsync();
      setDeleteMiscDialogOpen(false);
    } catch (error) {
      console.error('Failed to delete misc lien:', error);
    }
  }, [deleteMiscLien]);

  const handleAttorneyLienDelete = useCallback((id: string) => {
    setSelectedLienId(id);
    setDeleteAttorneyDialogOpen(true);
  }, []);

  const handleMiscLienDelete = useCallback((id: string) => {
    setSelectedLienId(id);
    setDeleteMiscDialogOpen(true);
  }, []);

  const { attorneyLiensTotal, miscLiensTotal } = useMemo(() => ({
    attorneyLiensTotal: attorneyLiens.reduce((acc, lien) => acc + parseFloat(lien.fee_amount || "0"), 0),
    miscLiensTotal: miscLiens.reduce((acc, lien) => acc + parseFloat(lien.lien_amount || "0"), 0)
  }), [attorneyLiens, miscLiens]);

  if (isLoadingAttorneyLiens || isLoadingMiscLiens) {
    return (
      <div className="w-full mt-4 flex flex-col gap-8">
        <div className="grid grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full mt-4 flex flex-col gap-8">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Briefcase className="h-5 w-5 text-[#060216]" />
            <h2 className="text-lg font-semibold text-[#060216]">Liens</h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleViewDetails}
          >
            <ExternalLink className="ml-2 h-4 w-4 text-gray-600" />
          </Button>
        </div>

        <div className="grid grid-cols-4 gap-4">
          <StatCard title="ATTORNEY LIENS" value={attorneyLiens.length} />
          <StatCard
            title="TOTAL ATTORNEY LIENS"
            value={`$${attorneyLiensTotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}`}
          />
          <StatCard title="MISC LIENS" value={miscLiens.length} />
          <StatCard
            title="TOTAL MISC LIENS"
            value={`$${miscLiensTotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}`}
          />
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-center cursor-pointer"
            onClick={() => setIsAttorneyOpen(!isAttorneyOpen)}>
            <div className="flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-[#060216]" />
              <h3 className="text-lg font-semibold text-[#060216]">ATTORNEY LIENS</h3>
            </div>
            <ChevronUp className={`h-4 w-4 text-gray-500 transition-transform ${!isAttorneyOpen ? 'rotate-180' : ''}`} />
          </div>
          {isAttorneyOpen && (
            <div className="flex flex-col gap-4 ml-4">
              {attorneyLiens.map((lien) => (
                <LienCard
                  key={lien.id}
                  lien={lien}
                  type="attorney"
                  onDelete={handleAttorneyLienDelete}
                />
              ))}
            </div>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-center cursor-pointer"
            onClick={() => setIsMiscOpen(!isMiscOpen)}>
            <div className="flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-[#060216]" />
              <h3 className="text-lg font-semibold text-[#060216]">MISC LIENS</h3>
            </div>
            <ChevronUp className={`h-4 w-4 text-gray-500 transition-transform ${!isMiscOpen ? 'rotate-180' : ''}`} />
          </div>
          {isMiscOpen && (
            <div className="flex flex-col gap-4 ml-4">
              {miscLiens.map((lien) => (
                <LienCard
                  key={lien.id}
                  lien={lien}
                  type="misc"
                  onDelete={handleMiscLienDelete}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      <DeleteConfirmationDialog
        open={deleteAttorneyDialogOpen}
        onOpenChange={setDeleteAttorneyDialogOpen}
        onConfirm={handleDeleteAttorneyLien}
        text="Attorney Lien"
      />

      <DeleteConfirmationDialog
        open={deleteMiscDialogOpen}
        onOpenChange={setDeleteMiscDialogOpen}
        onConfirm={handleDeleteMiscLien}
        text="Miscellaneous Lien"
      />
    </>
  );
}
