import React, { FC, Fragment, useState, useMemo } from 'react';
import { useTreatmentProvidersQuery, usePatchTreatmentProviderStatusMutation } from '@/services/case-management/medicalTreatmentService';
import { Separator } from "@/components/ui/separator";
import RichTextViewer from '@/components/ui/RichTextViewer';
import { Card, CardContent } from '@/components/ui/card';
import { AddressLink } from '@/components/gMap/address-link';
import { TreatmentProvider } from '@/type/case-management/medicalTreatmentTypes';
import { InfoFieldGroup } from '@/components/ui/InfoField';
import { Badge } from '@/components/ui/badge';
import { RecordStatus } from '@/type/case-management/medicalTreatmentTypes';
import { formatDateForApi, formatDateForDisplay } from '@/utils/dateUtils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HealthProviderProps {
  caseId: string;
}

interface ProviderItemProps {
  provider: TreatmentProvider;
  isLast: boolean;
  onStatusUpdate: (type: "records_status" | "billing_status", status: RecordStatus) => void;
  caseId: string;
}

// Helper function to determine badge variant based on status
const getStatusVariant = (status: string | undefined): "success" | "warning" | "secondary" | "default" => {
  if (status === "RECEIVED" || status === "NA" || status === "COMPLETE") return "success";
  if (status === "REQUESTED" || status === "TREATING") return "warning";
  return "secondary";
};

const ProviderItem: FC<ProviderItemProps> = ({ provider, isLast, onStatusUpdate, caseId }) => {
  const hasAddress = provider.medical_provider.street1 ||
    provider.medical_provider.street2 ||
    provider.medical_provider.city ||
    provider.medical_provider.state ||
    provider.medical_provider.zip_code;

  // Get insurance information from medical provider
  const insuranceInfo = typeof provider.medical_provider === 'number' 
    ? 'Not Specified'
    : provider.insurance_paid ? `$${provider.insurance_paid}` : 'Not Specified';

  return (
    <Fragment>
      <div className="space-y-6">
        {/* First Row - Provider Name, Insurance and Status */}
        <div className="flex justify-between items-start">
          <div>
            <div className="text-[#060216] text-base font-semibold">
              {provider.medical_provider.company || '—'}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              Insurance Paid: {insuranceInfo}
            </div>
          </div>
          <div className="flex flex-col items-end gap-1">
            <div className="flex flex-row items-center gap-4">
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Billing Status:</span>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Badge 
                        variant={getStatusVariant(provider.billing_status)} 
                        className="text-xs cursor-pointer hover:opacity-80"
                      >
                        {(provider.billing_status || 'NOT_REQUESTED').replace("_", " ")}
                      </Badge>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {Object.values(RecordStatus).map((status) => (
                        <DropdownMenuItem
                          key={status}
                          onClick={() => onStatusUpdate("billing_status", status)}
                        >
                          Billing {status.replace("_", " ")}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {/* Attribution for Billing Status */}
                {provider.billing_status === RecordStatus.RECEIVED && provider.billing_received_by && provider.billing_received_at ? (
                  <span className="text-xs text-gray-500 mt-1 whitespace-nowrap">
                    Received by {provider.billing_received_by.first_name} {provider.billing_received_by.last_name} on {formatDateForDisplay(provider.billing_received_at, "MM/dd/yyyy")}
                  </span>
                ) : (([
                  RecordStatus.REQUESTED,
                  RecordStatus.NA,
                  RecordStatus.COST_REQUESTED,
                  RecordStatus.NOT_REQUESTED
                ] as string[]).includes(provider.billing_status || "") && provider.billing_requested_by && provider.billing_requested_at && (
                  <span className="text-xs text-gray-500 mt-1 whitespace-nowrap">
                    Updated by {provider.billing_requested_by.first_name} {provider.billing_requested_by.last_name} on {formatDateForDisplay(provider.billing_requested_at, "MM/dd/yyyy")}
                  </span>
                ))}
              </div>
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Record Status:</span>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Badge 
                        variant={getStatusVariant(provider.records_status)} 
                        className="text-xs cursor-pointer hover:opacity-80"
                      >
                        {(provider.records_status || 'NOT_REQUESTED').replace("_", " ")}
                      </Badge>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {Object.values(RecordStatus).map((status) => (
                        <DropdownMenuItem
                          key={status}
                          onClick={() => onStatusUpdate("records_status", status)}
                        >
                          Records {status.replace("_", " ")}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {/* Attribution for Records Status */}
                {provider.records_status === RecordStatus.RECEIVED && provider.records_received_by && provider.records_received_at ? (
                  <span className="text-xs text-gray-500 mt-1 whitespace-nowrap">
                    Received by {provider.records_received_by.first_name} {provider.records_received_by.last_name} on {formatDateForDisplay(provider.records_received_at, "MM/dd/yyyy")}
                  </span>
                ) : (([
                  RecordStatus.REQUESTED,
                  RecordStatus.NA,
                  RecordStatus.COST_REQUESTED,
                  RecordStatus.NOT_REQUESTED
                ] as string[]).includes(provider.records_status || "") && provider.records_requested_by && provider.records_requested_at && (
                  <span className="text-xs text-gray-500 mt-1 whitespace-nowrap">
                    Updated by {provider.records_requested_by.first_name} {provider.records_requested_by.last_name} on {formatDateForDisplay(provider.records_requested_at, "MM/dd/yyyy")}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Consolidated Information Fields */}
        <InfoFieldGroup
          caseId={caseId}
          fields={[
            // Contact Information
            {
              label: "Email ID",
              value: provider.medical_provider.email || '—',
              isMail: true
            },
            {
              label: "Phone",
              value: provider.medical_provider.contacts?.[0]?.phone || '—',
              isPhone: true
            },
            {
              label: "Treatment Status",
              value: provider.treatment_status === 'COMPLETE' ? 'Complete' :
                provider.treatment_status === 'TREATING' ? 'In Progress' : '—'
            },
            // Address (conditionally added)
            ...(hasAddress ? [{
              label: "Address",
              value: (
                <AddressLink
                  address={{
                    street1: provider.medical_provider.street1 || '',
                    street2: provider.medical_provider.street2,
                    city: provider.medical_provider.city || '',
                    state: provider.medical_provider.state || '',
                    zip_code: provider.medical_provider.zip_code || ''
                  }}
                />
              ),
              className: "md:col-span-3"
            }] : []),
            // Financial Information
            {
              label: "Original Bill",
              value: provider.original_bill || 0,
              isCurrency: true,
              valueClassName: "text-green-600"
            },
            {
              label: "Still Owed",
              value: provider.still_owed || 0,
              isCurrency: true,
              valueClassName: "text-green-600"
            },
            {
              label: "MedPay/PIP Paid",
              value: provider.medpay_pip_paid || 0,
              isCurrency: true,
              valueClassName: "text-green-600"
            }
          ]}
        />

        {/* Treatment Description */}
        {provider.treatment_description && (
          <div className="p-2 bg-gray-50 rounded-lg border border-gray-100">
            <p className="text-xs text-gray-500 mb-1">Treatment Description</p>
            <div className="text-sm font-semibold">
              <RichTextViewer data={provider.treatment_description} isCopied={true} />
            </div>
          </div>
        )}
      </div>
      {!isLast && <Separator className="my-6" />}
    </Fragment>
  );
};

const HealthProvider: FC<HealthProviderProps> = ({ caseId }) => {
  const { data: treatmentProviders, isLoading } = useTreatmentProvidersQuery(caseId);
  const [selectedProviderId, setSelectedProviderId] = useState<string>("");
  
  // Initialize mutation with both required parameters
  const patchMutation = usePatchTreatmentProviderStatusMutation(caseId, selectedProviderId);

  // Sort treatments by original_bill in descending order
  const sortedProviders = useMemo(() => {
    if (!treatmentProviders) return [];
    return [...treatmentProviders].sort((a, b) => {
      const billA = parseFloat(a.original_bill || "0");
      const billB = parseFloat(b.original_bill || "0");
      return billB - billA;
    });
  }, [treatmentProviders]);

  const handleStatusUpdate = (providerId: string, type: "records_status" | "billing_status", status: RecordStatus) => {
    setSelectedProviderId(providerId);
    const currentDate = formatDateForApi(new Date());
    const currentUser = "Current User"; // TODO: Get actual current user

    const updateData = {
      [type]: status,
      [`${type === "records_status" ? "records" : "billing"}_date`]: currentDate,
    };

    // Add status-specific fields
    if (status === RecordStatus.REQUESTED) {
      updateData[`${type === "records_status" ? "records" : "billing"}_requested_at`] = currentDate;
      updateData[`${type === "records_status" ? "records" : "billing"}_requested_by`] = currentUser;
    } else if (status === RecordStatus.RECEIVED) {
      updateData[`${type === "records_status" ? "records" : "billing"}_received_at`] = currentDate;
      updateData[`${type === "records_status" ? "records" : "billing"}_received_by`] = currentUser;
    }

    patchMutation.mutate(updateData);
  };

  return (
    <div className="space-y-6 w-full">
      <Card className="bg-white rounded-lg shadow-sm w-full">
        <CardContent className="p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : sortedProviders && sortedProviders.length > 0 ? (
            <div className="animate-fadeIn">
              {sortedProviders.map((provider, index) => (
                <ProviderItem
                  key={provider.medical_provider.id}
                  provider={provider}
                  isLast={index === sortedProviders.length - 1}
                  onStatusUpdate={(type, status) => handleStatusUpdate(provider.id.toString(), type, status)}
                  caseId={caseId}
                />
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-center">No health providers found</div>
          )}
          {/* Documents Section */}
          {/* {provider.documents?.length > 0 && (
              <div>
                <p className="text-xs text-gray-500 mb-3">Documents</p>
                <div className="space-y-3">
                  {provider.documents.map((document, docIndex) => (
                    <div key={docIndex} className="flex items-center p-2 bg-gray-50 rounded-md">
                      <DocumentIcon className="h-4 w-4 text-gray-600 mr-2" />
                      <span className="text-xs font-medium">{document.name || 'Untitled Document'}</span>
                    </div>
                  ))}
                </div>
              </div>
            )} */}
        </CardContent>
      </Card>
    </div>
  );
};

export default HealthProvider;
