"use client";

import React from "react";
import { <PERSON><PERSON>con, ExternalLink, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { viewFile } from "@/services/case-management/newDocumentManagement";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface ChecklistItemFilesProps {
  fileIds: string[];
  onRemoveFile?: (fileId: string) => void;
  isReadOnly?: boolean;
}

export function ChecklistItemFiles({ fileIds, onRemoveFile, isReadOnly = false }: ChecklistItemFilesProps) {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  if (!fileIds || fileIds.length === 0) {
    return null;
  }

  const handleViewFile = async (fileId: string) => {
    try {
      await viewFile(fileId, accessToken || "");
    } catch (error) {
      console.error("Error viewing file:", error);
    }
  };

  const handleRemoveFile = (e: React.MouseEvent, fileId: string) => {
    e.stopPropagation();
    if (onRemoveFile) {
      onRemoveFile(fileId);
    }
  };

  return (
    <div className="mt-3 space-y-2">
      <div className="flex items-center">
        <Badge variant="outline" className="text-blue-600 bg-blue-50 border-blue-200 text-xs font-medium">
          Associated Files ({fileIds.length})
        </Badge>
      </div>
      <div className="space-y-1 pl-1">
        {fileIds.map((fileId) => (
          <div 
            key={fileId}
            className={cn(
              "flex items-center text-sm py-1 px-2 rounded-md group",
              !isReadOnly && "hover:bg-gray-100"
            )}
          >
            <FileIcon className="h-3.5 w-3.5 text-gray-500 mr-2 flex-shrink-0" />
            <span className="truncate flex-1 text-gray-700">{fileId}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => handleViewFile(fileId)}
            >
              <ExternalLink className="h-3.5 w-3.5 text-gray-500" />
            </Button>
            {!isReadOnly && onRemoveFile && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => handleRemoveFile(e, fileId)}
              >
                <Trash2 className="h-3.5 w-3.5 text-red-500" />
              </Button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
