import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTasksListQuery, useUpdateTaskMutation } from "@/services/case-management/noteTaskService";
import { Task, TaskStatus, TaskStatusConfig } from "@/type/case-management/noteTaskTypes";
import { Calendar, User2, Pencil, ChevronLeft, ChevronRight, MoreVertical } from "lucide-react";
import { format } from "date-fns";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import RichTextEditor from "@/components/ui/RichTextEditor";
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TaskCardsProps {
  caseId: string;
}

// const StatusIcons = {
//   [TaskStatus.PENDING]: Circle,
//   [TaskStatus.IN_PROGRESS]: Clock,
//   [TaskStatus.COMPLETED]: CheckCircle2,
//   [TaskStatus.REOPENED]: AlertCircle,
// };

// const StatusColors = {
//   [TaskStatus.PENDING]: "text-gray-500",
//   [TaskStatus.IN_PROGRESS]: "text-blue-500",
//   [TaskStatus.COMPLETED]: "text-green-500",
//   [TaskStatus.REOPENED]: "text-amber-500",
// };

const StatusLabels = {
  [TaskStatus.PENDING]: "Pending",
  [TaskStatus.IN_PROGRESS]: "In Progress",
  [TaskStatus.COMPLETED]: "Completed",
  [TaskStatus.REOPENED]: "Reopened",
};

// Array of all possible statuses for the dropdown
const ALL_STATUSES = [TaskStatus.PENDING, TaskStatus.IN_PROGRESS];

// Array of statuses to show in the main view
const VISIBLE_STATUSES = [TaskStatus.PENDING, TaskStatus.IN_PROGRESS];

// Remove the constant TASKS_PER_PAGE and replace with a function
const getTasksPerPage = (isDocumentSidebarVisible: boolean, isTabsPanelCollapsed: boolean): number => {
  return isDocumentSidebarVisible && !isTabsPanelCollapsed ? 3 : 4;
};

export default function TaskCards({ caseId }: TaskCardsProps) {
  const { data: tasksData, isLoading } = useTasksListQuery(caseId, 'Case');
  const updateTaskMutation = useUpdateTaskMutation(caseId, 'Case');
  const [currentPage, setCurrentPage] = useState(0);
  const [editingTask, setEditingTask] = useState<{
    id: number;
    title: string;
    description: string;
    assigned_to_id?: number;
    tagged_user_ids: number[];
    due_date?: string;
  } | null>(null);
  const [isHovering, setIsHovering] = useState(false);

  // Get sidebar states from Redux
  const { isDocumentSidebarVisible, isTabsPanelCollapsed } = useSelector((state: RootState) => state.auth);

  // Get dynamic tasks per page
  const tasksPerPage = getTasksPerPage(isDocumentSidebarVisible, isTabsPanelCollapsed);

  const handleStatusChange = async (taskId: number, newStatus: TaskStatus) => {
    try {
      const task = tasksData?.pages[0].results.find((t) => t.id === taskId);
      if (!task) return;

      await updateTaskMutation.mutateAsync({
        taskId: taskId.toString(),
        data: {
          case: caseId,
          status: newStatus,
          title: task.title,
          task_for: 'Case'
        },
      });
    } catch (error) {
      console.error("Failed to update task status:", error);
    }
  };

  const handleEdit = async () => {
    if (!editingTask) return;

    try {
      await updateTaskMutation.mutateAsync({
        taskId: editingTask.id.toString(),
        data: {
          case: caseId,
          title: editingTask.title,
          description: editingTask.description,
          assigned_to_id: editingTask.assigned_to_id,
          tagged_user_ids: editingTask.tagged_user_ids,
          due_date: editingTask.due_date,
          task_for: 'Case'
        },
      });
      setEditingTask(null);
    } catch (error) {
      console.error("Failed to update task:", error);
    }
  };

  if (isLoading) {
    return <div>Loading tasks...</div>;
  }

  const allTasks = tasksData?.pages[0]?.results || [];
  // Filter tasks to show only pending and in-progress
  const tasks = allTasks.filter((task: Task) => VISIBLE_STATUSES.includes(task.status));

  if (tasks.length === 0) {
    return (
      <div className="w-full p-4 text-center text-gray-500">
        No pending or in-progress tasks found
      </div>
    );
  }

  const totalPages = Math.ceil(tasks.length / tasksPerPage);
  const startIndex = currentPage * tasksPerPage;
  const visibleTasks = tasks.slice(startIndex, startIndex + tasksPerPage);

  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
  };

  return (
    <div className="w-full">
      <div
        className="relative group"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        <div className="flex items-center gap-2">
          {currentPage > 0 && (
            <Button
              variant="outline"
              size="icon"
              className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 transition-opacity duration-200 ${isHovering ? 'opacity-100' : 'opacity-0'
                }`}
              onClick={handlePrevPage}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}

          {currentPage < totalPages - 1 && (
            <Button
              variant="outline"
              size="icon"
              className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 transition-opacity duration-200 ${isHovering ? 'opacity-100' : 'opacity-0'
                }`}
              onClick={handleNextPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>

        <div className={`grid ${isDocumentSidebarVisible && !isTabsPanelCollapsed ? 'grid-cols-3' : 'grid-cols-4'} gap-2 px-6`}>
          {visibleTasks.map((task: Task) => (
            <Card key={task.id} className="bg-gray-50 hover:bg-gray-50/90 transition-all duration-200 backdrop-blur-sm border-none group w-full min-h-[100px]">
              <CardContent className="p-4 relative w-full h-full flex flex-col justify-between">
                {editingTask?.id === task.id ? (  
                  <div className="space-y-4">
                    <Input
                      value={editingTask.title}
                      onChange={(e) =>
                        setEditingTask((prev) =>
                          prev ? { ...prev, title: e.target.value } : null
                        )
                      }
                      className="w-full"
                      placeholder="Task title"
                    />
                    <div className="border rounded-lg overflow-hidden">
                      <RichTextEditor
                        value={editingTask.description}
                        onChange={(value) =>
                          setEditingTask((prev) =>
                            prev ? { ...prev, description: value } : null
                          )
                        }
                        placeholder="Task description"
                        className="min-h-[100px]"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingTask(null)}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleEdit}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex flex-col flex-grow">
                      <div className="flex gap-2 flex-col mt-2">
                        <div className="font-semibold text-sm  line-clamp-2">{task.title}</div>
                        <div className="text-sm text-gray-600">
                          <RichTextViewer data={task.description || ''} />
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col text-xs text-gray-500 mt-4">
                      {task.assigned_to && (
                        <div className="flex items-center gap-1">
                          <User2 className="h-4 w-4" />
                          <span>{task.assigned_to.name || task.assigned_to.email}</span>
                        </div>
                      )}
                      <div className="flex items-center justify-between ">
                        <div className="flex items-center gap-1">
                          {task.due_date && (
                            <>
                              <Calendar className="h-4 w-4" />
                              <span>{format(new Date(task.due_date), "dd MMM yyyy")}</span>
                            </>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Badge
                                variant="secondary"
                                className={`shrink-0 cursor-pointer ${TaskStatusConfig[task.status].color}`}
                              >
                                {StatusLabels[task.status]}
                              </Badge>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              {ALL_STATUSES.map((status) => (
                                <DropdownMenuItem
                                  key={status}
                                  className={`cursor-pointer ${status === task.status ? 'opacity-50' : ''}`}
                                  disabled={status === task.status}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStatusChange(task.id, status);
                                  }}
                                >
                                  {StatusLabels[status]}
                                </DropdownMenuItem>
                              ))}
                            </DropdownMenuContent>
                          </DropdownMenu>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 p-0 hover:bg-gray-100 rounded-full"
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem
                                onClick={() => setEditingTask({
                                  id: task.id,
                                  title: task.title,
                                  description: task.description || "",
                                  assigned_to_id: task.assigned_to?.id,
                                  tagged_user_ids: task.tagged_users.map((u) => u.id),
                                  due_date: task.due_date,
                                })}
                              >
                                <Pencil className="h-4 w-4 text-green-500 hover:text-green-500" />Edit Task
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* {totalPages > 1 && (
          <div className="flex justify-center mt-4 gap-2">
            {Array.from({ length: totalPages }).map((_, index) => (
              <Button
                key={index}
                variant={currentPage === index ? "default" : "outline"}
                size="sm"
                className="w-8 h-8 p-0"
                onClick={() => setCurrentPage(index)}
              >
                {index + 1}
              </Button>
            ))}
          </div>
        )} */}
      </div>
    </div>
  );
}