import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import FinalSettlementMemo, { MemoSection } from "./FinalSettlementMemo";
import { useSettlementMemoQuery } from "@/services/case-management/settlementMemoService";
import { useClientBasicDetailsQuery } from "@/services/case-management/clientDetailService";
import { useIncidentDetailsQuery } from "@/services/incidentService";
import { format, parseISO } from "date-fns";
import { useCurrentOrganization } from "@/services/organizationService";

interface FinalMemoPreviewModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  caseId: string;
}

export const FinalMemoPreviewModal = ({
  isOpen,
  onOpenChange,
  caseId,
}: FinalMemoPreviewModalProps) => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Fetch all required data
  const { data: settlementMemo, isLoading: isSettlementMemoLoading } = useSettlementMemoQuery(caseId);
  const { data: clientDetails, isLoading: isClientLoading } = useClientBasicDetailsQuery(caseId);
  const { data: incidentDetails, isLoading: isIncidentLoading } = useIncidentDetailsQuery(caseId);
  const { data: organization } = useCurrentOrganization();

  // Loading state
  if (isSettlementMemoLoading || isClientLoading || isIncidentLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Prepare memo sections
  const recoverySection: MemoSection = {
    title: "RECOVERY",
    items: [
      // Add third party recoveries
      ...(settlementMemo?.recovery.third_party || []).flatMap(recovery =>
        recovery.negotiations.map(negotiation => ({
          label: `${negotiation.status === 'ACCEPTED' ? 'SETTLEMENT' : negotiation.type === 'INITIAL_DEMAND' ? 'DEMAND' : 'OFFER'} from ${negotiation.defendant_name} (${recovery.company_name})`,
          amount: negotiation.amount,
        }))
      ),
      // Add UIM recoveries
      ...(settlementMemo?.recovery.uim || []).flatMap(recovery =>
        recovery.negotiations.map(negotiation => ({
          label: `${negotiation.status === 'ACCEPTED' ? 'UIM SETTLEMENT' : negotiation.type === 'INITIAL_DEMAND' ? 'UIM DEMAND' : 'UIM OFFER'} from ${recovery.company_name}${negotiation.policy_number ? ` (Policy: ${negotiation.policy_number})` : ''}`,
          amount: negotiation.amount,
        }))
      ),
      // Add MEDPAY recoveries
      ...(settlementMemo?.recovery.medpay?.third_party || []).map(medpay => ({
        label: `MEDPAY DEPOSIT from ${medpay.insurance_name} (${medpay.provider_name})`,
        amount: medpay.amount,
      })),
    ],
    totalLabel: "TOTAL SETTLEMENT",
    totalAmount: settlementMemo?.summary.total_settlement || 0,
  };

  const deductionsSection: MemoSection = {
    title: "DEDUCT AND RETAIN TO PAY OTHERS",
    items: [
      // Add attorney fees
      ...(settlementMemo?.expenses.third_party.attorney_fees || []).map(fee => ({
        label: `${fee.law_firm} ${fee.note ? `(${fee.note})` : ''}`,
        amount: -fee.amount,
      })),
      // Add attorney liens
      ...(settlementMemo?.expenses.third_party.attorney_liens || []).map(lien => ({
        label: `${lien.law_firm} ${lien.attorney ? `(${lien.attorney})` : ''}`,
        amount: -lien.amount,
      })),
      // Add medical expenses
      ...(settlementMemo?.expenses.third_party.medical_expenses || []).map(expense => ({
        label: `${expense.provider_name} ${expense.account_number ? `(${expense.account_number})` : ''}`,
        amount: -expense.amount,
      })),
      // Add health insurance liens
      ...(settlementMemo?.expenses.third_party.health_insurance_liens || []).map(lien => ({
        label: `${lien.insurance_name} ${lien.policy_number ? `(Policy: ${lien.policy_number})` : ''} ${lien.file_number ? `(File: ${lien.file_number})` : ''}`,
        amount: -lien.amount,
      })),
      // Add case costs
      ...(settlementMemo?.expenses.third_party.case_costs || []).map(cost => ({
        label: `${cost.contact_name} ${cost.memo ? `(${cost.memo})` : ''}`,
        amount: -cost.amount,
      })),
      // Add miscellaneous liens
      ...(settlementMemo?.expenses.third_party.miscellaneous_liens || []).map(lien => ({
        label: `${lien.lien_holder} ${lien.description ? `(${lien.description})` : ''}`,
        amount: -lien.amount,
      })),
      // Add adjustments
      ...(settlementMemo?.expenses.third_party.adjustments || []).map(adjustment => ({
        label: `${adjustment.name} ${adjustment.description ? `(${adjustment.description})` : ''}`,
        amount: -adjustment.amount,
      })),
    ],
    totalLabel: "TOTAL DUE OTHERS",
    totalAmount: -(settlementMemo?.summary.total_expenses || 0),
  };

  const handleDownloadPreviewMemo = async () => {
    const element = document.getElementById("final-settlement-memo-preview");
    if (!element) return;

    try {
      setIsGeneratingPDF(true);

      // Add a temporary class for PDF generation
      element.classList.add("pdf-mode");

      const canvas = await html2canvas(element, {
        scale: 2,
        logging: false,
        useCORS: true,
        backgroundColor: "#ffffff",
        windowWidth: element.scrollWidth,
        windowHeight: element.scrollHeight,
      });

      // Remove the temporary class
      element.classList.remove("pdf-mode");

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      const pdf = new jsPDF("p", "mm", "a4");

      // If the content is longer than one page, create multiple pages
      let heightLeft = imgHeight;
      let position = 0;
      const pageData = canvas.toDataURL("image/png");

      pdf.addImage(pageData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(pageData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      const fileName = `final-settlement-memo-${clientDetails?.last_name || "client"}-${new Date().toISOString().split("T")[0]}.pdf`;
      pdf.save(fileName);
    } catch (error) {
      console.error("Error generating preview PDF:", error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Default footer text
  const defaultFooterText = `I understand that ${organization?.name || "Law Firm"} is working on reducing and verifying medical liens with the health insurance carrier, PIP carrier, and/or the individual medical providers. The "Total amount due to client" will remain in trust pending balance verifications.`;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl space-y-0">
        <DialogHeader>
          <DialogTitle>Final Settlement Memo Preview</DialogTitle>
        </DialogHeader>
        <div className="overflow-y-auto max-h-[70vh] scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
          <div id="final-settlement-memo-preview">
            <FinalSettlementMemo
              clientName={`${clientDetails?.first_name || ""} ${clientDetails?.last_name || ""}`}
              date={incidentDetails?.incident_date ? format(parseISO(incidentDetails.incident_date), "MM/dd/yyyy") : "N/A"}
              sections={[recoverySection, deductionsSection]}
              netToClient={settlementMemo?.summary.net_to_client || 0}
              footerNote={defaultFooterText}
            />
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button
            onClick={() => {
              onOpenChange(false);
              handleDownloadPreviewMemo();
            }}
            className="bg-emerald-600 hover:bg-emerald-700 text-white"
            disabled={isGeneratingPDF}
          >
            {isGeneratingPDF ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating PDF...
              </>
            ) : (
              "Download"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FinalMemoPreviewModal;