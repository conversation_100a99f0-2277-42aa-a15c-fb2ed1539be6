import React, { useState } from "react";
import {
  UserIcon,
  ExternalLink,
  Briefcase,
  Phone,
  Mail,
  MapPin,
  Shield,
  LucideIcon,
  FileText,
  Hash,
  Trash,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useClientBasicDetailsQuery,
  useClientContactDetailsQuery,
  useClientInsuranceListQuery,
  useUpdateClientEmploymentStatusMutation,
  useInsuranceAdjusterDetailsQuery,
  useDeleteClientInsuranceMutation,
} from "@/services/case-management/clientDetailService";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/DocumentSidebar/components/LoadingSpinner";
import { cn } from "@/lib/utils";
import { AddressLink } from "@/components/gMap/address-link";
import { AdjusterDetail } from "@/type/case-management/insuranceTypes";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { formatDateForDisplay } from "@/utils/dateUtils";

interface InfoFieldProps {
  icon: LucideIcon | (() => JSX.Element);
  label: string;
  value: string | React.ReactNode;
}

interface InsuranceCardProps {
  insurance: {
    id: number;
    case: string;
    plan_type?: string;
    policy_number?: string;
    claim_number?: string;
    no_insurance?: boolean;
    insurance_company?: {
      name: string;
      phone?: string;
      email?: string;
      fax?: string;
    };
    coverage_status?: string;
    insured?: string;
    medpay_claim_number?: string;
  };
}

interface CardHeaderProps {
  title: string;
  status?: {
    label: string;
    isActive?: boolean;
  };
  caseId: string;
  templateConfig: {
    contextType: TemplateContextType;
    insuranceId?: string;
    adjusterId?: string;
  };
  onDelete?: () => void;
}

const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  status,
  caseId,
  templateConfig,
  onDelete,
}) => (
  <div className="flex items-center justify-between">
    <h5 className="font-medium text-[#060216]">{title}</h5>
    <div className="flex items-center gap-2">
      {status && (
        <span
          className={cn(
            "px-2 py-0.5 rounded-full text-xs font-medium",
            status.isActive
              ? "bg-green-100 text-green-700"
              : "bg-gray-100 text-gray-600"
          )}
        >
          {status.label}
        </span>
      )}
      {onDelete && (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 hover:bg-red-50 text-red-600"
          onClick={onDelete}
        >
          <Trash className="h-4 w-4" />
        </Button>
      )}
      <ManageEmailTemplate
        caseId={caseId}
        clientInsuranceId={templateConfig.insuranceId}
        adjusterId={templateConfig.adjusterId}
        contextType={templateConfig.contextType as TemplateContextType}
        className="h-8 w-8"
      />
    </div>
  </div>
);

const InfoField: React.FC<InfoFieldProps> = ({ icon: Icon, label, value }) => (
  <div className="flex items-start gap-3">
    {typeof Icon === "function" && !("render" in Icon) ? (
      <Icon />
    ) : (
      <Icon className="h-5 w-5 text-gray-500" />
    )}
    <div>
      <span className="text-gray-500 text-sm">{label}</span>
      <p className="text-sm font-medium text-[#060216]">
        {label === "Email" && typeof value === "string" && value !== "—" ? (
          <a href={`mailto:${value}`} className="hover:underline">
            {value}
          </a>
        ) : (
          value
        )}
      </p>
    </div>
  </div>
);

const InsuranceCard: React.FC<InsuranceCardProps & { caseId: string }> = ({
  insurance,
  caseId,
}) => {
  const { data: adjusterDetails } = useInsuranceAdjusterDetailsQuery(
    insurance.case,
    insurance.id.toString(),
    { enabled: !!insurance.case && !!insurance.id }
  );
  const { toast } = useToast();
  const deleteInsurance = useDeleteClientInsuranceMutation();
  const { refetch: refetchInsuranceList } = useClientInsuranceListQuery(caseId);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const getAdjusterDetails = (): AdjusterDetail | null => {
    if (!adjusterDetails) return null;
    return (
      adjusterDetails.bodily_injury ||
      adjusterDetails.medpay_pip ||
      adjusterDetails.property_damage ||
      null
    );
  };

  const adjuster = getAdjusterDetails();

  const insuranceFields = [
    {
      icon: FileText,
      label: "Plan Type",
      value: insurance.plan_type || "—",
    },
    {
      icon: Hash,
      label: "Policy Number",
      value: insurance.policy_number || "—",
    },
    {
      icon: Hash,
      label: "Claim Number",
      value: insurance.claim_number || "—",
    },
    {
      icon: UserIcon,
      label: "Insured",
      value: insurance.insured || "—",
    },
    {
      icon: Phone,
      label: "Company Phone",
      value: insurance.insurance_company?.phone || "—",
    },
    {
      icon: Mail,
      label: "Company Email",
      value: insurance.insurance_company?.email || "—",
    },
    {
      icon: Phone,
      label: "Company Fax",
      value: insurance.insurance_company?.fax || "—",
    },
    {
      icon: Hash,
      label: "Medpay Claim Number",
      value: insurance.medpay_claim_number || "—",
    },
    {
      icon: FileText,
      label: "No Insurance",
      value: insurance.no_insurance ? "Yes" : "No",
    },
  ];

  const handleDelete = async () => {
    try {
      await deleteInsurance.mutateAsync({
        caseId,
        insuranceId: insurance.id.toString(),
      });
      await refetchInsuranceList();
      toast({
        title: "Success",
        description: "Insurance record deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting insurance record:", error);
      toast({
        title: "Error",
        description: "Failed to delete insurance record",
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  return (
    <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 space-y-4">
      <CardHeader
        title={insurance.insurance_company?.name || "—"}
        status={
          insurance.coverage_status
            ? {
                label: insurance.coverage_status,
                isActive: insurance.coverage_status === "Active",
              }
            : undefined
        }
        caseId={caseId}
        templateConfig={{
          contextType: "client_insurance",
          insuranceId: insurance.id.toString(),
        }}
        onDelete={() => setDeleteDialogOpen(true)}
      />
      <div className="grid grid-cols-2 gap-4">
        {insuranceFields.map((field, index) => (
          <InfoField
            key={index}
            icon={field.icon}
            label={field.label}
            value={field.value}
          />
        ))}
      </div>

      {adjuster && (
        <div className="mt-6 border-t border-gray-200 pt-4">
          <CardHeader
            title="Adjuster Details"
            caseId={caseId}
            templateConfig={{
              contextType: "client_adjuster",
              insuranceId: insurance.id.toString(),
              adjusterId: adjuster.id?.toString(),
            }}
          />
          <div className="grid grid-cols-2 gap-4 mt-4">
            <InfoField
              icon={UserIcon}
              label="Name"
              value={
                `${adjuster.first_name || ""} ${
                  adjuster.last_name || ""
                }`.trim() || "—"
              }
            />
            <InfoField
              icon={Phone}
              label="Phone"
              value={adjuster.phone || "—"}
            />
            <InfoField
              icon={Mail}
              label="Email"
              value={adjuster.email || "—"}
            />
          </div>
        </div>
      )}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        text="Client Insurance"
        onConfirm={handleDelete}
      />
    </div>
  );
};

export function ClientDetails({ caseId }: { caseId: string }) {
  const router = useRouter();
  const { toast } = useToast();
  const {
    data: basicDetails,
    refetch: refetchBasicDetails,
    isLoading: isBasicLoading,
  } = useClientBasicDetailsQuery(caseId);
  const { data: contactDetails, isLoading: isContactLoading } =
    useClientContactDetailsQuery(caseId);
  const { data: insuranceList, isLoading: isInsuranceLoading } =
    useClientInsuranceListQuery(caseId);
  const updateEmploymentStatus =
    useUpdateClientEmploymentStatusMutation(caseId);

  const handleEmploymentChange = async (value: string) => {
    try {
      await updateEmploymentStatus.mutateAsync(value === "employed");
      await refetchBasicDetails();
      toast({
        title: "Success",
        description: `Employment status updated to ${
          value === "employed" ? "Employed" : "Unemployed"
        }`,
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to update employment status",
        variant: "destructive",
      });
    }
  };

  if (isBasicLoading || isContactLoading || isInsuranceLoading) {
    return <LoadingSpinner />;
  }

  const formatAddress = (
    details:
      | {
          address1_street1?: string | null;
          address1_street2?: string | null;
          address1_city?: string | null;
          address1_state?: string | null;
          address1_zip?: string | null;
        }
      | ""
  ) => {
    if (!details || Object.keys(details).length === 0) return "—";
    const street1 = details.address1_street1 || "";
    const street2 = details.address1_street2
      ? `, ${details.address1_street2}`
      : "";
    const city = details.address1_city || "";
    const state = details.address1_state || "";
    const zip = details.address1_zip || "";

    const fullAddress = `${street1}${street2} ${city} ${state} ${zip}`.trim();
    return fullAddress || "—";
  };

  return (
    <div className="w-full mt-4 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex flex-row items-center gap-3">
          <UserIcon className="h-5 w-5 text-[#060216]" />
          <h3 className="font-semibold text-base text-[#060216]">
            Client Overview
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <ManageEmailTemplate 
            caseId={caseId}
            clientId={basicDetails?.id?.toString()}
            contextType={"client" as TemplateContextType}
            className="h-8 w-8"
          />
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-gray-50 transition-colors"
            onClick={() => router.push(`/client-details/${caseId}`)}
          >
            <ExternalLink className="h-4 w-4 text-gray-600" />
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-2 gap-x-12 gap-y-6">
        <InfoField
          icon={UserIcon}
          label="Name"
          value={
            basicDetails
              ? `${basicDetails.first_name} ${basicDetails.last_name}`
              : "—"
          }
        />
        <InfoField
          icon={UserIcon}
          label="DOB"
          value={
            basicDetails
              ? formatDateForDisplay(basicDetails?.date_of_birth)
              : "—"
          }
        />
        <InfoField
          icon={Phone}
          label="Phone"
          value={contactDetails?.phone_number_1 || "—"}
        />
        <InfoField
          icon={Mail}
          label="Email"
          value={contactDetails?.primary_email || "—"}
        />
        <InfoField
          icon={() => (
            <AddressLink
              address={{
                street1: contactDetails?.address1_street1 || undefined,
                street2: contactDetails?.address1_street2 || undefined,
                city: contactDetails?.address1_city || undefined,
                state: contactDetails?.address1_state || undefined,
                zip_code: contactDetails?.address1_zip || undefined,
              }}
            >
              <MapPin className="h-5 w-5 text-gray-500 hover:text-[#1C7B35]" />
            </AddressLink>
          )}
          label="Address"
          value={formatAddress(contactDetails || "")}
        />
      </div>

      {/* Employment Status Section */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Briefcase className="h-5 w-5 text-[#060216]" />
            <h4 className="font-semibold text-base text-[#060216]">
              Employment Status
            </h4>
          </div>
          <Select
            value={basicDetails?.employed ? "employed" : "unemployed"}
            onValueChange={handleEmploymentChange}
            disabled={updateEmploymentStatus.isPending}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="employed">
                <span className="font-medium">Employed</span>
              </SelectItem>
              <SelectItem value="unemployed">
                <span className="font-medium">Unemployed</span>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Insurance Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Shield className="h-5 w-5 text-[#060216]" />
          <h4 className="font-semibold text-base text-[#060216]">
            Insurance Details
          </h4>
        </div>
        <div className="space-y-4">
          {insuranceList?.map((insurance, index) => (
            <InsuranceCard key={index} insurance={insurance} caseId={caseId} />
          ))}
          {!insuranceList?.length && (
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
              <div className="flex items-center gap-3">
                <Shield className="h-5 w-5 text-gray-400" />
                <p className="text-sm text-gray-500">
                  No insurance information available
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
