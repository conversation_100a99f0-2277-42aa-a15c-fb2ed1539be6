import React, { FC } from 'react';
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useCaseEventsQuery } from "@/services/letigation/litigationService";
import { format, parseISO } from "date-fns";
import { Stethoscope } from 'lucide-react';
import RichTextViewer from '@/components/ui/RichTextViewer';

interface LitigationEventsProps {
  caseId: string;
}

const LitigationEvents: FC<LitigationEventsProps> = ({ caseId }) => {
  const { data: eventsData, isLoading: isEventsLoading } = useCaseEventsQuery(caseId);

  const formatTime = (timeStr: string) => {
    if (!timeStr) return "";

    try {
      const [hours, minutes] = timeStr.split(":");
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes));

      return format(date, "h:mm a");
    } catch (e) {
      console.log(e);
      return timeStr;
    }
  };

  const formatEventDate = (dateStr: string | null) => {
    if (!dateStr) return "";
    try {
      const date = parseISO(dateStr);
      return format(date, "MM/dd/yyyy");
    } catch (e) {
      console.log(e);
      return dateStr;
    }
  };

  const events =
    eventsData?.map((event) => ({
      id: event.id,
      status: event.status || "active",
      event: event.event_type,
      description: event.description || "",
      date: formatEventDate(event.date),
      time:
        event.start_time && event.end_time
          ? `${formatTime(event.start_time)} - ${formatTime(event.end_time)}`
          : "",
    })) || [];

  if (events.length === 0 && !isEventsLoading) {
    return null;
  }

  return (
    <div className="space-y-4 w-full">
        {/* <h2 className="text-[#060216] text-base leading-5 tracking-[0.15px] font-Manrope mb-4 w-full"
          style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
        >
          <span className='text-base font-semibold leading-5 tracking-[0.15px]'>Litigation Events</span>
        </h2> */}
      <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-lg shadow-sm mb-4">
            <Stethoscope className="w-5 h-5 text-[#060216]" />
            <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
            Litigation Events
            </h2>
          </div>
      <Card className="w-full mb-6">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableCell>Litigation Event Status</TableCell>
              <TableCell>Event</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Date</TableCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isEventsLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center">
                  Loading events...
                </TableCell>
              </TableRow>
            ) : events.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center">
                  No events found
                </TableCell>
              </TableRow>
            ) : (
              events.map((event) => (
                <TableRow key={event.id} className="group">
                  <TableCell>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                    </div>
                  </TableCell>
                  <TableCell>{event.event}</TableCell>
                  <TableCell>{event.description ? <RichTextViewer data={event.description} /> : ""}</TableCell>
                  <TableCell>
                    <div>{event.date}</div>
                    <div className="text-sm text-gray-500">{event.time}</div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
};

export default LitigationEvents; 