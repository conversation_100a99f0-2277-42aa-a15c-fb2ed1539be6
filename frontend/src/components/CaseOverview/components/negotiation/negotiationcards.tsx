import React from 'react';
import { KPICard, KPICardProps } from '@/components/ui/kpi-card';

export interface NegotiationCardsProps {
  /**
   * Array of card data to display
   */
  data: KPICardProps[];
  
  /**
   * CSS class name for the container
   */
  className?: string;
  
  /**
   * Number of columns in grid on mobile (default: 2)
   */
  mobileColumns?: 1 | 2 | 3 | 4;
  
  /**
   * Number of columns in grid on medium screens (default: 4)
   */
  mediumColumns?: 2 | 3 | 4 | 5 | 6;
  
  /**
   * Gap between cards (default: 3)
   */
  gap?: 1 | 2 | 3 | 4 | 5;
}

/**
 * Renders a grid of KPI cards for negotiation statistics
 * 
 * @param props - Component properties
 * @returns JSX Element
 */
const NegotiationCards: React.FC<NegotiationCardsProps> = ({
  data,
  className = '',
  mobileColumns = 2,
  mediumColumns = 4,
  gap = 3
}) => {
  if (!data || data.length === 0) {
    return null;
  }

  // Map the values to actual Tailwind classes
  const gridClasses = `grid ${getGridColumnsClass(mobileColumns, mediumColumns)} ${getGapClass(gap)} ${className}`;

  return (
    <div className={gridClasses}>
      {data.map((cardProps, index) => (
        <KPICard
          key={`kpi-card-${index}`}
          {...cardProps}
        />
      ))}
    </div>
  );
};

/**
 * Helper function to get grid columns class
 */
function getGridColumnsClass(mobileColumns: 1 | 2 | 3 | 4, mediumColumns: 2 | 3 | 4 | 5 | 6): string {
  return `grid-cols-${mobileColumns} md:grid-cols-${mediumColumns}`;
}

/**
 * Helper function to get gap class
 */
function getGapClass(gap: 1 | 2 | 3 | 4 | 5): string {
  return `gap-${gap}`;
}

export default NegotiationCards;
