import React, { useState } from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Trash2, MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { NegotiationRead, NegotiationType, NegotiationUIMRead } from '@/type/negotiationTypes';
import { AddCounterOfferDialog } from '@/components/CaseOverview/AddCounterOfferDialog';
import { CurrencyDisplay } from '@/components/ui/currency-display';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { DefendantInsuranceListItem } from '@/type/case-management/defendantTypes';
import { ClientTrustEntryType, DepositType, ClientTrust } from "@/type/case-management/clientTrustTypes";
import { useCreateClientTrustMutation } from "@/services/case-management/clientTrustService";
import { useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { formatDateForApi } from '@/utils/dateUtils';

/**
 * Negotiation type specifying the context of the negotiation table
 */
export type NegotiationTableType = 'third_party' | 'uim' | 'all_defendants' | 'all_uim';

/**
 * Common type for negotiation data that works with both NegotiationRead and NegotiationUIMRead
 */
export type NegotiationData = NegotiationRead | NegotiationUIMRead;

/**
 * Props for individual table rows in the negotiation table
 */
export interface NegotiationTableRowProps<T extends NegotiationData> {
  negotiation: T;
  caseId: string;
  selectedDefendant: number | null;
  onAcceptOffer: (id: number) => void;
  onDeacceptOffer: (id: number) => void;
  hasAcceptedOffer: boolean;
  onDelete: (id: number) => void;
  parentIsArchived: boolean;
  insuranceList?: DefendantInsuranceListItem[];
  negotiationType?: NegotiationTableType;
  parentInsuranceCompany?: { id: number | string; name: string; };
  acceptedOfferId?: number | null;
  parentId: number;
  checkSettlementStatus?: (negotiation: T) => ClientTrust | null;
  handleVoidSettlement?: (negotiation: T) => void;
}

// Add form schema for settlement check dialog
const settlementCheckSchema = z.object({
  check_number: z.string().optional(),
});

type SettlementCheckFormData = z.infer<typeof settlementCheckSchema>;

// Add SettlementCheckDialog component
export const SettlementCheckDialog = ({
  isOpen,
  onClose,
  onSubmit,
  negotiation,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (checkNumber: string) => void;
  negotiation: NegotiationData | null;
}) => {
  const form = useForm<SettlementCheckFormData>({
    resolver: zodResolver(settlementCheckSchema),
    defaultValues: {
      check_number: "",
    },
  });

  const handleSubmit = (values: SettlementCheckFormData) => {
    onSubmit(values.check_number || "");
    form.reset();
  };

  if (!negotiation) return null;

  // Get insurance company name based on negotiation type
  const insuranceCompanyName = negotiation.insurance_company?.name || negotiation.client_insurance?.insurance_company?.name;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Settlement Check</DialogTitle>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="check_number">Check Number</Label>
            <Input
              id="check_number"
              {...form.register("check_number")}
              placeholder="Enter check number"
            />
          </div>
          <div className="space-y-2">
            <Label>Amount</Label>
            <div className="text-sm text-gray-500">
              ${Number(negotiation.amount).toLocaleString()}
            </div>
          </div>
          <div className="space-y-2">
            <Label>Insurance Company</Label>
            <div className="text-sm text-gray-500">
              {insuranceCompanyName}
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Create Settlement Check</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

/**
 * NegotiationTableRow component renders a single row in the negotiation table
 * with appropriate formatting and actions
 */
export const NegotiationTableRow = <T extends NegotiationData>({
  negotiation,
  caseId,
  selectedDefendant,
  onAcceptOffer,
  onDeacceptOffer,
  hasAcceptedOffer,
  onDelete,
  parentIsArchived,
  insuranceList,
  negotiationType,
  parentInsuranceCompany,
  acceptedOfferId,
  parentId,
  checkSettlementStatus,
  handleVoidSettlement
}: NegotiationTableRowProps<T>) => {
  const queryClient = useQueryClient();
  const createClientTrust = useCreateClientTrustMutation(queryClient);
  const [showSettlementDialog, setShowSettlementDialog] = useState(false);
  const [selectedNegotiation, setSelectedNegotiation] = useState<NegotiationData | null>(null);

  const isAcceptedOffer = negotiation.type === NegotiationType.ACCEPTED_OFFER;
  const isUIM = negotiationType === 'uim';

  // Modified rowDisabled logic based on negotiation type and accepted status
  let rowDisabled = false;

  if (acceptedOfferId) {
    // If there's a specific accepted offer ID provided
    rowDisabled = !isAcceptedOffer && (hasAcceptedOffer || parentIsArchived || negotiation.is_archived ||
      (hasAcceptedOffer && negotiation.id !== acceptedOfferId));
  } else {
    // Default behavior
    rowDisabled = !isAcceptedOffer && (hasAcceptedOffer || parentIsArchived || negotiation.is_archived);
  }

  // Get insurance company data from multiple possible sources in priority order
  const insuranceCompany = negotiation.insurance_company || // 1. Negotiation's own insurance company
    parentInsuranceCompany || // 2. Parent negotiation's insurance company
    (negotiation.adjuster?.insurance_company_name ? { // 3. Adjuster's insurance company
      id: negotiation.adjuster.insurance_company,
      name: negotiation.adjuster.insurance_company_name
    } : undefined) ||
    (insuranceList?.[0]?.insurance_company); // 4. Defendant's insurance company

  // Get client insurance ID for UIM negotiations
  const clientInsuranceId = isUIM && 'client_insurance' in negotiation
    ? negotiation.client_insurance?.id
    : Number(parentInsuranceCompany?.id);

  const handleSettlementCheck = async (negotiation: NegotiationData) => {
    if (!negotiation.amount || !negotiation.insurance_company) {
      toast({
        title: 'Error',
        description: 'Negotiation amount or insurance company name is missing',
        variant: 'destructive',
      });
      return;
    }

    setSelectedNegotiation(negotiation);
    setShowSettlementDialog(true);
  };

  const handleSettlementSubmit = async (checkNumber: string) => {
    if (!selectedNegotiation) return;

    try {
      const isUIM = 'client_insurance' in selectedNegotiation;
      const insuranceCompanyName = selectedNegotiation.insurance_company?.name || selectedNegotiation.client_insurance?.insurance_company?.name;

      await createClientTrust.mutateAsync({
        caseId,
        trustData: {
          check_number: checkNumber,
          amount: selectedNegotiation.amount.toString(),
          memo: `${isUIM ? 'UIM ' : ''}Settlement payment from ${insuranceCompanyName}`,
          issuer_payee: insuranceCompanyName || `${isUIM ? 'UIM ' : 'THIRD_PARTY'}Insurance Company`,
          deposit_date: formatDateForApi(new Date()),
          client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
          deposit_type: isUIM ? DepositType.UIM : DepositType.THIRD_PARTY,
          negotiation_id: selectedNegotiation.id
        }
      });
      toast({
        title: 'Success',
        description: 'Settlement check created successfully',
      });
      setShowSettlementDialog(false);
      setSelectedNegotiation(null);
    } catch (error) {
      console.error('Error creating settlement check:', error);
      toast({
        title: 'Error',
        description: 'Failed to create settlement check',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <TableRow
        key={negotiation.id}
        className={`${isAcceptedOffer ? 'bg-blue-50' : ''} ${rowDisabled ? 'opacity-40' : ''}`}
      >
        <TableCell className="text-sm">
          {negotiation.date_sent ? formatDateForDisplay(negotiation.date_sent) : formatDateForDisplay(negotiation.created_at)}
        </TableCell>
        <TableCell className="text-sm">
          <div className="flex items-center gap-2">
            {negotiation.notes || ""}
            {/* {isAcceptedOffer && (
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium">
                Accepted
              </span>
            )} */}
          </div>
        </TableCell>
        <TableCell className="text-sm">
          <CurrencyDisplay amount={negotiation.amount} />
        </TableCell>
        <TableCell className="text-sm">
          {formatDateForDisplay(negotiation.response_deadline)}
        </TableCell>
        {hasAcceptedOffer && ('accepted_date' in negotiation) && (
          <TableCell className="flex flex-col items-center text-sm">
            {isAcceptedOffer && <span className="text-blue-500">Accepted</span>}
            {isAcceptedOffer && negotiation.accepted_date ? formatDateForDisplay(negotiation.accepted_date) : ""}
          </TableCell>
        )}
        <TableCell className="text-sm text-right">
          <div className={`flex items-center justify-end space-x-2 ${rowDisabled ? 'pointer-events-none' : ''}`}>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span>
                    <AddCounterOfferDialog
                      isEdit={true}
                      caseId={caseId}
                      defendantId={!isUIM ? selectedDefendant : null}
                      previousOfferId={negotiation.previous_offer ?? parentId}
                      initialData={negotiation}
                      disabled={rowDisabled || isAcceptedOffer}
                      insuranceCompany={insuranceCompany}
                      isUIM={isUIM}
                      clientInsuranceId={clientInsuranceId}
                    />
                  </span>
                </TooltipTrigger>
                {rowDisabled && (
                  <TooltipContent>
                    <p>
                      {parentIsArchived
                        ? "Parent demand is archived"
                        : negotiation.is_archived
                          ? "Offer is archived"
                          : "Only accepted offer can be edited"}
                    </p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(negotiation.id);
              }}
              disabled={rowDisabled || isAcceptedOffer}
            >
              <Trash2 className={`h-4 w-4 ${rowDisabled ? 'text-gray-300' : 'text-red-500'}`} />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger disabled={rowDisabled}>
                <MoreVertical className={`h-4 w-4 ${rowDisabled ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 cursor-pointer'}`} />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {checkSettlementStatus && handleVoidSettlement && checkSettlementStatus(negotiation) && !checkSettlementStatus(negotiation)?.void ? (
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleVoidSettlement(negotiation);
                    }}
                    className="text-sm text-red-600"
                  >
                    Void
                  </DropdownMenuItem>
                ) : (
                  <>
                    {!hasAcceptedOffer && !negotiation.is_archived && !parentIsArchived && (
                      <DropdownMenuItem
                        onClick={() => onAcceptOffer(negotiation.id)}
                        disabled={!isUIM && !selectedDefendant}
                        className="text-sm"
                      >
                        Accept Offer
                      </DropdownMenuItem>
                    )}
                    {isAcceptedOffer && !negotiation.is_archived && !parentIsArchived && (
                      <>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onDeacceptOffer(negotiation.id);
                          }}
                          className="text-sm"
                        >
                          Unaccept Offer
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleSettlementCheck(negotiation);
                          }}
                          className="text-sm"
                        >
                          Settlement Check
                        </DropdownMenuItem>
                      </>
                    )}
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <SettlementCheckDialog
        isOpen={showSettlementDialog}
        onClose={() => {
          setShowSettlementDialog(false);
          setSelectedNegotiation(null);
        }}
        onSubmit={handleSettlementSubmit}
        negotiation={selectedNegotiation}
      />
    </>
  );
};

/**
 * Props for the NegotiationTable component
 */
export interface NegotiationTableProps<T extends NegotiationData> {
  negotiations: T[];
  parentId: number;
  caseId: string;
  selectedDefendant: number | null;
  onAcceptOffer: (id: number) => void;
  onDeacceptOffer: (id: number) => void;
  hasAcceptedOffer: boolean;
  onDelete: (id: number) => void;
  parentIsArchived: boolean;
  insuranceList?: DefendantInsuranceListItem[];
  negotiationType?: NegotiationTableType;
  parentInsuranceCompany?: { id: number | string; name: string; };
  acceptedOfferId?: number | null;
  checkSettlementStatus?: (negotiation: T) => ClientTrust | null;
  handleVoidSettlement?: (negotiation: T) => void;
}

/**
 * NegotiationTable component displays a table of negotiation offers
 * with support for different negotiation types
 */
export const NegotiationTable = <T extends NegotiationData>({
  negotiations,
  parentId,
  caseId,
  selectedDefendant,
  onAcceptOffer,
  onDeacceptOffer,
  hasAcceptedOffer,
  onDelete,
  parentIsArchived,
  insuranceList,
  negotiationType = 'third_party',
  parentInsuranceCompany,
  acceptedOfferId,
  checkSettlementStatus,
  handleVoidSettlement
}: NegotiationTableProps<T>) => {
  const childNegotiations = negotiations.filter(item => item.previous_offer === parentId) as T[];
  const showAcceptedColumn = hasAcceptedOffer && negotiations.some(n => 'accepted_date' in n);

  if (childNegotiations.length === 0) {
    return (
      <div className="text-center py-6 bg-slate-50">
        <p className="text-gray-500 text-sm">No counter offers exist.</p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Offer Date</TableHead>
          <TableHead>Note</TableHead>
          <TableHead>Offer Amount</TableHead>
          <TableHead>Deadline</TableHead>
          {showAcceptedColumn && <TableHead></TableHead>}
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {childNegotiations.map((negotiation) => (
          <NegotiationTableRow<T>
            key={negotiation.id}
            negotiation={negotiation}
            caseId={caseId}
            selectedDefendant={selectedDefendant}
            onAcceptOffer={onAcceptOffer}
            onDeacceptOffer={onDeacceptOffer}
            hasAcceptedOffer={hasAcceptedOffer}
            onDelete={onDelete}
            parentIsArchived={parentIsArchived}
            insuranceList={insuranceList}
            negotiationType={negotiationType}
            parentInsuranceCompany={parentInsuranceCompany}
            acceptedOfferId={acceptedOfferId}
            parentId={parentId}
            checkSettlementStatus={checkSettlementStatus}
            handleVoidSettlement={handleVoidSettlement}
          />
        ))}
      </TableBody>
    </Table>
  );
};

export default NegotiationTable;
