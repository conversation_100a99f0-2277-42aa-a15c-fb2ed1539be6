import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";

/**
 * Props for the FeePercentageSelect component
 */
interface FeePercentageSelectProps {
  /**
   * Current value of the fee percentage
   */
  value: string;
  
  /**
   * Callback function when value changes
   */
  onChange: (value: string) => void;
  
  /**
   * Whether the component is disabled
   */
  disabled?: boolean;
}

/**
 * A reusable component for selecting attorney fee percentages
 * Allows selecting from predefined values or entering a custom percentage
 * 
 * @param props Component props
 * @returns JSX.Element
 */
const FeePercentageSelect: React.FC<FeePercentageSelectProps> = ({
  value,
  onChange,
  disabled = false,
}): JSX.Element => {
  const PREDEFINED_VALUES = ["33.330000", "40.000000", "42.000000"];
  const [isCustom, setIsCustom] = useState(() => !PREDEFINED_VALUES.includes(value));
  const [localValue, setLocalValue] = useState(() => {
    if (!PREDEFINED_VALUES.includes(value)) {
      try {
        return parseFloat(value).toFixed(2);
      } catch {
        return "";
      }
    }
    return "";
  });

  const handleCustomValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (/^\d*\.?\d*$/.test(newValue)) {
      // Format to max 2 decimal places
      const formattedValue = newValue.includes(".")
        ? newValue
          .split(".")
          .map((part, index) => (index === 1 ? part.slice(0, 2) : part))
          .join(".")
        : newValue;

      setLocalValue(formattedValue);

      // Only update if we have a valid number
      if (formattedValue && !isNaN(parseFloat(formattedValue))) {
        onChange(parseFloat(formattedValue).toFixed(6));
      }
    }
  };

  const handleCancel = () => {
    setIsCustom(false);
    setLocalValue("");
    onChange("33.330000");
  };

  if (isCustom) {
    return (
      <div className="flex flex-wrap items-center gap-2">
        <div className="relative min-w-[120px] max-w-[150px] flex-1">
          <Input
            type="text"
            value={localValue}
            onChange={handleCustomValueChange}
            className="pr-8 w-full"
            placeholder="Enter"
            disabled={disabled}
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
            %
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="shrink-0"
          disabled={disabled}
        >
          Cancel
        </Button>
      </div>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={(val) => {
        if (val === "custom") {
          setIsCustom(true);
          setLocalValue("");
        } else {
          setIsCustom(false);
          setLocalValue("");
          onChange(val);
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger className="min-w-[120px] max-w-[150px]">
        <SelectValue placeholder="Fee %" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="33.330000">33.33 %</SelectItem>
        <SelectItem value="40.000000">40 %</SelectItem>
        <SelectItem value="42.000000">42 %</SelectItem>
        <SelectItem value="custom">Custom %</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default FeePercentageSelect; 