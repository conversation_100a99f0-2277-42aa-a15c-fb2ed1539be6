import React from 'react';
import { Phone, Mail } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/LoadingSpinner';

/**
 * Adjuster interface with common properties needed for display
 */
export interface AdjusterInfo {
  id?: number | string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  email?: string | null;
  insurance_company?: string;
  insurance_company_name?: string;
}

/**
 * Insurance company interface with common properties
 */
export interface InsuranceCompanyInfo {
  id?: number | string;
  name: string;
}

/**
 * Props for the KeyInformation component
 */
interface KeyInformationProps {
  /**
   * Type of negotiation - 'third_party' or 'uim'
   */
  negotiationType: 'third_party' | 'uim';
  
  /**
   * Array of adjusters associated with the negotiation
   */
  adjusters?: AdjusterInfo[];
  
  /**
   * Selected adjuster for UIM negotiations
   */
  selectedAdjuster?: AdjusterInfo | null;
  
  /**
   * Insurance company for UIM negotiations
   */
  insuranceCompany?: InsuranceCompanyInfo | null;
  
  /**
   * Whether data is currently loading
   */
  isLoading?: boolean;
  
  /**
   * Policy limit for UIM
   */
  policyLimit?: number | string | null;
}

/**
 * KeyInformation component displays relevant information based on negotiation type
 * 
 * @param props Component props
 * @returns JSX.Element
 */
const KeyInformation: React.FC<KeyInformationProps> = ({
  negotiationType,
  adjusters,
  selectedAdjuster,
  insuranceCompany,
  isLoading = false,
  policyLimit
}): JSX.Element => {
  // Conditionally render different content based on negotiation type
  const renderContent = () => {
    if (negotiationType === 'third_party') {
      return (
        <div>
          <p className="text-xs font-medium text-gray-600 mb-2">Insurance Adjusters</p>
          {isLoading ? (
            <LoadingSpinner />
          ) : !adjusters || adjusters.length === 0 ? (
            <p className="text-gray-500 text-sm">No adjusters assigned</p>
          ) : (
            <div className="space-y-3">
              {adjusters.map((adjuster, index) => (
                <div key={adjuster.id || index} className={`${index > 0 ? 'border-t pt-3' : ''}`}>
                  <p className="text-sm font-semibold">
                    {`${adjuster.first_name} ${adjuster.last_name}`}
                  </p>
                  <p className="text-xs text-gray-600">
                    {adjuster.insurance_company_name || adjuster.insurance_company}
                  </p>
                  <div className="flex space-x-2 mt-2">
                    {adjuster.phone && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 text-xs hover:bg-slate-50"
                        onClick={() => window.location.href = `tel:${adjuster.phone}`}
                      >
                        <Phone className="w-3 h-3 mr-1 text-gray-600" />
                        Call
                      </Button>
                    )}
                    {adjuster.email && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 text-xs hover:bg-slate-50"
                        onClick={() => window.location.href = `mailto:${adjuster.email}`}
                      >
                        <Mail className="w-3 h-3 mr-1 text-gray-600" />
                        Email
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      );
    } else if (negotiationType === 'uim') {
      return (
        <div className="space-y-4 h-full">
          <div>
            <p className="text-xs font-medium text-gray-600 mb-0.5">Insurance Company</p>
            <p className="text-sm font-semibold">
              {insuranceCompany?.name || "Not selected"}
            </p>
          </div>
          <div>
            <p className="text-xs font-medium text-gray-600 mb-0.5">Policy Type</p>
            <Badge className="px-2 py-0.5 text-xs rounded-full">UIM Coverage</Badge>
          </div>
          {policyLimit && (
            <div>
              <p className="text-xs font-medium text-gray-600 mb-0.5">Policy Limit</p>
              <p className="text-sm font-semibold">${Number(policyLimit).toLocaleString()}</p>
            </div>
          )}
          <div>
            <p className="text-xs font-medium text-gray-600 mb-2">Insurance Adjuster</p>
            {selectedAdjuster ? (
              <div className="space-y-1">
                <p className="text-sm font-semibold">
                  {`${selectedAdjuster.first_name} ${selectedAdjuster.last_name}`}
                </p>
                <p className="text-xs text-gray-600">
                  {selectedAdjuster.insurance_company || selectedAdjuster.insurance_company_name}
                </p>
                <div className="flex space-x-2 mt-2">
                  {selectedAdjuster.phone && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs hover:bg-slate-50"
                      onClick={() => window.location.href = `tel:${selectedAdjuster.phone}`}
                    >
                      <Phone className="w-3 h-3 mr-1 text-gray-600" />
                      Call
                    </Button>
                  )}
                  {selectedAdjuster.email && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs hover:bg-slate-50"
                      onClick={() => window.location.href = `mailto:${selectedAdjuster.email}`}
                    >
                      <Mail className="w-3 h-3 mr-1 text-gray-600" />
                      Email
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No adjuster assigned</p>
            )}
          </div>
        </div>
      );
    }
    
    return <p className="text-gray-500 text-sm">No information available</p>;
  };

  return (
    <Card className="border-none shadow-sm h-auto flex flex-col">
      <CardHeader className="border-b bg-slate-50 rounded-t-lg py-3 px-4">
        <CardTitle className="text-base font-semibold">Key Information</CardTitle>
      </CardHeader>
      <CardContent className="p-3 flex-grow">
        <div className="space-y-4">
          {renderContent()}
        </div>
      </CardContent>
    </Card>
  );
};

export default KeyInformation; 