import React from 'react';
import { 
  ChevronDown, 
  ChevronUp, 
  Trash2, 
  MoreVertical 
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from '@/components/ui/button';
import {
  Too<PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AddCounterOfferDialog } from '@/components/CaseOverview/AddCounterOfferDialog';
import { NegotiationType } from '@/type/negotiationTypes';
import { NegotiationRead, NegotiationUIMRead } from '@/type/negotiationTypes';
import { InsuranceCompany } from '@/type/case-management/clientDetailTypes';

/**
 * Generic interface for negotiation data that can be either NegotiationRead or NegotiationUIMRead
 */
interface BaseNegotiation {
  id: number;
  type: string | NegotiationType;
  amount: number | string | null;
  notes: string | null;
  date_sent: string | null;
  created_at: string;
  response_deadline: string | null;
  is_archived: boolean;
}

/**
 * Props for the ParentOfferDemand component
 */
interface ParentOfferDemandProps<T extends BaseNegotiation> {
  parent: T;
  isExpanded: boolean;
  toggleExpand: () => void;
  headerString: string;
  caseId: string;
  selectedDefendant: number | null;
  hasAcceptedOffer: boolean;
  hasAcceptedChild?: boolean;
  isAcceptedOffer: boolean;
  onDelete: (id: number) => void;
  onAcceptOffer: (id: number) => void;
  onDeacceptOffer: (id: number) => void;
  onSettlementCheck: (negotiation: T) => void;
  onEditDemand?: (negotiation: T) => void;
  onArchiveToggle: (id: number, isArchived: boolean) => void;
  checkSettlementStatus?: (negotiation: T) => unknown;
  handleVoidSettlement?: (negotiation: T) => void;
  insuranceCompany?: { id: number | string; name: string; };
  className?: string;
}

/**
 * A reusable component for rendering parent offer/demand items in negotiation views
 * Can be used across regular negotiations, UIM negotiations, and all defendants view
 */
export function ParentOfferDemand<T extends BaseNegotiation>({
  parent,
  isExpanded,
  toggleExpand,
  headerString,
  caseId,
  selectedDefendant,
  hasAcceptedOffer,
  hasAcceptedChild = false,
  isAcceptedOffer,
  onDelete,
  onAcceptOffer,
  onDeacceptOffer,
  onSettlementCheck,
  onEditDemand,
  onArchiveToggle,
  checkSettlementStatus,
  handleVoidSettlement,
  insuranceCompany,
  className = ''
}: ParentOfferDemandProps<T>): JSX.Element {
  return (
    <div className={`flex justify-between gap-2 hover:bg-slate-50 transition-colors ${parent.is_archived ? 'opacity-60' : ''} ${isAcceptedOffer ? 'bg-blue-50' : ''} ${className}`} onClick={toggleExpand}>
      <div
        className={`flex items-center justify-between p-3 cursor-pointer `}
        // ${parent.is_archived ? 'opacity-60' : ''} 
        //   ${isAcceptedOffer ? 'bg-blue-50' : ''}
        //   ${className}`}
      >
        <div className="flex-1">
          <div className="flex flex-col">
            <div className="flex items-center">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500 mr-2" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500 mr-2" />
              )}
              <span className={`font-semibold text-sm ${parent.is_archived ? 'line-through' : ''}`}>
                {headerString}
              </span>
              {parent.is_archived && (
                <span className="ml-2 text-xs text-gray-500">(Archived)</span>
              )}
              {isAcceptedOffer && (
                <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium">
                  Accepted
                </span>
              )}
            </div>
            <div className="ml-6 mt-1 text-sm flex items-start justify-between">
              <div className="flex-1">
                <span className="font-semibold">Note:</span> {parent.notes || ""}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={`ml-6 mt-1 text-sm flex items-center justify-between space-x-2 p-3 pt-0 ${(!isAcceptedOffer && !hasAcceptedChild && hasAcceptedOffer && !parent.is_archived) ? 'pointer-events-none opacity-40' : ''}`}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <AddCounterOfferDialog
                  caseId={caseId}
                  defendantId={selectedDefendant}
                  previousOfferId={parent.id}
                  disabled={hasAcceptedOffer || parent.is_archived}
                  initialData={parent as unknown as NegotiationRead | NegotiationUIMRead}
                  isEdit={false}
                  insuranceCompany={insuranceCompany as unknown as InsuranceCompany}
                  isUIM={selectedDefendant === null}
                />
              </span>
            </TooltipTrigger>
            {(hasAcceptedOffer || parent.is_archived) && (
              <TooltipContent>
                <p>{parent.is_archived ? "Demand is archived" : "Only accepted offer can be modified"}</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 p-0"
          disabled={hasAcceptedOffer || parent.is_archived}
          onClick={(e) => {
            e.stopPropagation();
            onDelete(parent.id);
          }}
        >
          <Trash2 className={`h-4 w-4 ${hasAcceptedOffer || parent.is_archived ? 'text-gray-300' : 'text-red-500'}`} />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger disabled={hasAcceptedOffer && !isAcceptedOffer}>
            <MoreVertical className={`h-4 w-4 ${hasAcceptedOffer && !isAcceptedOffer ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 cursor-pointer'}`} />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {isAcceptedOffer && !parent.is_archived ? (
              <>
                {checkSettlementStatus && handleVoidSettlement && (() => {
                  const settlementTrust = checkSettlementStatus(parent);
                  if (settlementTrust) {
                    return (
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleVoidSettlement(parent);
                        }}
                        className="text-sm text-red-500"
                      >
                        Void
                      </DropdownMenuItem>
                    );
                  } else {
                    return (
                      <>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onDeacceptOffer(parent.id);
                          }}
                          className="text-sm"
                        >
                          Unaccept Offer
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onSettlementCheck(parent);
                          }}
                          className="text-sm"
                        >
                          Settlement Check
                        </DropdownMenuItem>
                      </>
                    );
                  }
                })()}
                {!checkSettlementStatus && (
                  <>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onDeacceptOffer(parent.id);
                      }}
                      className="text-sm"
                    >
                      Unaccept Offer
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onSettlementCheck(parent);
                      }}
                      className="text-sm"
                    >
                      Settlement Check
                    </DropdownMenuItem>
                  </>
                )}
              </>
            ) : (
              <>
                {!hasAcceptedOffer && !parent.is_archived && (
                  <DropdownMenuItem
                    onClick={() => onAcceptOffer(parent.id)}
                    className="text-sm"
                  >
                    Accept Offer
                  </DropdownMenuItem>
                )}
                {!parent.is_archived && onEditDemand && (
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditDemand(parent);
                    }}
                    disabled={hasAcceptedOffer && !parent.is_archived}
                    className="text-sm"
                  >
                    Edit Demand
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onArchiveToggle(parent.id, !parent.is_archived);
                  }}
                  className="text-sm"
                >
                  {parent.is_archived ? 'Unarchive' : 'Archive'}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
