import {
  Briefcase,
  ExternalLink,
  Building2,
  UserCircle2,
  DollarSign,
  Clock,
  Phone,
  FileText,
  AlertCircle,
  User,
  Scale,
  Hash,
  LucideIcon,
  Trash2
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { useEmployersQuery, useDeleteEmployerMutation } from "@/services/case-management/employerService";
import { Skeleton } from "@/components/ui/skeleton";
import { AddEditEmployer } from "@/app/case/employment/components/AddEditEmployer";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import RichTextViewer from "@/components/ui/RichTextViewer";

interface EmploymentPageProps {
  caseId: string;
}

interface InfoFieldProps {
  icon: LucideIcon;
  label: string;
  value: string | number | React.ReactNode;
}

const InfoField = ({ icon: Icon, label, value }: InfoFieldProps) => (
  <div className="flex items-start gap-3">
    <div className="min-w-[140px] flex items-center gap-2 shrink-0">
      <Icon className="h-4 w-4 text-gray-500 shrink-0" />
      <span className="text-sm text-gray-500">{label}</span>
    </div>
    <div className="font-medium text-[#060216] text-justify break-words">{value}</div>
  </div>
);

export function EmploymentPage({ caseId }: EmploymentPageProps) {
  const router = useRouter();
  const { data: employers, isLoading, error } = useEmployersQuery(caseId);
  const deleteEmployer = useDeleteEmployerMutation(caseId);
  const [employerToDelete, setEmployerToDelete] = useState<{ id: string; name: string } | null>(null);

  if (error) {
    return (
      <div className="w-full mt-4">
        <div className="text-center text-red-600">
          Failed to load employment details. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mt-4 flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Briefcase className="h-5 w-5 text-[#060216]" />
          <h3 className="text-lg font-semibold text-[#060216]">
            Employment Details
          </h3>
        </div>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push(`/case/employment/${caseId}`)}
            className="hover:bg-gray-100"
          >
            <ExternalLink className="h-4 w-4 text-gray-600" />
          </Button>
        </div>
      </div>

      <AddEditEmployer isEdit={false} caseId={caseId} />

      {isLoading ? (
        // Loading skeleton
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-6 w-36" />
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-6 w-32" />
              </div>
              <div className="space-y-4">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-6 w-36" />
                <Skeleton className="h-6 w-40" />
              </div>
            </div>
          </CardContent>
        </Card>
      ) : employers && employers.length > 0 ? (
        employers.map((employer) => (
          <Card key={employer.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-gray-500" />
                  <h4 className="text-lg font-semibold text-[#060216]">{employer.company_name}</h4>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setEmployerToDelete({
                      id: employer.id.toString(),
                      name: employer.company_name
                    })}
                    className="hover:bg-red-100"
                  >
                    <Trash2 className="h-4 w-4 text-red-600" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6">
                <div className="space-y-4">
                  <InfoField
                    icon={Building2}
                    label="Company"
                    value={employer.company_name}
                  />
                  <InfoField
                    icon={UserCircle2}
                    label="Position"
                    value={employer.position || "—"}
                  />
                  <InfoField
                    icon={DollarSign}
                    label="Wage"
                    value={employer.wage ? `$${employer.wage} (${employer.income_type})` : "—"}
                  />
                  <InfoField
                    icon={Clock}
                    label="Time Off"
                    value={employer.weeks_missed || employer.hours_missed
                      ? `${employer.weeks_missed || 0}w ${employer.hours_missed || 0}h`
                      : "—"}
                  />
                  <InfoField
                    icon={DollarSign}
                    label="Total Lost Wages"
                    value={employer.total_lost_wages ? `$${employer.total_lost_wages}` : "—"}
                  />
                </div>

                <div className="space-y-4">
                  <InfoField
                    icon={AlertCircle}
                    label="Status"
                    value={
                      <span className={`text-xs px-3 py-1 rounded-full ${employer.lost_wages_status === "pending"
                          ? "bg-yellow-100 text-yellow-700"
                          : employer.lost_wages_status === "approved"
                            ? "bg-blue-100 text-[#060216]-700"
                            : employer.lost_wages_status === "denied"
                              ? "bg-red-100 text-red-700"
                              : employer.lost_wages_status === "not_applicable"
                                ? "bg-gray-100 text-gray-700"
                                : "bg-gray-100 text-gray-700"
                        }`}>
                        {employer.lost_wages_status || "—"}
                      </span>
                    }
                  />
                  <InfoField
                    icon={User}
                    label="Contact"
                    value={employer.contact_first_name || employer.contact_last_name
                      ? `${employer.contact_first_name || ""} ${employer.contact_last_name || ""}`.trim()
                      : "—"}
                  />
                  <InfoField
                    icon={Phone}
                    label="Phone"
                    value={employer.phone || "—"}
                  />
                </div>
              </div>
              <InfoField
                icon={FileText}
                label="Description"
                value={
                  employer.description ? (
                    <RichTextViewer 
                      data={employer.description} 
                      className="prose prose-sm max-w-none"
                    />
                  ) : "—"
                }
              />

              {employer.workers_compensation && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6">
                    {/* Legal Representative Section */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-[#060216] mb-4">
                        Legal Representative
                      </h4>
                      {employer.workers_compensation.law_firm &&
                        typeof employer.workers_compensation.law_firm === "object" && (
                          <>
                            <InfoField
                              icon={Scale}
                              label="Law Firm"
                              value={employer.workers_compensation.law_firm.office_name}
                            />
                            <InfoField
                              icon={Phone}
                              label="Phone"
                              value={employer.workers_compensation.law_firm.phone || "—"}
                            />
                          </>
                        )}
                      {employer.workers_compensation.attorney &&
                        typeof employer.workers_compensation.attorney === "object" && (
                          <>
                            <InfoField
                              icon={User}
                              label="Attorney"
                              value={`${employer.workers_compensation.attorney.first_name} ${employer.workers_compensation.attorney.last_name}`}
                            />
                            <InfoField
                              icon={Phone}
                              label="Phone"
                              value={employer.workers_compensation.attorney.phone || "—"}
                            />
                          </>
                        )}
                    </div>

                    {/* Insurance Provider Section */}
                    {employer.workers_compensation.insurance_company &&
                      typeof employer.workers_compensation.insurance_company === "object" && (
                        <div className="space-y-4">
                          <h4 className="font-semibold text-sm text-[#060216] mb-4">
                            Insurance Provider
                          </h4>
                          <InfoField
                            icon={Building2}
                            label="Company"
                            value={employer.workers_compensation.insurance_company.name}
                          />
                          <InfoField
                            icon={Phone}
                            label="Phone"
                            value={employer.workers_compensation.insurance_company.phone || "—"}
                          />
                          <InfoField
                            icon={Phone}
                            label="Fax"
                            value={employer.workers_compensation.insurance_company.fax || "—"}
                          />
                          <InfoField
                            icon={Hash}
                            label="Policy #"
                            value={employer.workers_compensation.insurance_policy_number || "—"}
                          />
                          <InfoField
                            icon={Hash}
                            label="Claim #"
                            value={employer.workers_compensation.insurance_claim_number || "—"}
                          />
                        </div>
                      )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="text-center text-gray-500">
          No employment details found.
        </div>
      )}

      <DeleteConfirmationDialog
        open={!!employerToDelete}
        onOpenChange={(open) => !open && setEmployerToDelete(null)}
        onConfirm={() => {
          if (employerToDelete) {
            deleteEmployer.mutate(employerToDelete.id);
            setEmployerToDelete(null);
          }
        }}
        title="Delete Employer"
        description={`Are you sure you want to delete ${employerToDelete?.name}? This action cannot be undone.`}
      />
    </div>
  );
}
