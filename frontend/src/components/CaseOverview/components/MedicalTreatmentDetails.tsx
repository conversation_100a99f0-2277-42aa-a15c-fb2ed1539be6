"use client";

import React, { MouseEvent, useState, useC<PERSON>back, useMemo, Fragment, useRef, ChangeEvent, useEffect } from "react";
import {
  FileText,
  Trash,
  Wallet,
  Link,
  ChevronDown,
  ChevronUp,
  Upload,
  Building2,
  Receipt,
  FileStack,
  Users,
  DollarSign,
  FileSpreadsheet,
} from "lucide-react";
import { useRouter } from 'next/navigation';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import SelectProver from "@/app/case/medical-treatment/components/SelectProver";
import {
  useTreatmentProvidersQuery,
  useDeleteTreatmentProviderMutation,
  usePatchTreatmentProviderStatusMutation,
  useMedicalProviderCasesQuery,
} from "@/services/case-management/medicalTreatmentService";
import { RecordStatus, requestedby, TreatmentProvider } from "@/type/case-management/medicalTreatmentTypes";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { formatDateForA<PERSON>, formatDateForDisplay } from "@/utils/dateUtils";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from "@/store/slices/templatesSlice";
import { Badge } from "@/components/ui/badge";
import { AddEditTreatment } from "@/app/case/medical-treatment/components/AddEditTreatment";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { MedicalProvider, ContactType } from "@/type/case-management/orgTypes";
import { AddEditCost } from "@/components/CaseOverview/components/AddEditCost";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { CurrencyDisplay } from "@/components/ui/currency-display";
import { CaseSection, useUploadBySectionMutation } from "@/services/case-management/newDocumentManagement";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PhoneLink } from "@/components/ui/phone-link";
import { AddressLink } from "@/components/gMap/address-link";
import { KPICard } from "@/components/ui/kpi-card";
import { InfoFieldGroup } from "@/components/ui/InfoField";
import { cn } from "@/lib/utils";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { MedPayDepositList } from "./MedPayDepositList";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface MedicalTreatmentProps {
  caseId: string;
}

// interface TreatmentInfoFieldProps {
//   icon: LucideIcon;
//   label: string;
//   value: string | ReactNode;
//   className?: string;
// }

// const TreatmentInfoField: FC<TreatmentInfoFieldProps> = ({
//   icon: Icon,
//   label,
//   value,
// }) => (
//   <div>
//     <div className="flex items-center gap-2 mb-1">
//       <Icon className="h-4 w-4 text-[#060216]/50" />
//       <label className="text-sm text-[#060216]/50">{label}</label>
//     </div>
//     <p className="text-sm text-[#060216]">{value}</p>
//   </div>
// );

// Helper function to determine badge variant based on status
const getStatusVariant = (status: RecordStatus | undefined): "success" | "warning" | "secondary" => {
  if (status === RecordStatus.RECEIVED || status === RecordStatus.NA) return "success";
  if (status === RecordStatus.REQUESTED) return "warning";
  return "secondary";
};

// Helper function to format status details
const formatStatusDetails = ({ status, requestedAt, requestedBy, receivedAt, receivedBy }: {
  status: RecordStatus | undefined,
  requestedAt?: string,
  requestedBy?: requestedby,
  receivedAt?: string,
  receivedBy?: requestedby
}) => {
  if (!status) return null;

  if (status === RecordStatus.RECEIVED && receivedAt) {
    return `Received by ${receivedBy?.first_name} ${receivedBy?.last_name} on ${formatDateForDisplay(receivedAt)} `;
  } else if (requestedAt && requestedBy) {
    // Show the requested by information for all status options, not just REQUESTED
    return `Updated by ${requestedBy?.first_name} ${requestedBy?.last_name} on ${formatDateForDisplay(requestedAt)} `;
  }
  return null;
};

// Create a separate component for individual treatment cards
function TreatmentCard({ treatment, caseId, onDelete, onStatusUpdate, isExpanded: defaultExpanded }: {
  treatment: TreatmentProvider;
  caseId: string;
  onDelete: (providerId: string, e: MouseEvent) => void;
  onStatusUpdate: (providerId: string, type: "records_status" | "billing_status", status: RecordStatus) => void;
  isExpanded: boolean;
}) {
  const providerId = typeof treatment.id === "number" ? treatment.id.toString() : treatment.id.id.toString();
  const [isExpanded, setIsExpanded] = useState<boolean>(defaultExpanded);
  const [showConnectedCases, setShowConnectedCases] = useState(false);
  const [selectedMedicalProviderId, setSelectedMedicalProviderId] = useState<number | undefined>(undefined);
  const [selectedProviderName, setSelectedProviderName] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const uploadBySection = useUploadBySectionMutation();
  const recordsFileInputRef = useRef<HTMLInputElement>(null);
  const billsFileInputRef = useRef<HTMLInputElement>(null);

  const router = useRouter();
  const { data: providerCasesData, isLoading: isLoadingCases } = useMedicalProviderCasesQuery(
    selectedMedicalProviderId,
    caseId,
    currentPage,
    pageSize
  );

  // Update local expansion state when parent state changes
  useEffect(() => {
    setIsExpanded(defaultExpanded);
  }, [defaultExpanded]);

  const toggleExpand = (e: MouseEvent) => {
    // Don't toggle if clicking on interactive elements
    const target = e.target as HTMLElement;
    if (
      target.closest('button') ||
      target.closest('.dropdown-menu') ||
      target.closest('a') ||
      target.closest('[role="button"]') ||
      target.closest('[role="menuitem"]')
    ) {
      return;
    }
    setIsExpanded(!isExpanded);
  };

  const handleShowConnectedCases = (providerId: number, providerName: string, e: MouseEvent) => {
    e.stopPropagation();
    setSelectedMedicalProviderId(providerId);
    setSelectedProviderName(providerName);
    setCurrentPage(1);
    setShowConnectedCases(true);
  };

  const handleConnectedCaseClick = (connectedCaseId: string) => {
    setShowConnectedCases(false);
    router.push(`/dashboard/case-view/${connectedCaseId}`);
  };

  const handleStatusChange = (
    type: "records_status" | "billing_status",
    status: RecordStatus
  ) => {
    const currentDate = formatDateForApi(new Date());
    const currentUser = "Current User"; // TODO: Get actual current user

    const updateData = {
      [type]: status,
      [`${type === "records_status" ? "records" : "billing"} _date`]: currentDate,
    };

    // Always update the requested fields for any status change to track who made the change
    updateData[`${type === "records_status" ? "records" : "billing"} _requested_at`] = currentDate;
    updateData[`${type === "records_status" ? "records" : "billing"} _requested_by`] = currentUser;

    // Add received fields only for RECEIVED status
    if (status === RecordStatus.RECEIVED) {
      updateData[`${type === "records_status" ? "records" : "billing"} _received_at`] = currentDate;
      updateData[`${type === "records_status" ? "records" : "billing"} _received_by`] = currentUser;
    }

    onStatusUpdate(providerId, type, status);
  };

  const handleExport = useCallback(() => {
    if (!providerCasesData) return;

    // Format the data for CSV export
    const csvData = [
      // Header row
      ['#', 'Case', 'Client', 'Status', 'Original', 'Adjusted', "Final", 'Health Ins', 'Still Owed', 'Record Status', 'Billing Status', 'Reduction'],
      // Data rows
      ...providerCasesData.cases.map((c, index) => [
        String(index + 1),
        c.case_name,
        c.client_name,
        c.organization_status,
        c.original ? `$${c.original.toFixed(2)} ` : '',
        c.adjusted ? `$${c.adjusted.toFixed(2)} ` : '',
        c.final ? `$${c.final.toFixed(2)} ` : '',
        c.health_ins ? `$${c.health_ins.toFixed(2)} ` : '',
        c.still_owed ? `$${c.still_owed.toFixed(2)} ` : '',
        c.record_status,
        c.billing_status,
        `${c.reduction.toFixed(2)}% `,
      ])
    ];

    // Convert to CSV string
    const csvContent = csvData.map(row => row.join(',')).join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${selectedProviderName.replace(/\s+/g, '_')}_cases_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [providerCasesData, selectedProviderName]);

  // Filter cases based on search query
  const filteredCases = useMemo(() => {
    if (!providerCasesData || !searchQuery.trim()) {
      return providerCasesData?.cases || [];
    }

    return providerCasesData.cases.filter(c =>
      c.case_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      c.organization_status.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [providerCasesData, searchQuery]);

  // Calculate pagination info
  const totalPages = useMemo(() => {
    if (!providerCasesData?.count) return 0;
    return Math.ceil(providerCasesData.count / pageSize);
  }, [providerCasesData?.count, pageSize]);

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
  };

  const handleFileUpload = async (section: CaseSection, event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      await uploadBySection.mutateAsync({
        case_id: caseId,
        section: section,
        file: file,
        medical_treatment_id: providerId
      });

      // Clear the file input
      if (event.target) {
        event.target.value = '';
      }
    } catch (error) {
      console.error("Upload error:", error);
    }
  };

  const handleUploadClick = (section: CaseSection) => {
    if (section === CaseSection.MEDICAL_RECORDS) {
      recordsFileInputRef.current?.click();
    } else if (section === CaseSection.MEDICAL_BILLS) {
      billsFileInputRef.current?.click();
    }
  };

  return (
    <Card
      key={typeof treatment.id === "number" ? treatment.id : treatment.id.id}
      className="relative cursor-pointer hover:bg-gray-50/50 transition-colors"
      onClick={toggleExpand}
    >
      <CardContent className="p-4">
        {/* Simplified Header - Always visible */}
        <div className="flex items-center justify-between" onClick={e => e.stopPropagation()}>
          <div className="flex-1">
            {/* Treatment Name and Original Bill */}
            <div className="flex items-center gap-5 mb-2 cursor-text">
              <div className="text-sm font-semibold text-[#060216]">
                {typeof treatment.medical_provider === "number"
                  ? "Loading..."
                  : treatment.medical_provider.company}
              </div>
              <div className="text-sm font-semibold text-[#516CF5]">
                <CurrencyDisplay amount={treatment.original_bill || 0} />
              </div>
              {treatment.medical_provider?.specialties
                ?.split(",")
                .map((specialty) => (
                  <Badge
                    key={specialty}
                    variant="secondary"
                    className="capitalize text-xs bg-blue-50 text-blue-600"
                  >
                    {specialty}
                  </Badge>
                ))}
            </div>
            {/* Status Badges - Now with details */}
            <div className="flex items-center gap-3" onClick={e => e.stopPropagation()}>
              <DropdownMenu>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex flex-col">
                        <DropdownMenuTrigger asChild>
                          <Badge
                            variant={getStatusVariant(treatment.records_status || RecordStatus.NOT_REQUESTED)}
                            className="cursor-pointer hover:opacity-80 transition-opacity text-xs"
                          >
                            Records {(treatment.records_status || RecordStatus.NOT_REQUESTED).replace("_", " ")}
                          </Badge>
                        </DropdownMenuTrigger>
                        {formatStatusDetails({
                          status: treatment.records_status,
                          requestedAt: treatment.records_requested_at,
                          requestedBy: treatment.records_requested_by,
                          receivedAt: treatment.records_received_at,
                          receivedBy: treatment.records_received_by
                        }) && (
                            <span className="text-xs text-gray-500 mt-1">
                              {formatStatusDetails({
                                status: treatment.records_status,
                                requestedAt: treatment.records_requested_at,
                                requestedBy: treatment.records_requested_by,
                                receivedAt: treatment.records_received_at,
                                receivedBy: treatment.records_received_by
                              })}
                            </span>
                          )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="space-y-1">
                        <p className="font-semibold">Medical Records Status</p>
                        <p className="text-sm">
                          {treatment.records_status === RecordStatus.RECEIVED && "Medical records have been received"}
                          {treatment.records_status === RecordStatus.REQUESTED && "Medical records have been requested but not yet received"}
                          {treatment.records_status === RecordStatus.NOT_REQUESTED && "Medical records have not been requested yet"}
                          {treatment.records_status === RecordStatus.NA && "Medical records are not applicable"}
                        </p>
                        <p className="text-xs text-gray-500">Click to change status</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <DropdownMenuContent>
                  {Object.values(RecordStatus).map((status) => (
                    <DropdownMenuItem
                      key={status}
                      onClick={() => handleStatusChange("records_status", status)}
                    >
                      Records {status.replace("_", " ")}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex flex-col">
                        <DropdownMenuTrigger asChild>
                          <Badge
                            variant={getStatusVariant(treatment.billing_status || RecordStatus.NOT_REQUESTED)}
                            className="cursor-pointer hover:opacity-80 transition-opacity text-xs"
                          >
                            Billing {(treatment.billing_status || RecordStatus.NOT_REQUESTED).replace("_", " ")}
                          </Badge>
                        </DropdownMenuTrigger>
                        {formatStatusDetails({
                          status: treatment.billing_status,
                          requestedAt: treatment.billing_requested_at,
                          requestedBy: treatment.billing_requested_by,
                          receivedAt: treatment.billing_received_at,
                          receivedBy: treatment.billing_received_by
                        }) && (
                            <span className="text-xs text-gray-500 mt-1">
                              {formatStatusDetails({
                                status: treatment.billing_status,
                                requestedAt: treatment.billing_requested_at,
                                requestedBy: treatment.billing_requested_by,
                                receivedAt: treatment.billing_received_at,
                                receivedBy: treatment.billing_received_by
                              })}
                            </span>
                          )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="space-y-1">
                        <p className="font-semibold">Medical Billing Status</p>
                        <p className="text-sm">
                          {treatment.billing_status === RecordStatus.RECEIVED && "Medical bills have been received"}
                          {treatment.billing_status === RecordStatus.REQUESTED && "Medical bills have been requested but not yet received"}
                          {treatment.billing_status === RecordStatus.NOT_REQUESTED && "Medical bills have not been requested yet"}
                          {treatment.billing_status === RecordStatus.NA && "Medical bills are not applicable"}
                        </p>
                        <p className="text-xs text-gray-500">Click to change status</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <DropdownMenuContent>
                  {Object.values(RecordStatus).map((status) => (
                    <DropdownMenuItem
                      key={status}
                      onClick={() => handleStatusChange("billing_status", status)}
                    >
                      Billing {status.replace("_", " ")}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Action Buttons - Always visible */}
          <div className="flex items-center gap-2" onClick={e => e.stopPropagation()}>
            <TooltipProvider>
              <DropdownMenu>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuTrigger asChild>
                      <Button variant="link" className="p-0">
                        <FileText className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Documents</p>
                  </TooltipContent>
                </Tooltip>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    className="hover:bg-emerald-50 cursor-pointer py-2 text-emerald-600"
                    onClick={() => handleUploadClick(CaseSection.MEDICAL_RECORDS)}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    <span>Upload Medical Records</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="hover:bg-emerald-50 cursor-pointer py-2 text-emerald-600"
                    onClick={() => handleUploadClick(CaseSection.MEDICAL_BILLS)}
                  >
                    <Upload className="mr-2 h-4 w-4 stroke-[1.5]" />
                    <span>Upload Medical Bills</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span>
                    <AddEditTreatment
                      caseId={caseId}
                      treatmentId={treatment.id?.toString()}
                      isEdit={true}
                      treatment={treatment}
                      provider={treatment.medical_provider as MedicalProvider}
                    />
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Edit</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="link"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(providerId, e);
                    }}
                    className="p-0"
                  >
                    <Trash className="h-4 w-4 text-red-500" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Delete</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span>
                    <ManageEmailTemplate
                      caseId={caseId}
                      contextType={"health_provider" as TemplateContextType}
                      healthProviderId={treatment.id.toString()}
                    />
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Email</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Connected Cases Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="link"
                    className="p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShowConnectedCases(
                        typeof treatment.medical_provider === "number"
                          ? treatment.medical_provider
                          : treatment.medical_provider.id,
                        typeof treatment.medical_provider === "number"
                          ? ""
                          : treatment.medical_provider.company || "",
                        e
                      );
                    }}
                  >
                    <Link className="h-4 w-4 text-gray-500" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Sync to linked cases</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Expand/Collapse Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="link"
                    className="p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsExpanded(!isExpanded);
                    }}
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </Button>
                </TooltipTrigger>
                {/* <TooltipContent>
                  <p>{isExpanded ? "Collapse details" : "Expand details"}</p>
                </TooltipContent> */}
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Expandable Content */}
        {isExpanded && (
          <div onClick={e => e.stopPropagation()}>
            {/* Financial Information Grid */}
            <div className="mt-6 px-6">
              {/* Primary Financial Information */}
              <InfoFieldGroup
                columns={6}
                fields={[
                  {
                    label: "Final Cost",
                    value: treatment.final_cost || '',
                    isCurrency: true,
                    className: "text-[#516CF5]"
                  },
                  {
                    label: "Original Bill",
                    value: treatment.original_bill || 0,
                    isCurrency: true,
                    className: "text-[#516CF5]"
                  },
                  {
                    label: "Paid",
                    value: Number(treatment.insurance_paid || 0) +
                      Number(treatment.medpay_pip_paid || 0) +
                      Number(treatment.client_paid || 0),
                    isCurrency: true,
                    className: "text-[#1C7B34]"
                  },
                  {
                    label: "Still Owed",
                    value: treatment.still_owed || 0,
                    isCurrency: true,
                    className: "text-[#D62424]"
                  },
                  {
                    label: "Adjustment",
                    value: treatment.adjusted_bill || 0,
                    isCurrency: true
                  },
                  {
                    label: "Write Off",
                    value: treatment.account_number || 0,
                    isCurrency: true
                  },
                  {
                    label: "Paid + Owed",
                    value: treatment.paid_plus_owed || 0,
                    isCurrency: true
                  },
                  {
                    label: "Insurance Paid",
                    value: treatment.insurance_paid || 0,
                    isCurrency: true
                  },
                  {
                    label: "MedPay/PIP Paid",
                    value: treatment.medpay_pip_paid || 0,
                    isCurrency: true
                  },
                  {
                    label: "Client Paid",
                    value: treatment.client_paid || 0,
                    isCurrency: true
                  },
                  {
                    label: "Treatment Status",
                    value: treatment.treatment_status || "—",
                  }
                ]}
              />

              <Separator className="my-4" />

              {/* Adjustments and Write-offs */}
              {/* <InfoFieldGroup
                columns={3}
                gap="gap-4"
                className="mb-4"
                fields={[
                  {
                    label: "Adjustment",
                    value: treatment.adjusted_bill || 0,
                    isCurrency: true
                  },
                  {
                    label: "Write Off",
                    value: treatment.account_number || 0,
                    isCurrency: true
                  },
                  {
                    label: "Paid + Owed",
                    value: treatment.paid_plus_owed || 0,
                    isCurrency: true
                  }
                ]}
              /> */}

              {/* Payment Details */}
              {/* <InfoFieldGroup
                columns={3}
                gap="gap-4"
                className="mb-6"
                fields={[
                  {
                    label: "Insurance Paid",
                    value: treatment.insurance_paid || 0,
                    isCurrency: true
                  },
                  {
                    label: "MedPay/PIP Paid",
                    value: treatment.medpay_pip_paid || 0,
                    isCurrency: true
                  },
                  {
                    label: "Client Paid",
                    value: treatment.client_paid || 0,
                    isCurrency: true
                  }
                ]}
              /> */}

              {/* Treatment Details */}
              <InfoFieldGroup
                columns={3}
                gap="gap-4"
                className="mb-6"
                fields={[
                  {
                    label: "First Visit",
                    value: treatment.first_visit ? formatDateForDisplay(treatment.first_visit) : "—"
                  },
                  {
                    label: "Last Visit",
                    value: treatment.last_visit ? formatDateForDisplay(treatment.last_visit) : "—"
                  },
                  {
                    label: "Number of Visits",
                    value: treatment.number_of_visits || "—"
                  }
                ]}
              />

              {/* Contact Information */}
              {/* <InfoFieldGroup
                columns={3}
                gap="gap-4"
                className="mb-6"
                fields={[
                  {
                    label: "Primary Phone",
                    value: officeContact?.phone || "—",
                    isPhone: true
                  },
                  {
                    label: "Fax",
                    value: officeContact?.fax || "—",
                    isPhone: true
                  },
                  {
                    label: "Email",
                    value: officeContact?.email || "—",
                    isMail: true
                  }
                ]}
              /> */}

              {/* Address Section */}
              {/* <InfoFieldGroup
                columns={1}
                className="mb-6"
                fields={[
                  {
                    label: "Address",
                    value: (
                      <AddressLink
                        address={{
                          street1: officeContact?.street1 || undefined,
                          street2: officeContact?.street2 || undefined,
                          city: officeContact?.city || undefined,
                          state: officeContact?.state || undefined,
                          zip_code: officeContact?.zip_code || undefined,
                        }}
                      />
                    )
                  }
                ]}
              /> */}

              {/* Treatment Description */}
              <div className="bg-[#F7F7F7] p-4 rounded-lg">
                <p className="text-xs text-gray-500 mb-1">Treatment Description</p>
                <div className="text-sm font-semibold">
                  {treatment.treatment_description ? (
                    <RichTextViewer data={treatment.treatment_description} isCopied={true} />
                  ) : (
                    "No description provided"
                  )}
                </div>
              </div>

              <div className="bg-[#F7F7F7] p-4 rounded-lg mt-2">
                <p className="text-xs text-gray-500 mb-1">Treatment Notes</p>
                <div className="text-sm font-semibold">
                  {treatment.medical_provider.note ? (
                    <RichTextViewer data={treatment.medical_provider?.note} isCopied={true} />
                  ) : (
                    "No notes provided"
                  )}
                </div> 
              </div>
            </div>

            <Separator className="my-4" />

            {/* Contact Information Tabs */}
            <div className="p-4 pt-0">
              {(() => {
                const contacts = {
                  office: typeof treatment.medical_provider !== "number" &&
                    treatment.medical_provider?.contacts?.find(
                      (contact) => contact.contact_type === "OFFICE"
                    ),
                  billing: typeof treatment.medical_provider !== "number" &&
                    treatment.medical_provider?.contacts?.find(
                      (contact) => contact.contact_type === "BILLING"
                    ),
                  records: typeof treatment.medical_provider !== "number" &&
                    treatment.medical_provider?.contacts?.find(
                      (contact) => contact.contact_type === "RECORDS"
                    )
                };

                const tabSections = [
                  { id: 'office', label: 'Office', icon: Building2, contact: contacts.office },
                  { id: 'billing', label: 'Billing', icon: Receipt, contact: contacts.billing },
                  { id: 'records', label: 'Records', icon: FileStack, contact: contacts.records }
                ];

                const availableTabs = tabSections.filter(section => section.contact);
                const defaultTab = availableTabs.length > 0 ? availableTabs[0].id : "office";

                return (
                  <Tabs defaultValue={defaultTab} className="w-full">
                    <div className="relative w-full">
                      <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                        <TabsList className="w-full">
                          {tabSections.map(section => (
                            <TabsTrigger
                              key={section.id}
                              value={section.id}
                              className={cn(
                                "flex items-center gap-2",
                                !section.contact && "hover:no-underline hover:text-muted-foreground cursor-not-allowed"
                              )}
                              disabled={!section.contact}
                            >
                              <section.icon className="h-4 w-4" />
                              {section.label}
                            </TabsTrigger>
                          ))}
                        </TabsList>
                      </div>
                    </div>

                    {availableTabs.length === 0 ? (
                      <div className="text-center py-2 text-gray-500">
                        No contact information available
                      </div>
                    ) : (
                      <div className="mt-2">
                        {tabSections.map(section => (
                          <TabsContent key={section.id} value={section.id}>
                            {section.contact ? (
                              <InfoFieldGroup
                                columns={3}
                                caseId={caseId}
                                // gap="gap-4"
                                fields={[
                                  {
                                    label: "Phone",
                                    value: (
                                      <>
                                        <PhoneLink phone={section.contact.phone} />
                                        {section.contact.phone_ext && (
                                          <span className="text-gray-500 ml-1">
                                            ext. {section.contact.phone_ext}
                                          </span>
                                        )}
                                      </>
                                    ),
                                    className: "bg-gray-50 p-2 rozunded"
                                  },
                                  {
                                    label: "Email",
                                    value: section.contact.email,
                                    isMail: true,

                                  },
                                  {
                                    label: "Fax",
                                    value: section.contact.fax,
                                    isPhone: true,
                                  },
                                  {
                                    label: "Address",
                                    value: (
                                      <AddressLink
                                        address={{
                                          street1: section.contact.street1 || undefined,
                                          street2: section.contact.street2 || undefined,
                                          city: section.contact.city || undefined,
                                          state: section.contact.state || undefined,
                                          zip_code: section.contact.zip_code || undefined
                                        }}
                                        className="text-blue-600 hover:underline"
                                      >
                                        {section.contact.street1}
                                        {section.contact.street2 && <br />}
                                        {section.contact.street2}
                                        {(section.contact.city || section.contact.state || section.contact.zip_code) && <br />}
                                        {[section.contact.city, section.contact.state, section.contact.zip_code]
                                          .filter(Boolean)
                                          .join(", ")}
                                      </AddressLink>
                                    ),
                                    className: "bg-gray-50 p-2 rounded col-span-3"
                                  }
                                ]}
                              />
                            ) : (
                              <div className="text-center py-8 text-gray-500">
                                No {section.label.toLowerCase()} contact information available
                              </div>
                            )}
                          </TabsContent>
                        ))}
                      </div>
                    )}
                  </Tabs>
                );
              })()}
            </div>
          </div>
        )}
      </CardContent>

      {/* Hidden file inputs for uploads */}
      <input
        type="file"
        ref={recordsFileInputRef}
        className="hidden"
        onChange={(e) => handleFileUpload(CaseSection.MEDICAL_RECORDS, e)}
        multiple
        onClick={e => e.stopPropagation()}
      />
      <input
        type="file"
        ref={billsFileInputRef}
        className="hidden"
        onChange={(e) => handleFileUpload(CaseSection.MEDICAL_BILLS, e)}
        multiple
        onClick={e => e.stopPropagation()}
      />

      {/* Connected Cases Dialog */}
      <Dialog open={showConnectedCases} onOpenChange={setShowConnectedCases}>
        <DialogContent className="max-w-4xl max-h-[85vh] flex flex-col overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100" onClick={e => e.stopPropagation()}>
          <DialogHeader>
            <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
              Connected Cases - {selectedProviderName}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1">
            {isLoadingCases ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
              </div>
            ) : providerCasesData ? (
              <div className="flex flex-col h-full">
                {/* Provider summary section */}
                <div className="px-6">
                  <div className="grid grid-cols-2 gap-x-8 border-b pb-4">
                    <div className="space-y-6">
                      <div>
                        <div className="text-sm text-gray-500">Money Paid</div>
                        <div className="text-xl font-normal">${providerCasesData.summary.money_paid.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-500">Avg. Per Case</div>
                        <div className="text-xl font-normal">${providerCasesData.summary.avg_per_case.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-500">Avg. Reduction</div>
                        <div className="text-xl font-normal">{providerCasesData.summary.avg_reduction.toFixed(2)}%</div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <div className="text-sm text-gray-500">Total Cases</div>
                        <div className="text-xl font-normal">{providerCasesData.summary.total_cases}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-500">Open Cases</div>
                        <div className="text-xl font-normal">{providerCasesData.summary.open_cases}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-500">Dropped Cases</div>
                        <div className="text-xl font-normal">{providerCasesData.summary.dropped_cases}</div>
                      </div>
                    </div>
                  </div>

                  {/* Search field */}
                  <div className="py-4">
                    <input
                      type="text"
                      placeholder="Search"
                      className="w-full border rounded p-2 text-sm"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                {/* Cases table with fixed header */}
                <div className="flex-1 overflow-hidden">
                  <div className="relative overflow-auto max-h-[50vh] rounded border">
                    <div className="sticky top-0 z-10 w-full overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-100 border-b">
                            <th className="sticky top-0 p-2 text-left min-w-[40px] w-[40px] font-medium text-sm">#</th>
                            <th className="sticky top-0 p-2 text-left min-w-[200px] w-[30%] font-medium text-sm">Case</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Status</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Original</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Adjusted</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Health Ins</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Final</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Still Owed</th>
                            <th className="sticky top-0 p-2 text-left min-w-[120px] font-medium text-sm">Record Status</th>
                            <th className="sticky top-0 p-2 text-left min-w-[120px] font-medium text-sm">Billing Status</th>
                            <th className="sticky top-0 p-2 text-left min-w-[100px] font-medium text-sm">Reduction</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredCases.map((connectedCase, index) => (
                            <tr
                              key={connectedCase.id}
                              className="border-b cursor-pointer hover:bg-gray-50"
                              onClick={() => handleConnectedCaseClick(connectedCase.id)}
                            >
                              <td className="p-2 text-sm">{((currentPage - 1) * pageSize) + index + 1}</td>
                              <td className="p-2 font-medium text-sm truncate max-w-[200px]">{connectedCase.case_name || 'Unnamed Case'}</td>
                              <td className="p-2 text-sm">{connectedCase.organization_status || '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.original ? `$${connectedCase.original.toLocaleString('en-US', { minimumFractionDigits: 2 })}` : '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.adjusted ? `$${connectedCase.adjusted.toLocaleString('en-US', { minimumFractionDigits: 2 })}` : '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.health_ins ? `$${connectedCase.health_ins.toLocaleString('en-US', { minimumFractionDigits: 2 })}` : '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.final ? `$${connectedCase.final.toLocaleString('en-US', { minimumFractionDigits: 2 })}` : '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.still_owed ? `$${connectedCase.still_owed.toLocaleString('en-US', { minimumFractionDigits: 2 })}` : '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.record_status || '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.billing_status || '—'}</td>
                              <td className="p-2 text-sm">{connectedCase.reduction > 0 ? `${connectedCase.reduction.toFixed(2)}%` : '0.00%'}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {filteredCases.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No cases found matching your search.
                    </div>
                  )}
                </div>

                {/* Pagination */}
                {providerCasesData.count > 0 && (
                  <div className="px-6 flex items-center justify-between border-t mt-4 pt-4">
                    <div className="text-sm text-gray-500">
                      Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, providerCasesData.count)} of {providerCasesData.count} results
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        disabled={currentPage === 1}
                        className="h-8 w-8 p-0"
                      >
                        <span className="sr-only">First Page</span>
                        <span>«</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="h-8 w-8 p-0"
                      >
                        <span className="sr-only">Previous Page</span>
                        <span>‹</span>
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center">
                        {Array.from({ length: Math.min(totalPages, 5) }, (_, idx) => {
                          let pageNum = currentPage;
                          if (totalPages <= 5) {
                            pageNum = idx + 1;
                          } else {
                            // Show 5 pages at most with current page in middle (if possible)
                            const offset = Math.min(
                              Math.max(0, currentPage - 3),
                              Math.max(0, totalPages - 5)
                            );
                            pageNum = idx + 1 + offset;
                          }

                          return (
                            <Button
                              key={pageNum}
                              variant={currentPage === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              className="h-8 w-8 p-0 mx-0.5"
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages || totalPages === 0}
                        className="h-8 w-8 p-0"
                      >
                        <span className="sr-only">Next Page</span>
                        <span>›</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(totalPages)}
                        disabled={currentPage === totalPages || totalPages === 0}
                        className="h-8 w-8 p-0"
                      >
                        <span className="sr-only">Last Page</span>
                        <span>»</span>
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500 text-center py-4">No connected cases</div>
            )}
          </div>

          <div className="border-t p-4 flex justify-end space-x-2">
            <Button
              variant="outline"
              className="bg-white hover:bg-gray-50"
              onClick={() => setShowConnectedCases(false)}
            >
              CANCEL
            </Button>
            <Button
              variant="outline"
              className="text-blue-600 bg-white hover:bg-blue-50"
              onClick={handleExport}
              disabled={!providerCasesData || providerCasesData.cases.length === 0}
            >
              EXPORT
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

export function MedicalTreatmentDetails({ caseId }: MedicalTreatmentProps) {
  const { data: treatments = [] } = useTreatmentProvidersQuery(caseId);
  const deleteMutation = useDeleteTreatmentProviderMutation();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [allExpanded, setAllExpanded] = useState(true);
  const pageSize = 10; // Number of items per page

  // Use a single mutation hook instead of creating multiple ones
  const patchMutation = usePatchTreatmentProviderStatusMutation(caseId, selectedProviderId);

  // Create a memoized function to handle status updates
  const handleStatusUpdate = useCallback((providerId: string, type: "records_status" | "billing_status", status: RecordStatus) => {
    setSelectedProviderId(providerId);
    const currentDate = formatDateForApi(new Date());
    const currentUser = "Current User"; // TODO: Get actual current user

    interface UpdateData {
      records_status?: RecordStatus;
      billing_status?: RecordStatus;
      records_date?: string;
      billing_date?: string;
      records_requested_at?: string;
      billing_requested_at?: string;
      records_received_at?: string;
      billing_received_at?: string;
      records_requested_by?: string;
      billing_requested_by?: string;
      records_received_by?: string;
      billing_received_by?: string;
    }

    const updateData: UpdateData = {
      [type]: status,
      [`${type === "records_status" ? "records" : "billing"}_date`]: currentDate,
    };

    // Always update the requested fields for any status change to track who made the change
    updateData[`${type === "records_status" ? "records" : "billing"}_requested_at`] = currentDate;
    updateData[`${type === "records_status" ? "records" : "billing"}_requested_by`] = currentUser;

    // Add received fields only for RECEIVED status
    if (status === RecordStatus.RECEIVED) {
      updateData[`${type === "records_status" ? "records" : "billing"}_received_at`] = currentDate;
      updateData[`${type === "records_status" ? "records" : "billing"}_received_by`] = currentUser;
    }

    patchMutation.mutate(updateData);
  }, [patchMutation]);

  const handleDelete = async (providerId: string, e: MouseEvent) => {
    e.stopPropagation();
    setSelectedProviderId(providerId);
    setDeleteDialogOpen(true);
  };

  // Sort treatments by first_visit date and Sort treatments by original_bill in descending order
  const sortedTreatments = useMemo(() => {
    return [...treatments].sort((a, b) => {
      // if (!a.first_visit && !b.first_visit) return 0;
      // if (!a.first_visit) return 1;
      // if (!b.first_visit) return -1;
      // return new Date(b.first_visit).getTime() - new Date(a.first_visit).getTime();
      const billA = parseFloat(a.original_bill || "0");
      const billB = parseFloat(b.original_bill || "0");
      return billB - billA;
    });
  }, [treatments]);

  // Calculate total pages
  const totalPages = Math.ceil(sortedTreatments.length / pageSize);

  // Get current page items
  const currentTreatments = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedTreatments.slice(startIndex, startIndex + pageSize);
  }, [sortedTreatments, currentPage, pageSize]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalProviders = treatments.length;
    const originalBills = treatments.reduce(
      (sum, t) => sum + parseFloat(t.original_bill || "0"),
      0
    );
    const paidOwedBills = treatments.reduce(
      (sum, t) => sum + parseFloat(t.paid_plus_owed || "0"),
      0
    );
    const recsProgress =
      treatments.length > 0
        ? (treatments.filter((t) => t.records_status === RecordStatus.RECEIVED)
          .length /
          treatments.length) *
        100
        : 0;

    return {
      totalProviders,
      originalBills,
      paidOwedBills,
      recsProgress,
    };
  }, [treatments]);

  return (
    <div className="w-full mt-4 flex flex-col gap-8">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          Medical Treatment
        </h2>
        <div className="flex gap-3">
          <MedPayDepositList caseId={caseId} onSuccess={() => { }} />
          <SelectProver caseId={caseId} />
          <AddEditCost isEdit={false} onSuccess={() => { }} defaultContactType={ContactType.HEALTH_PROVIDERS} />
        </div>
      </div>
      {/* Statistics Section */}
      <div className="grid grid-cols-4 gap-4">
        <KPICard
          icon={Users}
          title="TOTAL PROVIDERS"
          metrics={{
            primary: {
              type: 'number',
              value: statistics.totalProviders
            }
          }}
        />

        <KPICard
          icon={DollarSign}
          title="ORIGINAL BILLS"
          metrics={{
            primary: {
              type: 'monetary',
              value: statistics.originalBills
            }
          }}
          isCurrency={true}
        />

        <KPICard
          icon={Wallet}
          title="PAID + OWED BILLS"
          metrics={{
            primary: {
              type: 'monetary',
              value: statistics.paidOwedBills
            }
          }}
          isCurrency={true}
        />

        <KPICard
          icon={FileSpreadsheet}
          title="RECS / BILLS IN"
          metrics={{
            primary: {
              type: 'percentage',
              value: parseFloat(statistics.recsProgress.toFixed(2))
            }
          }}
          showProgress={true}
          rate={{
            type: 'percentage',
            value: statistics.recsProgress
          }}
        />
      </div>

      {/* Expand/Collapse All Button with improved visibility */}
      {sortedTreatments.length > 0 && (
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Treatment Providers</h3>
          <Button
            variant="link"
            onClick={() => setAllExpanded(!allExpanded)}
            className="text-green-500"
          >
            {allExpanded ? (
              <>
                <ChevronUp className="w-4 h-4" />
                Collapse All
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                Expand All
              </>
            )}
          </Button>
        </div>
      )}

      <div className="flex flex-col gap-4">
        {currentTreatments.map((treatment, index) => (
          <Fragment key={typeof treatment.id === "number" ? treatment.id : treatment.id.id}>
            <TreatmentCard
              treatment={treatment}
              caseId={caseId}
              onDelete={handleDelete}
              onStatusUpdate={handleStatusUpdate}
              isExpanded={allExpanded}
            />
            {/* Add separator if it's not the last item */}
            {index < currentTreatments.length - 1 && (
              <Separator className="my-2" />
            )}
          </Fragment>
        ))}

        {sortedTreatments.length === 0 && (
          <div className="text-gray-500 text-center py-4">No treatments</div>
        )}

        {/* Add pagination if there are multiple pages */}
        {totalPages > 1 && (
          <div className="flex justify-end mt-4">
            <Pagination>
              <PaginationContent className="flex justify-end">
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(currentPage - 1)}
                    className={
                      currentPage === 1
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>

                {[...Array(totalPages)].map((_, index) => {
                  const page = index + 1;
                  if (
                    page === 1 ||
                    page === totalPages ||
                    (page >= currentPage - 1 && page <= currentPage + 1)
                  ) {
                    return (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => handlePageChange(page)}
                          isActive={page === currentPage}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  } else if (
                    page === currentPage - 2 ||
                    page === currentPage + 2
                  ) {
                    return (
                      <PaginationItem key={page}>
                        <PaginationEllipsis />
                      </PaginationItem>
                    );
                  }
                  return null;
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(currentPage + 1)}
                    className={
                      currentPage === totalPages
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        text="Treatment Provider"
        onConfirm={async () => {
          try {
            await deleteMutation.mutate({
              caseId,
              providerId: selectedProviderId,
            });
          } catch (error) {
            console.error("Failed to delete treatment provider:", error);
          }
          setDeleteDialogOpen(false);
        }}
      />
    </div>
  );
}