import React, { useState } from 'react';
import {
  Users,
  ExternalLink,
  Edit,
  Trash2
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  useCasePartiesQuery,
  useDeleteCasePartyMutation,
  useDeleteCasePartyContactMutation
} from "@/services/case-management/partyService";
import { CasePartyContact, PartyStatus, PartyType } from "@/type/case-management/partyTypes";
import { LawFirmResponse, AttorneyResponse } from "@/type/case-management/orgTypes";
import { Button } from "@/components/ui/button";
import AddEditCaseParty from "@/app/case/caseparty/components/AddEditCaseParty";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { AddressLink } from "@/components/gMap/address-link";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { ManageEmailTemplate } from "@/components/ManageEmailTemplate";
import { TemplateContextType } from '@/store/slices/templatesSlice';
import { InfoFieldGroup, type InfoFieldProps } from '@/components/ui/InfoField';

const getContextTypeAndId = (partyType: string, partyId: string) => {
  switch (partyType?.toLowerCase()) {
    case 'other':
      return { contextType: 'other_party' as TemplateContextType, other_party_id: partyId };
    case 'plaintiff':
      return { contextType: 'other_plaintiff' as TemplateContextType, other_plaintiff_id: partyId };
    case 'witness':
      return { contextType: 'witness' as TemplateContextType, witness_id: partyId };
    case 'expert_witness':
      return { contextType: 'expert_witness' as TemplateContextType, expert_witness_id: partyId };
    default:
      return { contextType: 'other_party' as TemplateContextType, other_party_id: partyId };
  }
};

export function OtherParties({ caseId }: { caseId: string }) {
  const router = useRouter();
  const { data: parties, isLoading } = useCasePartiesQuery(caseId);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedParty, setSelectedParty] = useState<{ id: string; contactId?: number } | null>(null);

  const deletePartyMutation = useDeleteCasePartyMutation(caseId, Number(selectedParty?.id));
  const deleteContactMutation = useDeleteCasePartyContactMutation(caseId, selectedParty?.contactId || 0);

  const handleDeleteParty = (partyId: string, contactId?: number) => {
    setSelectedParty({ id: partyId, contactId });
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedParty) {
      try {
        await deletePartyMutation.mutateAsync();
        if (selectedParty.contactId) {
          await deleteContactMutation.mutateAsync();
        }
        setDeleteDialogOpen(false);
        setSelectedParty(null);
      } catch (error) {
        console.error('Error deleting party:', error);
      }
    }
  };

  if (isLoading) {
    return <Skeleton className="w-full h-48" />;
  }

  const getStatusStyles = (status: PartyStatus) => {
    switch (status) {
      case PartyStatus.PENDING_CONTACT:
        return "bg-yellow-100 text-yellow-700";
      case PartyStatus.NOT_RELEVANT:
        return "bg-gray-100 text-gray-600";
      case PartyStatus.TRIAL_WITNESS:
        return "bg-green-100 text-green-700";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const formatStatus = (status: string) => {
    return status.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  return (
    <div className="w-full mt-4 space-y-6">
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        text="Party"
      />

      <div className="flex justify-between items-center">
        <div className="flex flex-row items-center gap-3">
          <Users className="h-5 w-5 text-[#060216]" />
          <h3 className="font-semibold text-base text-[#060216]">Other Parties</h3>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push(`/case/caseparty/${caseId}`)}
        >
          <ExternalLink className="h-4 w-4 text-gray-600" />
        </Button>
      </div>

      <div className="space-y-6">
        {parties?.map((party) => {
          const contact = party.contact as CasePartyContact;
          const lawFirm = party.law_firm as LawFirmResponse | undefined;
          const attorney = party.attorney as AttorneyResponse | undefined;

          return (
            <div
              key={party.id}
              className="bg-white p-6 rounded-lg border border-gray-100 space-y-6"
            >
              {/* Party Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-[#060216]" />
                  <div className="space-y-1">
                    <h4 className="font-semibold text-base text-[#060216]">
                      {contact ? `${contact.first_name} ${contact.last_name}` : 'Unnamed Contact'}
                    </h4>
                    <span className="text-xs font-medium px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                      {contact?.party_type || 'UNKNOWN'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={cn(
                    "px-2 py-0.5 rounded-full text-xs font-medium",
                    getStatusStyles(party.status)
                  )}>
                    {party.status ? formatStatus(party.status) : 'Unknown Status'}
                  </span>
                  <AddEditCaseParty
                    caseId={caseId}
                    isEdit={true}
                    defaultValues={{
                      status: party.status || PartyStatus.PENDING_CONTACT,
                      description: party.description || "",
                      party_contact: contact?.id?.toString(),
                      law_firm: lawFirm?.id?.toString(),
                      attorney: attorney?.id?.toString(),
                      party_type: contact?.party_type || PartyType.OTHER
                    }}
                    partyId={party.id.toString()}
                    trigger={
                      <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                    }
                  />
                  <button
                    onClick={() => handleDeleteParty(party.id.toString(), contact?.id)}
                    className="p-2 hover:bg-red-50 rounded-full transition-colors"
                    title="Delete party"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </button>
                  <ManageEmailTemplate
                    caseId={caseId}
                    {...getContextTypeAndId(contact?.party_type, party.id.toString())}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <InfoFieldGroup
                fields={[
                  contact?.phone && {
                    label: "Phone",
                    value: contact.phone,
                    isPhone: true,
                  },
                  lawFirm?.office_name && {
                    label: "Law Firm",
                    value: lawFirm.office_name
                  },
                  contact?.street1 && {
                    label: (
                      <AddressLink
                        address={{
                          street1: contact.street1 || undefined,
                          street2: contact.street2 || undefined,
                          city: contact.city || undefined,
                          state: contact.state || undefined,
                          zip_code: contact.zip_code || undefined
                        }}
                        label="Address"
                      />
                    ),
                    value: (
                      <p>
                        {contact.street1}
                        {contact.street2 && `, ${contact.street2}`}
                        {contact.city && `, ${contact.city}`}
                        {contact.state && `, ${contact.state}`}
                        {contact.zip_code && ` ${contact.zip_code}`}
                      </p>
                    )
                  },
                  contact?.email && {
                    label: "Email",
                    value: (
                      <a href={`mailto:${contact.email}`} className="text-[#060216] hover:text-[#060216]-600">
                        {contact.email}
                      </a>
                    )
                  },
                  attorney && {
                    label: "Attorney",
                    value: (
                      <>
                        <p className="text-[#060216]">{`${attorney.first_name} ${attorney.last_name}`}</p>
                        {(attorney.phone || attorney.email) && (
                          <p className="text-[#060216]">
                            {attorney.phone && attorney.phone}
                            {attorney.phone && attorney.email && ' • '}
                            {attorney.email && attorney.email}
                          </p>
                        )}
                      </>
                    )
                  }
                ].filter(Boolean) as InfoFieldProps[]}
                columns={2}
                gap="gap-x-12 gap-y-6"
              />
            </div>
          );
        })}

        {!parties?.length && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-gray-400" />
              <p className="text-sm text-gray-500">No parties found.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 