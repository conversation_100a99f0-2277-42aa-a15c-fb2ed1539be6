import React, { useState, useRef, useEffect } from 'react';
import { useIncidentDetailsQuery, useUpdateIncidentDetailsMutation } from '@/services/incidentService';
import { Button } from "@/components/ui/button";
import { Edit, FileText, Upload } from 'lucide-react';
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// import IncidentSummary from '@/app/leads/components/leadview/Incident-detail/IncidentSummary';
// import StatusLimitation from '@/app/leads/components/leadview/Incident-detail/StatusLimitation';
// import IncidentInformation from '@/app/leads/components/leadview/Incident-detail/IncidentInformation';
// import InjuryAssessment from '@/app/leads/components/leadview/Incident-detail/InjuryAssessment';
import EditIncidentDetailsDialog from '@/components/CaseOverview/components/EditIncidentDetailsDialog';
import { useQueryClient } from '@tanstack/react-query';
import RichTextViewer from '@/components/ui/RichTextViewer';
import { CaseSection, useUploadBySectionMutation } from "@/services/case-management/newDocumentManagement";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { InfoFieldGroup } from '@/components/ui/InfoField';
import { formatDateForDisplay, formatDateForApi } from '@/utils/dateUtils';
import { DocRequestStatus } from '@/type/incidentTypes';

interface requestedby {
  id: number;
  name: string;
  email: string;
}

// interface DocumentRequestProps {
//   status?: DocRequestStatus;
//   notes?: string;
//   isEditableComponent: boolean;
// }

// function DocumentRequest({ status, notes, isEditableComponent }: DocumentRequestProps) {
//   const [isEditing, setIsEditing] = useState(false);

//   if (isEditing) {
//     // TODO: Add DocumentRequestForm component when needed
//     return null;
//   }

//   return (
//     <div className="w-full space-y-5">
//       <div className="flex items-center gap-1.5">
//         <FileTextIcon className="w-4 h-4" />
//         <h2 className="text-[14px] font-medium leading-5">Document Request</h2>
//       </div>

//       <InfoFieldGroup
//         fields={[
//           {
//             icon: FileTextIcon,
//             label: "Witness Information",
//             value: status ? status.split('_').join(' ') : 'Not specified'
//           },
//           {
//             icon: FileTextIcon,
//             label: "Notes",
//             value: notes ? <RichTextViewer data={notes} /> : 'No notes available'
//           }
//         ]}
//         columns={1}
//         className="gap-y-4"
//       />

//       {isEditableComponent && (
//         <Button
//           variant="ghost"
//           size="icon"
//           onClick={() => setIsEditing(true)}
//           className="hover:bg-green-50 hover:text-green-600"
//         >
//           <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
//         </Button>
//       )}
//     </div>
//   );
// }

// Police Report Status options and mapping
const POLICE_REPORT_STATUS_OPTIONS = [
  { value: DocRequestStatus.RECEIVED, label: 'Received' },
  { value: DocRequestStatus.REQUESTED, label: 'Requested' },
  { value: DocRequestStatus.DOES_NOT_EXIST, label: 'Does Not Exist' },
  { value: DocRequestStatus.NOT_YET_REQUESTED, label: 'Not Yet Requested' }
];

const docRequestStatusToDisplay = (status?: DocRequestStatus) => {
  const found = POLICE_REPORT_STATUS_OPTIONS.find(opt => opt.value === status);
  return found ? found.label : POLICE_REPORT_STATUS_OPTIONS[3].label;
};

const displayToDocRequestStatus = (label: string): DocRequestStatus => {
  const found = POLICE_REPORT_STATUS_OPTIONS.find(opt => opt.label === label);
  return found ? found.value : DocRequestStatus.NOT_YET_REQUESTED;
};

const formatStatusDetails = ({ status, requestedAt, requestedBy, receivedAt, receivedBy }: {
  status: DocRequestStatus | undefined,
  requestedAt?: string,
  requestedBy?: requestedby,
  receivedAt?: string,
  receivedBy?: requestedby
}) => {
  if (!status) return null;


  if (status === DocRequestStatus.RECEIVED && receivedAt) {
    return `Received by ${receivedBy?.name} on ${formatDateForDisplay(receivedAt)} `;
  } else if (status === DocRequestStatus.REQUESTED && requestedAt && requestedBy) {
    return `Requested by ${requestedBy?.name} on ${formatDateForDisplay(requestedAt)} `;
  } else {
    return '';
  }
  return null;
};

const getStatusVariant = (status: DocRequestStatus | undefined): "success" | "warning" | "secondary" => {
  if (status === DocRequestStatus.RECEIVED) return "success";
  if (status === DocRequestStatus.REQUESTED) return "warning";
  return "secondary";
};

export function IncidentDetails({ caseId, isEdit = false }: { caseId: string, isEdit?: boolean }) {
  const { data: incidentDetails, isLoading } = useIncidentDetailsQuery(caseId);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(isEdit);
  const uploadBySection = useUploadBySectionMutation();
  const updateIncidentDetails = useUpdateIncidentDetailsMutation(caseId);
  const queryClient = useQueryClient();
  const fileInputRefs = useRef<{ [key in CaseSection]?: HTMLInputElement }>({});

  // Use doc_request_status for initial value
  const [policeReportStatus, setPoliceReportStatus] = useState<string>(
    docRequestStatusToDisplay(incidentDetails?.doc_request_status)
  );

  useEffect(() => {
    setIsEditDialogOpen(isEdit);
  }, [isEdit]);

  // Sync policeReportStatus with incidentDetails changes
  useEffect(() => {
    setPoliceReportStatus(docRequestStatusToDisplay(incidentDetails?.doc_request_status));
  }, [incidentDetails]);

  const handleStatusChange = async (newStatus: string) => {
    try {
      const docRequestStatus = displayToDocRequestStatus(newStatus);
      const currentDate = formatDateForApi(new Date());

      interface UpdateData {
        doc_request_status?: DocRequestStatus;
        requested_at?: string;
        requested_by?: string;
        received_at?: string;
        received_by?: string;
      }

      const updateData: UpdateData = {
        doc_request_status: docRequestStatus,
      };

      // Add received fields only for RECEIVED status
      if (docRequestStatus === DocRequestStatus.RECEIVED) {
        updateData.received_at = currentDate;
      }

      if (docRequestStatus === DocRequestStatus.REQUESTED) {
        updateData.requested_at = currentDate;
      }

      await updateIncidentDetails.mutateAsync(updateData);
      setPoliceReportStatus(newStatus);
    } catch (error) {
      console.error("Failed to update status:", error);
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update police report status",
        variant: "destructive"
      });
      // Revert to previous status on error
      setPoliceReportStatus(docRequestStatusToDisplay(incidentDetails?.doc_request_status));
    }
  };

  const handleFileUpload = async (section: CaseSection, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      await uploadBySection.mutateAsync({
        case_id: caseId,
        section: section,
        file: file
      });

      // Clear the file input
      if (event.target) {
        event.target.value = '';
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['incidentDetails', caseId] });
    } catch (error) {
      console.error("Upload error:", error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload file",
        variant: "destructive"
      });
    }
  };

  const handleUploadClick = (section: CaseSection) => {
    fileInputRefs.current[section]?.click();
  };

  if (isLoading) return <div className="text-[#060216]">Loading...</div>;
  if (!incidentDetails) return <div className="text-[#060216]">No incident details found</div>;

  const affectedAreas = incidentDetails.affected_areas || {};

  return (
    <div className="w-full">
      <div className="flex flex-col mb-6">
        <div className="flex justify-between items-start">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-4">
              <h2 className="text-lg font-semibold">Incident Information</h2>
              <div className="flex flex-col">
                {/* Police Report Status Pill + Dropdown */}
                <DropdownMenu>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex flex-col">
                          <DropdownMenuTrigger asChild>
                            <Badge
                              variant={getStatusVariant(incidentDetails.doc_request_status)}
                              className="cursor-pointer hover:opacity-80 transition-opacity text-xs"
                            >
                              Police Report {policeReportStatus}
                            </Badge>
                          </DropdownMenuTrigger>
                          {formatStatusDetails({
                            status: incidentDetails.doc_request_status,
                            requestedAt: incidentDetails.requested_at,
                            requestedBy: incidentDetails.requested_by,
                            receivedAt: incidentDetails.received_at,
                            receivedBy: incidentDetails.received_by
                          }) && (
                            <span className="text-xs text-gray-500 mt-1">
                              {formatStatusDetails({
                                status: incidentDetails.doc_request_status,
                                requestedAt: incidentDetails.requested_at,
                                requestedBy: incidentDetails.requested_by,
                                receivedAt: incidentDetails.received_at,
                                receivedBy: incidentDetails.received_by
                              })}
                            </span>
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="space-y-1">
                          <p className="font-semibold">Police Report Status</p>
                          <p className="text-sm">
                            {incidentDetails.doc_request_status === DocRequestStatus.RECEIVED && "Police report has been received"}
                            {incidentDetails.doc_request_status === DocRequestStatus.REQUESTED && "Police report has been requested but not yet received"}
                            {incidentDetails.doc_request_status === DocRequestStatus.DOES_NOT_EXIST && "Police report does not exist"}
                            {incidentDetails.doc_request_status === DocRequestStatus.NOT_YET_REQUESTED && "Police report has not been requested yet"}
                          </p>
                          <p className="text-xs text-gray-500">Click to change status</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <DropdownMenuContent>
                    {POLICE_REPORT_STATUS_OPTIONS.map((option) => (
                      <DropdownMenuItem
                        key={option.value}
                        onClick={() => handleStatusChange(option.label)}
                      >
                        Police Report {option.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="hover:text-green-600"
              onClick={() => setIsEditDialogOpen(true)}
            >
              <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
            </Button>

            <input
              type="file"
              ref={(el) => {
                if (el) {
                  fileInputRefs.current[CaseSection.POLICE_REPORT] = el;
                }
              }}
              onChange={(e) => handleFileUpload(CaseSection.POLICE_REPORT, e)}
              className="hidden"
              accept=".pdf,.doc,.docx"
              disabled={uploadBySection.isPending}
            />
            <input
              type="file"
              ref={(el) => {
                if (el) {
                  fileInputRefs.current[CaseSection.ACCIDENT_PHOTOS] = el;
                }
              }}
              onChange={(e) => handleFileUpload(CaseSection.ACCIDENT_PHOTOS, e)}
              className="hidden"
              accept=".jpg,.jpeg,.png"
              disabled={uploadBySection.isPending}
            />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="link"
                        className="text-green-600"
                        disabled={uploadBySection.isPending}
                      >
                        <FileText className="h-4 w-4 text-green-600" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="shadow-lg rounded-lg border border-zinc-200"
                    >
                      <DropdownMenuItem
                        className="hover:bg-emerald-50 cursor-pointer py-2 text-emerald-600"
                        onClick={() => handleUploadClick(CaseSection.POLICE_REPORT)}
                        disabled={uploadBySection.isPending}
                      >
                        {uploadBySection.isPending ? (
                          <>
                            <span className="loading loading-spinner loading-xs mr-2"></span>
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="mr-2 h-4 w-4 stroke-[1.5]" />
                            <span>Upload Police Report</span>
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="hover:bg-emerald-50 cursor-pointer py-2 text-emerald-600"
                        onClick={() => handleUploadClick(CaseSection.ACCIDENT_PHOTOS)}
                        disabled={uploadBySection.isPending}
                      >
                        {uploadBySection.isPending ? (
                          <>
                            <span className="loading loading-spinner loading-xs mr-2"></span>
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="mr-2 h-4 w-4 stroke-[1.5]" />
                            <span>Upload Accident Photos</span>
                          </>
                        )}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Upload and manage incident documents</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-8">
        <InfoFieldGroup
          fields={[
            {
              label: "Date of Loss",
              value: incidentDetails.incident_date || '',
              isDate: true
            },
            {
              label: "Incident Type",
              value: incidentDetails.incident_type?.replace("_", " ") || ''
            },
            {
              label: "Location",
              value: incidentDetails.incident_location || ''
            },
            {
              label: "Insurance Status",
              value: !!incidentDetails.insurance_status
            },
            {
              label: "Previous Attorney",
              value: incidentDetails.previous_attorney || ''
            },
            {
              label: "Report Number",
              value: incidentDetails.report_number || '',
              isNumber: true
            },
            // {
            //   label: "Police Report Request Status",
            //   value: incidentDetails.doc_request_status?.replace(/_/g, " ") || ''
            // },
            {
              label: "Street 1",
              value: incidentDetails.street1 || ''
            },
            {
              label: "Street 2",
              value: incidentDetails.street2 || ''
            },
            {
              label: "City",
              value: incidentDetails.city || ''
            },
            {
              label: "State",
              value: incidentDetails.state || ''
            },
            {
              label: "ZIP Code",
              value: incidentDetails.zip_code || ''
            }
          ]}
        />
        <InfoFieldGroup
          fields={[
            {
              label: "Estimated Value",
              value: incidentDetails.estimated_value || '',
              isCurrency: true
            },
            {
              label: "Conflicts Checked",
              value: !!incidentDetails.conflicts_checked
            },
            {
              label: "Statute of Limitations",
              value: incidentDetails.statute_of_limitations || '',
              isDate: true
            }

          ]}
        />

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mt-4">
          <div className="bg-[#F7F7F7] border border-black/[0.08] rounded-lg p-4">
            <div className="space-y-0.5">
              <p className="text-[#060216] opacity-50 text-sm font-medium">Description</p>
              {incidentDetails.incident_description ? (
                <RichTextViewer
                  data={incidentDetails.incident_description}
                  className="text-[#060216] text-sm leading-5 font-semibold"
                />
              ) : (
                <p className="text-[#060216] text-sm leading-5 font-semibold">—</p>
              )}
            </div>
          </div>

          <div className="bg-[#F7F7F7] border border-black/[0.08] rounded-lg p-4">
            <div className="space-y-0.5">
              <p className="text-[#060216] opacity-50 text-sm font-medium">Police Report Details</p>
              {incidentDetails.police_report_details ? (
                <RichTextViewer
                  data={incidentDetails.police_report_details}
                  className="text-[#060216] text-sm leading-5 font-semibold"
                />
              ) : (
                <p className="text-[#060216] text-sm leading-5 font-semibold">—</p>
              )}
            </div>
          </div>
        </div>

        <Separator className="w-full" />

        <InfoFieldGroup
          fields={[
            {
              label: "Affected Areas",
              value: Object.entries(affectedAreas).map(([key, value]) => (
                <div key={key} className="flex items-center gap-2">
                  <span>{key}: </span>
                  <span>{value}</span>
                </div>
              ))
            },
            {
              label: "Critical Conditions",
              value: incidentDetails.critical_conditions || ''
            },
            {
              label: "Surgery Required",
              value: !!incidentDetails.surgery_required
            },
            {
              label: "Treatment Status",
              value: incidentDetails.medical_status || ''
            }
          ]}
          columns={4}
          className="gap-y-4"
        />
      </div>

      <EditIncidentDetailsDialog
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false);
          // Refetch incident details when modal closes (after update or cancel)
          queryClient.invalidateQueries({ queryKey: ['incidentDetails', caseId] });
        }}
        incidentDetails={incidentDetails}
        caseId={caseId}
        isEdit={true}
      />
    </div>
  );
}

