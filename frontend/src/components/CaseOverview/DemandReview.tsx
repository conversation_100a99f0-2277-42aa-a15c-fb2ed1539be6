import Damages from "@/app/dashboard/demand-generator/[caseId]/components/reasoningAI/Damages";
import DemandToSettle from "@/app/dashboard/demand-generator/[caseId]/components/reasoningAI/DemandToSettle";
import FactLiability from "@/app/dashboard/demand-generator/[caseId]/components/reasoningAI/FactLiability";
import InjuriesTreatments from "@/app/dashboard/demand-generator/[caseId]/components/reasoningAI/InjuriesTreatments";
import Introduction from "@/app/dashboard/demand-generator/[caseId]/components/reasoningAI/Introduction";
import CaseDetails from "@/app/dashboard/demand-generator/[caseId]/components/reasoningAI/CaseDetails";

interface DemandReviewProps {
    isFullscreen: boolean;
}

export function DemandReview({ isFullscreen = false }: DemandReviewProps) {
    
    
    return (
        <div className={`p-4 bg-white rounded-lg overflow-y-auto ${isFullscreen ? 'h-[calc(100vh-85px)]' : 'h-[calc(100vh-365px)]'}`}>
            <div className="mb-4">
                <CaseDetails />
            </div>
            <div className="mb-4">
                <Introduction />
            </div>
            <div className="mb-4">
                <FactLiability />
            </div>
            <div className="mb-4">
                <InjuriesTreatments />
            </div>
            <div className="border border-solid border-[#01010114] rounded-[10px] p-4 mb-4">
                <h2 className="text-xl font-semibold mb-4">Damages</h2>
                <div className="m-4">
                    <Damages />
                </div>
            </div>
            <div className="mb-4">
                <DemandToSettle />
            </div>
        </div>
    );
} 