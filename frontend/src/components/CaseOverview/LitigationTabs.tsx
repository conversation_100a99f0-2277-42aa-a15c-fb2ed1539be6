import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { LitigationContent } from "@/app/case/litigation/components/LitigationContent";
import DiscoveryContent from "@/app/case/discovery/components/DiscoveryContent";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect } from "react";

export function LitigationTabs({ caseId }: { caseId: string }) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const initialTab = searchParams?.get("litigationTab") || "litigation";

  const handleTabChange = (value: string) => {
    if (!searchParams) return;
    
    const newParams = new URLSearchParams(searchParams.toString());
    // Only preserve mainTab and litigationTab
    const mainTab = searchParams.get("mainTab");
    newParams.delete("overviewTab");
    newParams.delete("preLitigationTab");
    newParams.delete("settlementTab");
    if (mainTab) newParams.set("mainTab", mainTab);
    newParams.set("litigationTab", value);
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });
  };

  // Effect to expand all discoveries when discovery tab is active
  useEffect(() => {
    if (initialTab === "discovery") {
      // The DiscoveryContent component will handle the expansion internally
      console.log("Discovery tab is active");
    }
  }, [initialTab]);

  return (
    <div className="w-full">
      <Tabs 
        defaultValue={initialTab} 
        className="w-full"
        onValueChange={handleTabChange}
      >
        <TabsList className="mb-2">
          <TabsTrigger value="litigation">Litigation</TabsTrigger>
          <TabsTrigger value="discovery">Discovery</TabsTrigger>
          {/* <TabsTrigger value="negotiation">Negotiation</TabsTrigger>
          <TabsTrigger value="litigation-cost">Litigation Cost</TabsTrigger> */}
        </TabsList>
        <div className="overflow-y-auto max-h-[calc(80vh-30px)] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <TabsContent value="litigation">
            <LitigationContent caseId={caseId} />
          </TabsContent>
          <TabsContent value="discovery">
            <DiscoveryContent caseId={caseId} />
          </TabsContent>
          {/* <TabsContent value="negotiation">
            <Negotiation caseId={caseId} />
          </TabsContent>
          <TabsContent value="litigation-cost">
            <CaseCosts caseId={caseId} costFor={CostFor.LITIGATION_COST} />
          </TabsContent> */}
        </div>
      </Tabs>
    </div>
  );
}
