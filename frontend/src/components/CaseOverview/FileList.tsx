import { Checkbox } from "@/components/ui/checkbox"
import { Card } from "@/components/ui/card"

interface FileListProps {
    files: Array<{ file_name: string }>;
    selectedFiles: string[];
    setSelectedFiles: (files: string[]) => void;
}

export function FileList({ files, selectedFiles, setSelectedFiles }: FileListProps) {
    return (
        <Card className="p-4">
            <div className="space-y-2 max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {files.map((file, index) => (
                    <div 
                        key={index}
                        className="flex items-center p-3 border rounded-md hover:bg-accent transition-colors"
                    >
                        <Checkbox
                            id={`file-${index}`}
                            className="mr-3 h-4 w-4"
                            checked={selectedFiles.includes(file.file_name)}
                            onCheckedChange={(checked) => {
                                if (checked) {
                                    setSelectedFiles([...selectedFiles, file.file_name]);
                                } else {
                                    setSelectedFiles(selectedFiles.filter(name => name !== file.file_name));
                                }
                            }}
                        />
                        <label 
                            htmlFor={`file-${index}`}
                            className="flex-1 text-sm cursor-pointer"
                        >
                            {file.file_name}
                        </label>
                    </div>
                ))}
            </div>
        </Card>
    );
} 