import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useSettlementAdvancesQuery, useSettlementCalculationQuery, useUpdateSettlementCalculationMutation } from '@/services/case-management/settlementService';
import { useTreatmentProvidersQuery } from "@/services/case-management/medicalTreatmentService"
import { useAttorneyLiensQuery, useMiscLiensQuery } from "@/services/case-management/lienService"
import { useHealthInsurancesQuery } from "@/services/case-management/healthInsuranceService"
import { useClientBasicDetailsQuery } from '@/services/case-management/clientDetailService';
import { useIncidentDetailsQuery } from '@/services/incidentService';
import { notFound } from 'next/navigation';


export function Settlement() {
  const router = useRouter();
  const params = useParams();
  const caseId = params?.caseId as string;

  // Redirect to 404 if no caseId is present
  if (!caseId) {
    notFound();
  }

  // Add incident details query
  const { data: incidentDetails, isLoading: isIncidentLoading } = useIncidentDetailsQuery(caseId);
  const { data: clientDetails, isLoading: isClientLoading } = useClientBasicDetailsQuery(caseId);
  const { data: calculations, isLoading: isCalculationsLoading } = useSettlementCalculationQuery(caseId);
  const updateCalculation = useUpdateSettlementCalculationMutation(caseId);
  const { data: treatmentProviders } = useTreatmentProvidersQuery(caseId);
  const { data: attorneyLiensData } = useAttorneyLiensQuery(caseId);
  const { data: healthInsurances } = useHealthInsurancesQuery(caseId);
  const { data: miscLiensData } = useMiscLiensQuery(caseId);
  const { data: caseCosts } = useSettlementAdvancesQuery(caseId);
  // Update the save handler
  const handleSave = async () => {
    if (!calculations) return;
  };

  // Update loading check to include incident details
  if (isCalculationsLoading || isClientLoading || isIncidentLoading || !calculations) {
    return <div>Loading...</div>;
  }

  const calculateNetToClient = () => {
    if (!calculations) return "0.00";

    const total = Number(calculations.medpay_pip_fees || 0) -
      Number(calculations.settlement_advance_loan || 0) -
      Number(calculations.attorney_liens || 0) -
      Number(calculations.miscellaneous_liens || 0);
    return total.toFixed(2);
  }

  const Section = ({ title, amount, isNegative = false, children }: {
    title: string
    amount: number
    isNegative?: boolean
    children?: React.ReactNode
  }) => (
    <div className="bg-gray-100 rounded-md mb-4">
      <div className="flex items-center justify-between p-3 bg-gray-200">
        <span className="font-medium">{title}</span>
        <div className="flex items-center gap-2">
          {isNegative && <span>-</span>}
          <span>$</span>
          <Input
            value={typeof amount === 'number' ? amount.toFixed(2) : "0.00"}
            className="w-[200px]"
            onChange={(e) => {
              if (!calculations) return;
              const value = parseFloat(e.target.value) || 0;
              updateCalculation.mutate({
                ...calculations,
                [title.toLowerCase().replace(/\s/g, '_')]: value
              });
            }}
          />
        </div>
      </div>
      {children && <div className="p-3">{children}</div>}
    </div>
  )

  const LineItem = ({ label, amount }: {
    label: string
    amount: number
  }) => {
    if (!calculations) return null;
    return (
      <div className="flex items-center justify-between py-2">
        <span>{label}</span>
        <div className="flex items-center gap-2">
        <span>$</span>
          <Input
            value={new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: "USD",
              maximumFractionDigits: 2,
            }).format(amount)}
            className="w-[150px]"
            readOnly
          />
        </div>
      </div>
    )
  }

  return (
    <Card className="p-6">
      {/* Add client and incident details */}
      <div className="mb-4">
        {clientDetails && (
          <div className="text-lg">
            Client: {clientDetails.first_name} {clientDetails.last_name}
          </div>
        )}
        {incidentDetails && (
          <div className="text-sm text-gray-600 mt-1">
            DIL: {incidentDetails.incident_date ? new Date(incidentDetails.incident_date).toLocaleDateString() : 'N/A'}
            {incidentDetails.incident_location && (
              <span className="ml-2">
                Location: {incidentDetails.incident_location}
              </span>
            )}
          </div>
        )}
      </div>
      
      <div className="text-right text-xl font-bold mb-6 flex justify-end items-center gap-2">
        <span>NET TO CLIENT ${calculateNetToClient()}</span>
        <Button
          variant="ghost"
          className="p-2"
          onClick={() => router.push(`/case/settlement/${caseId}`)}
        >
          <ExternalLink className="h-6 w-6" />
        </Button>
      </div>

      {calculations && (
        <>
          <Section title="Settlement Proceeds" amount={Number(calculations.settlement_proceed) || 0} />
          <Section title="Settlement Fees" amount={Number(calculations.settlement_fees) || 0} isNegative />
          <Section title="Medpay / PIP Fees" amount={Number(calculations.medpay_pip_fees) || 0} isNegative />

          <Section title="Settlement Advance Loan" amount={Number(calculations.settlement_advance_loan) || 0} isNegative>
            {caseCosts?.map((item, i) => (
              <LineItem
                key={i}
                label={item.company.company || ''}
                amount={Number(item.total_owed) || 0}
              />
            ))}
          </Section>

          <Section title="Medical Bills" amount={Number(calculations.medical_bills) || 0} isNegative>
            {treatmentProviders?.map((item, i) => (
              <LineItem
                key={i}
                label={item.medical_provider?.company}
                amount={parseFloat(item.original_bill || "0")}
              />
            ))}
          </Section>

          <Section title="Health Insurance Liens" amount={Number(calculations.health_insurance_liens) || 0} isNegative>
            {healthInsurances?.map((item, i) => (
              <LineItem
                key={i}
                label={item.insurance_company_details?.name || ''}
                amount={Number(item.total_lien) || 0}
              />
            ))}
          </Section>

          <Section title="Attorney Liens" amount={Number(calculations.attorney_liens) || 0} isNegative>
            {attorneyLiensData?.map((item, i) => (
              <LineItem
                key={i}
                label={item.attorney?.first_name + ' ' + item.attorney?.last_name || ''}
                amount={Number(item.fee_amount) || 0}
              />
            ))}
          </Section>

          <Section title="Miscellaneous Liens" amount={Number(calculations.miscellaneous_liens) || 0} isNegative>
            {miscLiensData?.map((item, i) => (
              <LineItem
                key={i}
                label={item.lien_holder?.company || ''}
                amount={Number(item.lien_amount) || 0}
              />
            ))}
          </Section>
        </>
      )}

      <Button
        className="mt-6"
        variant="default"
        onClick={handleSave}
        disabled={!calculations}
      >
        SAVE
      </Button>
    </Card>
  )
}
