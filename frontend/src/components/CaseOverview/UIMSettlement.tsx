import { useRouter, use<PERSON>ara<PERSON> } from "next/navigation";
import { useState, useEffect, useMemo, useCallback } from "react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { MoreVertical, Plus, Download, Edit, Save, X, Loader2, GripVertical } from "lucide-react";
import { useClientBasicDetailsQuery } from "@/services/case-management/clientDetailService";
import { useIncidentDetailsQuery } from "@/services/incidentService";
import { useCurrentOrganization } from "@/services/organizationService";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { CurrencyDisplay } from "../ui/currency-display";
import { useClientTrustQuery, useCreateClientTrustMutation } from "@/services/case-management/clientTrustService";
import { useQueryClient } from '@tanstack/react-query';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import * as z from "zod";
import { ClientTrustEntryType, DepositType } from "@/type/case-management/clientTrustTypes";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { useUpdateTreatmentProviderMutation } from "@/services/case-management/medicalTreatmentService";
import { TreatmentStatus } from "@/type/case-management/medicalTreatmentTypes";
import { Button } from "@/components/ui/button";
import { useSettlementMemoQuery } from "@/services/case-management/settlementMemoService";
import { useSettlementCalculationQuery, useUpdateSettlementCalculationMutation } from "@/services/case-management/settlementService";
import { parseISO, format } from "date-fns";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { generateDocument } from "@/utils/documentUtils";
import { CaseSection, useUploadBySectionMutation } from "@/services/case-management/newDocumentManagement";
import { useCreateManualSettlementEntryMutation, useDeleteManualSettlementEntryMutation } from "@/services/case-management/settlementService";
import { toast } from "@/hooks/use-toast";
import { FinalMemoPreviewModal } from "./components/FinalMemoPreviewModal";
import { formatDateForApi } from "@/utils/dateUtils";

// Types
interface SettlementItem {
    id: string | number | null;
    description: string;
    amount: string | number;
    isManualEntry?: boolean;
    entryId?: number;
    clientResponsibility?: boolean;
}

interface ManualEntryForm {
    description: string;
    amount: string;
}

interface FooterText {
    content: string;
}

interface ExtendedClientTrust {
    id: number;
    deposit_type: DepositType;
    client_trust_entry_type: ClientTrustEntryType;
    memo?: string;
    amount: string;
    void: boolean;
    client_responsibility?: boolean;
}

// Form schema
const trustEntrySchema = z.object({
    check_number: z.string(),
    amount: z.string(),
    memo: z.string(),
    deposit_date: z.string(),
    client_trust_entry_type: z.nativeEnum(ClientTrustEntryType),
    issuer_payee: z.string(),
    medical_provider_id: z.number().nullable().optional(),
    void: z.boolean().optional()
});

type TrustEntryFormData = z.infer<typeof trustEntrySchema>;

// Update the FeePercentageSelect component
const FeePercentageSelect = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  const PREDEFINED_VALUES = ["33.330000", "40.000000", "42.000000"];
  const [isCustom, setIsCustom] = useState(() => !PREDEFINED_VALUES.includes(value));
  const [localValue, setLocalValue] = useState(() => {
    if (!PREDEFINED_VALUES.includes(value)) {
      try {
        return parseFloat(value).toFixed(2);
      } catch {
        return "";
      }
    }
    return "";
  });

  const handleCustomValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (/^\d*\.?\d*$/.test(newValue)) {
      const formattedValue = newValue.includes(".")
        ? newValue
          .split(".")
          .map((part, index) => (index === 1 ? part.slice(0, 2) : part))
          .join(".")
        : newValue;

      setLocalValue(formattedValue);

      if (formattedValue && !isNaN(parseFloat(formattedValue))) {
        const valueWithTwoDecimals = parseFloat(formattedValue).toFixed(2);
        onChange(parseFloat(valueWithTwoDecimals).toFixed(6));
      }
    }
  };

  const handleCancel = () => {
    setIsCustom(false);
    setLocalValue("");
    onChange("33.330000");
  };

  if (isCustom) {
    return (
      <div className="flex flex-wrap items-center gap-2">
        <div className="relative min-w-[120px] max-w-[150px] flex-1">
          <Input
            type="text"
            value={localValue}
            onChange={handleCustomValueChange}
            className="pr-8 w-full"
            placeholder="Enter"
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
            %
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="shrink-0"
        >
          Cancel
        </Button>
      </div>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={(val) => {
        if (val === "custom") {
          setIsCustom(true);
          setLocalValue("");
        } else {
          setIsCustom(false);
          setLocalValue("");
          onChange(val);
        }
      }}
    >
      <SelectTrigger className="min-w-[120px] max-w-[150px]">
        <SelectValue placeholder="Fee %" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="33.330000">33.33 %</SelectItem>
        <SelectItem value="40.000000">40.00 %</SelectItem>
        <SelectItem value="42.000000">42.00 %</SelectItem>
        <SelectItem value="custom">Custom %</SelectItem>
      </SelectContent>
    </Select>
  );
};

// Add SortableItem component
const SortableItem = ({
  id,
  description,
  amount,
  onCreateTrustEntry,
  isManualEntry = false,
  entryId,
  onDelete,
  isDeleting = false,
  clientResponsibility = false,
  onClientResponsibilityChange,
  onDeleteClick
}: {
  id: string;
  description: string;
  amount: string | number;
  onCreateTrustEntry: (description: string, amount: number) => void;
  isManualEntry?: boolean;
  entryId?: number;
  onDelete?: (entryId: number) => void;
  isDeleting?: boolean;
  clientResponsibility?: boolean;
  onClientResponsibilityChange?: (entryId: number, currentStatus: boolean) => void;
  onDeleteClick: (entryId: number, description: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleCreateCheck = () => {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    onCreateTrustEntry(description, Math.abs(numericAmount));
  };

  const handleDeleteClick = () => {
    if (typeof entryId === 'number') {
      onDeleteClick(entryId, description);
    }
  };

  return (
    <div ref={setNodeRef} style={style} className="flex items-center gap-2 py-1">
      <button {...attributes} {...listeners} className="cursor-grab hover:text-blue-500 focus:outline-none">
        <GripVertical className="h-4 w-4" />
      </button>
      <div className="flex justify-between items-center w-full">
        <span>{description}</span>
        <div className="flex items-center gap-2">
          <span>
            <CurrencyDisplay
              amount={amount}
              negative={true}
              showZeroAsCurrency={true}
            />
          </span>
          {clientResponsibility && (
            <span className="text-xs px-2 py-1 bg-green-50 text-green-700 rounded">
              Client Responsibility
            </span>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleCreateCheck}>
                Settlement Check
              </DropdownMenuItem>
              {onClientResponsibilityChange && entryId !== undefined && (
                <DropdownMenuItem
                  onClick={() => {
                    onClientResponsibilityChange(entryId, clientResponsibility);
                  }}
                  className={clientResponsibility ? "text-green-600 focus:text-green-600" : ""}
                >
                  {clientResponsibility ? "Mark as Not Client Responsibility" : "Mark as Client Responsibility"}
                </DropdownMenuItem>
              )}
              {isManualEntry && (
                <DropdownMenuItem
                  onClick={handleDeleteClick}
                  className="text-red-600 focus:text-red-600"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    "Delete"
                  )}
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

// Add TrustEntryDialog component
interface TrustEntryDialogProps {
    isOpen: boolean;
    form: ReturnType<typeof useForm<TrustEntryFormData>>;
    onClose: () => void;
    onSubmit: (values: TrustEntryFormData) => Promise<void>;
}

const TrustEntryDialog = ({ isOpen, form, onClose, onSubmit }: TrustEntryDialogProps) => {
    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Create UIM Trust Entry</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="check_number"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Check Number</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Amount</FormLabel>
                                    <FormControl>
                                        <Input {...field} type="number" step="0.01" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="memo"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Memo</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="deposit_date"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Deposit Date</FormLabel>
                                    <FormControl>
                                        <Input {...field} type="date" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="issuer_payee"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Issuer/Payee</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={onClose}>
                                Cancel
                            </Button>
                            <Button type="submit">Create</Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};

export const UIMSettlement = () => {
    const router = useRouter();
    const params = useParams();
    const caseId = params?.caseId as string;
    const queryClient = useQueryClient();

    // Query hooks
    const { data: clientDetails, isLoading: isClientLoading } = useClientBasicDetailsQuery(caseId ?? "");
    const { data: incidentDetails, isLoading: isIncidentLoading } = useIncidentDetailsQuery(caseId ?? "");
    const { data: clientTrust, isLoading: isClientTrustLoading } = useClientTrustQuery(caseId ?? "");
    const { data: settlementMemo, isLoading: isSettlementMemoLoading } = useSettlementMemoQuery(caseId ?? "");
    const { data: calculations } = useSettlementCalculationQuery(caseId ?? "");
    const { data: organization } = useCurrentOrganization();

    // State hooks
    const [selectedDepositId, setSelectedDepositId] = useState<number | null>(null);
    const [settlementItems, setSettlementItems] = useState<SettlementItem[]>([]);
    const [deletingEntryId, setDeletingEntryId] = useState<string | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [isTrustEntryDialogOpen, setIsTrustEntryDialogOpen] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [itemToDelete, setItemToDelete] = useState<{ index: number, description: string } | null>(null);
    const [formData, setFormData] = useState<ManualEntryForm>({
        description: "",
        amount: "0"
    });
    const [formState, setFormState] = useState<ManualEntryForm | null>(null);
    const [footerText, setFooterText] = useState<FooterText>({
        content: `I understand that ${organization?.name || "Law Firm"} is working on reducing and verifying medical liens with the health insurance carrier, PIP carrier, and/or the individual medical providers. The "Total amount due to client" will remain in trust pending balance verifications.`,
    });
    const [showDebugData, setShowDebugData] = useState(false);
    const [isFinalMemoOpen, setIsFinalMemoOpen] = useState(false);

    // Mutation hooks
    const updateClientResponsibility = useUpdateTreatmentProviderMutation(caseId ?? "", selectedDepositId?.toString() ?? "");
    const createManualEntry = useCreateManualSettlementEntryMutation(caseId);
    const updateSettlementCalculation = useUpdateSettlementCalculationMutation(caseId);
    const deleteManualEntry = useDeleteManualSettlementEntryMutation(caseId);
    const createClientTrust = useCreateClientTrustMutation(queryClient);
    const uploadBySection = useUploadBySectionMutation();

    // Form setup
    const form = useForm<TrustEntryFormData>({
        resolver: zodResolver(trustEntrySchema),
        defaultValues: {
            check_number: "",
            amount: "",
            memo: "",
            deposit_date: formatDateForApi(new Date()),
            client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
            issuer_payee: "",
        },
    });

    // Sensors for drag and drop
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    // Callbacks
    const refetchAllData = useCallback(() => {
        queryClient.invalidateQueries({ queryKey: ['settlementMemo', caseId] });
        queryClient.invalidateQueries({ queryKey: ['settlementCalculation', caseId] });
        queryClient.invalidateQueries({ queryKey: ['manualSettlementEntries', caseId] });
        queryClient.invalidateQueries({ queryKey: ['clientTrust', caseId] });
        queryClient.invalidateQueries({ queryKey: ['clientTrustSummary', caseId] });
    }, [queryClient, caseId]);

    const handleClientResponsibilityClick = useCallback((id: number, currentStatus: boolean) => {
        setSelectedDepositId(id);
        updateClientResponsibility.mutate(
            {
                client_responsibility: !currentStatus,
                treatment_status: TreatmentStatus.TREATING
            },
            {
                onSuccess: () => {
                    refetchAllData();
                }
            }
        );
    }, [updateClientResponsibility, refetchAllData]);

    const handleCreateTrustEntry = useCallback((description: string, amount: number) => {
        form.reset({
            check_number: "",
            amount: amount.toFixed(2),
            memo: `UIM Settlement payment for ${description}`,
            deposit_date: formatDateForApi(new Date()),
            client_trust_entry_type: ClientTrustEntryType.SETTLEMENT_PAYMENT,
            issuer_payee: description.replace(/[()]/g, "").trim(),
        });
        setIsTrustEntryDialogOpen(true);
    }, [form]);

    const handleTrustEntrySubmit = async (values: TrustEntryFormData) => {
        try {
            await createClientTrust.mutateAsync({
                caseId,
                trustData: {
                    ...values,
                    deposit_type: DepositType.UIM,
                },
            });
            setIsTrustEntryDialogOpen(false);
            toast({
                title: 'Success',
                description: 'Settlement check created successfully',
            });
            refetchAllData();
        } catch (error) {
            console.error("Error creating trust entry:", error);
            toast({
                title: 'Error',
                description: 'Failed to create settlement check',
                variant: 'destructive',
            });
        }
    };

    const handleDeleteManualEntry = (index: number) => {
        const item = settlementItems[index];
        if (item && item.isManualEntry && item.id) {
            deleteManualEntry.mutate(item.id.toString(), {
                onSuccess: () => {
                    setSettlementItems((prevItems) =>
                        prevItems.filter((_, i) => i !== index)
                    );
                    refetchAllData();
                },
                onError: (error) => {
                    console.error("Error deleting manual entry:", error);
                }
            });
        } else {
            setSettlementItems((prevItems) =>
                prevItems.filter((_, i) => i !== index)
            );
        }
    };

    const handleDeleteClick = (entryId: number, description: string) => {
        setItemToDelete({ index: entryId, description });
        setShowDeleteConfirm(true);
    };

    const handleConfirmDelete = () => {
        if (itemToDelete) {
            handleDeleteManualEntry(itemToDelete.index);
        }
        setShowDeleteConfirm(false);
        setItemToDelete(null);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData.description) return;

        createManualEntry.mutate(
            {
                company: formData.description,
                expense: formData.amount || "0",
                for_what: "UIM"
            },
            {
                onSuccess: () => {
                    setIsDialogOpen(false);
                    setFormData({ description: "", amount: "0" });
                    refetchAllData();
                },
            }
        );
    };

    const handleFeePercentageChange = useCallback((value: string) => {
        if (value && !isNaN(parseFloat(value))) {
            const formattedValue = parseFloat(value).toFixed(2);
            updateSettlementCalculation.mutate(
                {
                    fees_percentage: formattedValue,
                },
                {
                    onSuccess: () => {
                        refetchAllData();
                    }
                }
            );
        }
    }, [updateSettlementCalculation, refetchAllData]);

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        if (over && active.id !== over.id) {
            setSettlementItems((items) => {
                const oldIndex = items.findIndex((item) => item.id === active.id);
                const newIndex = items.findIndex((item) => item.id === over.id);
                const newItems = [...items];
                const [removed] = newItems.splice(oldIndex, 1);
                newItems.splice(newIndex, 0, removed);
                return newItems;
            });
        }
    };

    const handleSaveSettlement = async () => {
        const element = document.getElementById("settlement-memorandum");
        if (!element) return;

        try {
            setIsSaving(true);
            const editButton = element.querySelector(".edit-button");
            if (editButton) {
                editButton.classList.add("hidden");
            }

            element.classList.add("pdf-mode");

            const canvas = await html2canvas(element, {
                scale: 2,
                logging: false,
                useCORS: true,
                backgroundColor: "#ffffff",
                windowWidth: element.scrollWidth,
                windowHeight: element.scrollHeight,
            });

            element.classList.remove("pdf-mode");

            const imgWidth = 210;
            const pageHeight = 297;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;

            const pdf = new jsPDF("p", "mm", "a4");
            let heightLeft = imgHeight;
            let position = 0;
            const pageData = canvas.toDataURL("image/png");

            pdf.addImage(pageData, "PNG", 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(pageData, "PNG", 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            const fileName = `settlement-memorandum-${clientDetails?.last_name || "client"}-${new Date().toISOString().split("T")[0]}.pdf`;
            const pdfBlob = pdf.output('blob');
            const pdfFile = new File([pdfBlob], fileName, { type: 'application/pdf' });

            await uploadBySection.mutateAsync({
                case_id: caseId,
                section: CaseSection.SETTLEMENT_MEMO,
                file: pdfFile
            });

        } catch (error) {
            console.error("Error saving settlement memo:", error);
        } finally {
            setIsSaving(false);
            const editButton = element.querySelector(".edit-button");
            if (editButton) {
                editButton.classList.remove("hidden");
            }
        }
    };

    const handleDownloadPDF = async () => {
        const element = document.getElementById("settlement-memorandum");
        if (!element) return;

        try {
            setIsGeneratingPDF(true);
            const fileName = `uim-settlement-memorandum-${clientDetails?.last_name || "client"}-${new Date().toISOString().split("T")[0]}.pdf`;

            await generateDocument(
                element,
                {
                    title: "UIM SETTLEMENT MEMORANDUM",
                    fileName,
                    format: "pdf",
                    creator: organization?.name || "Law Firm"
                },
                [], // Not used for PDF since we're using the HTML element directly
                footerText.content
            );
        } catch (error) {
            console.error("Error generating PDF:", error);
        } finally {
            setIsGeneratingPDF(false);
        }
    };

    const handleDownloadWord = async () => {
        try {
            setIsGeneratingPDF(true);

            // Prepare sections data
            const sections = [
                {
                    title: "RECOVERY",
                    items: [
                        ...(settlementMemo?.recovery?.uim || []).map(item => ({
                            label: `UIM DEPOSIT from ${item.company_name || "Insurance Company"}`,
                            amount: item.amount,
                        })),
                        {
                            label: "TOTAL UIM SETTLEMENT",
                            amount: settlementMemo?.summary?.total_uim || 0,
                            isTotal: true
                        }
                    ]
                },
                {
                    title: "DEDUCT AND RETAIN TO PAY OTHERS",
                    items: [
                        ...settlementItems.map(item => ({
                            label: item.description,
                            amount: typeof item.amount === 'string' ? parseFloat(item.amount) : item.amount,
                            isNegative: true
                        }))
                    ]
                }
            ];

            const fileName = `uim-settlement-memorandum-${clientDetails?.last_name || "client"}-${new Date().toISOString().split("T")[0]}.docx`;

            await generateDocument(
                document.createElement('div'), // Dummy element since we're not using HTML for DOCX
                {
                    title: "UIM SETTLEMENT MEMORANDUM",
                    fileName,
                    format: "docx",
                    creator: organization?.name || "Law Firm"
                },
                sections,
                footerText.content
            );
        } catch (error) {
            console.error("Error generating Word document:", error);
        } finally {
            setIsGeneratingPDF(false);
        }
    };

    // Effects
    useEffect(() => {
        if (formData.description || formData.amount !== "0") {
            setFormState(formData);
        } else {
            setFormState(null);
        }
    }, [formData]);

    useEffect(() => {
        const shouldSetDefault = calculations?.fees_percentage === "0.00";
        if (shouldSetDefault) {
            const timer = setTimeout(() => {
                handleFeePercentageChange("33.330000");
            }, 100);
            return () => clearTimeout(timer);
        }
    }, [calculations?.fees_percentage, handleFeePercentageChange]);

    // Update settlement items from settlement memo
    useEffect(() => {
        if (!settlementMemo) return;

        const items: SettlementItem[] = [];

        // Add attorney fees
        if (settlementMemo.expenses.uim.attorney_fees) {
            settlementMemo.expenses.uim.attorney_fees.forEach(fee => {
                items.push({
                    id: fee.id,
                    description: `${fee.law_firm} ${fee.note ? `(${fee.note})` : ''}`,
                    amount: -fee.amount,
                    isManualEntry: false
                });
            });
        }

        // Add attorney liens
        if (settlementMemo.expenses.uim.attorney_liens) {
            settlementMemo.expenses.uim.attorney_liens.forEach(lien => {
                items.push({
                    id: lien.id,
                    description: `${lien.law_firm} ${lien.attorney ? `(${lien.attorney})` : ''}`,
                    amount: -lien.amount,
                    isManualEntry: false
                });
            });
        }

        // Add case costs
        if (settlementMemo.expenses.uim.case_costs) {
            const totalCaseCosts = settlementMemo.expenses.uim.case_costs.reduce((sum, cost) => sum + cost.amount, 0);
            if (totalCaseCosts > 0) {
                items.push({
                    id: null,
                    description: `${organization?.name || "Law Firm"} Cost`,
                    amount: -totalCaseCosts,
                    isManualEntry: false
                });
            }
        }

        // Add medical expenses
        if (settlementMemo.expenses.uim.medical_expenses) {
            settlementMemo.expenses.uim.medical_expenses.forEach(expense => {
                items.push({
                    id: expense.id,
                    description: `${expense.provider_name} ${expense.original_bill ? `(${expense.original_bill})` : ''}`,
                    amount: -expense.amount,
                    isManualEntry: false,
                    clientResponsibility: expense.client_responsibility
                });
            });
        }

        // Add health insurance liens
        if (settlementMemo.expenses.uim.health_insurance_liens) {
            settlementMemo.expenses.uim.health_insurance_liens.forEach(lien => {
                const details = [];
                if (lien.policy_number) details.push(`Policy: ${lien.policy_number}`);
                if (lien.file_number) details.push(`File: ${lien.file_number}`);

                items.push({
                    id: lien.id,
                    description: `${lien.insurance_name}${details.length > 0 ? ` (${details.join(', ')})` : ''}`,
                    amount: -lien.amount,
                    isManualEntry: false
                });
            });
        }

        // Add miscellaneous liens
        if (settlementMemo.expenses.uim.miscellaneous_liens) {
            settlementMemo.expenses.uim.miscellaneous_liens.forEach(lien => {
                items.push({
                    id: lien.id,
                    description: `${lien.lien_holder} ${lien.description ? `(${lien.description})` : ''}`,
                    amount: -lien.amount,
                    isManualEntry: false
                });
            });
        }

        // Add adjustments
        if (settlementMemo.expenses.uim.adjustments) {
            const uniqueAdjustments = new Map();

            settlementMemo.expenses.uim.adjustments.forEach(adjustment => {
                if (adjustment.name.includes("Migration Org Cost") || adjustment.company?.includes("Migration Org Cost")) {
                    return;
                }

                const key = adjustment.name + (adjustment.description || '');
                if (uniqueAdjustments.has(key)) {
                    const existing = uniqueAdjustments.get(key);
                    existing.amount += adjustment.amount;
                } else {
                    uniqueAdjustments.set(key, {
                        id: adjustment.id,
                        description: `${adjustment.name} ${adjustment.description ? `(${adjustment.description})` : ''}`,
                        amount: adjustment.amount,
                        isManualEntry: true,
                        entryId: adjustment.id
                    });
                }
            });

            uniqueAdjustments.forEach(adjustment => {
                items.push({
                    id: adjustment.id,
                    description: adjustment.description,
                    amount: -adjustment.amount,
                    isManualEntry: true,
                    entryId: adjustment.entryId
                });
            });
        }

        setSettlementItems(items);
    }, [settlementMemo, organization?.name]);

    // Early return guard
    if (!caseId) {
        router.push('/cases');
        return null;
    }

    // Loading state check
    if (isClientLoading || isIncidentLoading || isClientTrustLoading || isSettlementMemoLoading) {
        return <div>Loading...</div>;
    }

    return (
        <Card className="w-full max-w-3xl mx-auto p-6 shadow-none">
            <div className="flex justify-end mb-4 gap-4">
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed p-0"
                            disabled={isGeneratingPDF}
                        >
                            {isGeneratingPDF ? (
                                <Loader2 className="animate-spin" />
                            ) : (
                                <>
                                    <Download className="h-4 w-4 mr-2" />
                                    Download
                                </>
                            )}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem onClick={handleDownloadPDF}>
                            Download as PDF
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleDownloadWord}>
                            Download as Word
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                {/* Final Memo Button */}
                <Button
                    className="bg-white hover:bg-white-50 text-[#060216]-600 hover:text-[#060216]-700 border-none transition-all disabled:opacity-50 disabled:cursor-not-allowed p-0"
                    onClick={() => setIsFinalMemoOpen(true)}
                >
                    Final Memo
                </Button>
                <Button
                    variant="outline"
                    onClick={() => router.push(`/case/settlement/${caseId}`)}
                >
                    Details
                </Button>
                {/* Add a debug button that's only visible in development */}
                {process.env.NODE_ENV === 'development' && (
                    <Button
                        variant="outline"
                        onClick={() => setShowDebugData(!showDebugData)}
                    >
                        {showDebugData ? "Hide Debug" : "Show Debug"}
                    </Button>
                )}
                <FeePercentageSelect
                    value={
                        calculations?.fees_percentage &&
                            parseFloat(calculations.fees_percentage) > 0
                            ? calculations.fees_percentage
                            : "33.330000"
                    }
                    onChange={handleFeePercentageChange}
                />
                <Dialog open={isDialogOpen} onOpenChange={(open) => setIsDialogOpen(open)}>
                    <DialogTrigger asChild>
                        <Button
                            variant="link"
                            className="text-green-600"
                        >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Manual Entry
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add Manual Settlement Entry</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="description">Description *</Label>
                                <Input
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) =>
                                        setFormData((prev) => ({
                                            ...prev,
                                            description: e.target.value,
                                        }))
                                    }
                                    placeholder="Enter description"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="amount">Amount</Label>
                                <Input
                                    id="amount"
                                    type="number"
                                    step="0.01"
                                    value={formData.amount}
                                    onChange={(e) =>
                                        setFormData((prev) => ({
                                            ...prev,
                                            amount: e.target.value,
                                        }))
                                    }
                                    placeholder="0.00"
                                />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsDialogOpen(false)}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit">Save</Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <div
                className="space-y-6 bg-white print:p-0"
                id="settlement-memorandum"
                style={{ padding: "20px" }}
            >
                {/* Header Section */}
                <div className="space-y-1">
                    <div className="text-sm space-y-1">
                        {clientDetails && (
                            <p className="font-medium">
                                Client Name: {clientDetails.first_name} {clientDetails.last_name}
                            </p>
                        )}
                        {incidentDetails && (
                            <p className="font-medium">
                                DOL:{" "}
                                {incidentDetails.incident_date
                                    ? format(parseISO(incidentDetails.incident_date), "MM/dd/yyyy")
                                    : "N/A"}
                            </p>
                        )}
                    </div>
                    <h1 className="text-center font-bold text-base border-b border-black pb-1 mb-6">
                        UIM SETTLEMENT MEMORANDUM
                    </h1>
                </div>

                <CardContent className="space-y-4 px-0">
                    {/* Recovery Section */}
                    <div>
                        <p className="font-bold text-sm mb-1">RECOVERY</p>
                        <div className="border border-gray-200 p-2">
                            {settlementMemo?.recovery?.uim?.map((item, index) => (
                                <div key={index} className="flex justify-between mb-1">
                                    <div className="flex items-center gap-2">
                                        <span>
                                            {item.negotiations?.map(negotiation => (
                                                negotiation.status === 'ACCEPTED' ? 'SETTLEMENT' :
                                                negotiation.type === 'INITIAL_DEMAND' ? 'DEMAND' : 'OFFER'
                                            ))} from {item.company_name}
                                        </span>
                                    </div>
                                    <CurrencyDisplay amount={item.amount} />
                                </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-200 pt-1 mt-2">
                                <span>TOTAL UIM SETTLEMENT</span>
                                <CurrencyDisplay amount={settlementMemo?.summary?.total_uim || 0} />
                            </div>
                        </div>
                    </div>

                    {/* Settlement Items */}
                    <div className="space-y-2">
                        <h2 className="font-bold text-sm">DEDUCT AND RETAIN TO PAY OTHERS</h2>
                        <div className="border border-gray-200">
                            <DndContext
                                sensors={sensors}
                                collisionDetection={closestCenter}
                                onDragEnd={handleDragEnd}
                            >
                                <SortableContext
                                    items={settlementItems.map((_, index) => index.toString())}
                                    strategy={verticalListSortingStrategy}
                                >
                                    {settlementItems.map((item, index) => (
                                        <div key={index} className="border-b last:border-b-0 border-gray-100">
                                            <SortableItem
                                                id={index.toString()}
                                                description={item.description}
                                                amount={item.amount}
                                                onCreateTrustEntry={handleCreateTrustEntry}
                                                isManualEntry={'isManualEntry' in item ? !!item.isManualEntry : false}
                                                entryId={item.clientResponsibility !== undefined ? (item.id !== null ? Number(item.id) : undefined) : index}
                                                onDelete={handleDeleteManualEntry}
                                                isDeleting={'entryId' in item && item.entryId ? deletingEntryId === item.entryId.toString() : false}
                                                clientResponsibility={item.clientResponsibility}
                                                onClientResponsibilityChange={item.clientResponsibility !== undefined ? handleClientResponsibilityClick : undefined}
                                                onDeleteClick={handleDeleteClick}
                                            />
                                        </div>
                                    ))}
                                </SortableContext>
                            </DndContext>
                            <div className="flex justify-between font-bold px-4 py-2 border-t border-gray-200">
                                <span>TOTAL DUE OTHERS</span>
                                <span className="text-red-600">
                                    -${Math.abs(settlementMemo?.summary?.total_expenses || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Total Deductions */}
                    <div className="border border-gray-200">
                        <div className="flex justify-between font-bold px-4 py-2">
                            <span>TOTAL DEDUCTIONS:</span>
                            <span className="text-green-600">
                                ${Math.abs(settlementMemo?.summary?.total_expenses || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                            </span>
                        </div>
                    </div>

                    {/* Net Amount */}
                    <div className="border border-gray-200">
                        <div className="flex justify-between font-bold px-4 py-2">
                            <span>Net Amount Paid to Client:</span>
                            <span className="text-green-600">
                                ${(settlementMemo?.summary?.net_to_client_uim || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                            </span>
                        </div>
                    </div>

                    {/* Footer Note */}
                    <div className="space-y-4 mt-8">
                        <div className="flex justify-between items-center mb-2">
                            {!isGeneratingPDF && !isEditing && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => setIsEditing(true)}
                                    className="hover:bg-green-50 hover:text-green-600 edit-button ml-auto"
                                >
                                    <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                                </Button>
                            )}
                            {isEditing && (
                                <div className="flex justify-end gap-2">
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => setIsEditing(false)}
                                        className="hover:bg-green-50 hover:text-green-600"
                                    >
                                        <Save className="h-4 w-4 text-green-600" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => setIsEditing(false)}
                                        className="hover:bg-red-50 hover:text-red-600"
                                    >
                                        <X className="h-4 w-4 text-red-600" />
                                    </Button>
                                </div>
                            )}
                        </div>
                        {isEditing ? (
                            <div className="space-y-2">
                                <Textarea
                                    className="w-full p-2 text-sm border rounded min-h-[100px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={footerText.content}
                                    onChange={(e) => setFooterText({ content: e.target.value })}
                                    rows={4}
                                />
                            </div>
                        ) : (
                            <div className="text-sm">
                                <p>{footerText.content}</p>
                            </div>
                        )}
                    </div>
                </CardContent>
            </div>

            {/* Save Settlement Button */}
            <div className="flex justify-center mt-8">
                <Button
                    onClick={handleSaveSettlement}
                    disabled={isSaving}
                    className="w-full max-w-md bg-emerald-600 hover:bg-emerald-700 text-white py-6 text-lg font-medium rounded-lg"
                >
                    <Save className="h-5 w-5 mr-2" />
                    {isSaving ? "Saving in Cloud..." : "Save to Cloud"}
                </Button>
            </div>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={() => {
                    setIsDialogOpen(false);
                    setFormData({ description: "", amount: "0" });
                    setFormState(null);
                }}
                onCancel={() => setShowUnsavedAlert(false)}
            />

            <DeleteConfirmationDialog
                open={showDeleteConfirm}
                onOpenChange={setShowDeleteConfirm}
                onConfirm={handleConfirmDelete}
                text="Entry"
            />

            {/* Add TrustEntryDialog */}
            <TrustEntryDialog
                isOpen={isTrustEntryDialogOpen}
                form={form}
                onClose={() => setIsTrustEntryDialogOpen(false)}
                onSubmit={handleTrustEntrySubmit}
            />

            <FinalMemoPreviewModal
                isOpen={isFinalMemoOpen}
                onOpenChange={setIsFinalMemoOpen}
                caseId={caseId}
            />

            {/* Add debug section */}
            {showDebugData && calculations && (
                <div className="mb-4 p-4 border border-gray-300 rounded bg-gray-50">
                    <h3 className="font-bold mb-2">Settlement Calculation Data:</h3>
                    <pre className="text-xs overflow-auto">
                        {JSON.stringify(calculations, null, 2)}
                    </pre>
                </div>
            )}
        </Card>
    );
};

export default UIMSettlement;