import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useStreamChat } from '@/hooks/useStreamChat';
import { useCaseDocumentsCategories } from '@/services/documentService';
import { useToast } from "@/hooks/use-toast";
import { removeLastMessage } from '@/store/slices/caseChatSlice';
import { cn } from "@/lib/utils";
import { ChatHeader } from './components/ChatHeader';
import { ChatMessages } from './components/ChatMessages';
import { MessageInput } from './components/MessageInput';
import { IndexingRequired } from './components/IndexingRequired';
import { useIndexCaseFromS3, useUpdateCaseWithChatProcessingStatus } from '@/services/caseService';
import { useCaseDocuments } from '@/hooks/useCaseDocuments';
import { CaseChatProps } from '@/type/caseChat';
import { CategoryDocumentMapping } from '@/type/caseChat';
import { RootState } from '@/store';
import { DocumentWithCategories } from '@/type/doocument';
import { ChatRequest } from '@/type/dashboard';

export const CaseChat: React.FC<CaseChatProps> = ({ caseId }) => {
    const [message, setMessage] = useState<string>("");
    const [categoryDocumentMapping, setCategoryDocumentMapping] = useState<CategoryDocumentMapping>({});
    const [isExpanded, setIsExpanded] = useState(false);

    const maxLength = 4000;

    const { data: documentsWithCategories = [] } = useCaseDocumentsCategories(caseId);
    const dispatch = useDispatch();
    const { toast } = useToast();

    const { mutate: sendMessage } = useStreamChat({
        onError: (error) => {
            console.log(error)
            dispatch(removeLastMessage(caseId));
            toast({
                title: "Error",
                description: "Failed to send message. Please try again.",
                variant: "destructive",
            });
        }
    });

    const { allDocuments, selectedCaseDetail } = useSelector((state: RootState) => state.caseView);
    const { messages, currentMessage, isStreaming } = useSelector((state: RootState) => state.caseChat);
    const chatMessages = messages[caseId] || [];

    useEffect(() => {
        if (documentsWithCategories.length > 0) {
            const processedMapping = generateCategoryMapping(documentsWithCategories);
            setCategoryDocumentMapping(processedMapping);
        }
    }, [documentsWithCategories]);

    const generateCategoryMapping = (documents: DocumentWithCategories[]): CategoryDocumentMapping => {
        const mapping: CategoryDocumentMapping = documents.reduce((acc: CategoryDocumentMapping, doc) => {
            doc.categories.forEach((category: { category_display: string }) => {
                const categoryKey = category.category_display;
                if (!acc[categoryKey]) {
                    acc[categoryKey] = {
                        documents: [],
                        count: 0
                    };
                }
                if (!acc[categoryKey].documents.includes(doc.file_name)) {
                    acc[categoryKey].documents.push(doc.file_name);
                    acc[categoryKey].count++;
                }
            });
            return acc;
        }, {});

        return Object.fromEntries(
            Object.entries(mapping)
                .sort(([, a], [, b]) => b.count - a.count)
        );
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!message.trim() || message.length > maxLength) return;

        const chatRequest: ChatRequest = {
            messages: [{
                role: 'user',
                content: message
            }],
            file_names_to_chat_with: allDocuments.map(doc => doc.file_name),
            category_to_document_mapping: categoryDocumentMapping,
            client_name: selectedCaseDetail?.name_of_client || "Unknown Client",
            accident_date: selectedCaseDetail?.accident_date || "Unknown Date"
        };

        sendMessage({ chatRequest, caseId });
        setMessage('');
    };

    const documents = useSelector((state: RootState) => state.caseView.allDocuments);
    const not_indexed_documents = documents.filter(doc => !doc.is_indexed && doc.is_part_of_case_with_chat);
    const indexed_documents = documents.filter(doc => doc.is_indexed && doc.is_part_of_case_with_chat);
    const selected_document = documents.filter(doc => doc.is_part_of_case_with_chat);

    const hasUnindexedDocs = not_indexed_documents.length > 0 && not_indexed_documents.length < selected_document.length;
    const { mutate: indexCase } = useIndexCaseFromS3();
    const { mutate: updateStatus } = useUpdateCaseWithChatProcessingStatus();
    const { updateDocumentState } = useCaseDocuments(caseId);

    const handleIndex = () => {
        if (!selectedCaseDetail) return;
        
        indexCase({
            case_id: caseId,
            case_with_chat_filenames: not_indexed_documents.map(doc => doc.file_name),
            case_name: selectedCaseDetail?.case_name || "Unknown Client"
        }, {
            onSuccess: () => {
                updateStatus(caseId, {
                    onSuccess: () => {
                        updateDocumentState(true);
                    }
                });
            }
        });
    };

    return (
        <div className={cn(
            "flex flex-col h-full transition-all duration-300",
            isExpanded ? "fixed inset-0 z-50 bg-background" : "relative"
        )}>
            <ChatHeader
                isExpanded={isExpanded}
                onToggleExpand={() => setIsExpanded(!isExpanded)}
                hasUnindexedDocs={hasUnindexedDocs}
                unindexedCount={not_indexed_documents.length}
                handleIndex={handleIndex}
            />
            {indexed_documents.length === 0 && not_indexed_documents.length > 0 ? (
                <div className="flex-1 flex items-center justify-center">
                    <IndexingRequired handleIndex={handleIndex}/>
                </div>
            ) : (
                <ChatMessages
                    chatMessages={chatMessages}
                    isStreaming={isStreaming}
                    currentMessage={currentMessage}
                />
            )}
            <MessageInput
                message={message}
                setMessage={setMessage}
                handleSubmit={handleSubmit}
                isExceeded={message.length > maxLength}
                maxLength={maxLength}
                isDisabled={not_indexed_documents.length > 0 && indexed_documents.length === 0}
            />
        </div>
    );
} 