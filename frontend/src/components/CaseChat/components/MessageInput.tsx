import React from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send } from "lucide-react";
import { MessageInputProps } from '../types';

export const MessageInput: React.FC<MessageInputProps> = ({
    message,
    setMessage,
    handleSubmit,
    isExceeded,
    maxLength,
    isDisabled
}) => {
    return (
        <form onSubmit={handleSubmit} className="p-4 border-t">
            <div className="flex gap-2">
                <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 focus:outline-none focus-visible:outline-none focus:border-none focus:ring-0"
                    disabled={isDisabled}
                />
                <Button 
                    type="submit"
                    disabled={!message.trim() || isExceeded || isDisabled}
                >
                    <Send className="w-4 h-4" />
                </Button>
            </div>
            <div className="flex justify-end mt-1">
                <span className={`text-xs ${isExceeded ? 'text-destructive' : 'text-muted-foreground'}`}>
                    {message.length}/{maxLength}
                </span>
            </div>
        </form>
    );
}; 