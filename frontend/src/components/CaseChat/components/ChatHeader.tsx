import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Maximize2, Minimize2, Alert<PERSON>riangle } from "lucide-react";
import { ChatHeaderProps } from '../types';

export const ChatHeader: React.FC<ChatHeaderProps> = ({
    isExpanded,
    onToggleExpand,
    hasUnindexedDocs,
    unindexedCount,
    handleIndex
}) => {
    return (
        <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
                <h2 className="text-lg font-semibold">Chat Assistant</h2>
                {hasUnindexedDocs && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleIndex}
                        className="text-yellow-600"
                    >
                        <AlertTriangle className="w-4 h-4 mr-2" />
                        {unindexedCount} unindexed documents
                    </Button>
                )}
            </div>
            <Button
                variant="ghost"
                size="icon"
                onClick={onToggleExpand}
            >
                {isExpanded ? (
                    <Minimize2 className="w-4 h-4" />
                ) : (
                    <Maximize2 className="w-4 h-4" />
                )}
            </Button>
        </div>
    );
}; 