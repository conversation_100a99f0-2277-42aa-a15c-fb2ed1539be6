import React from 'react';
import { Bo<PERSON> } from 'lucide-react';
import { StreamingMessageProps } from '../types';
import { MarkdownContent } from './MarkdownContent';

export const StreamingMessage: React.FC<StreamingMessageProps> = ({ currentMessage }) => {
    return (
        <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                <Bot className="w-4 h-4 text-secondary-foreground" />
            </div>
            <div className="flex-1">
                <div className="inline-block max-w-[80%] rounded-lg p-3 bg-secondary text-secondary-foreground">
                    <MarkdownContent content={currentMessage} />
                </div>
            </div>
        </div>
    );
}; 