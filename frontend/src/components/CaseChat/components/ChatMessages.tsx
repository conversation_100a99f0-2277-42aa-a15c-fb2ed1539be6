import React, { useRef, useEffect } from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChatMessage } from './ChatMessage';
import { EmptyChat } from './EmptyChat';
import { StreamingMessage } from './StreamingMessage';
import { ChatMessagesProps } from '../types';
import { TypingIndicator } from './TypingIndicator';

export const ChatMessages: React.FC<ChatMessagesProps> = ({
    chatMessages,
    isStreaming,
    currentMessage,
}) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [chatMessages, currentMessage]);

    return (
        <ScrollArea className="flex-1 px-4 overflow-y-auto">
            <div className="space-y-4 py-4">
                {chatMessages.length === 0 ? (
                    <EmptyChat />
                ) : (
                    <>
                        {chatMessages.map((msg, index) => (
                            <ChatMessage key={index} msg={msg} />
                        ))}
                        {isStreaming && !currentMessage && <TypingIndicator />}
                        {isStreaming && currentMessage && <StreamingMessage currentMessage={currentMessage} />}
                        <div ref={messagesEndRef} />
                    </>
                )}
            </div>
        </ScrollArea>
    );
}; 