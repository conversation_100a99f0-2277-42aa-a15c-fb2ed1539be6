import React from 'react';
import { Bot } from 'lucide-react';

export const TypingIndicator: React.FC = () => {
    return (
        <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                <Bot className="w-4 h-4 text-secondary-foreground" />
            </div>
            <div className="flex-1">
                <div className="inline-block rounded-lg p-3 bg-secondary text-secondary-foreground">
                    <div className="flex gap-1">
                        <div className="w-2 h-2 rounded-full bg-current animate-bounce" />
                        <div className="w-2 h-2 rounded-full bg-current animate-bounce [animation-delay:0.2s]" />
                        <div className="w-2 h-2 rounded-full bg-current animate-bounce [animation-delay:0.4s]" />
                    </div>
                </div>
            </div>
        </div>
    );
}; 