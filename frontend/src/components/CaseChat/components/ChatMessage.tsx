import React from 'react';
import { Use<PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { ChatMessageProps } from '../types';
import { MarkdownContent } from './MarkdownContent';

export const ChatMessage: React.FC<ChatMessageProps> = ({ msg }) => {
    const isUser = msg.role === 'user';

    return (
        <div className={`flex gap-3 ${isUser ? 'flex-row-reverse' : ''}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                isUser ? 'bg-primary' : 'bg-secondary'
            }`}>
                {isUser ? (
                    <User className="w-4 h-4 text-primary-foreground" />
                ) : (
                    <Bot className="w-4 h-4 text-secondary-foreground" />
                )}
            </div>
            <div className={`flex-1 ${isUser ? 'text-right' : 'text-left'}`}>
                <div className={`inline-block max-w-[80%] rounded-lg p-3 ${
                    isUser ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'
                }`}>
                    <MarkdownContent content={msg.content} />
                </div>
                {msg.timestamp && (
                    <div className="text-xs text-muted-foreground mt-1">
                        {new Date(msg.timestamp).toLocaleTimeString()}
                    </div>
                )}
            </div>
        </div>
    );
}; 