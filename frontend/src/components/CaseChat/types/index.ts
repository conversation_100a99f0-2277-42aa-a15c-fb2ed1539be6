export interface ChatMessagesProps {
    chatMessages: ChatMessage[];
    isStreaming: boolean;
    currentMessage?: string;
}

export interface ChatMessageProps {
    msg: ChatMessage;
}

export interface ChatHeaderProps {
    isExpanded: boolean;
    onToggleExpand: () => void;
    hasUnindexedDocs: boolean;
    unindexedCount: number;
    handleIndex: () => void;
}

export interface MessageInputProps {
    message: string;
    setMessage: (message: string) => void;
    handleSubmit: (e: React.FormEvent) => void;
    isExceeded: boolean;
    maxLength: number;
    isDisabled: boolean;
}

export interface StreamingMessageProps {
    currentMessage: string;
}

export interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
    timestamp?: Date;
} 