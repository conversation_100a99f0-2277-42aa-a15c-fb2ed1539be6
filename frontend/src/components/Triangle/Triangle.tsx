import React from 'react';

interface TriangleProps {
  borderTopColor: string;
  borderLeftColor: string;
  borderBottomColor: string;
}

const Triangle: React.FC<TriangleProps> = ({ borderTopColor, borderLeftColor, borderBottomColor }) => {
  console.log(borderTopColor, borderLeftColor, borderBottomColor)
  return (
    <div className="relative">
      <div className="absolute -top-0.5 left-0 w-0 h-0 
            border-t-[31px] border-t-transparent
            border-l-[42px] border-l-gray-300
            border-b-[31px] border-b-transparent">
      </div>
      <div className="relative w-0 h-0 
            border-t-[30px] border-t-transparent
            border-l-[40px] border-l-[#E0D7FA]
            border-b-[30px] border-b-transparent">
      </div>
    </div>
  );
};

export default Triangle; 