'use client';

import React, { FC, useEffect, useState } from 'react';
import { useRenderTemplateMutation, Template, useTemplatesWithRedux } from '@/services/templateManagementService';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Mail } from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { setSelectedContextType, TemplateContextType } from '@/store/slices/templatesSlice';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ManageEmailTemplateProps {
  caseId?: string;
  leadId?: string;
  clientId?: string;
  clientInsuranceId?: string;
  adjusterId?: string;
  defendantId?: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  className?: string;
  contextType?: TemplateContextType;
  defendantInsuranceId?: string;
  healthInsuranceId?: string;
  other_party_id?: string;
  other_plaintiff_id?: string;
  witness_id?: string;
  expert_witness_id?: string;
  healthProviderId?: string;
}

export const ManageEmailTemplate: FC<ManageEmailTemplateProps> = ({
  caseId,
  leadId,
  clientId,
  clientInsuranceId,
  adjusterId,
  defendantId,
  onSuccess,
  onError,
  className,
  contextType = 'all',
  defendantInsuranceId,
  healthInsuranceId,
  other_party_id,
  other_plaintiff_id,
  witness_id,
  expert_witness_id,
  healthProviderId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const dispatch = useDispatch();
  // Use Redux state
  const {
    templates,
    loading: isLoadingTemplates,
    error: templatesError
  } = useSelector((state: RootState) => state.templates);

  // Use the new hook that combines Redux and React Query
  useTemplatesWithRedux({ context_type: contextType });

  // Initialize context type in Redux when component mounts
  useEffect(() => {
    dispatch(setSelectedContextType(contextType));
  }, [contextType, dispatch]);

  const renderTemplateMutation = useRenderTemplateMutation();

  const handleTemplateSelect = (template: Template) => {
    handleSendTemplate(template.id);
  };

  const handleSendTemplate = async (selectedTemplateId: number) => {
    setIsLoading(true);
    try {
      const selectedTemplate = templates.find((t: Template) => t.id === selectedTemplateId);
      
      // Call the render template mutation with the appropriate ID
      const {data: response, file_name} = await renderTemplateMutation.mutateAsync({
        templateId: selectedTemplateId,
        name: selectedTemplate?.name || '',
        ...(caseId && { case_id: caseId }),
        ...(leadId && { lead_id: leadId }),
        ...(defendantId && { defendant_id: defendantId }),
        ...(defendantInsuranceId && { defendant_insurance_id: defendantInsuranceId }),
        ...(clientId && { client_id: clientId }),
        ...(healthInsuranceId && { client_health_insurance_id: healthInsuranceId }),
        ...(other_party_id && { other_party_id }),
        ...(other_plaintiff_id && { other_plaintiff_id }),
        ...(witness_id && { witness_id }),
        ...(expert_witness_id && { expert_witness_id }),
        ...(clientInsuranceId && { client_insurance_id: clientInsuranceId }),
        ...(healthProviderId && { treatment_provider_id: healthProviderId }),
        ...(adjusterId && { adjuster_id: adjusterId }),
      });

      // Handle the blob response
      if (response instanceof Blob) {
        // Create a URL for the blob
        const url = window.URL.createObjectURL(response);

        // Create a link element and trigger download
        const link = document.createElement('a');
        link.href = url;
        link.download = decodeURIComponent(file_name);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the URL
        window.URL.revokeObjectURL(url);

        toast({
          title: 'Success',
          description: 'Template rendered successfully',
        });

        onSuccess?.();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to render template';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      onError?.(error instanceof Error ? error : new Error(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (templatesError) {
      toast({
        title: 'Error',
        description: templatesError,
        variant: 'destructive',
      });
    }
  }, [templatesError, toast]);

  const [searchQuery, setSearchQuery] = useState("");

  const filterTemplates = (templates: Template[]) => {
    return templates.filter(template =>
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
      (contextType === 'defendant' 
        ? (template.context_type === 'defendant' || template.context_type === 'litigation')
        : template.context_type === contextType)
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="p-0">
        <Button
          disabled={isLoading || isLoadingTemplates}
          variant="link"
          className={`${className}`}
          title="Send Template"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Mail className="h-4 w-4" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[300px]">
        <DropdownMenuLabel>Select Template</DropdownMenuLabel>
        <div className="px-2 py-2">
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-8"
          />
        </div>
        {filterTemplates(templates).length === 0 ? (
          <DropdownMenuItem disabled>No templates available</DropdownMenuItem>
        ) : (
          <ScrollArea className="h-[300px]">
            {contextType === 'defendant' ? (
              <>
                {/* Defendant Templates Group */}
                {filterTemplates(templates).filter(t => t.context_type === 'defendant').length > 0 && (
                  <DropdownMenuGroup>
                    <DropdownMenuLabel className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                      Defendant Templates
                    </DropdownMenuLabel>
                    {filterTemplates(templates)
                      .filter(template => template.context_type === 'defendant')
                      .map((template) => (
                        <DropdownMenuItem
                          key={template.id}
                          onClick={() => handleTemplateSelect(template)}
                          className="flex items-center justify-between h-8"
                        >
                          <span className="truncate">{template.name}</span>
                        </DropdownMenuItem>
                      ))}
                  </DropdownMenuGroup>
                )}

                {/* Litigation Templates Group */}
                {filterTemplates(templates).filter(t => t.context_type === 'litigation').length > 0 && (
                  <DropdownMenuGroup>
                    <DropdownMenuLabel className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                      Litigation Templates
                    </DropdownMenuLabel>
                    {filterTemplates(templates)
                      .filter(template => template.context_type === 'litigation')
                      .map((template) => (
                        <DropdownMenuItem
                          key={template.id}
                          onClick={() => handleTemplateSelect(template)}
                          className="flex items-center justify-between h-8"
                        >
                          <span className="truncate">{template.name}</span>
                        </DropdownMenuItem>
                      ))}
                  </DropdownMenuGroup>
                )}
              </>
            ) : (
              <DropdownMenuGroup>
                {filterTemplates(templates).map((template) => (
                  <DropdownMenuItem
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="flex items-center justify-between h-8"
                  >
                    <span className="truncate">{template.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            )}
          </ScrollArea>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ManageEmailTemplate; 