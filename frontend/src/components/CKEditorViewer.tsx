'use client';

import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { useEffect, useState } from 'react';
import styles from './CKEditor.module.css';

interface CKEditorViewerProps {
  /**
   * Content to display in the viewer
   */
  content: string;
  /**
   * Additional CSS class names
   */
  className?: string;
  /**
   * Minimum height of the viewer
   */
  minHeight?: string;
}

/**
 * A read-only CKEditor component for viewing content
 * @component
 */
const CKEditorViewer: React.FC<CKEditorViewerProps> = ({
  content = '',
  className = '',
  minHeight = '100px',
}) => {
  const [isReady, setIsReady] = useState<boolean>(false);

  // Handle ResizeObserver error
  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      if (event.message.includes('ResizeObserver')) {
        event.stopPropagation();
        event.preventDefault();
      }
    };

    window.addEventListener('error', errorHandler);
    return () => window.removeEventListener('error', errorHandler);
  }, []);

  const editorConfiguration = {
    toolbar: [], // Empty toolbar since it's read-only
    removePlugins: ['MediaEmbed', 'EasyImage'],
    ui: {
      viewportOffset: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      }
    }
  };

  return (
    <div 
      className={`${styles['ckeditor-wrapper']} ${className} viewer-mode`}
      style={{ 
        '--ck-min-height': minHeight 
      } as React.CSSProperties}
    >
      <style jsx global>{`
        .viewer-mode .ck.ck-toolbar.ck-toolbar_grouping {
          display: none !important;
        }
        .viewer-mode .ck.ck-editor__main {
          border-top: none;
        }
        .viewer-mode .ck.ck-editor__editable {
          border: none !important;
          box-shadow: none !important;
          background: transparent !important;
          padding: 0 !important;
        }
      `}</style>
      <CKEditor
        // @ts-expect-error - Type conflict between different CKEditor versions
        editor={ClassicEditor}
        config={editorConfiguration}
        data={content}
        disabled={true}
        onReady={() => setIsReady(true)}
      />
    </div>
  );
};

export default CKEditorViewer; 