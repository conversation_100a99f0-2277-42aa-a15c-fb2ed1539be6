import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { clearNewNotification } from '@/store/slices/notificationSlice';

/**
 * This component should be placed high in the component tree
 * to handle notifications globally
 */
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { toast } = useToast();
  const dispatch = useDispatch();
  
  // Get the notification state from Redux
  const { newNotification } = useSelector((state: RootState) => state.notification);

  // Show toast when there's a new notification
  useEffect(() => {
    if (newNotification) {
      toast({
        title: newNotification.title,
        description: newNotification.message,
        variant: newNotification.priority === 'HIGH' ? 'destructive' : 'default',
      });
      
      // Clear the new notification after showing the toast
      dispatch(clearNewNotification());
    }
  }, [newNotification, toast, dispatch]);

  return <>{children}</>;
}; 