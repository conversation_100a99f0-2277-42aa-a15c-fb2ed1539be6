'use client';

import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { useEffect, useState, useCallback, useRef } from 'react';
import styles from './CKEditor.module.css';
import type { DowncastWriter } from '@ckeditor/ckeditor5-engine';
import RootEditableElement from '@ckeditor/ckeditor5-engine/src/view/rooteditableelement';
import type { EventInfo } from '@ckeditor/ckeditor5-utils';
import type { Editor } from '@ckeditor/ckeditor5-core';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type EditorType = any;

interface CKEditorProps {
  /**
   * Initial content for the editor
   */
  initialValue?: string;
  /**
   * Callback function triggered when editor content changes
   */
  onChange?: (data: string) => void;
  /**
   * Additional CSS class names
   */
  className?: string;
  /**
   * Placeholder text when editor is empty
   */
  placeholder?: string;
  /**
   * Whether the editor is disabled
   */
  disabled?: boolean;
  /**
   * Minimum height of the editor
   */
  minHeight?: string;
}

/**
 * A reusable CKEditor component with TypeScript support
 * @component
 */
const CustomCKEditor: React.FC<CKEditorProps> = ({
  initialValue = '',
  onChange,
  className = '',
  placeholder = 'Start typing...',
  disabled = false,
  minHeight = '200px',
}) => {
  const [editorData, setEditorData] = useState<string>(initialValue);
  const [isReady, setIsReady] = useState<boolean>(false);
  const editorInstance = useRef<EditorType>(null);
  const resizeTimeout = useRef<NodeJS.Timeout>();

  // Handle ResizeObserver error
  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      if (event.message.includes('ResizeObserver')) {
        event.stopPropagation();
        event.preventDefault();
      }
    };

    window.addEventListener('error', errorHandler);
    return () => window.removeEventListener('error', errorHandler);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (editorInstance.current) {
        try {
          editorInstance.current.destroy();
        } catch (error) {
          console.warn('Error destroying editor:', error);
        }
      }
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!isReady) return;
    setEditorData(initialValue);
  }, [initialValue, isReady]);

  const handleEditorReady = useCallback((editor: EditorType) => {
    try {
      if (!editor) {
        console.warn('Editor instance is undefined in handleEditorReady');
        return;
      }

      setIsReady(true);
      editorInstance.current = editor;

      // Safely access and modify editor view root
      const view = editor.editing?.view;
      const viewDocument = view?.document;
      const viewRoot = viewDocument?.getRoot();

      if (view && viewRoot) {
        view.change((writer: DowncastWriter) => {
          if (resizeTimeout.current) {
            clearTimeout(resizeTimeout.current);
          }
          
          try {
            resizeTimeout.current = setTimeout(() => {
              writer.setStyle(
                'min-height',
                minHeight,
                viewRoot as RootEditableElement
              );
              writer.setStyle(
                'max-height',
                '500px',
                viewRoot as RootEditableElement
              );
              writer.setStyle(
                'overflow-y',
                'auto',
                viewRoot as RootEditableElement
              );
            }, 100);
          } catch (error) {
            console.warn('Error setting editor styles:', error);
          }
        });
      } else {
        console.warn('Editor view or root not available');
      }
    } catch (error) {
      console.error('Error in handleEditorReady:', error);
    }
  }, [minHeight]);

  const editorConfiguration = {
    placeholder,
    toolbar: [
      'heading',
      '|',
      'bold',
      'italic',
      'link',
      'bulletedList',
      'numberedList',
      '|',
      'outdent',
      'indent',
      '|',
      'blockQuote',
      'insertTable',
      'undo',
      'redo'
    ],
    removePlugins: ['MediaEmbed', 'EasyImage'],
    ui: {
      viewportOffset: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      }
    }
  };

  return (
    <div 
      className={`${styles['ckeditor-wrapper']} ${className}`}
      style={{ 
        '--ck-min-height': minHeight 
      } as React.CSSProperties}
    >
      <CKEditor
        // @ts-expect-error - Type conflict between different CKEditor versions
        editor={ClassicEditor}
        config={editorConfiguration}
        data={editorData}
        disabled={disabled}
        onReady={handleEditorReady}
        onChange={(_event: EditorType, editor: EditorType) => {
          const data = editor.getData();
          setEditorData(data);
          onChange?.(data);
        }}
      />
    </div>
  );
};

export default CustomCKEditor; 