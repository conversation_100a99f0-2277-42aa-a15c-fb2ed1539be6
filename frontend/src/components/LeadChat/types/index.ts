export interface LeadChatHeaderProps {
    isExpanded: boolean;
    onToggleExpand: () => void;
    hasUnindexedDocs: boolean;
    unindexedCount: number;
    handleIndex: () => void;
}

export interface LeadChatProps {
    leadId: string;
}

export interface ChatMessageProps {
    msg: {
        role: 'user' | 'assistant';
        content: string;
        timestamp?: string | number;
    };
}

export interface ChatMessagesProps {
    chatMessages: ChatMessageProps['msg'][];
    isStreaming: boolean;
    currentMessage?: string;
}

export interface MessageInputProps {
    message: string;
    setMessage: (message: string) => void;
    handleSubmit: (e: React.FormEvent) => void;
    isExceeded: boolean;
    maxLength: number;
    isDisabled: boolean;
}

export interface StreamingMessageProps {
    currentMessage: string;
}