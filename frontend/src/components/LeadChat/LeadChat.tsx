import React, { useState, useCallback, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { ChatHeader } from './components/ChatHeader';
import { ChatMessages } from './components/ChatMessages';
import { MessageInput } from './components/MessageInput';
import { IndexingRequired } from './components/IndexingRequired';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface LeadChatProps {
  leadId: string;
}

interface DocumentWithCategories {
  file_name: string;
  categories: { category_display: string }[];
  is_indexed: boolean;
  is_part_of_lead_with_chat: boolean;
}

interface CategoryDocumentMapping {
  [key: string]: {
    documents: string[];
    count: number;
  };
}

export function LeadChat({ leadId }: LeadChatProps) {
  console.log(leadId,"leadId")
  const [message, setMessage] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState(false);
  const [mockMessages, setMockMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const [categoryDocumentMapping, setCategoryDocumentMapping] = useState<CategoryDocumentMapping>({});
  const maxLength = 4000;
console.log(categoryDocumentMapping,"categoryDocumentMapping")
  const documentsWithCategories = useCallback(() => {
    // Your document categorization logic here
    return [] as DocumentWithCategories[];
  }, []);

  const { toast } = useToast();

  useEffect(() => {
    if (documentsWithCategories().length > 0) {
      const processedMapping = generateCategoryMapping(documentsWithCategories());
      setCategoryDocumentMapping(processedMapping);
    }
  }, [documentsWithCategories]);

  const generateCategoryMapping = (documents: DocumentWithCategories[]): CategoryDocumentMapping => {
    const mapping: CategoryDocumentMapping = documents.reduce((acc: CategoryDocumentMapping, doc) => {
      doc.categories.forEach((category: { category_display: string }) => {
        const categoryKey = category.category_display;
        if (!acc[categoryKey]) {
          acc[categoryKey] = {
            documents: [],
            count: 0
          };
        }
        if (!acc[categoryKey].documents.includes(doc.file_name)) {
          acc[categoryKey].documents.push(doc.file_name);
          acc[categoryKey].count++;
        }
      });
      return acc;
    }, {});

    return Object.fromEntries(
      Object.entries(mapping).sort(([, a], [, b]) => b.count - a.count)
    );
  };

  const mockSendMessage = useCallback((messageText: string) => {
    setIsStreaming(true);
    const assistantResponse = "This is a mock response from the AI assistant. I understand your message and I'm here to help.";
    
    setTimeout(() => {
      setMockMessages(prev => [...prev,
        { role: 'user', content: messageText },
        { role: 'assistant', content: assistantResponse }
      ]);
      setIsStreaming(false);
      setCurrentMessage("");
    }, 1000);
  }, []);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || message.length > maxLength) return;
    mockSendMessage(message);
    setMessage('');
  }, [message, maxLength, mockSendMessage]);

  const handleIndex = useCallback(() => {
    toast({
      title: "Indexing Started",
      description: "Documents are being indexed (mock functionality).",
    });
  }, [toast]);

  const documents = documentsWithCategories();
  const not_indexed_documents = documents.filter(doc => !doc.is_indexed && doc.is_part_of_lead_with_chat);
  const indexed_documents = documents.filter(doc => doc.is_indexed && doc.is_part_of_lead_with_chat);
  const selected_document = documents.filter(doc => doc.is_part_of_lead_with_chat);

  const hasUnindexedDocs = not_indexed_documents.length > 0 && not_indexed_documents.length < selected_document.length;

  return (
      <div className={cn(
        "flex flex-col h-full transition-all duration-300",
        isExpanded ? "fixed inset-0 z-50 bg-background" : "relative"
      )}>
        <ChatHeader
          isExpanded={isExpanded}
          onToggleExpand={() => setIsExpanded(!isExpanded)}
          hasUnindexedDocs={hasUnindexedDocs}
          unindexedCount={not_indexed_documents.length}
          handleIndex={handleIndex}
        />
        {indexed_documents.length === 0 && not_indexed_documents.length > 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <IndexingRequired handleIndex={handleIndex}/>
          </div>
        ) : (
          <ChatMessages
            chatMessages={mockMessages}
            isStreaming={isStreaming}
            currentMessage={currentMessage}
          />
        )}
        <MessageInput
          message={message}
          setMessage={setMessage}
          handleSubmit={handleSubmit}
          isExceeded={message.length > maxLength}
          maxLength={maxLength}
          isDisabled={not_indexed_documents.length > 0 && indexed_documents.length === 0}
        />
      </div>
  );
} 