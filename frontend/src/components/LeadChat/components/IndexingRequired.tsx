import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface IndexingRequiredProps {
    handleIndex: () => void;
}

export const IndexingRequired: React.FC<IndexingRequiredProps> = ({ handleIndex }) => {
    return (
        <Card className="w-[90%] max-w-md p-6">
            <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 bg-yellow-100 rounded-full">
                    <AlertTriangle className="w-8 h-8 text-yellow-600" />
                </div>
                
                <div className="space-y-2">
                    <h3 className="text-lg font-semibold">
                        Documents Need Indexing
                    </h3>
                    
                    <p className="text-sm text-muted-foreground">
                        Before you can start chatting, your documents need to be indexed. 
                        This process allows the AI to understand and reference your documents effectively.
                    </p>
                </div>

                <Button 
                    onClick={handleIndex}
                    className="w-full"
                >
                    Start Indexing
                </Button>

                <p className="text-xs text-muted-foreground">
                    This process may take a few minutes depending on the number and size of documents.
                </p>
            </div>
        </Card>
    );
}; 