import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Briefcase, BriefcaseBusiness, DollarSign, X } from "lucide-react";
import { FileViewer } from "@/components/FileViewer/FileViewer";
import { useDocumentContext } from "@/contexts/DocumentContext";
import { CaseOverview } from "@/components/CaseOverview/CaseOverview";
import { useState, useEffect } from "react";
import { SettlementAndNegotiation } from "@/components/CaseOverview/SettlementAndNegotiation";
// import { PreLitigationTabs } from "@/components/CaseOverview/PreLitigationTabs";
import { LitigationTabs } from "@/components/CaseOverview/LitigationTabs";
import { useSearchParams, useRouter } from "next/navigation";
import { CommandPalette } from "@/components/CommandPalette/CommandPalette";

interface DocumentTabsProps {
  isLoadingFiles: boolean;
  caseId: string;
}

enum <PERSON>b<PERSON>ey {
  CASE_OVERVIEW = "case-overview",
  PRE_LITIGATION = "pre-litigation",
  DEMAND_GENERATOR = "demand-generator",
  FILE_VIEWER = "file-viewer",
  LITIGATION = "litigation",
  SETTLEMENT = "settlement",
}

interface ActiveTab {
  value: string;
  key: TabKey;
}

interface Tab {
  name: string;
  value: string;
  component: React.ReactNode;
  removable: boolean;
  key: TabKey;
  icon?: React.ReactNode;
}

const getDefaultTabs = (caseId: string): Tab[] => [
  {
    name: "Case Overview",
    value: TabKey.CASE_OVERVIEW,
    component: <CaseOverview caseId={caseId} />,
    removable: false,
    key: TabKey.CASE_OVERVIEW,
    icon: <BriefcaseBusiness />
  },
  // {
  //   name: "Pre Litigation",
  //   value: TabKey.PRE_LITIGATION,
  //   component: <PreLitigationTabs caseId={caseId} />,
  //   removable: false,
  //   key: TabKey.PRE_LITIGATION,
  //   icon: <HandshakeIcon />
  // },
  {
    name: "Litigation",
    value: TabKey.LITIGATION,
    component: <LitigationTabs caseId={caseId} />,
    removable: false,
    key: TabKey.LITIGATION,
    icon: <Briefcase />
  },
  {
    name: "Negotiations & Settlement",
    value: TabKey.SETTLEMENT,
    component: <SettlementAndNegotiation caseId={caseId} />,
    removable: false,
    key: TabKey.SETTLEMENT,
    icon: <DollarSign />
  },
];

// const SCROLL_AMOUNT = 200;

export function DocumentTabs({ isLoadingFiles, caseId }: DocumentTabsProps) {
  const {
    selectedDocuments,
    activeDocumentId,
    setSelectedDocuments,
    setActiveDocumentId,
    isDemandGeneratorActive,
    setIsDemandGeneratorActive,
  } = useDocumentContext();

  const searchParams = useSearchParams();
  const router = useRouter();
  const initialMainTab = searchParams?.get("mainTab") || TabKey.CASE_OVERVIEW;
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false);

  const [showDemandGenerator, setShowDemandGenerator] = useState(false);
  const [activeTab, setActiveTab] = useState<ActiveTab>({
    value: initialMainTab,
    key: initialMainTab as TabKey,
  });

  const [tabs, setTabs] = useState<Tab[]>(() => getDefaultTabs(caseId));

  useEffect(() => {
    const mainTab = searchParams?.get("mainTab");
    if (mainTab) {
      const tab = tabs.find((t) => t.value === mainTab);
      if (tab) {
        setActiveTab({
          value: tab.value,
          key: tab.key,
        });

        if (tab.key === TabKey.FILE_VIEWER) {
          setActiveDocumentId(tab.value);
        } else {
          setActiveDocumentId(null);
          if (tab.value === TabKey.DEMAND_GENERATOR) {
            setShowDemandGenerator(true);
            setIsDemandGeneratorActive(true);
          }
        }
      }
    }
  }, [searchParams, tabs, setActiveDocumentId, setIsDemandGeneratorActive]);

  const scrollToLastTab = () => {
    const container = document.querySelector("#tabs-scroll-container");
    const tabsList = container?.querySelector('[role="tablist"]');
    if (!tabsList) return;

    // Get the last tab element
    const lastTab = tabsList.lastElementChild;
    if (!lastTab) return;

    // Scroll the last tab into view with smooth animation
    lastTab.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "end" });
  };

  useEffect(() => {
    console.log("Effect triggered. Selected documents:", selectedDocuments);
    console.log("Creating document tabs with documents:", selectedDocuments);
    const documentTabs = selectedDocuments.map((item) => {
      console.log("Creating tab for document:", item);
      return {
        name: item.name,
        value: item.id.toString(),
        key: TabKey.FILE_VIEWER,
        component: (
          <FileViewer
            activeDocument={item}
            isLoading={isLoadingFiles}
            selectedDocuments={selectedDocuments}
          />
        ),
        removable: true,
      };
    });

    const newTabs = [...getDefaultTabs(caseId), ...documentTabs];
    console.log("Setting new tabs:", newTabs);
    setTabs(newTabs);

    // If there are new document tabs, scroll to show the last one
    if (documentTabs.length > 0) {
      // Use setTimeout to ensure the DOM has updated
      setTimeout(scrollToLastTab, 100);
    }
  }, [selectedDocuments, showDemandGenerator, caseId, isLoadingFiles]);

  useEffect(() => {
    if (isDemandGeneratorActive) setShowDemandGenerator(true);
  }, [isDemandGeneratorActive]);

  useEffect(() => {
    if (activeDocumentId === null) {
      setActiveTab({
        value: TabKey.CASE_OVERVIEW,
        key: TabKey.CASE_OVERVIEW,
      });
    }
  }, [activeDocumentId]);

  // const scrollTabs = (direction: "left" | "right") => {
  //   const container = document.querySelector("#tabs-scroll-container");
  //   const tabsList = container?.querySelector('[role="tablist"]');
  //   if (!tabsList) return;

  //   const scrollAmount = direction === "left" ? -SCROLL_AMOUNT : SCROLL_AMOUNT;
  //   tabsList.scrollBy({
  //     left: scrollAmount,
  //     behavior: "smooth",
  //   });
  // };

  const handleTabChange = (value: string) => {
    console.log("[DocumentTabs] handleTabChange called with value:", value);
    console.log("[DocumentTabs] Current active tab:", activeTab);
    console.log("[DocumentTabs] Current search params:", Object.fromEntries(searchParams?.entries() || []));
    
    const tab = tabs.find((tab) => tab.value === value);
    if (!tab) {
      console.warn("[DocumentTabs] No tab found for value:", value);
      return;
    }

    // Create new URL params while cleaning up irrelevant ones
    const newParams = new URLSearchParams();
    newParams.set("mainTab", value);

    // Only keep the relevant secondary tab parameter based on the main tab
    const secondaryTabParam = searchParams?.get(getSecondaryTabParam(value));
    if (secondaryTabParam) {
      newParams.set(getSecondaryTabParam(value), secondaryTabParam);
    }

    console.log("[DocumentTabs] Setting new URL params:", newParams.toString());
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });

    // Always update the active tab first
    console.log("[DocumentTabs] Updating active tab to:", { value: tab.value, key: tab.key });
    setActiveTab({
      value: tab.value,
      key: tab.key,
    });

    // Handle different tab types
    if (tab.key === TabKey.FILE_VIEWER) {
      const docId = tab.value;
      if (docId) {
        const document = selectedDocuments.find((doc) => doc.id === docId);
        console.log("[DocumentTabs] Found document for file viewer tab:", document);

        if (document) {
          setActiveDocumentId(docId);
        }
      }
    } else {
      // For non-file tabs
      setActiveDocumentId(null);

      // Handle special cases
      if (tab.value === TabKey.DEMAND_GENERATOR) {
        setShowDemandGenerator(true);
        setIsDemandGeneratorActive(true);
      } else {
        setShowDemandGenerator(false);
        setIsDemandGeneratorActive(false);
      }
    }
    console.log("[DocumentTabs] Tab change complete");
  };

  // Helper function to get the correct secondary tab parameter name
  const getSecondaryTabParam = (mainTab: string): string => {
    switch (mainTab) {
      case TabKey.CASE_OVERVIEW:
        return "overviewTab";
      case TabKey.PRE_LITIGATION:
        return "preLitigationTab";
      case TabKey.LITIGATION:
        return "litigationTab";
      case TabKey.SETTLEMENT:
        return "settlementTab";
      default:
        return "";
    }
  };

  // // Render the current tab content
  // const renderTabContent = (tab: Tab) => {
  //   if (tab.key === TabKey.FILE_VIEWER) {
  //     const document = selectedDocuments.find(doc => doc.id.toString() === tab.value);
  //     if (document) {
  //       return (
  //         <FileViewer
  //           key={document.id}
  //           activeDocument={document}
  //           isLoading={isLoadingFiles}
  //           selectedDocuments={selectedDocuments}
  //         />
  //       );
  //     }
  //   }
  //   return tab.component;
  // };

  // Effect to update tabs when selected documents change
  useEffect(() => {

    const documentTabs = selectedDocuments.map((doc) => ({
      name: doc.name,
      value: doc.id.toString(),
      key: TabKey.FILE_VIEWER,
      component: (
        <FileViewer
          activeDocument={doc}
          isLoading={isLoadingFiles}
          selectedDocuments={selectedDocuments}
        />
      ),
      removable: true,
    }));

    const newTabs = [...getDefaultTabs(caseId), ...documentTabs];
    setTabs(newTabs);
  }, [selectedDocuments, caseId, isLoadingFiles]);

  // Effect to monitor URL parameter changes
  useEffect(() => {
    console.log("[DocumentTabs] URL params changed:", Object.fromEntries(searchParams?.entries() || []));
    const mainTab = searchParams?.get("mainTab");
    if (mainTab) {
      console.log("[DocumentTabs] Found mainTab in URL:", mainTab);
      const tab = tabs.find((t) => t.value === mainTab);
      if (tab) {
        console.log("[DocumentTabs] Setting active tab from URL to:", tab.value);
        setActiveTab({
          value: tab.value,
          key: tab.key,
        });

        if (tab.key === TabKey.FILE_VIEWER) {
          setActiveDocumentId(tab.value);
        } else {
          setActiveDocumentId(null);
          if (tab.value === TabKey.DEMAND_GENERATOR) {
            setShowDemandGenerator(true);
            setIsDemandGeneratorActive(true);
          }
        }
      } else {
        console.warn("[DocumentTabs] No matching tab found for mainTab:", mainTab);
      }
    }
  }, [searchParams, tabs, setActiveDocumentId, setIsDemandGeneratorActive]);

  // Effect to monitor active document changes
  useEffect(() => {
    console.log("[DocumentTabs] Active document changed:", activeDocumentId);
    if (activeDocumentId) {
      const document = selectedDocuments.find(
        (doc) => doc.id === activeDocumentId
      );
      console.log("[DocumentTabs] Found document for active ID:", document);
      if (document) {
        console.log("[DocumentTabs] Setting active tab for document");
        setActiveTab({
          value: activeDocumentId.toString(),
          key: TabKey.FILE_VIEWER,
        });
      }
    }
  }, [activeDocumentId, selectedDocuments]);

  // Effect to handle demand generator state
  useEffect(() => {
    if (isDemandGeneratorActive) {
      setShowDemandGenerator(true);
      setActiveTab({
        value: TabKey.DEMAND_GENERATOR,
        key: TabKey.DEMAND_GENERATOR,
      });
    }
  }, [isDemandGeneratorActive]);

  const handleCloseTab = (
    tab: Tab,
    e: React.MouseEvent | React.KeyboardEvent
  ) => {
    e.stopPropagation();
    e.preventDefault();

    if (tab.value === TabKey.DEMAND_GENERATOR) {
      setShowDemandGenerator(false);
      setIsDemandGeneratorActive(false);
    } else {
      // First, find the document we're closing
      const docToClose = selectedDocuments.find(
        (d) => d.id.toString() === tab.value
      );
      console.log("Document to close:", docToClose);

      if (docToClose) {
        // Remove the document from selectedDocuments
        const remainingDocs = selectedDocuments.filter(
          (d) => d.id.toString() !== tab.value
        );
        console.log("Remaining documents:", remainingDocs);
        setSelectedDocuments(remainingDocs);

        // Reset active document if we're closing the active one
        if (activeDocumentId?.toString() === tab.value) {
          setActiveDocumentId(null);
          setActiveTab({
            value: TabKey.CASE_OVERVIEW,
            key: TabKey.CASE_OVERVIEW,
          });
        }
      }
    }
  };

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey)) || 
          (e.key === 'k' && e.getModifierState('Meta'))) {
        e.preventDefault();
        setIsCommandPaletteOpen(prev => !prev);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  return (
    <div className="flex flex-col h-full">
      <CommandPalette 
        isOpen={isCommandPaletteOpen}
        onClose={() => setIsCommandPaletteOpen(false)}
        // caseId={caseId}
        setActiveTab={handleTabChange}
      />
      <div className="flex items-center gap-2">
        <div className="w-full relative" id="tabs-scroll-container">
          <Tabs
            value={activeTab.value}
            className="w-full"
            onValueChange={handleTabChange}
            defaultValue={initialMainTab}
          >
            <div className="relative flex items-center">
              <div
                className="mx-8 w-full"
                style={{ width: "calc(100% - 70px)" }}
              >
                <TabsList className="w-full overflow-x-auto flex-nowrap whitespace-nowrap scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 bg-'rgba(236, 236, 238, 0.40)'">
                  {tabs.map((tab) => (
                    <TabsTrigger
                      key={tab.name}
                      value={tab.value}
                      className="px-4 flex-shrink-0"
                    >
                      <span className="flex items-center gap-2">
                        {tab.icon}
                        {tab.name}
                        {tab.removable && (
                          <div
                            role="button"
                            tabIndex={0}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleCloseTab(tab, e);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                e.stopPropagation();
                                handleCloseTab(tab, e);
                              }
                            }}
                            className="ml-2 hover:bg-gray-200 rounded p-1 h-auto cursor-pointer"
                          >
                            <X className="h-3 w-3" />
                          </div>
                        )}
                      </span>
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
            </div>

            <div className="flex-1 h-[calc(100%-50px)] overflow-y-auto overflow-x-hidden">
              {tabs.map((tab) => (
                <TabsContent key={tab.value} value={tab.value} className="h-full">
                  {tab.key === TabKey.FILE_VIEWER ? (
                    <div className="h-full">
                      <FileViewer
                        key={tab.value}
                        activeDocument={selectedDocuments.find(
                          (doc) => doc.id.toString() === tab.value
                        )}
                        isLoading={isLoadingFiles}
                        selectedDocuments={selectedDocuments}
                      />
                    </div>
                  ) : (
                    tab.component
                  )}
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
