import {
  // Bell,
  // LayoutDashboard,
  Briefcase,
  Megaphone,
  Search,
  LogOut,
  BarChart3,
  Calendar as CalendarIcon,
  Mail,
  User,
  // ChevronDown,
  Loader2,
  Signature,
  Menu,
  X,
  AlarmClock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { RootState } from "@/store";
import { useSelector, useDispatch } from "react-redux";
import { logout, setSelectedNavigation } from "@/store";
// import { NavigationButton } from "./navigation-button";
import Link from "next/link";
import { useState, useCallback, useEffect, useMemo, useRef } from "react";
import { NotificationsDropdown } from "./notifications";
import debounce from "lodash/debounce";
import { useCasesGlobal } from "@/services/caseService";
import { CaseResponse } from "@/type/dashboard";
import { parseISO, format } from "date-fns";

// Define navigation item type
type NavigationItem = {
  href: string;
  icon: React.ElementType;
  label: string;
};

// Define navigation items without analytics
const BASE_NAVIGATION_ITEMS: NavigationItem[] = [
  // { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
  { href: "/cases", icon: Briefcase, label: "Cases" },
  { href: "/leads", icon: Briefcase, label: "Leads" },
  { href: "/marketing", icon: Megaphone, label: "Marketing" },
  // { href: "/calendar", icon: CalendarIcon, label: "Calendar" },
  { href: "/mails", icon: Mail, label: "Mail" },
  { href: "/esign", icon: Signature, label: "e-Sign" },
  // { href: "/alarms", icon: Bell, label: "Alarms" },
];

// Analytics item that will be conditionally added
const ANALYTICS_ITEM: NavigationItem = { href: "/analytics", icon: BarChart3, label: "Analytics" };

export function Header() {
  const user = useSelector((state: RootState) => state.auth.user);
  const selectedNavigation = useSelector(
    (state: RootState) => state.auth.selectedNavigation
  );
  const globalSearchResults = useSelector((state: RootState) => state.cases.global_search_results);
  const router = useRouter();
  const dispatch = useDispatch();
  const [searchQuery, setSearchQuery] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);

  // Add state for custom context menu
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    caseId: string | null;
  }>({ visible: false, x: 0, y: 0, caseId: null });
  const contextMenuRef = useRef<HTMLDivElement | null>(null);

  // Dynamically create navigation items based on user role
  const NAVIGATION_ITEMS = useMemo(() => {
    // Only add analytics tab for admin users
    if (user?.role === "admin") {
      return [...BASE_NAVIGATION_ITEMS, ANALYTICS_ITEM];
    }
    return BASE_NAVIGATION_ITEMS;
  }, [user?.role]);

  // Use the cases query hook with search params, but only enable it when needed
  const { isLoading: isSearching, refetch } = useCasesGlobal(
    {
      search: searchQuery,
      pageSize: 10,
      page: 1,
    },
    true, // isOrganizationCases = true
    {
      enabled: false // Disable automatic fetching
    }
  );

  // Debounced search function with proper cleanup
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      if (query.trim()) {
        refetch();
      } else {
        setShowResults(false);
      }
    }, 300),
    [] // Empty dependency array since we don't need refetch in deps
  );

  // Handle input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value); // Update search query immediately
    setShowResults(!!value.trim());
    setHighlightedIndex(-1); // Reset on new search
    debouncedSearch(value); // Debounce the actual search
  };

  // Click outside listener
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!(event.target as HTMLElement).closest(".relative.px-4")) {
        setShowResults(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close mobile menu on navigation
  useEffect(() => {
    const handleRouteChange = () => {
      setMobileMenuOpen(false);
      setIsMobileSearchOpen(false);
    };

    window.addEventListener('popstate', handleRouteChange);
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // const getCurrentTabLabel = () => {
  //   const currentTab = NAVIGATION_ITEMS.find(
  //     (item) => item.href === selectedNavigation
  //   );
  //   return currentTab || NAVIGATION_ITEMS[0];
  // };

  // const renderNavigationDropdown = () => {
  //   const currentTab = getCurrentTabLabel();
  //   const Icon = currentTab.icon;

  //   return (
  //     <DropdownMenu>
  //       <DropdownMenuTrigger asChild>
  //         <Button
  //           variant="ghost"
  //           className="flex items-center gap-2 px-4 py-2 text-white hover:bg-white/10 rounded-md hidden md:flex"
  //         >
  //           <Icon size={20} />
  //           <span className="text-sm font-medium">{currentTab.label}</span>
  //           <ChevronDown className="h-4 w-4 opacity-50" />
  //         </Button>
  //       </DropdownMenuTrigger>
  //       <DropdownMenuContent className="w-[200px] bg-white rounded-[10px] border border-[rgba(1,1,1,0.08)] shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] p-2">
  //         {NAVIGATION_ITEMS.map((item) => {
  //           const ItemIcon = item.icon;
  //           return (
  //             <DropdownMenuItem
  //               key={item.href}
  //               className={`flex items-center gap-3 px-3 py-2 cursor-pointer ${
  //                 selectedNavigation === item.href
  //                   ? "bg-[#082321]/10 text-[#082321]"
  //                   : "text-gray-600 hover:bg-gray-100"
  //               }`}
  //               onClick={() => {
  //                 dispatch(setSelectedNavigation(item.href));
  //                 router.push(item.href);
  //               }}
  //             >
  //               <ItemIcon
  //                 size={18}
  //                 className={
  //                   selectedNavigation === item.href
  //                     ? "text-[#082321]"
  //                     : "text-gray-500"
  //                 }
  //               />
  //               <span className="text-sm font-medium">{item.label}</span>
  //             </DropdownMenuItem>
  //           );
  //         })}
  //       </DropdownMenuContent>
  //     </DropdownMenu>
  //   );
  // };

  const renderNavigationButtons = () => {
    return (
      <div className="flex items-center gap-2">
        {NAVIGATION_ITEMS.map((item) => {
          const ItemIcon = item.icon;
          return (
            <Button
              key={item.href}
              variant="ghost"
              className={`flex items-center gap-2 px-3 py-2 text-white hover:bg-white/10 rounded-md ${selectedNavigation === item.href
                  ? "bg-white/10 text-white"
                  : "hover:bg-white/[0.2] hover:text-white"
                }`}
              onClick={() => {
                dispatch(setSelectedNavigation(item.href));
                router.push(item.href);
              }}
            >
              <ItemIcon className="text-white" size={18} />
              <span className="text-sm font-medium text-white">{item.label}</span>
            </Button>
          );
        })}
      </div>
    );
  };

  const onLogout = () => {
    dispatch(logout());
    router.push("/login");
  };

  /**
   * Handles keyboard navigation for the search results.
   * @param e React.KeyboardEvent<HTMLInputElement>
   */
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showResults || !globalSearchResults?.length) return;

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev < globalSearchResults.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev > 0 ? prev - 1 : globalSearchResults.length - 1
      );
    } else if (e.key === "Enter" && highlightedIndex >= 0) {
      const selected = globalSearchResults[highlightedIndex];
      if (selected) {
        router.push(`/dashboard/case-view/${selected.id}`);
        setShowResults(false);
        setSearchQuery("");
        setIsMobileSearchOpen(false);
        setHighlightedIndex(-1);
      }
    }
  };

  /**
   * Handles right-click (context menu) on a case result.
   * Shows a custom context menu at the mouse position.
   * @param e React.MouseEvent<HTMLDivElement>
   * @param caseId string
   */
  const handleCaseContextMenu = (
    e: React.MouseEvent<HTMLDivElement>,
    caseId: string
  ) => {
    e.preventDefault();
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      caseId,
    });
  };

  /**
   * Handles clicking the 'Open in new tab' option in the context menu.
   */
  const handleOpenInNewTab = () => {
    if (contextMenu.caseId) {
      window.open(`/dashboard/case-view/${contextMenu.caseId}`, "_blank", "noopener,noreferrer");
      setContextMenu((prev) => ({ ...prev, visible: false }));
    }
  };

  // Hide context menu on click elsewhere or scroll
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (
        contextMenuRef.current &&
        !contextMenuRef.current.contains(e.target as Node)
      ) {
        setContextMenu((prev) => ({ ...prev, visible: false }));
      }
    };
    const handleScroll = () => setContextMenu((prev) => ({ ...prev, visible: false }));
    if (contextMenu.visible) {
      document.addEventListener("mousedown", handleClick);
      window.addEventListener("scroll", handleScroll, true);
    }
    return () => {
      document.removeEventListener("mousedown", handleClick);
      window.removeEventListener("scroll", handleScroll, true);
    };
  }, [contextMenu.visible]);

  useEffect(() => {
    const el = document.querySelector('.search-highlighted');
    if (el) {
      (el as HTMLElement).scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [highlightedIndex]);

  const renderSearchSection = () => (
    <div className={`relative ${isMobileSearchOpen ? 'w-full' : 'hidden md:block md:w-full'} px-4`}>
      <Search
        className="absolute left-7 top-1/2 transform -translate-y-1/2 text-white/50"
        size={20}
      />
      {isSearching && (
        <Loader2
          className="absolute right-7 top-1/2 transform -translate-y-1/2 text-white/50 animate-spin"
          size={20}
        />
      )}
      <input
        type="text"
        onChange={handleSearchChange}
        onFocus={(e) => setShowResults(!!e.target.value.trim())}
        onKeyDown={handleSearchKeyDown}
        placeholder="Ask Alpha Law Anything"
        className="w-full h-10 bg-white/[0.12] rounded-full pl-10 pr-4 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-white/20 border-none"
      />
      {showResults &&
        ((globalSearchResults && globalSearchResults.length > 0) || isSearching) && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg max-h-[400px] overflow-y-auto z-50">
            {(() => {
              const filteredResults = globalSearchResults || [];
              return filteredResults.map((result: CaseResponse, idx: number) => (
                <div
                  key={result.id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 ${highlightedIndex === idx ? "bg-blue-50 search-highlighted" : ""}`}
                  onClick={() => {
                    router.push(`/dashboard/case-view/${result.id}`);
                    setShowResults(false);
                    setSearchQuery("");
                    setIsMobileSearchOpen(false);
                    setHighlightedIndex(-1);
                  }}
                  onMouseEnter={() => setHighlightedIndex(idx)}
                  onContextMenu={(e) => handleCaseContextMenu(e, result.id)}
                >
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-900 font-medium text-base">
                        {result.name_of_client}
                      </span>
                      <span className="text-sm px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                        {result.id}
                      </span>
                    </div>

                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      {result.accident_date && (
                        <span className="flex items-center gap-1">
                          <CalendarIcon size={14} />
                          {result.accident_date
                            ? format(parseISO(result.accident_date), "MM/dd/yyyy")
                            : "—"}
                        </span>
                      )}
                    </div>

                    {(result.status_display ||
                      result.organization_status_details?.display_name) && (
                        <div className="flex items-center gap-2 mt-1">
                          <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                            {result.organization_status_details?.display_name ||
                              result.status_display}
                          </span>
                        </div>
                      )}
                  </div>
                </div>
              ));
            })()}
            {isSearching && (
              <div className="p-4 text-center text-gray-500">
                <Loader2 className="animate-spin inline mr-2" size={16} />
                Searching...
              </div>
            )}
            {!isSearching &&
              (!globalSearchResults || globalSearchResults.length === 0) &&
              searchQuery && (
                <div className="p-4 text-center text-gray-500">
                  No results found
                </div>
              )}
          </div>
        )}
    </div>
  );

  // Mobile navigation menu
  const renderMobileNav = () => (
    <div className={`fixed inset-0 bg-[#082321] z-50 transition-transform duration-300 ease-in-out ${mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}>
      <div className="flex justify-between items-center p-4 border-b border-white/20">
        <Link href="/dashboard" onClick={() => setMobileMenuOpen(false)}>
          <div className="flex items-center gap-2">
            <Image
              src="/images/Logo.svg"
              alt="Alpha Law Logo"
              width={32}
              height={32}
            />
            <span className="text-white font-termina text-[18px] font-semibold leading-6 tracking-[0.15px]">
              Alpha Law
            </span>
          </div>
        </Link>
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/10"
          onClick={() => setMobileMenuOpen(false)}
        >
          <X size={24} />
        </Button>
      </div>

      <div className="p-4">
        <div className="mb-6">
          {renderSearchSection()}
        </div>

        <div className="space-y-1">
          {NAVIGATION_ITEMS.map((item) => {
            const ItemIcon = item.icon;
            return (
              <Button
                key={item.href}
                variant="ghost"
                className={`w-full justify-start gap-3 px-3 py-3 ${selectedNavigation === item.href
                    ? "bg-white/10 text-white"
                    : "text-white/70 hover:bg-white/5 hover:text-white"
                  }`}
                onClick={() => {
                  dispatch(setSelectedNavigation(item.href));
                  router.push(item.href);
                  setMobileMenuOpen(false);
                }}
              >
                <ItemIcon size={20} />
                <span>{item.label}</span>
              </Button>
            );
          })}
        </div>

        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/20">
          <Button
            variant="ghost"
            className="w-full justify-start gap-3 px-3 py-3 text-white/70 hover:bg-white/5 hover:text-white"
            onClick={onLogout}
          >
            <LogOut size={20} />
            <span>Logout</span>
          </Button>
        </div>
      </div>
    </div>
  );

  /**
   * Renders the user dropdown menu in the header.
   * @returns JSX.Element
   */
  const renderUserDropdown = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="w-10 h-10 p-0 rounded-full bg-white/[0.12] hover:bg-white/[0.2]"
        >
          <User className="text-white h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[280px] bg-white rounded-[10px] border border-[rgba(1,1,1,0.08)] shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] p-4"
        key="dropdown-menu-content"
        align="end"
      >
        <DropdownMenuItem asChild>
          <Link href="/profile" className="w-full">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-[#E8E8E8] flex items-center justify-center">
                <User className="text-[#060216] h-5 w-5" />
              </div>
              <div className="flex flex-col">
                <p className="text-[#060216] text-base font-semibold">
                  {user?.name || "User"}
                </p>
                <p className="text-[#6F6F6F] text-sm">{user?.email}</p>
              </div>
            </div>
          </Link>
        </DropdownMenuItem>

        {user?.role === "admin" && (
          <>
            <div className="my-3 h-[1px] bg-[#E8E8E8]" />
            <DropdownMenuItem asChild>
              <Link href="/organization" className="w-full">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-[#E8E8E8] flex items-center justify-center">
                    <Briefcase className="text-[#060216] h-5 w-5" />
                  </div>
                  <div className="flex flex-col">
                    <p className="text-[#060216] text-base font-semibold">
                      {user?.organization?.name || "N/A"}
                    </p>
                    <p className="text-[#6F6F6F] text-sm">Firm Admin</p>
                  </div>
                </div>
              </Link>
            </DropdownMenuItem>
          </>
        )}

        <div className="my-3 h-[1px] bg-[#E8E8E8]" />

        <DropdownMenuItem className="w-full cursor-pointer" onClick={onLogout}>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-[#E8E8E8] flex items-center justify-center">
              <LogOut className="text-[#060216] h-5 w-5" />
            </div>
            <span className="text-[#060216] text-base font-semibold">
              Logout
            </span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <>
      <header className="sticky top-0 z-40 bg-[#082321] h-16">
        <div className="flex items-center justify-between h-full px-4 md:px-8">
          {/* Logo section - Always visible */}
          <div className="flex items-center">
            <Link
              href="/dashboard"
              className="flex items-center gap-2 hover:opacity-80 transition-opacity"
            >
              <Image
                src="/images/Logo.svg"
                alt="Alpha Law Logo"
                width={32}
                height={32}
              />
              <span
                className="text-white font-termina text-[18px] font-semibold leading-6 tracking-[0.15px] hidden md:block"
                style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
              >
                Alpha Law
              </span>
            </Link>

            {/* Desktop navigation buttons - Hide on mobile */}
            <div className="hidden md:flex md:ml-8 border-l border-white/20 pl-4 overflow-x-auto">
              {renderNavigationButtons()}
              {/* {renderNavigationDropdown()} */}
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center gap-1 md:gap-2">
            {/* Mobile search toggle */}



            <Button
              variant="ghost"
              size="icon"
              className="md:hidden rounded-full hover:bg-white/[0.2]"
              onClick={() => setIsMobileSearchOpen(!isMobileSearchOpen)}
            >
              <Search className="text-white h-5 w-5" />
            </Button>

            {/* Desktop search section */}
            <div className="hidden md:block md:w-64 lg:w-80 xl:w-96 relative">
              {renderSearchSection()}
            </div>

            {/* Alarm button - Show on desktop, hide on smaller mobile */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden sm:flex rounded-full hover:bg-white/[0.2]"
              onClick={() => {
                dispatch(setSelectedNavigation("/alarms"));
                router.push("/alarms");
              }}
            >
              <AlarmClock className="text-white h-8 w-8" />
            </Button>

            {/* Calendar button - Show on desktop, hide on smaller mobile */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden sm:flex rounded-full hover:bg-white/[0.2]"
              onClick={() => router.push("/calendar")}
            >
              <CalendarIcon className="text-white h-5 w-5" />
            </Button>

            {/* Notifications - Always show */}
            <div className="flex-shrink-0">
              <NotificationsDropdown />
            </div>

            {/* User dropdown - Always show */}
            <div className="flex-shrink-0">
              {renderUserDropdown()}
            </div>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden rounded-full hover:bg-white/[0.2]"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu className="text-white h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Mobile search bar - conditionally rendered */}
        {isMobileSearchOpen && (
          <div className="md:hidden px-4 pb-4">
            {renderSearchSection()}
          </div>
        )}
      </header>

      {/* Mobile navigation overlay */}
      {renderMobileNav()}

      {/* Custom context menu for case results */}
      {contextMenu.visible && (
        <div
          ref={contextMenuRef}
          style={{
            position: "fixed",
            top: contextMenu.y,
            left: contextMenu.x,
            zIndex: 10000,
            background: "white",
            borderRadius: 8,
            boxShadow: "0 2px 12px rgba(0,0,0,0.15)",
            minWidth: 160,
            padding: 0,
            overflow: "hidden",
          }}
          tabIndex={-1}
        >
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-gray-900 cursor-pointer"
            style={{ border: "none", background: "none" }}
            onClick={handleOpenInNewTab}
          >
            Open in new tab
          </button>
        </div>
      )}
    </>
  );
}
