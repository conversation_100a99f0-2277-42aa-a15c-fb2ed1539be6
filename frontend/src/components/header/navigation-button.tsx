import { But<PERSON> } from "@/components/ui/button"
import { LucideIcon } from "lucide-react"
import { usePathname } from "next/navigation"

interface NavigationButtonProps {
  href: string
  icon: LucideIcon
  label: string
  onClick: () => void
}

export function NavigationButton({ href, icon: Icon, label, onClick }: NavigationButtonProps) {
  const pathname = usePathname()
  const isActive = pathname === href

  return (
    <Button
      variant="ghost"
      className={`text-white gap-1 rounded-full hover:bg-white/[0.12] hover:text-white ${
        isActive ? 'bg-white/[0.12]' : ''
      }`}
      onClick={onClick}
    >
      <Icon size={20} />
      {label}
    </Button>
  )
} 