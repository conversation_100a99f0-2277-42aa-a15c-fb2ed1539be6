import { MapPin } from 'lucide-react';
import React, { ReactNode, FC } from 'react';

interface AddressComponents {
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
}

export const getGoogleMapsUrl = (address: AddressComponents): string => {
  const addressParts = [
    address.street1,
    address.street2,
    address.city,
    address.state,
    address.zip_code
  ].filter(Boolean);

  return `http://maps.google.com/?q=${addressParts.join(' ')}`;
};

export const formatFullAddress = (address: AddressComponents): string => {
  const parts: string[] = [];

  if (address.street1) parts.push(address.street1);
  if (address.street2) parts.push(address.street2);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.zip_code) parts.push(address.zip_code);

  return parts.join(', ');
};

interface AddressLinkProps {
  address: AddressComponents;
  className?: string;
  children?: ReactNode;
  showIcon?: boolean;
  label?: string;
}

export const AddressLink: FC<AddressLinkProps> = ({
  address,
  className = "",
  children,
  showIcon = false,
  label
}) => {
  if (!address) return <span className="font-Manrope">—</span>;

  // const icon = showIcon && <MapPin className="h-5 w-5 text-gray-500 hover:text-[#1C7B35]" />;
  return (
    <a
      target="_blank"
      href={getGoogleMapsUrl(address)}
      className={`inline-flex items-center gap-2 hover:text-[#1C7B35] ${className}`}
      rel="noopener noreferrer"
    >
      {children || (
        <>
          {showIcon && <MapPin className="h-5 w-5 text-gray-500 hover:text-[#1C7B35]" />}
          {label && <span>{label}</span>}
          {[address.street1, address.street2, address.city, address.state, address.zip_code].some(Boolean) 
            ? [address.street1, address.street2, address.city, address.state, address.zip_code]
                .filter(Boolean)
                .join(" ")
            : "—"
          }
        </>
      )}
    </a>
  );
}; 