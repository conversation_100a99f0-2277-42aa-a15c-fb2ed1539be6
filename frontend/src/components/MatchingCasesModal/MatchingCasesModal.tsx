"use client";

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { MatchingClientCase, MatchingDefendantCase } from '@/services/case-management/matchingCasesService';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarIcon, User, Users } from "lucide-react";
import { formatDate } from "@/lib/utils";

interface MatchingCasesModalProps {
  isOpen: boolean;
  onClose: () => void;
  matchingClients: MatchingClientCase[];
  matchingDefendants: MatchingDefendantCase[];
}

export function MatchingCasesModal({
  isOpen,
  onClose,
  matchingClients,
  matchingDefendants,
}: MatchingCasesModalProps) {
  const hasMatches = matchingClients.length > 0 || matchingDefendants.length > 0;

  const handleViewCase = (caseId: string) => {
    window.open(`/dashboard/case-view/${caseId}`, '_blank');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Matching Cases Found</DialogTitle>
          <DialogDescription>
            {hasMatches 
              ? "We found existing cases that may match the information you entered."
              : "No matching cases found."}
          </DialogDescription>
        </DialogHeader>

        {hasMatches && (
          <Tabs defaultValue={matchingClients.length > 0 ? "clients" : "defendants"} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger 
                value="clients" 
                disabled={matchingClients.length === 0}
                className="flex items-center gap-2"
              >
                <User className="h-4 w-4" />
                Matching Clients ({matchingClients.length})
              </TabsTrigger>
              <TabsTrigger 
                value="defendants" 
                disabled={matchingDefendants.length === 0}
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                Matching Defendants ({matchingDefendants.length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="clients" className="mt-4">
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-4">
                  {matchingClients.map((client) => (
                    <Card key={client.case_id} className="border border-gray-200">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">{client.client_name}</CardTitle>
                        <CardDescription className="flex items-center gap-1">
                          <CalendarIcon className="h-3 w-3" />
                          {client.date_of_birth ? formatDate(client.date_of_birth) : "No DOB"}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <p className="text-sm text-gray-600">{client.case}</p>
                      </CardContent>
                      <CardFooter>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleViewCase(client.case_id)}
                        >
                          View Case
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="defendants" className="mt-4">
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-4">
                  {matchingDefendants.map((defendant) => (
                    <Card key={defendant.case_id} className="border border-gray-200">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">{defendant.defendant_name}</CardTitle>
                        <CardDescription className="flex items-center gap-1">
                          <CalendarIcon className="h-3 w-3" />
                          {defendant.date_of_birth ? formatDate(defendant.date_of_birth) : "No DOB"}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <p className="text-sm text-gray-600">{defendant.case}</p>
                      </CardContent>
                      <CardFooter>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleViewCase(defendant.case_id)}
                        >
                          View Case
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        )}
      </DialogContent>
    </Dialog>
  );
} 