"use client"

import * as z from "zod"
import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { format, parse, isValid } from "date-fns"
import caseCreationService from "@/services/case-management/createCase"
import { LEAD_SOURCE_CHOICES, INCIDENT_TYPE_CHOICES, MEDICAL_STATUS_CHOICES } from "@/type/lead/leadTypes"
import { USStates, USStatesLabels } from "@/constants/commont"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import { PostCaseCreationDialog } from "../caseCreation/PostCaseCreationDialog"
import { useOrganizationCaseStatuses } from "@/services/organizationService"

const formSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone is required"),
  source_type: z.string().min(1, "Source is required"),
  state: z.string().min(1, "State is required"),
  medical_status: z.string().min(1, "Medical status is required"),
  previous_attorney: z.string().optional(),
  has_insurance: z.boolean().default(false),
  incident_type: z.string().min(1, "Case type is required"),
  incident_date: z.string().min(1, "Incident date is required")
})

type FormData = z.infer<typeof formSchema>

const parseDate = (value: string): Date | null => {
  try {
    // Try parsing DD-MM-YYYY format
    const date = parse(value, 'dd-MM-yyyy', new Date())
    if (isValid(date) && date.getFullYear() > 1900 && date.getFullYear() < 2100) {
      return date
    }
  } catch {
    // If parsing fails, return null
  }
  return null
}

export default function CreateCaseForm() {
  const router = useRouter()
  const [date, setDate] = useState<Date | undefined>()
  const [showPostCreateDialog, setShowPostCreateDialog] = useState(false)
  const [createdCaseId, setCreatedCaseId] = useState<string>("")
  const createCase = caseCreationService.useCreateCase()
  const { data: orgStatuses } = useOrganizationCaseStatuses()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      source_type: '',
      state: USStates.WA,
      medical_status: '',
      previous_attorney: '',
      has_insurance: false,
      incident_type: 'auto_accident',
      incident_date: ''
    }
  })

  // Reset form when dialog is closed
  useEffect(() => {
    if (!showPostCreateDialog) {
      form.reset({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        source_type: '',
        state: USStates.WA,
        medical_status: '',
        previous_attorney: '',
        has_insurance: false,
        incident_type: 'auto_accident',
        incident_date: ''
      });
      setDate(undefined);
    }
  }, [showPostCreateDialog, form]);

  const onSubmit = async (data: FormData) => {
    if (!date) {
      form.setError("incident_date", {
        type: "manual",
        message: "Incident date is required"
      })
      return
    }

    if(date && date > new Date()) {
      form.setError("incident_date", {
        type: "manual",
        message: "Incident date cannot be in the future"
      })
      return
    }

    try {
      const formattedDate = format(date, "yyyy-MM-dd")
      
      const intakeStatus = orgStatuses?.find(status => 
        status.name.toLowerCase() === 'intake' && status.is_active
      )

      const result = await createCase.mutateAsync({
        ...data,
        incident_date: formattedDate,
        organization_status: intakeStatus?.id
      })

      setCreatedCaseId(result.id)
      setShowPostCreateDialog(true)
    } catch (error) {
      console.error('Error creating case:', error)
    }
  }

  // const handleDialogClose = () => {
  //   setShowPostCreateDialog(false)
  //   console.log(createdCaseId,"createdCaseId")
  //   router.push(`/dashboard/case-view/${createdCaseId}`)
  // }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Basic Information Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name*</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ''} placeholder="Enter First Name" className="h-12" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name*</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ''} placeholder="Enter Last Name" className="h-12" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="source_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Source*</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || ''}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <SelectValue placeholder="Select Source" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {LEAD_SOURCE_CHOICES.map((source) => (
                          <SelectItem key={source.value} value={source.value}>
                            {source.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone*</FormLabel>
                    <FormControl>
                      <Input type="tel" {...field} value={field.value || ''} placeholder="Enter Phone Number" className="h-12" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email*</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} value={field.value || ''} placeholder="Enter email" className="h-12" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Incident Summary Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Incident Summary</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormItem>
                <FormLabel>Incident Date*</FormLabel>
                <FormControl>
                  <CustomDateInput
                    value={form.getValues("incident_date")}
                    onChange={(value) => {
                      const parsedDate = parseDate(value);
                      if (parsedDate) {
                        setDate(parsedDate);
                        form.setValue("incident_date", value, {
                          shouldValidate: true,
                          shouldDirty: true,
                        });
                      }
                    }}
                    maxDate={new Date()}
                    error={!!form.formState.errors.incident_date}
                    onError={(message) => {
                      form.setError("incident_date", {
                        type: "manual",
                        message: message
                      });
                    }}
                  />
                </FormControl>
                {form.formState.errors.incident_date && (
                  <FormMessage>
                    {form.formState.errors.incident_date.message}
                  </FormMessage>
                )}
              </FormItem>

              <FormField
                control={form.control}
                name="previous_attorney"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Previous Attorney</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ''} placeholder="Enter Previous Attorney" className="h-12" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="incident_type"
                defaultValue="auto_accident"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type of Incident/Injury*</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || 'auto_accident'}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <SelectValue placeholder="Select Injury Type">
                            {INCIDENT_TYPE_CHOICES.find(type => type.value === field.value)?.label || 'Auto Injury'}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {INCIDENT_TYPE_CHOICES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="medical_status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Medical Treatment Status*</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || ''}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <SelectValue placeholder="Select Treatment Status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {MEDICAL_STATUS_CHOICES.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="state"
                defaultValue={USStates.WA}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>State*</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || USStates.WA}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <SelectValue placeholder="Select State">
                            {USStatesLabels[field.value as USStates] || 'Washington'}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(USStatesLabels).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="has_insurance"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2 pt-8">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value || false}
                        onChange={field.onChange}
                        className="h-4 w-4 rounded border-gray-300"
                      />
                    </FormControl>
                    <FormLabel className="font-normal">Has Insurance</FormLabel>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              className="min-w-[120px] rounded-full"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="min-w-[120px] rounded-full bg-[#6750A4] hover:bg-[#5c4794]"
              disabled={createCase.isPending}
            >
              {createCase.isPending ? "Creating..." : "Create Case"}
            </Button>
          </div>
        </form>
      </Form>

      <PostCaseCreationDialog
        open={showPostCreateDialog}
        onOpenChange={setShowPostCreateDialog}
        caseId={createdCaseId}
      />
    </>
  )
}