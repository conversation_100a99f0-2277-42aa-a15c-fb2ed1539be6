"use client";

import React, { useState, useEffect } from "react";
import { Input } from "./input";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";

interface CustomTimeInputProps {
  value?: string; // Expected in 24-hour format (HH:mm)
  onChange?: (value: string) => void; // Returns value in 24-hour format (HH:mm)
  error?: boolean;
  onError?: (message: string) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
}

export function CustomTimeInput({
  value,
  onChange,
  error,
  className,
  disabled,
  placeholder,
}: CustomTimeInputProps) {
  // HTML time input uses 24h format without AM/PM,
  // but we want to work with and display 12h format with AM/PM to the user
  const [timeValue, setTimeValue] = useState<string>("");
  const [period, setPeriod] = useState<"AM" | "PM">("AM");

  // Convert 24h time to HTML time input value and AM/PM
  useEffect(() => {
    if (value) {
      try {
        // Extract hour for AM/PM determination
        const [hourStr] = value.split(':');
        const hour = parseInt(hourStr, 10);
        
        // Determine period
        const newPeriod = hour >= 12 ? "PM" : "AM";
        setPeriod(newPeriod);
        
        // HTML time input value: keep original time, it needs 24h format
        setTimeValue(value);
      } catch (e) {
        console.error("Error parsing time:", e);
      }
    }
  }, [value]);

  // Handle time input change
  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTimeValue = e.target.value;
    setTimeValue(newTimeValue);
    
    if (newTimeValue) {
      try {
        // Extract hour to determine if we should change AM/PM
        const [hourStr] = newTimeValue.split(':');
        const hour = parseInt(hourStr, 10);
        
        // If changing from AM hours to PM hours or vice versa,
        // update the period
        if (hour >= 12 && period === "AM") {
          setPeriod("PM");
        } else if (hour < 12 && period === "PM") {
          setPeriod("AM");
        }
        
        // Pass the raw 24h time value to onChange
        onChange?.(newTimeValue);
      } catch (e) {
        console.error("Error handling time change:", e);
      }
    }
  };

  // Handle period change (AM/PM)
  const handlePeriodChange = (newPeriod: "AM" | "PM") => {
    if (newPeriod === period) return;
    
    setPeriod(newPeriod);
    
    if (timeValue) {
      try {
        // Split the current time value
        const [hourStr, minutes] = timeValue.split(':');
        let hour = parseInt(hourStr, 10);
        
        // Convert hour based on period change
        if (newPeriod === "AM" && hour >= 12) {
          // PM to AM: subtract 12 hours (except for 12PM → 12AM)
          hour = hour === 12 ? 0 : hour - 12;
        } else if (newPeriod === "PM" && hour < 12) {
          // AM to PM: add 12 hours (except for 12AM → 12PM)
          hour = hour === 0 ? 12 : hour + 12;
        }
        
        // Create new time value in 24h format
        const newTimeValue = `${hour.toString().padStart(2, '0')}:${minutes}`;
        setTimeValue(newTimeValue);
        
        // Pass the new 24h time value to onChange
        onChange?.(newTimeValue);
      } catch (e) {
        console.error("Error handling period change:", e);
      }
    }
  };

  return (
    <div className="flex w-full gap-2">
      <Input
        type="time"
        value={timeValue}
        onChange={handleTimeChange}
        className={cn(
          "flex-1 bg-white",
          error && "border-red-500 focus-visible:ring-red-500",
          className
        )}
        disabled={disabled}
        placeholder={placeholder}
      />
      <Select 
        value={period} 
        onValueChange={(value) => handlePeriodChange(value as "AM" | "PM")}
        disabled={disabled}
      >
        <SelectTrigger className="w-[80px] bg-white">
          <SelectValue placeholder={period} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="AM">AM</SelectItem>
          <SelectItem value="PM">PM</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
} 