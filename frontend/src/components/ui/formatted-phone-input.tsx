import React, { InputHTMLAttributes, FC, ChangeEvent } from 'react';
import { Input } from './input';

interface FormattedPhoneInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  error?: boolean;
  onChange: (value: string) => void;
  value: string;
}

export const FormattedPhoneInput: FC<FormattedPhoneInputProps> = ({
  error,
  onChange,
  value,
  ...props
}) => {
  const formatPhoneNumber = (input: string) => {
    // Remove all non-digits
    const cleaned = input.replace(/\D/g, '');
    
    // Limit to 10 digits
    const limited = cleaned.slice(0, 10);
    
    // Format as XXX-XXX-XXXX
    if (limited.length >= 3) {
      let formatted = limited.slice(0, 3);
      if (limited.length >= 6) {
        formatted += "-" + limited.slice(3, 6);
        if (limited.length >= 7) {
          formatted += "-" + limited.slice(6);
        }
      } else if (limited.length > 3) {
        formatted += "-" + limited.slice(3);
      }
      return formatted;
    }
    
    return limited;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    onChange(formatted);
  };

  return (
    <Input
      {...props}
      value={value}
      onChange={handleChange}
      placeholder="XXX-XXX-XXXX"
      maxLength={12} // 10 digits + 2 hyphens
      className={`${error ? 'border-red-500' : ''} ${props.className || ''}`}
    />
  );
}; 