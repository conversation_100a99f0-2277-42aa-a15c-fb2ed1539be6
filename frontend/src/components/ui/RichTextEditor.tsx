import React, { use<PERSON><PERSON>back, useMemo, useEffect, RefObject, ReactNode, CSSProperties, ComponentType, MouseEvent, KeyboardEvent, useRef } from 'react'
import isHotkey from 'is-hotkey'
import { Editable, withReact, useSlate, Slate, ReactEditor } from 'slate-react'
import {
  Editor,
  Transforms,
  createEditor,
  Descendant,
  Element as SlateElement,
  BaseEditor,
  Node,
} from 'slate'
import { withHistory, HistoryEditor } from 'slate-history'
import { Bold, Italic, Underline, Code, Quote, Heading1, Heading2, AlignLeft, AlignCenter, AlignRight, AlignJustify, ListOrdered, List } from 'lucide-react'
import styled from '@emotion/styled'
import { richTextToString, stringToRichText } from '@/utils/richTextUtils'

// Custom types
type TextAlign = 'left' | 'center' | 'right' | 'justify';

export type CustomElement = { 
  type: string; 
  align?: TextAlign; 
  children: CustomText[];
  parent?: CustomElement;  // Add parent property
}

export type CustomText = { 
  text: string; 
  [key: string]: boolean | string;  // Add index signature
}

declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor & HistoryEditor & { disabled?: boolean }
    Element: CustomElement
    Text: CustomText
  }
}

const HOTKEYS: { [key: string]: string } = {
  'mod+b': 'bold',
  'mod+i': 'italic',
  'mod+u': 'underline',
  'mod+`': 'code',
}

const LIST_TYPES = ['numbered-list', 'bulleted-list']
const TEXT_ALIGN_TYPES = ['left', 'center', 'right', 'justify']

const DEFAULT_INITIAL_VALUE: Descendant[] = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
]

// Update ElementAttributes to match Slate's RenderElementProps
interface ElementAttributes {
  'data-slate-node': 'element';
  'data-slate-inline'?: true;
  'data-slate-void'?: true;
  dir?: 'rtl';
  ref: RefObject<HTMLElement>;
  style?: CSSProperties;
}

// Update ElementProps to extend RenderElementProps
interface ElementProps {
  attributes: ElementAttributes;
  children: ReactNode;
  element: CustomElement;
}

// Update LeafProps to match Slate's RenderLeafProps
interface LeafProps {
  attributes: {
    'data-slate-leaf': true;
  };
  children: ReactNode;
  leaf: CustomText;
}

const Element = ({ attributes, children, element }: ElementProps) => {
  const style = { textAlign: element.align as TextAlign }
  
  switch (element.type) {
    case 'block-quote':
      return (
        <blockquote style={style} {...attributes} ref={attributes.ref as RefObject<HTMLQuoteElement>}>
          {children}
        </blockquote>
      )
    case 'bulleted-list':
      return (
        <ul 
          style={{ ...style, listStyleType: 'disc', paddingLeft: '1.5em', margin: '8px 0' }} 
          {...attributes} 
          ref={attributes.ref as RefObject<HTMLUListElement>}
        >
          {children}
        </ul>
      )
    case 'heading-one':
      return (
        <h1 style={style} {...attributes} ref={attributes.ref as RefObject<HTMLHeadingElement>}>
          {children}
        </h1>
      )
    case 'heading-two':
      return (
        <h2 style={style} {...attributes} ref={attributes.ref as RefObject<HTMLHeadingElement>}>
          {children}
        </h2>
      )
    case 'list-item':
      return (
        <li 
          style={{ ...style, margin: '4px 0' }} 
          {...attributes} 
          ref={attributes.ref as RefObject<HTMLLIElement>}
        >
          {children}
        </li>
      )
    case 'numbered-list':
      return (
        <ol 
          style={{ ...style, listStyleType: 'decimal', paddingLeft: '1.5em', margin: '8px 0' }} 
          {...attributes} 
          ref={attributes.ref as RefObject<HTMLOListElement>}
        >
          {children}
        </ol>
      )
    default:
      return (
        <p style={style} {...attributes} ref={attributes.ref as RefObject<HTMLParagraphElement>}>
          {children}
        </p>
      )
  }
}

const Leaf = ({ attributes, children, leaf }: LeafProps) => {
  if (leaf.bold) {
    children = <strong>{children}</strong>
  }

  if (leaf.italic) {
    children = <em>{children}</em>
  }

  if (leaf.underline) {
    children = <u>{children}</u>
  }

  if (leaf.code) {
    children = <code>{children}</code>
  }

  return <span {...attributes}>{children}</span>
}

const toggleBlock = (editor: Editor, format: string) => {
  const isActive = isBlockActive(editor, format)
  const isList = LIST_TYPES.includes(format)
  const isAlign = TEXT_ALIGN_TYPES.includes(format)

  if (isList) {
    // Get the current list type if any
    const currentListType = LIST_TYPES.find(type => isBlockActive(editor, type))
    
    // If we're toggling off the active list type
    if (isActive) {
      // First unwrap the list
      Transforms.unwrapNodes(editor, {
        match: n => 
          !Editor.isEditor(n) && 
          SlateElement.isElement(n) && 
          LIST_TYPES.includes(n.type),
        split: true,
      })

      // Then convert list items back to paragraphs
      Transforms.setNodes(editor, {
        type: 'paragraph',
        align: undefined
      }, {
        match: n => 
          !Editor.isEditor(n) && 
          SlateElement.isElement(n) && 
          n.type === 'list-item',
      })
      return
    }
    
    // If there's a different active list type, unwrap it first
    if (currentListType && currentListType !== format) {
      Transforms.unwrapNodes(editor, {
        match: n => 
          !Editor.isEditor(n) && 
          SlateElement.isElement(n) && 
          n.type === currentListType,
        split: true,
      })
    }

    // If we're activating a list type and not already in a list
    // Convert nodes to list-items if they aren't already
    Transforms.setNodes(editor, {
      type: 'list-item',
    }, {
      match: n => 
        !Editor.isEditor(n) && 
        SlateElement.isElement(n) && 
        n.type !== 'list-item',
    })

    // Wrap in the list type container (ol or ul)
    const block = { type: format, children: [] }
    Transforms.wrapNodes(editor, block)
    return
  }

  // Handle alignment
  if (isAlign) {
    Transforms.setNodes(editor, {
      align: isActive ? undefined : format,
    })
    return
  }

  // Handle other block types
  Transforms.setNodes(editor, {
    type: isActive ? 'paragraph' : format,
  })
}

const toggleMark = (editor: Editor, format: string) => {
  const isActive = isMarkActive(editor, format)

  if (isActive) {
    Editor.removeMark(editor, format)
  } else {
    Editor.addMark(editor, format, true)
  }
}

const isBlockActive = (editor: Editor, format: string) => {
  const { selection } = editor
  if (!selection) return false

  if (TEXT_ALIGN_TYPES.includes(format)) {
    const [match] = Array.from(
      Editor.nodes(editor, {
        at: Editor.unhangRange(editor, selection),
        match: n =>
          !Editor.isEditor(n) &&
          SlateElement.isElement(n) &&
          (n as CustomElement).align === format,
      })
    )
    return !!match
  }

  // For lists, check both the list container and list items
  if (LIST_TYPES.includes(format)) {
    // First check if we're directly in a list of this type
    const [listMatch] = Array.from(
      Editor.nodes(editor, {
        at: Editor.unhangRange(editor, selection),
        match: n =>
          !Editor.isEditor(n) &&
          SlateElement.isElement(n) &&
          n.type === format,
      })
    ) || [null]
    
    if (listMatch) return true
    
    // Then check if we're in a list-item whose parent is of this type
    const [listItemMatch] = Array.from(
      Editor.nodes(editor, {
        at: Editor.unhangRange(editor, selection),
        match: n =>
          !Editor.isEditor(n) &&
          SlateElement.isElement(n) &&
          n.type === 'list-item',
      })
    ) || [null]
    
    if (listItemMatch) {
      const [parentNode] = Editor.parent(editor, selection.focus.path)
      return SlateElement.isElement(parentNode) && parentNode.type === format
    }
    
    return false
  }

  // For other block types
  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n.type === format,
    })
  ) || [null]

  return !!match
}

const isMarkActive = (editor: Editor, format: string) => {
  const marks = Editor.marks(editor)
  return marks ? marks[format] === true : false
}

interface MarkButtonProps {
  format: string
  icon: ComponentType<{ className?: string }>
}

const Button = styled('button')({
    padding: '4px 8px',
    cursor: 'pointer',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&:hover': {
      backgroundColor: '#e2e8f0',
    },
    '&[data-active="true"]': {
      backgroundColor: '#e2e8f0',
      boxShadow: 'inset 0 0 0 1px #cbd5e1',
      color: '#3b82f6',
    },
    '&:disabled': {
      opacity: 0.5,
      cursor: 'not-allowed',
    },
    type: 'button',
})

const MarkButton = ({ format, icon: Icon }: MarkButtonProps) => {
  const editor = useSlate()
  const isActive = isMarkActive(editor, format)
  const isDisabled = editor.disabled
  return (
    <Button
      type="button"
      data-active={isActive}
      disabled={isDisabled}
      onMouseDown={(event: MouseEvent<HTMLButtonElement>) => {
        if (isDisabled) return
        event.preventDefault()
        event.stopPropagation()
        toggleMark(editor, format)
      }}
      onClick={(event: MouseEvent<HTMLButtonElement>) => {
        event.preventDefault()
        event.stopPropagation()
      }}
    >
      <Icon className={`h-4 w-4 ${isDisabled ? 'opacity-50' : ''}`} />
    </Button>
  )
}

interface BlockButtonProps {
  format: string
  icon: ComponentType<{ className?: string }>
}

const BlockButton = ({ format, icon: Icon }: BlockButtonProps) => {
  const editor = useSlate()
  const isActive = isBlockActive(editor, format)
  const isDisabled = editor.disabled
  return (
    <Button
      type="button"
      data-active={isActive}
      disabled={isDisabled}
      onMouseDown={(event: MouseEvent<HTMLButtonElement>) => {
        if (isDisabled) return
        event.preventDefault()
        event.stopPropagation()
        toggleBlock(editor, format)
      }}
      onClick={(event: MouseEvent<HTMLButtonElement>) => {
        event.preventDefault()
        event.stopPropagation()
      }}
    >
      <Icon className={`h-4 w-4 ${isDisabled ? 'opacity-50' : ''}`} />
    </Button>
  )
}

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  onKeyPress?: (event: KeyboardEvent<HTMLDivElement>) => void;
}

const Toolbar = styled('div')({
  display: 'flex',
  padding: '8px',
  gap: '4px',
  borderBottom: '1px solid #e2e8f0',
  background: '#f8fafc',
  flexWrap: 'wrap',
  '@media (max-width: 640px)': {
    padding: '4px',
    gap: '2px',
  }
})

const StyledEditable = styled(Editable)({
  padding: '16px',
  minHeight: '100px',
  maxHeight: '200px',
  minWidth: '200px',
  width: '100%',
  height: '100%',
  overflow: 'auto',
  wordWrap: 'break-word',
  overflowWrap: 'break-word',
  whiteSpace: 'pre-wrap',
  '&::-webkit-scrollbar': {
    width: '8px',
    height: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#c1c1c1',
    borderRadius: '4px',
    '&:hover': {
      background: '#a8a8a8',
    },
  },
  '&[data-slate-editor="true"][aria-disabled="true"]': {
    cursor: 'not-allowed',
    userSelect: 'none',
    opacity: 0.7,
  },
  '& blockquote': {
    borderLeft: '2px solid #cbd5e1',
    margin: '8px 0',
    padding: '8px 16px',
    color: '#64748b',
  },
  '& h1': {
    fontSize: '1.5em',
    fontWeight: 'bold',
    margin: '16px 0 8px',
  },
  '& h2': {
    fontSize: '1.25em',
    fontWeight: 'bold',
    margin: '14px 0 7px',
  },
  '& code': {
    backgroundColor: '#f1f5f9',
    padding: '2px 4px',
    borderRadius: '4px',
    fontSize: '0.9em',
  },
  '& ul': {
    listStyleType: 'disc',
    paddingLeft: '1.5em',
    margin: '8px 0',
  },
  '& ol': {
    listStyleType: 'decimal',
    paddingLeft: '1.5em',
    margin: '8px 0',
  },
  '& li': {
    margin: '4px 0',
  },
  '@media (max-width: 640px)': {
    padding: '12px',
    fontSize: '0.95em',
  }
})

const withLists = (editor: Editor) => {
  const { normalizeNode } = editor

  editor.normalizeNode = ([node, path]) => {
    if (path.length === 0) {
      // Ensure children are valid list items when parent is a list
      for (const [child, childPath] of Node.children(editor, path)) {
        const type = (child as CustomElement).type
        if (LIST_TYPES.includes(type)) {
          for (const [grandChild, grandChildPath] of Node.children(editor, childPath)) {
            if ((grandChild as CustomElement).type !== 'list-item') {
              Transforms.setNodes(
                editor,
                { type: 'list-item' },
                { at: grandChildPath }
              )
            }
          }
        }
      }
    }

    return normalizeNode([node, path])
  }

  return editor
}

const RichTextEditor = ({ value = "", onChange, disabled = false, placeholder = "Enter some text...", className = "", onKeyPress}: RichTextEditorProps) => {
  const renderElement = useCallback((props: ElementProps) => <Element {...props} />, [])
  const renderLeaf = useCallback((props: LeafProps) => <Leaf {...props} />, [])
  
  // Create a stable reference for the editor
  const editorRef = useRef<Editor | null>(null);
  
  const editor = useMemo(() => {
    const e = withLists(withHistory(withReact(createEditor())));
    e.disabled = disabled;
    editorRef.current = e;
    return e;
  }, [disabled]);

  // Create a stable reference for the current value
  const valueRef = useRef(value);
  valueRef.current = value;

  // Initialize editor with proper value
  const initialValue = useMemo(() => {
    try {
      return stringToRichText(value);
    } catch (error) {
      console.warn('Failed to parse editor value:', error);
      return DEFAULT_INITIAL_VALUE;
    }
  }, [value]);

  // Cleanup function to reset editor state
  useEffect(() => {
    return () => {
      if (editorRef.current) {
        try {
          // Reset selection and history on unmount
          editorRef.current.selection = null;
          editorRef.current.history = { undos: [], redos: [] };
          editorRef.current.children = DEFAULT_INITIAL_VALUE;
        } catch (error) {
          console.warn('Failed to cleanup editor:', error);
        }
      }
    };
  }, []);

  // Handle value updates
  useEffect(() => {
    if (!editorRef.current) return;

    try {
      const newValue = stringToRichText(value);
      const currentValue = editorRef.current.children;
      
      // Only update if content actually changed
      if (JSON.stringify(currentValue) !== JSON.stringify(newValue)) {
        // Ensure editor has at least one valid node
        if (!editorRef.current.children.length) {
          editorRef.current.children = DEFAULT_INITIAL_VALUE;
        }

        // Safely update content
        const start = Editor.start(editorRef.current, []);
        if (start) {
          Transforms.deselect(editorRef.current);
          
          // Delete existing content
          Transforms.delete(editorRef.current, {
            at: {
              anchor: start,
              focus: Editor.end(editorRef.current, []),
            },
          });
          
          // Insert new content
          Transforms.insertNodes(editorRef.current, newValue);
        }
      }
    } catch (error) {
      console.warn('Failed to update editor content:', error);
      // Fallback to default value if update fails
      try {
        Transforms.deselect(editorRef.current);
        Transforms.delete(editorRef.current, {
          at: {
            anchor: { path: [0, 0], offset: 0 },
            focus: { path: [0, 0], offset: 0 },
          },
        });
        Transforms.insertNodes(editorRef.current, DEFAULT_INITIAL_VALUE);
      } catch (fallbackError) {
        console.error('Failed to set fallback value:', fallbackError);
      }
    }
  }, [value]);

  const handleKeyDown = useCallback((event: KeyboardEvent<HTMLDivElement>) => {
    if (disabled) {
      event.preventDefault();
      return;
    }

    if (event.key === 'Enter' && event.shiftKey) {
      event.preventDefault();
    }

    for (const hotkey in HOTKEYS) {
      if (isHotkey(hotkey, event)) {
        event.preventDefault();
        const mark = HOTKEYS[hotkey];
        toggleMark(editorRef.current!, mark);
      }
    }
  }, [disabled]);

  const handleClick = useCallback((event: MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
  }, []);

  const handleSlateChange = useCallback((newValue: Descendant[]) => {
    if (disabled || !editorRef.current) return;
    
    try {
      const newContent = richTextToString(newValue);
      if (newContent !== valueRef.current) {
        onChange(newContent);
      }
    } catch (error) {
      console.warn('Failed to handle editor change:', error);
    }
  }, [disabled, onChange]);

  return (
    <div className="border rounded-md overflow-hidden w-full min-w-[200px]" onClick={handleClick}>
      <Slate 
        editor={editor} 
        initialValue={initialValue}
        onChange={handleSlateChange}
      >
        <Toolbar>
          <MarkButton format="bold" icon={Bold} />
          <MarkButton format="italic" icon={Italic} />
          <MarkButton format="underline" icon={Underline} />
          <MarkButton format="code" icon={Code} />
          <div className="w-px h-4 mx-1 bg-gray-200" />
          <BlockButton format="heading-one" icon={Heading1} />
          <BlockButton format="heading-two" icon={Heading2} />
          <BlockButton format="block-quote" icon={Quote} />
          <div className="w-px h-4 mx-1 bg-gray-200" />
          <BlockButton format="numbered-list" icon={ListOrdered} />
          <BlockButton format="bulleted-list" icon={List} />
          <div className="w-px h-4 mx-1 bg-gray-200" />
          <BlockButton format="left" icon={AlignLeft} />
          <BlockButton format="center" icon={AlignCenter} />
          <BlockButton format="right" icon={AlignRight} />
          <BlockButton format="justify" icon={AlignJustify} />
        </Toolbar>
        <StyledEditable
          renderElement={renderElement}
          renderLeaf={renderLeaf}
          placeholder={placeholder}
          spellCheck
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className={className}
          onKeyPress={onKeyPress}
        />
      </Slate>
    </div>
  )
}

export default RichTextEditor 