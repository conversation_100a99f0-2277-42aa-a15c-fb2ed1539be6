import React, { FC, Fragment, ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { PhoneLink } from './phone-link';
import { MailLink } from './mail-link';
import { CurrencyDisplay } from './currency-display';
import { CopyNumber } from './copy-number';
import { formatDateForDisplay } from '@/utils/dateUtils';

export type InfoFieldProps = {
  icon?: LucideIcon | (() => JSX.Element);
  label: string | ReactNode;
  value: string | boolean | ReactNode;
  className?: string;
  containerClassName?: string;
  labelClassName?: string;
  valueClassName?: string;
  isPhone?: boolean;
  isMail?: boolean;
  isNumber?: boolean;
  isCurrency?: boolean;
  isDate?: boolean;
  caseId?: string;
  isUrl?: boolean;
}

interface InfoFieldGroupProps {
  fields: InfoFieldProps[];
  className?: string;
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: string;
  caseId?: string;
}

export const InfoField: FC<InfoFieldProps> = ({
  icon: Icon,
  label,
  value,
  className,
  containerClassName,
  labelClassName,
  valueClassName,
  isPhone,
  isMail,
  isNumber,
  isCurrency,
  isDate,
  caseId,
  isUrl,
}) => {
  const renderValue = () => {
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }

    if (isPhone && typeof value === 'string') {
      return value.split('\n').map((phone, index) => (
        <Fragment key={index}>
          <PhoneLink phone={phone} />
          {index < value.split('\n').length - 1 && <br />}
        </Fragment>
      ));
    }

    if (isMail && typeof value === 'string') {
      return <MailLink email={value} caseId={caseId} />;
    }

    if (isNumber && typeof value === 'string') {
      return value ? <CopyNumber value={value} /> : '—';
    }

    if (isCurrency) {
      if (typeof value === 'string' || typeof value === 'number') {
        return <CurrencyDisplay amount={value} />;
      }
      return '—';
    }

    if (isDate) {
      return formatDateForDisplay(value as string);
    }

    if (isUrl) {
      return <a href={value as string} className="text-blue-500" target="_blank">{value}</a>;
    }
    
    return value || '—';
  };

  return (
    <div className={cn(
      "flex items-start relative",
      "w-full",
      className
    )}>
      {Icon && (
        <div className="min-w-[20px] flex-shrink-0">
          {typeof Icon === 'function' && !('render' in Icon) ?
            <Icon className="h-4 w-4 text-gray-500" /> :
            <Icon className="h-4 w-4 text-gray-500" />
          }
        </div>
      )}
      <div className={cn(
        "flex flex-col min-w-0 flex-1",
        containerClassName
      )}>
        <span className={cn(
          "text-[#06021699] text-sm leading-[21px] font-medium font-Manrope mb-[2px]",
          labelClassName
        )}>
          {label}
        </span>
        <div className={cn(
          "text-[#060216] text-[0.95em] leading-5 font-medium font-Manrope min-w-0",
          {
            "min-w-0": isMail && value,
            "break-words": !isMail || !value
          },
          valueClassName
        )}>
          {renderValue()}
        </div>
      </div>
    </div>
  );
};

export const InfoFieldGroup: FC<InfoFieldGroupProps> = ({
  fields,
  className,
  columns = 6,
  gap,
  caseId,
}) => {
  const isDocumentSidebarVisible = useSelector((state: RootState) => state.auth.isDocumentSidebarVisible);
  const isTabsPanelCollapsed = useSelector((state: RootState) => state.auth.isTabsPanelCollapsed);

  const getGridColumns = () => {
    if (columns) {
      if (columns === 1) {
        return 'grid-cols-1 md:w-1/2';
      } else {
        return `grid-cols-1 md:grid-cols-${columns} gap-x-5 gap-y-2`;
      }
    } else if (!isDocumentSidebarVisible && isTabsPanelCollapsed) {
      return 'grid-cols-1 md:grid-cols-4 gap-x-20 gap-y-6';
    }
    else if (
      (isDocumentSidebarVisible && isTabsPanelCollapsed) ||
      (!isDocumentSidebarVisible && !isTabsPanelCollapsed)
    ) {
      return 'grid-cols-1 md:grid-cols-6 gap-x-5 gap-y-2';
    }
    else {
      return `grid-cols-1 md:grid-cols-${columns} gap-x-5 gap-y-2`;
    }
  };

  return (
    <div className={cn(
      "w-full grid",
      getGridColumns(),
      gap,
      className
    )}>
      {fields.map((field, index) => (
        <InfoField key={`${field.label}-${index}`} {...field} caseId={caseId} />
      ))}
    </div>
  );
};

export default InfoField; 