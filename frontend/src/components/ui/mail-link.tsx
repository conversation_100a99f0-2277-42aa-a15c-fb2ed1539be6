"use client"

import React, { MouseEvent } from "react"
import { useComposeMail } from "@/app/mails/components/composeMail"
import { CopyNumber } from "./copy-number"

interface MailLinkProps {
    email?: string | null | undefined;
    caseId?: string;
    initialBcc?: string;
}

export function MailLink({ email, caseId, initialBcc }: MailLinkProps) {
    const { openComposer } = useComposeMail()

    if (!email || email === "—") return <span className="font-Manrope">—</span>;

    const isValidEmail = (email: string): boolean => {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(email);
    };

    const handleEmailClick = (e: MouseEvent) => {
        e.preventDefault();
        if (isValidEmail(email)) {
            openComposer(email, caseId, initialBcc);
        }
    }

    return (
        <div className="inline-flex items-center gap-2 max-w-full">
            <a 
                href={`mailto:${email}`}
                className="text-blue-600 underline font-Manrope uppercase truncate cursor-pointer"
                onClick={handleEmailClick}
            >
                {email}
            </a>
            {isValidEmail(email) && (
                <CopyNumber value={email} hideValue={true}/>
            )}
        </div>
    );
};