import { useState, useEffect } from 'react';

/**
 * Converts a YouTube URL to an embeddable format
 * @param url YouTube URL to format
 * @returns Properly formatted embed URL or null if invalid
 */
const formatYouTubeUrl = (url: string): string | null => {
  if (!url) return null;
  
  try {
    // Handle different YouTube URL formats
    let videoId = null;
    
    // Clean the URL (trim whitespace)
    const cleanUrl = url.trim();
    
    // Regular youtube.com/watch?v= format
    if (cleanUrl.includes('youtube.com/watch?v=')) {
      const urlObj = new URL(cleanUrl);
      videoId = urlObj.searchParams.get('v');
    } 
    // Shortened youtu.be format
    else if (cleanUrl.includes('youtu.be/')) {
      const urlParts = cleanUrl.split('youtu.be/');
      videoId = urlParts[1]?.split(/[?#]/)[0];
    }
    // Youtube embed format
    else if (cleanUrl.includes('youtube.com/embed/')) {
      const urlParts = cleanUrl.split('youtube.com/embed/');
      videoId = urlParts[1]?.split(/[?#]/)[0];
      if (videoId) return cleanUrl; // Already formatted correctly
    }
    // Youtube short format
    else if (cleanUrl.includes('youtube.com/shorts/')) {
      const urlParts = cleanUrl.split('youtube.com/shorts/');
      videoId = urlParts[1]?.split(/[?#]/)[0];
    }
    
    // Extract video ID using regex as fallback
    if (!videoId && /[a-zA-Z0-9_-]{11}/.test(cleanUrl)) {
      const match = cleanUrl.match(/([a-zA-Z0-9_-]{11})/);
      videoId = match ? match[1] : null;
    }
    
    if (!videoId) {
      console.error('Could not extract YouTube video ID from URL:', cleanUrl);
      return null;
    }
    
    // Return properly formatted embed URL
    return `https://www.youtube.com/embed/${videoId}`;
  } catch (error) {
    console.error('Error formatting YouTube URL:', error);
    return null;
  }
};

interface VideoPlayerProps {
  url: string;
}

/**
 * VideoPlayer component that properly handles YouTube URLs
 * Converts regular YouTube URLs to embedded format for iframe display
 */
const VideoPlayer = ({ url }: VideoPlayerProps) => {
  const [formattedUrl, setFormattedUrl] = useState<string | null>(null);
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [errorDetails, setErrorDetails] = useState('');

  useEffect(() => {
    try {
      console.log('Video URL received:', url);
      setLoading(true);
      setError(false);
      setErrorDetails('');
      
      if (!url || url.trim() === '') {
        setErrorDetails('Empty URL provided');
        setError(true);
        setLoading(false);
        return;
      }
      
      const formatted = formatYouTubeUrl(url);
      console.log('Formatted URL:', formatted);
      
      if (!formatted) {
        setErrorDetails('Could not parse as a valid YouTube URL');
        setError(true);
      } else {
        setFormattedUrl(formatted);
      }
    } catch (err) {
      console.error('Error in VideoPlayer:', err);
      setErrorDetails(err instanceof Error ? err.message : 'Unknown error');
      setError(true);
    } finally {
      setLoading(false);
    }
  }, [url]);

  const handleIframeError = () => {
    console.error('iframe loading error');
    setError(true);
    setErrorDetails('Failed to load video from the provided URL');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900">
        <div className="animate-pulse text-white text-sm">Loading video...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-white bg-gray-900 p-4">
        <p className="text-red-400 mb-2 font-medium">Invalid YouTube URL</p>
        <p className="text-sm text-gray-400 text-center mb-2">{url}</p>
        {errorDetails && (
          <p className="text-xs text-gray-500 text-center border border-gray-800 bg-gray-800/50 p-2 rounded">
            Error: {errorDetails}
          </p>
        )}
        <div className="mt-4 text-xs text-gray-400">
          Try using a URL like: https://www.youtube.com/watch?v=VIDEO_ID
        </div>
      </div>
    );
  }

  return (
    <iframe
      src={formattedUrl || ''}
      className="w-full h-full"
      allowFullScreen
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      title="Video Player"
      onError={handleIframeError}
      sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation"
      loading="lazy"
      referrerPolicy="no-referrer-when-downgrade"
    />
  );
};

export default VideoPlayer; 