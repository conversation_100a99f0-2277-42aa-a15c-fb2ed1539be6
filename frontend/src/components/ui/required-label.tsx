import React, { ComponentProps, ReactNode } from "react"
import { cn } from "@/lib/utils"
import { Label } from "./label"

interface RequiredLabelProps extends ComponentProps<typeof Label> {
  children: ReactNode
  className?: string
}

const RequiredLabel = ({ children, className, ...props }: RequiredLabelProps) => {
  return (
    <Label className={cn("flex items-center gap-0.5 pb-2", className)} {...props}>
      {children}
      <span className="text-red-500">*</span>
    </Label>
  )
}

export { RequiredLabel } 