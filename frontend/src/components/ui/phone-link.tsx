"use client"

import React from "react"
import { CopyNumber } from "./copy-number"
interface PhoneLinkProps {
  phone?: string | undefined | null;
}

// Format for display (XXX-XXX-XXXX)
export const formatPhoneNumber = (phoneNumber?: string): string => {
  if (!phoneNumber) return "";

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Handle US numbers (10 digits)
  if (cleaned.length === 10) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }

  // Handle numbers with country code (11 digits starting with 1)
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }

  // For other lengths, just return what we have
  if (cleaned.length > 10) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
  }

  return cleaned;
};

// Format for API calls (E.164 format: +1XXXXXXXXXX)
export const formatPhoneNumberForApi = (phoneNumber?: string): string => {
  if (!phoneNumber) return "";

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // US numbers (10 digits) - add +1 prefix
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  }

  // Numbers with country code (11 digits starting with 1)
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+${cleaned}`;
  }

  // If it's already in E.164 format, return as is
  if (phoneNumber.startsWith('+')) {
    return phoneNumber;
  }

  // For other cases, assume US and add +1 if it's 10 digits
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  }

  // Otherwise just add + to the cleaned number
  return `+${cleaned}`;
};

export function PhoneLink({ phone }: PhoneLinkProps) {

  if (!phone || phone === "") return <span className="font-Manrope">—</span>;

  const cleanedPhone = phone.replace(/\D/g, '');
  const isValidPhone = cleanedPhone.length >= 10;
  const formattedPhone = formatPhoneNumber(phone);

  return (
    <div className="flex items-center gap-2 overflow-hidden flex-wrap">
      <a
        href={`tel:${cleanedPhone}`}
        className="text-blue-600 underline font-Manrope break-all"
      >
        {formattedPhone}
      </a>
      {isValidPhone && (
        <CopyNumber value={phone} hideValue={true} />
      )}
    </div>
  );
}