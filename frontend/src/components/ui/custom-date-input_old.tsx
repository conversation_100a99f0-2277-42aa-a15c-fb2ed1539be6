"use client"

import { ChangeEvent, KeyboardEvent, useRef, useState } from "react"
import { CalendarIcon } from "lucide-react"
import { format, parse, isAfter, isBefore, startOfDay } from "date-fns"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "./button"
import { Input } from "./input"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"
import { Calendar as CalendarComponent } from "./calendar"
import { DayModifiers } from "react-day-picker"

interface CustomDateInputProps {
  value?: string
  onChange?: (value: string) => void
  error?: boolean
  className?: string
  placeholder?: string
  minDate?: Date
  maxDate?: Date
  onError?: (message: string) => void
}

const parseDate = (dateStr: string) => {
  try {
    // Try parsing DD-MM-YYYY format
    const date = parse(dateStr, 'dd-MM-yyyy', new Date())
    if (isNaN(date.getTime())) return null
    return date
  } catch {
    return null
  }
}

const formatDateInput = (value: string): string => {
  // Remove all non-digits and hyphens
  const cleanValue = value.replace(/[^\d-]/g, '')
  const parts = cleanValue.split("—").filter(Boolean)
  const numbers = parts.join('')
  
  // Handle day (DD)
  if (numbers.length <= 2) {
    const day = parseInt(numbers)
    // Limit day to 31
    if (day > 31) {
      return '31'
    }
    return numbers
  }
  
  // Handle month (MM)
  const day = numbers.slice(0, 2)
  const monthDigits = numbers.slice(2, 4)
  
  // Add leading zero to month if needed
  let month = monthDigits
  if (monthDigits.length === 1) {
    const monthNum = parseInt(monthDigits)
    if (monthNum > 1) {
      // If first digit is 2-9, automatically add leading zero
      month = `0${monthNum}`
    }
  } else if (monthDigits.length === 2) {
    const monthNum = parseInt(monthDigits)
    if (monthNum > 12) {
      // If month is greater than 12, take only first digit with leading zero
      month = `0${monthDigits[0]}`
    }
  }
  
  // Format with hyphens
  if (numbers.length <= 4) {
    return `${day}-${month}`
  }
  
  // Handle year (YYYY)
  const year = numbers.slice(4, 8)
  return `${day}-${month}-${year}`
}

const getDisabledDays = (minDate?: Date, maxDate?: Date): DayModifiers => {
  if (minDate && maxDate) {
    return {
      disabled: {
        before: startOfDay(minDate),
        after: startOfDay(maxDate)
      }
    }
  }
  if (minDate) {
    return {
      disabled: {
        before: startOfDay(minDate),
      }
    }
  }
  if (maxDate) {
    return {
      disabled: {
        after: startOfDay(maxDate)
      }
    }
  } 
  return {}
}

export function CustomDateInput({
  value,
  onChange,
  error,
  className,
  placeholder = "DD-MM-YYYY",
  minDate,
  maxDate,
  onError
}: CustomDateInputProps) {
  const [date, setDate] = useState<Date | undefined>(
    value ? parse(value, 'dd-MM-yyyy', new Date()) : undefined
  )
  const inputRef = useRef<HTMLInputElement>(null)

  const validateDate = (date: Date | null): boolean => {
    if (!date) return false

    const startOfToday = startOfDay(date)

    if (minDate && isBefore(startOfToday, startOfDay(minDate))) {
      onError?.(`Date cannot be before ${format(minDate, 'dd-MM-yyyy')}`)
      return false
    }

    if (maxDate && isAfter(startOfToday, startOfDay(maxDate))) {
      onError?.(`Date cannot be after ${format(maxDate, 'dd-MM-yyyy')}`)
      return false
    }

    return true
  }

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    let inputValue = e.target.value
    const selectionStart = e.target.selectionStart || 0
    
    // Handle backspace when it hits a hyphen
    if (inputValue.length < (value?.length || 0)) {
      if (value?.endsWith("—") && inputValue === value.slice(0, -1)) {
        inputValue = value.slice(0, -2)
      }
    }
    
    // Format the input value with automatic hyphens
    const formattedValue = formatDateInput(inputValue)
    
    // Add hyphen after day if exactly 2 digits entered
    if (formattedValue.length === 2 && !formattedValue.includes("—")) {
      onChange?.(`${formattedValue}-`)
      setTimeout(() => {
        inputRef.current?.setSelectionRange(3, 3)
      }, 0)
      return
    }
    
    // Add hyphen after month if valid month entered
    if (formattedValue.length >= 4 && formattedValue.split("—").length === 2) {
      const [, month] = formattedValue.split("—")
      if (month.length === 2) {
        onChange?.(`${formattedValue}-`)
        setTimeout(() => {
          inputRef.current?.setSelectionRange(6, 6)
        }, 0)
        return
      }
    }
    console.log("input change            :::   ", formattedValue)
    
    onChange?.(formattedValue)

    // Validate complete date
    if (formattedValue.length === 10) {
      const parsedDate = parseDate(formattedValue)
      if (parsedDate && validateDate(parsedDate)) {
        setDate(parsedDate)
      }
    } else {
      setDate(undefined)
    }

    // Maintain cursor position after formatting
    const hyphensBefore = (value?.substring(0, selectionStart) || '').split("—").length - 1
    const hyphensAfter = (formattedValue.substring(0, selectionStart) || '').split("—").length - 1
    const newPosition = selectionStart + (hyphensAfter - hyphensBefore)
    
    setTimeout(() => {
      inputRef.current?.setSelectionRange(newPosition, newPosition)
    }, 0)
  }

  const handleCalendarSelect = (newDate: Date | undefined) => {
    if (newDate && validateDate(newDate)) {
      setDate(newDate)
      const formattedDate = format(newDate, 'dd-MM-yyyy')
      onChange?.(formattedDate)
    }
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    console.log("key            :::   ", e.key)
    // Allow select all
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
      return
    }

    // Special handling for backspace
    if (e.key === 'Backspace') {
      const input = e.currentTarget
      const cursorPosition = input.selectionStart || 0
      
      // If cursor is just after a hyphen, move cursor before the hyphen
      if (value?.[cursorPosition - 1] === "—") {
        e.preventDefault()
        input.setSelectionRange(cursorPosition - 1, cursorPosition - 1)
      }
      return
    }

    // Prevent entering numbers that would make day > 31
    if (/^\d$/.test(e.key)) {
      const input = e.currentTarget
      const cursorPosition = input.selectionStart || 0
      const currentValue = input.value

      // Only check when modifying the day part (first two digits)
      if (cursorPosition <= 2) {
        const currentDay = currentValue.split("—")[0] || ''
        const newDay = cursorPosition === 0 
          ? e.key 
          : cursorPosition === 1 
            ? currentDay + e.key 
            : e.key

        if (parseInt(newDay) > 31) {
          e.preventDefault()
          return
        }
      }
    }

    // Allow delete, arrow keys, and numbers
    if (
      e.key === 'Delete' ||
      e.key === 'ArrowLeft' ||
      e.key === 'ArrowRight' ||
      e.key === 'Tab' ||
      /^\d$/.test(e.key)
    ) {
      return
    }
    
    // Prevent typing non-numeric characters
    e.preventDefault()
  }

  return (
    <div className="relative">
      <Input
        ref={inputRef}
        type="text"
        value={value || ""}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        maxLength={10} // DD-MM-YYYY = 10 characters
        className={cn(
          "h-12 pr-10",
          error && "border-red-500",
          className
        )}
        placeholder={placeholder}
      />
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "absolute right-0 top-0 h-full w-10 p-0 hover:bg-transparent",
              error && "text-red-500"
            )}
          >
            <CalendarIcon className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <CalendarComponent
            mode="single"
            selected={date}
            onSelect={handleCalendarSelect}
            initialFocus
            modifiers={getDisabledDays(minDate, maxDate)}
            modifiersStyles={{
              disabled: { opacity: 0.5, cursor: 'not-allowed' }
            }}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
} 