"use client";

import React from 'react';
import { Star } from 'lucide-react';
import { cn } from "@/lib/utils";

interface RatingProps {
  value: number;
  onChange?: (value: number) => void;
  size?: 'sm' | 'md' | 'lg';
  readOnly?: boolean;
  disabled?: boolean;
  showValue?: boolean;
  className?: string;
}

const STAR_COUNT = 5;

export function Rating({
  value,
  onChange,
  size = 'sm',
  readOnly = false,
  disabled = false,
  showValue = true,
  className
}: RatingProps) {
  const [hoverValue, setHoverValue] = React.useState<number | null>(null);

  // Size mappings
  const sizeMap = {
    sm: {
      star: 'h-4 w-4',
      container: 'gap-1',
    },
    md: {
      star: 'h-6 w-6',
      container: 'gap-2',
    },
    lg: {
      star: 'h-8 w-8',
      container: 'gap-3',
    },
  };

  const handleStarClick = (starValue: number) => {
    if (!readOnly && !disabled && onChange) {
      onChange(starValue);
    }
  };

  const handleStarHover = (starValue: number) => {
    if (!readOnly && !disabled) {
      setHoverValue(starValue);
    }
  };

  const handleMouseLeave = () => {
    setHoverValue(null);
  };

  const renderStar = (starValue: number) => {
    const isActive = (hoverValue ?? value) >= starValue;
    const starClassName = cn(
      sizeMap[size].star,
      'transition-colors duration-200',
      isActive ? 'text-green-600 fill-green-600' : 'text-gray-300',
      disabled && 'opacity-50',
      !readOnly && !disabled && 'cursor-pointer hover:text-green-500'
    );

    return (
      <Star
        key={starValue}
        className={starClassName}
        onClick={() => handleStarClick(starValue)}
        onMouseEnter={() => handleStarHover(starValue)}
      />
    );
  };

  return (
    <div className={cn('flex flex-col items-start', className)}>
      <div
        className={cn('flex items-center', sizeMap[size].container)}
        onMouseLeave={handleMouseLeave}
      >
        {[...Array(STAR_COUNT)].map((_, index) => renderStar(index + 1))}
      </div>
      {showValue && (
        <p className="text-sm text-gray-500 mt-1">
          {value} out of {STAR_COUNT} stars
        </p>
      )}
    </div>
  );
}