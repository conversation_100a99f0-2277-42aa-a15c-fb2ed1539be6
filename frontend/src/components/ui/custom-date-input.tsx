"use client";

import React, { useState, useRef, RefObject } from "react";
import { CalendarIcon } from "lucide-react";
import { format, parse, isAfter, isBefore, startOfDay } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Input } from "./input";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import { Calendar as CalendarComponent } from "./calendar";
import { DayModifiers } from "react-day-picker";

interface CustomDateInputProps {
  value?: string;
  onChange?: (value: string) => void;
  error?: boolean;
  className?: string;
  placeholder?: string;
  minDate?: Date;
  maxDate?: Date;
  onError?: (message: string) => void;
  disabled?: boolean;
  displayFormat?: (date: string | null | undefined) => string;
  required?: boolean;
}

export function CustomDateInput({
  value,
  onChange,
  error,
  minDate,
  maxDate,
  onError,
  disabled,
  required = false,
}: CustomDateInputProps) {
  const [date, setDate] = useState<Date | undefined>(
    value ? parse(value, "dd-MM-yyyy", new Date()) : undefined
  );

  const [parts, setParts] = useState({
    day: value ? value.split("-")[0] : "",
    month: value ? value.split("-")[1] : "",
    year: value ? value.split("-")[2] : "",
  });

  const dayRef = useRef<HTMLInputElement>(null);
  const monthRef = useRef<HTMLInputElement>(null);
  const yearRef = useRef<HTMLInputElement>(null);

  const validateDate = (day: string, month: string, year: string): boolean => {
    if (!day || !month || !year) return false;

    const dateStr = `${day}-${month}-${year}`;
    const parsedDate = parse(dateStr, "dd-MM-yyyy", new Date());
    if (isNaN(parsedDate.getTime())) return false;

    const startOfParsedDate = startOfDay(parsedDate);

    if (minDate && isBefore(startOfParsedDate, startOfDay(minDate))) {
      onError?.(`Date cannot be before ${format(minDate, "dd-MM-yyyy")}`);
      return false;
    }

    if (maxDate && isAfter(startOfParsedDate, startOfDay(maxDate))) {
      onError?.(`Date cannot be after ${format(maxDate, "dd-MM-yyyy")}`);
      return false;
    }

    return true;
  };

  const updateDate = (newParts: typeof parts) => {
    setParts(newParts);
    if (newParts.day && newParts.month && newParts.year) {
      const dateStr = `${newParts.day}-${newParts.month}-${newParts.year}`;
      if (validateDate(newParts.day, newParts.month, newParts.year)) {
        onChange?.(dateStr);
        setDate(parse(dateStr, "dd-MM-yyyy", new Date()));
      }
    }
  };

  const handleInputChange = (
    field: "day" | "month" | "year",
    value: string,
    nextRef?: RefObject<HTMLInputElement>
  ) => {
    let newValue = value.replace(/\D/g, "");

    if (field === "month") {
      if (newValue.length > 0 && parseInt(newValue) > 12) {
        newValue = "12";
      }
      if (newValue.length === 2 && nextRef) {
        nextRef.current?.focus();
      }
    }

    if (field === "day") {
      if (newValue.length > 0 && parseInt(newValue) > 31) {
        newValue = "31";
      }
      if (newValue.length === 2 && nextRef) {
        nextRef.current?.focus();
      }
    }

    if (field === "year") {
      newValue = newValue.slice(0, 4);
    }

    const newParts = { ...parts, [field]: newValue };
    updateDate(newParts);
  };

  const handleCalendarSelect = (newDate: Date | undefined) => {
    if (newDate) {
      const formattedDate = format(newDate, "dd-MM-yyyy");
      const [day, month, year] = formattedDate.split("-");
      const newParts = { day, month, year };
      updateDate(newParts);
    }
  };

  const getDisabledDays = (): DayModifiers => {
    if (minDate && maxDate) {
      return {
        disabled: {
          before: startOfDay(minDate),
          after: startOfDay(maxDate),
        },
      };
    }
    if (minDate) {
      return {
        disabled: {
          before: startOfDay(minDate),
        },
      };
    }
    if (maxDate) {
      return {
        disabled: {
          after: startOfDay(maxDate),
        },
      };
    }
    return {};
  };

  return (
    <div className="relative flex items-center">
      <div className="flex h-10 items-center space-x-1.5 rounded-l-md border border-r-0 bg-white px-3 shadow-sm transition-colors focus-within:border-primary">
        <Input
          ref={monthRef}
          type="text"
          value={parts.month}
          onChange={(e) => handleInputChange("month", e.target.value, dayRef)}
          className={cn(
            "w-[2.2rem] h-8 border-0 p-0 text-center focus-visible:ring-0",
            error && "text-destructive placeholder:text-destructive/50",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          placeholder="MM"
          maxLength={2}
          disabled={disabled}
          required={required}
        />
        <span className="text-muted-foreground/60">-</span>
        <Input
          ref={dayRef}
          type="text"
          value={parts.day}
          onChange={(e) => handleInputChange("day", e.target.value, yearRef)}
          className={cn(
            "w-[2.2rem] h-8 border-0 p-0 text-center focus-visible:ring-0",
            error && "text-destructive placeholder:text-destructive/50",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          placeholder="DD"
          maxLength={2}
          disabled={disabled}
          required={required}
        />
        <span className="text-muted-foreground/60">-</span>
        <Input
          ref={yearRef}
          type="text"
          value={parts.year}
          onChange={(e) => handleInputChange("year", e.target.value)}
          className={cn(
            "w-[3.2rem] h-8 border-0 p-0 text-center focus-visible:ring-0",
            error && "text-destructive placeholder:text-destructive/50",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          placeholder="YYYY"
          maxLength={4}
          disabled={disabled}
          required={required}
        />
      </div>

      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "h-10 w-10 rounded-l-none border-l-0 bg-white p-0 shadow-sm transition-colors hover:bg-gray-50/50",
              error &&
                "border-destructive text-destructive hover:bg-destructive/5",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <CalendarComponent
            mode="single"
            selected={date}
            onSelect={handleCalendarSelect}
            initialFocus
            modifiers={getDisabledDays()}
            modifiersStyles={{
              disabled: { opacity: 0.5, cursor: "not-allowed" },
            }}
            className="rounded-md border shadow-md"
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
