"use client"

import * as React from "react"
import { ChevronLeft } from "lucide-react"
import { DayPicker, DayModifiers } from "react-day-picker"
import { DateRange } from "react-day-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

type CalendarProps = {
  className?: string
  classNames?: Record<string, string>
  showOutsideDays?: boolean
  mode?: "single" | "range"
  selected?: Date | DateRange | undefined
  onSelect?: ((date: Date | undefined) => void) | ((range: DateRange | undefined) => void)
  initialFocus?: boolean
  modifiers?: DayModifiers
  modifiersStyles?: Record<string, React.CSSProperties>
  numberOfMonths?: number
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  mode = "single",
  selected,
  onSelect,
  modifiers,
  modifiersStyles,
  numberOfMonths = 1,
  ...props
}: CalendarProps) {
  const [month, setMonth] = React.useState<Date>(selected ? (Array.isArray(selected) ? selected[0] : selected) : new Date())
  
  // Generate years array (current year - 100 to current year + 50)
  const currentYear = new Date().getFullYear()
  const years = Array.from(
    { length: 151 }, // 100 years back + current year + 50 years forward = 151 years
    (_, i) => currentYear - 100 + i
  ) // Sort in descending order (newest to oldest)
  
  // Array of month names
  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ]

  // Custom caption component
  const CustomCaption = ({ displayMonth }: { displayMonth: Date }) => {
    return (
      <div className="flex justify-center space-x-2 items-center">
        <Select
          value={displayMonth.getMonth().toString()}
          onValueChange={(value) => {
            const newMonth = new Date(displayMonth)
            newMonth.setMonth(parseInt(value))
            setMonth(newMonth)
          }}
        >
          <SelectTrigger className="w-[120px] h-8">
            <SelectValue>{months[displayMonth.getMonth()]}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {months.map((month, index) => (
              <SelectItem key={index} value={index.toString()}>
                {month}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={displayMonth.getFullYear().toString()}
          onValueChange={(value) => {
            const newMonth = new Date(displayMonth)
            newMonth.setFullYear(parseInt(value))
            setMonth(newMonth)
          }}
        >
          <SelectTrigger className="w-[100px] h-8">
            <SelectValue>{displayMonth.getFullYear()}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {years.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  const commonProps = {
    showOutsideDays,
    className: cn("p-3", className),
    classNames: {
      months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
      month: "space-y-4",
      caption: "flex justify-center pt-1 relative items-center",
      caption_label: "text-sm font-medium",
      nav: "space-x-1 flex items-center",
      nav_button: cn(
        buttonVariants({ variant: "outline" }),
        "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
      ),
      nav_button_previous: "absolute left-1",
      nav_button_next: "absolute right-1",
      table: "w-full border-collapse space-y-1",
      head_row: "flex",
      head_cell:
        "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
      row: "flex w-full mt-2",
      cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
      day: cn(
        buttonVariants({ variant: "ghost" }),
        "h-9 w-9 p-0 font-normal aria-selected:opacity-100"
      ),
      day_range_end: "day-range-end",
      day_selected:
        "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
      day_today: "bg-accent text-accent-foreground",
      day_outside:
        "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
      day_disabled: "text-muted-foreground opacity-50",
      day_range_middle:
        "aria-selected:bg-accent aria-selected:text-accent-foreground",
      day_hidden: "invisible",
      ...classNames,
    },
    components: {
      IconLeft: () => <ChevronLeft className="h-4 w-4" />,
      IconRight: () => <ChevronLeft className="h-4 w-4 rotate-180" />,
      Caption: CustomCaption
    },
    month,
    onMonthChange: setMonth,
    numberOfMonths,
    modifiers,
    modifiersStyles,
    ...props
  }

  if (mode === "range") {
    return (
      <DayPicker
        mode="range"
        selected={selected as DateRange | undefined}
        onSelect={onSelect as (range: DateRange | undefined) => void}
        {...commonProps}
      />
    )
  }

  return (
    <DayPicker
      mode="single"
      selected={selected as Date | undefined}
      onSelect={onSelect as (date: Date | undefined) => void}
      {...commonProps}
    />
  )
}

Calendar.displayName = "Calendar"

export { Calendar }
