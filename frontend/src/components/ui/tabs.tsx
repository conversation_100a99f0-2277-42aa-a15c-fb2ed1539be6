"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "@/lib/utils"

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "flex w-full overflow-x-auto bg-white border-b border-gray-200 overflow-y-hidden mb-[13.75px]",
      "scrollbar scrollbar-h-1",
      "scrollbar-track-gray-100",
      "scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400",
      "scroll-smooth",
      "scrollbar-thin",
      "cursor-pointer",
      className
    )}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "flex items-center gap-2 px-4 py-3 text-sm font-medium text-gray-600 bg-transparent font-Manrope tracking-[0.15px] leading-[20px] transition-all duration-200 ease-in-out whitespace-nowrap relative [font-feature-settings:'liga'_off,'clig'_off]",
      "hover:text-[#1C7B35]",
      "data-[state=active]:text-[#1C7B35]",
      "after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-transparent data-[state=active]:after:bg-[#1C7B35] after:transition-all after:duration-200",
      "disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:text-gray-600",
      className
    )}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 px-[16.5px]",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }
