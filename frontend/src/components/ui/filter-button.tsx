import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "./button"
import { ButtonProps } from "@/components/ui/button"
import { ReactNode } from "react"
import { forwardRef } from "react"

interface FilterButtonProps extends ButtonProps {
  isActive?: boolean
  dotColor?: string
  children: ReactNode
}

const FilterButton = forwardRef<HTMLButtonElement, FilterButtonProps>(
  ({ className, isActive, dotColor = "#D0D5DD", children, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        variant="ghost"
        {...props}
        className={cn(
          "h-8 px-4 bg-white hover:bg-white flex items-center gap-2 text-sm font-medium transition-colors rounded-full border",
          isActive
            ? "border-current"
            : "text-[#667085] border-[#D0D5DD] hover:border-[#D0D5DD] hover:text-[#667085]",
          className
        )}
        style={isActive ? { color: dotColor, borderColor: dotColor } : undefined}
      >
        <div
          className="h-2 w-2 rounded-full"
          style={{ backgroundColor: dotColor }}
        />
        {children}
      </Button>
    )
  }
)

FilterButton.displayName = "FilterButton"

export { FilterButton }