import * as React from "react"
import { cn } from "@/lib/utils"

interface TimelineProps {
  children: React.ReactNode
  className?: string
}

interface TimelineItemProps {
  title: string
  description: string
  icon: React.ReactNode
  isActive?: boolean
  isCompleted?: boolean
  children?: React.ReactNode
  className?: string
}

const Timeline = React.forwardRef<
  HTMLDivElement,
  TimelineProps
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("space-y-4", className)}
      {...props}
    >
      {children}
    </div>
  )
})
Timeline.displayName = "Timeline"

const TimelineItem = React.forwardRef<
  HTMLDivElement,
  TimelineItemProps
>(({ className, title, description, icon, isActive, isCompleted, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "flex gap-4 rounded-lg p-6 transition-all",
        isActive ? "bg-secondary" : "opacity-50",
        className
      )}
      {...props}
    >
      <div className="relative">
        <div className={cn(
          "flex h-10 w-10 items-center justify-center rounded-full",
          isActive || isCompleted ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
        )}>
          {icon}
        </div>
        {isCompleted && (
          <div className="absolute left-1/2 top-10 h-full w-0.5 -translate-x-1/2 bg-muted-foreground/20" />
        )}
      </div>
      <div>
        <h4 className="mb-1 font-semibold">{title}</h4>
        <p className="mb-4 text-muted-foreground">{description}</p>
        {children}
      </div>
    </div>
  )
})
TimelineItem.displayName = "TimelineItem"

export { Timeline, TimelineItem } 