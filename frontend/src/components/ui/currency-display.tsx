import { cn } from "@/lib/utils"

interface CurrencyDisplayProps {
  amount: number | string
  className?: string
  negative?: boolean
  showZeroAsCurrency?: boolean
}

export function CurrencyDisplay({ amount, className, negative = false, showZeroAsCurrency = false }: CurrencyDisplayProps) {
  const formattedAmount = () => {
    // Convert string to number if needed
    const numericAmount = typeof amount === 'string' ? Number(amount.replace(/,/g, "")) : amount

    // Special case: if showZeroAsCurrency is true, we always format as currency even if zero
    if (showZeroAsCurrency || numericAmount !== 0) {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(numericAmount)
    }

    // Handle zero or invalid numbers with a dash (default behavior)
    if (isNaN(numericAmount) || numericAmount === 0) {
      // return '—'
      return "$0"
    }

    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(numericAmount)
  }

  return (
    <span className={cn(negative || Number(amount) < 0 ? "text-red-600" : "text-green-600", className)}>
      {`${negative && !formattedAmount().includes('-') ? "-" : ""}${formattedAmount()}`}
    </span>
  )
} 