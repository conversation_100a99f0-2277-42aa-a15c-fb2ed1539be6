import React, { forwardRef, ChangeEvent, InputHTMLAttributes } from 'react';
import { Input } from './input';

interface PhoneNumberInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value: string;
  onChange: (value: string) => void;
}

const PhoneNumberInput = forwardRef<HTMLInputElement, PhoneNumberInputProps>(
  ({ value, onChange, ...props }, ref) => {
    const formatPhoneNumber = (input: string) => {
      // Remove all non-digit characters (spaces, parentheses, hyphens)
      const cleaned = input.replace(/[^\d]/g, '');
      
      // Handle 11-digit numbers (with country code)
      let formatted = '';
      if (cleaned.length > 0) {
        // If number starts with '1' and has more digits, treat as 11-digit number
        if (cleaned.startsWith('1') && cleaned.length > 1) {
          const remaining = cleaned.substring(1);
          if (remaining.length >= 3) {
            formatted = '1-' + remaining.substring(0, 3) + '-';
            if (remaining.length >= 6) {
              formatted += remaining.substring(3, 6) + '-';
              formatted += remaining.substring(6, 10);
            } else {
              formatted += remaining.substring(3);
            }
          } else {
            formatted = '1-' + remaining;
          }
        } else {
          // Handle regular 10-digit number
          if (cleaned.length >= 3) {
            formatted += cleaned.substring(0, 3) + '-';
            if (cleaned.length >= 6) {
              formatted += cleaned.substring(3, 6) + '-';
              formatted += cleaned.substring(6, 10);
            } else {
              formatted += cleaned.substring(3);
            }
          } else {
            formatted = cleaned;
          }
        }
      }
      
      return formatted;
    };

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
      const input = e.target.value;
      
      // Handle backspace when there's a hyphen
      if (input.length < value.length) {
        if (value.endsWith('-')) {
          const newValue = value.slice(0, -2);
          onChange(newValue);
          return;
        }
      }
      
      const formatted = formatPhoneNumber(input);
      
      // Only update if the input is empty or contains valid digits
      // Remove the length check since we'll handle it in formatPhoneNumber
      if (input === '' || /[\d\s()\-]/g.test(input)) {
        onChange(formatted);
      }
    };

    return (
      <Input
        {...props}
        ref={ref}
        type="tel"
        value={value}
        onChange={handleChange}
        placeholder="************"
      />
    );
  }
);

PhoneNumberInput.displayName = 'PhoneNumberInput';

export { PhoneNumberInput }; 