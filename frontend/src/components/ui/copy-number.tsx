"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Check, Copy } from "lucide-react"
import { Button } from "./button"
import { useToast } from "@/hooks/use-toast"
import { useState } from "react"

interface CopyNumberProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string | number
  displayValue?: string | number
  className?: string
  buttonClassName?: string
  hideValue?: boolean
}

export function CopyNumber({
  value,
  displayValue,
  className,
  buttonClassName,
  hideValue = false,
  ...props
}: CopyNumberProps) {
  const [hasCopied, setHasCopied] = useState(false)
  const { toast } = useToast()

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(value.toString())
      setHasCopied(true)
      toast({
        description: "Copied to clipboard",
      })
      setTimeout(() => setHasCopied(false), 2000)
    } catch (err) {
      console.log("error at copy number time",err);
    }
  }

  return (
    <div className={cn("flex items-center gap-2", className)} {...props}>
      {!hideValue && (
        <span className="text-[#060216] text-sm leading-5 font-semibold font-Manrope">
          {displayValue || value}
        </span>
      )}
      {(value || displayValue) && (
        <Button
          variant="ghost"
          size="icon"
          className={cn("h-6 w-6 hover:bg-muted", buttonClassName)}
          onClick={copyToClipboard}
        >
          {hasCopied ? (
            <Check className="h-2 w-2" />
          ) : (
            <Copy className="h-2 w-2" />
          )}
        </Button>
      )}
    </div>
  )
} 