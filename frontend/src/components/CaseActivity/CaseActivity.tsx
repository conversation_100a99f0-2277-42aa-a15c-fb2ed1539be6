import { FC, useEffect, useState } from "react";
import {
  ClipboardEdit,
  UserPlus,
  FileEdit,
  DollarSign,
  AlertCircle,
  Scale,
  Users,
  CheckSquare,
  Search,
} from "lucide-react";
import { useInView } from "react-intersection-observer";
import { useCaseActivitiesQuery, Notification } from "@/services/notificationService";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";

interface CaseActivityProps {
  caseId: string;
  searchQuery?: string;
}

interface ActivityItemProps {
  activity: Notification;
}

const ActivityItem: FC<ActivityItemProps> = ({ activity }) => {
  const getIcon = () => {
    switch (activity.notification_type) {
      case "CASE_DEFENDANT_ADDED":
        return <UserPlus className="h-4 w-4 text-[#060216]-500" />;
      case "CASE_STATUS_CHANGED":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case "CASE_INCIDENT_UPDATED":
        return <FileEdit className="h-4 w-4 text-purple-500" />;
      case "CASE_COST_ADDED":
        return <DollarSign className="h-4 w-4 text-green-500" />;
      case "CASE_DETAILS_UPDATED":
        return <ClipboardEdit className="h-4 w-4 text-indigo-500" />;
      case "TASK_USERS_TAGGED":
      case "TASK_USER_ASSIGNED":
        return <Users className="h-4 w-4 text-[#060216]-500" />;
      case "TASK_CREATED":
      case "TASK_UPDATED":
        return <CheckSquare className="h-4 w-4 text-purple-500" />;
      default:
        return <Scale className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  return (
    <div className={cn(
      "group flex items-start gap-3 px-6 py-4 hover:bg-gray-50/50 transition-colors",
      "border-b border-gray-100 last:border-b-0"
    )}>
      <div className="flex-shrink-0 mt-1 w-5 h-5 flex items-center justify-center">
        {getIcon()}
      </div>
      <div className="flex-grow min-w-0">
        <h4 className="text-sm font-medium text-gray-900">
          {activity.title}
        </h4>
        <p className="text-sm text-gray-600 mt-0.5">
          {activity.message}
        </p>
        <span className="text-xs text-gray-400 mt-1 block">
          {formatDate(activity.created_at)}
        </span>
      </div>
    </div>
  );
};

const ActivitySkeleton: FC = () => (
  <div className="flex items-start gap-3 px-6 py-4 border-b border-gray-100">
    <Skeleton className="h-4 w-4 rounded-full flex-shrink-0 mt-1" />
    <div className="flex-grow space-y-2">
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-3 w-24" />
    </div>
  </div>
);

export const CaseActivity: FC<CaseActivityProps> = ({ caseId, searchQuery }) => {
  const [localSearch, setLocalSearch] = useState(searchQuery || '');
  const { ref, inView } = useInView();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useCaseActivitiesQuery(caseId, localSearch);

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Safely flatten all pages of activities
  const activities = data?.pages
    ?.flatMap((page) => page.results)
    .filter((activity): activity is Notification => 
      activity !== null && 
      activity !== undefined && 
      typeof activity === 'object' && 
      'id' in activity
    ) || [];

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="flex-none px-6 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Activity Log</h2>
          <span className="text-sm text-gray-500">
            {activities.length} {activities.length === 1 ? 'activity' : 'activities'}
          </span>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            type="search"
            placeholder="Search activities..."
            className="pl-9 w-full"
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
          />
        </div>
      </div>
      <ScrollArea className="flex-1">
        <div className="flex flex-col">
          {isLoading ? (
            // Show skeletons while loading
            Array.from({ length: 5 }).map((_, index) => (
              <ActivitySkeleton key={index} />
            ))
          ) : error ? (
            <div className="flex items-center justify-center p-8 text-red-500">
              Error loading activities
            </div>
          ) : activities.length > 0 ? (
            <>
              {activities.map((activity) => (
                <ActivityItem key={activity.id} activity={activity} />
              ))}
              {hasNextPage && (
                <div
                  ref={ref}
                  className="flex justify-center items-center py-4"
                >
                  {isFetchingNextPage && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-gray-600" />
                  )}
                </div>
              )}
            </>
          ) : (
            <div className="flex items-center justify-center p-8 text-gray-500">
              No activities found
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
