"use client";

import { useState, useRef, useEffect, ChangeEvent, KeyboardEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  Phone,
  MessageSquare,
  PhoneCall,
  PhoneOff,
  Send,
  ImagePlus,
  X,
  FileText,
  Download,
  Image,
  File,
  Play,
  Film,
  Paperclip
} from "lucide-react";
import twilioService from "@/services/twilioIntegrationService";
import { useClientContactDetailsQuery, useClientBasicDetailsQuery } from "@/services/case-management/clientDetailService";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { formatPhoneNumber } from "../ui/phone-link";

interface CaseDialerChatProps {
  caseId: string;
}

declare global {
  interface Window {
    RCAdapter?: {
      setMinimized: (minimized: boolean) => void;
      config: (config: {
        closeAfterCallEnd?: boolean;
        enablePopup?: boolean;
        minimized?: boolean;
        clickToDialInSearchResult?: boolean;
        enableMinimize?: boolean;
        hideCallTab?: boolean;
      }) => void;
      clickToCall: (phoneNumber: string) => void;
    };
  }
}

interface MediaItem {
  media_url: string;
  presigned_url: string;
  content_type: string;
  filename: string;
  created_at: string;
}

// Helper function to get the appropriate icon for a media type
const getMediaTypeIcon = (contentType: string) => {
  if (contentType.startsWith('image/')) {
    return <Image className="h-5 w-5" />;
  } else if (contentType.startsWith('video/')) {
    return <Film className="h-5 w-5" />;
  } else if (contentType.startsWith('audio/')) {
    return <Play className="h-5 w-5" />;
  } else if (contentType === 'application/pdf') {
    return <FileText className="h-5 w-5" />;
  } else {
    return <File className="h-5 w-5" />;
  }
};



const loadRingCentralScript = () => {
  const script = document.createElement("script");
  script.src =
    "https://apps.ringcentral.com/integration/ringcentral-embeddable/latest/adapter.js";
  script.async = true;
  document.body.appendChild(script);
};

export function CaseDialerChat({ caseId }: CaseDialerChatProps) {

  const [message, setMessage] = useState("");
  const [isCallActive, setIsCallActive] = useState(false);
  const [selectedPhoneIndex, setSelectedPhoneIndex] = useState<number>(0);
  const [phoneNumber, setPhoneNumber] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [images, setImages] = useState<File[]>([]);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [isLoadingMedia, setIsLoadingMedia] = useState(false);
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false);
  const [mediaFilter, setMediaFilter] = useState<'all' | 'images' | 'videos' | 'files'>('all');


  const { data: clientContactDetails } = useClientContactDetailsQuery(caseId);
  const { data: clientBasicDetails } = useClientBasicDetailsQuery(caseId);

  // Twilio service hooks
  const smsMutation = twilioService.useSendSMS();
  const {
    data: conversationHistory,
    isLoading: isLoadingHistory,
    error: historyError,
  } = twilioService.useGetConversationHistory(phoneNumber, {
    case_id: caseId,
  });

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
    // Update media items when conversation history changes
    if (conversationHistory && conversationHistory.length > 0) {
      fetchMediaItems();
    }
  }, [conversationHistory]);

  useEffect(() => {
    scrollToBottom();

    const interval = setInterval(() => {
      const container = chatContainerRef.current;
      if (container) {
        const isScrolledToBottom =
          container.scrollHeight - container.scrollTop <=
          container.clientHeight + 100;

        if (isScrolledToBottom) {
          scrollToBottom();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (clientContactDetails) {
      const numbers = [
        clientContactDetails.phone_number_1,
        clientContactDetails.phone_number_2,
        clientContactDetails.phone_number_3,
      ].filter(Boolean);

      if (numbers.length > 0) {
        setPhoneNumber(formatPhoneNumber(numbers[selectedPhoneIndex]));
      }
    }
  }, [clientContactDetails, selectedPhoneIndex]);

  useEffect(() => {
    loadRingCentralScript();

    const configureRingCentral = () => {
      if (window.RCAdapter && window.RCAdapter.config) {
        window.RCAdapter.config({
          closeAfterCallEnd: true,
          enablePopup: true,
          minimized: true,
          clickToDialInSearchResult: true,
          enableMinimize: true,
          hideCallTab: true,
        });
      }
    };

    const interval = setInterval(() => {
      if (window.RCAdapter) {
        configureRingCentral();
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleSendMessage = async () => {
    // Allow sending if there's a message OR images
    if (!message.trim() && images.length === 0) return;

    try {
      await smsMutation.mutateAsync({
        to_number: phoneNumber,
        message: message.trim(),
        media_files: images,
        case_id: caseId,
      });
      setMessage("");
      setImages([]);
      // Reset file input value
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      scrollToBottom();
    } catch (err) {
      console.error("Failed to send message:", err);
    }
  };

  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      // Only send if there's a message OR images
      if ((images.length > 0) && phoneNumber && !smsMutation.isPending) {
        handleSendMessage();
      }
    }
  };

  const handleCall = () => {
    if (window.RCAdapter && phoneNumber) {
      window.RCAdapter.setMinimized(false);

      setTimeout(() => {
        window.RCAdapter?.clickToCall(phoneNumber);
        setIsCallActive(true);
      }, 100);
    }
  };

  const getAvailablePhoneNumbers = () => {
    if (!clientContactDetails) return [];

    return [
      {
        number: clientContactDetails.phone_number_1,
        type: clientContactDetails.phone_type_1,
        preferred: clientContactDetails.is_preferred_1,
      },
      {
        number: clientContactDetails.phone_number_2,
        type: clientContactDetails.phone_type_2,
        preferred: clientContactDetails.is_preferred_2,
      },
      {
        number: clientContactDetails.phone_number_3,
        type: clientContactDetails.phone_type_3,
        preferred: clientContactDetails.is_preferred_3,
      },
    ].filter(({ number }) => number);
  };

  const getDisplayName = () => {
    if (clientBasicDetails) {
      const { first_name, middle_name, last_name } = clientBasicDetails;
      return `${first_name}${middle_name ? ` ${middle_name} ` : ' '}${last_name}`;
    }
    return "Brumley Law Firm";
  };

  const fetchMediaItems = () => {
    if (!phoneNumber || !conversationHistory) return;

    setIsLoadingMedia(true);
    try {
      // Extract media from conversation history
      const mediaFromConversation: MediaItem[] = [];

      // Process each message that has media_urls
      conversationHistory.forEach(msg => {
        if (msg.media_urls && msg.media_urls.length > 0) {
          msg.media_urls.forEach(media => {
            mediaFromConversation.push({
              media_url: media.twilio_url || '',
              presigned_url: media.presigned_url,
              content_type: media.type || 'unknown',
              filename: media.s3_key ? media.s3_key.split('/').pop() || 'File' : 'File',
              created_at: msg.created_at
            });
          });
        }
      });

      setMediaItems(mediaFromConversation);
    } catch (error) {
      console.error('Error processing media from conversation history:', error);
    } finally {
      setIsLoadingMedia(false);
    }
  };

  const phoneNumberSelector = (
    <div className="pt-2 border-b flex-none">
      <div className="space-y-2">
        <div className="text-sm font-medium mb-2 flex items-center justify-between">
          {/* <span>{getDisplayName()}</span> */}
          {getAvailablePhoneNumbers().length > 0 ? (
            <Select
              value={String(selectedPhoneIndex)}
              onValueChange={(value) => {
                setSelectedPhoneIndex(Number(value));
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a phone number" />
              </SelectTrigger>
              <SelectContent>
                {getAvailablePhoneNumbers().map(
                  ({ number, type, preferred }, index) => (
                    <SelectItem key={number} value={String(index)}>
                      <span className="flex items-center gap-2">
                        <span className="font-medium">
                          {formatPhoneNumber(number)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          ({type || "Phone"}
                          {preferred && " ★"})
                        </span>
                      </span>
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
          ) : (
            <Input
              type="tel"
              placeholder="Enter phone number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(formatPhoneNumber(e.target.value))}
              className="w-full"
            />
          )}
          <Button
            variant="outline"
            size="sm"
            className="ml-2"
            onClick={() => {
              setIsMediaDialogOpen(true);
              // Media items are already loaded from conversation history
            }}
          >
            <FileText className="h-4 w-4 mr-1" />
            Media
          </Button>
        </div>
      </div>

      {/* Add the Media Dialog */}
      <Dialog open={isMediaDialogOpen} onOpenChange={setIsMediaDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <div className="flex items-center justify-between border-b pb-4">
            <h3 className="text-lg font-semibold">Media Gallery</h3>
            <div className="flex space-x-2">
              <Button
                variant={mediaFilter === 'all' ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => setMediaFilter('all')}
              >
                <Paperclip className="h-4 w-4 mr-1" />
                All
              </Button>
              <Button
                variant={mediaFilter === 'images' ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => setMediaFilter('images')}
              >
                <Image className="h-4 w-4 mr-1" />
                Images
              </Button>
              <Button
                variant={mediaFilter === 'videos' ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => setMediaFilter('videos')}
              >
                <Film className="h-4 w-4 mr-1" />
                Videos
              </Button>
              <Button
                variant={mediaFilter === 'files' ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => setMediaFilter('files')}
              >
                <File className="h-4 w-4 mr-1" />
                Files
              </Button>
            </div>
          </div>

          <div className="py-4">
            {isLoadingMedia ? (
              <div className="text-center text-gray-500 py-8">
                Loading media...
              </div>
            ) : mediaItems.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No media found
              </div>
            ) : (
              <div>
                {/* Images Grid View */}
                {(mediaFilter === 'all' || mediaFilter === 'images') &&
                  mediaItems.some(item => item.content_type.startsWith('image/')) && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-3 text-gray-700">Images</h4>
                    <div className="grid grid-cols-3 sm:grid-cols-4 gap-3">
                      {mediaItems
                        .filter(item => item.content_type.startsWith('image/'))
                        .map((item, index) => (
                          <div
                            key={index}
                            className="relative cursor-pointer group aspect-square rounded-lg overflow-hidden"
                            onClick={() => setPreviewImage(item.presigned_url)}
                          >
                            <img
                              src={item.presigned_url}
                              alt={item.filename}
                              className="w-full h-full object-cover hover:opacity-90 transition-opacity"
                              loading="lazy"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = "/placeholder-image.png";
                                target.onerror = null;
                              }}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200"></div>
                            <div className="absolute bottom-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <a
                                href={item.presigned_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="bg-black bg-opacity-50 rounded-full p-1.5 inline-block"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Download className="h-3.5 w-3.5 text-white" />
                              </a>
                            </div>
                            <div className="absolute bottom-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <span className="bg-black bg-opacity-50 text-white text-xs px-1.5 py-0.5 rounded">
                                {format(new Date(item.created_at), "MM/dd")}
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* Videos */}
                {(mediaFilter === 'all' || mediaFilter === 'videos') &&
                  mediaItems.some(item => item.content_type.startsWith('video/')) && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-3 text-gray-700">Videos</h4>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {mediaItems
                        .filter(item => item.content_type.startsWith('video/'))
                        .map((item, index) => (
                          <div
                            key={index}
                            className="relative cursor-pointer group rounded-lg overflow-hidden bg-gray-200 aspect-video"
                            onClick={() => window.open(item.presigned_url, '_blank')}
                          >
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="bg-black bg-opacity-50 rounded-full p-2">
                                <Play className="h-6 w-6 text-white" />
                              </div>
                            </div>
                            <div className="absolute bottom-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <a
                                href={item.presigned_url}
                                download
                                className="bg-black bg-opacity-50 rounded-full p-1.5 inline-block"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Download className="h-3.5 w-3.5 text-white" />
                              </a>
                            </div>
                            <div className="absolute bottom-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <span className="bg-black bg-opacity-50 text-white text-xs px-1.5 py-0.5 rounded">
                                {format(new Date(item.created_at), "MM/dd")}
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* Other Files */}
                {(mediaFilter === 'all' || mediaFilter === 'files') &&
                  mediaItems.some(item => !item.content_type.startsWith('image/') && !item.content_type.startsWith('video/')) && (
                  <div>
                    <h4 className="text-sm font-medium mb-3 text-gray-700">Documents & Files</h4>
                    <div className="space-y-2">
                      {mediaItems
                        .filter(item => !item.content_type.startsWith('image/') && !item.content_type.startsWith('video/'))
                        .map((item, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                          >
                            <div className="flex items-center space-x-3 flex-1 min-w-0">
                              {getMediaTypeIcon(item.content_type)}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {item.filename || 'Untitled'}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {format(new Date(item.created_at), "MMM d, yyyy HH:mm")}
                                </p>
                              </div>
                            </div>
                            <a
                              href={item.presigned_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="ml-2 p-2 hover:bg-gray-200 rounded-full transition-colors"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <Download className="h-4 w-4 text-blue-500" />
                            </a>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* No media found for the selected filter */}
                {mediaFilter !== 'all' && (
                  (mediaFilter === 'images' && !mediaItems.some(item => item.content_type.startsWith('image/'))) ||
                  (mediaFilter === 'videos' && !mediaItems.some(item => item.content_type.startsWith('video/'))) ||
                  (mediaFilter === 'files' && !mediaItems.some(item => !item.content_type.startsWith('image/') && !item.content_type.startsWith('video/')))
                ) && (
                  <div className="text-center text-gray-500 py-8">
                    No {mediaFilter} found
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImages([...images, ...files]);
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  // Add this effect to reset the last message timestamp when changing conversations
  useEffect(() => {
    if (phoneNumber) {
      twilioService.resetConversationState();
    }
  }, [phoneNumber]);

  return (
    <div className="h-full flex flex-col">
      {/* Image Preview Dialog */}
      <Dialog
        open={!!previewImage}
        onOpenChange={(open) => {
          if (!open) setPreviewImage(null);
        }}
      >
        <DialogContent
          className="max-w-[90vw] max-h-[90vh] p-0 bg-transparent border-none shadow-none"
        >
          {previewImage && (
            <div
              className="relative w-full h-full flex items-center justify-center"
              onClick={() => setPreviewImage(null)}
            >
              <img
                src={previewImage}
                alt="Preview"
                className="max-w-[85vw] max-h-[85vh] object-contain rounded-lg shadow-xl cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              />

              {/* Controls overlay */}
              <div className="absolute top-0 left-0 right-0 flex justify-between items-center p-4 bg-gradient-to-b from-black/50 to-transparent">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-black/20"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPreviewImage(null);
                  }}
                >
                  <X className="h-5 w-5" />
                </Button>

                <div className="flex space-x-2">
                  <a
                    href={previewImage}
                    download
                    className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-black/20 text-white hover:bg-black/40 h-9 px-3"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </a>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Tabs defaultValue="messages" className="h-full flex flex-col">
        <div className="border-b flex-none">
          <TabsList className="w-full flex">
            <TabsTrigger value="messages" className="flex-1">
              <MessageSquare className="h-4 w-4 mr-2" />
              Messages
            </TabsTrigger>
            <TabsTrigger value="call" className="flex-1">
              <Phone className="h-4 w-4 mr-2" />
              Call
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="call" className="flex-1 overflow-y-auto h-full">
          <div className="p-4">
            {phoneNumberSelector}

            <div className="flex flex-col items-center space-y-8">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Call Controls</h3>
                <p className="text-sm text-gray-500">
                  {isCallActive ? "Call in progress" : "Ready to call"}
                </p>
              </div>

              <div className="flex gap-4">
                <Button
                  size="icon"
                  className={`h-12 w-12 ${isCallActive
                    ? "bg-red-500 hover:bg-red-600"
                    : "bg-green-500 hover:bg-green-600"
                    }`}
                  onClick={handleCall}
                  disabled={!phoneNumber.trim()}
                >
                  {isCallActive ? (
                    <PhoneOff className="h-6 w-6" />
                  ) : (
                    <PhoneCall className="h-6 w-6" />
                  )}
                </Button>
              </div>

              <div className="text-sm text-gray-500">
                Use RingCentral controls for mute and speaker options
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent
          value="messages"
          className="flex-1 flex flex-col h-full overflow-hidden"
        >
          {phoneNumberSelector}

          <div
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0"
          >
            {isLoadingHistory ? (
              <div className="text-center text-gray-500">
                Loading messages...
              </div>
            ) : historyError ? (
              <div className="text-center text-red-500">
                Failed to load messages
              </div>
            ) : conversationHistory?.length === 0 ? (
              <div className="text-center text-gray-500">No messages yet</div>
            ) : (
              conversationHistory?.map((msg) => (
                <div
                  key={msg.message_sid}
                  className={`max-w-[80%] ${msg.message_type === "incoming" ? "ml-0" : "ml-auto"
                    }`}
                >
                  <div className="text-xs text-gray-600 mb-1">
                    {msg.message_type === "incoming" ? getDisplayName() : "Brumley Law Firm"}
                  </div>
                  <div
                    className={`p-3 rounded-lg ${msg.message_type === "incoming"
                      ? "bg-gray-100"
                      : "bg-blue-100"
                      }`}
                  >
                    <p className="text-sm whitespace-pre-wrap break-words">
                      {msg.body}
                    </p>
                    {msg.media_urls && msg.media_urls.length > 0 && (
                      <div className="mt-2 grid grid-cols-2 gap-2">
                        {msg.media_urls.map((media, index) => {
                          const isImage = media.type?.startsWith('image/');
                          const isVideo = media.type?.startsWith('video/');

                          return (
                            <div
                              key={index}
                              className="relative cursor-pointer group"
                              onClick={() => setPreviewImage(media.presigned_url)}
                            >
                              {isImage ? (
                                <div className="relative rounded-lg overflow-hidden">
                                  <img
                                    src={media.presigned_url}
                                    alt="Attached media"
                                    className="w-full h-auto object-cover hover:opacity-90 transition-opacity rounded-lg"
                                    loading="lazy"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.src = "/placeholder-image.png";
                                      target.onerror = null;
                                    }}
                                  />
                                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Download className="h-3 w-3 text-white" />
                                  </div>
                                </div>
                              ) : isVideo ? (
                                <div className="relative rounded-lg overflow-hidden bg-gray-200 aspect-video flex items-center justify-center">
                                  <Film className="h-8 w-8 text-gray-500" />
                                  <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="bg-black bg-opacity-50 rounded-full p-2">
                                      <Play className="h-6 w-6 text-white" />
                                    </div>
                                  </div>
                                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Download className="h-3 w-3 text-white" />
                                  </div>
                                </div>
                              ) : (
                                <div className="relative rounded-lg overflow-hidden bg-gray-100 p-3 flex items-center space-x-2">
                                  {getMediaTypeIcon(media.type || 'application/octet-stream')}
                                  <span className="text-xs truncate flex-1">
                                    {media.s3_key ? media.s3_key.split('/').pop() : 'File'}
                                  </span>
                                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Download className="h-3 w-3 text-white" />
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {format(new Date(msg.created_at), "MMM d, HH:mm")}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="flex-none border-t bg-white p-4">
            {images.length > 0 && (
              <div className="mb-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-500">
                    {images.length} {images.length === 1 ? "image" : "images"} selected
                  </span>
                  {images.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 text-xs text-red-500 hover:text-red-700 p-0"
                      onClick={() => setImages([])}
                    >
                      Clear all
                    </Button>
                  )}
                </div>
                <ScrollArea className="w-full whitespace-nowrap" type="always">
                  <div className="flex gap-2">
                    {images.map((img, index) => (
                      <div
                        key={index}
                        className="relative inline-block flex-shrink-0"
                      >
                        <img
                          src={URL.createObjectURL(img)}
                          alt="Preview"
                          className="h-16 w-16 object-cover rounded-md border cursor-pointer"
                          onClick={() => setPreviewImage(URL.createObjectURL(img))}
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="h-5 w-5 absolute -top-2 -right-2 rounded-full p-0"
                          onClick={() => removeImage(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            <div className="flex gap-2">

              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 min-h-[90px] max-h-[200px] resize-none"
                disabled={!phoneNumber}
              />
              <div className="flex flex-col gap-2">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  id="dialer-image-upload"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => document.getElementById("dialer-image-upload")?.click()}
                  className="flex-shrink-0"
                  disabled={!phoneNumber}
                >
                  <ImagePlus className="h-4 w-4" />
                </Button>
                <Button
                  size="icon"
                  onClick={handleSendMessage}
                  disabled={!message.trim() || !phoneNumber || smsMutation.isPending}
                  className="flex-shrink-0"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
