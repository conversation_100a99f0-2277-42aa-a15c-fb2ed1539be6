import { Card, CardContent } from '@/components/ui/card';


interface StatusCircleProps {
  size?: number;
  color?: string;
}

export default function StatusCircle({ 
  size = 20,
  color = '#206fed'
}: StatusCircleProps) {
  const innerSize = Math.floor(size * 0.6);
  
  return (
    <Card className={`w-[${size}px] h-[${size}px] bg-transparent border-none`}>
      <CardContent className="flex items-center justify-center p-0">
        <div className={`relative`}
          style={{ width: `${size}px`, height: `${size}px` }}
        >
          <div 
            className="absolute inset-0 rounded-full opacity-20" 
            style={{ backgroundColor: color, opacity: 0.3 }}
          />
          <div 
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full"
            style={{ 
              backgroundColor: color,
              width: `${innerSize}px`,
              height: `${innerSize}px`
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
} 