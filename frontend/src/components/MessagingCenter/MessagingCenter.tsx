"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import twilioService from '@/services/twilioIntegrationService';
import { MessageSquare, History, RefreshCw, MessageCircle, BarChart2, Image } from 'lucide-react';
import { TwilioMessage } from '@/type/twilio';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from 'date-fns';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

export default function MessagingCenter() {
  const { toast } = useToast();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [message, setMessage] = useState('');
  const [page, setPage] = useState(1);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const perPage = 10;
  const days = 30;

  // Hooks from the service
  const smsMutation = twilioService.useSendSMS();
  const { data: messageHistory, isLoading, refetch } = twilioService.useGetMessages(page, perPage);
  const statusMutation = twilioService.useGetMessageStatus();
  const { data: conversations } = twilioService.useGetRecentConversations(days);
  const { data: conversationHistory } = twilioService.useGetConversationHistory(selectedConversation || '');
  const { data: unreadMessages } = twilioService.useGetUnreadMessages();
  const { data: statistics } = twilioService.useGetMessageStatistics(days);
  const markAsReadMutation = twilioService.useMarkMessagesAsRead();

  const handleSendMessage = async () => {
    if (!phoneNumber || !message) {
      toast({
        title: "Error",
        description: "Please provide both phone number and message",
        variant: "destructive",
      });
      return;
    }

    try {
      await smsMutation.mutateAsync({ 
        to_number: phoneNumber, 
        message 
      });
      setMessage('');
      setPhoneNumber('');
    } catch (_error) {
      console.error('Error sending SMS:', _error);
    }
  };

  const handleMarkAsRead = async (messageIds: number[]) => {
    try {
      await markAsReadMutation.mutateAsync(messageIds);
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  const handleRefreshStatus = async (messageSid: string) => {
    try {
      await statusMutation.mutateAsync(messageSid);
      refetch();
    } catch {
      toast({
        title: "Error",
        description: "Failed to refresh message status",
        variant: "destructive",
      });
    }
  };

  const getStatusBadgeColor = (status: TwilioMessage['status']) => {
    const colors: Record<string, string> = {
      delivered: 'bg-green-100 text-green-800',
      sent: 'bg-blue-100 text-blue-800',
      failed: 'bg-red-100 text-red-800',
      queued: 'bg-yellow-100 text-yellow-800',
      sending: 'bg-purple-100 text-purple-800',
      received: 'bg-indigo-100 text-indigo-800',
      undelivered: 'bg-gray-100 text-gray-800'
    };
    return colors[status || ''] || 'bg-gray-100 text-gray-800';
  };

  useEffect(() => {
    if (selectedConversation && unreadMessages?.length) {
      const unreadIds = unreadMessages
        .filter(msg => msg.from_number === selectedConversation)
        .map(msg => msg.id);
      
      if (unreadIds.length) {
        handleMarkAsRead(unreadIds);
      }
    }
  }, [selectedConversation, unreadMessages]);

  // Add this effect to reset the last message timestamp when changing conversations
  useEffect(() => {
    if (selectedConversation) {
      twilioService.resetConversationState();
    }
  }, [selectedConversation]);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Messaging Center</h1>
      
      <Tabs defaultValue="conversations" className="max-w-6xl">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="conversations">
            <MessageCircle className="w-4 h-4 mr-2" />
            Conversations
          </TabsTrigger>
          <TabsTrigger value="send">
            <MessageSquare className="w-4 h-4 mr-2" />
            Send Message
          </TabsTrigger>
          <TabsTrigger value="history">
            <History className="w-4 h-4 mr-2" />
            Message History
          </TabsTrigger>
          <TabsTrigger value="stats">
            <BarChart2 className="w-4 h-4 mr-2" />
            Statistics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="conversations">
          <div className="grid grid-cols-3 gap-4">
            {/* Conversations List */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Recent Conversations</CardTitle>
                <CardDescription>
                  Last {days} days of conversations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="space-y-4">
                    {conversations && Object.entries(conversations).map(([phone, summary]) => {
                      const hasUnread = unreadMessages?.some(msg => msg.from_number === phone);
                      return (
                        <div
                          key={phone}
                          className={`p-4 rounded-lg cursor-pointer hover:bg-gray-100 ${
                            selectedConversation === phone ? 'bg-gray-100' : ''
                          }`}
                          onClick={() => setSelectedConversation(phone)}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{phone}</span>
                              {hasUnread && (
                                <span className="w-2 h-2 rounded-full bg-blue-500" />
                              )}
                            </div>
                            <Badge variant={summary.message_type === 'incoming' ? 'secondary' : 'default'}>
                              {summary.total_messages}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 truncate">{summary.last_message}</p>
                          <p className="text-xs text-gray-400 mt-1">
                            {format(new Date(summary.last_message_date), 'MMM d, HH:mm')}
                          </p>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Conversation History */}
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>
                  {selectedConversation ? `Chat with ${selectedConversation}` : 'Select a conversation'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px] mb-4">
                  <div className="space-y-4">
                    {conversationHistory?.map((msg) => (
                      <div
                        key={msg.message_sid}
                        className={`p-4 rounded-lg ${
                          msg.message_type === 'incoming' ? 'bg-gray-100 ml-12' : 'bg-blue-100 mr-12'
                        }`}
                      >
                        {msg.body && <p className="text-sm mb-2">{msg.body}</p>}
                        {msg.media_urls && msg.media_urls.map((media: { 
                          presigned_url: string;
                          type: string;
                        }, index: number) => (
                          <div key={index} className="mb-2">
                            {media.type.startsWith('image/') ? (
                              <img 
                                src={media.presigned_url} 
                                alt="Message attachment" 
                                className="rounded-lg max-w-full h-auto"
                                loading="lazy"
                              />
                            ) : (
                              <a 
                                href={media.presigned_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:underline"
                              >
                                View Attachment
                              </a>
                            )}
                          </div>
                        ))}
                        <p className="text-xs text-gray-500 mt-1">
                          {format(new Date(msg.created_at), 'MMM d, HH:mm')}
                        </p>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                {selectedConversation && (
                  <div className="space-y-4">
                    <Textarea
                      placeholder="Type your message..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      className="min-h-[100px]"
                    />
                    <Button 
                      className="w-full" 
                      onClick={() => {
                        if (!message) {
                          toast({
                            title: "Error",
                            description: "Please provide a message",
                            variant: "destructive",
                          });
                          return;
                        }
                        smsMutation.mutateAsync({ 
                          to_number: selectedConversation, 
                          message 
                        }).then(() => {
                          setMessage('');
                        });
                      }}
                      disabled={smsMutation.isPending}
                    >
                      {smsMutation.isPending ? "Sending..." : "Send Message"}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="send">
          <Card>
            <CardHeader>
              <CardTitle>Send SMS Message</CardTitle>
              <CardDescription>
                Send SMS messages to your clients or contacts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="Phone Number (e.g., +1234567890)"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Textarea
                  placeholder="Type your message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
              <Button 
                className="w-full" 
                onClick={handleSendMessage}
                disabled={smsMutation.isPending}
              >
                {smsMutation.isPending ? "Sending..." : "Send Message"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Message History</CardTitle>
              <CardDescription>
                View all sent and received messages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>From</TableHead>
                      <TableHead>To</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-[50px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          Loading messages...
                        </TableCell>
                      </TableRow>
                    ) : messageHistory?.results.map((msg) => (
                      <TableRow key={msg.message_sid}>
                        <TableCell>
                          {format(new Date(msg.created_at), 'MMM d, yyyy HH:mm')}
                        </TableCell>
                        <TableCell className="capitalize">
                          {msg.message_type}
                        </TableCell>
                        <TableCell>{msg.from_number}</TableCell>
                        <TableCell>{msg.to_number}</TableCell>
                        <TableCell className="max-w-xs truncate">
                          {msg.body}
                          {msg.media_urls && (
                            <Image className="inline-block ml-2 w-4 h-4 text-gray-500" />
                          )}
                        </TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(msg.status)}`}>
                            {msg.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRefreshStatus(msg.message_sid)}
                            disabled={statusMutation.isPending}
                            title="Refresh Status"
                          >
                            <RefreshCw className={`h-4 w-4 ${statusMutation.isPending ? 'animate-spin' : ''}`} />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              {messageHistory && messageHistory.count > perPage && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={!messageHistory.previous}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setPage(p => p + 1)}
                    disabled={!messageHistory.next}
                  >
                    Next
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats">
          <Card>
            <CardHeader>
              <CardTitle>Message Statistics</CardTitle>
              <CardDescription>
                Last {days} days of messaging activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 gap-4">
                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-xl">Total</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-3xl font-bold">{statistics?.total_messages || 0}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-xl">Incoming</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-3xl font-bold">{statistics?.incoming_messages || 0}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-xl">Outgoing</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-3xl font-bold">{statistics?.outgoing_messages || 0}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-xl">Contacts</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-3xl font-bold">{statistics?.unique_contacts || 0}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-xl">Failed</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-3xl font-bold text-red-600">{statistics?.failed_messages || 0}</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 