import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import { cn } from '@/lib/utils';

type SearchResult = {
  id: string;
  title: string;
  subtitle?: string;
  params: {
    mainTab?: string;
    overviewTab?: string;
    litigationTab?: string;
    settlementTab?: string;
  };
  icon?: React.ReactNode;
};

type CommandPaletteProps = {
  isOpen: boolean;
  onClose: () => void;
  // caseId: string;
  setActiveTab?: (tab: string) => void;
};

export function CommandPalette({ isOpen, onClose, setActiveTab }: CommandPaletteProps) {
  const [search, setSearch] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const router = useRouter();
  const searchParams = useSearchParams();
  const listRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Define navigation items
  const navigationItems: SearchResult[] = [
    {
      id: 'case-details',
      title: 'Case Details',
      subtitle: 'View and manage case details',
      params: { mainTab: 'case-overview', overviewTab: 'Case Detail' },
    },
    {
      id: 'checklist',  
      title: 'Checklist',
      subtitle: 'View and manage case checklist',
      params: { mainTab: 'case-overview', overviewTab: 'Checklist' },
    },
    {
      id: 'client',
      title: 'Client',
      subtitle: 'View and manage case client',
      params: { mainTab: 'case-overview', overviewTab: 'Client' },
    },
    {
      id: 'defendants',
      title: 'Defendants',
      subtitle: 'View and manage case defendants',
      params: { mainTab: 'case-overview', overviewTab: 'Defendants' },
    },
    {
      id: 'incident',
      title: 'Incident',
      subtitle: 'View and manage case incident',
      params: { mainTab: 'case-overview', overviewTab: 'Incident' },
    },
    {
      id: 'witness',
      title: 'Witness',
      subtitle: 'View and manage case witness',
      params: { mainTab: 'case-overview', overviewTab: 'Witness' },
    },
    {
      id: 'other-parties',
      title: 'Other Parties',
      subtitle: 'View and manage case other parties',
      params: { mainTab: 'case-overview', overviewTab: 'Other Parties' },
    },
    {
      id: 'medical-treatment',
      title: 'Medical Treatment',
      subtitle: 'View and manage medical treatment',
      params: { mainTab: 'case-overview', overviewTab: 'Medical Treatment' },
    },
    {
      id: 'health-insurance',
      title: 'Health Insurance',
      subtitle: 'View and manage health insurance',
      params: { mainTab: 'case-overview', overviewTab: 'Health Insurance' },
    },
    {
      id: 'liens',
      title: 'Liens',
      subtitle: 'View and manage liens',
      params: { mainTab: 'case-overview', overviewTab: 'Liens' },
    },
    {
      id: 'case-costs',
      title: 'Case Costs',
      subtitle: 'View and manage case costs',
      params: { mainTab: 'case-overview', overviewTab: 'Case Costs' },
    },
    {
      id: 'documents',
      title: 'Documents',
      subtitle: 'View and manage case documents',
      params: { mainTab: 'case-overview', overviewTab: 'Media/Documents' },
    },
    {
      id: 'email',
      title: 'Email',
      subtitle: 'View and manage emails',
      params: { mainTab: 'case-overview', overviewTab: 'Emails' },
    },
    {
      id: 'workers',
      title: 'Workers', 
      subtitle: 'View and manage workers',
      params: { mainTab: 'case-overview', overviewTab: 'Workers' },
    },
    {
      id:'litigation',
      title: 'Litigation',
      subtitle: 'View and manage case litigation',
      params: { mainTab: 'litigation', litigationTab: 'litigation' },
    },
    {
      id:'discovery',
      title: 'Discovery',
      subtitle: 'View and manage case discovery',
      params: { mainTab: 'litigation', litigationTab: 'discovery' },
    },
    {
      id: 'litigation-negotiation',
      title: 'Litigation Negotiation',
      subtitle: 'View and manage case litigation negotiation',
      params: { mainTab: 'litigation', litigationTab: 'negotiation' },
    },
    {
      id: 'litigation-costs',
      title: 'Litigation Costs',
      subtitle: 'View and manage case litigation costs',
      params: { mainTab: 'litigation', litigationTab: 'litigation-cost' },
    },
    {
      id: 'settlement',
      title: 'Settlement',
      subtitle: 'View and manage case settlement',
      params: { mainTab: 'settlement', settlementTab: 'settlement' },
    },
    {
      id: 'settlement-negotiation',
      title: 'Settlement Negotiation',
      subtitle: 'View and manage settlement negotiations',
      params: { mainTab: 'settlement', settlementTab: 'negotiation' },
    },
    {
      id: 'settlement-uim',
      title: 'UIM',
      subtitle: 'View and manage UIM settlements',
      params: { mainTab: 'settlement', settlementTab: 'uim' },
    },
    {
      id: 'settlement-uim-settlement',
      title: 'UIM Settlement',
      subtitle: 'View and manage UIM settlement details',
      params: { mainTab: 'settlement', settlementTab: 'uim-settlement' },
    },
    {
      id: 'settlement-advance',
      title: 'Settlement Advance',
      subtitle: 'View and manage settlement advances',
      params: { mainTab: 'settlement', settlementTab: 'settlement-advance' },
    },
    {
      id: 'client-trust',
      title: 'Client Trust',
      subtitle: 'View and manage client trust accounts',
      params: { mainTab: 'settlement', settlementTab: 'client-trust' },
    },
  ];

  const filteredItems = navigationItems.filter((item) =>
    item.title.toLowerCase().includes(search.toLowerCase()) ||
    (item.subtitle && item.subtitle.toLowerCase().includes(search.toLowerCase()))
  );

  const handleSelect = useCallback((item: SearchResult) => {
    console.log("[CommandPalette] handleSelect called with item:", item);
    console.log("[CommandPalette] Current search params:", Object.fromEntries(searchParams?.entries() || []));
    
    const newParams = new URLSearchParams();
    
    // Set all params from the item
    Object.entries(item.params).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, value);
      }
    });

    console.log("[CommandPalette] Final URL params:", newParams.toString());
    router.replace(`${window.location.pathname}?${newParams.toString()}`, { scroll: false });
    
    if (setActiveTab && item.params.overviewTab) {
      console.log("[CommandPalette] Calling setActiveTab with:", item.params.overviewTab);
      setActiveTab(item.params.overviewTab);
    }
    
    onClose();
  }, [router, onClose, setActiveTab]);

  // Add keyboard shortcuts map
  // const keyboardShortcuts: { [key: string]: SearchResult } = {
  //   'c': navigationItems.find(item => item.id === 'checklist')!,
  //   'cl': navigationItems.find(item => item.id === 'client')!,
  //   'd': navigationItems.find(item => item.id === 'defendants')!,
  //   'i': navigationItems.find(item => item.id === 'incident')!,
  //   'w': navigationItems.find(item => item.id === 'witness')!,
  //   'o': navigationItems.find(item => item.id === 'other-parties')!,
  //   'm': navigationItems.find(item => item.id === 'medical-treatment')!,
  //   'h': navigationItems.find(item => item.id === 'health-insurance')!,
  //   'l': navigationItems.find(item => item.id === 'liens')!,
  //   'cc': navigationItems.find(item => item.id === 'case-costs')!,
  //   'doc': navigationItems.find(item => item.id === 'documents')!,
  //   'e': navigationItems.find(item => item.id === 'email')!,
  //   'lit': navigationItems.find(item => item.id === 'litigation')!,
  //   'dis': navigationItems.find(item => item.id === 'discovery')!,
  //   's': navigationItems.find(item => item.id === 'settlement')!,
  //   'sn': navigationItems.find(item => item.id === 'settlement-negotiation')!,
  //   'u': navigationItems.find(item => item.id === 'settlement-uim')!,
  //   'us': navigationItems.find(item => item.id === 'settlement-uim-settlement')!,
  //   'sa': navigationItems.find(item => item.id === 'settlement-advance')!,
  //   'ct': navigationItems.find(item => item.id === 'client-trust')!,
  // };

  // Add a function to handle keyboard shortcuts
  // const handleKeyboardShortcut = useCallback((input: string) => {
  //   const matchingShortcut = Object.entries(keyboardShortcuts).find(([shortcut]) => 
  //     shortcut.toLowerCase() === input.toLowerCase()
  //   );

  //   if (matchingShortcut) {
  //     handleSelect(matchingShortcut[1]);
  //     return true;
  //   }
  //   return false;
  // }, [keyboardShortcuts, handleSelect]);

  // Update search handling to include shortcuts
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearch(newValue);

    // Check if the input matches any keyboard shortcut
    // if (handleKeyboardShortcut(newValue)) {
    //   // Reset search after successful shortcut
    //   setSearch('');
    // }
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredItems.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredItems[selectedIndex]) {
          handleSelect(filteredItems[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        onClose();
        break;
    }
  }, [filteredItems, selectedIndex, handleSelect, onClose, isOpen]);

  useEffect(() => {
    // Reset selection when search changes
    setSelectedIndex(0);
  }, [search]);

  useEffect(() => {
    // Scroll selected item into view
    const selectedElement = listRef.current?.children[selectedIndex] as HTMLElement;
    if (selectedElement) {
      selectedElement.scrollIntoView({ block: 'nearest' });
    }
  }, [selectedIndex]);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey)) || 
          (e.key === 'k' && e.getModifierState('Meta'))) {
        e.preventDefault();
        // isOpen ? onClose() : onOpen();
      }
    };

    document.addEventListener('keydown', down);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', down);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose, handleKeyDown]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="p-0 overflow-hidden max-w-[500px]">
        <div className="flex flex-col mt-12">
          <div className="flex items-center px-3 mb-2">
            <input
              ref={inputRef}
              value={search}
              onChange={handleSearchChange}
              placeholder="Search or type a command (e.g. 'c' for Checklist, 's' for Settlement)..."
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              autoComplete="off"
              spellCheck={false}
            />
          </div>
          <div 
            ref={listRef}
            className="max-h-[300px] overflow-y-auto overflow-x-hidden"
          >
            {filteredItems.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                No results found.
              </div>
            ) : (
              <div className="p-2">
                <div className="text-xs font-medium text-muted-foreground px-2 mb-2">
                  Suggestions
                </div>
                {filteredItems.map((item, index) => (
                  <div
                    key={item.id}
                    onClick={() => handleSelect(item)}
                    onMouseEnter={() => setSelectedIndex(index)}
                    className={cn(
                      'flex items-center gap-2 px-4 py-2 text-sm cursor-pointer rounded-md',
                      'hover:bg-accent hover:text-accent-foreground',
                      selectedIndex === index && 'bg-accent text-accent-foreground'
                    )}
                  >
                    {item.icon}
                    <div className="flex-1">
                      <div className="font-medium">{item.title}</div>
                      {item.subtitle && (
                        <div className="text-xs text-muted-foreground">
                          {item.subtitle}
                        </div>
                      )}
                    </div>
                    {/* <div className="text-xs text-muted-foreground">
                      {Object.entries(keyboardShortcuts).find(([, shortcutItem]) => shortcutItem.id === item.id)?.[0]}
                    </div> */}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 