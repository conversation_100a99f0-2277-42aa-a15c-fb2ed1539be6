import { File as <PERSON><PERSON><PERSON>, Pin, PinOff, Download, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
} from "@/components/ui/context-menu";
import type { CaseContentItem, FolderContentItem } from "@/services/case-management/newDocumentManagement";
import { usePinFileMutation, usePinnedFileQuery } from "@/services/case-management/newDocumentManagement";
import { useToast } from "@/hooks/use-toast";

interface FileItemProps {
  item: CaseContentItem | FolderContentItem;
  level: number;
  onFileClick: (file: CaseContentItem | FolderContentItem) => void;
  onFileDownload: (file: CaseContentItem | FolderContentItem) => void;
  caseId: string;
  folderId?: string;
}

export function FileItem({
  item,
  level,
  onFileClick,
  onFileDownload,
  caseId,
  folderId
}: FileItemProps) {
  const { toast } = useToast();
  const pinFile = usePinFileMutation();
  const { data: pinnedFile, refetch: refetchPinnedFiles } = usePinnedFileQuery(caseId);

  // Check if the current file is pinned
  const isPinned = pinnedFile?.pinned_files?.some(file => file.id === item.id);

  // Function to check if file is an image
  const isImageFile = (fileName: string): boolean => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerFileName = fileName.toLowerCase();
    return imageExtensions.some(ext => lowerFileName.endsWith(ext));
  };

  const handlePinUnpin = async () => {
    try {
      // If trying to pin and not an image file, show error
      if (!isPinned && !isImageFile(item.name)) {
        toast({
          title: "Error",
          description: "Only image files can be pinned",
          variant: "destructive",
        });
        return;
      }

      await pinFile.mutateAsync({
        case_id: caseId,
        file_id: item.id.toString(),
        file_name: item.name,
        ...(!isPinned && { folder_id: folderId }),
        ...(isPinned && { unpin: isPinned })
      });
      await refetchPinnedFiles();
      
      toast({
        title: "Success",
        description: isPinned ? "File unpinned successfully" : "File pinned successfully",
      });
    } catch (error) {
      console.error('Error toggling pin state:', error);
      toast({
        title: "Error",
        description: `Failed to ${isPinned ? 'unpin' : 'pin'} file`,
        variant: "destructive",
      });
    }
  };

  // Calculate padding based on level - add more padding for files under folders
  const paddingClass = level === 0 ? "" :
    level === 1 ? "pl-6" :
    level === 2 ? "pl-12" :
    level === 3 ? "pl-18" : "pl-24";

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <TooltipProvider>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <div
                className={cn(
                  "flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 cursor-pointer",
                  "transition-colors duration-200 w-full max-w-full overflow-hidden",
                  paddingClass
                )}
                onClick={() => onFileClick(item)}
              >
                <div className="inline-flex items-center gap-2 bg-[#F7F7F7] px-2 py-1 rounded overflow-hidden relative" style={{ maxWidth: 'calc(100% - 20px)' }}>
                  <FileIcon className="h-4 w-4 text-gray-700 flex-shrink-0" />
                  <span className="text-sm overflow-hidden text-ellipsis whitespace-nowrap font-Manrope" style={{ maxWidth: '100%' }}>{item.name}</span>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent sideOffset={10} side="right">
              <p className="font-Manrope">{item.name}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={() => onFileDownload(item)}>
          <Download className="mr-2 h-4 w-4" />
          Download
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onFileClick(item)}>
          <ExternalLink className="mr-2 h-4 w-4" />
          Open in Case Cockpit
        </ContextMenuItem>
        {(isImageFile(item.name) || isPinned) && (
          <>
            <ContextMenuSeparator />
            <ContextMenuItem onClick={handlePinUnpin}>
              {isPinned ? (
                <>
                  <PinOff className="mr-2 h-4 w-4" />
                  Unpin File
                </>
              ) : (
                <>
                  <Pin className="mr-2 h-4 w-4" />
                  Pin File
                </>
              )}
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
} 