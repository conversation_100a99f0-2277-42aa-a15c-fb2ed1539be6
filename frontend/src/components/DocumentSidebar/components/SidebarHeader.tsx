import { Image, Phone, Mail, X, User, FileText, Link, SquareArrowRight, Edit, SquareArrowLeft, ArrowLeft, RotateCw, ChevronDown, ChevronUp, MailPlus, Trash, PinOff } from "lucide-react";
import { CaseFullDetails } from "@/type/dashboard";
import { useUpdateCaseOrganizationStatus } from "@/services/caseService";
import { useOrganizationCaseStatuses } from "@/services/organizationService";
import { useQueryClient } from "@tanstack/react-query";
import { useState, useEffect, ReactNode, MouseEvent } from "react";
import { usePinnedFileQuery, usePinFileMutation } from "@/services/case-management/newDocumentManagement";
import { useClientBasicDetailsQuery, useClientContactDetailsQuery } from "@/services/case-management/clientDetailService";
import { useIncidentDetailsQuery, useUpdateIncidentDetailsMutation } from "@/services/incidentService";
import { parseISO, differenceInYears, format } from "date-fns";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { LucideIcon } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useCaseCourtDetailsQuery } from "@/services/letigation/litigationService";
import { useLinkedCasesQuery } from "@/services/case-management/linkCaseService";
import { Button } from "@/components/ui/button";
import { PhoneLink } from "@/components/ui/phone-link";
import caseManagementService from "@/services/caseManagementService";
import { MailLink } from "@/components/ui/mail-link";
import { LeftToggleButton } from "./ToggleButton";
import { useDispatch, useSelector } from "react-redux";
import { toggleDocumentSidebar } from "@/store/slices/authSlice";
import { RootState } from "@/store";
// import { CurrencyDisplay } from "@/components/ui/currency-display";
import { Input } from "@/components/ui/input";
import { INCIDENT_TYPE_CHOICES } from "@/type/lead/leadTypes";
import { CustomDateInput } from "@/components/ui/custom-date-input";
import { formatDateForDisplay } from "@/utils/dateUtils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import SelectLinkCaseOrLead from "@/components/CaseOverview/components/SelectLinkCaseOrLead";
import { CopyNumber } from "@/components/ui/copy-number";
import { CreateCardDialog } from "@/components/CaseOverview/components/CaseDetail";
import { useCombinedCardsQuery, useDeleteUserCardMutation } from "@/services/case-management/cardService";
import RichTextViewer from "@/components/ui/RichTextViewer";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { useToast } from "@/hooks/use-toast";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";

interface SidebarHeaderProps {
  selectedCase?: CaseFullDetails;
  onStatusUpdate?: (newStatus: string) => void;
}

interface InfoItemProps {
  icon: LucideIcon;
  value: string | number | ReactNode;
  iconClassName?: string;
}

export const InfoItem = ({ icon: Icon, value, iconClassName = "h-3.5 w-3.5 text-[#06021685]" }: InfoItemProps) => {
  return (
    <div className="flex items-center gap-3 self-stretch text-[#060216] font-Manrope">
      <Icon className={iconClassName} />
      <span className="text-sm font-Manrope">{value}</span>
    </div>
  );
};

interface ImageModalProps {
  files: Array<{ id: string; name: string; view_url: string }>;
  currentIndex: number;
  onClose: () => void;
  onNavigate: (newIndex: number) => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ files, currentIndex, onClose, onNavigate }) => {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'ArrowLeft') {
        handlePrevious(e);
      } else if (e.key === 'ArrowRight') {
        handleNext(e);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  const handlePrevious = (e: MouseEvent | KeyboardEvent) => {
    e.stopPropagation();
    const newIndex = currentIndex === 0 ? files.length - 1 : currentIndex - 1;
    onNavigate(newIndex);
  };

  const handleNext = (e: MouseEvent | KeyboardEvent) => {
    e.stopPropagation();
    const newIndex = currentIndex === files.length - 1 ? 0 : currentIndex + 1;
    onNavigate(newIndex);
  };

  const currentFile = files[currentIndex];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" onClick={onClose}>
      <div className="relative max-w-4xl max-h-[90vh] w-full p-4">
        <button
          className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md z-10 hover:bg-gray-100"
          onClick={onClose}
        >
          <X className="h-5 w-5 text-gray-600" />
        </button>

        {/* Navigation buttons */}
        {files.length > 1 && (
          <>
            <button
              className="absolute left-6 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow-md z-10 hover:bg-white transition-all"
              onClick={handlePrevious}
            >
              <SquareArrowLeft className="h-6 w-6 text-gray-600" />
            </button>
            <button
              className="absolute right-6 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow-md z-10 hover:bg-white transition-all"
              onClick={handleNext}
            >
              <SquareArrowRight className="h-6 w-6 text-gray-600" />
            </button>
          </>
        )}

        <img
          src={currentFile.view_url}
          alt={currentFile.name}
          className="max-w-full max-h-[85vh] object-contain mx-auto rounded-lg"
          onClick={(e) => e.stopPropagation()}
        />
        <div className="mt-2 text-center text-white">
          <div className="text-sm font-Manrope">{currentFile.name}</div>
          {files.length > 1 && (
            <div className="text-xs font-Manrope mt-1">
              {currentIndex + 1} / {files.length}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface ImageSliderProps {
  files: Array<{ id: string; name: string; view_url: string }>;
  onImageClick: (file: { view_url: string; name: string }, index: number) => void;
  isImageFile: (fileName: string) => boolean;
  onUnpin?: (file: { id: string; name: string }) => void;
}

const ImageSlider = ({ files, onImageClick, isImageFile, onUnpin }: ImageSliderProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrevious = (e: MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev === 0 ? files.length - 1 : prev - 1));
  };

  const handleNext = (e: MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev === files.length - 1 ? 0 : prev + 1));
  };

  const currentFile = files[currentIndex];

  if (!files || files.length === 0) {
    return (
      <div className="w-[70px] h-[70px] flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="flex flex-col items-center justify-center">
          <FileText className="h-5 w-5 text-gray-400" />
          <span className="text-xs text-gray-500 mt-1 font-Manrope">No Files</span>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <div className="absolute inset-0 flex items-center justify-center">
        <ContextMenu>
          <ContextMenuTrigger>
            <div className="w-20 h-20 rounded-lg overflow-hidden cursor-pointer border-2 border-white shadow-lg hover:opacity-90 transition-all relative group"
              onClick={(e) => {
                e.stopPropagation();
                onImageClick(currentFile, currentIndex);
              }}
            >
              {isImageFile(currentFile.name) ? (
                <img
                  src={currentFile.view_url}
                  alt={currentFile.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100">
                  <Image className="h-6 w-6 text-gray-400" />
                </div>
              )}

              {/* Navigation Buttons - Only show if more than one file */}
              {files.length > 1 && (
                <>
                  {/* Left half for previous button */}
                  <div className="absolute left-0 top-0 w-1/2 h-full group/left">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePrevious(e);
                      }}
                      className="absolute left-0 top-1/2 -translate-y-1/2 p-1 bg-white/80 rounded-r opacity-0 group-hover/left:opacity-100 transition-opacity hover:bg-white"
                    >
                      <SquareArrowLeft className="h-4 w-4 text-gray-600" />
                    </button>
                  </div>

                  {/* Right half for next button */}
                  <div className="absolute right-0 top-0 w-1/2 h-full group/right">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNext(e);
                      }}
                      className="absolute right-0 top-1/2 -translate-y-1/2 p-1 bg-white/80 rounded-l opacity-0 group-hover/right:opacity-100 transition-opacity hover:bg-white"
                    >
                      <SquareArrowRight className="h-4 w-4 text-gray-600" />
                    </button>
                  </div>
                </>
              )}
            </div>
          </ContextMenuTrigger>
          <ContextMenuContent>
            <ContextMenuItem onClick={() => onUnpin && onUnpin(currentFile)}>
              <PinOff className="mr-2 h-4 w-4" />
              Unpin File
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
      </div>

      {/* File Counter - Only show if more than one file */}
      {files.length > 1 && (
        <div className="absolute bottom-1 left-1/2 -translate-x-1/2 bg-white/80 px-2 py-1 rounded-full text-xs font-Manrope">
          {currentIndex + 1}/{files.length}
        </div>
      )}
    </div>
  );
};

export const SidebarHeader: React.FC<SidebarHeaderProps> = ({ selectedCase }) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const dispatch = useDispatch();
  const isDocumentSidebarVisible = useSelector((state: RootState) => state.auth.isDocumentSidebarVisible);
  const updateOrgStatus = useUpdateCaseOrganizationStatus();
  const { data: orgStatuses } = useOrganizationCaseStatuses();
  const { data: pinnedFile, refetch: refetchPinnedFiles } = usePinnedFileQuery(selectedCase?.id?.toString() || "");
  const { data: incidentDetails } = useIncidentDetailsQuery(selectedCase?.id || "");
  const { data: clientDetails } = useClientBasicDetailsQuery(selectedCase?.id || "");
  const { data: contactDetails } = useClientContactDetailsQuery(selectedCase?.id || "");
  const { data: courtDetails } = useCaseCourtDetailsQuery(selectedCase?.id || "");
  const { data: caseWorkers } = caseManagementService.useCaseWorkers(selectedCase?.id || "");
  const { data: linkedCases } = useLinkedCasesQuery(selectedCase?.id || "");
  const updateIncidentDetails = useUpdateIncidentDetailsMutation(selectedCase?.id || "");
  const [isEditingEstimatedValue, setIsEditingEstimatedValue] = useState(false);
  const [estimatedValue, setEstimatedValue] = useState("");
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [showAllLinkedCases, setShowAllLinkedCases] = useState(false);
  const [isEditingPrimaryContact, setIsEditingPrimaryContact] = useState(false);
  const { data: availableUsers } = caseManagementService.useAvailableUsers(selectedCase?.id || "");
  const updateWorkersMutation = caseManagementService.useUpdateCaseWorkers();
  const { data: combinedCards } = useCombinedCardsQuery(selectedCase?.id || "");
  const deleteCard = useDeleteUserCardMutation(selectedCase?.id || "");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [cardToDelete, setCardToDelete] = useState<number | null>(null);
  const { toast } = useToast();
  const pinFile = usePinFileMutation();

  const toggleSidebar = () => {
    dispatch(toggleDocumentSidebar());
  };

  useEffect(() => {
    if (incidentDetails?.estimated_value) {
      setEstimatedValue(incidentDetails.estimated_value);
    }
  }, [incidentDetails?.estimated_value]);

  const handleOrgStatusChange = (newStatusId: string) => {
    if (!selectedCase?.id) return;

    updateOrgStatus
      .mutateAsync({
        caseId: selectedCase.id.toString(),
        organizationStatusId: parseInt(newStatusId),
      })
      .then(() => {
        queryClient.invalidateQueries({
          queryKey: ["case", selectedCase.id],
        });
      });
  };

  const isImageFile = (fileName: string): boolean => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerFileName = fileName.toLowerCase();
    return imageExtensions.some(ext => lowerFileName.endsWith(ext));
  };

  const handleImageClick = (file: { view_url: string; name: string }, index: number) => {
    setSelectedImageIndex(index);
  };

  const openCaseInNewTab = (caseId: string) => {
    window.open(`/dashboard/case-view/${caseId}`, '_blank');
  };

  const handleEstimatedValueSave = () => {
    if (!selectedCase?.id) return;

    updateIncidentDetails.mutateAsync({
      estimated_value: estimatedValue
    }).then(() => {
      setIsEditingEstimatedValue(false);
    });
  };

  const handlePrimaryContactChange = (userId: string) => {
    if (!selectedCase?.id) return;

    updateWorkersMutation.mutate({
      caseId: selectedCase.id.toString(),
      data: {
        primary_contact: parseInt(userId)
      }
    }, {
      onSuccess: () => {
        setIsEditingPrimaryContact(false);
      }
    });
  };

  const handleDeleteCard = (cardId: number) => {
    setCardToDelete(cardId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteCard = () => {
    if (cardToDelete) {
      deleteCard.mutate(cardToDelete);
      setIsDeleteDialogOpen(false);
      setCardToDelete(null);
    }
  };

  const handleUnpinFile = async (file: { id: string; name: string }) => {
    try {
      await pinFile.mutateAsync({
        case_id: selectedCase?.id?.toString() || "",
        file_id: file.id,
        unpin: true,
        file_name: file.name
      });
      await refetchPinnedFiles();
      toast({
        title: "Success",
        description: "File unpinned successfully",
      });
    } catch (error) {
      console.error('Error unpinning file:', error);
      toast({
        title: "Error",
        description: "Failed to unpin file",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-white font-Manrope relative w-full max-w-full h-[100vh] overflow-x-hidden">
      <Card className="border-none h-[100vh] w-full max-w-full overflow-y-auto overflow-x-hidden scrollbar-thin">
        <CardContent className="p-0 w-full max-w-full overflow-x-hidden">
          <div className="flex items-center justify-between opacity-[0.5] text-[#060216]">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-[8px] text-[#060216] font-Manrope"
            >
              <ArrowLeft size={20} className="flex items-center w-[20px] h-[20px]" />
              <span className="font-Manrope text-sm leading-[24px] tracking-[0.15px]">Go Back</span>
            </button>
            <LeftToggleButton isOpen={isDocumentSidebarVisible} onClick={toggleSidebar} />
          </div>
          <Separator className="w-full my-2" />

        

          <div className="flex items-center gap-3 self-stretch">
            <div className="flex-1">
              {isEditingEstimatedValue ? (
                <div className="space-y-2 w-[180px]">
                  <Input
                    value={estimatedValue}
                    onChange={(e) => setEstimatedValue(e.target.value)}
                    className="h-8 text-xs w-[180px]"
                    autoFocus
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleEstimatedValueSave();
                      } else if (e.key === 'Escape') {
                        setIsEditingEstimatedValue(false);
                        setEstimatedValue(incidentDetails?.estimated_value || "");
                      }
                    }}
                  />
                </div>
              ) : (
                <>
                  <h2 className="text-[#060216] font-Manrope text-[14px] font-bold leading-[20px] tracking-[0.15px] uppercase"
                    style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                  >
                    {clientDetails?.first_name} {clientDetails?.last_name}
                  </h2>
                  <div className="text-[#06021685] font-Manrope text-[14px] leading-[20px] tracking-[0.15px]"
                    style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                  >
                    DOL {incidentDetails?.incident_date
                      ? parseISO(
                        incidentDetails.incident_date
                      ).toLocaleDateString("en-US", {
                        month: "2-digit",
                        day: "2-digit",
                        year: "numeric",
                      })
                      : ""}
                  </div>
                  <div className="text-[#06021685] font-Manrope text-[16px] font-medium leading-[20px] tracking-[0.15px]"
                    style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}
                  >
                    {courtDetails?.[0]?.case_number}
                  </div>
                </>
              )}
            </div>
            <div className="w-[70px] h-[70px] relative">
              {pinnedFile?.pinned_files && pinnedFile.pinned_files.length > 0 ? (
                <ImageSlider
                  files={pinnedFile.pinned_files}
                  onImageClick={(file) => handleImageClick(file, 0)}
                  isImageFile={isImageFile}
                  onUnpin={handleUnpinFile}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
                  <div className="flex flex-col items-center justify-center">
                    <FileText className="h-5 w-5 text-gray-400" />
                    <span className="text-xs text-gray-500 mt-1">No Files</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-wrap items-start gap-2 text-[#06021685] font-Manrope text-[14px] leading-[20px] tracking-[0.15px] mt-5"
            style={{ fontFeatureSettings: "'liga' off, 'clig' off" }}>
            {isEditingEstimatedValue ? (
              <div className="flex flex-col gap-2 w-[180px]">
                <Select
                  value={clientDetails?.gender || ""}
                  onValueChange={(value) => {
                    console.log("value", value);
                    // Implement gender change logic
                  }}
                >
                  <SelectTrigger className="h-8 text-xs w-[180px]">
                    <SelectValue placeholder="Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Male">Male</SelectItem>
                    <SelectItem value="Female">Female</SelectItem>
                    <SelectItem value="Non-binary">Non-binary</SelectItem>
                  </SelectContent>
                </Select>

                <CustomDateInput
                  value={clientDetails?.date_of_birth ? format(parseISO(clientDetails.date_of_birth), "yyyy-MM-dd") : ""}
                  onChange={(value) => {
                    console.log("value", value);
                    // Implement date of birth change logic
                  }}
                  placeholder="Date of Birth"
                  className="h-8 text-xs w-[180px]"
                />

                <Select
                  value={incidentDetails?.incident_type || ""}
                  onValueChange={(value) => {
                    console.log("value", value);
                    // Implement incident type change logic
                  }}
                >
                  <SelectTrigger className="h-8 text-xs w-[180px]">
                    <SelectValue placeholder="Incident Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {INCIDENT_TYPE_CHOICES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="flex gap-1 w-full">
                {clientDetails?.gender && (
                  <>
                    <span className="whitespace-nowrap font-Manrope">{clientDetails.gender}</span>
                    {(incidentDetails?.incident_type || clientDetails?.date_of_birth) && (<span className="font-Manrope">|</span>)}
                  </>
                )}

                {clientDetails?.date_of_birth && (
                  <>
                    <span className="whitespace-nowrap font-Manrope ml-1">
                      Age {differenceInYears(new Date(), parseISO(clientDetails.date_of_birth))}
                    </span>
                    {(incidentDetails?.incident_type) && (<span className="hidden sm:inline-block font-Manrope">|</span>)}
                  </>
                )}

                {clientDetails?.date_of_birth && (
                  <>
                    <span className="whitespace-nowrap font-Manrope ml-1">
                      {formatDateForDisplay(clientDetails.date_of_birth)}
                    </span>
                    {(incidentDetails?.incident_type) && (<span className="hidden sm:inline-block font-Manrope">|</span>)}
                  </>
                )}

                {incidentDetails?.incident_type && (
                  <span className="whitespace-nowrap w-full sm:w-auto font-Manrope">
                    {incidentDetails.incident_type
                      .split('_')
                      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                      .join(' ')}
                  </span>
                )}
              </div>
            )}
          </div>

          <Separator className="w-full my-2" />

          {/* Case Highlight Section */}
          <div>
            {/* <div className="flex gap-2 justify-between items-center">
              <h3 className="text-sm font-semibold text-black-600">Case Highlight</h3>
            </div> */}
            {!combinedCards?.user_cards?.[0] && (
              <div className="px-4 bg-gray-50 text-gray-500 text-sm rounded-lg mt-2 flex items-end flex-col">
                {selectedCase?.id && !combinedCards?.user_cards?.[0] && (
                  <CreateCardDialog caseId={selectedCase.id.toString()} onSuccess={() => { }} />
                )}
                <span className="text-gray-500 text-sm">No case highlights found. Add a note to get started.</span>
              </div>
            )}
            {combinedCards?.user_cards?.[0] && (
              <div className="mt-2">
                <div className="p-2 bg-[#E8F0EA] backdrop-blur-sm border border-[#E8F0EA] rounded-lg shadow-sm w-full group relative">
                  <div className="relative w-full bg-[#E8F0EA]">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm text-[#4B5563] tracking-tight truncate pr-2">
                        {combinedCards.user_cards[0].title}
                      </h3>
                      <div className="flex items-center -space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-200">
                        <CreateCardDialog
                          caseId={selectedCase?.id || ""}
                          mode="edit"
                          initialData={{
                            id: combinedCards.user_cards[0].id,
                            title: combinedCards.user_cards[0].title,
                            content: combinedCards.user_cards[0].content
                          }}
                          onSuccess={() => { }}
                        >
                          <button className="p-0.5 transition-transform duration-200 hover:scale-110">
                            <Edit className="h-3.5 w-3.5 text-green-600 hover:text-green-700" />
                          </button>
                        </CreateCardDialog>
                        <button
                          className="p-0.5 transition-transform duration-200 hover:scale-110 text-red-600"
                          onClick={() => handleDeleteCard(combinedCards.user_cards[0].id)}
                        >
                          <Trash className="h-3.5 w-3.5" />
                        </button>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-md font-Manrope break-words line-clamp-3">
                        {combinedCards.user_cards[0].content && <RichTextViewer data={combinedCards.user_cards[0].content} className="text-red-600" />}
                      </div>
                    </div>
                    <div className="flex items-center justify-between pt-2">
                      <div className="text-xs text-[#4B5563] truncate">
                        {combinedCards.user_cards[0].created_by?.name && `Added by ${combinedCards.user_cards[0].created_by.name}`}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Separator className="w-full my-3" />

          <div className="space-y-3">
            <InfoItem
              icon={Phone}
              value={
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <PhoneLink phone={contactDetails?.phone_number_1} />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Client&apos;s phone number</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              }
            />
            <InfoItem
              icon={Mail}
              value={
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="truncate max-w-[180px]">
                        <MailLink email={contactDetails?.primary_email} caseId={selectedCase?.id} />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Client&apos;s email address</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              }
            />

            <InfoItem
              icon={MailPlus}
              value={
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center min-w-0 max-w-[260px] truncate">
                        <CopyNumber value={`${selectedCase?.id?.replace(/^CASE-/, '').toUpperCase()}@BCC.ALPHALAW.IO`} />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Case email address</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              }
            />

  {/* Case Status at the top with emphasis */}
          <div className="flex items-center justify-between mb-2">
            <InfoItem
              icon={RotateCw}
              value={
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="bg-green-100 text-green-800 font-bold rounded px-3 py-1 flex items-center gap-2 min-w-0">
                        <Select
                          value={typeof selectedCase?.organization_status === 'object' ? selectedCase?.organization_status?.id?.toString() : selectedCase?.organization_status?.toString()}
                          onValueChange={handleOrgStatusChange}
                        >
                          <SelectTrigger className="h-6 text-sm p-0 border-none gap-3 bg-transparent font-bold">
                            <SelectValue asChild>
                              <span className="font-Manrope font-bold truncate max-w-[180px]">
                                {orgStatuses?.find(status => status.id?.toString() === (typeof selectedCase?.organization_status === 'object' ? selectedCase?.organization_status?.id?.toString() : selectedCase?.organization_status?.toString()))?.display_name || "Select status"}
                              </span>
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            {orgStatuses?.sort((a, b) => (a.order || 0) - (b.order || 0))?.map((status) => (
                              <SelectItem
                                key={status.id}
                                value={status.id?.toString() || ""}
                              >
                                {status.display_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </TooltipTrigger>
                    {/* <TooltipContent>
                      <p>Case organization status</p>
                    </TooltipContent> */}
                  </Tooltip>
                </TooltipProvider>
              }
            />
          </div>
              
            <InfoItem
              icon={Link}
              value={
                <div className="relative group">
                  <div className="flex items-center justify-between w-full">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center gap-1 flex-wrap group-hover:pr-6 whitespace-normal break-words w-full">
                            {linkedCases ? (() => {
                              const allCases = [...(linkedCases.direct_cases || []), ...(linkedCases.indirect_cases || [])];

                              if (allCases.length === 0) {
                                return <span className="text-gray-500 font-Manrope">No Linked Cases</span>;
                              }

                              const casesToShow = showAllLinkedCases ? allCases : allCases.slice(0, 2);

                              return (
                                <>
                                  {casesToShow.map((case_, index, array) => (
                                    <span key={case_.id} className="inline-flex items-center font-Manrope whitespace-normal break-words">
                                      <Button
                                        variant="link"
                                        className="h-auto p-0 text-primary hover:text-primary/80 font-Manrope text-[14px] whitespace-normal break-words"
                                        onClick={() => openCaseInNewTab(case_.id)}
                                      >
                                        {case_.case_name} {formatDateForDisplay(case_.accident_date)}
                                      </Button>
                                      {index < array.length - 1 && <span className="mx-1 font-Manrope">,</span>}
                                    </span>
                                  ))}
                                  {allCases.length > 2 && (
                                    <Button
                                      variant="link"
                                      className="h-auto p-0 ml-1 text-primary hover:text-primary/80 font-Manrope text-[14px]"
                                      onClick={() => setShowAllLinkedCases(!showAllLinkedCases)}
                                    >
                                      {showAllLinkedCases ? <ChevronUp /> : <ChevronDown />}
                                    </Button>
                                  )}
                                </>
                              );
                            })() : <span className="text-gray-500 font-Manrope">No Linked Cases</span>}
                            {selectedCase?.id && (
                              <SelectLinkCaseOrLead
                                entityId={selectedCase.id}
                                entityType="case"
                                className="absolute right-0 top-1/2 -translate-y-1/2 h-4 w-4 text-green-500 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                              />
                            )}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>linked cases</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              }
            />
            <InfoItem
              icon={User}
              value={
                <div className="relative group">
                  {isEditingPrimaryContact ? (
                    <Select
                      value={caseWorkers?.primary_contact?.toString()}
                      onValueChange={handlePrimaryContactChange}
                    >
                      <SelectTrigger className="h-6 text-sm p-0 border-none gap-3">
                        <SelectValue placeholder="Select primary contact" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableUsers?.map((user) => (
                          <SelectItem key={user.id} value={user.id.toString()}>
                            {user.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="group-hover:pr-6">
                            <span className="truncate inline-block max-w-[180px]">
                              {caseWorkers?.primary_contact_details?.name || "No Case Workers"}
                            </span>
                            <Edit
                              className="absolute right-0 top-1/2 -translate-y-1/2 h-4 w-4 text-green-500 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                              onClick={() => setIsEditingPrimaryContact(true)}
                            />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Primary Contact</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              }
            />
          </div>
        </CardContent>
      </Card>

      {selectedImageIndex !== null && pinnedFile?.pinned_files && (
        <ImageModal
          files={pinnedFile.pinned_files}
          currentIndex={selectedImageIndex}
          onClose={() => setSelectedImageIndex(null)}
          onNavigate={setSelectedImageIndex}
        />
      )}

      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDeleteCard}
        text="case highlight"
      />
    </div>
  );
};