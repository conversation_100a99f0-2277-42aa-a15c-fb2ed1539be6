import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SearchBar = ({ value, onChange, placeholder = "Search Documents" }: SearchBarProps) => {
  return (
    <div className="px-4 my-2">
      <div className="relative flex items-center bg-white transition-all">
        <Input
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="pr-9 pl-4 font-manrope text-xs line-height-1 border-gray-300 hover:border-gray-400 border-[1.5px] rounded-full"
        />
        <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
      </div>
    </div>
  );
}; 