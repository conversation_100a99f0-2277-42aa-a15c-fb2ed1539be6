import { useEffect } from "react";
import { DocumentFile } from "@/type/doocument";
import { styles } from "../styles";
import { DocumentName } from "./DocumentName";
import { DocumentActions } from "./DocumentActions";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { useUpdateCaseChatFile } from "@/services/documentService";
import { useDocumentContext } from '@/contexts/DocumentContext';
import { useCaseDocuments } from "@/hooks/useCaseDocuments";

export const DocumentList = ({ caseId, showLoader, searchQuery }: { caseId: string, showLoader: (show: boolean) => void, searchQuery: string }) => {
  const documents = useSelector((state: RootState) => state.caseView.allDocuments);
  const { updateCaseChatFile, isLoading: isUpdateLoading } = useUpdateCaseChatFile();
  const { handleOpenDocument } = useDocumentContext();
  const { updateDocumentState, isLoading: isDocumentsLoading } = useCaseDocuments(caseId);

  const filteredDocuments = documents.filter(doc => 
    doc.file_name.toLowerCase().includes(searchQuery?.toLowerCase() || '')
  );

  const handleDocumentSelection = (doc: DocumentFile) => {
    if (caseId) {
      updateCaseChatFile({
        caseId: caseId,
        documentName: doc.file_name,
        isPartOfCaseWithChat: !doc.is_part_of_case_with_chat
      }, {
        onSuccess: () => {
          updateDocumentState(true);
        }
      });
    }
  }

  useEffect(() => {
    showLoader(isDocumentsLoading || isUpdateLoading);
  }, [isDocumentsLoading, isUpdateLoading]);

  const handleViewDoc = (doc: DocumentFile) => {
    handleOpenDocument({
      name: doc.file_name,
      url: doc.view_url || '',
      id: doc.id.toString(),
      content_type: doc.content_type
    });
  }

  return (
    <div className="flex-1 overflow-auto h-[calc(100vh-440px)] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      {filteredDocuments.map((doc, index) => (
        <div key={doc.id || index} className={styles.documentItem}>
          <div className="flex justify-between w-full">
            <div className="flex items-center gap-2 w-[50%]">
              <input
                type="checkbox"
                checked={doc.is_part_of_case_with_chat}
                onChange={() => handleDocumentSelection(doc)}
                className="rounded border-gray-300"
                disabled={isUpdateLoading || isDocumentsLoading}
              />
              <DocumentName name={doc.file_name} />
            </div>
            <div className="w-[30%] flex justify-end">
              <DocumentActions
                doc={doc}
                onViewDoc={handleViewDoc}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
} 