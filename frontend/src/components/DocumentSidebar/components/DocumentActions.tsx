import { DocumentFile } from "@/type/doocument";
import { Eye, Download } from "lucide-react";

interface DocumentActionsProps {
    doc: DocumentFile;
    onViewDoc: (doc: DocumentFile) => void;
}

export const DocumentActions = ({ doc, onViewDoc }: DocumentActionsProps) => {
    return (
        <div className="flex items-center gap-2">
            <div 
                className={`w-2 h-2 rounded-full ${doc.is_indexed ? 'bg-green-500' : 'bg-red-500'}`}
                title={doc.is_indexed ? 'Indexed' : 'Not indexed'}
            />
            <button
                onClick={() => onViewDoc(doc)}
                className="p-1 hover:bg-gray-100 rounded-full"
                title="View document"
            >
                <Eye className="w-4 h-4 text-gray-500" />
            </button>
            <button
                onClick={() => window.open(doc.download_url, '_blank')}
                className="p-1 hover:bg-gray-100 rounded-full"
                title="Download document"
            >
                <Download className="w-4 h-4 text-gray-500" />
            </button>
        </div>
    );
}; 