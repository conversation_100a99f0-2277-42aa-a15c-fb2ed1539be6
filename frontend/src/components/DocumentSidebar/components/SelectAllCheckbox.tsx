import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useBulkUpdateCaseChatFiles } from "@/services/documentService";
import { useCaseDocuments } from '@/hooks/useCaseDocuments';

interface SelectAllCheckboxProps {
  caseId: string;
  showLoader: (value: boolean) => void;
}

export const SelectAllCheckbox = ({ caseId, showLoader }: SelectAllCheckboxProps) => {
  const allDocuments = useSelector((state: RootState) => state.caseView.allDocuments);
  const isSelected = allDocuments.length > 0 && allDocuments.every(doc => doc.is_part_of_case_with_chat);

  const { mutate: bulkUpdate } = useBulkUpdateCaseChatFiles();
  const { updateDocumentState } = useCaseDocuments(caseId || '');

  const handleChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.checked;
      if (caseId) {
      showLoader(true);
      bulkUpdate({
        caseId,
        isPartOfCaseWithChat: newValue
      }, {
        onSuccess: () => {
          updateDocumentState(true);
          showLoader(false);
        },
        onError: () => {
          showLoader(false);
        } 
      });
    }
  };



  return (
    <div className="px-3 py-2 border-b">
      <label className="flex items-center gap-2 cursor-pointer">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={handleChange}
          className="rounded border-gray-300"
        />
        <span className="text-sm font-medium">Select All</span>
      </label>
    </div>
  );
}; 