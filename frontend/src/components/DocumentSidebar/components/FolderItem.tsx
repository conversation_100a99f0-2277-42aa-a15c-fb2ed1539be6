import { Folder, ChevronDown, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { LoadingSpinner } from "./LoadingSpinner";
import type { CaseContentItem, FolderContentItem } from "@/services/case-management/newDocumentManagement";

interface FolderItemProps {
  item: CaseContentItem | FolderContentItem;
  level: number;
  isExpanded: boolean;
  onToggle: (folderId: string) => void;
  folderContents: FolderContentItem[] | undefined;
  renderContents: (items: CaseContentItem[] | FolderContentItem[], level: number, folderId: string) => React.ReactNode;
}

export function FolderItem({
  item,
  level,
  isExpanded,
  onToggle,
  folderContents,
  renderContents
}: FolderItemProps) {
  const itemId = item.id.toString();
  
  // Calculate padding based on level
  const paddingClass = level === 0 ? "" :
    level === 1 ? "pl-6" :
    level === 2 ? "pl-12" :
    level === 3 ? "pl-18" : "pl-24";

  return (
    <div className="space-y-1 w-full max-w-full overflow-hidden">
      <TooltipProvider>
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <div
              className={cn(
                "flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 cursor-pointer",
                "transition-colors duration-200 w-full max-w-full overflow-hidden",
                paddingClass
              )}
              onClick={() => onToggle(itemId)}
            >
              <Folder className="h-4 w-4 text-gray-700 flex-shrink-0" />
              <span className="text-sm truncate font-Manrope text-gray-500">{item.name}</span>
              <div className="ml-auto">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-500 flex-shrink-0" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500 flex-shrink-0" />
                )}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent sideOffset={10} side="right">
            <p className="font-Manrope">{item.name}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      {isExpanded && (
        <div className="w-full max-w-full overflow-hidden">
          {folderContents ? (
            renderContents(folderContents, level + 1, itemId)
          ) : (
            <div className="py-2 px-4">
              <LoadingSpinner />
            </div>
          )}
        </div>
      )}
    </div>
  );
} 