import { useCallback } from "react";
import { FolderItem } from "./FolderItem";
import { FileItem } from "./FileItem";
import type { CaseContentItem, FolderContentItem } from "@/services/case-management/newDocumentManagement";

interface FolderContentsProps {
  items: CaseContentItem[] | FolderContentItem[];
  level?: number;
  expandedFolders: Set<string>;
  folderContentsMap: Record<string, FolderContentItem[]>;
  onToggleFolder: (folderId: string) => void;
  onFileClick: (file: CaseContentItem | FolderContentItem) => void;
  onFileDownload: (file: CaseContentItem | FolderContentItem) => void;
  searchQuery?: string;
  caseId: string;
  currentFolderId?: string;
}

// Filter items based on search query
const filterItems = (items: CaseContentItem[], searchQuery: string) => {
  if (!searchQuery) return items;
  const searchLower = searchQuery.toLowerCase();
  return items.filter(item => item.name.toLowerCase().includes(searchLower));
};

export function FolderContents({
  items,
  level = 0,
  expandedFolders,
  folderContentsMap,
  onToggleFolder,
  onFileClick,
  onFileDownload,
  searchQuery = "",
  caseId,
  currentFolderId
}: FolderContentsProps) {
  // Memoize renderContents to prevent unnecessary re-renders
  const renderContents = useCallback((
    contentItems: CaseContentItem[] | FolderContentItem[],
    contentLevel: number,
    folderId: string
  ) => {
    return (
      <FolderContents
        items={contentItems}
        level={contentLevel}
        expandedFolders={expandedFolders}
        folderContentsMap={folderContentsMap}
        onToggleFolder={onToggleFolder}
        onFileClick={onFileClick}
        onFileDownload={onFileDownload}
        searchQuery={searchQuery}
        caseId={caseId}
        currentFolderId={folderId}
      />
    );
  }, [expandedFolders, folderContentsMap, onToggleFolder, onFileClick, onFileDownload, searchQuery, caseId]);

  // Sort items to show folders first, then files
  const sortedItems = [...filterItems(items as CaseContentItem[], searchQuery)].sort((a, b) => {
    if (a.type === 'folder' && b.type !== 'folder') return -1;
    if (a.type !== 'folder' && b.type === 'folder') return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="space-y-3 w-full max-w-full overflow-hidden">
      {sortedItems.map((item) => {
        const isFolder = item.type === 'folder';
        const itemId = item.id.toString();

        if (isFolder) {
          return (
            <FolderItem
              key={itemId}
              item={item}
              level={level}
              isExpanded={expandedFolders.has(itemId)}
              onToggle={onToggleFolder}
              folderContents={folderContentsMap[itemId]}
              renderContents={renderContents}
            />
          );
        }

        return (
          <FileItem
            key={itemId}
            item={item}
            level={level}
            onFileClick={onFileClick}
            onFileDownload={onFileDownload}
            caseId={caseId}
            folderId={currentFolderId}
          />
        );
      })}
    </div>
  );
}