import { FileText, File } from "lucide-react";

interface DocumentNameProps {
  name: string;
}

export const DocumentName = ({ name }: DocumentNameProps) => {
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="w-4 h-4 text-red-500" />;
      case 'doc':
      case 'docx':
        return <FileText className="w-4 h-4 text-[#060216]-500" />;
      default:
        return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="flex items-center gap-2">
      {getFileIcon(name)}
      <span className="text-sm truncate max-w-[160px]" title={name}>
        {name}
      </span>
    </div>
  );
}; 