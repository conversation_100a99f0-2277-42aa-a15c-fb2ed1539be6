import { ChevronLeft, ChevronRight, SquareArrowLeft, SquareArrowRight } from "lucide-react";
import { styles } from "../styles";
import { cn } from "@/lib/utils";

interface ToggleButtonProps {
  isOpen: boolean;
  onClick: () => void;
}

export const ToggleButton = ({ isOpen, onClick }: ToggleButtonProps) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-8 h-24 bg-white flex items-center justify-center transition-all duration-200 hover:bg-gray-50 border border-gray-200 rounded-r-md shadow-md cursor-pointer",
        isOpen ? styles.toggleButton : "fixed left-0 top-1/2 -translate-y-1/2 z-10"
      )}
      aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
    >
      {isOpen ? (
        <SquareArrowLeft className="w-5 h-5 text-gray-600" />
      ) : (
        <SquareArrowRight className="w-5 h-5 text-gray-600" />
      )}
    </button>
  );
};

export const LeftToggleButton = ({ isOpen, onClick }: ToggleButtonProps) => {
  return (
    <button
      onClick={onClick}
      className="w-8 h-8 bg-white flex items-center justify-center transition-all duration-200 hover:bg-gray-50 border border-gray-200 rounded-md shadow-sm cursor-pointer"
      aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
    >
      {isOpen ? (
        <SquareArrowLeft className="w-4 h-4 text-gray-600" />
      ) : (
        <SquareArrowRight className="w-4 h-4 text-gray-600" />
      )}
    </button>
  );
};

export const RightToggleButton = ({ isOpen, onClick }: ToggleButtonProps) => {
  return (
    <button
      onClick={onClick}
      className={styles.rightSidebarToggleButton}
      aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
    >
      {isOpen ? (
        <ChevronRight className="w-4 h-4 text-gray-600" />
      ) : (
        <ChevronLeft className="w-4 h-4 text-gray-600" />
      )}
    </button>
  );
}
