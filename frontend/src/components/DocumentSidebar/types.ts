import { DocumentMetadata } from "@/type/doocument";

export interface DocumentSidebarProps {
  allDocuments: DocumentMetadata[];
  parent?: 'case' | 'demand';
  isLoadingDocuments?: boolean;
  currentCaseId?: string;
  selectedCase?: { id: string };
  onDocumentSelection?: (data: { doc: DocumentMetadata; isChecked: boolean }) => void;
  onOpenDocument?: (data: { name: string; url: string; id: number }) => void;
  onBulkSelection?: (isSelected: boolean) => void;
} 