import { cn } from "@/lib/utils";

export const styles = {
  container: (isOpen: boolean) => cn(
    "bg-white shadow-sm relative overflow-hidden transition-all duration-300 ease-in-out flex flex-col items-center gap-[3px]",
    isOpen ? "border border-gray-200 w-[320px]" : "border-0 w-0"
  ),
  sidebar: "bg-white flex flex-col overflow-hidden transition-all duration-300 h-full overflow-auto",
  toggleButton: "bg-transparent fixed top-1/2 -translate-y-1/2 w-7 h-24 z-50 flex items-center justify-center" + 
    " transition-all duration-200 hover:bg-gray-50/90 border border-gray-200 rounded-r-lg shadow-sm" +
    " cursor-pointer left-0",
  rightSidebarToggleButton: "fixed top-1/2 -translate-y-1/2 w-7 h-24 bg-white/40 z-50 flex items-center justify-center" + 
    " transition-all duration-200 hover:bg-gray-50/90 border border-gray-200 rounded-l-lg shadow-sm" +
    " cursor-pointer right-0 backdrop-blur-md",
  rightSidebarContainer: (isOpen: boolean) =>
    `relative flex flex-col ${isOpen ? "w-[400px]" : "w-[50px]"} transition-all duration-300 ease-in-out border-l bg-white`,
  searchContainer: "flex items-center border rounded-full px-4 py-3 gap-2 focus-within:ring-1 focus-within:ring-[#6750A4]",
  documentItem: "px-3 py-2 flex items-center justify-between hover:bg-gray-50"
} 