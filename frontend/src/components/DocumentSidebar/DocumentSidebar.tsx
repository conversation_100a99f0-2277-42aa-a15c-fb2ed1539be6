"use client";

import { useState, useEffect, useCallback } from "react";
import { styles } from "@/components/DocumentSidebar/styles";
import { SidebarHeader } from "@/components/DocumentSidebar/components/SidebarHeader";
// import { SearchBar } from "@/components/DocumentSidebar/components/SearchBar";
// import { LoadingSpinner } from "@/components/DocumentSidebar/components/LoadingSpinner";
import { LeftToggleButton } from "@/components/DocumentSidebar/components/ToggleButton";
// import { FolderContents } from "@/components/DocumentSidebar/components/FolderContents";
import { cn } from "@/lib/utils";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import type { DetailedHTMLProps, HTMLAttributes } from 'react';
import {
  useCaseFolderContentsQuery,
  useFolderContentsQuery,
  type CaseContentItem,
  type FolderContentItem,
  useFileViewLinkQuery,
  useFileDownloadMutation,
} from "@/services/case-management/newDocumentManagement";
import { useDocumentContext } from "@/contexts/DocumentContext";
import { Document } from "@/type/doocument";
import { toggleDocumentSidebar } from "@/store/slices/authSlice";
// import { Separator } from "../ui/separator";
// import { useCombinedCardsQuery, useDeleteUserCardMutation } from "@/services/case-management/cardService";
// import RichTextViewer from "@/components/ui/RichTextViewer";
// import { X, Edit } from "lucide-react";
// import { CreateCardDialog } from "../CaseOverview/components/CaseDetail";

interface DocumentSidebarProps extends DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  isLoadingDocuments?: boolean;
  caseId: string;
}

export function DocumentSidebar({
  // isLoadingDocuments,
  className,
  caseId,
  ...props
}: DocumentSidebarProps) {
  const isOpen = useSelector((state: RootState) => state.auth.isDocumentSidebarVisible);
  const dispatch = useDispatch();

  // const [searchQuery, setSearchQuery] = useState("");
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [currentSelectedFile, setCurrentSelectedFile] = useState<CaseContentItem | FolderContentItem | null>(null);
  const [folderContentsMap, setFolderContentsMap] = useState<Record<string, FolderContentItem[]>>({});

  const selectedCase = useSelector((state: RootState) => state.caseView.selectedCaseDetail);
  const { setSelectedDocuments, setActiveDocumentId, selectedDocuments } = useDocumentContext();

  const { data: caseContents, isLoading: isLoadingContents } = useCaseFolderContentsQuery(caseId);
  const { data: folderContents } = useFolderContentsQuery(currentFolderId || caseContents?.root_folder?.id || '', {
    enabled: !!currentFolderId
  });
  const { data: fileViewData } = useFileViewLinkQuery(currentSelectedFile?.id || '', 60, {
    enabled: !!currentSelectedFile
  });
  const fileDownload = useFileDownloadMutation();

  // Add combined cards query
  // const { data: combinedCards } = useCombinedCardsQuery(caseId);
  // const firstUserCard = combinedCards?.user_cards?.[0];
  // const deleteCard = useDeleteUserCardMutation(caseId);
  // const [isDeleting, setIsDeleting] = useState(false);

  const toggleFolder = useCallback((folderId: string) => {
    setExpandedFolders((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
        setCurrentFolderId(folderId);
      }
      return newSet;
    });
  }, []);

  const handleFileClick = useCallback((file: CaseContentItem | FolderContentItem) => {
    if (file.type !== 'file') return;
    setCurrentSelectedFile(file);
  }, []);

  const handleFileDownload = useCallback(async (file: FolderContentItem | CaseContentItem) => {
    try {
      await fileDownload.mutateAsync(file.id.toString());
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  }, [fileDownload]);

  useEffect(() => {
    if (currentFolderId && folderContents) {
      setFolderContentsMap(prev => ({
        ...prev,
        [currentFolderId]: folderContents
      }));
    }
  }, [currentFolderId, folderContents]);

  useEffect(() => {
    if (!currentSelectedFile || !fileViewData?.view_link) return;

    const newDocument: Document = {
      id: currentSelectedFile.id,
      name: currentSelectedFile.name,
      url: fileViewData.view_link,
      content_type: 'file',
    };

    const exists = selectedDocuments.some(
      (doc: Document) => doc.id === currentSelectedFile.id
    );

    if (!exists) {
      setSelectedDocuments([...selectedDocuments, newDocument]);
    }
    setActiveDocumentId(currentSelectedFile.id);
    setCurrentSelectedFile(null);
  }, [currentSelectedFile, fileViewData, selectedDocuments, setSelectedDocuments, setActiveDocumentId]);

  const handleToggleSidebar = () => {
    dispatch(toggleDocumentSidebar());
  };

  // const handleDelete = async () => {
  //   if (!firstUserCard) return;
  //   setIsDeleting(true);
  //   try {
  //     await deleteCard.mutateAsync(firstUserCard.id);
  //     toast({
  //       title: "Success",
  //       description: "Case highlight deleted successfully",
  //     });
  //   } catch {
  //     toast({
  //       title: "Error",
  //       description: "Failed to delete case highlight",
  //       variant: "destructive",
  //     });
  //   } finally {
  //     setIsDeleting(false);
  //   }
  // };

  // const handleEditSuccess = () => {
  //   toast({
  //     title: "Success",
  //     description: "Case highlight updated successfully",
  //   });
  // };

  return (
    <>
      {!isOpen && (
        <LeftToggleButton isOpen={isOpen} onClick={handleToggleSidebar} />
      )}
      <div
        {...props}
        className={cn(
          styles.container(isOpen),
          className
        )}
      >
        {/* <div className="w-full flex flex-col overflow-hidden"> */}
          <SidebarHeader selectedCase={selectedCase} />
          {/* <div className="flex gap-2 justify-between flex-col">
            <div className="flex gap-2 justify-between items-center">
              <h3 className="text-sm font-semibold text-black-600 pl-3 ">Case Highlight</h3>
              {selectedCase?.id && (
                <CreateCardDialog caseId={selectedCase.id.toString()} onSuccess={() => { }} />
              )}
            </div> */}
          {/* {!firstUserCard && (
              <div className="px-4 py-3 bg-gray-50 text-gray-500 text-sm rounded-lg mx-3">
                No case highlights found. Add a note to get started.
              </div>
            )} */}
          {/* {firstUserCard && (
              <div className="flex items-center justify-between">
                <div className="p-4 bg-[#E8F0EA] backdrop-blur-sm border border-[#E8F0EA] rounded-lg shadow-sm w-full mx-3 group relative">
                  <div className="relative w-full bg-[#E8F0EA]">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm text-[#4B5563] tracking-tight truncate pr-2">
                        {firstUserCard.title}
                      </h3>
                      <div className="flex items-center -space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-200">
                        <CreateCardDialog
                          caseId={caseId}
                          mode="edit"
                          initialData={{
                            id: firstUserCard.id,
                            title: firstUserCard.title,
                            content: firstUserCard.content
                          }}
                          onSuccess={handleEditSuccess}
                        >
                          <button className="p-0.5 transition-transform duration-200 hover:scale-110">
                            <Edit className="h-3.5 w-3.5 text-green-600 hover:text-green-700" />
                          </button>
                        </CreateCardDialog>
                        <button
                          className="p-0.5 transition-transform duration-200 hover:scale-110"
                          onClick={handleDelete}
                          disabled={isDeleting}
                        >
                          <X className="h-3.5 w-3.5 text-gray-600 hover:text-red-600" />
                        </button>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-md text-black-600 break-words line-clamp-3">
                        {firstUserCard.content && <RichTextViewer data={firstUserCard.content} />}
                      </div>
                    </div>
                    <div className="flex items-center justify-between pt-2">
                      <div className="text-sm text-[#4B5563] truncate">
                        {firstUserCard.created_by?.name && `Added by ${firstUserCard.created_by.name}`}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div> */}
          {/* <Separator className="mb-3 mr-3 ml-3" /> */}
          {/* <div className="flex-1 overflow-y-auto">
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
            />
            <div className="px-3">
              {isLoadingContents || isLoadingDocuments ? (
                <div className="flex justify-center items-center h-32">
                  <LoadingSpinner />
                </div>
              ) : caseContents?.contents ? (
                <FolderContents
                  items={caseContents.contents}
                  expandedFolders={expandedFolders}
                  folderContentsMap={folderContentsMap}
                  onToggleFolder={toggleFolder}
                  onFileClick={handleFileClick}
                  onFileDownload={handleFileDownload}
                  searchQuery={searchQuery}
                  caseId={caseId}
                  currentFolderId={caseContents.root_folder.id}
                />
              ) : null}
            </div>
          </div> */}
        {/* </div> */}
      </div>
    </>
  );
}