import { useState, useMemo } from "react";
import { DocumentFile } from "@/type/doocument";

export const useDocumentFiltering = (documents: DocumentFile[]) => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredDocuments = useMemo(() => {
    const searchTerm = searchQuery.toLowerCase().trim();
    return documents.filter(doc => 
      doc.file_name.toLowerCase().includes(searchTerm)
    );
  }, [documents, searchQuery]);

  return { searchQuery, setSearchQuery, filteredDocuments };
} 