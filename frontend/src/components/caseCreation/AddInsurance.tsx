import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { UmUimLimit, MedpayLimit, PipLimit, CoverageStatus, LiabilityStatus, PolicyType, PlanType } from '@/constants/insurance'
import { default as AddEditInsuranceCompany } from "@/app/client-details/components/AddEditInsurance"
import { useToast } from "@/hooks/use-toast"
import { UseFormReturn } from "react-hook-form"
import { FormValues } from "./PostCaseCreationDialog"
import { Calendar } from "lucide-react"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import { format, parse } from "date-fns"
import RichTextEditor from "@/components/ui/RichTextEditor"

interface AddInsuranceProps {
  form: UseFormReturn<FormValues>
  insuranceCompanies?: Array<{
    id: number;
    name: string;
  }>
  caseData?: {
    insurance?: {
      no_insurance?: boolean;
      company_id?: number;
      plan_type?: string;
      claim_number?: string;
      policy_number?: string;
      medpay_claim_number?: string;
      insured_name?: string;
      um_uim?: string;
      medpay?: string;
      pip?: string;
      deductible?: string;
      status?: string;
      liability_status?: string;
      liability_type?: string;
      policy_type?: string;
      stacked?: string;
      vehicles?: string;
      claim_note?: string;
    };
    created_at?: string;
    incident_date?: string;
  }
  onSaveAndAddMore?: () => void;
}

// Add this helper function at the top of the file
const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
  <span className="flex gap-1">
    {children}
    <span className="text-red-500">*</span>
  </span>
);

export function AddInsurance({
  form,
  insuranceCompanies,
  caseData,
}: AddInsuranceProps) {

  const { toast } = useToast()


  const noAutoInsurance = form.watch("no_insurance");

  useEffect(() => {
    if (noAutoInsurance) {
      form.reset({
        ...form.getValues(),
        company: '',
        planType: '',
        claimNumber: '',
        policyNumber: '',
        medpayClaimNumber: '',
        insured: '',
        umUim: '',
        medpay: '',
        pip: '',
        deductible: '',
        coverageStatus: '',
        liabilityStatus: '',
        liabilityPercent: '',
        policyType: '',
        stacked: '',
        vehicles: '',
        claimNote: '',
        incident_date: ''
      });
    }
  }, [noAutoInsurance, form]);
  
  useEffect(() => {
    if (caseData?.insurance) {
      form.reset({
        ...form.getValues(),
        no_insurance: caseData.insurance.no_insurance ?? false,
        company: caseData.insurance.company_id?.toString() || '',
        planType: caseData.insurance.plan_type || '',
        claimNumber: caseData.insurance.claim_number || '',
        policyNumber: caseData.insurance.policy_number || '',
        medpayClaimNumber: caseData.insurance.medpay_claim_number || '',
        insured: caseData.insurance.insured_name || '',
        umUim: caseData.insurance.um_uim || '',
        medpay: caseData.insurance.medpay || '',
        pip: caseData.insurance.pip || '',
        deductible: caseData.insurance.deductible?.toString() || '',
        coverageStatus: caseData.insurance.status || '',
        liabilityStatus: caseData.insurance.liability_status || '',
        liabilityPercent: caseData.insurance.liability_type || '',
        policyType: caseData.insurance.policy_type || '',
        stacked: caseData.insurance.stacked || '',
        vehicles: caseData.insurance.vehicles || '',
        claimNote: caseData.insurance.claim_note || '',
        incident_date: caseData.incident_date || ''
      });
    } else {
      form.reset({
        ...form.getValues(),
        no_insurance: false,
        company: '',
        planType: '',
        claimNumber: '',
        policyNumber: '',
        medpayClaimNumber: '',
        insured: '',
        umUim: '',
        medpay: '',
        pip: '',
        deductible: '',
        coverageStatus: '',
        liabilityStatus: '',
        liabilityPercent: '',
        policyType: '',
        stacked: '',
        vehicles: '',
        claimNote: '',
        incident_date: ''
      });
    }
  }, [caseData, form]);

  const handleEditClick = () => {
    const selectedCompany = form.getValues("company");
    if (!selectedCompany) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a company first"
      });
      return false;
    }
    return true;
  };

  return (
    <div className="max-h-[60vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      <div className="space-y-4">
        <FormField
          control={form.control}
          name="no_insurance"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Checkbox
                  checked={field.value || false}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <FormLabel className="text-sm text-gray-600">No Insurance</FormLabel>
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-8">
          <FormField
            control={form.control}
            name="company"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel className="text-sm text-gray-600">
                  {!noAutoInsurance && <RequiredLabel>Company</RequiredLabel>}
                  {noAutoInsurance && "Company"}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || ''}
                  disabled={noAutoInsurance}
                >
                  <FormControl>
                    <SelectTrigger className={`bg-white ${fieldState.error ? 'border-red-500' : ''}`}>
                      <SelectValue placeholder="Select company" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {insuranceCompanies?.map((company) => (
                      <SelectItem
                        key={company.id}
                        value={company.id.toString()}
                      >
                        {company.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {fieldState.error && (
                  <div className="text-sm text-red-500 mt-1">
                    {fieldState.error.message}
                  </div>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="planType"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm text-gray-600">Plan Type</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                  <FormControl>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select plan type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(PlanType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-4">
          <AddEditInsuranceCompany
            isEdit={false}
            trigger={
              <Button
                variant="outline"
                className="border-teal-600 text-teal-600 hover:bg-teal-50"
              >
                ADD
              </Button>
            }
            onSuccess={(newInsuranceId) => {
              if (newInsuranceId) {
                form.setValue("company", newInsuranceId.toString(), { 
                  shouldValidate: true,
                  shouldDirty: true,
                  shouldTouch: true
                });
                // Force re-render of select
                setTimeout(() => {
                  form.trigger("company");
                }, 100);
              }
            }}
          />

          <AddEditInsuranceCompany
            isEdit={true}
            company={form.getValues("company")}
            trigger={
              <Button
                variant="outline"
                className="border-teal-600 text-teal-600 hover:bg-teal-50"
                onClick={handleEditClick}
                disabled={noAutoInsurance}
              >
                EDIT
              </Button>
            }
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="claimNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Claim #</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} className="bg-white" disabled={noAutoInsurance} />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="policyNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Policy #</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} className="bg-white" disabled={noAutoInsurance} />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="medpayClaimNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Medpay Claim #</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} className="bg-white" disabled={noAutoInsurance} />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="insured"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Insured</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} className="bg-white" disabled={noAutoInsurance} />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="umUim"
            render={({ field }) => (
              <FormItem>
                <FormLabel>UM / UIM</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                  <FormControl>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select UM / UIM" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(UmUimLimit).map((limit) => (
                      <SelectItem key={limit} value={limit}>
                        {limit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="medpay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Medpay</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                  <FormControl>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select medpay" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(MedpayLimit).map((limit) => (
                      <SelectItem key={limit} value={limit}>
                        {limit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="pip"
            render={({ field }) => (
              <FormItem>
                <FormLabel>PIP</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                  <FormControl>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select PIP" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(PipLimit).map((limit) => (
                      <SelectItem key={limit} value={limit}>
                        {limit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="deductible"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Deductible</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    className="bg-white"
                    placeholder="Enter deductible amount"
                    disabled={noAutoInsurance}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="coverageStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Coverage Status</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                  <FormControl>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select coverage status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(CoverageStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.toLowerCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <div>
            <FormLabel>Liability Status</FormLabel>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="liabilityStatus"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                      <FormControl>
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(LiabilityStatus).map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="liabilityPercent"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                      <FormControl>
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Select percentage" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {[...Array(101)].map((_, i) => (
                          <SelectItem key={i} value={`${i}`}>{`${i}%`}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={form.control}
            name="policyType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Policy Type</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noAutoInsurance}>
                  <FormControl>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select policy type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(PolicyType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="vehicles"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Vehicles</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} className="bg-white" disabled={noAutoInsurance} />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="claimNote"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Claim Note</FormLabel>
                <FormControl>
                  <RichTextEditor
                    value={field.value || ""}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    placeholder="Enter claim note"
                    className="bg-white resize-none min-h-[100px]"
                    disabled={noAutoInsurance}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="incident_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Date of Loss
                </FormLabel>
                <FormControl>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-gray-500" />
                    <CustomDateInput
                      value={field.value ? format(new Date(field.value), "dd-MM-yyyy") : ""}
                      onChange={(value: string) => {
                        const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                        if (!isNaN(parsedDate.getTime())) {
                          field.onChange(format(parsedDate, 'yyyy-MM-dd'));
                        }
                      }}
                      error={false}
                      maxDate={new Date()}
                      onError={(message: string) => {
                        console.error(message);
                      }}
                      disabled={noAutoInsurance}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  )
} 