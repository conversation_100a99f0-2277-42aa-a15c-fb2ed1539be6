import React, { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { INCIDENT_TYPE_CHOICES, MEDICAL_STATUS_CHOICES } from '@/type/lead/leadTypes'
import { BODY_PARTS } from '@/constants/bodyParts'
import { USStatesLabels } from '@/constants/commont'
import { CaseIncidentDetails, DocRequestStatus } from '@/type/incidentTypes'
import { format, parse } from 'date-fns'
import { UseFormReturn } from "react-hook-form"
import { FormValues } from "../caseCreation/PostCaseCreationDialog"
import {
  FileText, AlertTriangle, MapPin, Shield, 
  Stethoscope, Clock, Plus, Trash2, Calendar
} from 'lucide-react'
import RichTextEditor from "@/components/ui/RichTextEditor"

interface AffectedArea {
  part: string;
  description: string;
}

export interface AddIncidentDetailsProps {
  form: UseFormReturn<FormValues>
  onContinue: () => void
  affectedAreas: AffectedArea[]
  handleAddArea: () => void
  handleRemoveArea: (index: number) => void
  handleAreaChange: (index: number, field: 'part' | 'description', value: string) => void

  isPartSelected: (part: string, currentIndex: number) => boolean
  incidentDetails?: CaseIncidentDetails
}

const DOC_REQUEST_STATUS_DISPLAY: Record<DocRequestStatus, string> = {
  [DocRequestStatus.RECEIVED]: "Received",
  [DocRequestStatus.REQUESTED]: "Requested",
  [DocRequestStatus.DOES_NOT_EXIST]: "Does Not Exist",
  [DocRequestStatus.NOT_YET_REQUESTED]: "Not Yet Requested",
};

const ICON_CLASSES = "h-5 w-5 text-gray-500"

export function AddIncidentDetails({
  form,
  affectedAreas,
  handleAddArea,
  handleRemoveArea,
  handleAreaChange,
  isPartSelected,

  incidentDetails
}: AddIncidentDetailsProps) {
  useEffect(() => {
    if (incidentDetails) {
      form.reset({
        ...form.getValues(),
        insurance_status: incidentDetails.insurance_status ?? false,
        conflicts_checked: incidentDetails.conflicts_checked ?? false,
        surgery_required: incidentDetails.surgery_required ?? false,
        incident_date: incidentDetails.incident_date || '',
        incident_type: incidentDetails.incident_type || '',
        street1: incidentDetails.street1 || '',
        street2: incidentDetails.street2 || '',
        city: incidentDetails.city || '',
        state: incidentDetails.state || '',
        zip: incidentDetails.zip_code || '',
        incident_description: incidentDetails.incident_description || '',
        weather_conditions: incidentDetails.weather_conditions || '',
        police_report_details: incidentDetails.police_report_details || '',
        witness_information: incidentDetails.witness_information || '',
        previous_attorney: incidentDetails.previous_attorney || '',
        medical_status: incidentDetails.medical_status,
        basic_facts: incidentDetails.basic_facts || '',
        injury_type: incidentDetails.injury_type || '',
        injury_location: incidentDetails.injury_location || '',
        critical_conditions: incidentDetails.critical_conditions || '',
        liability_assessment: incidentDetails.liability_assessment || '',
        estimated_value: incidentDetails.estimated_value || '',
        statute_of_limitations: incidentDetails.statute_of_limitations || '',
        doc_request_status: incidentDetails.doc_request_status || DocRequestStatus.NOT_YET_REQUESTED,
        doc_request_notes: incidentDetails.doc_request_notes || ''
      });
    } else {
      // Initialize with default values if no case data
      form.reset({
        ...form.getValues(), // Keep existing values
        insurance_status: false,
        conflicts_checked: false,
        surgery_required: false,
        incident_date: '',
        incident_type: '',
        street1: '',
        street2: '',
        city: '',
        state: '',
        zip: '',
        incident_description: '',
        weather_conditions: '',
        police_report_details: '',
        witness_information: '',
        previous_attorney: '',
        medical_status: undefined,
        basic_facts: '',
        injury_type: '',
        injury_location: '',
        critical_conditions: '',
        liability_assessment: '',
        estimated_value: '',
        statute_of_limitations: '',
        doc_request_status: DocRequestStatus.NOT_YET_REQUESTED,
        doc_request_notes: ''
      });
    }
  }, [incidentDetails, form]);

  return (
    <div className="max-h-[60vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      {/* Incident Details Section */}
      <div className="border-b pb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
          <AlertTriangle className="h-5 w-5 text-primary" />
          Incident Details
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="incident_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Incident Type</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className={ICON_CLASSES} />
                      <SelectTrigger>
                        <SelectValue placeholder="Select Type" />
                      </SelectTrigger>
                    </div>
                  </FormControl>
                  <SelectContent>
                    {INCIDENT_TYPE_CHOICES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="incident_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Incident Date</FormLabel>
                <FormControl>
                  <div className="flex items-center gap-2">
                    <Calendar className={ICON_CLASSES} />
                    <CustomDateInput
                      value={field.value ? format(new Date(field.value), "dd-MM-yyyy") : ""}
                      onChange={(value: string) => {
                        if (!value) {
                          field.onChange(undefined);
                          return;
                        }
                        try {
                          const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                          if (!isNaN(parsedDate.getTime())) {
                            field.onChange(format(parsedDate, 'yyyy-MM-dd'));
                          }
                        } catch (error) {
                          console.error('Date parsing error:', error);
                          field.onChange(undefined);
                        }
                      }}
                      error={!!form.formState.errors.incident_date}
                      maxDate={new Date()}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <div className="col-span-2">
            <FormField
              control={form.control}
              name="incident_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <RichTextEditor
                      value={field.value ||""}
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                      placeholder="Enter incident description"
                      className="resize-none bg-gray-50 min-h-[100px]"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* Location Information Section */}
      <div className="border-b py-4">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
          <MapPin className="h-5 w-5 text-primary" />
          Location Information
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-3 grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="street1"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Street Address 1</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <MapPin className={ICON_CLASSES} />
                      <Input {...field} value={field.value || ''} placeholder="Enter Street Address 1" className="bg-gray-50" />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="street2"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Street Address 2</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <MapPin className={ICON_CLASSES} />
                      <Input {...field} value={field.value || ''} placeholder="Enter Street Address 2" className="bg-gray-50" />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <MapPin className={ICON_CLASSES} />
                      <Input {...field} value={field.value || ''} placeholder="Enter City" className="bg-gray-50" />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value || ''}>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <MapPin className={ICON_CLASSES} />
                        <SelectTrigger>
                          <SelectValue placeholder="Select State" />
                        </SelectTrigger>
                      </div>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(USStatesLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="zip"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Zip Code</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <MapPin className={ICON_CLASSES} />
                      <Input {...field} value={field.value || ''} placeholder="Enter Zip Code" className="bg-gray-50" />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div className="col-span-2">
            <FormField
              control={form.control}
              name="weather_conditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Weather Conditions</FormLabel>
                  <FormControl>
                    <RichTextEditor
                      value={field.value || ""}
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                      placeholder="Describe weather conditions during incident"
                      className="resize-none bg-gray-50 min-h-[100px]"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* Documentation Section */}
      <div className="border-b py-4">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
          <FileText className="h-5 w-5 text-primary" />
          Documentation
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="police_report_details"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Police Report Details</FormLabel>
                <FormControl>
                  <RichTextEditor
                    value={field.value || ""}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    placeholder="Enter police report details"
                    className="resize-none bg-gray-50 min-h-[100px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="doc_request_status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Document Request Status</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <FileText className={ICON_CLASSES} />
                      <SelectTrigger>
                        <SelectValue placeholder="Select Status" />
                      </SelectTrigger>
                    </div>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(DOC_REQUEST_STATUS_DISPLAY).map(([status, display]) => (
                      <SelectItem key={status} value={status}>
                        {display}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="doc_request_notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Document Request Notes</FormLabel>
                <FormControl>
                  <RichTextEditor
                    value={field.value || ""}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    placeholder="Enter document request notes"
                    className="resize-none bg-gray-50 min-h-[100px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Insurance & Legal Section */}
      <div className="border-b py-4">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
          <Shield className="h-5 w-5 text-primary" />
          Insurance & Legal
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="insurance_status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Insurance Status</FormLabel>
                <FormControl>
                  <div className="flex items-center gap-2">
                    <Shield className={ICON_CLASSES} />
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={field.value || false}
                        onCheckedChange={field.onChange}
                      />
                      <Label>Insured</Label>
                    </div>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="conflicts_checked"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Conflicts Check</FormLabel>
                <FormControl>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={field.value || false}
                      onCheckedChange={field.onChange}
                    />
                    <Label>Completed</Label>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="previous_attorney"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Previous Attorney</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} placeholder="Enter previous attorney details" className="bg-gray-50" />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="statute_of_limitations"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Statute of Limitations</FormLabel>
                <FormControl>
                  <div className="flex items-center gap-2">
                    <Clock className={ICON_CLASSES} />
                    <CustomDateInput
                      value={field.value ? format(new Date(field.value), "dd-MM-yyyy") : ""}
                      onChange={(value: string) => {
                        if (!value) {
                          field.onChange(undefined);
                          return;
                        }
                        try {
                          const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                          if (!isNaN(parsedDate.getTime())) {
                            field.onChange(format(parsedDate, 'yyyy-MM-dd'));
                          }
                        } catch (error) {
                          console.error('Date parsing error:', error);
                          field.onChange(undefined);
                        }
                      }}
                      error={!!form.formState.errors.statute_of_limitations}
                      minDate={new Date()}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Medical Information Section */}
      <div className="border-b py-4">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
          <Stethoscope className="h-5 w-5 text-primary" />
          Medical Information
        </h3>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="medical_status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Treatment Status</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value || ''}>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <Stethoscope className={ICON_CLASSES} />
                        <SelectTrigger>
                          <SelectValue placeholder="Select Status" />
                        </SelectTrigger>
                      </div>
                    </FormControl>
                    <SelectContent>
                      {MEDICAL_STATUS_CHOICES.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="injury_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Injury Type</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value || ''} placeholder="Enter Injury Type" className="bg-gray-50" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="injury_location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Injury Location</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value || ''} placeholder="Enter Injury Location" className="bg-gray-50" />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Injury Assessment Section */}
          <div className="pt-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium flex items-center gap-2 text-gray-800">
                <AlertTriangle className="h-5 w-5 text-primary" />
                Injury Assessment
              </h4>
              <Button
                variant="outline"
                size="sm"
                type="button"
                onClick={handleAddArea}
                className="flex items-center gap-2 hover:bg-primary/10"
              >
                <Plus className="h-4 w-4" />
                Add Area
              </Button>
            </div>
            
            <div className="space-y-3">
              {affectedAreas.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No affected areas added. Click &quot;Add Area&quot; to add one.
                </div>
              ) : (
                affectedAreas.map((area, index) => (
                  <div key={index} className="flex gap-3 items-start bg-gray-50 p-3 rounded-md">
                    <Select
                      value={area.part}
                      onValueChange={(value) => handleAreaChange(index, 'part', value)}
                    >
                      <FormControl>
                        <div className="flex items-center gap-2">
                          <FileText className={ICON_CLASSES} />
                          <SelectTrigger className="w-[200px]">
                            <SelectValue placeholder="Select body part" />
                          </SelectTrigger>
                        </div>
                      </FormControl>
                      <SelectContent>
                        {BODY_PARTS.map(part => (
                          <SelectItem 
                            key={part.value} 
                            value={part.value}
                            disabled={isPartSelected(part.value, index)}
                          >
                            {part.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Input 
                      value={area.description || ''}
                      onChange={(e) => handleAreaChange(index, 'description', e.target.value)}
                      placeholder="Describe the injury (optional)"
                      className="flex-1 bg-gray-50"
                    />
                    
                    <Button
                      variant="ghost"
                      size="icon"
                      type="button"
                      onClick={() => handleRemoveArea(index)}
                      className="hover:bg-red-50 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Case Assessment Section */}
      <div className="py-4">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4 text-gray-800">
          <FileText className="h-5 w-5 text-primary" />
          Case Assessment
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="basic_facts"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Basic Facts</FormLabel>
                <FormControl>
                  <RichTextEditor
                    value={field.value || ""}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    placeholder="Enter basic facts of the case"
                    className="resize-none bg-gray-50 min-h-[100px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="liability_assessment"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Liability Assessment</FormLabel>
                <FormControl>
                  <RichTextEditor
                    value={field.value || ""}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    placeholder="Enter liability assessment"
                    className="resize-none bg-gray-50 min-h-[100px]"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="estimated_value"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Estimated Value</FormLabel>
                <FormControl>
                  <Input {...field} type="number" value={field.value || ''} placeholder="Enter estimated value" className="bg-gray-50" />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  )
} 