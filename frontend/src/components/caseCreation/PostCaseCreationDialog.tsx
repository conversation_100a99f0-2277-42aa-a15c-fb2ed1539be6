'use client'

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useForm } from "react-hook-form"
import { Form } from "@/components/ui/form"
import { DocRequestStatus, MedicalStatus } from '@/type/incidentTypes'
import { useRouter } from "next/navigation"
import {
  InsuranceStatus
} from '@/type/case-management/clientDetailTypes';
import { z } from 'zod';
import { useInsuranceCompaniesQuery } from "@/services/orgAPIs"
import { useCaseQuery } from "@/services/caseService"
import { useToast } from "@/hooks/use-toast"

import { useUpdateIncidentDetailsMutation } from "@/services/incidentService"
import { useCreateClientInsuranceMutation } from "@/services/case-management/clientDetailService"
import { useCreateHealthInsuranceMutation } from "@/services/case-management/healthInsuranceService"

import { ClientInsuranceCreateRequest } from '@/type/case-management/clientDetailTypes'
import { zodResolver } from "@hookform/resolvers/zod"
import { AddInsurance } from "./AddInsurance"
import { AddHealthInsurance } from "./AddHealthInsurance"
import { AddIncidentDetails } from "./AddIncidentDetails"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"

interface PostCaseCreationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  caseId: string
}

interface AffectedArea {
  part: string;
  description: string;
}

const incidentFormSchema = z.object({
  insurance_status: z.boolean().optional(),
  conflicts_checked: z.boolean().optional(),
  surgery_required: z.boolean().optional(),
  incident_date: z.string().optional(),
  incident_type: z.string().optional(),
  street1: z.string().optional(),
  street2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  incident_description: z.string().optional(),
  weather_conditions: z.string().optional(),
  police_report_details: z.string().optional(),
  witness_information: z.string().optional(),
  previous_attorney: z.string().optional(),
  medical_status: z.nativeEnum(MedicalStatus).optional(),
  basic_facts: z.string().optional(),
  injury_type: z.string().optional(),
  injury_location: z.string().optional(),
  critical_conditions: z.string().optional(),
  liability_assessment: z.string().optional(),
  estimated_value: z.string().optional(),
  statute_of_limitations: z.string().optional(),
  doc_request_status: z.nativeEnum(DocRequestStatus).optional(),
  doc_request_notes: z.string().optional(),
  affected_areas: z.record(z.string()).optional(),
});

const insuranceFormSchema = z.discriminatedUnion('no_insurance', [
  z.object({
    no_insurance: z.literal(true),
    company: z.string().optional(),
    planType: z.string().optional(),
    claimNumber: z.string().optional(),
    policyNumber: z.string().optional(),
    medpayClaimNumber: z.string().optional(),
    insured: z.string().optional(),
    umUim: z.string().optional(),
    medpay: z.string().optional(),
    pip: z.string().optional(),
    deductible: z.string().optional(),
    coverageStatus: z.string().optional(),
    liabilityStatus: z.string().optional(),
    liabilityPercent: z.string().optional(),
    policyType: z.string().optional(),
    stacked: z.string().optional(),
    vehicles: z.string().optional(),
    claimNote: z.string().optional(),
  }),
  z.object({
    no_insurance: z.literal(false),
    company: z.string({
      required_error: "Insurance company is required when 'No Auto Insurance' is not selected"
    }),
    planType: z.string().optional(),
    claimNumber: z.string().optional(),
    policyNumber: z.string().optional(),
    medpayClaimNumber: z.string().optional(),
    insured: z.string().optional(),
    umUim: z.string().optional(),
    medpay: z.string().optional(),
    pip: z.string().optional(),
    deductible: z.string().optional(),
    coverageStatus: z.string().optional(),
    liabilityStatus: z.string().optional(),
    liabilityPercent: z.string().optional(),
    policyType: z.string().optional(),
    stacked: z.string().optional(),
    vehicles: z.string().optional(),
    claimNote: z.string().optional(),
  })
]);

const healthInsuranceFormSchema = z.discriminatedUnion('noInsurance', [
  z.object({
    noInsurance: z.literal(true),
    insuranceCompany: z.string().optional(),
    healthInsured: z.string().optional(),
    groupNumber: z.string().optional(),
    totalLien: z.string().optional(),
    adjustedLien: z.string().optional(),
    fileNumber: z.string().optional(),
    healthPlanType: z.string().optional(),
    erisa: z.string().optional(),
    medicare: z.string().optional(),
    note: z.string().optional(),
  }),
  z.object({
    noInsurance: z.literal(false),
    insuranceCompany: z.string({
      required_error: "Insurance company is required when 'No Health Insurance' is not selected"
    }),
    healthInsured: z.string().optional(),
    groupNumber: z.string().optional(),
    totalLien: z.string().optional(),
    adjustedLien: z.string().optional(),
    fileNumber: z.string().optional(),
    healthPlanType: z.string().optional(),
    erisa: z.string().optional(),
    medicare: z.string().optional(),
    note: z.string().optional(),
  })
]);

export interface FormValues {
  activeTab: "incident" | "insurance" | "health-insurance";
  // Incident form fields
  insurance_status?: boolean;
  conflicts_checked?: boolean;
  surgery_required?: boolean;
  incident_date?: string;
  incident_type?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  incident_description?: string;
  weather_conditions?: string;
  police_report_details?: string;
  witness_information?: string;
  previous_attorney?: string;
  medical_status?: MedicalStatus;
  basic_facts?: string;
  injury_type?: string;
  injury_location?: string;
  critical_conditions?: string;
  liability_assessment?: string;
  estimated_value?: string;
  statute_of_limitations?: string;
  doc_request_status?: DocRequestStatus;
  doc_request_notes?: string;
  affected_areas?: Record<string, string>;
  // Insurance form fields
  no_insurance: boolean;
  company?: string;
  planType?: string;
  claimNumber?: string;
  policyNumber?: string;
  medpayClaimNumber?: string;
  insured?: string;
  umUim?: string;
  medpay?: string;
  pip?: string;
  deductible?: string;
  coverageStatus?: string;
  liabilityStatus?: string;
  liabilityPercent?: string;
  policyType?: string;
  stacked?: string;
  vehicles?: string;
  claimNote?: string;
  // Health Insurance form fields
  noInsurance: boolean;
  insuranceCompany?: string;
  healthInsured?: string;
  groupNumber?: string;
  totalLien?: string;
  adjustedLien?: string;
  fileNumber?: string;
  healthPlanType?: string;
  erisa?: string;
  medicare?: string;
  note?: string;
}

// const formSchema = z.discriminatedUnion("activeTab", [
//   z.object({
//     activeTab: z.literal("incident"),
//     ...incidentFormSchema.shape,
//     ...z.object({
//       no_insurance: z.boolean(),
//       company: z.string().optional(),
//       planType: z.string().optional(),
//       claimNumber: z.string().optional(),
//       policyNumber: z.string().optional(),
//       medpayClaimNumber: z.string().optional(),
//       insured: z.string().optional(),
//       umUim: z.string().optional(),
//       medpay: z.string().optional(),
//       pip: z.string().optional(),
//       deductible: z.string().optional(),
//       coverageStatus: z.string().optional(),
//       liabilityStatus: z.string().optional(),
//       liabilityPercent: z.string().optional(),
//       policyType: z.string().optional(),
//       stacked: z.string().optional(),
//       vehicles: z.string().optional(),
//       claimNote: z.string().optional(),
//     }).shape,
//     ...z.object({
//       noInsurance: z.boolean(),
//       insuranceCompany: z.string().optional(),
//       healthInsured: z.string().optional(),
//       groupNumber: z.string().optional(),
//       totalLien: z.string().optional(),
//       adjustedLien: z.string().optional(),
//       fileNumber: z.string().optional(),
//       healthPlanType: z.string().optional(),
//       erisa: z.string().optional(),
//       medicare: z.string().optional(),
//       note: z.string().optional(),
//     }).shape
//   }),
//   z.object({
//     activeTab: z.literal("insurance"),
//     ...incidentFormSchema.shape,
//     ...z.object({
//       no_insurance: z.boolean(),
//       company: z.string().optional(),
//       planType: z.string().optional(),
//       claimNumber: z.string().optional(),
//       policyNumber: z.string().optional(),
//       medpayClaimNumber: z.string().optional(),
//       insured: z.string().optional(),
//       umUim: z.string().optional(),
//       medpay: z.string().optional(),
//       pip: z.string().optional(),
//       deductible: z.string().optional(),
//       coverageStatus: z.string().optional(),
//       liabilityStatus: z.string().optional(),
//       liabilityPercent: z.string().optional(),
//       policyType: z.string().optional(),
//       stacked: z.string().optional(),
//       vehicles: z.string().optional(),
//       claimNote: z.string().optional(),
//     }).shape,
//     ...z.object({
//       noInsurance: z.boolean(),
//       insuranceCompany: z.string().optional(),
//       healthInsured: z.string().optional(),
//       groupNumber: z.string().optional(),
//       totalLien: z.string().optional(),
//       adjustedLien: z.string().optional(),
//       fileNumber: z.string().optional(),
//       healthPlanType: z.string().optional(),
//       erisa: z.string().optional(),
//       medicare: z.string().optional(),
//       note: z.string().optional(),
//     }).shape
//   }),
//   z.object({
//     activeTab: z.literal("health-insurance"),
//     ...incidentFormSchema.shape,
//     ...z.object({
//       no_insurance: z.boolean(),
//       company: z.string().optional(),
//       planType: z.string().optional(),
//       claimNumber: z.string().optional(),
//       policyNumber: z.string().optional(),
//       medpayClaimNumber: z.string().optional(),
//       insured: z.string().optional(),
//       umUim: z.string().optional(),
//       medpay: z.string().optional(),
//       pip: z.string().optional(),
//       deductible: z.string().optional(),
//       coverageStatus: z.string().optional(),
//       liabilityStatus: z.string().optional(),
//       liabilityPercent: z.string().optional(),
//       policyType: z.string().optional(),
//       stacked: z.string().optional(),
//       vehicles: z.string().optional(),
//       claimNote: z.string().optional(),
//     }).shape,
//     ...z.object({
//       noInsurance: z.boolean(),
//       insuranceCompany: z.string().optional(),
//       healthInsured: z.string().optional(),
//       groupNumber: z.string().optional(),
//       totalLien: z.string().optional(),
//       adjustedLien: z.string().optional(),
//       fileNumber: z.string().optional(),
//       healthPlanType: z.string().optional(),
//       erisa: z.string().optional(),
//       medicare: z.string().optional(),
//       note: z.string().optional(),
//     }).shape
//   })
// ]) satisfies z.ZodType<FormValues>;

export function PostCaseCreationDialog({
  open,
  onOpenChange,
  caseId,
}: PostCaseCreationDialogProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"incident" | "insurance" | "health-insurance">("incident")
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const updateIncidentMutation = useUpdateIncidentDetailsMutation(caseId)

  const createInsuranceMutation = useCreateClientInsuranceMutation(caseId)
  const createHealthInsuranceMutation = useCreateHealthInsuranceMutation(caseId)

  const form = useForm<FormValues>({
    defaultValues: {
      activeTab: "incident",
      no_insurance: false,
      noInsurance: false
    },
    resolver: zodResolver(
      z.object({
        activeTab: z.enum(["incident", "insurance", "health-insurance"]),
        ...incidentFormSchema.shape,
        ...insuranceFormSchema._def.options[1].shape,
        ...healthInsuranceFormSchema._def.options[1].shape
      })
    )
  });

  const { data: insuranceCompanies } = useInsuranceCompaniesQuery();
  const { data: caseData } = useCaseQuery(caseId);
  const [affectedAreas, setAffectedAreas] = useState<AffectedArea[]>([]);
  const { toast } = useToast();

  // Reset form when case data changes
  useEffect(() => {
    if (caseData) {
      form.reset({
        ...form.getValues(),
        activeTab: activeTab,
        // Incident data
        insurance_status: caseData.incident?.insurance_status ?? false,
        conflicts_checked: caseData.incident?.conflicts_checked ?? false,
        surgery_required: caseData.incident?.surgery_required ?? false,
        incident_date: caseData.incident?.incident_date || '',
        incident_type: caseData.incident?.incident_type || '',
        street1: caseData.incident?.street1 || '',
        street2: caseData.incident?.street2 || '',
        city: caseData.incident?.city || '',
        state: caseData.incident?.state || '',
        zip: caseData.incident?.zip || '',
        incident_description: caseData.incident?.incident_description || '',
        weather_conditions: caseData.incident?.weather_conditions || '',
        police_report_details: caseData.incident?.police_report_details || '',
        witness_information: caseData.incident?.witness_information || '',
        previous_attorney: caseData.incident?.previous_attorney || '',
        medical_status: caseData.incident?.medical_status,
        basic_facts: caseData.incident?.basic_facts || '',
        injury_type: caseData.incident?.injury_type || '',
        injury_location: caseData.incident?.injury_location || '',
        critical_conditions: caseData.incident?.critical_conditions || '',
        liability_assessment: caseData.incident?.liability_assessment || '',
        estimated_value: caseData.incident?.estimated_value || '',
        statute_of_limitations: caseData.incident?.statute_of_limitations || '',
        doc_request_status: caseData.incident?.doc_request_status || DocRequestStatus.NOT_YET_REQUESTED,
        doc_request_notes: caseData.incident?.doc_request_notes || '',
        affected_areas: caseData.incident?.affected_areas || {},

        // Insurance data
        no_insurance: caseData.insurance?.no_insurance ?? false,
        company: caseData.insurance?.company_id?.toString() || '',
        planType: caseData.insurance?.plan_type || '',
        claimNumber: caseData.insurance?.claim_number || '',
        policyNumber: caseData.insurance?.policy_number || '',
        medpayClaimNumber: caseData.insurance?.medpay_claim_number || '',
        insured: caseData.insurance?.insured_name || '',
        umUim: caseData.insurance?.um_uim || '',
        medpay: caseData.insurance?.medpay || '',
        pip: caseData.insurance?.pip || '',
        deductible: caseData.insurance?.deductible?.toString() || '',
        coverageStatus: caseData.insurance?.status || '',
        liabilityStatus: caseData.insurance?.liability_status || '',
        liabilityPercent: caseData.insurance?.liability_type || '',
        policyType: caseData.insurance?.policy_type || '',
        stacked: caseData.insurance?.stacked || '',
        vehicles: caseData.insurance?.vehicles || '',
        claimNote: caseData.insurance?.claim_note || '',

        // Health Insurance data
        noInsurance: caseData.health_insurance?.no_insurance ?? false,
        insuranceCompany: caseData.health_insurance?.company_id?.toString() || '',
        healthInsured: caseData.health_insurance?.insured || '',
        groupNumber: caseData.health_insurance?.group_number || '',
        totalLien: caseData.health_insurance?.total_lien?.toString() || '',
        adjustedLien: caseData.health_insurance?.adjusted_lien?.toString() || '',
        fileNumber: caseData.health_insurance?.file_number || '',
        healthPlanType: caseData.health_insurance?.plan_type || '',
        erisa: caseData.health_insurance?.erisa || '',
        medicare: caseData.health_insurance?.medicare || '',
        note: caseData.health_insurance?.note || ''
      });
    }
  }, [caseData, form, activeTab]);

  const handleTabChange = (value: string) => {
    const newTab = value as "incident" | "insurance" | "health-insurance";
    setActiveTab(newTab);
  }

  const handleSubmit = async (data: FormValues, saveAndAddMore?: boolean) => {
    try {
      if (data.activeTab === "incident") {
        const formValues = data;
        const isValid = await form.trigger([
          'insurance_status',
          'conflicts_checked',
          'surgery_required',
          'incident_date',
          'incident_type',
          'street1',
          'street2',
          'city',
          'state',
          'zip',
          'incident_description',
          'weather_conditions',
          'police_report_details',
          'witness_information',
          'previous_attorney',
          'medical_status',
          'basic_facts',
          'injury_type',
          'injury_location',
          'critical_conditions',
          'liability_assessment',
          'estimated_value',
          'statute_of_limitations',
          'doc_request_status',
          'doc_request_notes',
          'affected_areas'
        ]);
        if (!isValid) return;

        const incidentPayload = Object.entries({
          insurance_status: formValues.insurance_status ?? false,
          conflicts_checked: formValues.conflicts_checked ?? false,
          surgery_required: formValues.surgery_required ?? false,
          incident_date: formValues.incident_date,
          incident_type: formValues.incident_type,
          street1: formValues.street1,
          street2: formValues.street2,
          city: formValues.city,
          state: formValues.state,
          zip: formValues.zip,
          incident_description: formValues.incident_description,
          weather_conditions: formValues.weather_conditions,
          police_report_details: formValues.police_report_details,
          witness_information: formValues.witness_information,
          previous_attorney: formValues.previous_attorney,
          medical_status: formValues.medical_status,
          basic_facts: formValues.basic_facts,
          injury_type: formValues.injury_type,
          injury_location: formValues.injury_location,
          critical_conditions: formValues.critical_conditions,
          liability_assessment: formValues.liability_assessment,
          estimated_value: formValues.estimated_value,
          statute_of_limitations: formValues.statute_of_limitations,
          doc_request_status: formValues.doc_request_status,
          doc_request_notes: formValues.doc_request_notes,
          affected_areas: formValues.affected_areas
        }).reduce((acc: Record<string, unknown>, [key, value]) => {
          if (value !== null && value !== undefined && value !== '') {
            acc[key] = value;
          }
          return acc;
        }, {} as Record<string, unknown>);

        await updateIncidentMutation.mutateAsync(incidentPayload);
        setActiveTab("insurance");

      } else if (data.activeTab === "insurance") {
        const formValues = data;
        
        // Skip validation if no_insurance is true
        let isValid = true;
        if (!formValues.no_insurance) {
          isValid = await form.trigger([
            'company' // Only validate company when no_insurance is false
          ]);
          if (!isValid) return;
        }

        try {
          if (!formValues.no_insurance) {
            const basePayload: ClientInsuranceCreateRequest = {
              no_insurance: false,
              insurance_company: formValues.company ? parseInt(formValues.company) : 0
            };

            // Add optional fields only if they exist
            if (formValues.planType) basePayload.plan_type = formValues.planType;
            if (formValues.claimNumber) basePayload.claim_number = formValues.claimNumber;
            if (formValues.policyNumber) basePayload.policy_number = formValues.policyNumber;
            if (formValues.coverageStatus) basePayload.status = formValues.coverageStatus as InsuranceStatus;
            if (formValues.claimNote) basePayload.claim_note = formValues.claimNote;
            if (formValues.medpayClaimNumber) basePayload.medpay_claim_number = formValues.medpayClaimNumber;
            if (formValues.insured) basePayload.insured_name = formValues.insured;
            if (formValues.medpay) basePayload.medpay = formValues.medpay;
            if (formValues.pip) basePayload.pip = formValues.pip;
            if (formValues.liabilityStatus) basePayload.liability_status = formValues.liabilityStatus;
            if (formValues.liabilityPercent) basePayload.liability_type = formValues.liabilityPercent;
            if (formValues.policyType) basePayload.policy_type = formValues.policyType;
            if (formValues.stacked) basePayload.stacked = formValues.stacked;
            if (formValues.vehicles) basePayload.vehicles = formValues.vehicles;
            if (formValues.deductible) basePayload.deductible = formValues.deductible;

            await createInsuranceMutation.mutateAsync(basePayload);
            toast({
              title: "Success",
              description: "Insurance details saved successfully"
            });
          } else {
            const noInsurancePayload: Partial<ClientInsuranceCreateRequest> = {
              no_insurance: true
            };
            await createInsuranceMutation.mutateAsync(noInsurancePayload as ClientInsuranceCreateRequest);
            toast({
              title: "Success",
              description: "No insurance status saved successfully"
            });
          }
          if (!saveAndAddMore) {
            setActiveTab("health-insurance");
          }
        } catch (error) {
          console.error('Failed to save insurance details:', error);
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to save insurance details"
          });
        }

      } else {
        const formValues = data;
        
        // Skip validation if noInsurance is true
        let isValid = true;
        if (!formValues.noInsurance) {
          isValid = await form.trigger([
            'insuranceCompany' // Only validate insuranceCompany when noInsurance is false
          ]);
          if (!isValid) return;
        }

        try {
          if (!formValues.noInsurance && formValues.insuranceCompany) {
            const healthInsurancePayload = {
              no_insurance: false as const,
              insurance_company: parseInt(formValues.insuranceCompany),
              ...(formValues.healthInsured && { insured: formValues.healthInsured }),
              ...(formValues.groupNumber && { group_number: formValues.groupNumber }),
              ...(formValues.totalLien && { total_lien: formValues.totalLien }),
              ...(formValues.adjustedLien && { adjusted_lien: formValues.adjustedLien }),
              ...(formValues.fileNumber && { file_number: formValues.fileNumber }),
              ...(formValues.healthPlanType && { plan_type: formValues.healthPlanType }),
              ...(formValues.erisa && { erisa: formValues.erisa }),
              ...(formValues.medicare && { medicare: formValues.medicare }),
              ...(formValues.note && { note: formValues.note })
            };

            await createHealthInsuranceMutation.mutateAsync(healthInsurancePayload);
            toast({
              title: "Success",
              description: "Health insurance details saved successfully"
            });
          } else {
            await createHealthInsuranceMutation.mutateAsync({
              no_insurance: true as const,
              note: formValues.note || undefined
            });
            toast({
              title: "Success",
              description: "No health insurance status saved successfully"
            });
          }
          
          if (!saveAndAddMore) {
            handleClose();
            router.push(`/dashboard/case-view/${caseId}`);
          }
        } catch (error) {
          console.error('Failed to save health insurance details:', error);
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to save health insurance details"
          });
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while saving the form"
      });
    }
  };

  const handleClose = () => {
    form.reset({
      activeTab: "incident",
      insurance_status: false,
      conflicts_checked: false,
      surgery_required: false,
      incident_date: '',
      incident_type: '',
      street1: '',
      street2: '',
      city: '',
      state: '',
      zip: '',
      incident_description: '',
      weather_conditions: '',
      police_report_details: '',
      witness_information: '',
      previous_attorney: '',
      medical_status: undefined,
      basic_facts: '',
      injury_type: '',
      injury_location: '',
      affected_areas: {},
      critical_conditions: '',
      liability_assessment: '',
      estimated_value: '',
      statute_of_limitations: '',
      doc_request_status: DocRequestStatus.NOT_YET_REQUESTED,
      doc_request_notes: '',
      no_insurance: false,
      company: '',
      insuranceCompany: '',
      planType: '',
      claimNumber: '',
      policyNumber: '',
      medpayClaimNumber: '',
      insured: '',
      umUim: '',
      medpay: '',
      pip: '',
      deductible: '',
      coverageStatus: '',
      liabilityStatus: '',
      liabilityPercent: '',
      policyType: '',
      stacked: '',
      vehicles: '',
      claimNote: '',
      note: '',
      groupNumber: '',
      totalLien: '',
      adjustedLien: '',
      fileNumber: '',
      erisa: '',
      medicare: '',
      noInsurance: false
    });
    setActiveTab("incident");
    setAffectedAreas([]);
    onOpenChange(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        onOpenChange(true);
      }
    }
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleAddArea = () => {
    if (affectedAreas.length === 0 ||
      (affectedAreas[affectedAreas.length - 1].part && affectedAreas[affectedAreas.length - 1].description)) {
      setAffectedAreas([...affectedAreas, { part: '', description: '' }]);
    }
  };

  const handleRemoveArea = (index: number) => {
    const newAreas = affectedAreas.filter((_, i) => i !== index);
    setAffectedAreas(newAreas.length === 0 ? [] : newAreas);
  };

  const handleAreaChange = (index: number, field: 'part' | 'description', value: string) => {
    const newAreas = [...affectedAreas];
    newAreas[index][field] = value;
    setAffectedAreas(newAreas);
  };

  const isPartSelected = (part: string, currentIndex: number) => {
    return affectedAreas.some((area, index) =>
      index !== currentIndex && area.part === part
    );
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-6xl">
          <DialogHeader>
            <DialogTitle>Case Setup</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={(e) => e.preventDefault()}>
              <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="incident">Incident Details</TabsTrigger>
                  <TabsTrigger value="insurance">Insurance</TabsTrigger>
                  <TabsTrigger value="health-insurance">Health Insurance</TabsTrigger>
                </TabsList>
                <div className="mt-4">
                  <TabsContent value="incident">
                    <AddIncidentDetails
                      form={form}
                      onContinue={() => handleSubmit(form.getValues())}
                      affectedAreas={affectedAreas}
                      handleAddArea={handleAddArea}
                      handleRemoveArea={handleRemoveArea}
                      handleAreaChange={handleAreaChange}
                      isPartSelected={isPartSelected}
                    />
                  </TabsContent>
                  <TabsContent value="insurance">
                    <AddInsurance
                      form={form}
                      insuranceCompanies={insuranceCompanies}
                      caseData={caseData}
                      onSaveAndAddMore={async () => {
                        const formValues = form.getValues();
                        await handleSubmit(formValues, true);
                        form.reset({
                          ...form.getValues(),
                          no_insurance: false,
                          company: '',
                          planType: '',
                          claimNumber: '',
                          policyNumber: '',
                          medpayClaimNumber: '',
                          insured: '',
                          umUim: '',
                          medpay: '',
                          pip: '',
                          deductible: '',
                          coverageStatus: '',
                          liabilityStatus: '',
                          liabilityPercent: '',
                          policyType: '',
                          stacked: '',
                          vehicles: '',
                          claimNote: '',
                          incident_date: ''
                        });
                      }}
                    />
                  </TabsContent>
                  <TabsContent value="health-insurance">
                    <AddHealthInsurance
                      form={form}
                      insuranceCompanies={insuranceCompanies}
                      caseData={caseData}
                      onSaveAndAddMore={async () => {
                        const formValues = form.getValues();
                        await handleSubmit(formValues, true);
                        form.reset({
                          ...form.getValues(),
                          noInsurance: false,
                          insuranceCompany: '',
                          healthInsured: '',
                          groupNumber: '',
                          totalLien: '',
                          adjustedLien: '',
                          fileNumber: '',
                          healthPlanType: '',
                          erisa: '',
                          medicare: '',
                          note: ''
                        });
                      }}
                    />
                  </TabsContent>
                </div>
              </Tabs>

              <div className="flex justify-end space-x-2 mt-4">
                <Button variant="outline" onClick={handleCancelClick}>
                  Cancel
                </Button>
                {(activeTab === "insurance" || activeTab === "health-insurance") && (
                  <Button
                    variant="outline"
                    className="border-teal-600 text-teal-600 hover:bg-teal-50"
                    onClick={async () => {
                      const formValues = form.getValues();
                      await handleSubmit(formValues, true);
                      form.reset({
                        ...form.getValues(),
                        ...(activeTab === "insurance" ? {
                          no_insurance: false,
                          company: '',
                          planType: '',
                          claimNumber: '',
                          policyNumber: '',
                          medpayClaimNumber: '',
                          insured: '',
                          umUim: '',
                          medpay: '',
                          pip: '',
                          deductible: '',
                          coverageStatus: '',
                          liabilityStatus: '',
                          liabilityPercent: '',
                          policyType: '',
                          stacked: '',
                          vehicles: '',
                          claimNote: '',
                          incident_date: ''
                        } : {
                          noInsurance: false,
                          insuranceCompany: '',
                          healthInsured: '',
                          groupNumber: '',
                          totalLien: '',
                          adjustedLien: '',
                          fileNumber: '',
                          healthPlanType: '',
                          erisa: '',
                          medicare: '',
                          note: ''
                        })
                      });
                    }}
                  >
                    Save & Add More
                  </Button>
                )}
                <Button onClick={() => handleSubmit(form.getValues())}>
                  {activeTab === "health-insurance" ? "Complete Setup" : "Continue"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  )
} 