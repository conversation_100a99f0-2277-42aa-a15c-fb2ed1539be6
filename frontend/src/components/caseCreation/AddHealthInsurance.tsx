import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { PlanType } from '@/constants/insurance'
import { default as AddEditInsuranceCompany } from "@/app/client-details/components/AddEditInsurance"
import { useEffect } from "react"
import { UseFormReturn } from "react-hook-form"
import { FormValues } from "../caseCreation/PostCaseCreationDialog"
import { format, parse } from "date-fns"
import { Calendar } from "lucide-react"
import { CustomDateInput } from "@/components/ui/custom-date-input"
import RichTextEditor from "@/components/ui/RichTextEditor"

interface AddHealthInsuranceProps {
    form: UseFormReturn<FormValues>;
    insuranceCompanies?: Array<{
        id: number;
        name: string;
    }>;
    caseData?: {
        health_insurance?: {
            no_insurance?: boolean;
            company_id?: number;
            insured?: string;
            group_number?: string;
            total_lien?: number;
            adjusted_lien?: number;
            file_number?: string;
            plan_type?: string;
            erisa?: string;
            medicare?: string;
            note?: string;
            incident_date?: string;
        };
    };
    onSaveAndAddMore?: () => void;
}

const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
    <div className="flex items-center gap-1">
        {children}
        <span className="text-red-500">*</span>
    </div>
);

export function AddHealthInsurance({
    form,
    insuranceCompanies,
    caseData,
}: AddHealthInsuranceProps) {
    const noInsurance = form.watch("noInsurance")

    // Initialize form values
    useEffect(() => {
        if (caseData?.health_insurance) {
            form.reset({
                ...form.getValues(), // Keep existing values
                noInsurance: caseData.health_insurance.no_insurance ?? false,
                insuranceCompany: caseData.health_insurance.company_id?.toString() || '',
                healthInsured: caseData.health_insurance.insured || '',
                groupNumber: caseData.health_insurance.group_number || '',
                totalLien: caseData.health_insurance.total_lien?.toString() || '',
                adjustedLien: caseData.health_insurance.adjusted_lien?.toString() || '',
                fileNumber: caseData.health_insurance.file_number || '',
                healthPlanType: caseData.health_insurance.plan_type || '',
                erisa: caseData.health_insurance.erisa || '',
                medicare: caseData.health_insurance.medicare || '',
                note: caseData.health_insurance.note ||"",
                incident_date: caseData.health_insurance.incident_date || ''
            })
        } else {
            // Initialize with default values if no case data
            form.reset({
                ...form.getValues(), // Keep existing values
                noInsurance: false,
                insuranceCompany: '',
                healthInsured: '',
                groupNumber: '',
                totalLien: '',
                adjustedLien: '',
                fileNumber: '',
                healthPlanType: '',
                erisa: '',
                medicare: '',
                note: "",
                incident_date: ''
            })
        }
    }, [caseData, form])

    // Clear insurance company when noInsurance is checked
    useEffect(() => {
        if (noInsurance) {
            form.setValue("insuranceCompany", "")
            form.setValue("healthInsured", "")
            form.setValue("groupNumber", "")
            form.setValue("totalLien", "")
            form.setValue("adjustedLien", "")
            form.setValue("fileNumber", "")
            form.setValue("healthPlanType", "")
            form.setValue("erisa", "")
            form.setValue("medicare", "")
            form.setValue("note", "")
            form.setValue("incident_date", "")
        }
    }, [noInsurance, form])

    return (
        <div className="max-h-[60vh] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <div className="space-y-6">
                <FormField
                    control={form.control}
                    name="noInsurance"
                    render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                                <Checkbox
                                    checked={field.value || false}
                                    onCheckedChange={(checked) => {
                                        field.onChange(checked)
                                        if (checked) {
                                            form.setValue("insuranceCompany", "")
                                        }
                                    }}
                                />
                            </FormControl>
                            <FormLabel>No Insurance</FormLabel>
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name="insuranceCompany"
                    render={({ field }) => (
                        <div>
                            <FormItem>
                                <FormLabel>
                                    {!noInsurance && <RequiredLabel>Insurance Company</RequiredLabel>}
                                    {noInsurance && "Insurance Company"}
                                </FormLabel>
                                <Select
                                    onValueChange={field.onChange}
                                    value={field.value || ''}
                                    disabled={noInsurance}
                                >
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select company" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {insuranceCompanies?.map((company) => (
                                            <SelectItem key={company.id} value={company.id.toString()}>
                                                {company.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <div className="flex space-x-4 mt-2">
                                    <AddEditInsuranceCompany 
                                        isEdit={false} 
                                        trigger={
                                            <Button variant="outline" className="border-teal-600 text-teal-600 hover:bg-teal-50" disabled={noInsurance}>
                                                ADD
                                            </Button>
                                        } 
                                        onSuccess={(newInsuranceId) => {
                                            if (newInsuranceId) {
                                                form.setValue("insuranceCompany", newInsuranceId.toString(), { 
                                                    shouldValidate: true,
                                                    shouldDirty: true,
                                                    shouldTouch: true
                                                });
                                                // Force re-render of select
                                                setTimeout(() => {
                                                    form.trigger("insuranceCompany");
                                                }, 100);
                                            }
                                        }}
                                    />
                                    {field.value && (
                                        <AddEditInsuranceCompany
                                            isEdit={true}
                                            company={field.value}
                                            trigger={
                                                <Button variant="outline" className="border-teal-600 text-teal-600 hover:bg-teal-50" disabled={noInsurance}>
                                                    EDIT
                                                </Button>
                                            }
                                        />
                                    )}
                                </div>
                            </FormItem>
                        </div>
                    )}
                />

                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="healthInsured"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Insured</FormLabel>
                                <FormControl>
                                    <Input {...field} value={field.value || ''} disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="groupNumber"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Group Number</FormLabel>
                                <FormControl>
                                    <Input {...field} value={field.value || ''} disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormField
                        control={form.control}
                        name="totalLien"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Total Lien</FormLabel>
                                <FormControl>
                                    <Input {...field} type="number" value={field.value || ''} placeholder="Enter total lien amount" disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="adjustedLien"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Adjusted Lien</FormLabel>
                                <FormControl>
                                    <Input {...field} type="number" value={field.value || ''} placeholder="Enter adjusted lien amount" disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="fileNumber"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>File Number</FormLabel>
                                <FormControl>
                                    <Input {...field} value={field.value || ''} disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormField
                        control={form.control}
                        name="incident_date"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Date of Loss</FormLabel>
                                <FormControl>
                                    <div className="flex items-center gap-2">
                                        <Calendar className="h-5 w-5 text-gray-500" />
                                        <CustomDateInput
                                            value={field.value ? format(new Date(field.value), "dd-MM-yyyy") : ""}
                                            onChange={(value: string) => {
                                                const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
                                                if (!isNaN(parsedDate.getTime())) {
                                                    field.onChange(format(parsedDate, 'yyyy-MM-dd'));
                                                }
                                            }}
                                            error={false}
                                            maxDate={new Date()}
                                            onError={(message: string) => {
                                                console.error("error: ", message);
                                            }}
                                            disabled={noInsurance}
                                        />
                                    </div>
                                </FormControl>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="healthPlanType"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Plan Type</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value || ''} disabled={noInsurance}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select plan type" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {Object.values(PlanType).map((type) => (
                                            <SelectItem key={type} value={type}>
                                                {type}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="erisa"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>ERISA</FormLabel>
                                <FormControl>
                                    <Input {...field} value={field.value || ''} disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormField
                        control={form.control}
                        name="medicare"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Medicare</FormLabel>
                                <FormControl>
                                    <Input {...field} value={field.value || ''} disabled={noInsurance} />
                                </FormControl>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="note"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Note</FormLabel>
                                <FormControl>
                                    <RichTextEditor
                                        value={field.value ||""}
                                        onChange={(value) => {
                                            field.onChange(value);
                                        }}
                                        placeholder="Enter note..."
                                        className="min-h-[100px] border-gray-200 focus:ring-2 focus:ring-blue-100"
                                        disabled={noInsurance}
                                    />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                </div>
            </div>
        </div>
    )
} 