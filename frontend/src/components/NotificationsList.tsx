import React from 'react';
import { useNotifications } from '@/hooks/use-notifications';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useMarkNotificationReadMutation, useArchiveNotificationMutation } from '@/services/notificationService';

export const NotificationsList: React.FC = () => {
  const { notifications, isLoading, error, fetchNextPage, hasNextPage, isFetchingNextPage } = useNotifications();
  const markAsRead = useMarkNotificationReadMutation();
  const archiveNotification = useArchiveNotificationMutation();

  const handleMarkAsRead = (id: string) => {
    markAsRead.mutate(id);
  };

  const handleArchive = (id: string, isArchived: boolean) => {
    archiveNotification.mutate({ id, isArchived });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return <div className="p-4 text-red-500">Error loading notifications: {error}</div>;
  }

  return (
    <div className="space-y-4 p-4">
      {notifications.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">
          No notifications to display
        </div>
      ) : (
        <>
          {notifications.map((notification) => (
            <Card
              key={notification.id}
              className={`${!notification.is_read ? 'border-l-4 border-l-primary' : ''}`}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-base">{notification.title}</CardTitle>
                  <div className="flex gap-2">
                    <Badge variant={notification.priority === 'HIGH' ? 'destructive' : notification.priority === 'MEDIUM' ? 'default' : 'outline'}>
                      {notification.priority}
                    </Badge>
                    {notification.requires_action && (
                      <Badge variant="secondary">Action Required</Badge>
                    )}
                  </div>
                </div>
                <CardDescription className="text-xs">
                  {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{notification.message}</p>
                {/* {notification.case && (
                  <p className="text-xs mt-2">
                    Case: <span className="font-medium">{notification.case.case_name}</span>
                  </p>
                )} */}
              </CardContent>
              <CardFooter className="flex justify-end gap-2 pt-2">
                {!notification.is_read && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleMarkAsRead(notification.id)}
                    disabled={markAsRead.isPending}
                  >
                    {markAsRead.isPending ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
                    Mark as Read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleArchive(notification.id, notification.is_archived)}
                  disabled={archiveNotification.isPending}
                >
                  {archiveNotification.isPending ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
                  {notification.is_archived ? 'Unarchive' : 'Archive'}
                </Button>
              </CardFooter>
            </Card>
          ))}

          {hasNextPage && (
            <div className="flex justify-center pt-4">
              <Button
                variant="outline"
                onClick={() => fetchNextPage()}
                disabled={isFetchingNextPage}
              >
                {isFetchingNextPage ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading more...
                  </>
                ) : (
                  'Load More'
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};