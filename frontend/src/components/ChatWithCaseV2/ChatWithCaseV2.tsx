import { useState, useEffect, useRef } from 'react';
import { useSendMessageMutation, useConversationsQuery, useUpdateConversationTitleMutation } from '@/services/ai-service/chat-with-case.service';
import { Button } from '@/components/ui/button';
import { Loader2, Send, MessageSquare, ChevronDown, Pencil, MoreVertical } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { websocketService } from '@/services/websocket.service';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format } from 'date-fns';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { motion, AnimatePresence } from 'framer-motion';
import { MarkdownContent } from '@/components/CaseChat/components/MarkdownContent';

// Update WebSocket message type to include all message types
type WebSocketMessageType = 'authentication' | 'chat_message' | 'authentication_successful' | 'typing_indicator' | 'notification_message';

interface WebSocketMessage {
  type: WebSocketMessageType;
  data?: {
    conversation_id?: string;
    type?: string;
    content?: string;
    message?: string;
    user?: {
      first_name: string;
      last_name: string;
    };
  };
  message?: string;
  timestamp?: string;
  status?: string;
  entity_type?: string;
  entity_id?: string;
}

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
  user?: {
    first_name: string;
    last_name: string;
  };
}

// Add interface for chat message from API
interface ApiChatMessage {
  role: string;
  content: string;
  created_at: string;
  user: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  } | null;
}

// Add interface for conversation
interface Conversation {
  id: string;
  title: string;
  created_at: string;
  messages: ApiChatMessage[];
  // Add other fields as needed
}

interface ChatWithCaseV2Props {
  caseId: string;
}

export function ChatWithCaseV2({ caseId }: ChatWithCaseV2Props) {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [conversationId, setConversationId] = useState<string | undefined>(undefined);
  const [isTyping, setIsTyping] = useState(false);
  const [editingTitle, setEditingTitle] = useState(false);
  const [newTitle, setNewTitle] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [showNewChatDialog, setShowNewChatDialog] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const sendMessage = useSendMessageMutation();
  const updateTitle = useUpdateConversationTitleMutation();
  const { data: conversationsData } = useConversationsQuery(caseId);

  useEffect(() => {
    if (!accessToken) {
      console.log('No access token available, skipping WebSocket connection');
      return;
    }

    console.log('Initializing WebSocket connection with token');
    websocketService.connect(accessToken);

    const unsubscribe = websocketService.subscribe((wsMessage: WebSocketMessage) => {
      console.log('Received WebSocket message in ChatWithCaseV2:', wsMessage);
      
      if (wsMessage.type === 'notification_message') {
        // Handle notification message
        const messageContent = wsMessage.data?.content || wsMessage.message;
        if (messageContent && wsMessage.data?.conversation_id === conversationId) {
          console.log('Adding notification message to chat:', messageContent);
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: messageContent,
            timestamp: wsMessage.timestamp
          }]);
          setIsTyping(false);
        }
      } else if (wsMessage.type === 'chat_message') {
        // Handle regular chat message
        const messageContent = wsMessage.data?.content || wsMessage.data?.message || wsMessage.message;
        const messageType = wsMessage.data?.type === 'assistant_response' ? 'assistant' : 'user';
        
        if (messageContent) {
          console.log('Adding chat message to conversation:', messageContent);
          setMessages(prev => [...prev, {
            role: messageType,
            content: messageContent,
            timestamp: wsMessage.timestamp,
            user: wsMessage.data?.user
          }]);
          setIsTyping(false);
        }
      } else if (wsMessage.type === 'typing_indicator') {
        console.log('Received typing indicator');
        setIsTyping(true);
      } else if (wsMessage.type === 'authentication_successful') {
        console.log('WebSocket authentication successful');
      }
    });

    return () => {
      console.log('Cleaning up WebSocket subscription');
      unsubscribe();
    };
  }, [accessToken, conversationId]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    try {
      setIsTyping(true);
      const response = await sendMessage.mutateAsync({
        conversationId: conversationId || '',
        message: message,
        caseId: caseId
      });

      if (response.data?.id && !conversationId) {
        setConversationId(response.data.id);
        setSelectedConversation({
          id: response.data.id,
          title: response.data.title || 'New Chat',
          created_at: response.data.created_at || new Date().toISOString(),
          messages: response.data.messages || []
        });
      }

      setMessages(prev => [...prev, {
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      }]);

      setMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      // Don't prevent default - allow new line
      return;
    }
  };

  const handleTitleUpdate = async () => {
    if (!conversationId || !newTitle.trim()) return;

    try {
      // Update the selected conversation with new title
      setSelectedConversation(prev => prev ? {
        ...prev,
        title: newTitle.trim()
      } : null);
      
      setEditingTitle(false);
    } catch (error) {
      console.error('Failed to update title:', error);
    }
  };

  const handleTitleEdit = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    setNewTitle(conversation.title);
    setEditingTitle(true);
  };

  const selectConversation = (conversation: Conversation) => {
    setConversationId(conversation.id);
    setSelectedConversation(conversation);
    // Convert API messages to component message format
    const formattedMessages: Message[] = conversation.messages.map((msg: ApiChatMessage) => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
      timestamp: msg.created_at,
      user: msg.user ? {
        first_name: msg.user.first_name,
        last_name: msg.user.last_name
      } : undefined
    }));
    setMessages(formattedMessages);
  };

  const handleStartNewChat = () => {
    setShowNewChatDialog(true);
  };

  const handleCreateNewChat = () => {
    setConversationId(undefined);
    setSelectedConversation(null);
    setMessages([]);
    setShowNewChatDialog(false);
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-white to-emerald-50/30 rounded-xl shadow-lg border border-emerald-100">
      <div className="flex items-center justify-between p-4 bg-white/80 backdrop-blur-sm border-b border-emerald-100 rounded-t-xl">
        <div className="flex items-center gap-3">
          <div className="flex flex-col">
            <h2 className="text-lg font-semibold text-gray-900">
              {selectedConversation ? selectedConversation.title : 'New Chat'}
            </h2>
            {selectedConversation && (
              <span className="text-xs text-emerald-600">
                {format(new Date(selectedConversation.created_at), 'MMM d, yyyy')}
              </span>
            )}
          </div>
          {selectedConversation && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleTitleEdit(selectedConversation)}
              className="h-8 w-8 p-0 rounded-full hover:bg-emerald-50"
            >
              <Pencil className="h-4 w-4 text-emerald-600" />
            </Button>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleStartNewChat}
            className="gap-2 bg-white"
          >
            <MessageSquare className="w-4 h-4" />
            <span>New Chat</span>
          </Button>
          
          {conversationsData?.data && conversationsData.data.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2 bg-white">
                  <MessageSquare className="w-4 h-4" />
                  <span>History</span>
                  <ChevronDown className="w-4 h-4 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[300px] p-2">
                <div className="mb-2 px-2 py-1.5 text-sm font-medium text-gray-500">Recent Conversations</div>
                {conversationsData.data.map((conversation) => (
                  <DropdownMenuItem
                    key={conversation.id}
                    onClick={() => selectConversation(conversation)}
                    className="flex items-start gap-3 px-2 py-3 rounded-lg cursor-pointer hover:bg-emerald-50"
                  >
                    <MessageSquare className="w-5 h-5 mt-0.5 text-emerald-500" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm text-gray-900 truncate">
                        {conversation.title}
                      </div>
                      <div className="text-xs text-gray-500 mt-0.5">
                        {format(new Date(conversation.created_at), 'MMM d, h:mm a')}
                      </div>
                    </div>
                    {conversation.id === conversationId && (
                      <div className="w-2 h-2 mt-1.5 bg-emerald-500 rounded-full" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4 max-w-3xl mx-auto">
          <AnimatePresence initial={false}>
            {messages.map((msg, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className={cn(
                  "flex items-start gap-3",
                  msg.role === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {msg.role === 'assistant' && (
                  <Avatar className="w-8 h-8 border-2 border-white shadow-sm">
                    <AvatarImage src="/ai-avatar.png" />
                    <AvatarFallback>AI</AvatarFallback>
                  </Avatar>
                )}
                <div
                  className={cn(
                    "relative max-w-[80%] rounded-2xl px-4 py-3 shadow-sm",
                    msg.role === 'user' 
                      ? 'bg-emerald-600 text-white ml-12' 
                      : 'bg-white border border-emerald-100 mr-12'
                  )}
                >
                  <div className={cn(
                    "prose prose-sm max-w-none markdown-content",
                    msg.role === 'user' ? 'text-white' : 'text-gray-800'
                  )}>
                    {msg.role === 'assistant' ? (
                      <MarkdownContent content={msg.content} />
                    ) : (
                      msg.content
                    )}
                  </div>
                  {msg.timestamp && (
                    <div className={cn(
                      "text-[10px] mt-1.5",
                      msg.role === 'user' ? 'opacity-70' : 'text-gray-500'
                    )}>
                      {format(new Date(msg.timestamp), 'h:mm a')}
                    </div>
                  )}
                </div>
                {msg.role === 'user' && (
                  <Avatar className="w-8 h-8 border-2 border-white shadow-sm">
                    <AvatarImage src="/user-avatar.png" />
                    <AvatarFallback>
                      {msg.user ? `${msg.user.first_name[0]}${msg.user.last_name[0]}` : 'U'}
                    </AvatarFallback>
                  </Avatar>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center gap-2 text-sm text-gray-500"
            >
              <div className="flex gap-1">
                <motion.div
                  animate={{ y: [0, -3, 0] }}
                  transition={{ duration: 0.6, repeat: Infinity, repeatDelay: 0.1 }}
                  className="w-1.5 h-1.5 bg-emerald-400 rounded-full"
                />
                <motion.div
                  animate={{ y: [0, -3, 0] }}
                  transition={{ duration: 0.6, repeat: Infinity, repeatDelay: 0.2 }}
                  className="w-1.5 h-1.5 bg-emerald-400 rounded-full"
                />
                <motion.div
                  animate={{ y: [0, -3, 0] }}
                  transition={{ duration: 0.6, repeat: Infinity, repeatDelay: 0.3 }}
                  className="w-1.5 h-1.5 bg-emerald-400 rounded-full"
                />
              </div>
              <span className="text-sm font-medium">AI is thinking...</span>
            </motion.div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <div className="p-4 bg-white border-t border-emerald-100 rounded-b-xl">
        <div className="max-w-3xl mx-auto">
          <div className="flex gap-2">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type your message..."
              className="min-h-[60px] max-h-[200px] resize-none rounded-xl border-emerald-200 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!message.trim() || sendMessage.isPending}
              className="px-4 h-[60px] rounded-xl bg-emerald-600 hover:bg-emerald-700"
            >
              {sendMessage.isPending ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </Button>
          </div>
          <div className="mt-2 text-xs text-center text-gray-500">
            Press Enter for new line. Click send button or use the send icon to send your message.
          </div>
        </div>
      </div>

      <Dialog open={editingTitle} onOpenChange={setEditingTitle}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Conversation Title</DialogTitle>
            <DialogDescription>
              Give your conversation a meaningful title to help you find it later.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            <Input
              value={newTitle}
              onChange={(e) => setNewTitle(e.target.value)}
              placeholder="Enter new title"
              className="col-span-3"
              autoFocus
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setEditingTitle(false)}
                className="px-4"
              >
                Cancel
              </Button>
              <Button
                onClick={handleTitleUpdate}
                disabled={!newTitle.trim() || updateTitle.isPending}
                className="px-4"
              >
                {updateTitle.isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : null}
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showNewChatDialog} onOpenChange={setShowNewChatDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Start New Chat</DialogTitle>
            <DialogDescription>
              Would you like to start a new chat? Your current conversation will be saved.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setShowNewChatDialog(false)}
              className="px-4"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateNewChat}
              className="px-4"
            >
              Start New Chat
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 