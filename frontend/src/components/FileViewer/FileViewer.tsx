"use client"

import { useEffect, useState, useMemo, useRef, useCallback } from "react";
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from "@cyntler/react-doc-viewer";
import { Document } from "@/type/doocument";
import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";
import { useDocumentContext } from "@/contexts/DocumentContext";
import Image from "next/image";

const getFileType = (fileName: string): string => {
    const extension = fileName.toLowerCase().split('.').pop() || '';
    const fileTypeMap: { [key: string]: string } = {
        //pdf
        'pdf': 'pdf',
        //video
        'mp4': 'media',
        'mkv': 'media',
        'avi': 'media',
        'webm': 'media',
        'mov': 'media',
        //audio
        'mp3': 'media',
        'wav': 'media',
        'aac': 'media',
        'ogg': 'media',
        'm4a': 'media',
        //docs
        'doc': 'office',
        'docx': 'office',
        //text
        'txt': 'text',
        //image
        'png': 'image',
        'jpg': 'image',
        'jpeg': 'image',
        'gif': 'image',
        'bmp': 'image',
        //excel
        'xlsx': 'office',
        'xls': 'office',
        //csv
        'csv': 'text',
        //powerpoint
        'ppt': 'office',
        'pptx': 'office'
    };

    return fileTypeMap[extension] || 'unknown';
};

const ImageViewer = ({ url, fileName }: { url: string; fileName: string }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000;
    const [imgDimensions, setImgDimensions] = useState({ width: 0, height: 0 });

    useEffect(() => {
        if (!url) return;

        const loadImage = () => {
            const img = new window.Image();
            img.onload = () => {
                setImgDimensions({
                    width: img.width,
                    height: img.height
                });
                setIsLoading(false);
                setError(null);
                setRetryCount(0);
            };
            img.onerror = () => {
                if (retryCount < MAX_RETRIES) {
                    setTimeout(() => {
                        setRetryCount(prev => prev + 1);
                        loadImage();
                    }, RETRY_DELAY * Math.pow(2, retryCount));
                } else {
                    setError('Failed to load image after multiple attempts');
                    setIsLoading(false);
                }
            };
            img.src = url;
        };

        loadImage();
    }, [url, retryCount]);

    useEffect(() => {
        setRetryCount(0);
        setError(null);
    }, [url]);

    return (
        <div className="w-full h-full flex items-center justify-center bg-gray-100">
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
            )}
            {error && (
                <div className="text-red-500">
                    Error loading image: {error}
                    {retryCount < MAX_RETRIES && (
                        <button
                            onClick={() => setRetryCount(prev => prev + 1)}
                            className="ml-2 text-blue-500 hover:text-blue-700"
                        >
                            Retry
                        </button>
                    )}
                </div>
            )}
            {!isLoading && !error && imgDimensions.width > 0 && (
                <div className="relative max-w-full max-h-full">
                    <Image
                        src={url}
                        alt={fileName}
                        width={imgDimensions.width}
                        height={imgDimensions.height}
                        className="max-w-full max-h-full object-contain"
                        style={{
                            opacity: isLoading ? 0 : 1,
                            transition: 'opacity 0.3s ease-in-out'
                        }}
                        priority
                    />
                </div>
            )}
        </div>
    );
};

const TextViewer = ({ url }: { url: string; }) => {
    const [content, setContent] = useState<string>('');
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const contentRef = useRef<HTMLPreElement>(null);

    useEffect(() => {
        const fetchContent = async () => {
            try {
                setIsLoading(true);
                const response = await fetch(url);
                if (!response.ok) throw new Error('Failed to fetch content');
                const text = await response.text();
                setContent(text);
                setError(null);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Failed to load content');
                console.error('Error loading text content:', err);
            } finally {
                setIsLoading(false);
            }
        };

        if (url) {
            fetchContent();
        }
    }, [url]);

    useEffect(() => {
        if (contentRef.current) {
            contentRef.current.textContent = content;
        }
    }, [content]);

    if (isLoading) {
        return (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <div className="text-red-500">Error loading content: {error}</div>
            </div>
        );
    }

    return (
        <div className="w-full h-full bg-white p-4 overflow-auto">
            <pre ref={contentRef} className="whitespace-pre-wrap font-mono text-sm">
                {content}
            </pre>
        </div>
    );
};

const OfficeViewer = ({ url }: { url: string; }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000;

    const getOfficeViewerUrl = (fileUrl: string) => {
        const encodedUrl = encodeURIComponent(fileUrl);
        return `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
    };

    useEffect(() => {
        if (!url) return;

        const updateIframe = () => {
            if (iframeRef.current) {
                try {
                    const viewerUrl = getOfficeViewerUrl(url);
                    iframeRef.current.src = viewerUrl;
                    setError(null);
                    setRetryCount(0);
                } catch (err) {
                    console.error('Error setting office viewer URL:', err);
                    if (retryCount < MAX_RETRIES) {
                        setTimeout(() => {
                            setRetryCount(prev => prev + 1);
                            updateIframe();
                        }, RETRY_DELAY * Math.pow(2, retryCount));
                    } else {
                        setError('Failed to load document after multiple attempts');
                    }
                }
            }
        };

        updateIframe();
    }, [url, retryCount]);

    useEffect(() => {
        setRetryCount(0);
        setError(null);
    }, [url]);

    return (
        <div className="w-full h-full flex flex-col">
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
            )}
            {error && (
                <div className="flex items-center justify-center text-red-500">
                    Error loading document: {error}
                    {retryCount < MAX_RETRIES && (
                        <button
                            onClick={() => setRetryCount(prev => prev + 1)}
                            className="ml-2 text-blue-500 hover:text-blue-700"
                        >
                            Retry
                        </button>
                    )}
                </div>
            )}
            <iframe
                ref={iframeRef}
                src={getOfficeViewerUrl(url)}
                className="w-full h-full border-0"
                onLoad={() => {
                    setIsLoading(false);
                    setError(null);
                    setRetryCount(0);
                }}
                onError={() => {
                    setIsLoading(false);
                    if (retryCount >= MAX_RETRIES) {
                        setError('Failed to load document');
                    }
                }}
            />
        </div>
    );
};

const PDFViewer = ({ url }: { url: string; }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const docRef = useRef<HTMLDivElement>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000;

    const createBlobUrl = useCallback(async () => {
        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error('Failed to fetch PDF');
            const blob = await response.blob();
            return URL.createObjectURL(blob);
        } catch (err) {
            console.error('Error creating blob URL:', err);
            throw err;
        }
    }, [url]);

    useEffect(() => {
        if (!url || !docRef.current) return;

        const loadPdf = async () => {
            try {
                setIsLoading(true);
                setError(null);

                const blobUrl = await createBlobUrl();
                // Store current ref value in a variable to use in cleanup
                const currentIframeRef = iframeRef.current;

                if (currentIframeRef) {
                    currentIframeRef.src = blobUrl;
                    setIsLoading(false);
                    setRetryCount(0);
                }
            } catch (err) {
                console.error('Error loading PDF:', err);
                if (retryCount < MAX_RETRIES) {
                    setTimeout(() => {
                        setRetryCount(prev => prev + 1);
                    }, RETRY_DELAY * Math.pow(2, retryCount));
                } else {
                    setError('Failed to load PDF after multiple attempts');
                    setIsLoading(false);
                }
            }
        };

        loadPdf();

        return () => {
            // Use the stored ref value in cleanup
            if (iframeRef.current?.src) {
                URL.revokeObjectURL(iframeRef.current.src);
            }
        };
    }, [url, retryCount, createBlobUrl]);

    useEffect(() => {
        setRetryCount(0);
        setError(null);
        setIsLoading(true);
    }, [url]);

    return (
        <div ref={docRef} className="relative w-full h-full bg-gray-100">
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center z-10">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
            )}
            {error && (
                <div className="absolute inset-0 flex items-center justify-center z-10">
                    <div className="text-red-500">
                        {error}
                        {retryCount < MAX_RETRIES && (
                            <button
                                onClick={() => setRetryCount(prev => prev + 1)}
                                className="ml-2 text-blue-500 hover:text-blue-700"
                            >
                                Retry
                            </button>
                        )}
                    </div>
                </div>
            )}
            <iframe
                ref={iframeRef}
                className={`w-full h-full border-0 ${isLoading || error ? 'invisible' : ''}`}
                style={{ background: 'white' }}
                onLoad={() => {
                    setIsLoading(false);
                    setError(null);
                }}
                onError={() => {
                    if (retryCount >= MAX_RETRIES) {
                        setError('Failed to load PDF');
                        setIsLoading(false);
                    }
                }}
            />
        </div>
    );
};

const MediaViewer = ({ url, fileName }: { url: string; fileName: string }) => {
    const getMediaType = (fileName: string): { isVideo: boolean; mimeType: string } => {
        const extension = fileName.toLowerCase().split('.').pop() || '';
        const videoFormats = {
            'mp4': 'video/mp4',
            'mov': 'video/quicktime',
            'webm': 'video/webm',
            'mkv': 'video/x-matroska',
            'avi': 'video/x-msvideo'
        };
        
        // Check if the extension exists in our formats map
        const mimeType = videoFormats[extension as keyof typeof videoFormats];
        
        return { 
            isVideo: true, 
            mimeType: mimeType || 'video/mp4' // Fallback to video/mp4 if not found
        };
    };

    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const videoRef = useRef<HTMLVideoElement>(null);
    const MAX_RETRIES = 3;

    useEffect(() => {
        // Reset states when URL changes
        setIsLoading(true);
        setError(null);
        setRetryCount(0);
    }, [url]);

    const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
        setIsLoading(false);
        const videoElement = e.currentTarget;
        const errorMessage = 'Failed to load video file';
        let errorDetails = '';
        
        if (videoElement.error) {
            switch (videoElement.error.code) {
                case 1:
                    errorDetails = 'The video loading was aborted';
                    break;
                case 2:
                    errorDetails = 'Network error while loading video';
                    break;
                case 3:
                    errorDetails = 'Error decoding video';
                    break;
                case 4:
                    errorDetails = 'Video format not supported';
                    break;
                default:
                    errorDetails = videoElement.error.message || 'Unknown error occurred';
            }
        } else {
            errorDetails = 'Unknown error occurred while loading video';
        }
        
        const fullErrorMessage = `${errorMessage}: ${errorDetails}`;
        setError(fullErrorMessage);
        
        // Enhanced error logging with more details
        const mediaType = getMediaType(fileName);
        console.error('Video Error Details:', {
            errorCode: videoElement.error?.code,
            errorMessage: errorDetails,
            videoURL: url,
            fileName: fileName,
            fileExtension: fileName.toLowerCase().split('.').pop() || '',
            mimeType: mediaType.mimeType,
            retryCount: retryCount,
            videoElement: {
                networkState: videoElement.networkState,
                readyState: videoElement.readyState,
                error: videoElement.error ? {
                    code: videoElement.error.code,
                    message: videoElement.error.message
                } : null
            }
        });

        // Increment retry count
        setRetryCount(prev => prev + 1);
    };

    const handleRetry = () => {
        if (videoRef.current) {
            setIsLoading(true);
            setError(null);
            
            // Try to reset the video element completely
            if (videoRef.current.src) {
                const currentSrc = videoRef.current.src;
                videoRef.current.src = '';
                setTimeout(() => {
                    if (videoRef.current) {
                        videoRef.current.src = currentSrc;
                        videoRef.current.load();
                    }
                }, 100);
            } else {
                videoRef.current.load();
            }
        }
    };

    // Get media type information
    const mediaTypeInfo = getMediaType(fileName);

    return (
        <div className="w-full h-full flex items-center justify-center bg-gray-100">
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
            )}
            {error ? (
                <div className="text-red-500 p-4 text-center">
                    <p className="mb-2">{error}</p>
                    <p className="mb-2 text-sm text-gray-600">File: {fileName} (Type: {mediaTypeInfo.mimeType})</p>
                    {retryCount < MAX_RETRIES && (
                        <button 
                            onClick={handleRetry}
                            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                        >
                            Try Again ({MAX_RETRIES - retryCount} attempts remaining)
                        </button>
                    )}
                    <button 
                        onClick={() => window.open(url, '_blank')}
                        className="mt-2 ml-2 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                    >
                        Open in New Tab
                    </button>
                </div>
            ) : (
                <video
                    ref={videoRef}
                    controls
                    playsInline
                    preload="metadata"
                    className="max-w-full max-h-full"
                    onLoadedData={() => setIsLoading(false)}
                    onError={handleVideoError}
                >
                    <source 
                        src={url} 
                        type={mediaTypeInfo.mimeType}
                    />
                    <p>Your browser does not support the video tag.</p>
                </video>
            )}
        </div>
    );
};

interface FileViewerProps {
    activeDocument?: {
        name: string;
        url: string;
        id: string;
    } | undefined;
    isLoading?: boolean;
    selectedDocuments?: Document[];
}

export function FileViewer({ activeDocument, isLoading, selectedDocuments }: FileViewerProps) {
    const [key, setKey] = useState(0);
    const { registerDocumentElement } = useDocumentContext();
    const viewerRef = useRef<HTMLDivElement>(null);

    const fileType = activeDocument ? getFileType(activeDocument.name) : null;

    useEffect(() => {
        if (activeDocument && viewerRef.current) {
            registerDocumentElement(activeDocument.id, viewerRef.current);
        }
    }, [activeDocument, registerDocumentElement]);

    const docs = useMemo(() => {
        if (!selectedDocuments) return [];

        return selectedDocuments
            .filter(doc => !['image', 'text', 'pdf', 'office'].includes(getFileType(doc.name)))
            .map((doc) => ({
                uri: doc.url,
                fileType: getFileType(doc.name),
                fileName: doc.name,
                id: doc.id
            }));
    }, [selectedDocuments]);

    useEffect(() => {
        if (activeDocument) {
            setKey(prev => prev + 1);
        }
    }, [activeDocument?.id]);

    if (isLoading) {
        return (
            <div className="h-full flex items-center justify-center">
                <span>Loading...</span>
            </div>
        );
    }

    if (!activeDocument) {
        return (
            <div className="h-full flex items-center justify-center">
                <span>Select a document to view</span>
            </div>
        );
    }

    const renderViewer = () => {
        switch (fileType) {
            case 'image':
                return (
                    <div ref={viewerRef} className="w-full h-full flex items-center justify-center bg-gray-100">
                        <ImageViewer
                            url={activeDocument.url}
                            fileName={activeDocument.name}
                        />
                    </div>
                );
            case 'text':
                return (
                    <div ref={viewerRef} className="w-full h-full flex items-center justify-center bg-gray-100">
                        <TextViewer
                            url={activeDocument.url}
                        // fileName={activeDocument.name}
                        />
                    </div>
                );
            case 'pdf':
                return (
                    <div ref={viewerRef} className="w-full h-full flex items-center justify-center bg-gray-100">
                        <PDFViewer
                            url={activeDocument.url}
                        // fileName={activeDocument.name} 
                        />
                    </div>
                );
            case 'office':
                return (
                    <div ref={viewerRef} className="w-full h-full flex items-center justify-center bg-gray-100">
                        <OfficeViewer
                            url={activeDocument.url}
                        // fileName={activeDocument.name} 
                        />
                    </div>
                );
            case 'media':
                return (
                    <div ref={viewerRef} className="w-full h-full flex items-center justify-center bg-gray-100">
                        <MediaViewer
                            url={activeDocument.url}
                            fileName={activeDocument.name}
                        />
                    </div>
                );
            default:
                return (
                    <DocViewer
                        key={key}
                        documents={docs}
                        initialActiveDocument={docs.find(doc => doc.fileName === activeDocument.name)}
                        pluginRenderers={DocViewerRenderers}
                        style={{ height: '100%' }}
                        config={{
                            header: {
                                disableHeader: true,
                                disableFileName: true,
                            },
                            pdfZoom: {
                                defaultZoom: 1.1,
                                zoomJump: 0.1,
                            },
                            pdfVerticalScrollByDefault: true,
                        }}
                    />
                );
        }
    };

    return (
        <div className="w-full h-[calc(100vh-194px)]">
            {renderViewer()}
        </div>
    );
} 