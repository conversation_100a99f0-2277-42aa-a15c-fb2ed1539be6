import { Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState } from "react";
import { Contact } from "./types";
import { CallDialog } from "./CallDialog";

interface CallViewProps {
  contacts: Contact[];
}

export function CallView({ contacts }: CallViewProps) {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false);

  const handleCall = (contact: Contact) => {
    setSelectedContact(contact);
    setIsCallDialogOpen(true);
  };

  return (
    <>
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {contacts.map((contact) => (
            <div
              key={contact.id}
              className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50"
            >
              <div>
                <p className="font-medium">{contact.name}</p>
                <p className="text-sm text-gray-500">{contact.phone}</p>
              </div>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => handleCall(contact)}
              >
                <Phone className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </ScrollArea>

      <CallDialog
        isOpen={isCallDialogOpen}
        onClose={() => setIsCallDialogOpen(false)}
        contact={selectedContact}
      />
    </>
  );
} 