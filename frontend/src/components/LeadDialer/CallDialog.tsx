import { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Contact } from "./types";

interface CallDialogProps {
  isOpen: boolean;
  onClose: () => void;
  contact: Contact | null;
}

export function CallDialog({ isOpen, onClose, contact }: CallDialogProps) {
  const [callDuration, setCallDuration] = useState(0);
  const [callStatus, setCallStatus] = useState<
    "connecting" | "active" | "ended"
  >("connecting");

  useEffect(() => {
    if (isOpen) {
      setCallStatus("connecting");
      const timer = setTimeout(() => setCallStatus("active"), 1000);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (callStatus === "active") {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [callStatus]);

  if (!contact) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] transition-all duration-300">
        <div className="flex flex-col items-center space-y-6 py-8">
          <div className="text-center">
            <h2 className="text-2xl font-semibold">{contact.name}</h2>
            <p className="text-gray-500">{contact.phone}</p>
            <p className="mt-2 text-sm">
              {callStatus === "connecting" ? (
                <span className="text-yellow-500 animate-pulse">
                  Connecting...
                </span>
              ) : callStatus === "active" ? (
                <span className="text-green-500">
                  {new Date(callDuration * 1000).toISOString().substr(14, 5)}
                </span>
              ) : (
                <span className="text-red-500">Call Ended</span>
              )}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 