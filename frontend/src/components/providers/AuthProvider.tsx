// src/components/providers/AuthProvider.tsx
"use client";
import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { PUBLIC_PATHS } from '@/constants/routes';
import { RootState } from '@/store';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, accessToken } = useSelector((state: RootState) => state.auth);
  useEffect(() => {
    const isPublicRoute = PUBLIC_PATHS.includes(pathname || '');
    const isAuthenticated = !!accessToken && !!user;

    if (!isAuthenticated && !isPublicRoute) {
      router.push(`/login?from=${pathname}`);
    }

    if (isAuthenticated && isPublicRoute) {
      router.push('/dashboard');
    }
  }, [user, accessToken, pathname, router]);

  return <>{children}</>;
}