import { CaseReportFilters, ValueRangeFilter } from "@/type/case-management/caseAnalyticsTypes";
import { Slider } from "@/components/ui/slider";
import { useState } from "react";

interface RangeSliderFilterProps {
  label: string;
  filterKeyPrefix: string;
  rangeData: ValueRangeFilter[] | undefined;
  filters: CaseReportFilters;
  setFilters: (filters: CaseReportFilters) => void;
  prefixSign: string;
}

export const RangeSliderFilter = ({
  label,
  filterKeyPrefix,
  rangeData,
  filters,
  setFilters,
  prefixSign='',
}: RangeSliderFilterProps) => {
  
  const minKey = `${filterKeyPrefix}_min` as keyof CaseReportFilters;
  const maxKey = `${filterKeyPrefix}_max` as keyof CaseReportFilters;
  
  const defaultMin = rangeData && rangeData[0] ? rangeData[0].min : 0;
  const defaultMax = rangeData && rangeData[0] ? rangeData[0].max : 0;
  
  // For value display and state tracking
  const [displayValues, setDisplayValues] = useState<[number, number]>([
    filters[minKey] ? parseFloat(filters[minKey] as string) : defaultMin,
    filters[maxKey] ? parseFloat(filters[maxKey] as string) : defaultMax
  ]);
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const [activeThumb, setActiveThumb] = useState<number>(0);
  
  // Format values for display
  const formatValue = (value: number) => {
    const isMaxValue = value === defaultMax;
    const suffix = isMaxValue ? '+' : '';
    return `${prefixSign}${value.toLocaleString('en-US', { maximumFractionDigits: 0 })}${suffix}`;
  };
  
  // Format abbreviated values (e.g., $307k)
  const formatAbbr = (value: number) => {
    // Add a plus sign if this is the maximum value
    const isMaxValue = value === defaultMax;
    const suffix = isMaxValue ? '+' : '';
    
    if (value >= 1000000) {
      return `${prefixSign}${(value / 1000000).toFixed(1)}M${suffix}`;
    } else if (value >= 1000) {
      return `${prefixSign}${(value / 1000).toFixed(0)}k${suffix}`;
    }
    return `${prefixSign}${value}${suffix}`;
  };

  
  const calculateStepSize = () => {
    if (!rangeData || !rangeData[0]) return 1;
    
    const range = rangeData[0].max - rangeData[0].min;
    
    if (range <= 100) return 1;
    if (range <= 1000) return 5;
    if (range <= 10000) return 100;
    return 1000;
  };


  if (!rangeData) return null;
    
    return (
      <div className="space-y-1 relative">
        <div className="flex justify-between items-center mb-2">
          <label className="text-sm font-medium">{label}</label>
          <div className="text-sm">
            {formatAbbr(displayValues[0])} - {formatAbbr(displayValues[1])}
          </div>
        </div>
        
        <div className="relative pt-6 pb-2 w-full max-w-sm mx-auto">
          {showTooltip && (
            <div 
              className="absolute -top-2 z-10 bg-green-500 text-white px-2 py-0.5 rounded text-xs font-medium transform -translate-x-1/2"
              style={{ 
                left: `${((displayValues[activeThumb] - rangeData[0].min) / (rangeData[0].max - rangeData[0].min)) * 100}%` 
              }}
            >
              {formatValue(displayValues[activeThumb])}
            </div>
          )}
          
          <Slider
            value={displayValues}
            min={rangeData[0].min}
            max={rangeData[0].max}
            step={calculateStepSize()}
            className="custom-slider w-full"
            onValueChange={(values) => {
              setDisplayValues(values as [number, number]);
              setActiveThumb(values.length > 1 && values[1] === displayValues[1] ? 1 : 0);
              setShowTooltip(true);
            }}
            onValueCommit={(values) => {
              const newValues = values as [number, number];
              setFilters({
                ...filters,
                [minKey]: newValues[0].toString(),
                [maxKey]: newValues[1].toString(),
                page: 1
              });
              setShowTooltip(false);
            }}
          />
        </div>
      </div>
    );
  };