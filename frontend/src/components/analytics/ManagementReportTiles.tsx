"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { 
  ReportTile, 
  ReportType 
} from "@/type/case-management/caseAnalyticsTypes";

interface ManagementReportTilesProps {
  data: {
    tiles?: ReportTile[];
  } | null | undefined;
  reportType: ReportType;
  onTileClick: (filterValue: string) => void;
}

export function ManagementReportTiles({ data, reportType, onTileClick }: ManagementReportTilesProps) {
  // Check if data has tiles property
  if (!data || !data.tiles || !Array.isArray(data.tiles) || data.tiles.length === 0) {
    return null;
  }

  return (
    <>
      {data.tiles.map((tile: ReportTile) => (
        <Card
          key={tile.filter_value}
          className={cn(
            "relative overflow-hidden transition-all duration-300 cursor-pointer",
            "bg-white hover:bg-gradient-to-b hover:from-white hover:to-slate-50/70",
            "border rounded-xl",
            "hover:shadow-lg hover:translate-y-[-2px]",
            tile.active 
              ? "border-green-400/40 shadow-md shadow-green-300/20 ring-1 ring-green-400/30" 
              : "border-slate-200/80 shadow-sm hover:border-slate-300/80"
          )}
          onClick={() => onTileClick(tile.filter_value)}
        >
          <CardContent className="p-3">
            <div className="absolute top-0 left-0 right-0 h-1 bg-green-500"></div>
            
            <div className="flex flex-col mt-1">
              <span className="text-xs font-medium text-green-600 uppercase tracking-wider mb-0.5">
                {tile.title}
              </span>
              { reportType === "sourcePerformance" ? (
                <div className="flex flex-col gap-1">
                  <div className="flex items-baseline gap-1">
                    <span className="text-sm text-green-600">Leads:</span>
                    <span className="text-lg font-bold text-green-700">
                      {tile.lead_count || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-baseline gap-1">
                      <span className="text-xs text-green-600">Retained:</span>
                      <span className="text-xs font-medium text-green-700 bg-green-50 px-2 py-0.5 rounded-full">
                        {tile.retained_count || 0}
                      </span>
                    </div>
                    <div className="flex items-baseline gap-1">
                      <span className="text-xs text-green-600">Rate:</span>
                      <span className="text-xs font-medium text-green-700 bg-green-50 px-2 py-0.5 rounded-full">
                        {tile.conversion_rate || "0%"}
                      </span>
                    </div>
                  </div>
                </div>
              ) : null}

              {reportType === "settlementManagement" ? (
                <div className="flex flex-col gap-1">
                  <div className="flex items-baseline gap-1">
                    <span className="text-sm text-green-600">Total:</span>
                    <span className="text-lg font-bold text-green-700">
                      {tile.total || "$0.00"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    {tile.fees && (
                      <div className="flex items-baseline gap-1">
                        <span className="text-xs text-green-600">Fees:</span>
                        <span className="text-xs font-medium text-green-700 bg-green-50 px-2 py-0.5 rounded-full">
                          {tile.fees}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ) : null}
            </div>
            {
              tile.event_count && (
                <div className="flex items-center justify-between">
                  <span className="text-xs text-green-600">
                    Events Count: {tile.event_count || 0}
                  </span>
                </div>
              )
            }

            {tile.active && (
              <>
                <div className="absolute top-0 right-0 w-2 h-2 bg-green-500 rounded-full -mt-0.5 -mr-0.5 animate-pulse"></div>
                <div className="absolute top-0 left-0 w-full h-0.5 bg-green-500"></div>
                <div className="absolute inset-0 border border-green-400/30 rounded-xl pointer-events-none"></div>
              </>
            )}
          </CardContent>
        </Card>
      ))}
    </>
  );
} 