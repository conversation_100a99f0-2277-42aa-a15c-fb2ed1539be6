/* eslint-disable */
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface EmployeeCase {
  name: string;
  casesCount: number;
  activeCount: number;
  closedCount: number;
}

interface CaseStage {
  stage: string;
  count: number;
}

interface ReferralSource {
  source: string;
  count: number;
  value: number;
}

interface ExpectedSettlement {
  month: string;
  count: number;
  value: number;
}

export type ReportData = {
  "employee-cases": EmployeeCase[];
  "case-stages": CaseStage[];
  "referral-sources": ReferralSource[];
  "expected-settlements": ExpectedSettlement[];
};

// Mock data with proper typing
const mockData: ReportData = {
  "employee-cases": [
    { name: "<PERSON>", casesCount: 45, activeCount: 32, closedCount: 13 },
    { name: "<PERSON>", casesCount: 38, activeCount: 25, closedCount: 13 },
  ],
  "case-stages": [
    { stage: "Intake", count: 45 },
    { stage: "Investigation", count: 32 },
    { stage: "Negotiation", count: 28 },
    { stage: "Litigation", count: 15 },
  ],
  "referral-sources": [
    { source: "Google Ads", count: 85, value: 425000 },
    { source: "Referral Network", count: 65, value: 325000 },
    { source: "Website", count: 45, value: 225000 },
  ],
  "expected-settlements": [
    { month: "March 2024", count: 12, value: 600000 },
    { month: "April 2024", count: 15, value: 750000 },
    { month: "May 2024", count: 8, value: 400000 },
  ],
};

interface ReportResultsProps {
  reportType: keyof ReportData | "";
}

export function ReportResults({ reportType }: ReportResultsProps) {
  if (!reportType) return null;

  const data = mockData[reportType];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Report Results</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey={
                    reportType === "employee-cases"
                      ? "name"
                      : reportType === "case-stages"
                      ? "stage"
                      : reportType === "referral-sources"
                      ? "source"
                      : "month"
                  }
                />
                <YAxis />
                <Tooltip />
                <Bar
                  dataKey={
                    reportType === "employee-cases"
                      ? "casesCount"
                      : reportType === "case-stages"
                      ? "count"
                      : "value"
                  }
                  fill="#1C7B35"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                {reportType === "employee-cases" && (
                  <>
                    <TableHead>Employee</TableHead>
                    <TableHead>Total Cases</TableHead>
                    <TableHead>Active Cases</TableHead>
                    <TableHead>Closed Cases</TableHead>
                  </>
                )}
                {reportType === "case-stages" && (
                  <>
                    <TableHead>Stage</TableHead>
                    <TableHead>Number of Cases</TableHead>
                  </>
                )}
                {reportType === "referral-sources" && (
                  <>
                    <TableHead>Source</TableHead>
                    <TableHead>Number of Cases</TableHead>
                    <TableHead>Total Value</TableHead>
                  </>
                )}
                {reportType === "expected-settlements" && (
                  <>
                    <TableHead>Month</TableHead>
                    <TableHead>Number of Cases</TableHead>
                    <TableHead>Expected Value</TableHead>
                  </>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((item: any, index: number) => (
                <TableRow key={index}>
                  {reportType === "employee-cases" && (
                    <>
                      <TableCell>{item.name}</TableCell>
                      <TableCell>{item.casesCount}</TableCell>
                      <TableCell>{item.activeCount}</TableCell>
                      <TableCell>{item.closedCount}</TableCell>
                    </>
                  )}
                  {reportType === "case-stages" && (
                    <>
                      <TableCell>{item.stage}</TableCell>
                      <TableCell>{item.count}</TableCell>
                    </>
                  )}
                  {reportType === "referral-sources" && (
                    <>
                      <TableCell>{item.source}</TableCell>
                      <TableCell>{item.count}</TableCell>
                      <TableCell>${item.value.toLocaleString()}</TableCell>
                    </>
                  )}
                  {reportType === "expected-settlements" && (
                    <>
                      <TableCell>{item.month}</TableCell>
                      <TableCell>{item.count}</TableCell>
                      <TableCell>${item.value.toLocaleString()}</TableCell>
                    </>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
