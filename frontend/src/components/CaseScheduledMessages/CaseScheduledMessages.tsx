"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { format, parseISO } from "date-fns";
import { Clock, X, Calendar, Timer, Check, Edit } from "lucide-react";
import twilioService from "@/services/twilioIntegrationService";
import { useClientContactDetailsQuery } from "@/services/case-management/clientDetailService";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { formatPhoneNumber } from "../ui/phone-link";

interface CaseScheduledMessagesProps {
  caseId: string;
}

export function CaseScheduledMessages({ caseId }: CaseScheduledMessagesProps) {
  const [message, setMessage] = useState("");
  const [selectedPhoneIndex, setSelectedPhoneIndex] = useState<number>(0);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [scheduledDateTime, setScheduledDateTime] = useState("");
  const [editingMessageId, setEditingMessageId] = useState<number | null>(null);
  const [editedMessage, setEditedMessage] = useState("");
  const [editedDateTime, setEditedDateTime] = useState("");
  const [messageToDelete, setMessageToDelete] = useState<number | null>(null);

  const { data: clientContactDetails } = useClientContactDetailsQuery(caseId);
  const scheduleMutation = twilioService.useScheduleMessage();
  const { data: scheduledMessages } = twilioService.useGetScheduledMessages(caseId);
  const updateMutation = twilioService.useUpdateScheduledMessage();
  const cancelMutation = twilioService.useCancelScheduledMessage();

  const getAvailablePhoneNumbers = useCallback(() => {
    if (!clientContactDetails) return [];

    return [
      {
        number: clientContactDetails.phone_number_1,
        preferred: clientContactDetails.is_preferred_1,
      },
      {
        number: clientContactDetails.phone_number_2,
        preferred: clientContactDetails.is_preferred_2,
      },
      {
        number: clientContactDetails.phone_number_3,
        preferred: clientContactDetails.is_preferred_3,
      },
    ].filter(({ number }) => number);
  }, [clientContactDetails]);

  useEffect(() => {
    if (clientContactDetails) {
      const numbers = getAvailablePhoneNumbers();
      if (numbers.length > 0) {
        setPhoneNumber(formatPhoneNumber(numbers[0].number));
      }
    }
  }, [clientContactDetails, getAvailablePhoneNumbers]);

  const phoneNumberSelector = (
    <Select
      value={String(selectedPhoneIndex)}
      onValueChange={(value) => {
        setSelectedPhoneIndex(Number(value));
        const numbers = getAvailablePhoneNumbers();
        if (numbers[Number(value)]) {
          setPhoneNumber(formatPhoneNumber(numbers[Number(value)].number));
        }
      }}
    >
      <SelectTrigger className="w-[200px]">
        <SelectValue placeholder="Select phone number" />
      </SelectTrigger>
      <SelectContent>
        {getAvailablePhoneNumbers().map(
          ({ number, preferred }, index) => (
            <SelectItem key={number} value={String(index)}>
              <span className="flex items-center gap-2">
                <span className="font-medium">
                  {formatPhoneNumber(number)}
                </span>
                <span className="text-sm text-muted-foreground">
                  {preferred && "★"}
                </span>
              </span>
            </SelectItem>
          )
        )}
      </SelectContent>
    </Select>
  );

  const handleScheduleMessage = async () => {
    if (!message.trim() || !phoneNumber || !scheduledDateTime) return;

    try {
      // Create a date object from the selected datetime to get local timezone
      const date = new Date(scheduledDateTime);
      // Format with local timezone offset
      const formattedDateTime = date.toISOString();
      
      await scheduleMutation.mutateAsync({
        to_number: phoneNumber,
        message_body: message.trim(),
        scheduled_time: formattedDateTime,
        case_id: caseId,
      });
      setMessage("");
      setScheduledDateTime("");
    } catch (err) {
      console.error("Failed to schedule message:", err);
    }
  };

  const handleEditMessage = async (messageId: number) => {
    const messageToEdit = scheduledMessages?.find(msg => msg.id === messageId);
    if (!messageToEdit) return;
    
    setEditingMessageId(messageId);
    setEditedMessage(messageToEdit.message_body);
    setEditedDateTime(messageToEdit.scheduled_time.slice(0, 16));
  };

  const handleSaveEdit = async (messageId: number) => {
    try {
      const date = new Date(editedDateTime);
      const formattedDateTime = date.toISOString();
      
      await updateMutation.mutateAsync({
        messageId: messageId,
        updates: {
          message_body: editedMessage,
          scheduled_time: formattedDateTime,
        },
      });
      setEditingMessageId(null);
    } catch (err) {
      console.error("Failed to update message:", err);
    }
  };

  const handleCancelMessage = async (messageId: number) => {
    try {
      await cancelMutation.mutateAsync(messageId);
      setMessageToDelete(null);
    } catch (err) {
      console.error("Failed to cancel message:", err);
    }
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Phone Number Selector - Fixed at top */}
      <div className="flex-none border-b bg-white px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">To:</span>
            {phoneNumberSelector}
          </div>
        </div>
      </div>

      <div className="flex-1 flex flex-col min-h-0">
        {/* Messages List Section - Scrollable */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-4">
              {scheduledMessages?.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full py-8 text-center">
                  <Clock className="h-12 w-12 text-muted-foreground/50 mb-4" />
                  <p className="text-sm text-muted-foreground">No scheduled messages yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {scheduledMessages?.map((msg) => (
                    <div 
                      key={msg.id} 
                      className="flex flex-col gap-2 p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              msg.status === "pending"
                                ? "default"
                                : msg.status === "sent"
                                  ? "success"
                                  : "destructive"
                            }
                            className="h-5 capitalize"
                          >
                            {msg.status}
                          </Badge>
                        </div>
                        {msg.status === "pending" && (
                          <div className="flex items-center gap-2">
                            {editingMessageId === msg.id ? (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleSaveEdit(msg.id)}
                                className="h-8 w-8 p-0 text-green-500 hover:text-green-600 hover:bg-green-50"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                            ) : (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditMessage(msg.id)}
                                className="h-8 w-8 p-0 text-green-500 hover:text-green-600 hover:bg-green-50"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setMessageToDelete(msg.id)}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        {editingMessageId === msg.id ? (
                          <div className="flex items-center gap-2 w-full">
                            <Input
                              type="datetime-local"
                              value={editedDateTime}
                              onChange={(e) => setEditedDateTime(e.target.value)}
                              min={new Date().toISOString().slice(0, 16)}
                              className="w-48"
                            />
                          </div>
                        ) : (
                          <>
                            <Calendar className="h-4 w-4" />
                            <span>{format(parseISO(msg.scheduled_time), "MMM d, yyyy")}</span>
                            <Timer className="h-4 w-4 ml-2" />
                            <span>{format(parseISO(msg.scheduled_time), "h:mm a")}</span>
                          </>
                        )}
                      </div>
                      
                      <div className="bg-secondary/10 rounded-lg p-3">
                        {editingMessageId === msg.id ? (
                          <Textarea
                            value={editedMessage}
                            onChange={(e) => setEditedMessage(e.target.value)}
                            className="min-h-[100px] w-full"
                          />
                        ) : (
                          <p className="text-sm whitespace-pre-wrap">{msg.message_body}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Message Input Section - Fixed at bottom */}
        <div className="flex-none border-t bg-white p-3">
          <div className="flex items-center gap-2 mb-2">
            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block">Schedule Date & Time</label>
              <Input
                type="datetime-local"
                value={scheduledDateTime}
                onChange={(e) => setScheduledDateTime(e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
                className="w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-end gap-2">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 min-h-[44px] max-h-[120px] resize-none px-3 py-2"
              rows={1}
              required
            />
            <Button
              onClick={handleScheduleMessage}
              disabled={!message.trim() || !scheduledDateTime || !phoneNumber}
            >
              <Clock className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <DeleteConfirmationDialog
        open={messageToDelete !== null}
        onOpenChange={(open) => !open && setMessageToDelete(null)}
        onConfirm={() => messageToDelete && handleCancelMessage(messageToDelete)}
        title="Cancel Scheduled Message"
        description="Are you sure you want to cancel this scheduled message? This action cannot be undone."
        confirmText="Cancel Message"
        cancelText="Keep Message"
      />
    </div>
  );
} 