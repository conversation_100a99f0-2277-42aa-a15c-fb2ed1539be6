import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from '@/components/ui/button';
import StatusCircle from '@/components/StatusCircle/StatusCircle';
import { ChevronDownIcon } from "@heroicons/react/24/outline";

const statusOptions = [
  { label: 'Send', color: '#206FEE', textColor: 'text-[#060216]-500' },
  { label: 'Approved', color: '#09BD24', textColor: 'text-green-500' },
  { label: 'File Missing', color: '#FF2323', textColor: 'text-yellow-500' },
  { label: 'Re-work', color: '#8906F6', textColor: 'text-purple-500' },
  { label: 'Pending', color: '#FA7817', textColor: 'text-orange-500' },
];

export default function StatusDropdown({ selectedStatus, setSelectedStatus }: { selectedStatus: string, setSelectedStatus: (status: string) => void }) {
  const getStatusColor = (status: string) => {
    const option = statusOptions.find(opt => opt.label === status);
    return option ? option.textColor : 'text-gray-500';
  };

  const getStatusInfo = (status: string) => {
    const option = statusOptions.find(opt => opt.label === status);
    return option ? option.color : '#206fed';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex justify-between items-center">
          <div className={`flex items-center gap-2 ${getStatusColor(selectedStatus)}`}>
            <StatusCircle size={20} color={getStatusInfo(selectedStatus)} />
            {selectedStatus}
          </div>
          <ChevronDownIcon className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[130px]">
        {statusOptions.map(option => (
          <DropdownMenuItem
            key={option.label}
            onClick={() => setSelectedStatus(option.label)}
            className={`flex items-center gap-2 ${option.textColor} focus:${option.textColor}`}
          >
            <StatusCircle size={20} color={option.color} />
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 