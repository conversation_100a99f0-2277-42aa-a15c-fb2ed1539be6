"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { CaseResponse } from "@/type/dashboard";
import {
  FileText,
  MessageSquare,
  CheckSquare,
  X,
  Maximize2,
  Minimize2,
  ExternalLink,
  MinusSquare
} from "lucide-react";
import { CaseTasks } from "@/components/CaseTasks/CaseTasks";
import { CheckList } from "@/components/CaseOverview/components/CheckList";
import { CaseNotes } from "@/components/CaseNotes/CaseNotes";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface CaseDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  caseData: CaseResponse;
}

export function CaseDetailsDialog({ caseData, isOpen, onClose }: CaseDetailsDialogProps) {
  const [viewMode, setViewMode] = useState<'normal' | 'expanded' | 'minimized'>('expanded');
  const router = useRouter();

  if (!isOpen) return null;
  
  const handleNavigateToCase = () => {
    router.push(`/dashboard/case-view/${caseData.id}`);
  };

  const getHeight = () => {
    switch (viewMode) {
      case 'expanded':
        return 'h-[65%]';
      case 'minimized':
        return 'h-[8%]';
      default:
        return 'h-[45%]';
    }
  };

  const handleViewModeChange = () => {
    if (viewMode === 'minimized') {
      setViewMode('expanded');
    } else {
      setViewMode('minimized');
    }
  };

  const handleMinimize = () => {
    if (viewMode === 'expanded') {
      setViewMode('normal');
    } else {
      setViewMode('expanded');
    }
  };
  
  return (
    <div className={`fixed bottom-0 right-0 w-1/2 ${getHeight()} border-l border-border shadow-[0px_5px_15px_rgba(0,0,0,0.35)] z-50`}>
      <div className="h-full flex flex-col bg-background">
        <div className="flex-none flex justify-between items-center p-1 border-b">
          <div className="flex items-center gap-2">
            <h2 className="text-md font-semibold pl-2">Case Details</h2>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4"
              onClick={handleNavigateToCase}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="ghost" size="icon" onClick={handleMinimize}>
              {viewMode === 'expanded' ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
            <Button variant="ghost" size="icon" onClick={handleViewModeChange}>
            <MinusSquare className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        {viewMode !== 'minimized' && (
          <Tabs defaultValue="checklist" className="flex-1 flex flex-col min-h-0">
            <div className="flex-none relative border-b">
              <div className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white to-transparent pointer-events-none z-10" />
              <TabsList className="w-full overflow-x-auto scrollbar-none flex justify-around">
                <TabsTrigger value="tasks" className="flex items-center gap-1 text-sm">
                  <CheckSquare className="h-4 w-4" />
                  Tasks
                </TabsTrigger>
                <TabsTrigger value="checklist" className="flex items-center gap-1 text-sm">
                  <FileText className="h-4 w-4" />
                  Checklist
                </TabsTrigger>
                <TabsTrigger value="notes" className="flex items-center gap-1 text-sm">
                  <MessageSquare className="h-4 w-4" />
                  Notes
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 min-h-0">
              <TabsContent value="tasks" className="h-full m-0">
                <CaseTasks caseId={caseData.id} taskFor="Case" />
              </TabsContent>

              <TabsContent value="checklist" className="h-full m-0">
                <CheckList caseId={caseData.id} />
              </TabsContent>

              <TabsContent value="notes" className="h-full m-0">
                <CaseNotes caseId={caseData.id} noteFor="Case" />
              </TabsContent>
            </div>
          </Tabs>
        )}
      </div>
    </div>
  );
} 