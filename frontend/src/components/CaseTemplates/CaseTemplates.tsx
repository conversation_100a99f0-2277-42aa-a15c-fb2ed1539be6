import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRenderTemplateMutation, useTemplatesListQuery } from '@/services/templateManagementService';
import { Download } from 'lucide-react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Loader2 } from 'lucide-react';

interface CaseTemplatesProps {
    caseId: string;
}

export function CaseTemplates({ caseId }: CaseTemplatesProps) {
    const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
    const [isProcessing, setIsProcessing] = useState(false);

    // Fetch templates that are marked for use in cases
    const { data: templates, isLoading } = useTemplatesListQuery({
        context_type: 'client'
    });

    // Filter templates that are marked for use in cases
    const caseTemplates = templates?.filter(template => template.use_in_case) || [];

    const renderTemplateMutation = useRenderTemplateMutation();

    const handleProcessDocument = async () => {
        if (!selectedTemplateId) {
            // alert('Please select a template first');
            return;
        }

        setIsProcessing(true);
        try {
            const {data: processedFile, file_name} = await renderTemplateMutation.mutateAsync({
                templateId: parseInt(selectedTemplateId),
                case_id: caseId,
                name: 'case_template'
            });
            
            // Create download link for the processed file
            const url = window.URL.createObjectURL(processedFile);
            const link = document.createElement('a');
            link.href = url;
            link.download = decodeURIComponent(file_name);
            document.body.appendChild(link);
            link.click();
            
            // Cleanup
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('Error processing template:', error);
        } finally {
            setIsProcessing(false);
        }
    };

    return (
        <div className="h-full flex flex-col p-4 overflow-auto">
            <Card className="p-4">
                <h2 className="text-lg font-semibold mb-4">Case Templates</h2>
                <div className="space-y-4">
                    <p className="text-sm text-gray-600 mb-4">
                        Select a template to generate a document with case information.
                    </p>
                    
                    <div className="space-y-4">

                        {isLoading ? (
                            <div className="flex justify-center py-4">
                                <Loader2 className="h-6 w-6 animate-spin" />
                            </div>
                        ) : caseTemplates.length === 0 ? (
                            <div className="text-center py-4 text-gray-500">
                                No templates available for cases
                            </div>
                        ) : (
                            <Select
                                value={selectedTemplateId}
                                onValueChange={setSelectedTemplateId}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a template" />
                                </SelectTrigger>
                                <SelectContent>
                                    {caseTemplates.filter(template => template.context_type === 'client').map((template) => (
                                        <SelectItem 
                                            key={template.id} 
                                            value={template.id.toString()}
                                        >
                                            {template.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        )}

                        <Button
                            onClick={handleProcessDocument}
                            disabled={!selectedTemplateId || isProcessing}
                            className="w-full"
                        >
                            {isProcessing ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <Download className="w-4 h-4 mr-2" />
                                    Generate Document
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </Card>
        </div>
    );
} 