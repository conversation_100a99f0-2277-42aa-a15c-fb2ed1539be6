.ckeditor-wrapper {
  /* Base styles */
  width: 100%;
  border-radius: 0.375rem;
  background-color: white;
  overflow: hidden;
  position: relative; /* Add positioning context */
  transition: height 0.2s ease; /* Smooth height transitions */
}

.ckeditor-wrapper :global(.ck-editor__editable) {
  min-height: var(--ck-min-height, 200px) !important;
  max-height: 500px !important; /* Match JS max-height */
  background-color: white !important;
  color: #1a1a1a;
  overflow-y: auto !important; /* Enable scrolling */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Scrollbar styling for Webkit browsers */
.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar {
  width: 8px;
}

.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 4px;
  border: 2px solid #f1f5f9;
}

.ckeditor-wrapper :global(.ck-editor__editable)::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

.ckeditor-wrapper :global(.ck-editor__editable.ck-focused) {
  border-color: #2563eb !important;
}

/* Prevent layout shifts during resize */
.ckeditor-wrapper :global(.ck.ck-editor__main) {
  position: relative;
  z-index: 1;
}

/* Enhanced toolbar styling */
.ckeditor-wrapper :global(.ck.ck-toolbar) {
  border: none !important;
  border-bottom: 1px solid #e2e8f0 !important;
  background: #f8fafc !important;
  padding: 0.75rem !important;
  width: 100% !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.5rem !important;
  justify-content: flex-start !important;
  min-height: 50px !important;
  position: relative;
  z-index: 2;
}

.ckeditor-wrapper :global(.ck.ck-toolbar__items) {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.5rem !important;
  justify-content: flex-start !important;
  width: 100% !important;
}

/* Toolbar button styling */
.ckeditor-wrapper :global(.ck.ck-button) {
  padding: 0.5rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  transition: all 0.2s ease-in-out !important;
  margin: 0 !important;
}

.ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
  max-width: 100% !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Toolbar separator styling */
.ckeditor-wrapper :global(.ck.ck-toolbar__separator) {
  margin: 0 !important;
  width: 1px !important;
  background: #e2e8f0 !important;
}

/* Content area styling */
.ckeditor-wrapper :global(.ck-content) {
  font-size: 1rem;
  line-height: 1.5;
  padding: 1rem !important;
}

/* Responsive design - Mobile first approach */
@media screen and (max-width: 640px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.5rem !important;
    gap: 0.25rem !important;
    min-height: auto !important;
  }

  .ckeditor-wrapper :global(.ck.ck-toolbar__items) {
    gap: 0.25rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.375rem !important;
    font-size: 0.75rem !important;
  }

  /* Hide labels on very small screens */
  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    display: none !important;
  }

  /* Show only icons */
  .ckeditor-wrapper :global(.ck.ck-button .ck-icon) {
    margin: 0 !important;
  }

  .ckeditor-wrapper :global(.ck-content) {
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* Tablet screens */
@media screen and (min-width: 641px) and (max-width: 1024px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.625rem !important;
    gap: 0.375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-toolbar__items) {
    gap: 0.375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.4375rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    max-width: 100px !important;
  }
}

/* Desktop screens */
@media screen and (min-width: 1025px) {
  .ckeditor-wrapper :global(.ck.ck-toolbar) {
    padding: 0.75rem !important;
    gap: 0.5rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.5rem 0.75rem !important;
  }

  .ckeditor-wrapper :global(.ck.ck-button .ck-button__label) {
    max-width: 150px !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .ckeditor-wrapper :global(.ck.ck-button) {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* Active and hover states */
.ckeditor-wrapper :global(.ck.ck-button:hover) {
  background: #e2e8f0 !important;
  color: #1a1a1a !important;
}

.ckeditor-wrapper :global(.ck.ck-button.ck-on) {
  background: #e2e8f0 !important;
  color: #1a1a1a !important;
}

/* Dropdown styling */
.ckeditor-wrapper :global(.ck.ck-dropdown) {
  margin: 0 !important;
}

.ckeditor-wrapper :global(.ck.ck-dropdown .ck-button.ck-dropdown__button) {
  padding-right: 1.5rem !important;
}

.ckeditor-wrapper :global(.ck.ck-dropdown .ck-dropdown__panel) {
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Style for placeholder text */
.ckeditor-wrapper :global(.ck-placeholder) {
  color: #9ca3af !important;
}

/* Ensure toolbar buttons are properly styled */
.ckeditor-wrapper :global(.ck-button) {
  color: #4b5563 !important;
}

.ckeditor-wrapper :global(.ck-button:hover) {
  background: #f3f4f6 !important;
}

.ckeditor-wrapper :global(.ck-button.ck-on) {
  background: #e5e7eb !important;
  color: #1a1a1a !important;
}

/* Responsive adjustments */
.ckeditor-wrapper :global(.ck-content) {
  font-size: 16px;
  line-height: 1.5;
}

/* Mobile-first responsive design */
@media screen and (max-width: 640px) {
  .ckeditor-wrapper :global(.ck-toolbar__items) {
    gap: 0.125rem;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.25rem !important;
  }

  .ckeditor-wrapper :global(.ck-content) {
    font-size: 14px;
  }

  .ckeditor-wrapper :global(.ck-editor__editable_inline) {
    padding: 0.5rem;
  }
}

/* Tablet and larger screens */
@media screen and (min-width: 641px) and (max-width: 1024px) {
  .ckeditor-wrapper :global(.ck-toolbar__items) {
    gap: 0.175rem;
  }

  .ckeditor-wrapper :global(.ck.ck-button) {
    padding: 0.375rem !important;
  }
}

/* Handle images and tables responsively */
.ckeditor-wrapper :global(.ck-content img) {
  max-width: 100%;
  height: auto;
}

.ckeditor-wrapper :global(.ck-content table) {
  width: 100% !important;
  overflow-x: auto;
  display: block;
}

/* Ensure toolbar buttons are properly spaced and sized */
.ckeditor-wrapper :global(.ck.ck-toolbar > *) {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Improve touch targets on mobile */
@media (hover: none) and (pointer: coarse) {
  .ckeditor-wrapper :global(.ck.ck-button) {
    min-height: 36px;
    min-width: 36px;
  }
} 