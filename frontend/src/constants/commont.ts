export enum Pronouns {
    HE_HIM = 'he_him',
    SHE_HER = 'she_her',
    THEY_THEM = 'they_them',
    OTHER = 'other'
}

export enum PlaintiffRole {
    PRIMARY = 'primary',
    DEPENDENT = 'dependent',
    GUARDIAN = 'guardian',
    OTHER = 'other'
}

export enum FamilyStatus {
    SINGLE = 'single',
    MARRIED = 'married',
    DIVORCED = 'divorced',
    WIDOWED = 'widowed'
}


export enum DependentStatus {
    NONE = 'none',
    CHILDREN = 'children',
    ELDERLY = 'elderly',
    BOTH = 'both'
}

export enum CaseType {
    AUTO_ACCIDENT = 'auto_accident',
    SLIP_FALL = 'slip_fall',
    MEDICAL_MALPRACTICE = 'medical_malpractice'
}

export enum PolicyType {
    AUTO = 'auto',
    GENERAL_LIABILITY = 'general_liability',
    PROFESSIONAL = 'professional'
}

export enum DefendantEntityType {
    INDIVIDUAL = 'individual',
    BUSINESS = 'business',
    GOVERNMENT = 'government'
}

export enum RecipientType {
    ATTORNEY = 'attorney',
    ADJUSTER = 'adjuster',
    EXPERT = 'expert'
  }

export const PronounsLabels: Record<Pronouns, string> = {
    [Pronouns.HE_HIM]: 'He/Him',
    [Pronouns.SHE_HER]: 'She/Her',
    [Pronouns.THEY_THEM]: 'They/Them',
    [Pronouns.OTHER]: 'Other'
};


export const PlaintiffRoleLabels: Record<PlaintiffRole, string> = {
    [PlaintiffRole.PRIMARY]: 'Primary Plaintiff',
    [PlaintiffRole.DEPENDENT]: 'Dependent',
    [PlaintiffRole.GUARDIAN]: 'Legal Guardian',
    [PlaintiffRole.OTHER]: 'Other'
};

export const FamilyStatusLabels: Record<FamilyStatus, string> = {
    [FamilyStatus.SINGLE]: 'Single',
    [FamilyStatus.MARRIED]: 'Married',
    [FamilyStatus.DIVORCED]: 'Divorced',
    [FamilyStatus.WIDOWED]: 'Widowed'
};

export const DependentStatusLabels: Record<DependentStatus, string> = {
    [DependentStatus.NONE]: 'No Dependents',
    [DependentStatus.CHILDREN]: 'Has Children',
    [DependentStatus.ELDERLY]: 'Elderly Dependents',
    [DependentStatus.BOTH]: 'Both Children and Elderly'
};


export const caseTypeLabels: Record<CaseType, string> = {
    [CaseType.AUTO_ACCIDENT]: 'Auto Accident',
    [CaseType.SLIP_FALL]: 'Slip & Fall',
    [CaseType.MEDICAL_MALPRACTICE]: 'Medical Malpractice'
};


export enum USStates {
    AL = 'AL',
    AK = 'AK',
    AZ = 'AZ',
    AR = 'AR',
    CA = 'CA',
    CO = 'CO',
    CT = 'CT',
    DE = 'DE',
    FL = 'FL',
    GA = 'GA',
    HI = 'HI',
    ID = 'ID',
    IL = 'IL',
    IN = 'IN',
    IA = 'IA',
    KS = 'KS',
    KY = 'KY',
    LA = 'LA',
    ME = 'ME',
    MD = 'MD',
    MA = 'MA',
    MI = 'MI',
    MN = 'MN',
    MS = 'MS',
    MO = 'MO',
    MT = 'MT',
    NE = 'NE',
    NV = 'NV',
    NH = 'NH',
    NJ = 'NJ',
    NM = 'NM',
    NY = 'NY',
    NC = 'NC',
    ND = 'ND',
    OH = 'OH',
    OK = 'OK',
    OR = 'OR',
    PA = 'PA',
    RI = 'RI',
    SC = 'SC',
    SD = 'SD',
    TN = 'TN',
    TX = 'TX',
    UT = 'UT',
    VT = 'VT',
    VA = 'VA',
    WA = 'WA',
    WV = 'WV',
    WI = 'WI',
    WY = 'WY'
}

export const USStatesLabels: Record<USStates, string> = {
    [USStates.AL]: 'Alabama',
    [USStates.AK]: 'Alaska',
    [USStates.AZ]: 'Arizona',
    [USStates.AR]: 'Arkansas',
    [USStates.CA]: 'California',
    [USStates.CO]: 'Colorado',
    [USStates.CT]: 'Connecticut',
    [USStates.DE]: 'Delaware',
    [USStates.FL]: 'Florida',
    [USStates.GA]: 'Georgia',
    [USStates.HI]: 'Hawaii',
    [USStates.ID]: 'Idaho',
    [USStates.IL]: 'Illinois',
    [USStates.IN]: 'Indiana',
    [USStates.IA]: 'Iowa',
    [USStates.KS]: 'Kansas',
    [USStates.KY]: 'Kentucky',
    [USStates.LA]: 'Louisiana',
    [USStates.ME]: 'Maine',
    [USStates.MD]: 'Maryland',
    [USStates.MA]: 'Massachusetts',
    [USStates.MI]: 'Michigan',
    [USStates.MN]: 'Minnesota',
    [USStates.MS]: 'Mississippi',
    [USStates.MO]: 'Missouri',
    [USStates.MT]: 'Montana',
    [USStates.NE]: 'Nebraska',
    [USStates.NV]: 'Nevada',
    [USStates.NH]: 'New Hampshire',
    [USStates.NJ]: 'New Jersey',
    [USStates.NM]: 'New Mexico',
    [USStates.NY]: 'New York',
    [USStates.NC]: 'North Carolina',
    [USStates.ND]: 'North Dakota',
    [USStates.OH]: 'Ohio',
    [USStates.OK]: 'Oklahoma',
    [USStates.OR]: 'Oregon',
    [USStates.PA]: 'Pennsylvania',
    [USStates.RI]: 'Rhode Island',
    [USStates.SC]: 'South Carolina',
    [USStates.SD]: 'South Dakota',
    [USStates.TN]: 'Tennessee',
    [USStates.TX]: 'Texas',
    [USStates.UT]: 'Utah',
    [USStates.VT]: 'Vermont',
    [USStates.VA]: 'Virginia',
    [USStates.WA]: 'Washington',
    [USStates.WV]: 'West Virginia',
    [USStates.WI]: 'Wisconsin',
    [USStates.WY]: 'Wyoming'
};

export const SPECIALTIES_OPTIONS = [
    { label: "Acupuncture", value: "acupuncture" },
    { label: "All", value: "all" },
    { label: "Allergy", value: "allergy" },
    { label: "Ambulance", value: "ambulance" },
    { label: "Anesthesiology", value: "anesthesiology" },
    { label: "Audiology", value: "audiology" },
    { label: "Autopsy", value: "autopsy" },
    { label: "Cardiology", value: "cardiology" },
    { label: "Chiropractic", value: "chiropractic" },
    { label: "Cognitive Therapy", value: "cognitive_therapy" },
    { label: "Counseling", value: "counseling" },
    { label: "Dentistry", value: "dentistry" },
    { label: "Dermatology", value: "dermatology" },
    { label: "Dialysis", value: "dialysis" },
    { label: "Emergency Room", value: "emergency_room" },
    { label: "Endocrinology", value: "endocrinology" },
    { label: "ENT", value: "ent" },
    { label: "ER Physicians", value: "er_physicians" },
    { label: "Eye Care", value: "eye_care" },
    { label: "Family Practice", value: "family_practice" },
    { label: "Fire Department", value: "fire_department" },
    { label: "Gastroenterology", value: "gastroenterology" },
    { label: "General Practice", value: "general_practice" },
    { label: "Geriatrics", value: "geriatrics" },
    { label: "Gynecology", value: "gynecology" },
    { label: "Hand Specialty", value: "hand_specialty" },
    { label: "Hematology", value: "hematology" },
    { label: "Hepatology", value: "hepatology" },
    { label: "Home Care", value: "home_care" },
    { label: "Hospital", value: "hospital" },
    { label: "Immunology", value: "immunology" },
    { label: "Infectious Diseases", value: "infectious_diseases" },
    { label: "Internal Medicine", value: "internal_medicine" },
    { label: "Laboratory", value: "laboratory" },
    { label: "Massage Therapy", value: "massage_therapy" },
    { label: "Medical Billing", value: "medical_billing" },
    { label: "Medical Devices", value: "medical_devices" },
    { label: "Medical Transport", value: "medical_transport" },
    { label: "MRI Imaging", value: "mri_imaging" },
    { label: "Nephrology", value: "nephrology" },
    { label: "Neurology", value: "neurology" },
    { label: "Neuropathy", value: "neuropathy" },
    { label: "Neuropsychiatry", value: "neuropsychiatry" },
    { label: "Neuropsychology", value: "neuropsychology" },
    { label: "Neurosurgery", value: "neurosurgery" },
    { label: "NICU", value: "nicu" },
    { label: "Nursing Home", value: "nursing_home" },
    { label: "Obstetrics", value: "obstetrics" },
    { label: "Occupational Therapy", value: "occupational_therapy" },
    { label: "Oncology", value: "oncology" },
    { label: "Ophthalmology", value: "ophthalmology" },
    { label: "Optometry", value: "optometry" },
    { label: "Orthopedic", value: "orthopedic" },
    { label: "Osteopathy", value: "osteopathy" },
    { label: "Pain Management", value: "pain_management" },
    { label: "Pathology", value: "pathology" },
    { label: "Pediatric", value: "pediatric" },
    { label: "Pharmacy", value: "pharmacy" },
    { label: "Physiatry", value: "physiatry" },
    { label: "Physical Therapy", value: "physical_therapy" },
    { label: "Plastic Surgery", value: "plastic_surgery" },
    { label: "Podiatry", value: "podiatry" },
    { label: "Primary Care Provider", value: "primary_care_provider" },
    { label: "Prosthetics", value: "prosthetics" },
    { label: "Psychiatry", value: "psychiatry" },
    { label: "Psychology", value: "psychology" },
    { label: "Pulmonary", value: "pulmonary" },
    { label: "Radiology", value: "radiology" },
    { label: "Rheumatology", value: "rheumatology" },
    { label: "Skilled Nurse", value: "skilled_nurse" },
    { label: "Speech Therapy", value: "speech_therapy" },
    { label: "Sports Medicine", value: "sports_medicine" },
    { label: "Surgery", value: "surgery" },
    { label: "Therapy", value: "therapy" },
    { label: "Trauma Physician", value: "trauma_physician" },
    { label: "Urgent Care", value: "urgent_care" },
    { label: "Urology", value: "urology" },
    { label: "Vascular Surgery", value: "vascular_surgery" },
    { label: "Vocal Therapy", value: "vocal_therapy" },
    { label: "Vocational Therapy", value: "vocational_therapy" },
    { label: "Wound Care", value: "wound_care" }
];

export type SpecialtyType = typeof SPECIALTIES_OPTIONS[number]['value'];

export const WITNESS_SPECIALTIES_OPTIONS = [
    { value: "all", label: "Display All" },
    { value: "accident_reconstruction", label: "Accident Reconstruction" },
    { value: "air_quality", label: "Air Quality" },
    { value: "animal_accidents", label: "Animal Accidents" },
    { value: "aquatics", label: "Aquatics" },
    { value: "atv_accidents", label: "ATV Accidents" },
    { value: "aviation_accidents", label: "Aviation Accidents" },
    { value: "bicycle_accidents", label: "Bicycle Accidents" },
    { value: "biomechanics", label: "Biomechanics" },
    { value: "boating_accidents", label: "Boating Accidents" },
    { value: "car_accidents", label: "Car Accidents" },
    { value: "carbon_monoxide", label: "Carbon Monoxide" },
    { value: "cell_phone", label: "Cell Phone" },
    { value: "chemical_accidents", label: "Chemical Accidents" },
    { value: "childcare_expert", label: "Childcare Expert" },
    { value: "chiropractic", label: "Chiropractic" },
    { value: "commercial_driver_licenses", label: "Commercial Driver Licenses" },
    { value: "dentistry", label: "Dentistry" },
    { value: "department_of_transportation", label: "Department of Transportation" },
    { value: "disabilities", label: "Disabilities" },
    { value: "dme", label: "DME" },
    { value: "dog_trainer", label: "Dog Trainer" },
    { value: "economist", label: "Economist" },
    { value: "electrical_accidents", label: "Electrical Accidents" },
    { value: "emergency_response", label: "Emergency Response" },
    { value: "engineering", label: "Engineering" },
    { value: "financial", label: "Financial" },
    { value: "firearms", label: "Firearms" },
    { value: "fire_explosions", label: "Fire & Explosions" },
    { value: "health_care_industry", label: "Health Care Industry" },
    { value: "highway_defects", label: "Highway Defects" },
    { value: "hospital_industry", label: "Hospital Industry" },
    { value: "industrial_accidents", label: "Industrial Accidents" },
    { value: "insurance_bad_faith", label: "Insurance Bad Faith" },
    { value: "insurance_coverage", label: "Insurance Coverage" },
    { value: "insurance_industry_practices", label: "Insurance Industry Practices" },
    { value: "insurance_risk_management", label: "Insurance Risk Management" },
    { value: "law_enforcement_excessive_force", label: "Law Enforcement Excessive Force" },
    { value: "law_enforcement_industry_practices", label: "Law Enforcement Industry Practices" },
    { value: "life_care_planning", label: "Life Care Planning" },
    { value: "lighting_visibility", label: "Lighting & Visibility" },
    { value: "machinery", label: "Machinery" },
    { value: "manufacturing", label: "Manufacturing" },
    { value: "medical", label: "Medical" },
    { value: "medical_billing", label: "Medical Billing" },
    { value: "medical_examiner", label: "Medical Examiner" },
    { value: "mental_health", label: "Mental Health" },
    { value: "motorcycle_accidents", label: "Motorcycle Accidents" },
    { value: "mva_accidents", label: "MVA Accidents" },
    { value: "neurology", label: "Neurology" },
    { value: "neuropsychiatrist", label: "Neuropsychiatrist" },
    { value: "neuropsychologist", label: "Neuropsychologist" },
    { value: "neurosurgery", label: "Neurosurgery" },
    { value: "nursing", label: "Nursing" },
    { value: "oil_gas", label: "Oil & Gas" },
    { value: "orthopedic", label: "Orthopedic" },
    { value: "other", label: "Other" },
    { value: "pain_management", label: "Pain Management" },
    { value: "pathology", label: "Pathology" },
    { value: "pedestrian_accidents", label: "Pedestrian Accidents" },
    { value: "pediatrics", label: "Pediatrics" },
    { value: "pharmacology", label: "Pharmacology" },
    { value: "physiatry", label: "Physiatry" },
    { value: "police_pursuit", label: "Police Pursuit" },
    { value: "premises_liability", label: "Premises Liability" },
    { value: "psychiatry", label: "Psychiatry" },
    { value: "psychology", label: "Psychology" },
    { value: "security", label: "Security" },
    { value: "sleep_fatigue", label: "Sleep & Fatigue" },
    { value: "slip_fall_accidents", label: "Slip & Fall Accidents" },
    { value: "social_worker", label: "Social Worker" },
    { value: "toxicology", label: "Toxicology" },
    { value: "traffic_offenses", label: "Traffic Offenses" },
    { value: "train_accidents", label: "Train Accidents" },
    { value: "tree_landscaping", label: "Tree & Landscaping" },
    { value: "vehicle_defect", label: "Vehicle Defect" },
    { value: "vocational_rehabilitation", label: "Vocational Rehabilitation" },
    { value: "weather", label: "Weather" },
    { value: "workers_compensation", label: "Workers Compensation" }
] as const;

export type WitnessSpecialtyType = typeof WITNESS_SPECIALTIES_OPTIONS[number]['value'];

export const STATUS_STYLES = {
  NEW: { color: 'bg-blue-100', textColor: 'text-[#060216]-700' },
  CONTACTED: { color: 'bg-yellow-100', textColor: 'text-yellow-700' },
  QUALIFIED: { color: 'bg-green-100', textColor: 'text-green-700' },
  CONVERTED: { color: 'bg-emerald-100', textColor: 'text-emerald-700' },
  LOST: { color: 'bg-red-100', textColor: 'text-red-700' },
  REJECTED: { color: 'bg-red-100', textColor: 'text-red-700' },
  CASE_CREATED: { color: 'bg-purple-100', textColor: 'text-purple-700' },
} as const;
