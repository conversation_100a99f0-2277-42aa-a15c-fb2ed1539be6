// Define public and protected routes
export const PUBLIC_PATHS: readonly string[] = ['/login', '/register', '/forgot-password', '/auth/reset-password', '/join-organization'] as const;
export const PROTECTED_PATHS: readonly string[] = ['/dashboard', '/leads', '/files', '/ai-chat', '/profile'] as const;

// Helper function to check if a path is public
export const isPublicPath = (path: string) => 
  PUBLIC_PATHS.some(publicPath => path.startsWith(publicPath));

// Helper function to check if a path is protected
export const isProtectedPath = (path: string) => 
  PROTECTED_PATHS.some(protectedPath => path.startsWith(protectedPath)); 