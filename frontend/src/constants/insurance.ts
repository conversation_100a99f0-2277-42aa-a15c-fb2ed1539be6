export enum UmUimLimit {
  NA = 'n/a',
  TEN_TWENTY = '10k / 20k',
  FIFTEEN_THIRTY = '15k / 30k',
  FIFTEEN_THIRTY_EO = '15k / 30k EO',
  TWENTY = '20k',
  TWENTY_FORTY = '20k / 40k',
  TWENTY_FIVE_FIFTY = '25k / 50k',
  TWENTY_FIVE_FIFTY_EO = '25k / 50k EO',
  TWENTY_FIVE_SIXTY_FIVE = '25k / 65k',
  TWENTY_FIVE_HUNDRED = '25k / 100k',
  THIRTY_SIXTY = '30k / 60k',
  THIRTY_EIGHTY_FIVE = '30k / 85k',
  FORTY_EIGHTY = '40k / 80k',
  FIFTY_HUNDRED = '50k / 100k',
  FIFTY_HUNDRED_EO = '50k / 100k EO',
  FIFTY_CSL = '50k CSL',
  SIXTY = '60k',
  SEVENTY_FIVE_ONE_FIFTY = '75k / 150k',
  EIGHTY_ONE_SIXTY = '80k / 160k',
  HUNDRED_CSL = '100k CSL',
  HUNDRED_TWO_HUNDRED = '100k / 200k',
  HUNDRED_THREE_HUNDRED = '100k / 300k',
  HUNDRED_THREE_HUNDRED_EO = '100k / 300k EO',
  ONE_TWENTY_FIVE_TWO_FIFTY = '125k / 250k',
  TWO_HUNDRED = '200k',
  TWO_HUNDRED_CSL = '200k CSL',
  TWO_HUNDRED_THREE_HUNDRED = '200k / 300k',
  TWO_FIFTY = '250k',
  TWO_FIFTY_FIVE_HUNDRED = '250k / 500k',
  THREE_HUNDRED = '300k',
  THREE_HUNDRED_FIVE_HUNDRED = '300k / 500k',
  THREE_HUNDRED_CSL = '300k CSL',
  THREE_FIFTY = '350k',
  THREE_FIFTY_CSL = '350k CSL',
  FOUR_HUNDRED = '400k',
  FOUR_HUNDRED_CSL = '400k CSL',
  FIVE_HUNDRED = '500k',
  FIVE_HUNDRED_CSL = '500k CSL',
  FIVE_HUNDRED_ONE_MIL = '500k / 1mil',
  SIX_HUNDRED = '600k',
  SIX_HUNDRED_CSL = '600k CSL',
  SEVEN_HUNDRED = '700k',
  SEVEN_HUNDRED_CSL = '700k CSL',
  SEVEN_FIFTY = '750k',
  SEVEN_FIFTY_CSL = '750k CSL',
  EIGHT_HUNDRED = '800k',
  EIGHT_HUNDRED_CSL = '800k CSL',
  NINE_HUNDRED = '900k',
  NINE_HUNDRED_CSL = '900k CSL',
  NINE_FIFTY = '950k',
  NINE_FIFTY_CSL = '950k CSL',
  ONE_MIL = '1mil',
  ONE_MIL_CSL = '1mil CSL',
  ONE_TWENTY_FIVE_MIL = '1.25mil',
  ONE_FIVE_MIL = '1.5mil',
  ONE_MIL_ONE_FIVE_MIL = '1mil / 1.5mil',
  ONE_MIL_TWO_MIL = '1mil / 2mil',
  ONE_MIL_THREE_MIL = '1mil / 3mil',
  TWO_MIL = '2mil',
  THREE_MIL = '3mil',
  FOUR_MIL = '4mil',
  FIVE_MIL = '5mil',
  TEN_MIL = '10mil',
  TWENTY_FIVE_MIL = '25mil'
}

export const UM_UIM_OPTIONS = Object.values(UmUimLimit); 

export enum MedpayLimit {
  NA = 'n/a',
  FIVE_HUNDRED = '500',
  ONE_THOUSAND = '1000',
  ONE_THOUSAND_EXCESS = '1000 in excess',
  FIFTEEN_HUNDRED = '1500',
  FIFTEEN_HUNDRED_EXCESS = '1500 in excess',
  TWO_THOUSAND = '2000',
  TWO_THOUSAND_EXCESS = '2000 in excess',
  TWENTY_FIVE_HUNDRED = '2500',
  TWENTY_FIVE_HUNDRED_EXCESS = '2500 in excess',
  THREE_THOUSAND = '3000',
  THREE_THOUSAND_EXCESS = '3000 in excess',
  FOUR_THOUSAND = '4000',
  FOUR_THOUSAND_EXCESS = '4000 in excess',
  FIVE_THOUSAND = '5000',
  FIVE_THOUSAND_EXCESS = '5000 in excess',
  SEVEN_FIVE_HUNDRED = '7500',
  EIGHT_THOUSAND = '8000',
  TEN_THOUSAND = '10000',
  TEN_THOUSAND_EXCESS = '10000 in excess',
  FIFTEEN_THOUSAND = '15000',
  FIFTEEN_THOUSAND_EXCESS = '15000 in excess',
  TWENTY_FIVE_THOUSAND = '25000',
  TWENTY_FIVE_THOUSAND_EXCESS = '25000 in excess',
  FORTY_THOUSAND = '40000',
  FIFTY_THOUSAND = '50000',
  FIFTY_THOUSAND_EXCESS = '50000 in excess',
  SEVENTY_FIVE_THOUSAND = '75000',
  HUNDRED_THOUSAND = '100000',
  HUNDRED_THOUSAND_EXCESS = '100000 in excess'
}

export const MEDPAY_OPTIONS = Object.values(MedpayLimit); 

export enum PipLimit {
  NA = 'n/a',
  FIVE_HUNDRED = '500',
  ONE_THOUSAND = '1000',
  FIFTEEN_HUNDRED = '1500',
  TWO_THOUSAND = '2000',
  TWO_THOUSAND_EIGHT_THOUSAND = '2000 / 8000',
  TWENTY_FIVE_HUNDRED = '2500',
  THREE_THOUSAND = '3000',
  FOUR_THOUSAND = '4000',
  FORTY_FIVE_HUNDRED = '4500',
  FIVE_THOUSAND = '5000',
  EIGHT_THOUSAND = '8000',
  TEN_THOUSAND = '10000',
  TWELVE_FIVE_HUNDRED = '12500',
  FIFTEEN_THOUSAND = '15000',
  TWENTY_THOUSAND = '20000',
  TWENTY_FIVE_THOUSAND = '25000',
  THIRTY_THOUSAND = '30000',
  THIRTY_FIVE_THOUSAND = '35000',
  FIFTY_THOUSAND = '50000',
  SIXTY_THOUSAND = '60000',
  SEVENTY_FIVE_THOUSAND = '75000',
  HUNDRED_THOUSAND = '100000',
  HUNDRED_FIFTY_THOUSAND = '150000',
  TWO_FIFTY_THOUSAND = '250000',
  ONE_MILLION = '1000000'
}

export const PIP_OPTIONS = Object.values(PipLimit); 

export enum CoverageStatus {
  ACCEPTED = 'accepted',
  DENIED = 'denied',
  PENDING = 'pending'
}

export const COVERAGE_STATUS_OPTIONS = Object.values(CoverageStatus); 

export enum LiabilityStatus {
  ACCEPTED = 'accepted',
  DENIED = 'denied',
  PENDING = 'pending'
}

export const LIABILITY_STATUS_OPTIONS = Object.values(LiabilityStatus); 

export enum PolicyType {
  FULL_TORT = 'Full Tort',
  LIMITED_TORT = 'Limited Tort',
  IN_EXCESS = 'In Excess',
  REDUCED_BY = 'Reduced By',
  UNKNOWN = 'Unknown',
  NA = 'n/a'
}

export const POLICY_TYPE_OPTIONS = Object.values(PolicyType); 

export enum PlanType {
  UM = 'UM',
  UIM = 'UIM',
  UMBRELLA = 'Umbrella'
}

export const PLAN_TYPE_OPTIONS = Object.values(PlanType); 