import { KPI } from '@/services/kpiService';
import { UserTag } from '@/services/organizationService';

// Define which KPIs should be shown for each organization role
const roleKPIMap: Record<string, string[]> = {
  'Attorney': [],
  'Litigation': [
    'avg_case_age',
    'cases_touched',
    'notes_entered',
    'depositions_taken',
    'mediations_arb_trials',
    // 'hearings',
  ],
  'Litigation Team': [
    'avg_case_age',
    'cases_touched',
    'notes_entered',
    'depositions_taken',
    'mediations_arb_trials',
    // 'hearings',
  ],
  'Paralegal': [
    'avg_case_age',
    'cases_touched',
    'notes_entered',
    'depositions_taken',
    'mediations_arb_trials',
    'discovery_response_completed',
    'deposition_responses'
  ],
  'Paralegal Team': [
    'avg_case_age',
    'cases_touched',
    'notes_entered',
    'depositions_taken',
    'mediations_arb_trials',
    'discovery_response_completed',
    'deposition_responses'
  ],
  'Negotiations': [
    'case_closed',
    'cases_in_negotiation',
    'policy_limit_tenders',
    'total_attorney_fees',
  ],
  'Negotiation Team': [
    'case_closed',
    'cases_in_negotiation',
    'policy_limit_tenders',
    'team_settlement_rate',
    'team_recovery_amount',
    'team_response_time',
    'team_performance',
    'total_attorney_fees',
  ],
  'Case Manager': [
    'case_demand_ready',
    'case_closed',
    'cases_touched',
    'treatment_period',
    'avg_case_age',
    'care_calls_percentage',
    'notes_entered',
    'three_plus_providers'
  ],
  'Intake': [
    'intake_completed',
    'leads_converted_to_cases',
    'calls_within_first_hour',
    'time_to_sign_on',
    'conversion_rate',
    'avg_case_transfer_time',
    'first_hour_calls',
    'intake_completed',
    'leads_completed',
    'leads_converted',
    'leads_lost'
  ],
  'Intake Team': [
    'intake_completed',
    'leads_converted_to_cases',
    'calls_within_first_hour',
    'time_to_sign_on',
    'conversion_rate',
    'avg_case_transfer_time',
    'first_hour_calls',
    'intake_completed',
    'leads_completed',
    'leads_converted',
    'leads_lost'
  ],
  'Legal Assistant': [],
  'Marketing': [],
  'Reception': [
    'daily_duties_completed',
    'weekly_duties_completed',
    'daily_clean_completed'
  ],
  'Receptionist Team': [
    'daily_duties_completed',
    'weekly_duties_completed',
    'daily_clean_completed'
  ],
  'Managing Partner': []
};

export const filterKPIsByRole = (kpis: Record<string, KPI>, userTags: UserTag[]): Record<string, KPI> => {
  // If user has no tags, show minimal KPIs
  if (!userTags || userTags.length === 0) {
    return Object.entries(kpis).reduce((filtered, [key, kpi]) => {
      if (['cases_touched', 'notes_entered'].includes(kpi.kpi_type)) {
        filtered[key] = kpi;
      }
      return filtered;
    }, {} as Record<string, KPI>);
  }

  // Get all allowed KPIs based on user's tags
  const allowedKPIs = new Set<string>();
  userTags.forEach(tag => {
    const kpisForRole = roleKPIMap[tag.name];
    if (kpisForRole) {
      kpisForRole.forEach(kpi => allowedKPIs.add(kpi));
    }
  });

  // Filter KPIs based on user's roles
  return Object.entries(kpis).reduce((filtered, [key, kpi]) => {
    if (allowedKPIs.has(kpi.kpi_type)) {
      filtered[key] = kpi;
    }
    return filtered;
  }, {} as Record<string, KPI>);
};

// Function to check if user has Intake role
export const hasIntakeRole = (userTags: UserTag[]): boolean => {
  return userTags.some(tag => tag.name === 'Intake');
};

// Function to filter lead KPIs - only shown to Intake role
export const filterLeadKPIs = (leadKPIs: KPI[], userTags: UserTag[]): KPI[] => {
  // Only show lead KPIs to users with Intake role
  if (!hasIntakeRole(userTags)) {
    return [];
  }
  return leadKPIs;
}; 