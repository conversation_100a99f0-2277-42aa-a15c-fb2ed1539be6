import { Document, Packer, Paragraph, TextRun, AlignmentType, HeadingLevel, Table, TableRow, TableCell, WidthType, BorderStyle } from 'docx';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export interface DocumentGenerationOptions {
  title: string;
  fileName: string;
  format: 'pdf' | 'docx';
  creator?: string;
}

interface SectionData {
  title: string;
  items: Array<{
    label: string;
    amount: number;
    isTotal?: boolean;
    isNegative?: boolean;
  }>;
}

export const generateDocument = async (
  element: HTMLElement,
  options: DocumentGenerationOptions,
  sections: SectionData[],
  footerText?: string
): Promise<void> => {
  if (options.format === 'pdf') {
    await generatePDF(element, options.fileName);
  } else {
    await generateDOCX(sections, options, footerText);
  }
};

const generatePDF = async (element: HTMLElement, fileName: string): Promise<void> => {
  try {
    // Temporarily hide the edit button during PDF generation
    const editButton = element.querySelector(".edit-button");
    if (editButton) {
      editButton.classList.add("hidden");
    }

    // Add a temporary class for PDF generation
    element.classList.add("pdf-mode");

    const canvas = await html2canvas(element, {
      scale: 2,
      logging: false,
      useCORS: true,
      backgroundColor: "#ffffff",
      windowWidth: element.scrollWidth,
      windowHeight: element.scrollHeight,
    });

    // Remove the temporary class
    element.classList.remove("pdf-mode");

    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    const pdf = new jsPDF("p", "mm", "a4");

    // If the content is longer than one page, create multiple pages
    let heightLeft = imgHeight;
    let position = 0;
    const pageData = canvas.toDataURL("image/png");

    pdf.addImage(pageData, "PNG", 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(pageData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    pdf.save(fileName);

    // Restore the edit button
    if (editButton) {
      editButton.classList.remove("hidden");
    }
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};

const BOX_BG = 'F5F5F5'; // light gray
const BORDER_COLOR = 'CCCCCC';
const PADDING = 200; // twips (approx 0.14in)

const generateDOCX = async (
  sections: SectionData[],
  options: DocumentGenerationOptions,
  footerText?: string
): Promise<void> => {
  try {
    const children: (Paragraph | Table)[] = [
      new Paragraph({
        text: options.title,
        heading: HeadingLevel.HEADING_1,
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 },
      }),
    ];

    sections.forEach(section => {
      // Section Title
      children.push(
        new Paragraph({
          children: [new TextRun({ text: section.title, bold: true, size: 28 })],
          spacing: { before: 400, after: 200 },
        })
      );
      // Table for section items (boxed look)
      children.push(
        new Table({
          width: { size: 100, type: WidthType.PERCENTAGE },
          rows: section.items.map(item =>
            new TableRow({
              children: [
                new TableCell({
                  width: { size: 70, type: WidthType.PERCENTAGE },
                  margins: { top: PADDING, bottom: PADDING, left: PADDING, right: PADDING },
                  shading: { fill: BOX_BG },
                  borders: {
                    top: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                    bottom: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                    left: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                    right: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                  },
                  children: [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: item.label,
                          bold: !!item.isTotal,
                          size: 24,
                        }),
                      ],
                    }),
                  ],
                }),
                new TableCell({
                  width: { size: 30, type: WidthType.PERCENTAGE },
                  margins: { top: PADDING, bottom: PADDING, left: PADDING, right: PADDING },
                  shading: { fill: BOX_BG },
                  borders: {
                    top: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                    bottom: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                    left: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                    right: { style: BorderStyle.SINGLE, size: 2, color: BORDER_COLOR },
                  },
                  children: [
                    new Paragraph({
                      alignment: AlignmentType.RIGHT,
                      children: [
                        new TextRun({
                          text: `${item.isNegative ? '-' : ''}$${Math.abs(item.amount).toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}`,
                          bold: !!item.isTotal,
                          color: item.isTotal
                            ? (item.isNegative ? 'C00000' : '00B050')
                            : item.isNegative
                            ? 'C00000'
                            : '00B050',
                          size: 24,
                        }),
                      ],
                    }),
                  ],
                }),
              ],
            })
          ),
        })
      );
    });

    // Add footer text if provided
    if (footerText) {
      children.push(
        new Paragraph({
          children: [new TextRun({ text: footerText, size: 22 })],
          spacing: { before: 400 },
        })
      );
    }

    const doc = new Document({
      sections: [{ properties: {}, children }],
      creator: options.creator,
      title: options.title,
    });

    const blob = await Packer.toBlob(doc);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = options.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating Word document:', error);
    throw error;
  }
}; 