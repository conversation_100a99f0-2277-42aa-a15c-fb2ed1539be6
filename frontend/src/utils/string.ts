/**
 * Formats a string by replacing underscores with spaces and capitalizing first letter of each word
 * @param str - The input string to format
 * @returns Formatted string with spaces and proper capitalization
 * @example
 * formatText("hello_world") // returns "Hello World"
 * formatText("user_first_name") // returns "User First Name"
 */
export const formatText = (str: string): string => {
  if (!str) return '';
  
  return str
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}; 