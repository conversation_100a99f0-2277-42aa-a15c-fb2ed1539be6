import { Descendant } from "slate";

// export const parseRichTextData = (data: string | Descendant[]): Descendant[] => {
//   if (!data) {
//     return [{ type: 'paragraph', children: [{ text: '' }] }];
//   }
//   console.log(data, typeof data);
//   if (typeof data === 'string') {
//     try {
//       const parsed = JSON.parse(data);
//       if (Array.isArray(parsed)) {
//         console.log(parsed, typeof parsed, "parsed");
//         return parsed;
//       } else if (parsed && typeof parsed === 'object' && 'richText' in parsed) {
//         console.log(parsed.richText, "parsed.richText");
//         return parsed.richText;
//       } else if (parsed && typeof parsed === 'object') {
//         console.log(parsed, "parsed");
//         return parsed;
//       } else {
//         return parsed;
//       }
//     } catch (error) {
//       console.error('Error parsing rich text data:', error);
//       return [{
//         type: 'paragraph',
//         children: [{ text: data }]
//       }];
//     }
//   } else if (typeof data === 'object') {
//     return data;
//   }
//   return Array.isArray(data) ? data : [{
//     type: 'paragraph',
//     children: [{ text: String(data) }]
//   }];
// };

export const stringToRichText = (data: string): Descendant[] => {
  if (!data) {
    return [{ type: 'paragraph', children: [{ text: '' }] }];
  }

  try {
    const parsed = JSON.parse(data);
    if (Array.isArray(parsed)) {
      return parsed;
    } else if (parsed && typeof parsed === 'object' && 'richText' in parsed) {
      return parsed.richText;
    } else if (parsed && typeof parsed === 'object') {
      return parsed;
    } else {
      return JSON.parse(parsed);
    }
  } catch (error) {
    console.log('Error parsing rich text data:', error);
    return [{
      type: 'paragraph',
      children: [{ text: data }]
    }];
  }
};

export const richTextToString = (data: Descendant[]): string => {
  if (!data || !Array.isArray(data)) {
    return '';
  }
  return JSON.stringify(data);
};