// src/utils/extractionUtils.ts

import { MedicalAnalysisData, TreatmentExpenseData } from '@/type/documentData';
import { DocumentCategory, DocumentWithCategories } from '@/type/doocument';

// Add these new type definitions
type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
type JsonObject = { [key: string]: JsonValue };
type JsonArray = JsonValue[];

interface TreatmentDetail {
  [key: string]: JsonValue;
}

interface TreatmentExpense {
  [key: string]: JsonValue;
}

export interface RawFileData {
  treatment_detail?: string | TreatmentDetail;
  treatment_expense?: {
    treatment_expenses?: TreatmentExpense[];
    total_amount_charged?: string | number;
    supporting_documents?: string[];
  };
}

/**
 * Formats a monetary amount into a currency string
 */
export const formatAmount = (amount: string | number | null | undefined): string => {
  if (!amount) return 'N/A';
  
  if (Array.isArray(amount)) {
    return amount.map(formatSingleAmount).join(', ');
  }
  return formatSingleAmount(amount);
};

/**
 * Formats a single monetary value into a currency string
 */
const formatSingleAmount = (amount: string | number): string => {
  if (typeof amount === 'number') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  if (typeof amount === 'string') {
    // Return as is if already formatted
    if (amount.startsWith('$')) return amount;
    
    // Try to parse and format
    const parsed = parseFloat(amount.replace(/[$,]/g, ''));
    if (!isNaN(parsed)) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(parsed);
    }
  }
  
  return 'N/A';
};

/**
 * Checks if a value is considered "missing" data
 */
export const isMissingData = (value: unknown): boolean => {
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  return value === undefined || 
         value === null || 
         value === '' || 
         value === 'N/A' ||
         value === 'undefined' ||
         value === 'null';
};

/**
 * Gets the filename from a file path
 */
export const getFileName = (filePath: string): string => {
  const parts = filePath.split('/');
  return parts[parts.length - 1];
};

/**
 * Transforms raw medical analysis data into a consistent format
 */
export const transformMedicalData = (data: Record<string, RawFileData>): Record<string, MedicalAnalysisData> => {
  const transformed: Record<string, MedicalAnalysisData> = {};

  for (const [fileName, fileData] of Object.entries(data)) {
    if (typeof fileData === 'object' && fileData !== null) {
      let treatmentDetail: TreatmentDetail = {};

      // Handle treatment_detail which can be either an object or a JSON string
      if (fileData.treatment_detail) {
        treatmentDetail = typeof fileData.treatment_detail === 'string'
          ? (safeJsonParse(fileData.treatment_detail) as TreatmentDetail)
          : fileData.treatment_detail;
      }

      transformed[fileName] = {
        treatment_timeline: getArrayOrStringValue(treatmentDetail, 'treatment_timeline'),
        number_of_visits: getStringOrNumberValue(treatmentDetail, 'number_of_visits'),
        hospital_name: getStringValue(treatmentDetail, 'hospital_name'),
        doctor_name: getStringValue(treatmentDetail, 'doctor_name'),
        summary: getStringValue(treatmentDetail, 'summary')
      };
    }
  }

  return transformed;
};

/**
 * Transforms raw treatment expense data into a consistent format
 */
export const transformTreatmentExpenseData = (
  data: Record<string, RawFileData>
): Record<string, TreatmentExpenseData> => {
  const transformed: Record<string, TreatmentExpenseData> = {};

  for (const [fileName, fileData] of Object.entries(data)) {
    if (fileData?.treatment_expense?.treatment_expenses) {
      transformed[fileName] = {
        treatment_expense: {
          treatment_expenses: fileData.treatment_expense.treatment_expenses.map((expense: TreatmentExpense) => ({
            hospital_name: getStringValue(expense, 'hospital_name'),
            doctor_name: getStringValue(expense, 'doctor_name'),
            treatment_period: getStringValue(expense, 'treatment_period'),
            amount_charged: getStringOrNumberValue(expense, 'amount_charged')
          })),
          total_amount_charged: getStringOrNumberValue(
            fileData.treatment_expense, 
            'total_amount_charged'
          ),
          supporting_documents: Array.isArray(fileData.treatment_expense.supporting_documents)
            ? fileData.treatment_expense.supporting_documents
            : []
        }
      };
    }
  }

  return transformed;
};

/**
 * Safely parses a JSON string
 */
const safeJsonParse = (str: string): JsonValue => {
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return {};
  }
};

/**
 * Gets a value that can be either an array or string
 */
const getArrayOrStringValue = (obj: JsonObject | null | undefined, key: string): string[] | string => {
  if (!obj || !obj[key]) return '';
  return Array.isArray(obj[key]) ? obj[key] as string[] : String(obj[key]);
};

/**
 * Gets a string value from an object, with fallback
 */
const getStringValue = (obj: JsonObject | null | undefined, key: string): string => {
  if (!obj || !obj[key]) return '';
  return String(obj[key]);
};

/**
 * Gets a value that can be either a string or number
 */
const getStringOrNumberValue = (obj: JsonObject | null | undefined, key: string): string | number => {
  if (!obj || !obj[key]) return '';
  return obj[key] as string | number;
};

/**
 * Flattens a nested object into a single level
 */
export const flattenObject = (
  obj: JsonObject,
  prefix: string = ''
): Record<string, JsonValue> => {
  return Object.keys(obj).reduce((acc: Record<string, JsonValue>, k: string) => {
    const pre = prefix.length ? prefix + '.' : '';
    if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
      Object.assign(acc, flattenObject(obj[k] as JsonObject, pre + k));
    } else {
      acc[pre + k] = obj[k];
    }
    return acc;
  }, {});
};

/**
 * Capitalizes the first letter of each word and replaces underscores with spaces
 */
export const formatFieldName = (field: string): string => {
  return field
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Gets unique file names from a list of categories using a category-to-file mapping
 * @param categories - Array of category names
 * @param categoryToFileMapping - Object mapping categories to file paths
 * @returns Array of unique file names
 */
export const getFileNamesFromCategories = (
  categories: string[],
  categoryToFileMapping: Record<string, string[]>
): string[] => {
  if (!categoryToFileMapping || Object.keys(categoryToFileMapping).length === 0) {
    return [];
  }

  const uniqueFilePaths = new Set<string>();

  categories.forEach(category => {
    const files = categoryToFileMapping[category] || [];
    files.forEach(file => uniqueFilePaths.add(file));
  });

  return Array.from(uniqueFilePaths).map(filePath => getFileName(filePath));
};


export const createCategoryFileMapping = (documents: DocumentWithCategories[]): Record<string, string[]> => {
  const mapping: Record<string, string[]> = {};
  
  documents.forEach(doc => {
    doc.categories.forEach((cat: DocumentCategory) => {
      const categoryDisplay = cat.category_display;
      
      if (!mapping[categoryDisplay]) {
        mapping[categoryDisplay] = [];
      }
      
      // Add case_id prefix to maintain old format
      const fullPath = `${doc.case_id}/${doc.file_name}`;
      if (!mapping[categoryDisplay].includes(fullPath)) {
        mapping[categoryDisplay].push(fullPath);
      }
    });
  });
  
  return mapping;
};







