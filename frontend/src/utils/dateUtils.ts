import { format, parse } from "date-fns";

// Format date from yyyy-MM-dd to dd-MM-yyyy for input display
export const formatDateForInput = (date: string | null | undefined) => {
  if (!date) return "";
  try {
    // Add time component to prevent timezone issues
    const parsedDate = new Date(date + "T00:00:00");
    return format(parsedDate, "dd-MM-yyyy");
  } catch {
    return "";
  }
};

// Format date from dd-MM-yyyy to yyyy-MM-dd for API
export const formatDateForApi = (date: string | Date) => {
  console.log("date", date)
  if (!date) return "";
  try {
    if (date instanceof Date) {
      // If it's already a Date object, format it directly
      return format(date, "yyyy-MM-dd");
    }
    
    // Try to parse as dd-MM-yyyy first
    let parsedDate = parse(date, "dd-MM-yyyy", new Date());

    // If invalid, try to parse as M/d/yyyy
    if (isNaN(parsedDate.getTime())) {
      parsedDate = parse(date, "M/d/yyyy", new Date());
    }

    console.log("parsedDate", parsedDate)
    // Format without timezone conversion
    return format(parsedDate, "yyyy-MM-dd");
  } catch {
    console.log("error at formatDateForApi")
    return "";
  }
};

// Validate a date string in dd-MM-yyyy format
export const validateDate = (date: string, allowFuture: boolean = true) => {
  if (!date) {
    return { isValid: false, error: "Date is required" };
  }
  try {
    const parsedDate = parse(date, "dd-MM-yyyy", new Date());
    // Add time component to prevent timezone issues
    const today = new Date(new Date().toDateString());

    if (isNaN(parsedDate.getTime())) {
      return { isValid: false, error: "Invalid date format" };
    }
    if (!allowFuture && parsedDate > today) {
      return { isValid: false, error: "Date cannot be in the future" };
    }
    return { isValid: true, error: "" };
  } catch {
    return { isValid: false, error: "Invalid date format" };
  }
};

// Format date from yyyy-MM-dd to MM/dd/yyyy for display
export const formatDateForDisplay = (date: string | null | undefined, formatString: string = "MM/dd/yyyy"): string => {
  if (!date) return "";
  try {
    // Handle ISO date strings with timezone
    let parsedDate: Date;
    if (date.includes('T')) {
      // If it's an ISO date string, parse it directly
      parsedDate = new Date(date);
    } else {
      // For simple date strings, append time component
      parsedDate = new Date(date + "T00:00:00");
    }

    if (isNaN(parsedDate.getTime())) {
      console.error("Invalid date:", date);
      return "";
    }

    const formattedDate = format(parsedDate, formatString);
    return formattedDate;
  } catch (error) {
    console.error("Error in formatDateForDisplay:", error);
    return "";
  }
};

// Format time from 24-hour format (HH:mm) to 12-hour format (hh:mm AM/PM) for display
export const format24HourTo12Hour = (time24: string): string => {
  if (!time24 || !time24.includes(':')) return '';

  try {
    const [hourStr, minuteStr] = time24.split(':');
    const hour = parseInt(hourStr, 10);

    if (isNaN(hour) || hour < 0 || hour > 23) return '';

    const period = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;

    return `${hour12.toString().padStart(2, '0')}:${minuteStr} ${period}`;
  } catch {
    return '';
  }
};

// Format time from 12-hour format (hh:mm AM/PM) to 24-hour format (HH:mm) for API
export const format12HourTo24Hour = (time12: string): string => {
  if (!time12) return '';

  try {
    // Extract components: hours, minutes, and period (AM/PM)
    const match = time12.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
    if (!match) return '';

    const [, hourStr, minuteStr, period] = match;
    let hour = parseInt(hourStr, 10);

    // Validate hour
    if (hour < 1 || hour > 12) return '';

    // Convert to 24-hour format
    if (period.toUpperCase() === 'PM' && hour < 12) {
      hour += 12;
    } else if (period.toUpperCase() === 'AM' && hour === 12) {
      hour = 0;
    }

    return `${hour.toString().padStart(2, '0')}:${minuteStr}`;
  } catch {
    return '';
  }
};

/**
 * Formats a date into yyyy-MM-dd format for consistent comparison across timezones
 * @param date - Date to format (can be Date object, ISO string, or any valid date string)
 * @returns Formatted date string in yyyy-MM-dd format, normalized to UTC
 * @throws Error if date is invalid
 */
export const formatDateForComparison = (date: Date | string): string => {
  try {
    // Convert input to Date object if it's a string
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      throw new Error('Invalid date');
    }

    // Get UTC components to avoid timezone issues
    const year = dateObj.getUTCFullYear();
    const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getUTCDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Error in formatDateForComparison:', error);
    throw new Error('Failed to format date for comparison');
  }
};

/**
 * Formats a date to show the day name (e.g., "Monday", "Sunday")
 * @param date - Date to format (can be Date object, ISO string, or any valid date string)
 * @returns Day name string (e.g., "Monday", "Sunday")
 */
export const getDayName = (date: string | Date | null | undefined): string => {
  if (!date) return "";
  
  try {
    // Convert string to Date if needed
    const parsedDate = typeof date === 'string' 
      ? date.includes('T') 
        ? new Date(date)
        : new Date(date + "T00:00:00")
      : date;

    if (isNaN(parsedDate.getTime())) {
      console.error("Invalid date:", date);
      return "";
    }

    return parsedDate.toLocaleDateString('en-US', { weekday: 'long' });
  } catch (error) {
    console.error("Error in getDayName:", error);
    return "";
  }
}; 