import { User } from "./user";
import { USStates } from "@/constants/commont";

export const ENGAGEMENT_CATEGORIES = [
  "Networking",
  "Education/Training",
  "Marketing",
  "Support",
  "Other"
] as const;

export enum PreferredContactMethod {
  EMAIL = "EMAIL",
  PHONE = "PHONE",
  TEXT = "TEXT",
  MAIL = "MAIL",
}

export enum PartnerType {
  MEDICAL = "MEDICAL",
  LEGAL = "LEGAL",
  INSURANCE = "INSURANCE",
  OTHER = "OTHER"
}

export enum RelationshipStatus {
  NEW = "NEW",
  DEVELOPING = "DEVELOPING",
  ACTIVE = "ACTIVE",
  STRONG = "STRONG",
  INACTIVE = "INACTIVE",
  PROSPECT = "PROSPECT"
}

export const PartnerTypeLabels: Record<PartnerType, string> = {
  [PartnerType.MEDICAL]: "Medical Provider",
  [PartnerType.LEGAL]: "Legal",
  [PartnerType.INSURANCE]: "Insurance",
  [PartnerType.OTHER]: "Other"
};

export const RelationshipStatusLabels: Record<RelationshipStatus, string> = {
  [RelationshipStatus.NEW]: "New Contact",
  [RelationshipStatus.DEVELOPING]: "Developing Relationship",
  [RelationshipStatus.ACTIVE]: "Active Partner",
  [RelationshipStatus.STRONG]: "Strong Partner",
  [RelationshipStatus.INACTIVE]: "Inactive",
  [RelationshipStatus.PROSPECT]: "Prospect"
};

export interface PartnerContact {
  id: number;
  organization: number;
  source_tag?: number;
  source_tag_id?: number;

  // Basic Information
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  phone_ext?: string;
  role?: string;
  photo_url?: string;
  profile_photo?: string;
  profile_photo_url?: string;

  // Business Information
  business_name: string;
  partner_type: PartnerType;
  relationship_status: RelationshipStatus;
  website?: string;
  instagram?: string;
  specialties?: string;

  // Medical Provider Relationship
  medical_provider?: MedicalProvider | null;
  medical_provider_id?: number | null;

  // Address Information
  street1: string;
  street2?: string;
  city: string;
  state: USStates;
  zip_code: string;
  latitude?: number;
  longitude?: number;

  // Medical Provider Specific
  accepts_liens: boolean;
  no_records_service: boolean;
  do_not_use: boolean;

  // Additional Information
  notes?: string;
  is_active: boolean;
  assigned_to?: User;
  assigned_to_id?: number;

  // Metrics
  total_referrals: number;
  successful_referrals: number;
  last_engagement_date?: string;
  next_engagement_date?: string;
  average_case_value?: number;
  total_revenue_generated?: number;

  // Timestamps
  created_at: string;
  updated_at: string;

  created_by?: User;
}

export interface EngagementType {
  id: number;
  name: string;
  description?: string;
  category: string;
  default_duration?: number;
  is_active: boolean;
  requires_follow_up: boolean;
  typical_cost?: number;
  success_metrics?: string;
}

export interface Engagement {
  id: number;
  partner: number | PartnerContact;
  partner_name: string;
  partner_type?: string;
  partner_type_display?: string;
  engagement_type: number;
  engagement_type_name?: string;
  status: string;
  date: string;
  duration?: number | null;
  follow_up_date?: string;
  reminder_sent: boolean;
  reminder_sent_date?: string;
  location_type: string;
  location?: string;
  virtual_meeting_link?: string;
  virtual_meeting_platform?: string;
  agenda?: string;
  notes?: string;
  follow_up_notes?: string;
  outcomes?: string;
  next_steps?: string;
  attendees: number[];
  attendees_names?: string[];
  created_by?: User;
  created_by_name?: string;
  budgeted_cost?: number;
  actual_cost?: number;
  cost_notes?: string;
  success_rating?: number;
  metrics_notes?: string;
  expected_referrals?: number;
  actual_referrals?: number;
  expected_revenue?: number;
  actual_revenue?: number;
  created_at: string;
  updated_at: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface MapData {
  id: number;
  business_name: string;
  partner_type: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address: string;
}

export interface Statistics {
  total_partners: number;
  active_partners: number;
  total_referrals: number;
  successful_referrals: number;
  total_revenue: number;
  upcoming_engagements: number;
}

export interface DashboardPartnerStats {
    total_partners: number;
    active_partners: number;
}

export interface DashboardEngagementStats {
    total_engagements: number;
    engagement_status: {
        [key: string]: number;
    };
    upcoming_engagements: number;
    engagement_types: {
        [key: string]: number;
    };
}

export interface DashboardRecentActivity {
    new_partners: number;
    completed_engagements: number;
    planned_engagements: number;
}

export interface DashboardTimeRange {
    start_date: string;
    end_date: string;
}

export interface DashboardResponse {
    partner_stats: DashboardPartnerStats;
    engagement_stats: DashboardEngagementStats;
    recent_activity: DashboardRecentActivity;
    time_range: DashboardTimeRange;
}

export interface MedicalProvider {
  id: number;
  company: string;
  specialties: string;
}

export interface LinkMedicalProviderResponse {
  message: string;
  medical_provider?: MedicalProvider;
}