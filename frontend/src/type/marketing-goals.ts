// marketing-goals.ts - Types for marketing goals

import { User } from "./user";
import { EngagementType } from "./marketing";

// Partner activity interface
export interface PartnerActivity {
  id: number;
  partner_id: number;
  partner_name: string;
  activity_type: string;
  activity_date: string;
  description: string;
}

// Top partner interface
export interface TopPartner {
  partner_id: number;
  partner_name: string;
  total_revenue: number;
  engagement_count: number;
  success_rate: number;
}

export enum GoalCategory {
  REFERRAL = "REFERRAL",
  ENGAGEMENT = "ENGAGEMENT",
  REVENUE = "REVENUE",
  PARTNER = "PARTNER",
  OTHER = "OTHER" // Added for fallback
}

export enum GoalRecurrence {
  NONE = "NONE",
  MONTHLY = "MONTHLY",
  QUARTERLY = "QUARTERLY",
  YEARLY = "YEARLY"
}

export enum GoalMetricType {
  COUNT = "COUNT",
  MONEY = "MONEY",
  PERCENTAGE = "PERCENTAGE"
}

export enum GoalStatus {
  ACTIVE = "ACTIVE",
  COMPLETED = "COMPLETED",
  ARCHIVED = "ARCHIVED"
}

export const GoalCategoryLabels: Record<GoalCategory, string> = {
  [GoalCategory.REFERRAL]: "Referral",
  [GoalCategory.ENGAGEMENT]: "Engagement",
  [GoalCategory.REVENUE]: "Revenue",
  [GoalCategory.PARTNER]: "Partner",
  [GoalCategory.OTHER]: "Other"
};

export const GoalRecurrenceLabels: Record<GoalRecurrence, string> = {
  [GoalRecurrence.NONE]: "No Recurrence",
  [GoalRecurrence.MONTHLY]: "Monthly",
  [GoalRecurrence.QUARTERLY]: "Quarterly",
  [GoalRecurrence.YEARLY]: "Yearly"
};

export const GoalMetricTypeLabels: Record<GoalMetricType, string> = {
  [GoalMetricType.COUNT]: "Count",
  [GoalMetricType.MONEY]: "Money",
  [GoalMetricType.PERCENTAGE]: "Percentage"
};

export const GoalStatusLabels: Record<GoalStatus, string> = {
  [GoalStatus.ACTIVE]: "Active",
  [GoalStatus.COMPLETED]: "Completed",
  [GoalStatus.ARCHIVED]: "Archived"
};

export interface MarketingGoal {
  id: number;
  name: string;
  category: GoalCategory;
  recurrence: GoalRecurrence;
  metric_type: GoalMetricType;
  default_target: number;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;

  created_by?: User;
}

// Using EngagementType imported at the top of the file

export interface UserMarketingGoal {
  id: number;
  user: User;
  marketing_goal: MarketingGoal | null;
  // marketing_goal_id?: number; // For create/update operations
  goal_id?: number; // For create/update operations
  target_value: number;
  current_period_start: string;
  current_period_end: string;
  is_recurring: boolean;
  recurrence_end_date?: string;
  status: GoalStatus;
  current_progress: number;
  completion_percentage: number;
  notes?: string;
  created_at: string;
  updated_at: string;

  // New fields for engagement goals
  type?: string; // 'goal' or 'engagement'
  type_of_goal?: string; // 'USER_MARKETING_GOAL' or 'ENGAGEMENT_GOAL'
  engagement_goal?: EngagementType; // For engagement goals
  engagement_type?: EngagementType; // Alternative field name for engagement goals
  created_by?: User; // User who created the goal
}

export interface UserMarketingGoalHistory {
  id: number;
  user_goal: UserMarketingGoal;
  period_start: string;
  period_end: string;
  target_value: number;
  achieved_value: number;
  completion_percentage: number;
  completed_at: string;
}

export interface GoalProgressUpdate {
  progress: number;
}

export interface GoalsSummary {
  overall_metrics: {
    total_goals: number;
    completed_goals: number;
    avg_completion: number;
  };
  goals_by_category: Array<{
    marketing_goal__category: GoalCategory;
    total: number;
    completed: number;
    avg_progress: number;
  }>;
  personal_goals: Array<{
    marketing_goal__name: string;
    target_value: number;
    current_progress: number;
    current_period_end: string;
    progress_percentage: number;
  }>;
  top_performers: Array<{
    user__first_name: string;
    user__last_name: string;
    goals_completed: number;
    total_goals: number;
    completion_rate: number;
  }>;
}

export interface AnalyticsDashboardResponse {
  goals_summary: GoalsSummary;
  partner_metrics: {
    total_partners: number;
    active_partners: number;
    status_breakdown: Array<{
      relationship_status: string;
      count: number;
      percentage: number;
    }>;
    recent_activity: Array<PartnerActivity>; // Partner activity
    type_distribution: Array<{
      partner_type: string;
      count: number;
      total_revenue: number;
    }>;
    top_partners: Array<TopPartner>; // Top performing partners
  };
  engagement_analytics: {
    metrics: {
      total_engagements: number;
      total_cost: number;
      avg_success_rating: number;
      total_revenue: number;
    };
    type_breakdown: Array<{
      engagement_type__name: string;
      engagement_type__category: string;
      count: number;
      avg_success: number;
      total_revenue: number;
      roi: number;
    }>;
    monthly_trends: Array<{
      month: string;
      count: number;
      revenue: number;
      cost: number;
    }>;
    upcoming_engagements: Array<{
      date: string;
      engagement_type__name: string;
      partner__business_name: string;
      location_type: string;
    }>;
  };
  performance_trends: {
    goal_completion_trends: Array<{
      month: string;
      avg_completion: number;
      total_goals: number;
    }>;
    revenue_trends: Array<{
      month: string;
      revenue: number;
      cost: number;
      roi: number;
    }>;
    year_over_year: {
      current_year: {
        total_revenue: number;
        total_engagements: number;
        new_partners: number;
        goal_completion_rate: number;
      };
      previous_year: {
        total_revenue: number;
        total_engagements: number;
        new_partners: number;
        goal_completion_rate: number;
      };
      growth_rates: {
        total_revenue: number;
        total_engagements: number;
        new_partners: number;
        goal_completion_rate: number;
      };
    };
  };
}

// User Analytics Response Interface
export interface UserAnalyticsResponse {
  user: {
    id: number;
    name: string;
    email: string;
  };
  time_period: {
    start_date: string;
    end_date: string;
  };
  goal_analytics: {
    overall_metrics: {
      total_goals: number;
      completed_goals: number;
      avg_completion: number;
    };
    goals_by_category: Array<{
      marketing_goal__category: GoalCategory;
      total: number;
      completed: number;
      avg_progress: number;
    }>;
    engagement_goals: Array<{
      engagement_goal__name: string;
      engagement_goal__category: string;
      total: number;
      completed: number;
      avg_progress: number;
    }>;
    goal_history: Array<{
      id: number;
      period_start: string;
      period_end: string;
      target_value: number;
      achieved_value: number;
      completion_percentage: number;
      completed_at: string;
    }>;
    active_goals: Array<{
      id: number;
      marketing_goal__name: string | null;
      engagement_goal__name: string | null;
      target_value: number;
      current_progress: number;
      current_period_end: string;
      progress_percentage: number;
    }>;
  };
  engagement_analytics: {
    created_metrics: {
      total_engagements: number;
      completed_engagements: number;
      cancelled_engagements: number;
      avg_success_rating: number;
      total_cost: number | null;
      total_revenue: number | null;
      expected_referrals: number | null;
      actual_referrals: number | null;
      roi: number;
      referral_conversion_rate: number;
    };
    attended_count: number;
    engagement_types: Array<{
      engagement_type__name: string;
      engagement_type__category: string;
      count: number;
      completed: number;
      avg_success: number | null;
      total_revenue: number | null;
      total_cost: number | null;
      roi: number;
    }>;
    monthly_trends: Array<{
      month: string;
      count: number;
      completed: number;
      revenue: number | null;
      cost: number | null;
      referrals: number | null;
    }>;
    upcoming_engagements: Array<{
      id: number;
      date: string;
      engagement_type__name: string;
      partner__business_name: string;
      location_type: string;
      status: string;
    }>;
  };
  partner_analytics: {
    total_partners: number;
    active_partners: number;
    status_breakdown: Array<{
      relationship_status: string;
      count: number;
      percentage: number;
    }>;
    type_distribution: Array<{
      partner_type: string;
      count: number;
      total_revenue: number | null;
    }>;
    partner_engagement: Array<{
      partner_id: number;
      partner__business_name: string;
      engagement_count: number;
      completed_engagements: number;
      revenue_generated: number | null;
      referrals_generated: number | null;
    }>;
    partner_improvements: Array<{
      partner_id: number;
      partner__business_name: string;
      avg_success_rating: number;
      engagement_count: number;
    }>;
    total_revenue: number;
    total_referrals: number;
  };
  performance_trends: {
    goal_trends: Array<{
      month: string;
      avg_completion: number;
      total_goals: number;
      completed_goals?: number;
    }>;
    engagement_trends: Array<{
      month: string;
      count: number;
      completed: number;
      avg_success: number;
      revenue: number | null;
      referrals: number | null;
    }>;
    improvement_rates: Record<string, {
      current_period: number;
      previous_period: number;
      change_percentage: number;
    }>;
  };
  comparative_analytics: {
    organization_averages: {
      goal_completion: number;
      engagement_success_rating: number;
      referral_conversion: number;
      engagement_roi: number;
    };
    user_percentiles: {
      goal_completion_percentile: number;
      engagement_success_percentile: number;
    };
    top_performers: Array<{
      id: number;
      first_name: string;
      last_name: string;
      goal_completion: number;
    }>;
  };
  performance_score: {
    total_score: number;
    category: string;
    component_scores: {
      goal_achievement: number;
      engagement_effectiveness: number;
      partner_management: number;
      improvement: number;
    };
  };
}