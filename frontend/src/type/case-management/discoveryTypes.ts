import { UserDetail } from './commonTypes';
import { Defendant } from './defendantTypes';


export enum DiscoveryPropoundedBy {
  DEFENSE = "Defense",
  PLAINTIFF = "Plaintiff",
  INTERVENOR = "Intervenor"
}

export enum DiscoveryType {
  DISCLOSURE_REQUESTS = "194-Disclosure Requests",
  EXPERT_WITNESSES = "Demand for Exchange of Expert Witnesses",
  MEDICAL_EXAMINATION = "Demand for Medical Examination",
  WRITTEN_QUESTIONS = "Deposition by Written Questions",
  FORM_INTERROGATORIES = "Form Interrogatories",
  NOTICE_OF_DEPOSITION = "Notice of Deposition",
  NOTICE_OF_DEPOSITION_PRODUCTION = "Notice of Deposition - Production Request",
  NOTICE_OF_RECORDS_DEPOSITIONS = "Notice of Records Depositions",
  REQUEST_FOR_ADMISSIONS = "Request for Admissions",
  REQUEST_FOR_PRODUCTION = "Request for Production of Documents",
  SPECIAL_INTERROGATORIES = "Special Interrogatories",
  STATEMENT_OF_DAMAGES = "Statement of Damages",
  SUPPLEMENTAL_INTERROGATORIES = "Supplemental Interrogatories",
  SUPPLEMENTAL_PRODUCTION = "Supplemental Request for Production"
}

export enum DeliveryMethod {
  BY_MAIL = "By Mail",
  EMAIL = "Email",
  E_SERVICE = "E-Service",
  FACSIMILE = "Facsimile",
  IN_PERSON = "In Person",
  OVERNIGHT_MAIL = "Overnight Mail"
}

export interface DiscoveryResponse {
  id: number;
  discovery: number;
  date: string;
  note: string;
  created_at: string;
  updated_at: string;
}

export interface DiscoveryExtension {
  id: number;
  discovery: number;
  date: string;
  created_at: string;
  updated_at: string;
}

export interface CaseDiscoveryRead {
  id: number;
  case: string;
  propounded_by: DiscoveryPropoundedBy;
  discovery_type: DiscoveryType;
  set_number: number;
  delivery_method?: DeliveryMethod;
  defendant: Defendant;
  served?: string;
  due?: string;
  description?: string;
  workers: UserDetail[];
  status_note?: string;
  completed_date?: string;
  created_at: string;
  updated_at: string;
  responses: DiscoveryResponse[];
  extensions: DiscoveryExtension[];
}

export interface CaseDiscoveryWrite {
  id?: number;
  case?: string;
  propounded_by: DiscoveryPropoundedBy;
  discovery_type: DiscoveryType;
  set_number: number;
  delivery_method?: DeliveryMethod;
  defendant: number;
  served?: string;
  due?: string;
  description?: string;
  workers: number[];
  status_note?: string;
} 