export enum ClientTrustEntryType {
  CLIENT_PAYMENT = "CLIENT_CHECK",
  FEE_REIMBURSEMENT = "FEE_REIMBURSEMENT",
  TRUST_REIMBURSEMENT = "TRUST_REIMBURSEMENT",
  SETTLEMENT_PAYMENT = "SETTLEMENT_CHECK",
  ATTORNEY_PAYMENT = "ATTORNEY_CHECK",
  OTHER_CHECK = "OTHER_CHECK",
  OTHER_DEPOSIT = "OTHER_DEPOSIT",
  PROVIDER_PAYMENT = "PROVIDER_CHECK",
  BRUMLEY_LAW_FIRM_COST = "BRUMLEY_LAW_FIRM_COST",
  REFERRED_FEE = "REFERRED_FEE",
  MEDPAY_DEPOSIT = "MEDPAY_DEPOSIT"
}

export enum DepositType {
  THIRD_PARTY = "THIRD_PARTY",
  UIM = "UIM"
}

// Display values for UI
export const TRUST_ENTRY_DISPLAY_VALUES: Record<ClientTrustEntryType, string> = {
  [ClientTrustEntryType.CLIENT_PAYMENT]: "CLIENT PAYMENT",
  [ClientTrustEntryType.FEE_REIMBURSEMENT]: "FEE REIMBURSEMENT",
  [ClientTrustEntryType.TRUST_REIMBURSEMENT]: "TRUST REIMBURSEMENT", 
  [ClientTrustEntryType.SETTLEMENT_PAYMENT]: "SETTLEMENT PAYMENT",
  [ClientTrustEntryType.ATTORNEY_PAYMENT]: "ATTORNEY PAYMENT",
  [ClientTrustEntryType.OTHER_CHECK]: "OTHER CHECK",
  [ClientTrustEntryType.OTHER_DEPOSIT]: "OTHER DEPOSIT",
  [ClientTrustEntryType.PROVIDER_PAYMENT]: "PROVIDER PAYMENT",
  [ClientTrustEntryType.BRUMLEY_LAW_FIRM_COST]: "BRUMLEY LAW FIRM COST",
  [ClientTrustEntryType.REFERRED_FEE]: "REFERRED FEE",
  [ClientTrustEntryType.MEDPAY_DEPOSIT]: "MEDPAY DEPOSIT"
};

export interface ClientTrust {
  id?: number;
  case?: string;
  check_number?: string;
  issuer_payee?: string;
  memo?: string;
  amount: string;
  demand_type?: string;
  client_trust_entry_type?: ClientTrustEntryType;
  is_deposit?: boolean;
  deposit_date?: string;
  balance?: string;
  created_at?: string;
  updated_at?: string;
  void?: boolean;
  deposit_type?: string;
  negotiation_id?: number;
}

export interface ClientTrustSummary {
  total_deposits: string;
  total_debits: string;
  current_balance: string;
}
