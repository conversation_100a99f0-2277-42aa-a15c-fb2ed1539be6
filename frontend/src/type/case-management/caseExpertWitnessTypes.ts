import { DateString } from '@/type/case-management/commonTypes';

// Base Expert Witness Contact type
export interface ExpertWitnessContact {
    id: number;
    first_name: string;
    last_name: string;
    specialties?: string;
    email?: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    retainer_fee?: string;
    testimony_fee?: string;
    tax_id?: string;
    note?: string;
    created_at: DateString;
    updated_at: DateString;
}

// Case Expert Witness Read type (from CaseExpertWitnessRead in swagger)
export interface CaseExpertWitnessRead {
    id: number;
    case: string;
    expert_witness: ExpertWitnessContact;
    type?: 'Defense' | 'Plaintiff';
    description?: string;
    created_at: DateString;
    updated_at: DateString;
}

// Case Expert Witness Write type (from CaseExpertWitnessWrite in swagger)
export interface CaseExpertWitnessWrite {
    expert_witness: number;
    type?: 'Defense' | 'Plaintiff';
    description?: string;
}

// Response types
export interface CaseExpertWitnessListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CaseExpertWitnessRead[];
}

// Request types
export type CaseExpertWitnessCreateRequest = CaseExpertWitnessWrite;
export type CaseExpertWitnessUpdateRequest = CaseExpertWitnessWrite;
