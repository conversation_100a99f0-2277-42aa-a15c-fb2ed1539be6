

// Common Types
type UUID = string;
type DateString = string; // ISO 8601 format
type PhoneNumber = string; // Format: XXX-XXX-XXXX
type SSN = string; // Format: XXX-XX-XXXX

// Enums
enum Gender {
    Male = "Male",
    Female = "Female",
    NonBinary = "Non-binary"
}

enum MaritalStatus {
    Single = "Single",
    Married = "Married",
    Divorced = "Divorced",
    Widowed = "Widowed",
    Separated = "Separated"
}

enum PhoneType {
    Mobile = "Mobile",
    Home = "Home",
    Work = "Work",
    Other = "Other"
}

enum UserRole {
    CaseManager = "case_manager",
    Attorney = "attorney",
    Assistant = "assistant",
    Investigator = "investigator",
    Accountant = "accountant"
}

// User Related Interfaces
interface UserDetail {
    id: number;
    email: string;
    name: string;
    roles: UserRole[];
    is_active: boolean;
    first_name?: string;
    last_name?: string;
}

// Case Workers
interface CaseWorkers {
    id: number;
    case: UUID;

    // Worker IDs
    primary_contact?: number;
    case_manager?: number;
    lead_attorney?: number;
    case_assistant?: number;
    lien_negotiator?: number;
    supervising_attorney?: number;
    intake_specialist?: number;
    investigator?: number;
    accountant?: number;
    litigation_firm?: string;
    litigation_attorney?: number;
    litigation_assistant?: number;

    // Worker Details
    primary_contact_details?: UserDetail;
    case_manager_details?: UserDetail;
    lead_attorney_details?: UserDetail;
    case_assistant_details?: UserDetail;
    lien_negotiator_details?: UserDetail;
    supervising_attorney_details?: UserDetail;
    intake_specialist_details?: UserDetail;
    investigator_details?: UserDetail;
    accountant_details?: UserDetail;
    litigation_attorney_details?: UserDetail;
    litigation_assistant_details?: UserDetail;
}

// Insurance Related
interface InsuranceAdjuster {
    id: number;
    client_insurance: number;
    bodily_injury?: AdjusterContact;
    bi_supervisor?: AdjusterContact;
    medpay_pip?: AdjusterContact;
    medpay_pip_supervisor?: AdjusterContact;
    property_damage?: AdjusterContact;
    pd_supervisor?: AdjusterContact;
    created_at: DateString;
    updated_at: DateString;
}

interface AdjusterContact {
    id: number;
    insurance_company: number;
    first_name: string;
    last_name: string;
    payee?: string;
    gender?: Gender;
    phone?: PhoneNumber;
    phone_ext?: string;
    cell?: PhoneNumber;
    fax?: PhoneNumber;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
    insurance_company_name?: string; // added for negotiation list all adjuster API response
    created_at: DateString;
    updated_at: DateString;
}

// Property Damage
interface DefendantPropertyDamage {
    id: number;
    defendant: number;
    vehicle_make?: string;
    vehicle_model?: string;
    vehicle_year?: number;
    vehicle_vin?: string;
    license_plate?: string;
    mileage?: number;
    damage_description?: string;
    insurance?: string;
    created_at: DateString;
    updated_at: DateString;
}

// API Response Types
interface ApiResponse<T> {
    data: T;
    status: number;
    message?: string;
}

interface PaginatedResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
}

export type {
    UUID,
    DateString,
    PhoneNumber,
    SSN,
    Gender,
    MaritalStatus,
    PhoneType,
    UserRole,
    UserDetail,
    CaseWorkers,
    InsuranceAdjuster,
    AdjusterContact,
    DefendantPropertyDamage,
    ApiResponse,
    PaginatedResponse
};