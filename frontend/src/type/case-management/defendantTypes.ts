import { UUID, DateString } from './commonTypes';
import { InsuranceLegalRepresentationResponse, InsuranceAdjusterRequest } from './insuranceTypes';
import { AdjusterDetailType, InsuranceCompany } from './orgTypes';

export interface DefendantCreateRequest {
    defendant_type: string;
    first_name?: string;
    last_name?: string;
    company_entity?: string | null;
    registered_agent?: string | null;
    email?: string | null;
    phone?: string | null;
    phone_ext?: string | null;  // Added
    cell?: string | null;       // Added
    fax?: string | null;        // Added
    street1?: string | null;    // Added
    street2?: string | null;    // Added
    city?: string | null;       // Added
    state?: string | null;      // Added
    zip_code?: string | null;   // Added
    ssn?: string | null;
    date_of_birth?: string | null;
    gender?: string | null;     // Added
    language?: string | null;   // Added
    race?: string | null;       // Added
    driver_license_type?: string | null; // 
    driver_license?: string | null;
    description?: string | null;
    middle_name?: string | null;
}

export interface Defendant {
    id: number;
    defendant_type: string;
    first_name: string;
    last_name: string;
    middle_name: string | null;
    company_entity: string | null;
    registered_agent: string | null;
    email: string | null;
    phone: string | null;
    phone_ext: string | null;  // Added
    cell: string | null;       // Added
    fax: string | null;        // Added
    street1: string | null;    // Added
    street2: string | null;    // Added
    city: string | null;       // Added
    state: string | null;      // Added
    zip_code: string | null;   // Added
    ssn: string | null;
    date_of_birth: DateString | null;
    gender: string | null;     // Added
    language: string | null;   // Added
    race: string | null;       // Added
    drivers_license: {         // Added
        state: string;
        number: string;
    } | null;
    case: UUID;
    description: string | null;
    driver_license_type: string | null;
    driver_license: string | null;
    created_at: string;       // Added
    updated_at: string;       // Added
}

export enum LiabilityStatus {
    ACCEPTED = "accepted",
    DENIED = "denied",
    PENDING = "pending"
}

export enum MedPayLimit {
    NA = "—",
    LIMIT_500 = "500",
    LIMIT_1000 = "1000",
    LIMIT_1000_EXCESS = "1000-excess",
    LIMIT_1500 = "1500",
    LIMIT_1500_EXCESS = "1500-excess",
    LIMIT_2000 = "2000",
    LIMIT_2000_EXCESS = "2000-excess",
    LIMIT_2500 = "2500",
    LIMIT_2500_EXCESS = "2500-excess",
    LIMIT_3000 = "3000",
    LIMIT_3000_EXCESS = "3000-excess",
    LIMIT_4000 = "4000",
    LIMIT_4000_EXCESS = "4000-excess",
    LIMIT_5000 = "5000",
    LIMIT_5000_EXCESS = "5000-excess",
    LIMIT_7500 = "7500",
    LIMIT_8000 = "8000",
    LIMIT_10000 = "10000",
    LIMIT_10000_EXCESS = "10000-excess",
    LIMIT_15000 = "15000",
    LIMIT_15000_EXCESS = "15000-excess",
    LIMIT_25000 = "25000",
    LIMIT_25000_EXCESS = "25000-excess",
    LIMIT_40000 = "40000",
    LIMIT_50000 = "50000",
    LIMIT_50000_EXCESS = "50000-excess",
    LIMIT_75000 = "75000",
    LIMIT_100000 = "100000",
    LIMIT_100000_EXCESS = "100000-excess"
}

export enum PolicyLimits {
    NA = "—",
    LIMIT_10_20 = "10k / 20k",
    LIMIT_20 = "20k",
    LIMIT_15_30 = "15k / 30k",
    LIMIT_20_40 = "20k / 40k",
    LIMIT_25_50 = "25k / 50k",
    LIMIT_25_65 = "25k / 65k",
    LIMIT_25_100 = "25k / 100k",
    LIMIT_30_60 = "30k / 60k",
    LIMIT_30_70 = "30k / 70k",
    LIMIT_30_85 = "30k / 85k",
    LIMIT_40_80 = "40k / 80k",
    LIMIT_50_100 = "50k / 100k",
    LIMIT_50_CSL = "50k CSL",
    LIMIT_60_120 = "60k / 120k",
    LIMIT_75 = "75k",
    LIMIT_75_150 = "75k / 150k",
    LIMIT_80_160 = "80k / 160k",
    LIMIT_950 = "950k",
    LIMIT_950_CSL = "950k CSL",
    LIMIT_100 = "100k",
    LIMIT_100_200 = "100k / 200k",
    LIMIT_100_300 = "100k / 300k",
    LIMIT_100_CSL = "100k CSL",
    LIMIT_125_250 = "125k / 250k",
    LIMIT_200 = "200k",
    LIMIT_200_300 = "200k / 300k",
    LIMIT_250_500 = "250k / 500k",
    LIMIT_200_CSL = "200k CSL",
    LIMIT_300 = "300k",
    LIMIT_300_CSL = "300k CSL",
    LIMIT_350 = "350k",
    LIMIT_350_CSL = "350k CSL",
    LIMIT_300_500 = "300k / 500k",
    LIMIT_400 = "400k",
    LIMIT_400_CSL = "400k CSL",
    LIMIT_500 = "500k",
    LIMIT_500_CSL = "500k CSL",
    LIMIT_500_1M = "500k / 1mil",
    LIMIT_600 = "600k",
    LIMIT_600_CSL = "600k CSL",
    LIMIT_700 = "700k",
    LIMIT_700_CSL = "700k CSL",
    LIMIT_750 = "750k",
    LIMIT_750_CSL = "750k CSL",
    LIMIT_800 = "800k",
    LIMIT_800_CSL = "800k CSL",
    LIMIT_900 = "900k",
    LIMIT_900_CSL = "900k CSL",
    LIMIT_1M = "1mil",
    LIMIT_1M_CSL = "1mil CSL",
    LIMIT_1_25M = "1.25mil",
    LIMIT_1_5M = "1.5mil",
    LIMIT_1M_1_5M = "1mil / 1.5mil",
    LIMIT_1M_2M = "1mil / 2mil",
    LIMIT_1M_3M = "1mil / 3mil",
    LIMIT_25M = "25mil",
    LIMIT_2M = "2mil",
    LIMIT_3M = "3mil",
    LIMIT_4M = "4mil",
    LIMIT_5M = "5mil",
    LIMIT_6M = "6mil",
    LIMIT_7M = "7mil",
    LIMIT_8M = "8mil",
    LIMIT_9M = "9mil",
    LIMIT_10M = "10mil",
    TBD = "TBD"
}

export enum LiabilityPercentage {
    ZERO = "0%",
    ONE = "1%",
    TWO = "2%",
    THREE = "3%",
    FOUR = "4%",
    FIVE = "5%",
    TEN = "10%",
    FIFTEEN = "15%",
    TWENTY = "20%",
    TWENTY_FIVE = "25%",
    THIRTY = "30%",
    THIRTY_FIVE = "35%",
    FORTY = "40%",
    FORTY_FIVE = "45%",
    FIFTY = "50%",
    FIFTY_FIVE = "55%",
    SIXTY = "60%",
    SIXTY_FIVE = "65%",
    SEVENTY = "70%",
    SEVENTY_FIVE = "75%",
    EIGHTY = "80%",
    EIGHTY_FIVE = "85%",
    NINETY = "90%",
    NINETY_FIVE = "95%",
    HUNDRED = "100%"
}

export interface DefendantInsuranceCreateRequest {
    defendant?: number | null;
    insurance_company: number | null;
    no_insurance: boolean | false;
    claim_number?: string | null;
    policy_number?: string | null;
    insured?: string | null;
    policy_limits?: string | null;
    liability_status?: string | null;
    liability_percentage?: string | null;
    coverage_status?: string | null;
    med_pay?: string | null;
    confirmed?: boolean | false;
    claim_note?: string | null;
    umbrella_policy?: boolean | false;
}

export type DefendantInsuranceUpdateRequest = Omit<DefendantInsuranceCreateRequest, 'defendant'>;

export interface DefendantInsurance {
    id: number;
    defendant: number;
    insurance_company: number;
    no_insurance: boolean;
    claim_number: string | null;
    policy_number: string | null;
    insured_name: string | null;
    policy_limits: string | null;
    liability_status: string | null;
    liability_percentage: string | null;
    coverage_status: string | null;
    med_pay: string | null;
    confirmed: boolean;
    claim_note: string | null;
    created_at: string;
    updated_at: string;
}

export type DefendantInsuranceResponse = DefendantInsurance;

export interface DefendantListItem extends Defendant {
    insurances: {
        id: number;
        insurance_company: {
            id: number;
            name: string;
        };
        claim_number: string | null;
        policy_number: string | null;
        policy_type: string | null;
        liability_status: string | null;
        coverage_status: string | null;
        claim_note: string | null;
        adjusters?: DefendantInsuranceAdjusterResponse[];
        legal_representation?: InsuranceLegalRepresentationResponse[];
    }[];
    law_firms: {
        id: number;
        office_name: string;
        attorneys: {
            id: number;
            first_name: string;
            last_name: string;
            email: string | null;
            phone: string | null;
        }[];
    }[];
}

export interface DefendantListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: DefendantListItem[];
}

export type DefendantDetailResponse = DefendantListItem;

export interface DefendantInsuranceListItem {
    id: number;
    claim_note: string | null;
    claim_number: string | null;
    confirmed: boolean;
    coverage_status: string | null;
    defendant: number;
    insurance_company: {
        id: number;
        name: string;
        phone: string | null;
        cell: string | null;
        fax: string | null;
        email: string | null;
        street1: string | null;
        street2: string | null;
        city: string | null;
        state: string | null;
        zip_code: string | null;
    };
    insured: string | null;
    liability_status: string | null;
    med_pay: string | null;
    no_insurance: boolean | null;
    policy_limits: string | null;
    policy_number: string | null;
    umbrella_policy: boolean | null;
    created_at: string;
    updated_at: string;
    liability_percentage: string | null;
    adjusters: DefendantInsuranceAdjusterResponse[];
    legal_representation: DefendantInsuranceLegalRepresentationResponse[] | string | null;
}

export interface DefendantInsuranceListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: DefendantInsuranceListItem[];
}

export interface DefendantInsuranceAdjusterResponse {
    bodily_injury: AdjusterDetailType | null;
    bi_supervisor: AdjusterDetailType | null;
    medpay_pip: AdjusterDetailType | null;
    medpay_pip_supervisor: AdjusterDetailType | null;
    property_damage: AdjusterDetailType | null;
    pd_supervisor: AdjusterDetailType | null;
}

export type DefendantInsuranceLegalRepresentationResponse = InsuranceLegalRepresentationResponse;

export type DefendantInsuranceAdjusterRequest = InsuranceAdjusterRequest;

export interface DefendantInsuranceLegalRepresentationRequest {
    defendant_insurance: number;
    law_firm?: number | null;
    co_counsel_law_firm?: number | null;
    attorney?: number | null;
    co_counsel_attorney?: number | null;
}

export interface CaseDefendantV2 {
    id: number;
    case: UUID;
    defendant_type: string;
    first_name: string | null;
    last_name: string | null;
    company_entity: string | null;
    registered_agent: string | null;
    email: string | null;
    phone: string | null;
    phone_ext: string | null;
    cell: string | null;
    fax: string | null;
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip_code: string | null;
    ssn: string | null;
    date_of_birth: DateString | null;
    gender: string | null;
    language: string | null;
    race: string | null;
    drivers_license: {
        state: string;
        number: string;
    } | null;
    created_at: DateString;
    updated_at: DateString;
    insurances: {
        id: number;
        insurance_company: {
            id: number;
            name: string;
        };
        claim_number: string | null;
        policy_number: string | null;
        policy_type: string | null;
        liability_status: string | null;
        coverage_status: string | null;
        claim_note: string | null;
    }[];
    law_firms: {
        id: number;
        office_name: string;
        attorneys: {
            id: number;
            first_name: string;
            last_name: string;
            email: string | null;
            phone: string | null;
        }[];
    }[];
}

export interface DefendantPropertyDamage {
    id: number;
    damage: "Major" | "Minor" | "Moderate" | null;
    frame_damage: boolean;
    total_loss: boolean;
    registered_owner: string | null;
    auto_body_shop: string | null;
    estimate: string | null;
    final: string | null;
    make: string | null;
    model: string | null;
    color: string | null;
    year: string | null;
    plate: string | null;
    vin: string | null;
    mileage: string | null;
    note: string | null;
    created_at: string;
    updated_at: string;
    insurance_company: InsuranceCompany | null;
}

export interface DefendantPropertyDamageCreateRequest {
    damage: string | null;
    frame_damage: boolean;
    total_loss: boolean;
    registered_owner: string | null;
    auto_body_shop: string | null;
    estimate: string | null;
    final: string | null;
    make: string | null;
    model: string | null;
    color: string | null;
    year: string | null;
    plate: string | null;
    vin: string | null;
    mileage: string | null;
    note: string | null;
    insurance_company: string | null;
}

export type DefendantPropertyDamageUpdateRequest = DefendantPropertyDamageCreateRequest;

export interface DefendantPropertyDamageResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: DefendantPropertyDamage[];
}

export enum CoverageStatus {
    ACCEPTED = "accepted",
    DENIED = "denied",
    PENDING = "pending"
}

// Add this type for update requests
export type DefendantUpdateRequest = Omit<DefendantCreateRequest, 'case'>;

// Update the existing DefendantResponse if needed
export interface DefendantResponse extends Defendant {
    created_at: string;
    updated_at: string;
}

export enum DefendantType {
    COMMERCIAL = "COMMERCIAL",
    FED_GOV_ENTITY = "FED_GOV_ENTITY",
    PRIVATE_PARTY = "PRIVATE_PARTY",
    PUBLIC_ENTITY = "PUBLIC_ENTITY"
}

export interface DefendantSyncRequest {
    source_defendant_id: number;
    target_case_ids: string[];
    sync_options?: {
        sync_insurances?: boolean;
        sync_property_damage?: boolean;
        sync_legal_representation?: boolean;
        sync_adjusters?: boolean;
    };
}

export interface DefendantSyncResponse {
    success: boolean;
    message?: string;
}

