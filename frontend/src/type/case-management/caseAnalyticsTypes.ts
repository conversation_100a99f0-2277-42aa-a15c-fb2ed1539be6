// Base types for common fields
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface CaseSummary {
  total_cases: number;
  average_case_age: number;
  touched_last_7_days: number;
  aged_over_1_year: number;
}

export interface CaseWorker {
  id: string;
  name: string;
  role: string;
}

export interface CaseData {
  id: string;
  days: number;
  case_grade?: string;
  case_grade_reason?: string;
  case_status: string;
  case_type?: string;
  pd?: number;
  pd_estimate?: number;
  medical_bills?: number;
  initial_demand?: number;
  last_offer?: number;
  primary?: string;
  lead_attorney?: string;
  case_manager?: string;
  supervising_attorney?: string;
  status: string;
  language?: string;
  last_touched: string;
  client_comm?: string;
  file_number?: string;
  rating?: number;
  team: CaseWorker[];
}

export interface CaseListResponse {
  summary: CaseSummary;
  results: CaseData[];
}

// Case Status Report
export interface CaseStatusSummary {
  longest_average_status: {
    status: string | null;
    days: number;
  };
  shortest_average_status: {
    status: string | null;
    days: number;
  };
}

export interface CaseStatusDetail {
  case_status: string;
  case_count: number;
  average_days: number;
  display_name: string;
}

export interface CaseStatusResponse {
  summary: CaseStatusSummary;
  status_details: CaseStatusDetail[];
  results: CaseStatusDetail[];
}

// Statistics Report
export interface CaseStatistics {
  total: number;
  new_sign_ups: number;
  active_cases: number;
  referrals: number;
  dropped: number;
  lawsuits_filed: number;
  settlements: number;
  settlement_total: number;
  outstanding_settlements_total: number;
  attorney_liens: number;
  attorney_liens_total: number;
  date_range: {
    from: string;
    to: string;
  };
  results: Record<string, unknown>[];
}

// Check Deposits Report
export interface PaymentSummary {
  total_amount: number;
  total_payments: number;
}

export interface PaymentData {
  case: {
    id: string;
    number: string;
    url: string;
  };
  deposited: string | null;
  check_number: string | null;
  check_type: string | null;
  issuer_payee: string | null;
  lead_attorney: string | null;
  case_manager: string | null;
  amount: number;
  payment_type: string;
  status: string;
  requested_date: string | null;
}

export interface CheckDepositsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Array<{
    id: string;
    amount: number;
    check_type: string;
    deposited: string | null;
    requested_date: string | null;
    lead_attorney: string | null;
    case_manager: string | null;
  }>;
  summary: PaymentSummary;
}

// Clients Report
export interface ClientSummary {
  total_clients: number;
  birthdays_this_month: number;
}

export interface ClientData {
  lastname: string | null;
  firstname: string | null;
  dob: string | null;
  age: number | null;
  intake_date: string | null;
  case: {
    id: string;
    number: string;
    url: string;
  };
  case_type: string | null;
  case_status: string;
  email: string | null;
  language: string | null;
  source_type: string | null;
  source_detail: string | null;
  is_deceased: boolean;
  has_email: boolean;
}

export interface ClientsResponse {
  summary: ClientSummary;
  results: ClientData[];
}

// Defendant Types Report
export interface DefendantTypeSummary {
  public_entity: number;
  fed_gov_entity: number;
  commercial: number;
  private_party: number;
}

export interface DefendantData {
  case: {
    id: string;
    number: string;
    url: string;
  };
  defendant: string;
  defendant_type: string;
  case_type: string | null;
  case_status: string;
  statute: string | null;
  insurance_1: string | null;
  lead_attorney: string | null;
  case_manager: string | null;
}

export interface DefendantTypesResponse {
  summary: DefendantTypeSummary;
  results: DefendantData[];
}

// Health Providers Report
export interface HealthProviderSummary {
  total_providers: number;
  accepts_liens: number;
  no_records_service: number;
  do_not_use: number;
  by_specialty: Record<string, number>;
}

export interface ProviderContact {
  type: string;
  name: string;
  payee: string;
  phone: string | null;
  phone_ext: string | null;
  cell: string | null;
  fax: string | null;
  email: string | null;
  website: string | null;
  address: {
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip_code: string | null;
  };
  tax_id: string | null;
}

export interface HealthProviderData {
  office_name: string;
  specialties: string | null;
  city: string | null;
  office_phone: string | null;
  open_cases: number;
  total_cases: number;
  original_amount: number;
  final_amount: number;
  avg_reduction: number;
  accepts_liens: boolean;
  no_records_service: boolean;
  do_not_use: boolean;
  contacts: ProviderContact[];
  note: string | null;
  created_at: string;
  updated_at: string;
}

export interface HealthProvidersResponse {
  summary: HealthProviderSummary;
  results: HealthProviderData[];
}

// Insurance Adjusters Report
export interface AdjusterSummary {
  total_adjusters: number;
  by_company: Record<string, number>;
}

export interface AdjusterContactInfo {
  phone: string | null;
  phone_ext: string | null;
  cell: string | null;
  fax: string | null;
  email: string | null;
  website: string | null;
  address: {
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip_code: string | null;
  };
}

export interface AdjusterData {
  name: string;
  company: string;
  phone: string | null;
  email: string | null;
  open_cases: number;
  total_cases: number;
  contact_info: AdjusterContactInfo;
  personal_info: {
    gender: string | null;
    payee: string | null;
    tax_id: string | null;
  };
  note: string | null;
  created_at: string;
  updated_at: string;
  insurance_company_info: {
    id: string;
    name: string;
    type: string | null;
    phone: string | null;
    email: string | null;
    website: string | null;
  };
  roles: {
    bodily_injury: number;
    bi_supervisor: number;
    medpay_pip: number;
    medpay_pip_supervisor: number;
    property_damage: number;
    pd_supervisor: number;
  };
}

export interface InsuranceAdjustersResponse {
  summary: AdjusterSummary;
  results: AdjusterData[];
}

// Insurance Companies Report
export interface InsuranceCompanySummary {
  total_companies: number;
  by_type: Record<string, number>;
  by_insurance_type: {
    plaintiff: number;
    defendant: number;
  };
}

export interface InsuranceCompanyData {
  company: string;
  type: string | null;
  open_cases: number;
  total_cases: number;
  total_medical: number;
  total_settlements: number;
  contact_info: {
    phone: string | null;
    phone_ext: string | null;
    fax: string | null;
    email: string | null;
    website: string | null;
    address: {
      street1: string | null;
      street2: string | null;
      city: string | null;
      state: string | null;
      zip_code: string | null;
    };
  };
  adjusters: Array<{
    id: string;
    name: string;
    phone: string | null;
    email: string | null;
  }>;
  payee: string | null;
  tax_id: string | null;
  note: string | null;
  created_at: string;
  updated_at: string;
  insurance_types: {
    plaintiff: number;
    defendant: number;
  };
  primary_insurance_type: 'plaintiff' | 'defendant' | 'both';
}

export interface InsuranceCompaniesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: InsuranceCompanyData[];
  summary: InsuranceCompanySummary;
}

// Lien Holders Report
export interface LienHolderSummary {
  total: number;
  by_type: Record<string, LienStats>;
}

export interface LienHolderCaseData {
  case_id: string;
  office_name: string | null;
  lien_negotiator: string | null;
  case_status: string;
  city: string | null;
  office_phone: string | null;
  open_cases: number;
  total_cases: number;
  amount: number;
}

export interface LienHolderData {
  name: string;
  company: string;
  phone: string | null;
  email: string | null;
  open_cases: number;
  total_cases: number;
  lien_stats?: LienStats;
  amounts?: CaseAmounts;
}

export interface LienHoldersResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: LienHolderData[];
  summary: LienHoldersSummary;
}

export interface StatisticsResponse {
  summary: StatisticsSummary;
  filter_options: CaseReportFilters;
  tiles?: ReportTile[];
}
// Add these new types for filter options
export interface FilterOption {
  value: string;
  label: string;
}

export interface WorkerOption {
  id: string | number;
  name: string;
  role?: string;
}

// First define specific filter option types for each report type
export interface BaseFilterOptions {
  date_fields?: FilterOption[];
  case_types?: FilterOption[];
  case_managers?: WorkerOption[];
  lead_attorneys?: WorkerOption[];
  case_statuses?: FilterOption[];
  organization_statuses?: FilterOption[];
  show_attorney_fees?: FilterOption[];
  open_cases_checkbox?: FilterOption[];
  range_filters?: ValueRangeFilter[];
}

export interface CaseListFilterOptions extends BaseFilterOptions {
  status_categories?: FilterOption[];
  supervising_attorneys?: WorkerOption[];
  workers?: WorkerOption[];
  source_types?: FilterOption[];
  source_details?: FilterOption[];
  case_ages?: FilterOption[];
  role_options?: WorkerOption[];
}

export interface CheckDepositsFilterOptions extends BaseFilterOptions {
  check_types?: FilterOption[];
}

export interface ClientsFilterOptions extends BaseFilterOptions {
  case_types?: FilterOption[];
  organization_statuses?: FilterOption[];
  source_types?: FilterOption[];
  languages?: FilterOption[];
  birthday_months?: FilterOption[];
}

export interface DefendantTypesFilterOptions extends BaseFilterOptions {
  case_types?: FilterOption[];
  case_statuses?: FilterOption[];
  defendant_types?: FilterOption[];
  case_managers?: WorkerOption[];
  lead_attorneys?: WorkerOption[];
}

export interface HealthProvidersFilterOptions extends BaseFilterOptions {
  specialties?: FilterOption[];
  cities?: FilterOption[];
  status_filters?: FilterOption[];
}

export interface InsuranceAdjustersFilterOptions extends BaseFilterOptions {
  insurance_companies?: WorkerOption[];
}

export interface InsuranceCompaniesFilterOptions extends BaseFilterOptions {
  insurance_types?: FilterOption[];
  company_types?: FilterOption[];
}

export interface LienHoldersFilterOptions extends BaseFilterOptions {
  lien_status?: FilterOption[];
}

// Change to use interface merging approach instead of extending multiple interfaces
export interface SettlementManagementFilterOptions extends BaseFilterOptions {
  case_managers?: WorkerOption[];
  lead_attorneys?: WorkerOption[];
  case_status_categories?: FilterOption[];
}

export interface LitigationManagementFilterOptions extends BaseFilterOptions {
  assigned_workers?: WorkerOption[];
  event_types?: FilterOption[];
  status_options?: FilterOption[];
}

// Use a simpler approach with a record type
export interface FilterOptions extends BaseFilterOptions {
  // Common filter options
  date_fields?: FilterOption[];
  case_types?: FilterOption[];
  case_managers?: WorkerOption[];
  lead_attorneys?: WorkerOption[];
  open_cases_checkbox?: FilterOption[];

  // Case list specific
  supervising_attorneys?: WorkerOption[];
  workers?: WorkerOption[];
  teams?: WorkerOption[];
  source_types?: FilterOption[];
  source_details?: FilterOption[];
  case_ages?: FilterOption[];

  // Check deposits specific
  check_types?: FilterOption[];

  // Clients specific
  organization_statuses?: FilterOption[];
  languages?: FilterOption[];
  birthday_months?: FilterOption[];

  // Defendant types specific
  case_statuses?: FilterOption[];
  defendant_types?: FilterOption[];

  // Health providers specific
  specialties?: FilterOption[];
  cities?: FilterOption[];
  status_filters?: FilterOption[];

  // Insurance adjusters specific
  insurance_companies?: WorkerOption[];

  // Insurance companies specific
  insurance_types?: FilterOption[];
  company_types?: FilterOption[];

  // Lien holders specific
  lien_status?: FilterOption[];

  // Medpay requests specific

  // Notes specific
  tags?: FilterOption[];

  // Source performance specific
  view_by?: FilterOption[];
  quick_filters?: FilterOption[];

  // Medical requests specific
  treatment_statuses?: FilterOption[];
  request_types?: FilterOption[];
  request_statuses?: FilterOption[];

  // Statute specific
  incident_states?: FilterOption[];

  // Demands specific
  demand_types?: FilterOption[];
  case_assistants?: WorkerOption[];

  // Settlement specific
  lien_negotiators?: WorkerOption[];

  // Settlement management specific

  // Litigation management specific
  assigned_workers?: WorkerOption[];
  event_types?: FilterOption[];
  status_options?: FilterOption[];

  // Client Trust specific
  cases?: FilterOption[];
  deposit_types?: FilterOption[];
  client_trust_entry_types?: FilterOption[];

  role_options?: WorkerOption[];

  show_attorney_fees?: FilterOption[];

  estimated_value_range?: ValueRangeFilter[];
  

  // Generic index signature for any other filter options
  [key: string]: FilterOption[] | WorkerOption[] | ValueRangeFilter[] | undefined;
}

// Type map for filter options based on report type
export type FilterOptionsTypeMap = {
  caseList: CaseListFilterOptions;
  caseListClosed: CaseListFilterOptions;
  caseListRejected: CaseListFilterOptions;
  caseListDropped: CaseListFilterOptions;
  caseStatus: CaseListFilterOptions;
  statistics: BaseFilterOptions;
  checkDeposits: CheckDepositsFilterOptions;
  clients: ClientsFilterOptions;
  clientsClosed: ClientsFilterOptions;
  clientsRejected: ClientsFilterOptions;
  clientsOpen: ClientsFilterOptions;
  clientsReferredIn: ClientsFilterOptions;
  clientsSubbedOut: ClientsFilterOptions;
  clientsIntakeReady: ClientsFilterOptions;
  defendantTypes: DefendantTypesFilterOptions;
  healthProviders: HealthProvidersFilterOptions;
  insuranceAdjusters: InsuranceAdjustersFilterOptions;
  insuranceCompanies: InsuranceCompaniesFilterOptions;
  lienHolders: LienHoldersFilterOptions;
  medpayRequests: MedpayRequestsFilterOptions;
  notes: NotesFilterOptions;
  sourcePerformance: SourcePerformanceFilterOptions;
  medicalRequests: MedicalRequestsFilterOptions;
  statute: StatuteFilterOptions;
  demands: DemandsFilterOptions;
  settlement: SettlementFilterOptions;
  settlementManagement: SettlementManagementFilterOptions;
  litigationManagement: LitigationManagementFilterOptions;
  clientTrust: ClientTrustFilterOptions;
  clientTrustAttorneyFees: ClientTrustFilterOptions;
  clientTrustIssuedPayments: ClientTrustFilterOptions;
  clientTrustCheckDeposits: ClientTrustFilterOptions;
  clientTrustConsolidated: ClientTrustFilterOptions;
  caseReview: CaseReviewFilterOptions;
  caseCosts: CaseCostFilterOptions;
  contacts: ContactsFilterOptions;
  caseEventsCalendar: CaseEventsCalendarFilterOptions;
  estimatedValue: EstimatedValueFilterOptions;
  medpayDeposits: MedpayDepositsFilterOptions;
};

// Update CaseReportFilters to include all possible filter keys
export interface CaseReportFilters {
  view?: 'my_cases' | 'all_cases';
  page?: number;
  from_date?: string;
  to_date?: string;
  search?: string;
  case_type?: string;
  case_status?: string;
  status_category?: string;
  case_manager?: string;
  lead_attorney?: string;
  supervising_attorney?: string;
  worker?: string;
  team?: string;
  source_type?: string;
  source_detail?: string;
  case_age?: string;
  date_field?: string;
  // Add missing filter keys
  check_type?: string;
  organization_status?: string;
  language?: string;
  birthday_month?: string;
  defendant_type?: string;
  specialty?: string;
  city?: string;
  insurance_company?: string;
  insurance_type?: string;
  company_type?: string;
  lien_status?: string;
  // New filters for medpay requests
  include_auto_notes?: boolean;
  directed_to?: string;
  // New filters for source performance
  view_by?: string;
  quick_filter?: string;
  // New filters for medical requests
  treatment_status?: string;
  request_type?: string;
  // New filters for statute
  incident_state?: string;
  // New filters for demands
  demand_type?: string;
  case_assistant?: string;
  // New filters for settlement
  accepted_from?: string;
  accepted_to?: string;
  lien_negotiator?: string;
  // New filters for settlement management and litigation management
  assigned_worker?: string;
  event_type?: string;
  status?: string;
  case_status_category?: string;
  tile?: string;
  // Client Trust filters
  case_id?: string;
  is_deposit?: string;
  date_from?: string;
  date_to?: string;
  amount_min?: string;
  amount_max?: string;
  // Case costs specific filters
  payment_type?: string;
  priority?: string;
  section?: string;
  cost_type?: string;
  contact_type?: string;
  litigation_event_status?: string;
  // Add new filter fields for estimated value report
  calculated_value_min?: string;
  calculated_value_max?: string;
  estimated_value_min?: string;
  estimated_value_max?: string;
  min?: number;
  max?: number;
  label?: string;
  [key: string]: string | number | undefined | boolean;  // Add index signature
}

// Update ReportType to include new report types
export type ReportType =
  | "caseList"
  | "caseListClosed"
  | "caseListRejected"
  | "caseListDropped"
  | "caseStatus"
  | "statistics"
  | "checkDeposits"
  | "clients"
  | "clientsClosed"
  | "clientsRejected"
  | "clientsOpen"
  | "clientsReferredIn"
  | "clientsSubbedOut"
  | "clientsIntakeReady"
  | "clientsRetained"
  | "defendantTypes"
  | "healthProviders"
  | "insuranceAdjusters"
  | "insuranceCompanies"
  | "lienHolders"
  | "medpayRequests"
  | "medpayDeposits"
  | "notes"
  | "sourcePerformance"
  | "medicalRequests"
  | "statute"
  | "demands"
  | "settlement"
  | "settlementManagement"
  | "litigationManagement"
  | "clientTrust"
  | "clientTrustAttorneyFees"
  | "clientTrustIssuedPayments"
  | "clientTrustCheckDeposits"
  | "clientTrustConsolidated"
  | "caseReview"
  | "caseCosts"
  | "contacts"
  | "caseEventsCalendar"
  | "estimatedValue";

// Add these new interfaces to handle the data structures
export interface ContactAddress {
  city: string | null;
  state: string | null;
  zip_code: string | null;
}

export interface Contact {
  type: string;
  name: string;
  phone: string | null;
  email: string | null;
  address: ContactAddress;
}

export interface LienStats {
  signed: number;
  filed: number;
  total: number;
}

export interface CaseAmounts {
  original: number;
  adjusted: number;
}

export interface CaseCounts {
  open: number;
  total: number;
}

// Base summary interfaces for each report type
export interface CaseListSummary {
  total_cases: number;
  average_case_age: number;
  touched_last_7_days: number;
  touched_recently_7_days_count: number;
  touched_last_7_days_percentage: number;
  aged_over_1_year: number;
}

export interface StatisticsSummary {
  total: number;
  new_sign_ups: number;
  active_cases: number;
  settlements: number;
  settlement_total: number;
  lawsuits_filed: number;
  referrals: number;
  dropped: number;
  attorney_liens_count: number;
  attorney_liens_total: number;
  outstanding_settlements: number;
}

export interface CheckDepositsSummary extends PaymentSummary {
  checks: number;
  total: number;
}

export interface ClientsSummary {
    total_clients: number;
    birthdays_this_month: number;
}

export interface DefendantTypesSummary {
    total_defendants: number;
      public_entity: number;
      fed_gov_entity: number;
      commercial: number;
      private_party: number;
}

export interface HealthProvidersSummary {
    total_providers: number;
    accepts_liens: number;
    no_records_service: number;
    do_not_use: number;
    by_specialty: Record<string, number>;
}

export interface InsuranceAdjustersSummary {
    total_adjusters: number;
    by_company: Record<string, number>;
}

export interface InsuranceCompaniesSummary {
    total_companies: number;
    by_type: {
      client: number;
      defendant: number;
    };
}

export interface LienHoldersSummary {
    total_lien_holders: number;
    total_health_liens: number;
}

// Add new summary types for the new reports
export interface MedpayRequestsSummary {
  medpay_requests: number;
  total_demanded: number;
}

export interface NotesSummary {
  total_notes: number;
  by_tags: Record<string, number>;
}

export interface SourcePerformanceSummary {
  total_leads: number;
  total_retained: number;
  retention_rate: number;
  total_settlements: number;
  total_fees: number;
  total_settled_cases: number;
}

export interface MedicalRequestsSummary {
  total_requests: number;
  billing_requests: number;
  records_requests: number;
}

export interface StatuteSummary {
  next_7_days: number;
  next_30_days: number;
  next_60_days: number;
  next_90_days: number;
  total: number;
}

export interface DemandsSummary {
  demands_sent: number;
  total_demanded: number;
  third_party_demands: number;
  um_uim_demands: number;
  expired: number;
}

export interface SettlementSummary {
  total: number;
  offers: number;
  avg_per_case: number;
}

// Update ReportSummary union type to include new summary types
export type ReportSummary =
  | CaseListSummary
  | CaseStatusSummary
  | StatisticsSummary
  | CheckDepositsSummary
  | ClientsSummary
  | DefendantTypesSummary
  | HealthProvidersSummary
  | InsuranceAdjustersSummary
  | InsuranceCompaniesSummary
  | LienHoldersSummary
  | MedpayRequestsSummary
  | MedpayDepositsSummary
  | NotesSummary
  | SourcePerformanceSummary
  | MedicalRequestsSummary
  | StatuteSummary
  | DemandsSummary
  | SettlementSummary
  | SettlementManagementSummary
  | LitigationManagementSummary
  | ClientTrustSummary
  | ClientTrustConsolidatedSummary
  | StatisticsSummary
  | CaseReviewSummary
  | CaseCostSummary
  | ContactsSummary
  | CaseEventsCalendarSummary
  | EstimatedValueSummary;


// Update SummaryTilesProps to use the new types
export interface SummaryTilesProps {
  data: ReportSummary;
  reportType: ReportType;
  onTileClick?: (filterValue: string) => void;
}

// Helper type to get the correct summary type for a given report type
export type SummaryTypeMap = {
  caseList: CaseListSummary;
  caseListClosed: CaseListSummary;
  caseListRejected: CaseListSummary;
  caseListDropped: CaseListSummary;
  caseStatus: CaseStatusSummary;
  statistics: StatisticsSummary;
  checkDeposits: CheckDepositsSummary;
  clients: ClientsSummary;
  clientsClosed: ClientsSummary;
  clientsRejected: ClientsSummary;
  clientsOpen: ClientsSummary;
  clientsReferredIn: ClientsSummary;
  clientsSubbedOut: ClientsSummary;
  clientsIntakeReady: ClientsSummary;
  clientsRetained: ClientsSummary;
  defendantTypes: DefendantTypesSummary;
  healthProviders: HealthProvidersSummary;
  insuranceAdjusters: InsuranceAdjustersSummary;
  insuranceCompanies: InsuranceCompaniesSummary;
  lienHolders: LienHoldersSummary;
  medpayRequests: MedpayRequestsSummary;
  medpayDeposits: MedpayDepositsSummary;
  notes: NotesSummary;
  sourcePerformance: SourcePerformanceSummary;
  medicalRequests: MedicalRequestsSummary;
  statute: StatuteSummary;
  demands: DemandsSummary;
  settlement: SettlementSummary;
  settlementManagement: SettlementManagementSummary;
  litigationManagement: LitigationManagementSummary;
  clientTrust: ClientTrustSummary;
  clientTrustAttorneyFees: ClientTrustSummary;
  clientTrustIssuedPayments: ClientTrustSummary;
  clientTrustCheckDeposits: ClientTrustSummary;
  clientTrustConsolidated: ClientTrustConsolidatedSummary;
  caseReview: CaseReviewSummary;
  caseCosts: CaseCostSummary;
  contacts: ContactsSummary;
  caseEventsCalendar: CaseEventsCalendarSummary;
  estimatedValue: EstimatedValueSummary;
};

// Type guard functions to check summary types
export const isCaseListSummary = (summary: ReportSummary): summary is CaseListSummary => {
  return 'total_cases' in summary;
};

export const isCaseStatusSummary = (summary: ReportSummary): summary is CaseStatusSummary => {
  return 'longest_average_status' in summary;
};

// First, let's define a CaseCounts type for consistency
export interface CaseCounts {
  open: number;
  total: number;
}

// First, update BaseTableRow to be more specific about team and contacts properties
export interface ContactRelatedCase {
  id: string;
  client_name: string;
  incident_date: string | null;
  organization_status: string | null;
}

export interface ContactAddress {
  street1: string | null;
  street2: string | null;
  city: string | null;
  state: string | null;
  zip_code: string | null;
}

// Update the BaseTableRow interface to support the new types
export interface BaseTableRow {
  [key: string]:
    | string
    | number
    | boolean
    | null
    | undefined
    | Contact[]
    | CaseEventWorker[]  // Add this new type
    | LienStats
    | CaseAmounts
    | CaseCounts
    | CaseWorker[]
    | ContactRelatedCase[]
    | ContactAddress
    | {
        id: number;
        name: string;
        role: string;
      }[];
}

// Update the specific row interfaces to explicitly type these properties
export interface CaseListRow extends BaseTableRow {
  id: string;
  days: number;
  case_grade?: string;
  case_status: string;
  case_type?: string;
  primary?: string;
  lead_attorney?: string;
  case_manager?: string;
  status: string;
  last_touched: string;
  team: CaseWorker[];  // Explicitly type team property
}

export interface HealthProviderRow extends BaseTableRow {
  office_name: string;
  specialties: string;
  city: string;
  office_phone: string;
  open_cases: number;
  total_cases: number;
  original_amount: number | string;
  final_amount: number | string;
  avg_reduction: number | string;
  accepts_liens: boolean;
  no_records_service: boolean;
  do_not_use: boolean;
  contacts: Contact[] | string;  // Explicitly type contacts property
}

export interface InsuranceAdjusterRow extends BaseTableRow {
  name: string;
  company: string;
  phone: string;
  email: string | null;
  open_cases: number;
  total_cases: number;
}

export interface InsuranceCompanyRow extends BaseTableRow {
  name: string;
  type: string;
  phone: string;
  email: string | null;
  total_adjusters: number;
  defendant_cases: CaseCounts;  // Use CaseCounts interface
  client_cases: CaseCounts;     // Use CaseCounts interface
}

export interface LienHolderRow extends BaseTableRow {
  name: string;
  company: string;
  phone: string;
  email: string;
  open_cases: number;
  total_cases: number;
  lien_stats: LienStats;
  amounts: CaseAmounts;
}

// Define the defendant types row interface
export interface DefendantTypeRow extends BaseTableRow {
  case_id: string;
  case: string;
  defendant: string;
  defendant_type: string;
  case_type: string;
  case_status: string;
  statute: string | null;
  insurance_1: string | null;
}

// Update the TableRowTypeMap to use DefendantTypeRow
export type TableRowTypeMap = {
  caseList: CaseListRow;
  caseListClosed: CaseListRow;
  caseListRejected: CaseListRow;
  caseListDropped: CaseListRow;
  caseStatus: CaseListRow;
  statistics: never;  // Statistics report doesn't have a table
  checkDeposits: BaseTableRow;
  clients: BaseTableRow;
  clientsClosed: BaseTableRow;
  clientsRejected: BaseTableRow;
  clientsOpen: BaseTableRow;
  clientsReferredIn: BaseTableRow;
  clientsSubbedOut: BaseTableRow;
  clientsIntakeReady: BaseTableRow;
  clientsRetained: BaseTableRow;
  defendantTypes: DefendantTypeRow;
  healthProviders: HealthProviderRow;
  insuranceAdjusters: InsuranceAdjusterRow;
  insuranceCompanies: InsuranceCompanyRow;
  lienHolders: LienHolderRow;
  medpayRequests: MedpayRequestRow;
  medpayDeposits: MedpayDepositsRow;
  notes: NoteRow;
  sourcePerformance: SourcePerformanceRow;
  medicalRequests: MedicalRequestRow;
  statute: StatuteRow;
  demands: DemandRow;
  settlement: SettlementRow;
  settlementManagement: SettlementManagementRow;
  litigationManagement: LitigationManagementRow;
  clientTrust: ClientTrustRow;
  clientTrustAttorneyFees: ClientTrustRow;
  clientTrustIssuedPayments: ClientTrustRow;
  clientTrustCheckDeposits: ClientTrustRow;
  clientTrustConsolidated: ClientTrustConsolidatedRow;
  caseReview: CaseReviewRow;
  caseCosts: CaseCostRow;
  contacts: ContactData;
  caseEventsCalendar: CaseEventsCalendarRow;
  estimatedValue: EstimatedValueRow;
};

// Generic type for table data based on report type
export type TableData<T extends ReportType> = T extends keyof TableRowTypeMap
  ? TableRowTypeMap[T][]
  : BaseTableRow[];

// Update DynamicTableProps to use the generic type
export interface DynamicTableProps {
  data: TableData<ReportType>;
  reportType: ReportType;
}

// Type guard functions for table row data
export const isHealthProviderRow = (row: BaseTableRow): row is HealthProviderRow => {
  return 'office_name' in row && 'specialties' in row;
};

export const isInsuranceAdjusterRow = (row: BaseTableRow): row is InsuranceAdjusterRow => {
  return 'name' in row && 'company' in row && 'total_cases' in row;
};

export const isInsuranceCompanyRow = (row: BaseTableRow): row is InsuranceCompanyRow => {
  return 'name' in row && 'type' in row && 'total_adjusters' in row;
};

export const isLienHolderRow = (row: BaseTableRow): row is LienHolderRow => {
  return 'name' in row && 'company' in row && 'lien_stats' in row;
};

// Add type guard for DefendantTypeRow
export const isDefendantTypeRow = (row: BaseTableRow): row is DefendantTypeRow => {
  return 'defendant_type' in row && 'case_id' in row;
};

// Add type guard for DefendantTypesSummary
export const isDefendantTypesSummary = (summary: ReportSummary): summary is DefendantTypesSummary => {
  return 'total_defendants' in summary && 'by_type' in summary;
};

// Add type conversion helpers
export const convertInsuranceCompanySummary = (summary: InsuranceCompanySummary): InsuranceCompaniesSummary => {
  return {
    total_companies: summary.total_companies,
    by_type: {
      client: summary.by_type['client'] || 0,
      defendant: summary.by_type['defendant'] || 0
    }
  };
};

// Add new row types for the new reports
export interface MedpayRequestRow extends BaseTableRow {
  id: string;
  case_id: string;
  case_name: string;
  incident_date: string | null;
  date_sent: string;
  insurance: string | null;
  medpay_limits: string | null | number;
  total_meds: number | string;
  costs: number | string;
  case_manager: string | null;
  lead_attorney: string | null;
  requested: number | string;
}

export interface NoteRow extends BaseTableRow {
  id: string;
  case: string;
  note: string;
  date: string;
  tags: string;
  added_by: string | null;
  directed_to: string | null;
}

export interface SourcePerformanceRow extends BaseTableRow {
  source_detail: string;
  source_type: string;
  cost_per_month: number;
  leads: number;
  retained: number;
  conversion: number;
  total_settlements: number;
  total_fees: number;
  settled_cases: number;
  fees_per_case: number;
}

export interface MedicalRequestRow extends BaseTableRow {
  id: string;
  case: string;
  case_status: string;
  medical_provider: string | null;
  request_type: string | null;
  status: string | null;
  request_date: string;
  treatment_status: string;
  lead_attorney: string | null;
}

export interface StatuteRow extends BaseTableRow {
  id: string;
  case: string;
  case_type: string | null;
  case_status: string;
  lead_attorney: string | null;
  case_manager: string | null;
  incident_state: string | null;
  statute: string | null;
  days_left: number | null;
  doi: string | null;
}

export interface DemandRow extends BaseTableRow {
  id: string;
  case: string;
  date_sent: string;
  date_expires: string | null;
  type: string;
  insurance: string | null;
  policy: string | null;
  total_meds: number;
  costs: number;
  amount: number;
  offer: number;
}

export interface SettlementRow extends BaseTableRow {
  id: string;
  case: string;
  case_type: string;
  case_status: string;
  accepted_date: string;
  offer_status: string;
  fees: number;
  accepted_offer: number;
  initial_offer: number;
  net_to_client: number;
  medical_bills: number;
  liens: number;
  advanced_costs: number;
}

// Add new filter option types for the new reports
export interface MedpayRequestsFilterOptions extends BaseFilterOptions {
  // Additional filter options specific to medpay requests
  insurance_companies: WorkerOption[];
}

export interface NotesFilterOptions extends BaseFilterOptions {
  workers: WorkerOption[];
  organization_statuses: FilterOption[];
  tags: FilterOption[];
}

export interface SourcePerformanceFilterOptions extends BaseFilterOptions {
  source_details: FilterOption[];
  view_by: FilterOption[];
  quick_filters: FilterOption[];
  source_types: FilterOption[];
}

export interface MedicalRequestsFilterOptions extends BaseFilterOptions {
  specialties: FilterOption[];
  treatment_statuses: FilterOption[];
  request_types: FilterOption[];
  request_statuses: FilterOption[];
}

export interface StatuteFilterOptions extends BaseFilterOptions {
  incident_states: FilterOption[];
}

export interface DemandsFilterOptions extends BaseFilterOptions {
  demand_types: FilterOption[];
  case_assistants: WorkerOption[];
}

export interface SettlementFilterOptions extends BaseFilterOptions {
  lien_negotiators: WorkerOption[];
}

// Add response types for the new API endpoints
export interface MedpayRequestsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: MedpayRequestRow[];
  summary: MedpayRequestsSummary;
  filter_options: MedpayRequestsFilterOptions;
}

export interface NotesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: NoteRow[];
  summary: NotesSummary;
  filter_options: NotesFilterOptions;
}

export interface SourcePerformanceResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SourcePerformanceRow[];
  summary: SourcePerformanceSummary;
  filter_options: SourcePerformanceFilterOptions;
  tiles?: ReportTile[];
}

export interface MedicalRequestsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: MedicalRequestRow[];
  summary: MedicalRequestsSummary;
  filter_options: MedicalRequestsFilterOptions;
}

export interface StatuteResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: StatuteRow[];
  summary: StatuteSummary;
  filter_options: StatuteFilterOptions;
}

export interface DemandsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: DemandRow[];
  summary: DemandsSummary;
  filter_options: DemandsFilterOptions;
}

export interface SettlementResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SettlementRow[];
  summary: SettlementSummary;
  filter_options: SettlementFilterOptions;
}

// Settlement Management Report
export interface SettlementManagementSummary {
  demands: {
    total: number;
    count: number;
    fees: number;
  };
  offers: {
    total: number;
    count: number;
    fees: number;
  };
  issues: {
    total: number;
    count: number;
    fees: number;
  };
  pending: {
    total: number;
    count: number;
    fees: number;
  };
  checks_this_month: {
    total: number;
    count: number;
    fees: number;
  };
  checks_this_year: {
    total: number;
    count: number;
    fees: number;
  };
}

export interface SettlementManagementRow extends BaseTableRow {
  id: string;
  case: string;
  expiration: string;
  type: string;
  insurance: string;
  policy: string;
  total_meds: number;
  note: string | null;
  amount: number;
  est_fees: number;
  mri_status: string;
  ttd_status: string;
  injection_status: string;
}

export interface SettlementManagementResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SettlementManagementRow[];
  summary: SettlementManagementSummary;
  filter_options: SettlementManagementFilterOptions;
  tiles?: ReportTile[];
}

// Litigation Management Report
export interface LitigationManagementSummary {
  open_pending: number;
  complete: number;
}

export interface LitigationManagementRow extends BaseTableRow {
  id: string;
  litigation_event_status: string;
  date: string;
  case: string;
  case_type: string;
  event: string;
  description: string;
  court: string;
  case_name: string;
  status: string;
}

export interface LitigationManagementResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: LitigationManagementRow[];
  summary: LitigationManagementSummary;
  filter_options: LitigationManagementFilterOptions;
  tiles?: ReportTile[];
}

// Add type guards for the new summary types
export const isSettlementManagementSummary = (summary: ReportSummary): summary is SettlementManagementSummary => {
  return 'demands' in summary && 'offers' in summary && 'checks_this_month' in summary;
};

export const isLitigationManagementSummary = (summary: ReportSummary): summary is LitigationManagementSummary => {
  return 'open_pending' in summary && 'complete' in summary;
};

// Add type guards for the new row types
export const isSettlementManagementRow = (row: BaseTableRow): row is SettlementManagementRow => {
  return 'est_fees' in row && 'mri_status' in row && 'injection_status' in row;
};

export const isLitigationManagementRow = (row: BaseTableRow): row is LitigationManagementRow => {
  return 'litigation_event_status' in row && 'court' in row && 'event' in row;
};

// Client Trust Report
export interface ClientTrustSummary {
  total_deposits: number;
  total_debits: number;
  current_balance: number;
  total_entries: number;
  trust_count: number;
}

export interface ClientTrustRow extends BaseTableRow {
  id: string;
  case_id: string;
  client_name: string;
  organization_status: string | null;
  is_deposit: boolean;
  deposit_date: string | null;
  issuer_payee: string;
  memo: string;
  check_number: string;
  amount: number;
  balance: number;
  created_at: string | null;
  client_trust_entry_type: string | null;
}

export interface ClientTrustFilterOptions extends BaseFilterOptions {
  cases?: FilterOption[];
  deposit_types?: FilterOption[];
}

export interface ClientTrustResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ClientTrustRow[];
  summary: ClientTrustSummary;
  filter_options: ClientTrustFilterOptions;
}

// Add type guards for Client Trust
export const isClientTrustSummary = (summary: ReportSummary): summary is ClientTrustSummary => {
  return 'total_deposits' in summary && 'total_debits' in summary && 'current_balance' in summary;
};

export const isClientTrustRow = (row: BaseTableRow): row is ClientTrustRow => {
  return 'case_id' in row && 'is_deposit' in row && 'balance' in row;
};

// Add tile interface for settlement and litigation management
export interface ReportTile {
  title: string;
  count?: number;
  total?: string;
  fees?: string;
  filter_value: string;
  active: boolean;
  event_count?: number;
  lead_count?: number;
  retained_count?: number;
  conversion_rate?: string;
}

// Case Review Report
export interface CaseReviewSummary {
  total_cases: number;
  average_case_age: number;
  touched_last_7_days: number;
  aged_over_1_year: number;
}

export interface CaseReviewRow extends BaseTableRow {
  id: string;
  case: string;
  days: number;
  case_type: string | null;
  case_status: string;
  case_grade: string | null;
  case_grade_reason: string | null;
  worker: string | null;
  primary: string | null;
  lead_attorney: string | null;
  supervising_attorney: string | null;
  team: CaseWorker[];
}

export interface CaseReviewFilterOptions extends BaseFilterOptions {
  case_types?: FilterOption[];
  case_statuses?: FilterOption[];
  case_grades?: FilterOption[];
  organization_statuses?: FilterOption[];
  status_categories?: FilterOption[];
  workers?: WorkerOption[];
  primary?: WorkerOption[];
  lead_attorneys?: WorkerOption[];
  supervising_attorneys?: WorkerOption[];
  case_managers?: WorkerOption[];
}

export interface CaseReviewResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CaseReviewRow[];
  summary: CaseReviewSummary;
  filter_options: CaseReviewFilterOptions;
}

// Add interface for the consolidated client trust row
export interface ClientTrustConsolidatedRow extends BaseTableRow {
  case: string;
  case_id: string;
  client_name: string;
  organization_status: string | null;
  total_deposits: number;
  total_debits: number;
  total_case_costs: number;
  total_outgoing: number;
  balance: number;
  last_activity_date: string | null;
}

// Add interface for the consolidated client trust summary
export interface ClientTrustConsolidatedSummary {
  total_deposits: number;
  total_debits: number;
  total_case_costs: number;
  total_outgoing: number;
  current_balance: number;
  case_count: number;
}

// Add response type for the consolidated client trust report
export interface ClientTrustConsolidatedResponse {
  count: number;
  next: string | null;
  previous: string | null;
  total_pages: number;
  current_page: number;
  page_size: number;
  results: ClientTrustConsolidatedRow[];
  summary: ClientTrustConsolidatedSummary;
  filter_options: ClientTrustFilterOptions;
}

// Case Costs Report
export interface CaseCostSummary {
  total_costs: number;
  total_entries: number;
  case_count: number;
}

export interface CaseCostRow extends BaseTableRow {
  id: string;
  case: string;
  client_name: string;
  organization_status: string | null;
  status: string;
  cost_type: string;
  paid_date: string | null;
  payee: string;
  memo: string;
  check_number: string;
  amount: number;
  created_at: string | null;
  updated_at: string | null;
}

export interface CaseCostFilterOptions extends BaseFilterOptions {
  status_options?: FilterOption[];
  cost_type_options?: FilterOption[];
  payment_type_options?: FilterOption[];
  priority_options?: FilterOption[];
  section_options?: FilterOption[];
}

export interface CaseCostResponse {
  count: number;
  next: string | null;
  previous: string | null;
  total_pages: number;
  current_page: number;
  page_size: number;
  results: CaseCostRow[];
  summary: CaseCostSummary;
  filter_options: CaseCostFilterOptions;
}

export interface ContactData extends BaseTableRow {
  id: string;
  type: string;
  first_name: string | null;
  last_name: string | null;
  company: string | null;
  phone: string | null;
  email: string | null;
  address: {
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip_code: string | null;
  };
  related_cases_count: number;
  related_cases: Array<{
    id: string;
    client_name: string;
    incident_date: string | null;
    organization_status: string | null;
  }>;
}

export interface ContactsSummary {
  total_contacts: number;
  contacts_by_type: Record<string, number>;
}

export interface ContactsFilterOptions extends BaseFilterOptions {
  contact_types?: FilterOption[];
}

export interface ContactsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ContactData[];
  summary: ContactsSummary;
  filter_options: {
    contact_types: FilterOption[];
  };
}

// Helper function to check if summary is ContactsSummary
export const isContactsSummary = (summary: ReportSummary): summary is ContactsSummary => {
  return 'total_contacts' in summary && 'contacts_by_type' in summary;
};

// Helper function to check if row is ContactData
export const isContactData = (row: BaseTableRow): row is ContactData => {
  return 'type' in row && 'related_cases_count' in row && 'related_cases' in row;
};

// Case Events Calendar Report
export interface CaseEventsCalendarSummary {
  today: number;
  next_7_days: number;
  total_events: number;
}

export interface CaseEventWorker {
  id: string | number;
  name: string;
}

export interface CaseEventsCalendarRow extends BaseTableRow {
  date: string;
  event_name: string;
  type: string;
  case: string;
  description: string;
  workers: CaseEventWorker[];
  litigation_event_type: string;
  litigation_event_status: string;
  doi: string;
}

export interface CaseEventsCalendarFilterOptions extends BaseFilterOptions {
  workers?: WorkerOption[];
  case_types?: FilterOption[];
  case_status_categories?: FilterOption[];
  event_types?: FilterOption[];
  litigation_event_statuses?: FilterOption[];
}

export interface CaseEventsCalendarResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CaseEventsCalendarRow[];
  summary: CaseEventsCalendarSummary;
  filter_options: CaseEventsCalendarFilterOptions;
}

// Add type guard function for Case Events Calendar
export const isCaseEventsCalendarSummary = (summary: ReportSummary): summary is CaseEventsCalendarSummary => {
  return 'today' in summary && 'next_7_days' in summary && 'total_events' in summary;
};

// Add Estimated Value Report types
export interface EstimatedValueSummary {
  total_cases: number;
  total_calculated_estimated_value: number;
  total_manual_estimated_value: number;
  total_medical_bills: number;
  total_lost_wages: number;
}

export interface EstimatedValueRow extends BaseTableRow {
  id: string;
  case: string;
  client_name: string;
  number_of_defendants: number;
  third_party_policy_limit: number;
  uim_policy_limit: number;
  medical_bills_total: number;
  loss_of_wages: number;
  calculated_estimated_value: number;
  estimated_value: number;
  case_manager?: string | null;
  lead_attorney?: string | null;
}

export interface ValueRangeFilter {
  min: number;
  max: number;
  label: string;
}

export interface EstimatedValueFilterOptions extends BaseFilterOptions {
  case_types?: FilterOption[];
  organization_statuses?: FilterOption[];
  case_managers?: WorkerOption[];
  lead_attorneys?: WorkerOption[];
  calculated_estimated_value_range?: ValueRangeFilter;
  manual_estimated_value_range?: ValueRangeFilter;
}

export interface EstimatedValueResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: EstimatedValueRow[];
  summary: EstimatedValueSummary;
  filter_options: EstimatedValueFilterOptions;
}

// Add these new interfaces for Medpay Deposits report after the other report types
export interface MedpayDepositsSummary {
  total_deposits: number;
  total_amount: number;
  paid_amount: number;
  pending_amount: number;
  client_medpay_amount: number;
  third_party_medpay_amount: number;
}

export interface MedpayDepositsRow extends BaseTableRow {
  case: string;
  medpay_type: string;
  insurance: string | null;
  request: number;
  deposited: number;
  total_meds: number;
  total_costs: number;
  status: string;
  medpay_limits: number | null;
  check_number: string | null;
  medpay_request_date: string | null;
  medpay_deposit_date: string | null;
  note: string | null;
  team: CaseWorker[];
}

export interface MedpayDepositsFilterOptions extends BaseFilterOptions {
  organization_statuses?: FilterOption[];
  role_options?: WorkerOption[];
  date_fields?: FilterOption[];
}

export interface MedpayDepositsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  total_pages: number;
  current_page: number;
  page_size: number;
  results: MedpayDepositsRow[];
  summary: MedpayDepositsSummary;
  filter_options: MedpayDepositsFilterOptions;
}
