import { <PERSON><PERSON><PERSON>, DateString, Gender, SSN, PhoneNumber, PhoneType, MaritalStatus } from '@/type/case-management/commonTypes';

// Client Basic Details
interface ClientBasicDetails {
    id: number;
    case: UUID;
    first_name: string;
    middle_name?: string;
    last_name: string;
    nickname?: string;
    gender: Gender;
    date_of_birth: DateString;
    ssn?: SSN;
    driver_license_type?: string;
    driver_license?: string;
    minor_fee_signed: boolean;
    source_type?: string;
    source_detail?: string;
    referred_by?: string;
    note?: string;
    deceased: boolean;
    language?: string;
    race?: string;
    employed?: boolean;
    created_at: DateString;
    updated_at: DateString;
}

interface ClientDetailState {
    clientBasicDetails: {
        data: ClientBasicDetails | null;
        isLoading: boolean;
        error: string | null;
    };
    clientContactDetails: {
        data: ClientContactDetails | null;
        isLoading: boolean;
        error: string | null;
    };
}

interface ClientContactDetails {
    id: number;
    case: UUID;

    // Phone Numbers
    phone_number_1?: PhoneNumber;
    phone_type_1?: PhoneType;
    is_preferred_1: boolean;

    phone_number_2?: PhoneNumber;
    phone_type_2?: PhoneType;
    is_preferred_2: boolean;

    phone_number_3?: PhoneNumber;
    phone_type_3?: PhoneType;
    is_preferred_3: boolean;

    // Address 1
    address1_type?: string;
    address1_street1?: string;
    address1_street2?: string;
    address1_city?: string;
    address1_state?: string;
    address1_zip?: string;
    address1_is_preferred: boolean;
    address1_is_outside_us: boolean;

    // Address 2
    address2_type?: string;
    address2_street1?: string;
    address2_street2?: string;
    address2_city?: string;
    address2_state?: string;
    address2_zip?: string;
    address2_is_preferred: boolean;
    address2_is_outside_us: boolean;

    // Email Addresses
    primary_email?: string;
    secondary_email?: string;

    // Key Contact
    key_contact_first_name?: string;
    key_contact_last_name?: string;
    key_contact_phone?: PhoneNumber;
    key_contact_relationship?: string;
    key_contact_email?: string;
    key_contact_ssn?: SSN;

    // Spouse Information
    marital_status?: MaritalStatus;
    spouse_first_name?: string;
    spouse_last_name?: string;
    spouse_phone?: PhoneNumber;
    spouse_email?: string;
    can_discuss_case: boolean;

    // Emergency Contact
    emergency_contact_first_name?: string;
    emergency_contact_last_name?: string;
    emergency_contact_phone?: PhoneNumber;
    emergency_contact_relationship?: string;
    emergency_contact_email?: string;

    created_at: DateString;
    updated_at: DateString;
}

interface PhoneData {
    number?: PhoneNumber;
    type?: PhoneType;
    isPreferred: boolean;
}

interface AddressData {
    type?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip?: string;
    isPreferred: boolean;
    isOutsideUs: boolean;
}

interface ContactPersonData {
    firstName?: string;
    lastName?: string;
    phone?: PhoneNumber;
    email?: string;
    relationship?: string;
}

type ContactFormData = {
    phones: {
        [key in 'phone1' | 'phone2' | 'phone3']: {
            number: string;
            type: string;
            isPreferred: boolean;
        }
    };
    addresses: {
        address1: AddressData;
        address2: AddressData;
    };
    emails: {
        primary?: string;
        secondary?: string;
    };
    keyContact: ContactPersonData & {
        ssn?: SSN;
    };
    spouse: {
        firstName?: string;
        lastName?: string;
        phone?: PhoneNumber;
        email?: string;
        maritalStatus?: MaritalStatus;
        canDiscussCase: boolean;
    };
    emergencyContact: ContactPersonData;
}


// Enums
export enum InsuranceType {
    HEALTH = "HEALTH",
    AUTO = "AUTO",
    WORKERS_COMP = "WORKERS_COMP",
    MEDICARE = "MEDICARE",
    MEDICAID = "MEDICAID",
    OTHER = "OTHER"
}

export enum InsuranceStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    PENDING = "PENDING",
    TERMINATED = "TERMINATED"
}

export enum CoverageType {
    PRIMARY = "PRIMARY",
    SECONDARY = "SECONDARY",
    TERTIARY = "TERTIARY"
}


export enum StackedStatus {
    YES = "yes",
    NO = "no",
    UNKNOWN = "unknown"
}

// Main Interface
interface ClientInsurance {
    id: number;
    insurance_company: InsuranceCompany;
    no_insurance: boolean;
    plan_type?: string;
    claim_number?: string;
    policy_number?: string;
    medpay_claim_number?: string;
    insured_name?: string;
    um_uim?: string;
    medpay?: string;
    pip?: string;
    deductible?: string;
    coverage_status?: string;
    liability_status?: string;
    liability_type?: string;
    policy_type?: string;
    stacked?: string;
    vehicles?: string;
    claim_note?: string;
    created_at: DateString;
    updated_at: DateString;
    case: string;
    liability_percentage?: string;
    insurance_type?: InsuranceType;
    coverage_type?: CoverageType;
    incident_date?: string;
    group_number?: string;
    member_number?: string;
}

// Add InsuranceCompany interface if not already defined
interface InsuranceCompany {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    fax?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    payee?: string;
    tax_id?: string;
    note?: string;
    created_at: DateString;
    updated_at: DateString;
    adjuster?: {
        id: number;
        name: string;
        phone?: string;
        email?: string;
    };
}

// Request type for creating client insurance
interface ClientInsuranceCreateRequest {
    insurance_company?: number;
    no_insurance: boolean;
    plan_type?: string;
    group_number?: string;
    member_number?: string;
    claim_number?: string;
    policy_number?: string;
    um_uim?: string;
    medpay_claim_number?: string;
    insured_name?: string;
    coverage_type?: CoverageType;
    insurance_type?: InsuranceType;
    status?: InsuranceStatus;
    coverage_status?: string;
    coverage_start_date?: DateString;
    coverage_end_date?: DateString;
    policy_limits?: string;
    deductible?: string;
    copay?: string;
    liability_percentage?: string;
    coverage_verified?: boolean;
    coverage_verified_date?: DateString;
    coverage_verified_by?: string;
    note?: string;
    claim_note?: string;
    medpay?: string;
    pip?: string;
    liability_status?: string;
    liability_type?: string;
    policy_type?: string;
    stacked?: string;
    vehicles?: string;
}

// Add this interface for the update request
type ClientInsuranceUpdateRequest = ClientInsuranceCreateRequest;

// Response types
interface ClientInsuranceListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: ClientInsurance[];
}

interface ClientInsuranceResponse {
    data: ClientInsurance;
    status: number;
    message?: string;
}

// Add this interface for the create request
interface ClientPropertyDamageCreateRequest {
    case: string;
    damage: string | null;
    frame_damage: boolean;
    total_loss: boolean;
    registered_owner: string | null;
    auto_body_shop: string | null;
    estimate: string | null;
    final: string | null;
    vehicle_make: string | null;
    vehicle_model: string | null;
    color: string | null;
    vehicle_year: string | null;
    license_plate: string | null;
    vehicle_vin: string | null;
    mileage: string | null;
    note: string | null;
    insurance_company: string | null;
}

// Client Sync Types
interface ClientSyncRequest {
    source_card_id: number;
    target_case_ids: string[];
    sync_options?: {
        sync_insurances?: boolean;
        sync_property_damage?: boolean;
        sync_legal_representation?: boolean;
        sync_adjusters?: boolean;
    };
}

interface ClientSyncResponse {
    success: boolean;
    message?: string;
}

export type {
    ClientBasicDetails, ClientDetailState, ClientContactDetails, ContactFormData, PhoneData,
    ClientInsurance,
    ClientInsuranceCreateRequest,
    ClientInsuranceUpdateRequest,
    ClientInsuranceListResponse,
    ClientInsuranceResponse,
    InsuranceCompany,
    ClientPropertyDamageCreateRequest,
    ClientSyncRequest,
    ClientSyncResponse,
};


