import {
  // type AdjusterDetailType,
  type LawFirmResponse,
  type AttorneyResponse,
  InsuranceCompany
} from './orgTypes';

// Keep only insurance-specific interfaces
export interface AdjusterDetail {
  id?: number;
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  phone_ext?: string;
}

export interface InsuranceAdjusterDetails {
  id: number;
  client_insurance: number;
  bodily_injury: AdjusterDetail | null;
  bi_supervisor: AdjusterDetail | null;
  medpay_pip: AdjusterDetail | null;
  medpay_pip_supervisor: AdjusterDetail | null;
  property_damage: AdjusterDetail | null;
  pd_supervisor: AdjusterDetail | null;
  created_at: string;
  updated_at: string;
}

export type InsuranceAdjusterResponse = InsuranceAdjusterDetails;

interface InsuranceAdjusterRequest {
  bodily_injury?: number | null;
  bi_supervisor?: number | null;
  medpay_pip?: number | null;
  medpay_pip_supervisor?: number | null;
  property_damage?: number | null;
  pd_supervisor?: number | null;
}

interface InsuranceLegalRepresentationResponse {
  id: number;
  client_insurance: number;
  law_firm: LawFirmResponse | null;
  co_counsel_law_firm: LawFirmResponse | null;
  attorney: AttorneyResponse | null;
  co_counsel_attorney: AttorneyResponse | null;
  note?: string;
  created_at: string;
  updated_at: string;
}

interface InsuranceLegalRepresentationUpdateRequest {
  client_insurance: number;
  law_firm?: number | null;
  co_counsel_law_firm?: number | null;
  attorney?: number | null;
  co_counsel_attorney?: number | null;
  note?: string;
}

interface ClientPropertyDamage {
  id: number;
  vehicle_make: string;
  vehicle_model: string;
  vehicle_year: number | null;
  vehicle_vin: string;
  license_plate: string;
  mileage: number | null;
  color: string;
  damage: string;
  frame_damage: boolean;
  total_loss: boolean;
  damage_description: string | null;
  registered_owner: string;
  auto_body_shop: string;
  estimate: string;
  final: string;
  note: string | null;
  case: string;
  insurance_company: InsuranceCompany | null;
}

interface ClientPropertyDamageResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ClientPropertyDamage[];
}

// Add this interface for the update request
interface ClientPropertyDamageUpdateRequest {
    insurance?: string | null;
    damage: string;
    frame_damage?: boolean;
    total_loss?: boolean;
    registered_owner?: string | null;
    auto_body_shop?: string | null;
    estimate?: string | null;
    final?: string | null;
    make?: string | null;
    model?: string | null;
    color?: string | null;
    year?: string | null;
    plate_number?: string | null;
    vin_number?: string | null;
    mileage?: string | null;
    note?: string | null;
}

export type { ClientPropertyDamage, ClientPropertyDamageResponse, ClientPropertyDamageUpdateRequest };

export {
  type InsuranceAdjusterRequest,
  type InsuranceLegalRepresentationResponse,
  type InsuranceLegalRepresentationUpdateRequest,
};
