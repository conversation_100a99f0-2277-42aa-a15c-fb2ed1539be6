import { ExpertWitnessContact } from "./orgTypes";

// Enums for party types and status
export enum PartyType {
    PLAINTIFF = "PLAINTIFF",
    WITNESS = "WITNESS",
    OTHER = "OTHER",
    EXPERT_WITNESS="EXPERT_WITNESS"
}

export enum PartyStatus {
    OTHER = "OTHER",    
    PENDING_CONTACT = "PENDING_CONTACT",
    STATEMENT_REQUESTED = "STATEMENT_REQUESTED",
    STATEMENT_RECEIVED = "STATEMENT_RECEIVED",
    UNRESPONSIVE = "UNRESPONSIVE",
    UNCOOPERATIVE = "UNCOOPERATIVE",
    PURSUING_CLAIM = "PURSUING_CLAIM",
    SETTLED_CLAIM = "SETTLED_CLAIM",
    WAIVED_CLAIM = "WAIVED_CLAIM",
    NOT_RELEVANT = "NOT_RELEVANT",
    DEPOSITION_COMPLETE = "DEPOSITION_COMPLETE",
    DEPOSITION_PENDING = "DEPOSITION_PENDING",
    TRIAL_WITNESS = "TRIAL_WITNESS"
}

// Interface for law firm
export interface LawFirm {
    id: number;
    office_name: string;
    payee?: string;
    phone?: string;
    email?: string;
    website?: string;
    city?: string;
    state?: string;
}

// Interface for attorney
export interface Attorney {
    id: number;
    first_name: string;
    last_name: string;
    email?: string;
    phone?: string;
}

// Interface for case party contact
export interface CasePartyContact {
    id: number;
    party_type: PartyType;
    first_name: string;
    last_name: string;
    payee?: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

// Interface for case party
export interface CaseParty {
    id: number;
    status: PartyStatus;
    description?: string;
    case: string;
    contact: CasePartyContact;
    law_firm?: LawFirm;
    attorney?: Attorney;
    created_at: string;
    updated_at: string;
}

// Request interfaces
export interface CasePartyCreateRequest {
    status: PartyStatus;
    description?: string;
    contact: number;
    law_firm?: number;
    attorney?: number;
}

export interface CasePartyUpdateRequest {
    status: PartyStatus;
    description?: string;
    contact: number;
    law_firm?: number;
    attorney?: number;
}

export interface CasePartyContactCreateRequest {
    party_type: PartyType;
    first_name: string;
    last_name: string;
    payee?: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
}

export type CasePartyContactUpdateRequest = CasePartyContactCreateRequest;

// Response interfaces
export interface CasePartyListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CaseParty[];
}

export interface CasePartyContactListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CasePartyContact[];
}

// Constants for dropdown choices
export const PARTY_TYPE_CHOICES = [
    { value: PartyType.PLAINTIFF, label: "Plaintiff" },
    { value: PartyType.WITNESS, label: "Witness" },
    { value: PartyType.OTHER, label: "Other" }
];

export const PARTY_STATUS_CHOICES = [
    { value: PartyStatus.OTHER, label: "Other" },
    { value: PartyStatus.PENDING_CONTACT, label: "Pending Contact" },
    { value: PartyStatus.STATEMENT_REQUESTED, label: "Statement Requested" },
    { value: PartyStatus.STATEMENT_RECEIVED, label: "Statement Received" },
    { value: PartyStatus.UNRESPONSIVE, label: "Unresponsive" },
    { value: PartyStatus.UNCOOPERATIVE, label: "Uncooperative" },
    { value: PartyStatus.PURSUING_CLAIM, label: "Pursuing Claim" },
    { value: PartyStatus.SETTLED_CLAIM, label: "Settled Claim" },
    { value: PartyStatus.WAIVED_CLAIM, label: "Waived Claim" },
    { value: PartyStatus.NOT_RELEVANT, label: "Not Relevant" },
    { value: PartyStatus.DEPOSITION_COMPLETE, label: "Deposition Complete" },
    { value: PartyStatus.DEPOSITION_PENDING, label: "Deposition Pending" },
    { value: PartyStatus.TRIAL_WITNESS, label: "Trial Witness" }
];


export interface CaseExpertWitness {
    id: number;
    case: string;
    expert_witness: ExpertWitnessContact;
    type?: 'Defense' | 'Plaintiff';
    description?: string;
    created_at: string;
    updated_at: string;
    retained_date?: string;
    record_received_date?: string;
}

export interface CaseExpertWitnessCreateRequest {
    expert_witness: number;
    type?: 'Defense' | 'Plaintiff';
    description?: string;
    retained_date?: string;
    record_received_date?: string;
}

export type CaseExpertWitnessUpdateRequest = CaseExpertWitnessCreateRequest;