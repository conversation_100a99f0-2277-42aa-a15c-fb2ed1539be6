import { CaseNoteTag } from "@/services/organizationService";
import { DateString } from "./commonTypes";


export interface TaskSyncRequest {
    source_task_id: number;
    target_case_ids: string[];
}

export interface TaskSyncResponse {
    success: boolean;
    message?: string;
}

export interface NoteSyncRequest {
    source_note_id: number;
    target_case_ids: string[];
}

export interface NoteSyncResponse {
    success: boolean;
    message?: string;
}

interface UserDetail {
    id: number;
    email: string;
    name: string,
    role: string,
    subscription_type: string
    organizations: {
        id: string,
        name: string
    },
    verified: boolean
}

export type NotesTasksSectionTypes = 'Case' | 'Negotiation' | 'Negotiations_UIM';
export const Notes_Tasks_Section_Types_Choices: NotesTasksSectionTypes[] = ['Case', 'Negotiation', 'Negotiations_UIM'];

// Note Types
interface CaseNote {
    id: number;
    case: string;
    title: string;
    content: string;
    created_by: UserDetail;
    tagged_users: UserDetail[];
    tagged_user_ids: number[];
    tags: CaseNoteTag[];
    created_at: DateString;
    updated_at: DateString;
    is_editable: boolean;
    defendant_id?: string;
    client_id?: string;
    note_for: NotesTasksSectionTypes;
}

interface CaseNoteWrite {
    case: string;
    title: string;
    content: string;
    tagged_user_ids?: number[];
    tag_ids?: number[];
    defendant_id?: string;
    client_id?: string;
    note_for?: NotesTasksSectionTypes;
}

// Task Types
export enum TaskStatus {
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    REOPENED = "reopened"
}

export const TaskStatusConfig = {
    [TaskStatus.PENDING]: {
        label: "Pending",
        nextStatus: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED, TaskStatus.REOPENED],
        color: "text-yellow-500"
    },
    [TaskStatus.IN_PROGRESS]: {
        label: "In Progress",
        nextStatus: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED, TaskStatus.REOPENED],
        color: "text-blue-500"
    },
    [TaskStatus.COMPLETED]: {
        label: "Completed",
        nextStatus: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED, TaskStatus.REOPENED],
        color: "text-green-500"
    },
    [TaskStatus.REOPENED]: {
        label: "Reopened",
        nextStatus: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED, TaskStatus.REOPENED],
        color: "text-red-500"
    }
};

export enum TaskPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent",
    CRITICAL = "critical",
    CANCELLED = "cancelled"
}

export interface TaskFilters {
    case?: string;
    status?: TaskStatus | string; // Allow string for comma-separated statuses
    priority?: TaskPriority;
    due_date?: string;
    assigned_to?: string;
    is_overdue?: string;
    overwritten_by_admin?: string;
    created_by?: string;
    tagged_users?: string;
    overwritten_by?: string;
    due_date_after?: string;
    due_date_before?: string;
    created_at_after?: string;
    created_at_before?: string;
    page?: number;
    exclude_closed_cases?: boolean;
    for_dashboard?: boolean;
}

interface Task {
    id: number;
    case: string;
    title: string;
    description?: string;
    status: TaskStatus;
    priority?: TaskPriority;
    assigned_to?: UserDetail;
    assigned_to_id?: number;
    tagged_users: UserDetail[];
    tagged_user_ids: number[];
    due_date?: DateString;
    created_by: UserDetail;
    created_at: DateString;
    updated_at: DateString;
    defendant_id?: string;
    task_for: NotesTasksSectionTypes;
    // Support for case_name directly in the task object (from API)
    case_name?: string;
    // Support for case_details object
    case_details?: {
        status: string;
        name?: string;
        case_name?: string;
        name_of_client?: string;
        client_name?: string;
    };
}

interface TaskWrite {
    case: string;
    title: string;
    description?: string;
    status?: TaskStatus;
    priority?: TaskPriority;
    assigned_to_id?: number;
    tagged_user_ids?: number[];
    due_date?: DateString;
    defendant_id?: string;
    task_for?: NotesTasksSectionTypes;
}

interface TaskCreate {
    case: string;
    title: string;
    description?: string;
    assigned_to?: string;
    assigned_to_id?: number;
    status?: TaskStatus;
    priority?: TaskPriority;
    tagged_user_ids?: number[];
    due_date?: DateString;
    defendant_id?: string;
    client_id?: string;
    task_for?: NotesTasksSectionTypes;
}

// Response Types
interface ListResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
}

export type {
    CaseNote,
    CaseNoteWrite,
    Task,
    TaskWrite,
    TaskCreate,
    ListResponse,
    UserDetail
};