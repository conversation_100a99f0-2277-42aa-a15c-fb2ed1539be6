import { DateString } from '@/type/case-management/commonTypes';

// Base interface for settlement advance
interface SettlementAdvance {
    id: number;
    company: LoanCompany;
    amount: string;
    handling_fee: string;
    rate_per_year: string;
    interest: string;
    total_owed: string;
    note?: string;
    created_at: DateString;
    updated_at: DateString;
}

// Request type for creating settlement advance
interface SettlementAdvanceCreateRequest {
    id?: number;
    company: string;
    amount: string;
    handling_fee?: string;
    rate_per_year?: string;
    interest?: string;
    total_owed: string;
    note?: string;
}

// Request type for updating settlement advance
type SettlementAdvanceUpdateRequest = Partial<SettlementAdvanceCreateRequest>;

// Response types
interface SettlementAdvanceListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: SettlementAdvance[];
}

// Loan Company interface
interface LoanCompany {
    id: number;
    company: string;
    payee?: string;
    tax_id?: string;
    phone?: string;
    cell?: string;
    email?: string;
    fax?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    note?: string;
    created_at: DateString;
    updated_at: DateString;
}

// Response type for loan company list
interface LoanCompanyListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: LoanCompany[];
}

// Request type for creating loan company
interface LoanCompanyCreateRequest {
    company: string;
    payee?: string;
    tax_id?: string;
    phone?: string;
    cell?: string;
    email?: string;
    fax?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    note?: string;
}

// Request type for updating loan company
type LoanCompanyUpdateRequest = Partial<LoanCompanyCreateRequest>;

interface LineItem {
    label: string;
    amount: number;
    percentage?: number;
}

interface DefendantNegotiation {
    defendant_id: number;
    regular_negotiation: {
        amount: string;
    };
    total_amount: string;
}

export interface SettlementCalculation {
    id: number;
    case: string;
    fees_percentage: string;
    settlement_proceed: string;
    settlement_fees: string;
    medpay_pip_fees: string;
    advanced_costs: string;
    settlement_advance_loan: string;
    medical_bills: string;
    health_insurance_liens: string;
    attorney_liens: string;
    miscellaneous_liens: string;
    total_liens: string;
    net_to_client: string;
    latest_negotiations: DefendantNegotiation[];
    manual_settlement_entries?: string;
    created_at: DateString;
    updated_at: DateString;
}

export interface TransformedLien {
    payee: string;
    original: number;
    adjusted: number;
    amount: number;
}

export interface LawFirmResponse {
    company_name?: string;
    payee?: string;
    id?: number;
}

export interface AttorneyResponse {
    full_name?: string;
    id?: number;
}

type SettlementCalculationUpdateRequest = {
    fees_percentage?: string;
};

// Manual Settlement Entry interface
interface ManualSettlementEntry {
    id: number;
    company: string;
    expense: string;
    created_at: DateString;
    updated_at: DateString;
}

// Request type for creating manual settlement entry
interface ManualSettlementEntryCreateRequest {
    company: string;
    expense: string;
    for_what?: 'THIRD_PARTY_DEMAND' | 'UIM';
}

// Request type for updating manual settlement entry
type ManualSettlementEntryUpdateRequest = Partial<ManualSettlementEntryCreateRequest>;

interface ClientTrust {
    id?: number;
    case?: string;
    check_number?: string;
    issuer_payee: string;
    memo?: string;
    amount: string;
    deposit_date: string;
}

interface ClientTrustCreateRequest {
    check_number?: string;
    issuer_payee: string;
    memo?: string;
    amount: string;
    deposit_date: string;
}

type ClientTrustUpdateRequest = ClientTrustCreateRequest;

interface ClientTrustSummary {
    total_deposits: number;
    total_withdrawals: number;
    current_balance: number;
}

export type {
    SettlementAdvance,
    SettlementAdvanceCreateRequest,
    SettlementAdvanceUpdateRequest,
    SettlementAdvanceListResponse,
    LoanCompany,
    LoanCompanyListResponse,
    LoanCompanyCreateRequest,
    LoanCompanyUpdateRequest,
    SettlementCalculationUpdateRequest,
    LineItem,
    ManualSettlementEntry,
    ManualSettlementEntryCreateRequest,
    ManualSettlementEntryUpdateRequest,
    ClientTrust,
    ClientTrustCreateRequest,
    ClientTrustUpdateRequest,
    ClientTrustSummary,
};
