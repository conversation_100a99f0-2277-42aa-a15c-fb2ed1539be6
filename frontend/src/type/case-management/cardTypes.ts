export interface UserCardSyncRequest {
    source_card_id: number;
    target_case_ids: string[];
  }

  export interface UserCardSyncResponse {
    success: boolean;
    message?: string;
  }

export interface User {
    id: number;
    email: string;
    name: string;
    first_name: string;
    last_name: string;
}

// Card Data Types
export interface CaseAgeData {
    age_days: number;
}

export interface PendingStatuteData {
    days_remaining: number;
}

export interface PolicyLimitDefendant {
    name: string;
    limit: string;
    company: string;
    policy_number: string;
}

export interface PolicyLimitsData {
    defendants: PolicyLimitDefendant[];
}

export interface LiensData {
    count: number;
    total: string;
}

export interface HealthProvidersData {
    count: number;
}

export interface IncidentReportData {
    status: string;
}

export interface PropertyDamageData {
    total_loss: boolean;
    damage_type: string;
    vehicle_info: {
        make: string | null;
        model: string | null;
        year: string | null;
    };
    estimate: string | null;
    final: string | null;
}

export interface HealthInsuranceData {
    has_health_insurance: boolean;
    health_insurance_count: number;
    health_insurances: HealthInsuranceCardData[];
    total_lien_amount: string;
    last_updated: string;
}

export interface LostWagesEmployer {
    company: string;
    lost_wages: string;
    hours_missed: string | null;
    weeks_missed: string | null;
    status: string;
}

export interface LostWagesData {
    is_employed: boolean;
    total_lost_wages: string;
    employer_count: number;
    employers: LostWagesEmployer[];
}

export interface AdvancedLoansData {
    amount: string;
}

export interface PipMedpayDetailData {
    insurance_company: string;
    policy_number: string;
    pip_amount: string | null;
    medpay_amount: string | null;
    claim_number: string;
}

export interface PipMedpayData {
    has_pip_medpay: boolean;
    pip_medpay_count: number;
    pip_medpay_details: PipMedpayDetailData[];
    total_amount: string;
    last_updated: string;
}

export interface PoliceReportStatusData {
    status: string;
    status_display: string;
    last_updated: string;
}

export interface MedicalTreatmentProvider {
    provider_name: string;
    treatment_status: string;
    records_status: string | null;
    billing_status: string | null;
    original_bill: string | null;
    visits: {
        first_visit: string | null;
        last_visit: string | null;
        total_visits: number | null;
    };
    billing: {
        adjusted_bill: string | null;
        insurance_paid: string | null;
        medpay_pip_paid: string | null;
        client_paid: string | null;
        still_owed: string | null;
    };
    lien_info: {
        signed_lien: boolean;
        filed_lien: boolean;
    };
}

export interface MedicalTreatmentData {
    total_original_bills: string;
    provider_count: number;
    providers: MedicalTreatmentProvider[];
    last_updated: string;
}

// Card Update Types
export interface CardVisibilityUpdate {
    is_visible: boolean;
}

export interface CardOrderUpdate {
    id: number;
    order: number;
}

export interface BulkCardOrderUpdate {
    cardType: 'system' | 'user';
    updates: CardOrderUpdate[];
}

export interface UserCardUpdate extends CardVisibilityUpdate, CardOrderUpdate {
    is_pinned?: boolean;
    color?: string;
    title?: string;
    content?: string;
}

export interface ClientAgeData {
    years: number;
    months: number;
    days: number;
    display: string;
}

/**{
    "has_health_insurance": true,
    "health_insurance_count": 3,
    "health_insurances": [
        {
            "no_insurance": false,
            "insurance_company": "Anurag Maurya",
            "policy_number": null,
            "medicare": "False",
            "plan_type": "medicare_supplement",
            "erisa": "False",
            "total_lien": "234.00",
            "adjusted_lien": null
        },
        {
            "no_insurance": false,
            "insurance_company": "Anurag Maurya",
            "policy_number": null,
            "medicare": "False",
            "plan_type": null,
            "erisa": "False",
            "total_lien": null,
            "adjusted_lien": null
        },
        {
            "no_insurance": false,
            "insurance_company": "Anurag Maurya",
            "policy_number": null,
            "medicare": "False",
            "plan_type": null,
            "erisa": "False",
            "total_lien": null,
            "adjusted_lien": null
        }
    ],
    "has_pip": true,
    "medicare_details": [
        {
            "insurance_company": "Anurag Maurya",
            "medicare_info": "False"
        },
        {
            "insurance_company": "Anurag Maurya",
            "medicare_info": "False"
        },
        {
            "insurance_company": "Anurag Maurya",
            "medicare_info": "False"
        }
    ],
    "pip_count": 1,
    "pip_details": [
        {
            "insurance_company": "Anurag Maurya",
            "policy_number": "",
            "pip_amount": "4500",
            "claim_number": "",
            "medpay_claim_number": ""
        }
    ],
    "last_updated": "2025-03-04T11:44:27.936788+00:00"
} */

export interface HealthInsuranceCardData {
    no_insurance: boolean;
    insurance_company: string;
    policy_number: string | null;
    medicare: string;
    plan_type: string | null;
    erisa: string;
    total_lien: string;
    adjusted_lien: string | null;
}

export interface PipDetailCardData {
    insurance_company: string;
    policy_number: string;
    pip_amount: string;
    claim_number: string;
    medpay_claim_number: string;
}

export interface HealthInsuranceAndPipData {
    has_health_insurance: boolean;
    health_insurance_count: number;
    health_insurances: HealthInsuranceCardData[];
    has_pip: boolean;
    pip_count: number;
    pip_details: PipDetailCardData[];
    last_updated: string;
}

export interface CaseCostData {
    pending_amount: string;
    paid_amount: string;
    total_amount: string;
    cost_count: number;
    last_updated: string;
}

// Union type for all possible card data types
export type CardData =
    | { card_type: 'CASE_AGE'; data: CaseAgeData }
    | { card_type: 'PENDING_STATUTE'; data: PendingStatuteData }
    | { card_type: 'THIRD_PARTY_POLICY_LIMIT'; data: PolicyLimitsData }
    | { card_type: 'ATTORNEY_LIENS'; data: LiensData }
    | { card_type: 'MISC_LIENS'; data: LiensData }
    | { card_type: 'HEALTH_PROVIDERS'; data: HealthProvidersData }
    | { card_type: 'INCIDENT_REPORT'; data: IncidentReportData }
    | { card_type: 'PROPERTY_DAMAGE'; data: PropertyDamageData }
    | { card_type: 'HEALTH_INSURANCE'; data: HealthInsuranceData }
    | { card_type: 'LOST_WAGES'; data: LostWagesData }
    | { card_type: 'ADVANCED_LOANS'; data: AdvancedLoansData }
    | { card_type: 'POLICE_REPORT_STATUS'; data: PoliceReportStatusData }
    | { card_type: 'MEDICAL_TREATMENT'; data: MedicalTreatmentData }
    | { card_type: 'CLIENT_AGE'; data: ClientAgeData }
    | { card_type: 'HEALTH_INSURANCE_AND_PIP'; data: HealthInsuranceAndPipData }
    | { card_type: 'PIP_MEDPAY'; data: PipMedpayData }
    | { card_type: 'CASE_COST'; data: CaseCostData };

export interface SystemCard {
    id: number;
    case: string;
    title: string;
    description: string | null;
    order: number;
    is_visible: boolean;
    card_type: CardData['card_type'];
    data: CardData['data'];
    last_calculated_at: string;
    created_at: string;
    updated_at: string;
}

export interface UserCard {
    id: number;
    case: string;
    title: string;
    description: string;
    order: number;
    is_visible: boolean;
    created_by: User;
    content: string;
    is_pinned: boolean;
    color: string;
    created_at: string;
    updated_at: string;
}

export interface CombinedCardsResponse {
    system_cards: SystemCard[];
    user_cards: UserCard[];
}

export interface UserCardCreateRequest {
    title: string;
    content: string;
    is_pinned?: boolean;
    color?: string;
    order?: number;
    is_visible?: boolean;
}

export interface SystemCardUpdateRequest {
    is_visible: boolean;
    order?: number;
}

export type SystemCardData =
    | CaseAgeData
    | PendingStatuteData
    | PolicyLimitsData
    | LiensData
    | HealthProvidersData
    | IncidentReportData
    | PropertyDamageData
    | HealthInsuranceData
    | LostWagesData
    | AdvancedLoansData
    | PoliceReportStatusData
    | MedicalTreatmentData
    | PipMedpayData
    | CaseCostData;

export type SystemCardType =
    | "CASE_AGE"
    | "PENDING_STATUTE"
    | "THIRD_PARTY_POLICY_LIMIT"
    | "ATTORNEY_LIENS"
    | "MISC_LIENS"
    | "HEALTH_PROVIDERS"
    | "INCIDENT_REPORT"
    | "PROPERTY_DAMAGE"
    | "HEALTH_INSURANCE"
    | "LOST_WAGES"
    | "ADVANCED_LOANS"
    | "POLICE_REPORT_STATUS"
    | "MEDICAL_TREATMENT"
    | "CLIENT_AGE"
    | "HEALTH_INSURANCE_AND_PIP"
    | "PIP_MEDPAY"
    | "CASE_COST";
