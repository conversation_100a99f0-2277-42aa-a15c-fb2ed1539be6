export interface ImpactAssessment {
  id: number;
  case: string;
  loss_of_income: number | string | undefined;
  out_of_pocket_expenses: number | string | undefined;
  household_services: number | string | undefined;
  additional_facts: string;
  impact_on_recreation: string;
  impact_on_entertainment: string;
  impact_on_home_activities: string;
  impact_on_social_time: string;
  impact_on_self_care: string;
  additional_impact: string;
  created_at: string;
  updated_at: string;
}

export interface ImpactAssessmentFormData {
  loss_of_income: number | string | undefined;
  out_of_pocket_expenses: number | string | undefined;
  household_services: number | string | undefined;
  additional_facts: string;
  impact_on_recreation: string;
  impact_on_entertainment: string;
  impact_on_home_activities: string;
  impact_on_social_time: string;
  impact_on_self_care: string;
  additional_impact: string;
}
