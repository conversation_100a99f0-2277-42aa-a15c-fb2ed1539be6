// Organization-wide type definitions

import { LoanCompany } from "./settlementTypes";

type DateString = string; // ISO 8601 format
type PhoneNumber = string; // Format: XXX-XXX-XXXX

/**
 * Enum for different types of insurance
 */
export enum InsuranceType {
  AUTO = "AUTO",
  HEALTH = "HEALTH",
  LIABILITY = "LIABILITY",
  WORKERS_COMP = "WORKERS_COMP",
  OTHER = "OTHER"
}

/**
 * Enum for insurance categories
 */
export enum InsuranceCategory {
  PRIMARY = "PRIMARY",
  SECONDARY = "SECONDARY",
  TERTIARY = "TERTIARY"
}

// Insurance Company Types
/**
 * Interface representing an insurance company
 */
export interface InsuranceCompany {
  id: number;
  organization?: number;
  company?: string;
  name: string;
  insurance_type?: InsuranceType;
  category?: InsuranceCategory;
  payee?: string;
  tax_id?: string;
  phone?: PhoneNumber;
  phone_ext?: string;
  cell?: PhoneNumber;
  fax?: PhoneNumber;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  note?: string;
  is_active?: boolean;
  medicare_id?: string;
  medicare_type?: string;
  medicare_state?: string;
  medicare_region?: string;
  medicare_address?: string;
  created_at: DateString;
  updated_at: DateString;
}

/**
 * Interface for creating/updating an insurance company
 */
export interface InsuranceCompanyCreateRequest {
  name: string;
  insurance_type?: InsuranceType;
  category?: InsuranceCategory;
  payee?: string;
  tax_id?: string;
  phone?: PhoneNumber;
  phone_ext?: string;
  cell?: PhoneNumber;
  fax?: PhoneNumber;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  note?: string;
  is_active?: boolean;
  medicare_id?: string;
  medicare_type?: string;
  medicare_state?: string;
  medicare_region?: string;
  medicare_address?: string;
}

/**
 * Interface for insurance company list response
 */
export interface InsuranceCompanyListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: InsuranceCompany[];
}

/**
 * Interface for insurance company API response
 */
export interface InsuranceCompanyResponse {
  data: InsuranceCompany;
  status: number;
  message?: string;
}

// Law Firm Types
/**
 * Interface for creating a law firm
 */
export interface LawFirmCreateRequest {
  office_name: string;
  payee?: string;
  phone?: string;
  phone_ext?: string;
  cell?: string;
  fax?: string;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  tax_id?: string;
  note?: string;
}

/**
 * Interface for law firm response
 */
export interface LawFirmResponse extends LawFirmCreateRequest {
  id: number;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for law firm list response
 */
export interface LawFirmListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: LawFirmResponse[];
}

/**
 * Interface for updating a law firm
 */
export type LawFirmUpdateRequest = LawFirmCreateRequest;

// Attorney Types
/**
 * Interface for creating an attorney
 */
export interface AttorneyCreateRequest {
  payee?: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  gender?: string;
  phone?: string;
  phone_ext?: string;
  cell?: string;
  fax?: string;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  tax_id?: string;
  note?: string;
}

/**
 * Interface for attorney response
 */
export interface AttorneyResponse extends AttorneyCreateRequest {
  id: number;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for attorney list response
 */
export interface AttorneyListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: AttorneyResponse[];
}

/**
 * Interface for updating an attorney
 */
export type AttorneyUpdateRequest = AttorneyCreateRequest;

// Adjuster Types
/**
 * Interface for adjuster contact
 */
export interface AdjusterContact {
  id: number;
  insurance_company: number;
  first_name: string;
  last_name: string;
  payee?: string | null;
  gender?: string | null;
  email?: string | null;
  phone?: string | null;
  fax?: string | null;
  extension?: string | null;
  address_line_1?: string | null;
  address_line_2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for creating an adjuster
 */
export interface AdjusterCreateRequest {
  insurance_company: number;
  first_name: string;
  last_name: string;
  payee?: string;
  gender?: 'Male' | 'Female' | 'Non-binary';
  phone?: string;
  phone_ext?: string;
  cell?: string;
  fax?: string;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  tax_id?: string;
  note?: string;
}

/**
 * Interface for adjuster response
 */
export interface AdjusterResponse extends AdjusterCreateRequest {
  id: number;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for adjuster details/contact information
 */
export interface AdjusterDetailType {
  id: number;
  insurance_company: number;
  first_name: string;
  last_name: string;
  payee?: string | null;
  gender?: 'Male' | 'Female' | 'Non-binary' | null;
  phone?: string | null;
  phone_ext?: string | null;
  cell?: string | null;
  fax?: string | null;
  email?: string | null;
  website?: string | null;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  tax_id?: string | null;
  note?: string | null;
  created_at: string;
  updated_at: string;
}

// Contact Type Choices
export enum ContactType {
    COPY_SERVICES = "COPY_SERVICES",
    COURT_REPORTER = "COURT_REPORTER",
    COURTS = "COURTS",
    EXPERT_WITNESS = "EXPERT_WITNESS",
    GENERAL = "GENERAL",
    HEALTH_INSURANCES = "HEALTH_INSURANCES",
    HEALTH_PROVIDERS = "HEALTH_PROVIDERS",
    INTERPRETER = "INTERPRETER",
    INVESTIGATION = "INVESTIGATION",
    LAW_FIRMS = "LAW_FIRMS",
    LITIGATION_FIRMS = "LITIGATION_FIRMS",
    LOAN_COMPANIES = "LOAN_COMPANIES",
    MEDIATORS = "MEDIATORS",
    MEDICAL_LIEN_HOLDERS = "MEDICAL_LIEN_HOLDERS",
    MISC_LIEN_HOLDERS = "MISC_LIEN_HOLDERS",
    POLICE_DEPARTMENTS = "POLICE_DEPARTMENTS",
    PROCESS_SERVICES = "PROCESS_SERVICES",
    SUBROGATION_COMPANIES = "SUBROGATION_COMPANIES"
}

export const CONTACT_TYPE_CHOICES = [
    { value: ContactType.COPY_SERVICES, label: "Copy Services" },
    { value: ContactType.COURT_REPORTER, label: "Court Reporter" },
    { value: ContactType.COURTS, label: "Courts" },
    { value: ContactType.EXPERT_WITNESS, label: "Expert Witness" },
    { value: ContactType.GENERAL, label: "General" },
    { value: ContactType.HEALTH_INSURANCES, label: "Health Insurances" },
    { value: ContactType.HEALTH_PROVIDERS, label: "Health Providers" },
    { value: ContactType.INTERPRETER, label: "Interpreter" },
    { value: ContactType.INVESTIGATION, label: "Investigation" },
    { value: ContactType.LAW_FIRMS, label: "Law Firms" },
    { value: ContactType.LITIGATION_FIRMS, label: "Litigation Firms" },
    { value: ContactType.LOAN_COMPANIES, label: "Loan Companies" },
    { value: ContactType.MEDIATORS, label: "Mediators" },
    { value: ContactType.MEDICAL_LIEN_HOLDERS, label: "Medical Lien Holders" },
    { value: ContactType.MISC_LIEN_HOLDERS, label: "Misc Lien Holders" },
    { value: ContactType.POLICE_DEPARTMENTS, label: "Police Departments" },
    { value: ContactType.PROCESS_SERVICES, label: "Process Services" },
    { value: ContactType.SUBROGATION_COMPANIES, label: "Subrogation Companies" },
] as const;

// Medical Provider Types
/**
 * Interface for medical provider contact
 */
export interface MedicalProviderContact {
    id: number;
    medical_provider: number;
    contact_type: "OFFICE" | "BILLING" | "RECORDS";
    name: string | null;
    payee: string | null;
    phone: string | null;
    phone_ext: string | null;
    cell: string | null;
    fax: string | null;
    email: string | null;
    website: string | null;
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip_code: string | null;
    tax_id: string | null;
    note: string | null;
    created_at: string;
    updated_at: string;
}

/**
 * Interface for medical provider
 */
export interface MedicalProvider {
  id: number;
  organization?: number;
  company: string;
  payee?: string;
  tax_id?: string;
  phone?: PhoneNumber;
  phone_ext?: string;
  cell?: PhoneNumber;
  fax?: PhoneNumber;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  note?: string;
  is_active: boolean;
  specialties?: string;
  accepts_liens?: string;
  no_records_service: boolean;
  do_not_use: boolean;
  contacts?: MedicalProviderContact[];
  created_at?: DateString;
  updated_at?: DateString;
  client_responsibility?: boolean;
}

/**
 * Interface for creating/updating a medical provider
 */
export interface MedicalProviderCreateRequest {
  company: string;
  payee?: string;
  tax_id?: string;
  phone?: PhoneNumber;
  phone_ext?: string;
  cell?: PhoneNumber;
  fax?: PhoneNumber;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  note?: string;
  is_active?: boolean;
  specialties?: string;
  accepts_liens?: string;
  no_records_service?: boolean;
  do_not_use?: boolean;
}

/**
 * Interface for medical provider list response
 */
export interface MedicalProviderListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: MedicalProvider[];
}

/**
 * Interface for medical provider API response
 */
export interface MedicalProviderResponse {
  data: MedicalProvider;
  status: number;
  message?: string;
}

export interface LienHolder {
    id: number;
    organization: number;
    company: string;
    payee: string | null;
    first_name: string | null;
    last_name: string | null;
    phone: string | null;
    phone_ext: string | null;
    cell: string | null;
    fax: string | null;
    email: string | null;
    website: string | null;
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip_code: string | null;
    tax_id: string | null;
    note: string | null;
    created_at: string;
    updated_at: string;
}

export interface LienHolderCreateRequest {
    company: string;
    payee?: string | null;
    first_name?: string | null;
    last_name?: string | null;
    phone?: string | null;
    phone_ext?: string | null;
    cell?: string | null;
    fax?: string | null;
    email?: string | null;
    website?: string | null;
    street1?: string | null;
    street2?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
    tax_id?: string | null;
    note?: string | null;
}

/**
 * Interface for subrogation contact
 */
export interface SubrogationContact {
  id: number;
  organization: number;
  payee?: string | null;
  first_name: string;
  last_name?: string | null;
  phone?: string | null;
  phone_ext?: string | null;
  cell?: string | null;
  fax?: string | null;
  email?: string | null;
  website?: string | null;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  tax_id?: string | null;
  note?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for creating/updating a subrogation contact
 */
export interface SubrogationContactCreateRequest {
  payee?: string;
  first_name: string;
  last_name?: string;
  phone?: string;
  phone_ext?: string;
  cell?: string;
  fax?: string;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  tax_id?: string;
  note?: string;
}

/**
 * Interface for subrogation company
 */
export interface SubrogationCompany {
  id: number;
  organization: number;
  company: string;
  payee?: string | null;
  phone?: string | null;
  phone_ext?: string | null;
  cell?: string | null;
  fax?: string | null;
  email?: string | null;
  website?: string | null;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  tax_id?: string | null;
  note?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for creating/updating a subrogation company
 */
export interface SubrogationCompanyCreateRequest {
  company: string;
  payee?: string;
  phone?: string;
  phone_ext?: string;
  cell?: string;
  fax?: string;
  email?: string;
  website?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  tax_id?: string;
  note?: string;
}

export interface OrgCostContact {
    id?: number;
    organization?: number;
    contact_type: ContactType;
    company_name: string;
    memo?: string;
    payee?: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
    created_at?: string;
    updated_at?: string;
    full_display_name?: string;
}

export type OrgCostContactCreateRequest = Omit<OrgCostContact, 'id' | 'created_at' | 'updated_at'>;
export type OrgCostContactUpdateRequest = Partial<OrgCostContactCreateRequest>; 

export interface ExpertWitnessContact {
    id: number;
    company: string;
    payee: string;
    first_name: string;
    last_name: string;
    phone_ext?: string;
    specialties?: string;
    hourly_fee?: string;
    testimony_fee?: string;
    phone?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
    created_at?: string;
    updated_at?: string;
    zip?: string;
    retained_date?: string;
    record_received_date?: string;
} 

export interface CaseCost {
    id: number;
    company: LoanCompany;
    case: string;
    contact: number;
    amount: string;
    memo?: string | null;
    payment_type?: 'CHECK' | 'CREDIT_CARD' | null;
    check_number?: string | null;
}

export type CaseCostCreateRequest = Omit<CaseCost, 'id' | 'case'>;
export type CaseCostUpdateRequest = Partial<CaseCostCreateRequest>; 

export interface User {
    id: number;
    email: string;
    name: string;
    roles: string;
    is_active: boolean;
}

export interface UserListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: User[];
}

export interface Contact {
  id?: number;
  full_display_name?: string;
  contact_type?: ContactType;
  // Add other contact fields as needed
}

/**
 * Enum for KPI tracking intervals
 */
export enum KPIInterval {
  DAILY = "daily",
  WEEKLY = "weekly",
  MONTHLY = "monthly",
  QUARTERLY = "quarterly",
  YEARLY = "yearly"
}

/**
 * Enum for KPI metric types
 */
export enum KPIMetricType {
  COUNT = "count",
  MONETARY = "monetary",
  TIME = "time",
  PERCENTAGE = "percentage",
  RATE = "rate"
}

/**
 * Interface for KPI goal
 */
export interface KPIGoal {
  id: number;
  kpi_type: string;
  interval: KPIInterval;
  target_value: number;
  metric_type: KPIMetricType;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for creating a KPI goal
 */
export interface KPIGoalCreateRequest {
  kpi_type: string;
  interval: KPIInterval;
  target_value: number;
  metric_type: KPIMetricType;
  is_active?: boolean;
}

/**
 * Interface for bulk creating KPI goals
 */
export interface KPIGoalBulkCreateRequest {
  goals: KPIGoalCreateRequest[];
}

/**
 * Interface for updating a KPI goal
 */
export interface KPIGoalUpdateRequest {
  id: number;
  data: Partial<KPIGoalCreateRequest>;
} 