import { OrgCostContact } from "./orgTypes";

// Enums for cost status and priority
export enum CostStatus {
    PAID = "PAID",
    CANCELLED = "CANCELLED",
    PENDING = "PENDING"
}

export enum CostPriority {
    REGULAR = "REGULAR",
    URGENT = "URGENT",
    HIGH = "HIGH"
}

export enum PaymentType {
    CHECK = "CHECK",
    CREDIT_CARD = "CREDIT_CARD"
}

export enum CostFor {
    CASE_COST = "CASE_COST",
    LITIGATION_COST = "LITIGATION_COST",
}

// Interface for case cost
export interface CaseCost {
    id?: number;
    case?: string;
    contact: number;
    amount: string;
    memo?: string;
    payment_type?: PaymentType;
    check_number?: string;
    credit_card_reference?: string;
    invoice_number?: string;
    invoice_date?: string;
    paid_date?: string;
    requested_date?: string;
    status: CostStatus;
    priority: CostPriority;
    cost_for: CostFor;
    void: boolean;
    created_at?: string;
    updated_at?: string;
    created_by?: number;
    files?: Array<{
        id: number;
        file: string;
        filename: string;
        content_type: string;
    }>;
    cost_contact?: OrgCostContact;
    request_created_by?: string;
    paid_created_by?: string;
}

// Interface for creating/updating a case cost
export type CaseCostCreateRequest = {
    contact: number;
    amount: string;
    memo?: string;
    payment_type?: PaymentType;
    check_number?: string;
    credit_card_reference?: string;
    invoice_number?: string;
    invoice_date?: string;
    paid_date?: string;
    requested_date?: string;
    status?: CostStatus;
    priority: CostPriority;
    is_void?: boolean;
    cost_for?: CostFor;
};

export type CaseCostUpdateRequest = Partial<CaseCostCreateRequest>;

// Interface for case cost list response
export interface CaseCostListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CaseCost[];
}

// Constants for dropdown choices
export const STATUS_CHOICES = [
    { value: CostStatus.PAID, label: "Paid" },
    { value: CostStatus.CANCELLED, label: "Cancelled" },
    { value: CostStatus.PENDING, label: "Pending" }
];

export const PAYMENT_TYPE_CHOICES = [
    { value: PaymentType.CHECK, label: "Check" },
    { value: PaymentType.CREDIT_CARD, label: "Credit Card" }
];

export const PRIORITY_CHOICES = [
    { value: CostPriority.REGULAR, label: "Regular" },
    { value: CostPriority.URGENT, label: "Urgent" },
    { value: CostPriority.HIGH, label: "High" }
];

export interface Cost {
  id: number;
  case: string;
  amount: string;
  check_number?: string;
  memo?: string;
  status: CostStatus;
  cost_for: CostFor;
  cost_contact?: OrgCostContact;
  created_at: string;
  updated_at: string;
  void?: boolean;
} 