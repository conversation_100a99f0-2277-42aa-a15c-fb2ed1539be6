import { InsuranceCompany, SubrogationCompany, SubrogationContact } from './orgTypes';

export interface HealthInsurance {
    id?: number;
    case?: string;
    no_insurance: boolean;
    insurance_company?: InsuranceCompany;
    subrogation_company?: InsuranceCompany;
    insured?: string;
    group_number?: string;
    member_number?: string;
    policy_number?: string;
    representative?: SubrogationContact;
    total_lien?: string;
    adjusted_lien?: string;
    file_number?: string;
    plan_type?: string;
    erisa?: string;
    medicare?: string;
    note?: string;
    created_at?: string;
    updated_at?: string;
    lead?: string;
    final_lien?: boolean;
    final_lien_date?: string;
    final_amount?: string;
}

export interface HealthInsuranceCreateRequest {
    no_insurance: boolean;
    insurance_company?: number;
    subrogation_company?: number;
    insured?: string;
    group_number?: string;
    member_number?: string;
    policy_number?: string;
    representative?: number;
    total_lien?: string;
    adjusted_lien?: string;
    file_number?: string;
    plan_type?: string;
    erisa?: string;
    medicare?: string;
    note?: string;
    target_cases?: string[];
    final_lien?: boolean;
    final_lien_date?: string;
    final_amount?: string;
}

export interface HealthInsuranceResponse extends HealthInsurance {
    insurance_company_details?: InsuranceCompany;
    subrogation_company_details?: SubrogationCompany;
    representative_details?: SubrogationContact;
    target_cases?: string[];
} 