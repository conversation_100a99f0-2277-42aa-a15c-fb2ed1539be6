import { InsuranceCompany, LawFirmResponse, AttorneyResponse } from './orgTypes';

export interface Employer {
    id: number;
    case: string;
    position?: string;
    lost_wages_status?: 'pending' | 'approved' | 'denied' | 'not_applicable';
    total_lost_wages?: string;
    hours_missed?: string;
    weeks_missed?: string;
    income_type?: 'hourly' | 'salary';
    wage?: string;
    company_name: string;
    contact_first_name?: string;
    contact_last_name?: string;
    phone?: string;
    phone_ext?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    description?: string;
    unemployed: boolean;
    created_at: string;
    updated_at: string;
}

export interface EmployerCreateRequest {
    position?: string;
    lost_wages_status?: string;
    total_lost_wages?: string;
    hours_missed?: string;
    weeks_missed?: string;
    income_type?: string;
    wage?: string;
    company_name: string;
    contact_first_name?: string;
    contact_last_name?: string;
    phone?: string;
    phone_ext?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    description?: string;
    unemployed: boolean;
}

export interface EmployerWorkersCompensation {
    id: number;
    employer: number;
    status?: 'pending' | 'approved' | 'denied' | 'not_applicable';
    law_firm?: number | LawFirmResponse;
    attorney?: number | AttorneyResponse;
    insurance_company?: number | InsuranceCompany;
    insurance_policy_number?: string;
    insurance_claim_number?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface EmployerWorkersCompensationCreateRequest {
    status?: string;
    law_firm?: number;
    attorney?: number;
    insurance_company?: number;
    insurance_policy_number?: string;
    insurance_claim_number?: string;
    note?: string;
}

export interface EmployerResponse extends Employer {
    workers_compensation?: EmployerWorkersCompensationResponse;
}

export interface EmployerWorkersCompensationResponse extends EmployerWorkersCompensation {
    law_firm_details?: LawFirmResponse;
    attorney_details?: AttorneyResponse;
    insurance_company_details?: InsuranceCompany;
} 