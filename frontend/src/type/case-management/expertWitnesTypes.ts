import { DateString } from '@/type/case-management/commonTypes';

// Expert Witness Contact
export interface ExpertWitnessContact {
    id: number;
    company: string;
    payee?: string;
    first_name: string;
    last_name: string;
    phone?: string;
    cell?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip?: string;
    email?: string;
    fax?: string;
    tax_id?: string;
    website?: string;
    note?: string;
    specialties: string;
    hourly_fee?: string;
    testimony_fee?: string;
}

// Request type for creating/updating expert witness
export interface ExpertWitnessCreateRequest {
    company: string;
    payee?: string;
    first_name: string;
    last_name: string;
    phone?: string;
    cell?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip?: string;
    email?: string;
    fax?: string;
    tax_id?: string;
    website?: string;
    note?: string;
    specialties: string;
    hourly_fee?: string;
    testimony_fee?: string;
    case: string;
}

// Case Expert Witness
export interface CaseExpertWitness {
    id: number;
    case: string;
    expert_witness: ExpertWitnessContact;
    type?: 'Defense' | 'Plaintiff';
    description?: string;
    created_at: DateString;
    updated_at: DateString;
}

// Request type for creating/updating case expert witness
export interface CaseExpertWitnessCreateRequest {
    expert_witness: number;
    type?: 'Defense' | 'Plaintiff';
    description?: string;
}

// Response types
export interface ExpertWitnessListResponse {
    results: ExpertWitnessContact[];
    count: number;
}
