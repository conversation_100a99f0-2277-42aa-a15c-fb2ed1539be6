import { OrgCostContact } from "./orgTypes";

// Enums for cost status and priority
export enum CostStatus {
    PENDING = "PENDING",
    APPROVED = "APPROVED",
    PAID = "PAID",
    CANCELLED = "CANCELLED"
}

export enum CostPriority {
    REGULAR = "REGULAR",
    URGENT = "URGENT",
    HIGH = "HIGH"
}

export enum PaymentType {
    CHECK = "CHECK",
    CREDIT_CARD = "CREDIT_CARD"
}
export interface LitigationCost {
    id?: string;
    case?: string;
    contact: number;
    amount: string;
    memo?: string;
    payment_type?: PaymentType;
    check_number?: string;
    credit_card_reference?: string;
    invoice_number?: string;
    invoice_date?: string;
    paid_date?: string;
    requested_date?: string;
    status: CostStatus;
    priority: CostPriority;
    is_void: boolean;
    created_at?: string;
    updated_at?: string;
    created_by?: number;
    files?: Array<{
        id: number;
        file: string;
        filename: string;
        content_type: string;
    }>;
    cost_contact?: OrgCostContact;
    request_created_by?: string;
    paid_created_by?: string;
}

export interface LitigationCostCreateRequest {
    contact: number;
    amount: string;
    memo?: string;
    payment_type?: PaymentType;
    check_number?: string;
    credit_card_reference?: string;
    invoice_number?: string;
    invoice_date?: string;
    paid_date?: string;
    requested_date?: string;
    status?: CostStatus;
    priority: CostPriority;
    is_void?: boolean;
}

export type LitigationCostUpdateRequest = Partial<LitigationCostCreateRequest>;

export interface LitigationCostListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: LitigationCost[];
}

export const STATUS_CHOICES = [
    { value: CostStatus.PENDING, label: "Pending" },
    { value: CostStatus.APPROVED, label: "Approved" },
    { value: CostStatus.PAID, label: "Paid" },
    { value: CostStatus.CANCELLED, label: "Cancelled" }
];

export const PAYMENT_TYPE_CHOICES = [
    { value: PaymentType.CHECK, label: "Check" },
    { value: PaymentType.CREDIT_CARD, label: "Credit Card" }
];

export const PRIORITY_CHOICES = [
    { value: CostPriority.REGULAR, label: "Regular" },
    { value: CostPriority.URGENT, label: "Urgent" },
    { value: CostPriority.HIGH, label: "High" }
]; 