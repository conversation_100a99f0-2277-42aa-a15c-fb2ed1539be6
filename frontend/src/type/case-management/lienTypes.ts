import { LawFirmResponse, AttorneyResponse, LienHolder } from './orgTypes';

// Base attorney lien interface for create/update
export interface CaseAttorneyLien {
  id?: number;
  case?: string;
  law_firm: number;
  attorney: number;
  fee_amount: string;
  note?: string;
  final_lien?: boolean;
  final_lien_date?: string | null;
  final_amount?: string;
  created_at?: string;
  updated_at?: string;
}

// Extended interface for GET responses with expanded objects
export interface CaseAttorneyLienResponse extends Omit<CaseAttorneyLien, 'law_firm' | 'attorney'> {
  law_firm: LawFirmResponse;
  attorney: AttorneyResponse;
}

// Base miscellaneous lien interface for create/update
export interface CaseMiscellaneousLien {
  id?: number;
  case?: string;
  lien_holder: number;
  lien_name: string;
  lien_amount: string;
  description?: string;
  final_lien?: boolean;
  final_lien_date?: string | null;
  final_amount?: string;
  created_at?: string;
  updated_at?: string;
}

// Extended interface for GET responses with expanded objects
export interface CaseMiscellaneousLienResponse extends Omit<CaseMiscellaneousLien, 'lien_holder'> {
  lien_holder: LienHolder;
}

// Request interfaces
export type CaseAttorneyLienCreateRequest = Omit<CaseAttorneyLien, 'id' | 'case' | 'created_at' | 'updated_at'>;
export type CaseAttorneyLienUpdateRequest = Partial<CaseAttorneyLienCreateRequest>;

export type CaseMiscellaneousLienCreateRequest = Omit<CaseMiscellaneousLien, 'id' | 'case' | 'created_at' | 'updated_at'>;
export type CaseMiscellaneousLienUpdateRequest = Partial<CaseMiscellaneousLienCreateRequest>;

export interface HealthInsuranceLien {
  id: number;
  case: string;
  insurance_company?: {
    id: number;
    company_name: string;
  };
  total_lien: string;
  adjusted_lien: string;
}

export interface HealthInsuranceLienResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: HealthInsuranceLien[];
}
