// Medical Treatment Types

import { LienHolder, MedicalProvider } from "./orgTypes";

/**
 * Enum for treatment status
 */
export enum TreatmentStatus {
    TREATING = "TREATING",
    COMPLETE = "COMPLETE",
    UNKNOWN = "UNKNOWN"
}

/**
 * Enum for records/billing status
 */
export enum RecordStatus {
    REQUESTED = "REQUESTED",
    RECEIVED = "RECEIVED",
    NA = "NA",
    COST_REQUESTED = "COST_REQUESTED",
    NOT_REQUESTED = "NOT_REQUESTED",
    // RECORD_REQUESTED = "RECORD_REQUESTED"
    
}

export interface requestedby {
     id: number,
     first_name: string,
     last_name: string,
     email: string
}
/**
 * Interface for treatment provider
 */
export interface TreatmentProvider {
    id: number | { id: number, company: string };
    case: number;
    medical_provider: MedicalProvider;
    lien_holder?: number | LienHolder;
    treatment_status: TreatmentStatus;
    records_status?: RecordStatus;
    billing_status?: RecordStatus;
    records_requested_at?: string;
    records_requested_by?: requestedby;
    records_received_at?: string;
    records_received_by?: requestedby;
    billing_requested_at?: string;
    billing_requested_by?: requestedby;
    billing_received_at?: string;
    billing_received_by?: requestedby;
    records_date?: string;
    billing_date?: string;
    treatment_description?: string;
    first_visit?: string;
    last_visit?: string;
    number_of_visits?: number;
    original_bill?: string | null;
    adjusted_bill?: string | null;
    insurance_paid?: string | null;
    medpay_pip_paid?: string | null;
    client_paid?: string | null;
    still_owed?: string | null;
    paid_plus_owed?: string | null;
    account_number?: string;
    signed_lien: boolean;
    filed_lien: boolean;
    created_at: string;
    updated_at: string;
    payment_date?: string;
    check_number?: string;
    payment_note?: string;
    target_cases?: string[];
    final_cost?: string;
    client_responsibility?: boolean;
}

/**
 * Interface for creating/updating a treatment provider
 */
export interface TreatmentProviderCreateRequest {
    medical_provider?: number;
    lien_holder?: number;
    treatment_status: TreatmentStatus;
    records_status?: RecordStatus;
    billing_status?: RecordStatus;
    records_date?: string;
    billing_date?: string;
    treatment_description?: string;
    first_visit?: string | null;
    last_visit?: string | null;
    number_of_visits?: number;
    original_bill?: string;
    adjusted_bill?: string;
    insurance_paid?: string;
    medpay_pip_paid?: string;
    client_paid?: string;
    still_owed?: string;
    paid_plus_owed?: string;
    account_number?: string;
    signed_lien?: boolean;
    filed_lien?: boolean;
    target_cases?: string[];
    client_responsibility?: boolean;
}

/**
 * Interface for treatment provider list response
 */
export interface TreatmentProviderListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: TreatmentProvider[];
}

/**
 * Interface for treatment provider API response
 */
export interface TreatmentProviderResponse {
    data: TreatmentProvider;
    status: number;
    message?: string;
}

/**
 * Interface for medical provider contact creation request
 */
export interface MedicalProviderContactCreateRequest {
    name?: string | null;
    medical_provider?: number;  // ID of the medical provider
    contact_type: string;
    payee?: string | null;
    phone?: string | null;
    phone_ext?: string | null;
    cell?: string | null;
    fax?: string | null;
    email?: string | null;
    website?: string | null;
    street1?: string | null;
    street2?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
    tax_id?: string | null;
    note?: string | null;
}

/**
 * Combined interface for creating provider with contact
 */
export interface MedicalProviderWithContactCreateRequest {
    provider: {
        company: string;
        payee?: string;
        tax_id?: string;
        phone?: string;
        phone_ext?: string;
        cell?: string;
        fax?: string;
        email?: string;
        website?: string;
        street1?: string;
        street2?: string;
        city?: string;
        state?: string;
        zip_code?: string;
        note?: string;
        is_active?: boolean;
        specialties?: string;
        accepts_liens?: boolean;
        no_records_service?: boolean;
        do_not_use?: boolean;
    };
    contact?: MedicalProviderContactCreateRequest;
}

/**
 * Interface for medical provider update request
 */
export interface MedicalProviderUpdateRequest {
    company?: string;
    specialties?: string;
    accepts_liens?: boolean;
    is_active?: boolean;
    no_records_service?: boolean;
    do_not_use?: boolean;
    note?: string;
    client_responsibility?: boolean;
}

/**
 * Interface for the response
 */
export interface MedicalProviderUpdateResponse {
    data: MedicalProvider;
    status: number;
    message?: string;
}

/**
 * Interface for the patch request
 */
export interface TreatmentProviderPatchRequest {
    records_status?: RecordStatus;
    billing_status?: RecordStatus;
    records_requested_at?: string;
    records_requested_by?: string;
    records_received_at?: string;
    records_received_by?: string;
    billing_requested_at?: string;
    billing_requested_by?: string;
    billing_received_at?: string;
    billing_received_by?: string;
    records_date?: string;
    billing_date?: string;
}

/**
 * Interface for Medpay data
 */
export interface MedpayData {
    thirdParty: ThirdPartyMedpay[];
    client: ClientMedpay[];
}

export interface ThirdPartyMedpay {
    description: string;
    amount?: number;
    date?: string;
}

export interface ClientMedpay {
    date: string;
    checkNumber: string;
    note: string;
    amount: number;
}

/**
 * Interface for medical bills
 */
export interface MedicalBill {
    payee: string;
    original: number;
    clientPaid: number;
    medpayPaid: number;
    healthIns: number;
    adjusted: number;
    stillOwed: number;
    paidAndOwed: number;
    pi25: number;
}

/**
 * Interface for medical provider case summary
 */
export interface MedicalProviderCaseSummary {
    money_paid: number;
    total_cases: number;
    avg_per_case: number;
    open_cases: number;
    avg_reduction: number;
    dropped_cases: number;
}

/**
 * Interface for individual medical provider case
 */
export interface MedicalProviderCase {
    id: string;
    case_name: string;
    client_name: string;
    organization_status: string;
    created_at: string;
    original: number | null;
    adjusted: number | null;
    health_ins: number | null;
    final: number | null;
    still_owed: number | null;
    record_status: string;
    billing_status: string;
    reduction: number;
}

/**
 * Interface for medical provider cases response
 */
export interface MedicalProviderCasesResponse {
    summary: MedicalProviderCaseSummary;
    cases: MedicalProviderCase[];
    count: number;
    next: string | null;
    previous: string | null;
}

/**
 * Enum for MedPay deposit status
 */
export enum MedPayDepositStatus {
    INVOICE_RAISED = "INVOICE_RAISED",
    PAID = "PAID"
}

/**
 * Interface representing insurance company details in API responses
 */
export interface InsuranceCompanyResponse {
    id: number;
    name: string;
    payee?: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
    type?: string;
    created_at: string;
    updated_at: string;
}

/**
 * Interface for client insurance in API responses
 */
export interface ClientInsuranceResponse {
    id: number;
    insurance_company: InsuranceCompanyResponse;
    no_insurance: boolean;
    plan_type?: string | null;
    claim_number?: string | null;
    policy_number?: string | null;
    medpay_claim_number?: string | null;
    insured_name?: string | null;
    um_uim?: string | null;
    medpay?: string | null;
    pip?: string | null;
    deductible?: string | null;
    coverage_status?: string | null;
    liability_status?: string | null;
    liability_type?: string | null;
    policy_type?: string | null;
    stacked?: string | null;
    vehicles?: string | null;
    claim_note?: string | null;
    created_at: string;
    updated_at: string;
    case: string;
}

/**
 * Interface for defendant insurance in API responses
 */
export interface DefendantInsuranceResponse {
    id: number;
    defendant: number;
    insurance_company: InsuranceCompanyResponse;
    no_insurance: boolean;
    claim_number?: string | null;
    policy_number?: string | null;
    insured_name?: string | null;
    policy_limits?: string | null;
    liability_status?: string | null;
    liability_percentage?: string | null;
    coverage_status?: string | null;
    med_pay?: string | null;
    confirmed?: boolean;
    claim_note?: string | null;
    created_at: string;
    updated_at: string;
}

/**
 * Interface for MedicalProviderContact in API responses
 */
export interface MedicalProviderContactResponse {
    id: number;
    medical_provider: number;
    name: string;
    contact_type: string;
    payee?: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string | null;
    note?: string;
    created_at: string;
    updated_at: string;
}

/**
 * Interface for MedicalProvider in API responses
 */
export interface MedicalProviderResponse {
    id: number;
    organization: number;
    company: string;
    specialties?: string;
    specialties_list?: string[];
    is_all_specialties?: boolean;
    accepts_liens?: boolean;
    no_records_service?: boolean;
    do_not_use?: boolean;
    note?: string;
    contacts?: MedicalProviderContactResponse[];
    created_at: string;
    updated_at: string;
}

/**
 * Interface for MedPay deposit in request operations (create/update)
 * This is used when making API requests
 */
export interface MedPayDepositRequest {
    id?: number;
    case?: string;
    insurance_type: "CLIENT_MEDPAY" | "THIRD_PARTY_MEDPAY";
    client_insurance?: number;
    defendant_insurance?: number;
    medical_provider: number;
    amount: string;
    check_number?: string;
    status?: MedPayDepositStatus;
    medpay_request_date: string;
    medpay_deposit_date?: string;
    client_trust_entry?: number;
    note?: string;
}

/**
 * Interface for MedPay deposit in API responses
 * This reflects the full structure with nested objects that the API returns
 */
export interface MedPayDepositResponse {
    id: number;
    case: string;
    insurance_type: "CLIENT_MEDPAY" | "THIRD_PARTY_MEDPAY";
    insurance_type_display?: string;
    client_insurance?: ClientInsuranceResponse;
    defendant_insurance?: DefendantInsuranceResponse;
    medical_provider: MedicalProviderResponse;
    amount: string;
    check_number?: string;
    status: MedPayDepositStatus;
    status_display?: string;
    medpay_request_date: string;
    medpay_deposit_date?: string;
    client_trust_entry?: number;
    note?: string;
    created_at: string;
    updated_at: string;
    client_responsibility?: boolean;
}

/**
 * Interface for backward compatibility
 * This is used in places where code hasn't been updated yet
 */
export interface MedPayDeposit extends MedPayDepositRequest {
    id: number;
    created_at: string;
    updated_at: string;
}

/**
 * Interface for creating/updating a MedPay deposit
 */
export interface MedPayDepositCreateRequest {
    insurance_type: "CLIENT_MEDPAY" | "THIRD_PARTY_MEDPAY";
    client_insurance?: number;
    defendant_insurance?: number;
    medical_provider: number;
    amount: string;
    check_number?: string;
    medpay_request_date: string;
    medpay_deposit_date?: string;
    note?: string;
}

/**
 * Interface for updating a MedPay deposit
 */
export interface MedPayDepositUpdateRequest {
    insurance_type?: "CLIENT_MEDPAY" | "THIRD_PARTY_MEDPAY";
    client_insurance?: number;
    defendant_insurance?: number;
    medical_provider?: number;
    amount?: string;
    check_number?: string;
    medpay_request_date?: string;
    medpay_deposit_date?: string;
    note?: string;
}
/**
 * Interface for MedPay deposit summary
 */
export interface MedPayDepositSummary {
    total_requested: number;
    total_paid: number;
    pending_amount: number;
    deposit_count: number;
    client_insurance_deposits: number;
    defendant_insurance_deposits: number;
    paid_count: number;
    invoice_raised_count: number;
}

export interface MedpayRecovery {
    amount: number;
    insurance_name?: string;
    provider_name?: string;
    insurance_type?: string;
    client_responsibility?: boolean;
    id?: number;
}

export interface ThirdPartyRecovery {
    amount: number;
    company_name: string;
    client_responsibility?: boolean;
    id?: number;
}
