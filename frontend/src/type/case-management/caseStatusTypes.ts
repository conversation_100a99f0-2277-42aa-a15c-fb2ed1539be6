export enum CaseStatus {
  CASE_CREATED = "case_created",
  INTAKE = "intake",
  MEDICAL_DOCUMENTATION = "medical_documentation",
  DEMAND_WRITING = "demand_writing",
  INSURANCE_COMMUNICATION = "insurance_communication",
  NEGOTIATIONS = "negotiations",
  MEDIATION_ARBITRATION = "mediation_arbitration",
  COMPLAINTS_FILING = "complaints_filing",
  DISCOVERY = "discovery",
  DEPOSITIONS = "depositions",
  EXPERT_DISCOVERY = "expert_discovery",
  LITIGATION = "litigation",
  MOTION_TRACKER = "motion_tracker",
  TRIAL_PRACTICE = "trial_practice",
  TRIAL = "trial",
  POST_TRIAL = "post_trial",
  APPEAL = "appeal",
  SETTLEMENT = "settlement",
  DISBURSEMENT = "disbursement",
  ARCHIVED = "archived"
}

export const CaseStatusDisplay: Record<CaseStatus, string> = {
  [CaseStatus.CASE_CREATED]: "Case Created",
  [CaseStatus.INTAKE]: "Intake",
  [CaseStatus.MEDICAL_DOCUMENTATION]: "Medical Documentation",
  [CaseStatus.DEMAND_WRITING]: "Demand Writing",
  [CaseStatus.INSURANCE_COMMUNICATION]: "Insurance Communication/Demand Sent",
  [CaseStatus.NEGOTIATIONS]: "Negotiations",
  [CaseStatus.MEDIATION_ARBITRATION]: "Mediation/Arbitration",
  [CaseStatus.COMPLAINTS_FILING]: "Complaints Filing",
  [CaseStatus.DISCOVERY]: "Discovery",
  [CaseStatus.DEPOSITIONS]: "Depositions",
  [CaseStatus.EXPERT_DISCOVERY]: "Expert Discovery",
  [CaseStatus.LITIGATION]: "Litigation",
  [CaseStatus.MOTION_TRACKER]: "Motion Tracker",
  [CaseStatus.TRIAL_PRACTICE]: "Trial Practice",
  [CaseStatus.TRIAL]: "Trial",
  [CaseStatus.POST_TRIAL]: "Post Trial",
  [CaseStatus.APPEAL]: "Appeal",
  [CaseStatus.SETTLEMENT]: "Settlement",
  [CaseStatus.DISBURSEMENT]: "Disbursement",
  [CaseStatus.ARCHIVED]: "Archived"
};

export interface ChecklistItem {
  id: number;
  name: string;
  description: string;
  is_completed: boolean;
  completed_at?: string;
  completed_by?: string;
}

export interface ChecklistResponse {
  case_id: number;
  current_status: string;
  status_display: string;
  required_items: ChecklistItem[];
  optional_items: ChecklistItem[];
}

export interface StatusUpdateRequest {
  status: string;
}

export interface StatusUpdateResponse {
  detail: string;
  case_id: number;
  status: string;
  status_display: string;
}

export interface ChecklistItemUpdateRequest {
  item_id: number;
  is_completed: boolean;
}

export interface ChecklistItemUpdateResponse {
  detail: string;
  item_id: number;
  name: string;
  is_completed: boolean;
  completed_at: string | null;
  completed_by: string | null;
} 