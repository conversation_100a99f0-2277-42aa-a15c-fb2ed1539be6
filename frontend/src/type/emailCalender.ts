export interface NylasIntegration {
  id?: string;
  email: string;
  provider: string;
  is_active: boolean;
  created_at: string;
  calendars: NylasCalendar[];
  account_connected: boolean;
  grant_id: string;
  last_sync_time: string;
  scopes: string[];
  settings: NylasIntegrationSettings;
}

export interface NylasAuthResponse {
  status: string;
  email?: string;
  message?: string;
}

export interface NylasEmail {
  id: string;
  subject: string;
  snippet: string;
  body: string;
  date: number;
  sender: Array<{
    name: string;
    email: string;
  }>;
}

export interface Participant {
  email: string;
  name?: string;
  status?: string;
}

export interface NylasEvent {
  id: string;
  title?: string;
  description?: string;
  location?: string;
  start_time?: number; // Made optional for all-day events
  end_time?: number; // Made optional for all-day events
  start_date?: string; // Added for all-day events
  end_date?: string; // Added for all-day events
  is_all_day?: boolean; // Flag for all-day events
  start?: string | Date;
  end?: string | Date;
  participants?: Participant[];
  status?: string;
  calendar_id?: string;
  links?: string[];
  color?: string;
  case_id?: string;
  case_name?: string;
  event_type?: string;
  // Add the when property for Nylas API response format
  when?: {
    object: string; // 'timespan' for regular events, 'datespan' for all-day events
    start_time?: number;
    end_time?: number;
    start_date?: string;
    end_date?: string;
    start_timezone?: string;
    end_timezone?: string;
  };
}

export interface NylasContact {
  id: string;
  given_name: string;
  surname: string;
  email: string;
  phone_numbers?: Array<{
    number: string;
    type?: string;
  }>;
}

export interface NylasCalendar {
  id: string;
  name: string;
  description: string | null;
  is_primary: boolean;
  read_only: boolean;
  location: string | null;
  timezone: string | null;
}

export interface NylasIntegrationSettings {
  default_calendar_id: string;
  timezone: string | null;
}


export interface EmailMessage {
  id: string;
  subject: string;
  sender: {
    email: string;
    name?: string;
  };
  recipients: Array<{
    email: string;
    name?: string;
  }>;
  cc?: Array<{
    email: string;
    name?: string;
  }>;
  bcc?: Array<{
    email: string;
    name?: string;
  }>;
  date: number;
  snippet: string;
  body?: string;
  unread: boolean;
  starred: boolean;
  folder?: string;
  files?: Array<{
    id: string;
    filename: string;
    size: number;
    content_type: string;
    download_url?: string;
  }>;
}

export interface Label {
  id: string;
  name: string;
  path?: string;
  type: 'label' | 'folder';
  unread_count: number;
  total_count: number;
  color?: string;
}

export type EmailBaseFolder =
  | 'inbox'
  | 'sent'
  | 'drafts'
  | 'spam'
  | 'trash'
  | 'archive'
  | 'all'
  | 'starred';

export interface EmailListParams {
  page?: number;
  limit?: number;
  folder?: string;
  label_id?: string;
  search?: string;
  unread?: boolean;
  start_date?: string;
  end_date?: string;
}

export interface EmailListResponse {
  messages: EmailMessage[];
  page: number;
  limit: number;
  folder: string;
  total_count: number;
}

export interface SendEmailData {
  subject: string;
  body: string;
  to: string[] | Array<{ email: string; name?: string }>;
  cc?: string[] | Array<{ email: string; name?: string }>;
  bcc?: string[] | Array<{ email: string; name?: string }>;
  attachments?: File[];
  case_id?: string;
}

export interface Label {
  id: string;
  name: string;
  display_name: string;
  system_folder: boolean;
  unread_count: number;
  total_count: number;
  color?: string;
}

export interface CreateLabelRequest {
  name: string;
  color?: string;
}

export interface ApplyLabelRequest {
  label_id: string;
}

export interface ApplyLabelResponse {
  success: boolean;
  message_id: string;
  labels: string[];
}


export interface AgendaEvent extends NylasEvent {
  links?: string[];
}

export interface AgendaDay {
  date: string;
  day_name: string;
  is_today: boolean;
  events: AgendaEvent[];
}

export interface AgendaResponse {
  current_date: string;
  start_date: string;
  end_date: string;
  days: AgendaDay[];
  integration_status?: 'not_connected' | 'error' | 'connected';
  message?: string;
  error?: string;
}

export interface EventFormData {
  title: string;
  description?: string; // Made optional
  location?: string; // Made optional
  start_time?: number; // Optional for all-day events
  end_time?: number; // Optional for all-day events
  start_date?: string; // For all-day events
  end_date?: string; // For all-day events
  is_all_day?: boolean; // Flag for all-day events
  participants: Participant[];
  calendar_id?: string;
  case_id?: string;
  case_name?: string; // Added for fallback display
  event_type?: string;
}

export interface EmailSignature {
  id?: string;
  name: string;
  content: string;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
}