import { AdjusterContact, DateString } from '@/type/case-management/commonTypes';
import { UserDetail } from '@/type/case-management/commonTypes';
import { Defendant } from '@/type/case-management/defendantTypes';
import { ClientInsurance, InsuranceCompany } from './case-management/clientDetailTypes';

// Enums
export enum DialogNegotiationType {
    INITIAL_DEMAND = "INITIAL_DEMAND",
    COUNTER_DEMAND = "COUNTER_OFFER",
}

export enum NegotiationType {
    INITIAL_DEMAND = "INITIAL_DEMAND",
    COUNTER_DEMAND = "COUNTER_DEMAND",
    OFFER = "OFFER",
    ACCEPTED_OFFER = "ACCEPTED_OFFER",
    COUNTER_OFFER = "COUNTER_OFFER",
}

export enum NegotiationStatus {
    SENT = "SENT",
    RECEIVED = "RECEIVED"
}

// Base interface for common properties
interface NegotiationBase {
    defendant?: number | null;
    client_insurance?: number | null;
    type: NegotiationType;
    amount: string;
    // date: DateString;
    status: NegotiationStatus;
    notes?: string;
    date_sent?: DateString;
    response_deadline?: DateString;
    response_received_date?: DateString;
    extension_date?: DateString;
    settlement_terms?: string;
    payment_terms?: string;
    conditions?: string;
    release_terms?: string;
    previous_offer?: number;
    insurance_company: number;
    accepted_date?: DateString;
}

// Write request interface
export interface NegotiationRead {
    id: number;
    adjuster: AdjusterContact;
    defendant?: Defendant;
    client_insurance?: ClientInsurance;
    amount: number | string;
    assigned_to: UserDetail | null;
    conditions: string | null;
    extension_date: string | null;
    date_sent: string | null;
    notes: string | null;
    payment_terms: string | null;
    previous_offer: number | null;
    release_terms: string | null;
    response_deadline: string | null;
    response_received_date: string | null;
    settlement_terms: string | null;
    status: string;
    status_display: string;
    type: string;
    type_display: string;
    created_at: DateString;
    updated_at: DateString;
    insurance_company: InsuranceCompany;
    is_archived: boolean;
    accepted_date: DateString | null;
}

export interface NegotiationWrite extends NegotiationBase {
    id?: number;
    adjuster?: number;
}

// List response interface
export interface NegotiationListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: NegotiationRead[];
}

// Query parameters interface
export interface NegotiationQueryParams {
    defendant?: number;
    client_insurance?: number;
    type?: NegotiationType;
    status?: NegotiationStatus;
    assigned_to?: string;
    search?: string;
    ordering?: string;
}

export interface NegotiationProps {
  caseId: string;
}

// Add new interface for partial update
export interface NegotiationUpdateRequest {
    defendant?: number;
    type?: NegotiationType;
    status?: NegotiationStatus;
    notes?: string;
    amount?: string;
    date_sent?: DateString;
    response_deadline?: DateString;
    response_received_date?: DateString;
    extension_date?: DateString;
    settlement_terms?: string;
    payment_terms?: string;
    conditions?: string;
    release_terms?: string;
    insurance_company?: number;
    is_archived?: boolean;
}

// UIM Negotiation Types
export interface NegotiationUIMRead {
    id: number;
    client_insurance: ClientInsurance;
    type: NegotiationType;
    type_display: string;
    amount: string;
    status: NegotiationStatus;
    status_display: string;
    notes: string | null;
    date_sent: DateString | null;
    response_deadline: DateString | null;
    response_received_date: DateString | null;
    extension_date: DateString | null;
    settlement_terms: string | null;
    payment_terms: string | null;
    conditions: string | null;
    release_terms: string | null;
    previous_offer: number | null;
    assigned_to: UserDetail;
    created_at: DateString;
    updated_at: DateString;
    created_by: UserDetail;
    adjuster: AdjusterContact | null;
    insurance_company: InsuranceCompany;
    is_archived: boolean;
}

export interface NegotiationUIMWrite {
    client_insurance: number;
    type: NegotiationType;
    amount: string;
    status: NegotiationStatus;
    notes?: string;
    date_sent?: DateString;
    response_deadline?: DateString;
    response_received_date?: DateString;
    extension_date?: DateString;
    settlement_terms?: string;
    payment_terms?: string;
    conditions?: string;
    release_terms?: string;
    previous_offer?: number;
    assigned_to?: number;
    adjuster?: number;
    insurance_company?: number;
}

export interface NegotiationUIMUpdateRequest {
    client_insurance?: number;
    type?: NegotiationType;
    status?: NegotiationStatus;
    notes?: string;
    amount?: string;
    date_sent?: DateString;
    response_deadline?: DateString;
    response_received_date?: DateString;
    extension_date?: DateString;
    settlement_terms?: string;
    payment_terms?: string;
    conditions?: string;
    release_terms?: string;
    assigned_to?: number;
    adjuster?: number;
    insurance_company?: number;
    is_archived?: boolean;
}
