// First, let's create a types file for shared interfaces
export interface Message {
    role: 'user' | 'assistant';
    content: string;
}

export interface CategoryMapping {
    documents: string[];
    count: number;
}

export interface CategoryDocumentMapping {
    [key: string]: CategoryMapping;
}

export interface ChatMessagesProps {
    chatMessages: Message[];
    isStreaming: boolean;
    currentMessage: string | null;
    messagesEndRef: React.RefObject<HTMLDivElement>;
}

export interface ChatMessageProps {
    msg: Message;
}

export interface AvatarComponentProps {
    src: string;
    alt: string;
}

export interface StreamingMessageProps {
    currentMessage: string;
}

export interface MessageInputProps {
    message: string;
    setMessage: (message: string) => void;
    handleSubmit: (e: React.FormEvent) => void;
    isExceeded: boolean;
    maxLength: number;
    isDisabled: boolean;
}

export interface CaseChatProps {
    caseId: string;
}


export interface CaseViewState {
    allDocuments: Array<{
        file_name: string;
        // ... other document properties
    }>;
    selectedCaseDetail: {
        name_of_client?: string;
        accident_date?: string;
        // ... other case detail properties
    } | null;
}

export interface CaseChatState {
    messages: {
        [key: string]: Message[];
    };
    currentMessage: string | null;
    isStreaming: boolean;
}