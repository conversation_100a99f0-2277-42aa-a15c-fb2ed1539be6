// src/types/extraction.ts

export interface TreatmentExpenseEntry {
    hospital_name: string;
    doctor_name: string;
    treatment_period: string;
    amount_charged: string | number;
}

export interface TreatmentExpenseData {
    treatment_expense: {
        treatment_expenses: TreatmentExpenseEntry[];
        total_amount_charged?: string | number;
        supporting_documents: string[];
    };
}







// ===================================
// src/types/extraction.ts

export interface MedicalAnalysisData {
    treatment_timeline: string[] | string;
    number_of_visits: string | number;
    hospital_name: string;
    doctor_name: string;
    summary: string | null;
}

export interface TreatmentExpenseEntry {
    hospital_name: string;
    doctor_name: string;
    treatment_period: string;
    amount_charged: string | number;
}

export interface LossOfEarningsData {
    employment_status: string;
    employer_name: string;
    job_title: string;
    salary: number | string;
    work_hours: number | string;
    time_off_work: string;
    income_loss: number | string;
    supporting_documents?: string[];
}

export interface IncidentDetailsData {
    incident_date: string;
    incident_location: string;
    incident_description: string;
    injuries_sustained: string[];
    witness_statements?: string[];
    police_report?: boolean;
    police_report_number?: string;
    supporting_documents?: string[];
}

export interface FutureTreatmentEntry {
    treatment_type: string;
    provider: string;
    estimated_cost: string | number;
    frequency: string;
    duration: string;
    medical_necessity: string;
}

export interface FutureTreatmentData {
    recommended_treatments: FutureTreatmentEntry[];
    total_estimated_cost: string | number;
    supporting_documents?: string[];
}

// Request/Response Types
export interface AnalysisRequestParams {
    caseId: string;
    fileNames: string[];
    accidentDate: string;
}


// Component Props Types
export interface ExtractionAIAnalysisProps {
    caseId: string;
    accidentDate: string;
}

export interface MedicalAnalysisProps {
    caseId: string;
    accidentDate: string;
    data: Record<string, MedicalAnalysisData>;
    onAnalysisStart?: () => void;
    onDataUpdate?: (data: Record<string, MedicalAnalysisData>) => void;
}

export interface TreatmentExpenseAnalysisProps {
    caseId: string;
    accidentDate: string;
    data: Record<string, TreatmentExpenseData>;
    onAnalysisStart?: () => void;
    onDataUpdate?: (data: Record<string, TreatmentExpenseData>) => void;
}

export interface LossOfEarningsAnalysisProps {
    caseId: string;
    accidentDate: string;
    data: Record<string, LossOfEarningsData>;
    onAnalysisStart?: () => void;
    onDataUpdate?: (data: Record<string, LossOfEarningsData>) => void;
}

export interface IncidentDetailsAnalysisProps {
    caseId: string;
    accidentDate: string;
    data: Record<string, IncidentDetailsData>;
    onAnalysisStart?: () => void;
    onDataUpdate?: (data: Record<string, IncidentDetailsData>) => void;
}

export interface FutureTreatmentAnalysisProps {
    caseId: string;
    accidentDate: string;
    data: Record<string, FutureTreatmentData>;
    onAnalysisStart?: () => void;
    onDataUpdate?: (data: Record<string, FutureTreatmentData>) => void;
}

// Common Component Props
export interface AnalysisHeaderProps {
    title: string;
    isLoading: boolean;
    onAnalysisStart: () => void;
}


// Analysis Status
export type AnalysisStatus =
    | 'idle'
    | 'loading'
    | 'processing'
    | 'completed'
    | 'error';

// File Categories
export type FileCategory =
    | 'Treatment detail'
    | 'Treatment expense'
    | 'Loss of earnings'
    | 'Incident detail'
    | 'Future treatment'
    | 'Demand questionaire';

export interface FileCategoryMapping {
    [category: string]: string[];
}

// Document Processing Status
export interface DocumentStatus {
    fileName: string;
    category: FileCategory;
    status: AnalysisStatus;
    lastUpdated: string;
    error?: string;
}

// Context Types
export interface ExtractionContext {
    selectedCase: string;
    accidentDate: string;
    documentMapping: FileCategoryMapping;
    updateDocumentMapping: (mapping: FileCategoryMapping) => void;
    refreshAnalysis: () => void;
}

interface FileAnalysis {
    treatment_detail: string; // JSON string that parses to TreatmentDetail
}

export interface MedicalAnalysisResponse {
    [fileName: string]: FileAnalysis;
}