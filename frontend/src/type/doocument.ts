export interface DocumentMetadata {
    document: number;
    case_reference: string;
    document_name: string;
    is_part_of_demand_package: boolean;
    is_part_of_case_with_chat: boolean;
    is_text_extraction_completed: boolean;
    is_record_ai_completed: boolean;
    is_indexed: boolean;
    upload_date: string;
    view_url: string | null;
    download_url: string;
    is_viewable: boolean;
}


export interface FolderDocumentResponse {
    id: number;
    case_id: string;
    file_name: string;
    s3_key: string;
    file_size: number;
    content_type: string;
    upload_date: string;
    status: string;
    view_url: string | null;
    download_url: string;
    is_viewable: boolean;
    categories: FolderCategoryInfo[];
}

export interface FolderCategoryInfo {
    id: number;
    category: string;
    created_at: string;
    updated_at: string;
}

export interface FolderOperationResponse {
    message: string;
}

export interface FolderDeleteRequest {
    case_id: string;
    category: string;
    filename: string;
}

export interface DocumentFile {
    id: number;
    case_id: string;
    file_name: string;
    s3_key?: string;
    file_size: number;
    content_type: string;
    upload_date: string;
    updated_at: string;
    status: 'COMPLETED' | 'PENDING' | 'FAILED';
    view_url: string | null;
    download_url: string;
    is_viewable: boolean;
    uploaded_by: number;
    is_indexed: boolean;
    is_part_of_demand_package: boolean;
    is_part_of_case_with_chat: boolean;
    is_demand_file: boolean;
    is_text_extraction_completed: boolean;
    is_record_ai_completed: boolean;
}


export interface DocumentCategory {
    id: number;
    document: number;
    category: string;
    category_display: string;
    created_at: string;
    updated_at: string;
}

export interface DocumentWithCategories {
    id: number;
    case_id: string;
    file_name: string;
    upload_date: string;
    categories: DocumentCategory[];
}

export interface CategorizeFilesRequest {
    case_id: string;
    all_files_names: string[];
    documentsWithCategories: DocumentWithCategories[];
    accident_date: string;
}

export interface DocumentCategoryResponse {
    message: string;
    categories?: DocumentCategory[];
}


export interface DocumentUpdateResponse {
    message: string;
    data: DocumentMetadata; // Adjust the type based on the actual response structure
}

export interface BulkDocumentUpdateResponse {
    message: string;
    updated_documents: {
        document_name: string;
        metadata: DocumentMetadata;
    }[];
}

export interface Document {
    name: string;
    url: string;
    id: string;
    content_type: string;
    
}

export interface CategorizeFilesBackgroundRequest {
    case_id: string;
    all_files_names: string[];
    documentsWithCategories: DocumentWithCategories[];
    accident_date: string;
    state_of_incident: string;
    case_type: string;
}
