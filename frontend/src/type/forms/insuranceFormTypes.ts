import { z } from "zod";
import { UmUimLimit, MedpayLimit, PipLimit, CoverageStatus, LiabilityStatus, PolicyType, PlanType } from '@/constants/insurance';

// Define the validation schema
export const insuranceFormSchema = z.object({
    no_insurance: z.boolean().default(false),
    insurance_company: z.string().optional(),
    plan_type: z.nativeEnum(PlanType).optional(),
    claim_number: z.string().optional(),
    policy_number: z.string().optional(),
    medpay_claim_number: z.string().optional(),
    insured_name: z.string().optional(),
    um_uim: z.nativeEnum(UmUimLimit).optional(),
    medpay: z.nativeEnum(MedpayLimit).optional(),
    pip: z.nativeEnum(PipLimit).optional(),
    deductible: z.string().optional(),
    coverage_status: z.nativeEnum(CoverageStatus).optional(),
    liability_status: z.nativeEnum(LiabilityStatus).optional(),
    liability_type: z.string().optional(),
    policy_type: z.nativeEnum(PolicyType).optional(),
    stacked: z.string().optional(),
    vehicles: z.string().optional(),
    claim_note: z.string().optional(),
    incident_date: z.string().optional(),
    group_number: z.string().optional(),
    member_number: z.string().optional(),
}).refine((data) => {
    // Custom validation: insurance_company is required when no_insurance is false
    if (!data.no_insurance && !data.insurance_company) {
        return false;
    }
    return true;
}, {
    message: "Insurance company is required when No Insurance is not checked",
    path: ["insurance_company"]
});

export type InsuranceFormData = z.infer<typeof insuranceFormSchema>;

export const getDefaultInsuranceFormValues = (): InsuranceFormData => ({
    no_insurance: false,
    insurance_company: undefined,
    plan_type: undefined,
    claim_number: undefined,
    policy_number: undefined,
    medpay_claim_number: undefined,
    insured_name: undefined,
    um_uim: undefined,
    medpay: undefined,
    pip: undefined,
    deductible: undefined,
    coverage_status: undefined,
    liability_status: undefined,
    liability_type: undefined,
    policy_type: undefined,
    stacked: undefined,
    vehicles: undefined,
    claim_note: undefined,
    incident_date: undefined,
    group_number: undefined,
    member_number: undefined,
});

export const insuranceCompanyFormSchema = z.object({
    name: z.string(),
    payee: z.string().optional(),
    tax_id: z.string().optional(),
    phone: z.string().optional(),
    phone_ext: z.string().optional(),
    cell: z.string().optional(),
    fax: z.string().optional(),
    email: z.string().optional(),
    website: z.string().optional(),
    street1: z.string().optional(),
    street2: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip_code: z.string().optional(),
    note: z.string().optional(),
});

export type InsuranceCompanyFormData = z.infer<typeof insuranceCompanyFormSchema>; 