import { LiabilityStatus, CoverageStatus, PolicyType, MedpayLimit, PipLimit, PlanType, UmUimLimit } from "@/constants/insurance";
import { HealthInsuranceResponse } from "../case-management/healthInsuranceTypes";
import { SubrogationContact } from "../case-management/orgTypes";

export interface LeadStatistics {
    total_leads: number;
    status_breakdown: Array<{ status: string; count: number }>;
    source_breakdown: Array<{ lead_source: string; count: number }>;
    recent_leads: number;
    conversion_rate: number;
    previous_conversion_rate: number;
}


export interface LeadQueryParams {
    status?: string;
    lead_source?: string;
    assignee?: string;
    search?: string;
    ordering?: string;
    page?: number;
    page_size?: number;
}


export interface Lead {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    address: string;
    received_date: string; // date-time
    date_entered: string; // date-time
    status: LeadStatus;
    lead_source: LeadSource;
    assignee?: number;
    assignee_name?: string;
    converted_case?: string | null;
    source_type?: string;
    source_detail?: string;
    referred_by?: string;
    linked_leads?: number;

    // Incident Details
    incident_date: string; // date
    incident_type: string;
    incident_location: string;
    incident_description: string;
    weather_conditions: string;
    police_report_details: string;
    witness_information: string;
    insurance_status: boolean;
    previous_attorney: string;
    medical_status: MedicalStatus;

    // Injury Information
    injury_type: string;
    injury_location: string;
    affected_areas: Record<string, string>;
    surgery_required: boolean;
    critical_conditions: string;

    // Case Details
    basic_facts: string;
    conflicts_checked: boolean;
    liability_assessment: string;
    estimated_value?: string;
    next_follow_up?: string; // date-time
    statute_of_limitations?: string; // date
    notes: string;
    documents?: LeadDocument[];
    first_name_key_contact?: string;
    last_name_key_contact?: string;
    phone_key_contact?: string;
    email_key_contact?: string;
    relationship_key_contact?: string;
    social_security_number_key_contact?: string;
    emergency_contact_first_name?: string;
    emergency_contact_last_name?: string;
    emergency_contact_phone?: string;
    emergency_contact_relationship?: string;
    emergency_contact_email?: string;
    street1?: string | null;
    street2?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
    date_of_birth?: string | null;
    client_ssn?: string | null;
}



export type LeadStatus = 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'REJECTED' | 'CONVERTED' | 'LOST' | 'CASE_CREATED';

export type LeadSource = 'WEBSITE' | 'REFERRAL' | 'SOCIAL' | 'PHONE' | 'EMAIL' | 'OTHER';

export type MedicalStatus = 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'OTHER';

export const LEAD_SOURCE_CHOICES = [
    { value: "WEBSITE", label: "Website" },
    { value: "REFERRAL", label: "Referral" },
    { value: "SOCIAL", label: "Social Media" },
    { value: "PHONE", label: "Phone" },
    { value: "EMAIL", label: "Email" },
    { value: "OTHER", label: "Other" },
] as const;

export const LEAD_STATUS_CHOICES = [
    { value: "NEW", label: "New" },
    { value: "CONTACTED", label: "Contacted" },
    { value: "QUALIFIED", label: "Qualified" },
    { value: "CONVERTED", label: "Converted" },
    { value: "LOST", label: "Lost" },
    { value: "REJECTED", label: "Rejected" },
    { value: "CASE_CREATED", label: "Case Created" },
] as const;


export const INCIDENT_TYPE_CHOICES = [
    { value: "AUTO_ACCIDENT", label: "Auto Accident" },
    { value: "SLIP_/_FALL", label: "Slip and Fall" },
    { value: "MEDICAL_MALPRACTICE", label: "Medical Malpractice" },
    { value: "WORKPLACE_INJURY", label: "Workplace Injury" },
    { value: "PRODUCT_LIABILITY", label: "Product Liability" },
    { value: "WRONGFUL_DEATH", label: "Wrongful Death" },
    { value: "PEDESTRIAN", label: "Pedestrian" },
    { value: "CONSORTIUM", label: "Consortium" },
    { value: "MOTORCYCLE", label: "Motorcycle" },
    { value: "WORKERS_COMP", label: "Workers Comp" },
    { value: "PERSONAL_INJURY", label: "Personal Injury" },
    { value: "BICYCLE", label: "Bicycle" },
    { value: "DOG_BITE", label: "Dog Bite" },
    { value: "PREMISES_LIABILITY", label: "Premises Liability" },
    { value: "CRIMINAL", label: "Criminal" },
    { value: "EMPLOYMENT", label: "Employment" },
    { value: "COMMERCIAL", label: "Commercial" },
    { value: "ASSAULT", label: "Assault" },
    { value: "ELECTRIC_SCOOTER", label: "Electric Scooter" },
    { value: "NEGLIGENCE", label: "Negligence" },
    { value: "PROPERTY_DAMAGE", label: "Property Damage" },
    { value: "DISCRIMINATION", label: "Discrimination" },
    { value: "SEXUAL_ASSAULT", label: "Sexual Assault" },
    { value: "DUI", label: "DUI" },
    { value: "IMMIGRATION", label: "Immigration" },
    { value: "FAMILY", label: "Family" },
    { value: "BUSINESS_INTERRUPTION", label: "Business Interruption" },
    { value: "OTHER", label: "Other" },
] as const;

export const MEDICAL_STATUS_CHOICES = [
    { value: "ACTIVE", label: "Active" },
    { value: "INACTIVE", label: "Inactive" },
    { value: "PENDING", label: "Pending" },
    { value: "OTHER", label: "Other" },
] as const;

export type MedicalStatusLink = {
    value: string;
    label: string;
}

export const MEDICAL_STATUS_LINK: MedicalStatusLink[] = [
    { value: "ongoing", label: "Ongoing" },
    { value: "completed", label: "Completed" },
    { value: "pending", label: "Pending" },
] as const;


export type LeadFormData = {
    first_name: string;
    last_name: string;
    email?: string;
    phone?: string;
    lead_source?: LeadSource;
    incident_date?: string;
    incident_type?: string;
    incident_location?: string;
    medical_status?: MedicalStatus;
    previous_attorney?: string;
    insurance_status?: boolean;
}


export interface BasicInfoUpdate {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    lead_source: LeadSource;
    address: string;
    lead?: string | null;
    first_name_key_contact?: string | null;
    last_name_key_contact?: string | null;
    phone_key_contact?: string | null;
    email_key_contact?: string | null;
    relationship_key_contact?: string | null;
    social_security_number_key_contact?: string | null;
}


export interface IncidentSummaryUpdate {
    incident_date: string | null; // date
    incident_type: string;
    street1: string | null;
    street2?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
    medical_status: MedicalStatus;
    previous_attorney: string;
    insurance_status: boolean;
    incident_location: string | null;
}































export type JSONValue =
    | string
    | number
    | boolean
    | null
    | JSONValue[]
    | { [key: string]: JSONValue };


export interface PaginatedLeadResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: Lead[];
}

export interface Document {
    id: number;
    lead: string;
    document_type: string;
    file_name: string;
    notes: string;
    uploaded_at: string;
    status: string;
}

export interface DocumentUpload {
    file: File;
    document_type: string;
    lead: string;
    notes?: string;
}

export interface Activity {
    id: number;
    title: string;
    message: string;
    notification_type: string;
    date: string;
}

export interface LeadActivitiesResponse {
    count: number;
    next: string | null;
    previous: string | null;
    total_activities: number;
    last_activity: Activity;
    activities: Activity[];
}

export interface LeadEvaluation {
    liability_assessment: string;
    estimated_value: string;
}


export interface IncidentInfoUpdate {
    incident_description: string;
    weather_conditions: string;
    police_report_details?: string;
    witness_information?: string;
}

export interface StatuteUpdate {
    basic_facts?: string;
    conflicts_checked?: boolean;
    statute_of_limitations?: string; // date
}

export interface InjuryAssessmentUpdate {
    injury_type?: string;
    injury_location?: string;
    affected_areas?: Record<string, string | boolean>;
    surgery_required?: boolean;
    critical_conditions?: string;
}

export interface ConvertToCaseResponse {
    message: string;
    case_id: string;
    status: string;
}

export interface LeadPartialUpdate {
    id?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
    address?: string;
    lead_source?: LeadSource;
    source_type?: string;
    source_detail?: string;
    incident_date?: string; // date
    incident_type?: string;
    incident_location?: string;
    incident_description?: string;
    weather_conditions?: string;
    police_report_details?: string;
    witness_information?: string;
    insurance_status?: boolean;
    previous_attorney?: string;
    medical_status?: MedicalStatus;
    injury_type?: string;
    injury_location?: string;
    affected_areas?: Record<string, string>;
    surgery_required?: boolean;
    critical_conditions?: string;
    basic_facts?: string;
    conflicts_checked?: boolean;
    liability_assessment?: string;
    estimated_value?: string;
    status?: LeadStatus;
    assignee?: string;
    next_follow_up?: string; // date-time
    statute_of_limitations?: string; // date
    notes?: string;
}

export interface LeadDocument {
    id: string;
    document_type: DocumentType;
    notes?: string;
    status: DocumentStatus;
    lead: string;
}

export interface InsuranceCompany {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    fax?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    payee?: string;
    tax_id?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface LeadHealthInsurance {
    id?: number;
    lead: string;
    no_insurance: boolean;
    insurance_company: InsuranceCompany;
    subrogation_company?: InsuranceCompany;
    representative?: SubrogationContact;
    insured?: string;
    group_number?: string;
    member_number?: string;
    policy_number?: string;
    total_lien?: string;
    adjusted_lien?: string;
    file_number?: string;
    plan_type?: string;
    erisa?: string;
    medicare?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface ExtendedHealthInsuranceResponse extends HealthInsuranceResponse {
    isEditing?: boolean;
  }

export interface LeadHealthInsuranceCreateRequest {
    lead: string;
    no_insurance: boolean;
    insurance_company?: number;
    subrogation_company?: number;
    representative?: number;
    insured?: string;
    group_number?: string;
    member_number?: string;
    policy_number?: string;
    total_lien?: string;
    adjusted_lien?: string;
    file_number?: string;
    plan_type?: string;
    erisa?: string;
    medicare?: string;
    note?: string;
    final_lien?: boolean;
}

export interface LeadHealthInsuranceUpdateRequest extends LeadHealthInsuranceCreateRequest {
    id: number;
}

export interface LeadHealthInsuranceListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: LeadHealthInsurance[];
}

export type DocumentType = 
    | 'ACCIDENT_REPORT'
    | 'MEDICAL_RECORD' 
    | 'INSURANCE'
    | 'PHOTOS_EVIDENCE'
    | 'ID'
    | 'POLICE_REPORT'
    | 'WITNESS_REPORT'
    | 'COMMUNICATION'
    | 'OTHER';

export type DocumentStatus = 
    | 'PENDING'
    | 'PROCESSING'
    | 'COMPLETED'
    | 'FAILED'
    | 'MISSING'
    | 'EXTRACTING_TEXT'
    | 'INDEXING'
    | 'FAILED_TO_EXTRACT_TEXT'
    | 'FAILED_TO_INDEX';

export interface LeadClientInsurance {
    id: number;
    lead: number;
    no_insurance: boolean;
    insurance_company?: number | { id: number; name: string };
    plan_type?: PlanType;
    claim_number?: string;
    policy_number?: string;
    medpay_claim_number?: string;
    insured_name?: string;
    um_uim?: UmUimLimit;
    medpay?: MedpayLimit;
    pip?: PipLimit;
    deductible?: string;
    coverage_status?: CoverageStatus;
    liability_status?: LiabilityStatus;
    liability_type?: string;
    policy_type?: PolicyType;
    stacked?: string;
    vehicles?: string;
    claim_note?: string;
    incident_date?: string;
    group_number?: string;
    member_number?: string;
}

export interface LeadClientInsuranceCreateRequest {
    lead: number;
    insurance_company?: number;
    no_insurance: boolean;
    plan_type?: string;
    claim_number?: string;
    policy_number?: string;
    medpay_claim_number?: string;
    insured_name?: string;
    um_uim?: string;
    medpay?: string;
    pip?: string;
    deductible?: string;
    coverage_status?: string;
    liability_status?: string;
    liability_type?: string;
    policy_type?: string;
    stacked?: string;
    vehicles?: string;
    claim_note?: string;
}

export interface LeadClientInsuranceUpdateRequest extends LeadClientInsuranceCreateRequest {
    id: number
}

export interface LeadClientInsuranceListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: LeadClientInsurance[];
}