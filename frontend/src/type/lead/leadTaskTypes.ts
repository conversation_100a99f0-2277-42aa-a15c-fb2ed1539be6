import { CaseNoteTag } from "@/services/organizationService";

export enum TaskStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  REOPENED = "reopened"
}

export interface TaskStatusConfig {
  [key: string]: {
    label: string;
    nextStatus: TaskStatus[];
    color: string;
  };
}

export const TaskStatusConfig: TaskStatusConfig = {
  pending: {
    label: 'Pending',
    nextStatus: [TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED],
    color: 'gray'
  },
  in_progress: {
    label: 'In Progress',
    nextStatus: [TaskStatus.COMPLETED, TaskStatus.REOPENED],
    color: 'blue'
  },
  completed: {
    label: 'Completed',
    nextStatus: [TaskStatus.REOPENED],
    color: 'green'
  },
  reopened: {
    label: 'Reopened',
    nextStatus: [TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED],
    color: 'red'
  }
};

export interface UserDetail {
  id: number;
  first_name?: string;
  last_name?: string;
  email: string;
}

export interface Task {
  id: number;
  lead: string;
  title: string;
  description?: string;
  status: TaskStatus;
  assigned_to?: UserDetail;
  tagged_users: UserDetail[];
  due_date?: string;
  created_at: string;
  updated_at: string;
}

export interface TaskCreate {
  lead: string;
  title: string;
  description?: string;
  status?: TaskStatus;
  assigned_to?: string;
  assigned_to_id?: number;
  tagged_user_ids?: number[];
  due_date?: string;
}

export interface ListResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface LeadTaskCreate {
  lead: string;
  title: string;
  description?: string;
  status?: TaskStatus;
  assigned_to?: string;
  assigned_to_id?: number;
  tagged_user_ids?: number[];
  due_date?: string;
}

export interface LeadNote {
  id: number;
  lead: number;
  title: string;
  content: string;
  created_by: UserDetail;
  tagged_users: UserDetail[];
  tagged_user_ids?: number[];
  created_at: string;
  updated_at: string;
  tags: CaseNoteTag[];
}

export interface LeadNoteCreate {
  lead: number;
  title: string;
  content: string;
  tagged_user_ids?: number[];
  tag_ids?: number[];
}

export interface Activity {
  id: number;
  title: string;
  message: string;
  notification_type:
    | "case_update"
    | "task_assigned"
    | "task_updated"
    | "task_created"
    | "task_status_changed"
    | "task_tagged"
    | "note_created"
    | "note_tagged"
    | "case_status_changed"
    | "org_update"
    | "system_update"
    | "deadline_reminder"
    | "document_uploaded"
    | "permission_changed"
    | "conflict_detected"
    | "lead_status_changed"
    | "lead_assigned"
    | "lead_note_created"
    | "lead_task_created"
    | "lead_task_status_changed"
    | "lead_task_tagged"
    | "lead_communication_added";
  created_at: string;
  level: "system" | "organization" | "case" | "lead";
  is_activity: boolean;
  content_object: string;
  case_id?: string;
  organization_id: string;
}

export interface ActivityListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Activity[];
}