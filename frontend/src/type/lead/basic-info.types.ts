export interface BasicInfoProps {
    id?: number;
    name: string;
    email: string;
    phone: string;
    address?: string;
    received_date?: string;
    status?: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'REJECTED' | 'CONVERTED' | 'LOST' | 'CASE_CREATED';
    lead_source: 'WEBSITE' | 'REFERRAL' | 'SOCIAL' | 'PHONE' | 'EMAIL' | 'OTHER';
    assignee?: number;
    assignee_name?: string;
    incident_date: string;
    incident_type: string;
    incident_location: string;
    insurance_status?: boolean;
    liability_assessment?: string;
    estimated_value?: string;
    statute_of_limitations?: string;
    next_follow_up?: string;
    notes?: string;
    basic_facts?: string;
    conflicts_checked?: boolean;
    incident_description?: string;
    weather_conditions?: string;
    police_report_details?: string;
    witness_information?: string;
    injury_type?: string;
    injury_location?: string;
    surgery_required?: boolean;
    critical_conditions?: string;
    previous_attorney?: string;
    medical_status?: 'UNTREATED' | 'TREATING' | 'RELEASED' | 'UNKNOWN';
    isEditableComponent?: boolean;
    // Updated Key Contact Fields
    key_contact_first_name?: string;
    key_contact_last_name?: string;
    key_contact_phone?: string;
    key_contact_email?: string;
    key_contact_relationship?: string;
    key_contact_social_security_number?: string;
    emergency_contact_first_name?: string;
    emergency_contact_last_name?: string;
    emergency_contact_phone?: string;
    emergency_contact_email?: string;
    emergency_contact_relationship?: string;
    emergency_contact_social_security_number?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    isReadOnly?: boolean;
    date_of_birth?: string;
    client_ssn?: string;
    source_type?: string;
    source_details?: string;
    referred_by?: string;
    caseId?: string | number;
}

export interface BasicInfoFormData {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    address: string;
    lead_source: 'WEBSITE' | 'REFERRAL' | 'SOCIAL' | 'PHONE' | 'EMAIL' | 'OTHER';
    date_entered: string;
    first_name_key_contact: string | null;
    last_name_key_contact: string | null;
    phone_key_contact: string | null;
    email_key_contact: string | null;
    relationship_key_contact: string | null;
    social_security_number_key_contact: string | null;
    street1: string;
    street2: string;
    city: string;
    state: string;
    zip_code: string;
    date_of_birth: string;
    client_ssn: string;
    insurance_status: boolean;
}

// Field validation constraints
export const FIELD_CONSTRAINTS = {
    first_name: { maxLength: 100, minLength: 1 },
    last_name: { maxLength: 100, minLength: 1 },
    email: { maxLength: 254, minLength: 1 },
    phone: { maxLength: 15, minLength: 1 },
    incident_type: { maxLength: 100, minLength: 1 },
    incident_location: { minLength: 1 },
} as const;