export interface DocumentHeader {
    title: string;
    date: string;
    recipient: string;
    phone: string;
    insured: string;
    driver: string;
    client: string;
    lossDate: string;
    claimNo: string;
}

export interface DocumentData {
    header?: DocumentHeader;
    introduction_section: string;
    fact_and_liability_section: string;
    injuries_and_treatments_section: InjuriesAndTreatmentsSection;
    damages_section: DamagesSection;
    demand_to_settle_section: DemandToSettleSection;
}

export interface InjuriesAndTreatmentsSection {
    chronological_data: ChronologicalData;
    disease_info_data: { [key: string]: string };
}

export interface ChronologicalData {
    treatments: Treatment[];
    status?: string;
}

export interface Treatment {
    id: string;
    hospital_name: string;
    timeline: string;
    num_of_visits: number;
    doctor_name: string;
    summary: string;
    documents: string[];
    facility: string;
}

export interface DamagesSection {
    loss_of_household_services: LossOfHouseholdServices;
    loss_of_wages: LossOfWages;
    pain_and_suffering: PainAndSuffering;
    treatment_expenses: { [key: string]: TreatmentExpense };
    future_treatments: FutureTreatment[];
}

export interface LossOfHouseholdServices {
    table: LossOfHouseholdServicesTable;
    summary: {report: string};
}

export interface LossOfHouseholdServicesTable {
    table: LossOfHouseholdServicesEntry[];
}

export interface LossOfHouseholdServicesEntry {
    start_date: string;
    end_date: string;
    hourly_rate: string;
    hours_per_day: string;
    impairment_percentage: string;
    total_loss_amount: string;
}

export interface LossOfWages {
    table: LossOfWagesTable;
    summary: string | {
        report: string;
    };
}

export interface LossOfWagesTable {
    table: LossOfWagesEntry[];
}

export interface LossOfWagesEntry {
    start_date: string;
    end_date: string;
    hourly_rate: string;
    total_hours: string;
    total_loss_amount: string;
}

export interface PainAndSuffering {
    initial: PainAndSufferingData;
    subsequent: PainAndSufferingData;
    summary: string;
}

export interface PainAndSufferingData {
    columns: string[];
    index: number[];
    data: (string | number)[][];
}

export interface TreatmentExpense {
    hospital_name: string[];
    treatment_period: string[];
    amount_charged: string | number;
    supporting_documents: string[];
}

export interface FutureTreatment {
    Treatment: string;
    Cost: number;
    Years: number;
    PerYear: number;
    Total: number;
}

export interface DemandToSettleSection {
    info: string;
}

export interface MedicalAnalysisData {
    treatment_timeline: string[] | string;
    number_of_visits: string | number;
    hospital_name: string;
    doctor_name: string;
    summary: string;
}

export interface TreatmentExpenseData {
    treatment_expense: {
        treatment_expenses: Array<{
            hospital_name: string;
            doctor_name: string;
            treatment_period: string;
            amount_charged: string | number;
        }>;
        total_amount_charged: string | number;
        supporting_documents: string[];
    };
} 
