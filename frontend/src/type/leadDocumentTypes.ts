export type LeadDocumentType = 
  | "ACCIDENT_REPORT"
  | "MEDICAL_RECORD"
  | "INSURANCE"
  | "PHOTOS_EVIDENCE"
  | "ID"
  | "POLICE_REPORT"
  | "WITNESS_REPORT"
  | "COMMUNICATION"
  | "OTHER";

export interface LeadDocument {
  id: string | number;
  lead: string;
  document_type: LeadDocumentType;
  file_name: string;
  file_size: number;
  content_type: string;
  uploaded_at: string;
  notes?: string;
  status: string;
  s3_key?: string;
}

export interface LeadDocumentUpload {
  file: File;
  document_type: LeadDocumentType;
  lead: string;
  notes?: string;
}

export interface LeadDocumentDownloadUrl {
  url: string;
} 

export const LEAD_DOCUMENT_TYPES = [
  { value: "ACCIDENT_REPORT", label: "Accident Report" },
  { value: "MEDICAL_RECORD", label: "Medical Record" },
  { value: "INSURANCE", label: "Insurance" },
  { value: "PHOTOS_EVIDENCE", label: "Photos/Evidence" },
  { value: "ID", label: "Identification" },
  { value: "POLICE_REPORT", label: "Police Report" },
  { value: "WITNESS_REPORT", label: "Witness Report" },
  { value: "COMMUNICATION", label: "Communication" },
  { value: "OTHER", label: "Other" }
] as const;