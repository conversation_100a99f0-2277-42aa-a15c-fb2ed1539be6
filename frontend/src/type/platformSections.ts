/**
 * Platform Section Enums
 * These enums match the backend PlatformSection enum values
 */
export enum PlatformSection {
    DASHBOARD = "dashboard",
    LMS = "lms",
    CASE_MANAGEMENT = "case_management",
    TEMPLATES = "templates",
    CHAT_WITH_CASE = "chat_with_case",
    MESSAGES = "messages",
    CALLING = "calling",
    CALENDAR = "calendar",
    ESIGN = "esign",
    SKELETON = "skeleton",
    MARKETING = "marketing",
    DEMAND = "demand",
    ANTI_COLOSSUS = "anti_colossus",
    NOTIFICATIONS = "notifications",
    NOTES = "notes",
    ADMIN = "admin"
  }
  
  /**
   * Dashboard Subsection Enums
   */
  export enum DashboardSubsection {
    CALENDAR = "calendar",
    ACTIVE_CASES = "active_cases",
    INACTIVE_CASES = "inactive_cases",
    ALARMS = "alarms",
    KPIS = "kpis"
  }
  
  /**
   * LMS Subsection Enums
   */
  export enum LMSSubsection {
    INTAKE = "intake",
    SMS = "sms",
    TEMPLATE = "template",
    NOTES = "notes",
    TASKS = "tasks",
    ACTIVITY_LOG = "activity_log"
  }
  
  /**
   * Case Management Subsection Enums
   */
  export enum CaseManagementSubsection {
    CASE_SUMMARY = "case_summary",
    LITIGATION = "litigation",
    NEGOTIATION_SETTLEMENT = "negotiation_settlement",
    EMAIL = "email",
    WORKERS = "workers",
    CHECKLIST = "checklist",
    CLIENT = "client",
    DEFENDANT = "defendant",
    INCIDENT = "incident",
    OTHER_PARTIES = "other_parties",
    MEDICAL_TREATMENT = "medical_treatment",
    HEALTH_LIENS = "health_liens",
    MEDIA_FOLDERS = "media_folders",
    CASE_COSTS = "case_costs",
    OPEN_FILES = "open_files",
    ACTIVITY = "activity"
  }
  
  /**
   * Case Summary Subsection Enums
   */
  export enum CaseSummarySubsection {
    CASE_HIGHLIGHT = "case_highlight",
    CARDS = "cards",
    PINNED_IMAGE = "pinned_image",
    SUMMARY_DETAILS = "summary_details"
  }
  
  /**
   * Negotiation Settlement Subsection Enums
   */
  export enum NegotiationSettlementSubsection {
    NEGOTIATION = "negotiation",
    NOTES_TASKS = "notes_tasks",
    MEMO = "memo",
    CLIENT_TRUST = "client_trust",
    SETTLEMENT_ADVANCE = "settlement_advance"
  }
  
  /**
   * Templates Subsection Enums
   */
  export enum TemplatesSubsection {
    VARIABLES = "variables"
  }
  
  /**
   * Skeleton Subsection Enums
   */
  export enum SkeletonSubsection {
    USERS = "users",
    USER_INVITE = "user_invite",
    CASE_STATUSES = "case_statuses",
    INTEGRATIONS = "integrations",
    TAGS = "tags",
    ORG_ROLES = "org_roles",
    TEMPLATES = "templates",
    INTAKE_SOURCES = "intake_sources",
    FOLDERS = "folders",
    KPI_GOALS = "kpi_goals",
    KPI_ANALYTICS = "kpi_analytics"
  }
  
  /**
   * Marketing Subsection Enums
   */
  export enum MarketingSubsection {
    ADD_PROVIDER = "add_provider",
    ENGAGEMENTS = "engagements",
    LEADS = "leads",
    TRACKING = "tracking"
  }
  
  /**
   * Demand Subsection Enums
   */
  export enum DemandSubsection {
    DEMAND_GENERATION = "demand_generation",
    DEMAND_EDITING = "demand_editing"
  }
  
  /**
   * Notifications Subsection Enums
   */
  export enum NotificationsSubsection {
    GENERAL_NOTIFICATION = "general_notification",
    MESSAGE_NOTIFICATIONS = "message_notifications"
  }
  
  /**
   * Notes Subsection Enums
   */
  export enum NotesSubsection {
    GENERAL_NOTES = "general_notes",
    ATTACH_EMAIL = "attach_email"
  }
  
  /**
   * Admin Subsection Enums
   */
  export enum AdminSubsection {
    ONEDRIVE = "onedrive",
    USER_MANAGEMENT = "user_management",
    KPI_MANAGEMENT = "kpi_management",
    CASE_MANAGEMENT = "case_management",
    CASE_FOLDER_MANAGEMENT = "case_folder_management",
    CASE_CHECKLIST_MANAGEMENT = "case_checklist_management",
    TEMPLATES = "templates"
  }
  
  /**
   * Helper function to get subsections for a given platform section
   */
  export function getSectionSubsections(section: PlatformSection): string[] {
    const sectionMap: Record<PlatformSection, string[]> = {
      [PlatformSection.DASHBOARD]: Object.values(DashboardSubsection),
      [PlatformSection.LMS]: Object.values(LMSSubsection),
      [PlatformSection.CASE_MANAGEMENT]: Object.values(CaseManagementSubsection),
      [PlatformSection.TEMPLATES]: Object.values(TemplatesSubsection),
      [PlatformSection.SKELETON]: Object.values(SkeletonSubsection),
      [PlatformSection.MARKETING]: Object.values(MarketingSubsection),
      [PlatformSection.DEMAND]: Object.values(DemandSubsection),
      [PlatformSection.NOTIFICATIONS]: Object.values(NotificationsSubsection),
      [PlatformSection.NOTES]: Object.values(NotesSubsection),
      [PlatformSection.ADMIN]: Object.values(AdminSubsection),
      // Add other sections with empty arrays if they don't have subsections
      [PlatformSection.CHAT_WITH_CASE]: [],
      [PlatformSection.MESSAGES]: [],
      [PlatformSection.CALLING]: [],
      [PlatformSection.CALENDAR]: [],
      [PlatformSection.ESIGN]: [],
      [PlatformSection.ANTI_COLOSSUS]: []
    };
    
    return sectionMap[section] || [];
  }
  
  /**
   * Helper function to get nested subsections for a given section and subsection
   */
  export function getNestedSubsections(section: PlatformSection, subsection: string): string[] {
    const nestedMap: Record<string, string[]> = {
      [`${PlatformSection.CASE_MANAGEMENT}_${CaseManagementSubsection.CASE_SUMMARY}`]: Object.values(CaseSummarySubsection),
      [`${PlatformSection.CASE_MANAGEMENT}_${CaseManagementSubsection.NEGOTIATION_SETTLEMENT}`]: Object.values(NegotiationSettlementSubsection)
    };
    
    return nestedMap[`${section}_${subsection}`] || [];
  }
  
  /**
   * Helper function to get display names for sections and subsections
   */
  export function getDisplayName(value: string): string {
    // Convert snake_case to Title Case
    return value
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  
  /**
   * Helper function to get all platform sections with display names
   */
  export function getPlatformSectionsWithDisplayNames(): { value: string; label: string }[] {
    return Object.values(PlatformSection).map(section => ({
      value: section,
      label: getDisplayName(section)
    }));
  }
  
  /**
   * Helper function to get all subsections for a section with display names
   */
  export function getSubsectionsWithDisplayNames(section: PlatformSection): { value: string; label: string }[] {
    return getSectionSubsections(section).map(subsection => ({
      value: subsection,
      label: getDisplayName(subsection)
    }));
  }
  
  /**
   * Helper function to get all admin sections with display names
   */
  export function getAdminSectionsWithDisplayNames(): { value: string; label: string }[] {
    return Object.values(AdminSubsection).map(section => ({
      value: section,
      label: getDisplayName(section)
    }));
  }