export enum RelationshipType {
    RELATED = "related",
    PARENT = "parent",
    CHILD = "child",
    CONSOLIDATED = "consolidated",
    SPLIT = "split",
    REFERENCED = "referenced"
}

export type RelationshipTypeLabel = {
    [key in RelationshipType]: string;
};

export const relationshipTypeLabels: RelationshipTypeLabel = {
    [RelationshipType.RELATED]: "Related",
    [RelationshipType.PARENT]: "Parent",
    [RelationshipType.CHILD]: "Child",
    [RelationshipType.CONSOLIDATED]: "Consolidated",
    [RelationshipType.SPLIT]: "Split",
    [RelationshipType.REFERENCED]: "Referenced"
};


export interface OrganizationStatusDetails {
    status: string;
    notes?: string;
    updated_at?: string;
}

export interface AssignedUser {
    id: number;
    username: string;
    email: string;
    first_name?: string;
    last_name?: string;
    role?: string;
}

export interface ChecklistItem {
    id: number;
    title: string;
    description?: string;
    is_completed: boolean;
    due_date?: string;
}

export interface CaseBasicDetails {
    id: string;
    case_name: string;
    accident_date: string;
    name_of_client: string;
    is_multiple_client_involved: boolean;
    defendent_policy_limit: number | null;
    status: string;
    status_display: string;
    organization_status: number | null;
    organization_status_details: OrganizationStatusDetails | null;
    created_by: number;
    organization: number;
    created_at: string;
    updated_at: string;
    assigned_users: AssignedUser[];
    metadata: {
        id: number;
        case_type: string;
        date_of_incident: string;
        state_of_incident: string;
        created_at: string;
        updated_at: string;
        case: string;
    };
    checklists: {
        required: ChecklistItem[];
        optional: ChecklistItem[];
    };
}

export interface CreateLinkRequest {
    target_case: string;
    relationship_type: RelationshipType;
    description?: string;
}

export interface LinkedCasesResponse {
    direct_cases: CaseBasicDetails[];
    indirect_cases: CaseBasicDetails[];
}

export interface LinkedCase {
    id: string;
    source_case: string;
    source_case_name: string;
    target_case: string;
    target_case_name: string;
    relationship_type: RelationshipType;
    description?: string;
    created_at: string;
    updated_at: string;
    created_by_email: string;
}