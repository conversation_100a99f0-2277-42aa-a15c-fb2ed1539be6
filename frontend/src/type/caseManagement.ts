export interface UserDetails {
  id: number;
  email: string;
  name: string,
  role: string,
  subscription_type: string
  organizations: {
    id: string,
    name: string
  },
  verified: boolean
}

export interface CaseWorker {
  id: number;
  case: string;
  primary_contact: number;
  case_manager: number;
  accident_recreation_worker: number;
  lead_attorney: number;
  case_assistant: number;
  lien_negotiator: number;
  supervising_attorney: number;
  intake_specialist: number;
  investigator: number;
  accountant: number;
  litigation_firm: string;
  litigation_attorney: number;
  litigation_assistant: number;
  primary_contact_details?: UserDetails;
  case_manager_details?: UserDetails;
  lead_attorney_details?: UserDetails;
  case_assistant_details?: UserDetails;
  lien_negotiator_details?: UserDetails;
  supervising_attorney_details?: UserDetails;
  intake_specialist_details?: UserDetails;
  investigator_details?: UserDetails;
  accountant_details?: UserDetails;
  litigation_attorney_details?: UserDetails;
  litigation_assistant_details?: UserDetails;
}

export interface UpdateCaseWorkersData {
  primary_contact?: number;
  case_manager?: number;
  lead_attorney?: number;
  case_assistant?: number;
  lien_negotiator?: number;
  supervising_attorney?: number;
  intake_specialist?: number;
  investigator?: number;
  accountant?: number;
  litigation_firm?: string;
  litigation_attorney?: number;
  litigation_assistant?: number;
  accident_recreation_worker?: number;
}

export type WorkerRoleKey = keyof UpdateCaseWorkersData;

export interface WorkerRole {
  key: WorkerRoleKey;
  label: string;
}
