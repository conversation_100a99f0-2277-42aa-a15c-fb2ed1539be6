import { DateString } from '@/type/case-management/commonTypes';

export enum MedicalStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    PENDING = "PENDING",
    OTHER = "OTHER"
}

export enum DocRequestStatus {
    RECEIVED = "RECEIVED",
    REQUESTED = "REQUESTED",
    DOES_NOT_EXIST = "DOES_NOT_EXIST",
    NOT_YET_REQUESTED = "NOT_YET_REQUESTED",
}


export interface CaseIncidentDetails {
    id: number;
    case: string;
    report_number?: string;
    incident_date?: string;
    incident_type?: string;
    incident_location?: string;
    insurance_status: boolean;
    incident_description?: string;
    doc_request_status?: DocRequestStatus;
    doc_request_notes?: string;
    weather_conditions?: string;
    police_report_details?: string;
    witness_information?: string;
    previous_attorney?: string;
    medical_status?: MedicalStatus;
    basic_facts?: string;
    conflicts_checked: boolean;
    injury_type?: string;
    injury_location?: string;
    affected_areas?: Record<string, string>;
    surgery_required: boolean;
    critical_conditions?: string;
    liability_assessment?: string;
    estimated_value?: string;
    statute_of_limitations?: string;
    created_at: DateString;
    updated_at: DateString;
    updated_by?: {
        id: number;
        first_name: string;
        last_name: string;
        email: string;
    };
    received_at?: string;
    received_by?: {
        id: number;
        name: string;
        email: string;
    };
    requested_at?: string;
    requested_by?: {
        id: number;
        name: string;
        email: string;
    };
    medical_status_display: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
}

export interface CaseIncidentDetailsResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CaseIncidentDetails[];
}

export interface IncidentDetailsUpdateRequest {
    report_number?: string;
    incident_date?: string;
    incident_type?: string;
    insurance_status?: boolean;
    incident_description?: string;
    weather_conditions?: string;
    police_report_details?: string;
    witness_information?: string;
    previous_attorney?: string;
    medical_status?: MedicalStatus;
    basic_facts?: string;
    conflicts_checked?: boolean;
    injury_type?: string;
    injury_location?: string;
    affected_areas?: Record<string, string>;
    surgery_required?: boolean;
    critical_conditions?: string;
    liability_assessment?: string;
    estimated_value?: string;
    statute_of_limitations?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    doc_request_status?: DocRequestStatus;
    doc_request_notes?: string;
}

export interface IncidentDetailsCreateRequest {
    case: string;
    incident_date?: string;
    incident_type?: string;
    incident_location?: string;
    insurance_status: boolean;
    incident_description?: string;
    weather_conditions?: string;
    police_report_details?: string;
    witness_information?: string;
    previous_attorney?: string;
    medical_status?: MedicalStatus;
    basic_facts?: string;
    conflicts_checked: boolean;
    injury_type?: string;
    injury_location?: string;
    affected_areas?: Record<string, string>;
    surgery_required: boolean;
    critical_conditions?: string;
    liability_assessment?: string;
    estimated_value?: string;
    statute_of_limitations?: string;
}
