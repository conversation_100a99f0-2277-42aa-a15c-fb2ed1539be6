import { CaseType, DefendantEntityType, DependentStatus, FamilyStatus, PlaintiffRole, PolicyType, RecipientType, USStates } from "@/constants/commont";

import { Pronouns } from "@/constants/commont";

export interface userDetail {
    id: number;
    email: string;
    name: string;
}


export interface CaseMetadata {
    id: number;
    case_type: CaseType | null;
    date_of_incident: string;
    state_of_incident: string | null;
    created_at: string;
    updated_at: string;
    case: string;
}
export interface CaseResponse {
    id: string;
    case_name: string;
    name_of_client: string;
    accident_date: string;
    is_multiple_client_involved: boolean;
    user_id: string;
    status: string;
    created_at: string;
    updated_at: string;
    assigned_users: userDetail[];
    metadata: CaseMetadata;
    status_display: string;
    created_by: userDetail;
    defendent_policy_limit: string;
    linked_cases: number;
    organization: number;
    organization_status_details: {
        display_name: string;
        name: string;
    }
}

export interface PaginatedResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
}

// export type CaseStatus = 'document_required' | 'in_progress' | 'review_pending' | 'completed' | 'archived';

export interface CaseQueryParams {
    page?: number;
    pageSize?: number;
    search?: string;
    ordering?: 'created_at' | '-created_at' | 'accident_date' | '-accident_date';
    organization_status?: string;
    assigned_to?: string;
    created_by?: string;
    workers?: string;
    updated_at_after?: string;
    updated_at_before?: string;
    accidentDateAfter?: string;
    accidentDateBefore?: string;
    isMultipleClientInvolved?: boolean;
    my_cases?: boolean;
    only_primary_contact?: boolean;
    kpi_type?: string;
    case_kpi_status?: 'open' | 'closed';
}

export interface CaseStatistics {
    document_required: number;
    in_progress: number;
    review_pending: number;
    completed: number;
    archived: number;
    case_created: number;
    demand_generation: number;
    negotiations: number;
    settlements: number;
}

export interface CaseStatisticsResponse {
    statistics: CaseStatistics;
    total_cases: number;
    user_role: string;
    organization: string;
}


export interface CaseState {
    all_cases: CaseResponse[];
    statistics: CaseStatistics | null;
    isLoading: boolean;
    showOrganizationCases: boolean;
}

export interface CaseActions {
    setShowOrganizationCases: (show: boolean) => void;
    setCases: (cases: CaseResponse[]) => void;
    setStatistics: (statistics: CaseStatistics | null) => void;
    setIsLoading: (isLoading: boolean) => void;
}



// ===========================================================

export interface BaseResponse {
    created_at: string;
    updated_at: string;
}


export interface CreateCaseFormData {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    lead_source: string;
    incident_location: string;
    medical_status: string;
    previous_attorney?: string;
    insurance_status: boolean;
    metadata: {
        case_type: string;
        state_of_incident: string;
        date_of_incident: string;
    }
}

export interface PlaintiffResponse extends BaseResponse {
    id: number;
    case: string;
    first_name: string;
    last_name: string;
    pronouns: Pronouns;
    role: PlaintiffRole;
    state: USStates | null;
    family_status: FamilyStatus;
    dependent_status: DependentStatus;
    is_minor: boolean;
}

export interface CaseFullDetails extends CaseResponse {
    case_number?: string;
    status: string;
    organization_status?: number | {
        id: number;
        organization: number;
        name: string;
        display_name: string;
        kpi_type: string | null;
        description: string;
        order: number;
        is_active: boolean;
        legacy_status: string | null;
        legacy_status_display: string | null;
        created_at: string;
        updated_at: string;
    };
    plaintiffs: PlaintiffResponse[];
    metadata: MetadataResponse;
    policy_info: PolicyInfoResponse;
    defendants: DefendantResponse[];
    recipients: RecipientResponse[];
    economic_impact: EconomicImpactResponse;
    non_economic_impact: NonEconomicImpactResponse;
}

export interface PlaintiffCreate {
    case: string;
    first_name: string;
    last_name: string;
    pronouns?: Pronouns;
    role?: PlaintiffRole;
    state?: string;
    family_status?: FamilyStatus;
    dependent_status?: DependentStatus;
    is_minor?: boolean;
}

export type PlaintiffUpdate = Partial<PlaintiffCreate>;

export interface MetadataResponse extends BaseResponse {
    id: number;
    case: string;
    case_type: CaseType | null;
    date_of_incident: string;
    state_of_incident: string | null;
}

export interface MetadataCreate {
    case: string;
    case_type?: CaseType;
    date_of_incident: string;
    state_of_incident?: string;
}

export type MetadataUpdate = Partial<MetadataCreate>;

export interface PolicyInfoResponse extends BaseResponse {
    id: number;
    case: string;
    carrier_name: string;
    policy_type: PolicyType | null;
    policy_coverage_type: string;
    policy_limit: number | null;
    policy_number: string;
    claim_number: string;
}

export interface PolicyInfoCreate {
    case: string;
    carrier_name?: string;
    policy_type?: PolicyType;
    policy_coverage_type?: string;
    policy_limit?: number;
    policy_number?: string;
    claim_number?: string;
}

export type PolicyInfoUpdate = Partial<PolicyInfoCreate>;

// Defendant
export interface DefendantResponse extends BaseResponse {
    id: number;
    case: string;
    entity_type: DefendantEntityType;
    first_name: string;
    last_name: string;
    pronouns: Pronouns | null;
    business_name: string;
}

export interface DefendantCreate {
    case: string;
    entity_type: DefendantEntityType;
    first_name?: string;
    last_name?: string;
    pronouns?: Pronouns;
    business_name?: string;
}

export type DefendantUpdate = Partial<DefendantCreate>;

// Recipient
export interface RecipientResponse extends BaseResponse {
    id: number;
    case: string;
    recipient_type: RecipientType;
    first_name: string;
    last_name: string;
    email: string;
    street: string;
    street_line2: string;
    city: string;
    state: string;
    zip_code: string;
    fax_number: string;
}

export interface RecipientCreate {
    case: string;
    recipient_type: RecipientType;
    first_name: string;
    last_name: string;
    email: string;
    street: string;
    street_line2?: string;
    city: string;
    state: string;
    zip_code: string;
    fax_number?: string;
}

export type RecipientUpdate = Partial<RecipientCreate>;


// Economic Impact
export interface EconomicImpactResponse extends BaseResponse {
    id: number;
    case: string;
    loss_of_income: number;
    facts: string;
    out_of_pocket_expense: number;
    household_services: number;
}

export interface EconomicImpactCreate {
    case: string;
    loss_of_income?: number;
    facts?: string;
    out_of_pocket_expense?: number;
    household_services?: number;
}

export type EconomicImpactUpdate = Partial<EconomicImpactCreate>;

// Non-Economic Impact
export interface NonEconomicImpactResponse extends BaseResponse {
    id: number;
    case: string;
    impact_recreation_leisure: string;
    impact_entertainment: string;
    impact_home_activities: string;
    impact_social_time: string;
    impact_self_care: string;
    additional_impact: string;
}

export interface NonEconomicImpactCreate {
    case: string;
    impact_recreation_leisure?: string;
    impact_entertainment?: string;
    impact_home_activities?: string;
    impact_social_time?: string;
    impact_self_care?: string;
    additional_impact?: string;
}

export type NonEconomicImpactUpdate = Partial<NonEconomicImpactCreate>;

export interface DiscoveryWorkflowRequest {
    case_id: string;
    demand_letter_filenames: string;
    user_prompt: string;
}

export interface DemandGenerationRequestResponse {
    id: number;
    case: string;
    is_submitted_for_demand_generation: boolean;
    demand_generation_completed: boolean;
    is_submitted_for_review: boolean;
    review_completed: boolean;
    review_text_history: string;
    review_count: number;
    status: string;
    created_at: string;
    updated_at: string;

    is_submitted_for_verdict_analysis?: boolean;
    verdict_analysis_completed?: boolean;
    verdict_analysis_text?: string;
}


export interface ValidationWorkflowRequest {
    case_id: string;
    demand_letter_filenames: string;
    validation_type: 'full_validation' | 'treatment_expenses' | 'lost_wages' | 'pain_and_suffering' | 'medical_causation' | 'liability_analysis' | 'settlement_demand' | 'loss_of_household';
    user_prompt?: string;
}

export interface ChatMessage {
    role: string;
    content: string;
}

export interface ChatRequest {
    messages: ChatMessage[];
    file_names_to_chat_with?: string[];
    category_to_document_mapping?: { [key: string]: { documents: string[], count: number } };
    client_name: string;
    accident_date: string;
}



export interface AIEditRequest {
    caseId: string;
    sectionType: string;
    sectionId: string;
    prompt: string;
    currentContent: string;
}

export interface AIEditResponse {
    newContent: string;
    changes: Array<{
        type: string;
        content: string;
    }>;
}


export interface TransformDocDataResponse {
    new_text: string;
    reason: string;
}

export interface RecentCasesResponse extends CaseResponse {
    last_activity_date: string;
    latest_checklist_date: string | null;
    latest_note_date: string;
    latest_task_date: string;
}

// Add these new interfaces after the existing PaginatedResponse interface
export interface CaseActivitySection {
    cases: RecentCasesResponse[];
    page: number;
    page_size: number;
    total: number;
}

export interface CaseActivityResponse {
    counts?: {
        active: number;
        low_activity: number;
        no_activity: number;
    };
    active_cases: CaseActivitySection;
    low_activity_cases: CaseActivitySection;
    no_activity_cases: CaseActivitySection;
}