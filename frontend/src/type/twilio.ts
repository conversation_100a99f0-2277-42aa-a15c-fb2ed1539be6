export interface TwilioCallRequest {
  to: string;
  message: string;
}

export interface TwilioSMSRequest {
  to_number: string;
  message: string;
  organization_id?: string;
}

export interface TwilioResponse {
  success: boolean;
  message: string;
  sid?: string;
}

export interface TwilioCallResponse extends TwilioResponse {
  callSid?: string;
  status?: 'queued' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'busy' | 'no-answer';
}

export interface TwilioSMSResponse extends TwilioResponse {
  message_sid?: string;
  status?: 'queued' | 'sending' | 'sent' | 'delivered' | 'failed' | 'undelivered' | 'received';
  to?: string;
  from?: string;
  date_sent?: string;
  error_message?: string | null;
  direction?: 'inbound' | 'outbound-api';
  price?: string;
}

export interface MediaUrl {
  type: string;
  bucket: string;
  s3_key: string;
  expires_at: string;
  twilio_url: string;
  presigned_url: string;
}

export interface TwilioMessage {
  id: string;
  message_sid: string;
  message_type: 'incoming' | 'outgoing';
  from_number: string;
  to_number: string;
  body: string;
  status: TwilioSMSResponse['status'];
  media_urls: MediaUrl[] | null;
  created_at: string;
  updated_at: string;
  organization_id: string;
  num_media?: number; 
  case_id?: string;
  lead_id?: string;
}

export interface TwilioMessageListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: TwilioMessage[];
} 