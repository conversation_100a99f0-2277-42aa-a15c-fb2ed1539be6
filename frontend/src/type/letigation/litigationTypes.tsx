// Court Contact Types
export interface CourtContact {
    id: number;
    company: string;
    payee?: string;
    county: string;
    court_type?: string;
    phone?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    local_rules_url?: string;
    case_access_url?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface CourtContactCreateRequest {
    company: string;
    payee?: string;
    county?: string;
    court_type?: string;
    phone?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    local_rules_url?: string;
    case_access_url?: string;
    note?: string;
}

export interface CourtContactResponse {
    data: CourtContact;
    status: number;
    message?: string;
}

// Court Contact List Response
export interface CourtContactListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: CourtContact[];
}

// Court Contact List Query Result
export interface CourtContactListQueryResult {
    data: CourtContact[];
    isLoading: boolean;
    error: string | null;
}

// Add these new interfaces
export interface CaseCourtDetailsRead {
    id: number;
    case: string;
    court: CourtContact;
    case_number: string | null;
    case_name: string | null;
    trial_note: string | null;
    created_at: string;
    updated_at: string;
}

export interface CaseCourtDetailsWrite {
    id?: number;
    court?: number;
    case_number?: string | null;
    case_name?: string | null;
    trial_note?: string | null;
}

export type CaseCourtDetailsCreateRequest = CaseCourtDetailsWrite;

export type CaseCourtDetailsUpdateRequest = CaseCourtDetailsWrite;

// Add these interfaces after existing types

export interface JudgeContact {
    id: number;
    contact_type: 'judge' | 'arbitrator';
    phone?: string;
    cell?: string;
    fax?: string;
    department_phone?: string;
    email?: string;
    payee?: string;
    first_name: string;
    middle_name?: string;
    last_name: string;
    phone_ext?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    department?: string;
    chamber_rules_url?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface JudgeContactCreateRequest {
    contact_type: 'judge' | 'arbitrator';
    payee?: string;
    first_name: string;
    middle_name?: string;
    last_name: string;
    phone?: string;
    cell?: string;
    fax?: string;
    email?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    department?: string;
    department_phone?: string;
    chamber_rules_url?: string;
    note?: string;
}

export interface JudgeContactResponse {
    data: JudgeContact;
    status: number;
    message?: string;
}

export interface ClerkContact {
    id: number;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    payee?: string;
    first_name: string;
    last_name: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface ClerkContactCreateRequest {
    payee?: string;
    first_name: string;
    last_name: string;
    phone?: string;
    phone_ext?: string;
    cell?: string;
    fax?: string;
    email?: string;
    website?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    note?: string;
}

export interface ClerkContactResponse {
    data: ClerkContact;
    status: number;
    message?: string;
}

// Add these interfaces after existing types

export interface JudgeContactListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: JudgeContact[];
}

export interface ClerkContactListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: ClerkContact[];
}

export interface JudgeContactListQueryResult {
    data: JudgeContact[];
    isLoading: boolean;
    error: string | null;
}

export interface ClerkContactListQueryResult {
    data: ClerkContact[];
    isLoading: boolean;
    error: string | null;
}

export interface CaseJudgeDetailsRead {
    id: number;
    case: string;
    judge: JudgeContact;
    clerk: ClerkContact;
    deliberation_note?: string;
    created_at: string;
    updated_at: string;
}

export interface CaseJudgeDetailsWrite {
    judge?: number;
    clerk?: number;
    deliberation_note?: string;
}

export interface CaseJudgeDetailsResponse {
    data: CaseJudgeDetailsRead;
    status: number;
    message?: string;
}

export interface CaseJudgeDetailsCreateRequest {
    judge: number;
    clerk?: number;
    deliberation_note?: string;
}

export type CaseJudgeDetailsUpdateRequest = CaseJudgeDetailsCreateRequest;

export interface MediatorContact {
    id: number;
    company: string;
    payee?: string;
    first_name: string;
    last_name: string;
    phone?: string;
    cell?: string;
    fax?: string;
    email?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    website?: string;
    note?: string;
    created_at: string;
    updated_at: string;
}

export interface MediatorContactCreateRequest {
    company: string;
    payee?: string;
    first_name: string;
    last_name: string;
    phone?: string;
    cell?: string;
    fax?: string;
    email?: string;
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    tax_id?: string;
    website?: string;
    note?: string;
}

export interface MediatorContactResponse {
    data: MediatorContact;
    status: number;
    message?: string;
}

export interface MediatorContactListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: MediatorContact[];
}

export interface MediatorContactListQueryResult {
    data: MediatorContact[];
    isLoading: boolean;
    error: string | null;
}

export type MediatorContactUpdateRequest = MediatorContactCreateRequest;

// Add these interfaces
export interface CaseMediatorDetailsRead {
    id: number;
    case: string;
    mediator: MediatorContact;
    mediation_note?: string;
    created_at: string;
    updated_at: string;
}

export interface CaseMediatorDetailsWrite {
    mediator: number;
    mediation_note?: string;
}

export type CaseMediatorDetailsCreateRequest = CaseMediatorDetailsWrite;

export type CaseMediatorDetailsUpdateRequest = CaseMediatorDetailsWrite;

export interface CaseMediatorDetailsResponse {
    data: CaseMediatorDetailsRead;
    status: number;
    message?: string;
}

// Add these interfaces to your existing file

export interface CaseEventRead {
    id: number;
    case: string;
    event_type: string;
    address?: string;
    description?: string;
    date: string;
    start_time: string;
    end_time: string;
    status?: string;
    workers: UserDetail[];
    created_at: string;
    updated_at: string;
}

export interface CaseEventWrite {
    event_type: string;
    address?: string;
    description?: string;
    date: string;
    start_time: string;
    end_time: string;
    status?: string;
}

export interface UserDetail {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
}

export enum EventType {
    ARBITRATION = "Arbitration",
    CASE_MANAGEMENT_CONF = "Case Management Conf.",
    DEADLINE = "Deadline",
    DEF_MEDICAL_EXAM = "Def. Medical Exam",
    DEPOSITION = "Deposition",
    DISCOVERY = "Discovery",
    DISCOVERY_CUTOFF = "Discovery Cutoff",
    EXCEPTION = "Exception",
    EX_PARTE = "Ex Parte",
    FINAL_SETTLEMENT_CONF = "Final Settlement Conf.",
    FINAL_STATUS_CONFERENCE = "Final Status Conference",
    FIRST_EXPERT_EXCHANGE = "First Expert Exchange",
    HEARING = "Hearing",
    JURY_FEE_DEADLINE = "Jury Fee Deadline",
    LAST_DAY_998 = "Last Day for 998",
    MANDATORY_SETTLEMENT_CONF = "Mandatory Settlement Conf",
    MEDIATION = "Mediation",
    MEET_AND_CONFER = "Meet and Confer",
    MEETING = "Meeting",
    MOTION_CUTOFF = "Mot. Cutoff",
    MOTION_IN_LIMINE_DEADLINE = "Mot. in Limine Deadline",
    MOTION = "Motion",
    MOTION_TO_COMPEL = "Mot. to Compel",
    NOTICE_TO_APPEAR = "Notice to Appear",
    OPPOSITION_MOTION_IN_LIMINE_DEADLINE = "Opp'n. Mot. in Limine Deadline",
    OPPOSITION_SUMMARY_JUDGMENT_MOTION_DEADLINE = "Opp'n. Summ. J. Mot. Deadline",
    ORDER_TO_SHOW_CAUSE = "Order to Show Cause",
    OTHER = "Other",
    RULE = "Rule",
    SECOND_EXPERT_EXCHANGE = "Second Expert Exchange",
    STATUS_CONF = "Status Conf.",
    SUBPOENA = "Subpoena",
    SUMMARY_JUDGMENT_MOTION_DEADLINE = "Summ. J. Mot. Deadline",
    TRIAL = "Trial",
    TRIAL_READINESS_CONF = "Trial Readiness Conf.",
    TRIAL_SETTING_CONF = "Trial Setting Conf.",
    WITNESS_LIST_DUE = "Witness List Due"
}

// Add this with the other event-related interfaces
export interface CaseEventCreateRequest {
    event_type: string;
    address?: string;
    description?: string;
    date: string;
    start_time: string;
    end_time: string;
    status?: string;
    workers?: number[]; // Array of worker IDs
    timezone?: string;
    create_or_update_calendar_event?: "create" | "update";
}

export interface CaseEventResponse {
    data: CaseEventRead;
    status: number;
    message?: string;
}

// Add this interface
export interface CaseEventUpdateRequest extends CaseEventCreateRequest {
    id: number;
}

export interface CaseEventUpdateResponse {
    data: CaseEventRead;
    status: number;
    message?: string;
}

// Litigation Dates Types
export interface LitigationDates {
    filed_date?: string;
    service_date?: string;
    affidavit_date?: string;
    served_date?: string;
    default_date?: string;
    answered_date?: string;
    appeared_date?: string;
    dismissed_date?: string;
}

export interface LitigationDatesResponse {
    id: number;
    case: string;
    defendant: number;
    filed_date: string | null;
    service_date: string | null;
    affidavit_date: string | null;
    served_date: string | null;
    default_date: string | null;
    answered_date: string | null;
    appeared_date: string | null;
    dismissed_date: string | null;
    created_at: string;
    updated_at: string;
}

export interface LitigationDatesUpdateRequest {
    filed_date?: string | null;
    service_date?: string | null;
    affidavit_date?: string | null;
    served_date?: string | null;
    default_date?: string | null;
    answered_date?: string | null;
    appeared_date?: string | null;
    dismissed_date?: string | null;
}
