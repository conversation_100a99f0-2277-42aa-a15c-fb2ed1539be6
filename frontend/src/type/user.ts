export interface User {
  id: number
  name: string
  email: string
  organization: {
    name: string
  }
  role: string
  user_id?: string
  subscription_type?: string
}

export interface AuthState extends AuthActions {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
}

// Define the actions interface
export interface AuthActions {
  setUser: (user: User | null) => void;
  setTokens: (accessToken: string | null, refreshToken: string | null) => void;
  logout: () => void;
  isAuthenticated: () => boolean;
}