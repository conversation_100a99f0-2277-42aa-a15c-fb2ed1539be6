declare module 'react-rating-stars-component' {
  import { ComponentType } from 'react';

  interface ReactStarsProps {
    count?: number;
    value?: number;
    char?: string;
    color?: string;
    activeColor?: string;
    size?: number;
    edit?: boolean;
    isHalf?: boolean;
    emptyIcon?: JSX.Element;
    halfIcon?: JSX.Element;
    filledIcon?: JSX.Element;
    classNames?: string;
    onChange?: (newValue: number) => void;
  }

  const ReactStars: ComponentType<ReactStarsProps>;
  export default ReactStars;
} 