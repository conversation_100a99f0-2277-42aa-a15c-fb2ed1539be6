export type WebSocketMessageType = 'authentication' | 'chat_message' | 'authentication_successful' | 'typing_indicator' | 'notification_message';

export interface WebSocketMessage {
  type: WebSocketMessageType;
  data?: {
    conversation_id?: string;
    type?: string;
    content?: string;
    message?: string;
    user?: {
      first_name: string;
      last_name: string;
    };
  };
  message?: string;
  timestamp?: string;
  status?: string;
  entity_type?: string;
  entity_id?: string;
} 