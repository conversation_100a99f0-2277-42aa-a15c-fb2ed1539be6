"use client";

import { useState, useEffect } from "react";
import { useUserProfile, useUpdateProfile, useChangePassword } from "@/services/userService";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User, KeyRound, Calendar, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCheckIntegration, useRemoveIntegration } from "@/services/emailCalenderIntegrationService";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function ProfilePage() {
    const { data: profile, isLoading } = useUserProfile();
    const updateProfile = useUpdateProfile();
    const changePassword = useChangePassword();
    const { toast } = useToast();
    const { data: integration, isLoading: isLoadingIntegration } = useCheckIntegration();
    const removeIntegration = useRemoveIntegration();
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const [editMode, setEditMode] = useState(false);
    const [formData, setFormData] = useState({
        first_name: '',
        last_name: ''
    });
    const [passwordData, setPasswordData] = useState({
        old_password: '',
        new_password: '',
        confirm_password: ''
    });

    // Initialize form data when profile is loaded
    useEffect(() => {
        if (profile) {
            setFormData({
                first_name: profile.first_name || '',
                last_name: profile.last_name || ''
            });
        }
    }, [profile]);

    const handleUpdateProfile = async () => {
        try {
            await updateProfile.mutateAsync({
                first_name: formData.first_name,
                last_name: formData.last_name
            });
            setEditMode(false);
        } catch (error) {
            console.error('Failed to update profile:', error);
        }
    };

    const handleChangePassword = async () => {
        if (passwordData.new_password !== passwordData.confirm_password) {
            toast({
                title: "Error",
                description: "Passwords do not match",
                variant: "destructive"
            });
            return;
        }

        try {
            await changePassword.mutateAsync({
                old_password: passwordData.old_password,
                new_password: passwordData.new_password,
                confirm_password: passwordData.confirm_password
            });
            setPasswordData({
                old_password: '',
                new_password: '',
                confirm_password: ''
            });
        } catch (error) {
            console.error('Failed to change password:', error);
        }
    };

    const handleRemoveIntegration = async () => {
        try {
            await removeIntegration.mutateAsync();
            setIsDialogOpen(false);
        } catch (error) {
            console.error('Failed to remove integration:', error);
        }
    };

    if (isLoading) {
        return <div className="flex items-center justify-center min-h-screen">Loading profile...</div>;
    }

    return (
        <div className="container max-w-2xl mx-auto p-6 space-y-8">
            <div className="space-y-2">
                <h1 className="text-2xl font-bold">Profile Settings</h1>
                <p className="text-muted-foreground">
                    Manage your account settings and preferences
                </p>
            </div>

            <Card className="p-6">
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <User className="h-5 w-5" />
                            <h2 className="text-xl font-semibold">Profile Information</h2>
                        </div>
                        {!editMode ? (
                            <Button onClick={() => setEditMode(true)}>
                                Edit Profile
                            </Button>
                        ) : (
                            <div className="flex gap-2">
                                <Button variant="outline" onClick={() => setEditMode(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleUpdateProfile}>
                                    Save Changes
                                </Button>
                            </div>
                        )}
                    </div>

                    <div className="grid gap-4">
                        <div className="space-y-2">
                            <Label>Email</Label>
                            <Input value={profile?.email || ''} disabled />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label>First Name</Label>
                                <Input
                                    value={formData.first_name}
                                    onChange={(e) => setFormData({ 
                                        ...formData, 
                                        first_name: e.target.value
                                    })}
                                    disabled={!editMode}
                                    placeholder="Enter your first name"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label>Last Name</Label>
                                <Input
                                    value={formData.last_name}
                                    onChange={(e) => setFormData({ 
                                        ...formData, 
                                        last_name: e.target.value
                                    })}
                                    disabled={!editMode}
                                    placeholder="Enter your last name"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label>Role</Label>
                            <Input value={profile?.role || ''} disabled />
                        </div>

                        <div className="space-y-2">
                            <Label>Organization</Label>
                            <Input value={profile?.organization?.name || ''} disabled />
                        </div>
                    </div>
                </div>
            </Card>

            <Card className="p-6">
                <div className="space-y-6">
                    <div className="flex items-center gap-2">
                        <KeyRound className="h-5 w-5" />
                        <h2 className="text-xl font-semibold">Change Password</h2>
                    </div>

                    <div className="grid gap-4">
                        <div className="space-y-2">
                            <Label>Current Password</Label>
                            <Input
                                type="password"
                                value={passwordData.old_password}
                                onChange={(e) => setPasswordData({ ...passwordData, old_password: e.target.value })}
                                placeholder="Enter your current password"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label>New Password</Label>
                            <Input
                                type="password"
                                value={passwordData.new_password}
                                onChange={(e) => setPasswordData({ ...passwordData, new_password: e.target.value })}
                                placeholder="Enter your new password"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label>Confirm New Password</Label>
                            <Input
                                type="password"
                                value={passwordData.confirm_password}
                                onChange={(e) => setPasswordData({ ...passwordData, confirm_password: e.target.value })}
                                placeholder="Confirm your new password"
                            />
                        </div>

                        <Button 
                            onClick={handleChangePassword}
                            disabled={!passwordData.old_password || !passwordData.new_password || !passwordData.confirm_password}
                        >
                            Change Password
                        </Button>
                    </div>
                </div>
            </Card>

            <Card className="p-6">
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            <h2 className="text-xl font-semibold">Calendar Integration</h2>
                        </div>
                    </div>

                    {isLoadingIntegration ? (
                        <div className="text-muted-foreground">Loading integration status...</div>
                    ) : integration?.account_connected ? (
                        <div className="space-y-4">
                            <div className="flex items-center gap-2 text-green-600">
                                <div className="h-2 w-2 rounded-full bg-green-600" />
                                <span>Calendar integration is active</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                                Connected to {integration?.email}
                            </div>
                            <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                                <AlertDialogTrigger asChild>
                                    <Button variant="destructive" className="mt-2">
                                        Remove Integration
                                    </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                        <AlertDialogTitle>Remove Calendar Integration</AlertDialogTitle>
                                        <AlertDialogDescription>
                                            Are you sure you want to remove your calendar integration? This will disconnect your calendar and remove all associated data.
                                        </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction 
                                            onClick={handleRemoveIntegration}
                                            disabled={removeIntegration.isPending}
                                        >
                                            {removeIntegration.isPending ? "Removing..." : "Remove Integration"}
                                        </AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="flex items-center gap-2 text-muted-foreground">
                                <AlertCircle className="h-5 w-5" />
                                <span>No calendar integration found</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                                Connect your calendar to sync your events and manage your schedule.
                            </div>
                            <Button 
                                onClick={() => window.location.href = '/calendar'}
                                className="mt-2"
                            >
                                Connect Calendar
                            </Button>
                        </div>
                    )}
                </div>
            </Card>
        </div>
    );
}
