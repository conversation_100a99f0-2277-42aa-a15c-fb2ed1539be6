"use client";

import { useState, useCallback, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  CheckCircle2,
  Clock,
  ArrowRight,
  FileText,
  MessageSquare,
  Loader2,
  AlertCircle,
  Circle,
  ChevronLeft,
  ExternalLink,
} from "lucide-react";
import dynamic from "next/dynamic";
import { useAllTasksListQuery, useUpdateTaskMutation } from "@/services/case-management/noteTaskService";
import { format } from "date-fns";
import { TaskStatus } from "@/type/case-management/noteTaskTypes";
import { TaskStatusConfig } from "@/type/case-management/noteTaskTypes";
import { useAgendaView, checkIntegrationStatus, connectNylasCalendar } from "@/services/emailCalenderIntegrationService";
import { useToast } from "@/hooks/use-toast";
import DOMPurify from 'dompurify';
import { cn } from "@/lib/utils";
import { AgendaEvent } from '@/type/emailCalender';

// Add helper function for link extraction
const extractLinks = (text: string): { meetLinks: string[] } => {
  const meetingRegex = /(https:\/\/(meet\.google\.com|zoom\.us|teams\.microsoft\.com)[^\s<>"]+)/g;
  const meetLinks = text.match(meetingRegex) || [];
  return { meetLinks };
};

interface EventInfo {
  cleanTitle: string;
  cleanDescription: string;
  meetLinks: string[];
}

const ReactConfetti = dynamic(() => import("react-confetti"), {
  ssr: false,
});

export default function NewDashboardPage() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [showConfetti, setShowConfetti] = useState(false);
  const [taskStatuses, setTaskStatuses] = useState<Map<number, TaskStatus>>(new Map());
  const [isCalendarIntegrated, setIsCalendarIntegrated] = useState(false);
  const [isCheckingIntegration, setIsCheckingIntegration] = useState(true);
  const { toast } = useToast();

  // Update useAgendaView to use selected date
  const { data: agendaData, isLoading: isLoadingAgenda, isError: isAgendaError } = useAgendaView(
    format(selectedDate, 'yyyy-MM-dd')
  );

  useEffect(() => {
    checkIntegrationAndLoadEvents();
  }, []);

  const checkIntegrationAndLoadEvents = async () => {
    try {
      setIsCheckingIntegration(true);
      const status = await checkIntegrationStatus();

      if (status) {
        setIsCalendarIntegrated(status.is_active);
      } else {
        setIsCalendarIntegrated(false);
      }
    } catch (error) {
      console.error('Error checking integration:', error);
      setIsCalendarIntegrated(false);
    } finally {
      setIsCheckingIntegration(false);
    }
  };

  const handleIntegration = async () => {
    try {
      await connectNylasCalendar();
    } catch (error) {
      console.error('Failed to start integration:', error);
      toast({
        title: "Error",
        description: "Failed to start calendar integration",
        variant: "destructive"
      });
    }
  };

  // Fetch tasks using the API
  const { data: tasksData, isLoading: isLoadingTasks, isError: isTasksError } = useAllTasksListQuery();
  const tasks = tasksData?.pages.flatMap(page => page.results) || [];

  // Task update mutation
  const updateTaskMutation = useUpdateTaskMutation("");

  const kpiData = [
    {
      label: "Active Cases",
      value: "45",
      goal: "50",
      progress: 90,
      trend: "+5 from last week",
    },
    {
      label: "Cases Touched This Week",
      value: "12",
      goal: "15",
      progress: 80,
      trend: "+3 from yesterday",
    },
    {
      label: "Pending Motions",
      value: "8",
      goal: "5",
      progress: 60,
      trend: "-2 needed",
    },
    {
      label: "Upcoming Hearings",
      value: "5",
      goal: "5",
      progress: 100,
      trend: "On target",
    },
  ];

  const recentCases = [
    {
      id: "1",
      client: "Walker, James",
      caseType: "Auto Accident",
      lastAction: "Medical records requested",
      lastActionDate: "2 hours ago",
      status: "active",
      priority: "high",
      nextDeadline: "Medical records due in 5 days",
    },
    {
      id: "2",
      client: "Thompson, Sarah",
      caseType: "Slip and Fall",
      lastAction: "Demand letter sent",
      lastActionDate: "1 day ago",
      status: "pending",
      priority: "medium",
      nextDeadline: "Insurance response due in 15 days",
    },
    {
      id: "3",
      client: "Martinez, Carlos",
      caseType: "Auto Accident",
      lastAction: "Settlement offer received",
      lastActionDate: "3 days ago",
      status: "urgent",
      priority: "high",
      nextDeadline: "Response needed in 2 days",
    },
  ];

  const handleTaskComplete = useCallback(
    async (taskId: string, currentStatus: TaskStatus, caseId: string, title: string) => {
      // Get the next allowed status from the TaskStatusConfig
      const nextStatus = TaskStatusConfig[currentStatus].nextStatus[0];
      if (!nextStatus) return;

      // Update UI immediately
      setTaskStatuses(prev => new Map(prev).set(parseInt(taskId), nextStatus));

      try {
        await updateTaskMutation.mutate({
          taskId,
          data: {
            case: caseId,
            title: title,
            status: nextStatus
          }
        });

        // Show confetti if completed
        if (nextStatus === TaskStatus.COMPLETED) {
          setShowConfetti(true);
          setTimeout(() => setShowConfetti(false), 3000);
        }
      } catch (error) {
        // Revert UI on error
        setTaskStatuses(prev => new Map(prev).set(parseInt(taskId), currentStatus));
        console.error('Failed to update task status:', error);
      }
    },
    [updateTaskMutation]
  );

  const cleanAndExtractEventInfo = (event: AgendaEvent): EventInfo => {
    // Extract the title from HTML if needed
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = event.title || '';
    const cleanTitle = tempDiv.textContent || tempDiv.innerText || event.title || '';

    // Clean description HTML
    const cleanDescription = event.description ? DOMPurify.sanitize(event.description, {
      ALLOWED_TAGS: ['a', 'br', 'p', 'span'],
      ALLOWED_ATTR: ['href']
    }) : '';

    // Extract any meeting links
    const { meetLinks } = extractLinks(event.description || '');

    return {
      cleanTitle,
      cleanDescription,
      meetLinks
    };
  };

  // Add navigation functions
  const handlePrevDay = () => {
    setSelectedDate(prev => {
      const newDate = new Date(prev);
      newDate.setDate(prev.getDate() - 1);
      return newDate;
    });
  };

  const handleNextDay = () => {
    setSelectedDate(prev => {
      const newDate = new Date(prev);
      newDate.setDate(prev.getDate() + 1);
      return newDate;
    });
  };

  const handleToday = () => {
    setSelectedDate(new Date());
  };

  return (
    <div className="p-6 max-w-[1400px] mx-auto space-y-6">
      {showConfetti && (
        <ReactConfetti
          width={window.innerWidth}
          height={window.innerHeight}
          recycle={false}
          numberOfPieces={200}
          gravity={0.3}
        />
      )}

      {/* KPIs */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Dashboard</h2>
        </div>

        <div className="grid grid-cols-4 gap-4">
          {kpiData.map((kpi, index) => (
            <Card key={index} className="p-4">
              <h3 className="text-sm font-medium text-gray-500">{kpi.label}</h3>
              <div className="mt-1 space-y-2">
                <div className="flex justify-between items-baseline">
                  <p className="text-2xl font-bold">{kpi.value}</p>
                  <p className="text-sm text-gray-500">Goal: {kpi.goal}</p>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${kpi.progress}%` }}
                  />
                </div>
                <p className="text-xs text-gray-600">{kpi.trend}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Top Row - Tasks & Agenda */}
      <div className="grid grid-cols-2 gap-6">
        {/* Tasks Section */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Tasks</h2>
          <Card className="p-4 space-y-4">
            {isLoadingTasks ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
              </div>
            ) : isTasksError ? (
              <div className="text-center py-4 text-red-500">
                Failed to load tasks. Please try again later.
              </div>
            ) : tasks.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No tasks available
              </div>
            ) : (
              tasks.map((task) => {
                const currentStatus = taskStatuses.get(task.id) || task.status;
                return (
                  <div
                    key={task.id}
                    className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg transition-all duration-200"
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 rounded-full p-0 transition-all duration-200"
                      onClick={() => handleTaskComplete(task.id.toString(), currentStatus, task.case, task.title)}
                      disabled={updateTaskMutation.isPending}
                    >
                      {(() => {
                        const StatusIcon = currentStatus === TaskStatus.COMPLETED ? CheckCircle2 :
                                         currentStatus === TaskStatus.IN_PROGRESS ? Clock :
                                         currentStatus === TaskStatus.REOPENED ? AlertCircle : Circle;
                        return (
                          <StatusIcon
                            className={`h-4 w-4 transition-all duration-200 ${
                              currentStatus === TaskStatus.COMPLETED
                                ? "text-green-500 fill-green-500"
                                : currentStatus === TaskStatus.IN_PROGRESS
                                ? "text-[#060216]-500"
                                : currentStatus === TaskStatus.REOPENED
                                ? "text-red-500"
                                : ""
                            }`}
                          />
                        );
                      })()}
                    </Button>
                    <div className="flex-1">
                      <h3
                        className={`font-medium transition-all duration-200 ${
                          currentStatus === TaskStatus.COMPLETED
                            ? "line-through text-gray-400"
                            : ""
                        }`}
                      >
                        {task.title}
                      </h3>
                      <p
                        className={`text-sm transition-all duration-200 ${
                          currentStatus === TaskStatus.COMPLETED
                            ? "text-gray-400"
                            : "text-gray-600"
                        }`}
                      >
                        Due: {task.due_date ? format(new Date(task.due_date), 'MM/dd/yyyy') : 'No due date'}
                      </p>
                    </div>
                  </div>
                );
              })
            )}
          </Card>
        </div>

        {/* Agenda Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Agenda</h2>
            <div className="flex items-center gap-2">
              <div className="flex items-center bg-secondary rounded-lg p-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePrevDay}
                  className="h-8 px-2 hover:bg-background/50"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleToday}
                  className={cn(
                    "h-8 px-3",
                    format(selectedDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
                      ? "bg-primary text-primary-foreground hover:bg-primary/90"
                      : "hover:bg-background/50"
                  )}
                >
                  Today
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleNextDay}
                  className="h-8 px-2 hover:bg-background/50"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
              <Button variant="link" className="text-[#060216]-600" asChild>
                <a href="/calendar">View Calendar</a>
              </Button>
            </div>
          </div>
          <Card className="p-4 space-y-4">
            {isCheckingIntegration ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
              </div>
            ) : !isCalendarIntegrated ? (
              <div className="flex flex-col items-center justify-center py-8">
                <h3 className="text-xl font-bold mb-4">Calendar Integration Required</h3>
                <p className="text-gray-600 mb-4">Please connect your calendar to continue</p>
                <Button onClick={handleIntegration}>
                  Connect Calendar
                </Button>
              </div>
            ) : isLoadingAgenda ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
              </div>
            ) : isAgendaError ? (
              <div className="text-center py-4 text-red-500">
                Failed to load agenda. Please try again later.
              </div>
            ) : !agendaData || agendaData.days.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No events scheduled
              </div>
            ) : (
              <div className="space-y-6">
                {agendaData.days
                  .filter(day => {
                    const dayDate = new Date(day.date);
                    const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');
                    const dayDateStr = format(dayDate, 'yyyy-MM-dd');
                    return dayDateStr === selectedDateStr;
                  })
                  .map((day) => (
                    <div key={day.date}>
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium text-gray-900">
                          {format(new Date(day.date), "EEEE")}
                        </h3>
                        <span className="text-sm text-gray-500">
                          {format(new Date(day.date), "MMMM d")}
                        </span>
                        {format(new Date(day.date), 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd') && (
                          <span className="px-2 py-0.5 rounded-full text-xs bg-blue-100 text-[#060216]-800">
                            Today
                          </span>
                        )}
                      </div>
                      {day.events.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">
                          No events scheduled for this day
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {day.events.map((event) => {
                            const { cleanTitle, cleanDescription, meetLinks } = cleanAndExtractEventInfo(event);
                            const hasGoogleMeet = meetLinks.some((link: string) => link.includes('meet.google.com'));

                            return (
                              <div key={event.id} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded-lg">
                                <div className="w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center">
                                  <span className="text-purple-600 text-lg">
                                    {hasGoogleMeet ? '👥' : '⚖️'}
                                  </span>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h3 className="font-medium">{cleanTitle}</h3>
                                  <p className="text-sm text-gray-600">
                                    {event.is_all_day ? (
                                      "All day"
                                    ) : event.start_time && event.end_time ? (
                                      <>
                                        {format(new Date(event.start_time * 1000), "h:mm a")} - {format(new Date(event.end_time * 1000), "h:mm a")}
                                      </>
                                    ) : (
                                      "Time not specified"
                                    )}
                                  </p>
                                  {meetLinks.length > 0 && (
                                    <div className="mt-1 space-x-2">
                                      {meetLinks.map((link: string, index: number) => (
                                        <Button
                                          key={index}
                                          variant="outline"
                                          size="sm"
                                          className="text-xs h-7 bg-blue-50 hover:bg-blue-100 text-[#060216]-600 border-blue-200"
                                          onClick={() => window.open(link, '_blank')}
                                        >
                                          Join Meeting
                                        </Button>
                                      ))}
                                    </div>
                                  )}
                                  {cleanDescription && !cleanDescription.includes('meet.google.com') && (
                                    <div
                                      className="text-sm text-gray-500 mt-1 break-words"
                                      dangerouslySetInnerHTML={{ __html: cleanDescription }}
                                    />
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* Recent Cases Section (moved to bottom) */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Recent Cases</h2>
          <Button variant="ghost" className="text-[#060216]-600">
            View All Cases <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-3 gap-4">
          {recentCases.map((case_) => (
            <Card
              key={case_.id}
              className="p-4 hover:shadow-md transition-shadow"
            >
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{case_.client}</h3>
                    <p className="text-sm text-gray-500">{case_.caseType}</p>
                  </div>
                  <div
                    className={`
                    px-2 py-1 rounded-full text-xs
                    ${
                      case_.status === "urgent"
                        ? "bg-red-100 text-red-800"
                        : case_.status === "active"
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }
                  `}
                  >
                    {case_.status}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>
                      {case_.lastAction} • {case_.lastActionDate}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p className="font-medium">Next Deadline:</p>
                    <p>{case_.nextDeadline}</p>
                  </div>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <FileText className="h-4 w-4 mr-2" />
                    Documents
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Notes
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
