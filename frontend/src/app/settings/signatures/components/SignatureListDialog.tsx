"use client"

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, <PERSON>cil, Trash2, Loader2 } from 'lucide-react'
import emailService from '@/services/emailCalenderIntegrationService'
import { EmailSignature } from '@/type/emailCalender'
import SignatureDialog from './SignatureDialog'
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"

interface SignatureListDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function SignatureListDialog({ open, onOpenChange }: SignatureListDialogProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingSignature, setEditingSignature] = useState<EmailSignature | null>(null)
  const [deleteSignatureId, setDeleteSignatureId] = useState<string | null>(null)
  const { toast } = useToast()

  const { data: signatures, isLoading } = emailService.useSignatures()
  const deleteSignatureMutation = emailService.useDeleteSignature()

  const handleDelete = async (id: string) => {
    setDeleteSignatureId(id)
  }

  const handleConfirmDelete = async () => {
    if (!deleteSignatureId) return
    
    try {
      await deleteSignatureMutation.mutateAsync(deleteSignatureId)
      toast({
        title: "Success",
        description: "Signature deleted successfully",
      })
    } catch (error) {
      console.error('Error deleting signature:', error)
      toast({
        title: "Error",
        description: "Failed to delete signature",
        variant: "destructive",
      })
    } finally {
      setDeleteSignatureId(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">
            Manage Signatures
          </DialogTitle>
        </DialogHeader>

        <div className="flex justify-end mb-4">
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add New
          </Button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-[#060216]/50" />
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Content</TableHead>
                <TableHead>Default</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {signatures?.map((signature) => (
                <TableRow key={signature.id}>
                  <TableCell>{signature.name}</TableCell>
                  <TableCell className="max-w-[400px] truncate">
                    {signature.content}
                  </TableCell>
                  <TableCell>{signature.is_default ? 'Yes' : 'No'}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setEditingSignature(signature)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => signature.id && handleDelete(signature.id)}
                        disabled={deleteSignatureMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        <SignatureDialog
          open={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          signature={null}
        />

        <SignatureDialog
          open={!!editingSignature}
          onOpenChange={(open) => !open && setEditingSignature(null)}
          signature={editingSignature}
        />

        <DeleteConfirmationDialog
          open={!!deleteSignatureId}
          onOpenChange={(open) => !open && setDeleteSignatureId(null)}
          onConfirm={handleConfirmDelete}
          text="signature"
        />
      </DialogContent>
    </Dialog>
  )
} 