"use client"

import { useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { EmailSignature } from '@/type/emailCalender'
import emailService from '@/services/emailCalenderIntegrationService'
import { Textarea } from "@/components/ui/textarea"

const signatureSchema = z.object({
    name: z.string().min(1, "Name is required"),
    content: z.string().min(1, "Content is required"),
    is_default: z.boolean().default(false)
})

type SignatureFormData = z.infer<typeof signatureSchema>

interface SignatureDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    signature?: EmailSignature | null
}

export default function SignatureDialog({ open, onOpenChange, signature }: SignatureDialogProps) {
    const defaultValues = {
        name: signature?.name || "",
        content: signature?.content || "",
        is_default: signature?.is_default || false
    }
    const form = useForm<SignatureFormData>({
        resolver: zodResolver(signatureSchema),
        defaultValues: defaultValues,
    })

    const createSignatureMutation = emailService.useCreateSignature()
    const updateSignatureMutation = emailService.useUpdateSignature()

    useEffect(() => {
        form.reset(defaultValues)
    }, [signature, form])

    const onSubmit = async (data: SignatureFormData) => {
        try {
            if (signature?.id) {
                await updateSignatureMutation.mutateAsync({
                    id: signature.id,
                    data
                })
            } else {
                await createSignatureMutation.mutateAsync(data)
            }
            form.reset(defaultValues)
            onOpenChange(false)
        } catch (error) {
            console.error('Error saving signature:', error)
        }
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-3xl">
                <DialogHeader>
                    <DialogTitle>
                        {signature ? 'Edit Signature' : 'Add Signature'}
                    </DialogTitle>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Signature name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="content"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Content</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Enter your signature content here..."
                                            className="min-h-[200px] font-mono"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="is_default"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                        <FormLabel>
                                            Set as default signature
                                        </FormLabel>
                                    </div>
                                </FormItem>
                            )}
                        />

                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => onOpenChange(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={createSignatureMutation.isPending || updateSignatureMutation.isPending}
                            >
                                {signature ? 'Update' : 'Create'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
} 