import React, { MouseEvent, useState, useMemo, useCallback } from 'react';
import { NylasEvent, EventFormData } from '@/type/emailCalender';
import { format } from 'date-fns';
import {
  Video,
  Edit,
  X,
  Calendar,
  Users,
  MapPin,
  FileText,
  Copy,
  Trash2,
  Briefcase,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import CKEditorViewer from "@/components/CKEditorViewer";
import { AddEditEvent } from "./AddEditEvent";
import { Separator } from "@/components/ui/separator";
import { useDeleteEvent } from "@/services/emailCalenderIntegrationService";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

type CalendarView = 'month' | 'week' | 'day' | 'agenda';

interface CalendarEventProps {
  event: NylasEvent;
  view: CalendarView;
  onEventUpdate?: (updatedData: Partial<NylasEvent>) => Promise<void>;
  handleEventSuccess?: () => void;
  color?: string;
}

export const CalendarEvent: React.FC<CalendarEventProps> = ({ event, view, onEventUpdate, handleEventSuccess, color }) => {
  const [calendarEventViewOpen, setCalendarEventViewOpen] = useState(false);
  const [calendarEventEditOpen, setCalendarEventEditOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const deleteEvent = useDeleteEvent();

  // Ensure we have valid event data
  const safeEvent = useMemo(() => {
    const now = Math.floor(Date.now() / 1000);
    return {
      ...event,
      title: event.title || '',
      description: event.description || '',
      location: event.location || '',
      start_time: event.start_time,
      end_time: event.end_time,
      start_date: event.start_date,
      end_date: event.end_date,
      is_all_day: event.is_all_day || false,
      participants: event.participants || [],
      case_id: event.case_id || '',
    };
  }, [event]);

  const eventData: EventFormData = {
    title: safeEvent.title,
    description: safeEvent.description,
    location: safeEvent.location,
    start_time: safeEvent.start_time,
    end_time: safeEvent.end_time,
    start_date: safeEvent.start_date,
    end_date: safeEvent.end_date,
    is_all_day: safeEvent.is_all_day,
    participants: safeEvent.participants || [],
    case_id: safeEvent.case_id
  };

  // Get event date for display
  const getEventDate = (timestamp?: number): Date => {
    if (!timestamp) return new Date();
    try {
      return new Date(timestamp * 1000);
    } catch (e) {
      console.error('Invalid timestamp:', timestamp, e);
      return new Date();
    }
  };

  // Get formatted date and time for display
  const getFormattedDateTime = useMemo(() => {
    if (safeEvent.is_all_day) {
      // For all-day events, use start_date
      if (safeEvent.start_date) {
        const startDate = new Date(safeEvent.start_date);
        const formattedDate = format(startDate, 'EEE, MMM d');

        // If there's an end_date and it's different from start_date
        if (safeEvent.end_date) {
          const endDate = new Date(safeEvent.end_date);
          // Subtract one day from end_date because it's exclusive in the API
          endDate.setDate(endDate.getDate() - 1);

          // If multi-day event
          if (endDate.getTime() > startDate.getTime()) {
            const formattedEndDate = format(endDate, 'EEE, MMM d');
            return {
              dateDisplay: `${formattedDate} - ${formattedEndDate}`,
              timeDisplay: 'All day',
              formattedDateShort: formattedDate
            };
          }
        }

        return {
          dateDisplay: formattedDate,
          timeDisplay: 'All day',
          formattedDateShort: formattedDate
        };
      }
    }

    // For regular events with timestamps
    if (safeEvent.start_time) {
      const startDate = getEventDate(safeEvent.start_time);
      const formattedDate = format(startDate, 'EEE, MMM d');
      const startTime = format(startDate, 'h:mm a');
      const endTime = safeEvent.end_time ? format(getEventDate(safeEvent.end_time), 'h:mm a') : '';

      return {
        dateDisplay: formattedDate,
        timeDisplay: `${startTime}${endTime ? ` - ${endTime}` : ''}`,
        formattedDateShort: formattedDate,
        startTime,
        endTime
      };
    }

    // Fallback
    return {
      dateDisplay: format(new Date(), 'EEE, MMM d'),
      timeDisplay: '',
      formattedDateShort: format(new Date(), 'EEE, MMM d'),
      startTime: '',
      endTime: ''
    };
  }, [safeEvent]);

  const { dateDisplay, timeDisplay, formattedDateShort, startTime, endTime } = getFormattedDateTime;

  const hasMeetingLink = safeEvent.description?.includes('meet.google.com') ||
    safeEvent.description?.includes('zoom.us') ||
    safeEvent.description?.includes('teams.microsoft.com');

  // Extract meeting link from description
  const getMeetingLink = () => {
    if (!safeEvent.description) return null;
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const matches = safeEvent.description.match(urlRegex);
    if (!matches) return null;
    return matches.find(url =>
      url.includes('meet.google.com') ||
      url.includes('zoom.us') ||
      url.includes('teams.microsoft.com')
    );
  };

  // Function to determine if the background color is dark
  const isColorDark = (color: string) => {
    try {
      const hex = color.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      return brightness < 128;
    } catch (e) {
      return false;
    }
  };

  // Get background color
  const backgroundColor = safeEvent.color || 'transparent';
  const isDark = isColorDark(backgroundColor);

  // Define text colors based on background
  const textColor = isDark ? 'text-white' : 'text-gray-900';
  // const subTextColor = isDark ? 'text-white/90' : 'text-gray-700';
  const mutedTextColor = isDark ? 'text-white/75' : 'text-gray-600';

  const renderEventContent = () => {
    switch (view) {
      case 'month':
        return (
          <div className={`flex items-center gap-1 px-1 py-0.5 overflow-hidden h-full ${textColor}`}>
            <span className={`text-[10px] font-medium ${mutedTextColor} whitespace-nowrap`}>
              {safeEvent.is_all_day ? '🕒 All day' : startTime}
            </span>
            <span className="text-xs font-medium truncate leading-tight flex-1">{safeEvent.title}</span>
            {hasMeetingLink && <Video className="h-3 w-3" />}
          </div>
        );
      case 'agenda':
        return (
          <div className="space-y-1.5" style={{ borderLeft: color ? `4px solid ${color}` : 'none', paddingLeft: color ? '8px' : '0', backgroundColor: 'transparent' }}>
            <h4 className="font-medium text-xs line-clamp-2">{safeEvent.title}</h4>
            <div className="text-xs text-muted-foreground">
              {safeEvent.is_all_day ? 'All day' : timeDisplay}
            </div>
            {hasMeetingLink && (
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start gap-1 h-6 text-xs bg-blue-50/50 hover:bg-blue-100/50"
                onClick={(e) => {
                  e.stopPropagation();
                  const meetingLink = getMeetingLink();
                  if (meetingLink) {
                    window.open(meetingLink, '_blank');
                  }
                }}
              >
                <Video className="h-3 w-3 text-[#060216]-600" />
                <span className="text-[#060216]-600 truncate">
                  Join Meeting
                </span>
              </Button>
            )}
          </div>
        );
      case 'week':
        return (
          <div className={`flex items-center gap-2 px-1.5 py-1 overflow-hidden h-full ${textColor}`}>
            <span className={`text-xs font-medium ${mutedTextColor} whitespace-nowrap`}>
              {safeEvent.is_all_day ? 'All day' : startTime}
            </span>
            <span className="text-sm font-medium truncate leading-tight flex-1">{safeEvent.title}</span>
            {hasMeetingLink && <Video className="h-3.5 w-3.5" />}
          </div>
        );
      case 'day':
        return (
          <div className={`flex items-center gap-2 px-2 py-1 overflow-hidden h-full ${textColor}`}>
            <span className={`text-xs font-medium ${mutedTextColor} whitespace-nowrap`}>
              {safeEvent.is_all_day ? 'All day' : startTime}
            </span>
            <span className="text-sm font-medium truncate leading-tight flex-1">{safeEvent.title}</span>
            {hasMeetingLink && <Video className="h-3.5 w-3.5" />}
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-1 px-1 py-0.5 overflow-hidden text-white h-full">
            <span className="text-xs font-medium truncate leading-tight flex-1">{safeEvent.title}</span>
          </div>
        );
    }
  };

  const meetingLink = getMeetingLink();

  const handleEditClick = useCallback((e: MouseEvent) => {
    try {
      e.stopPropagation();
      setCalendarEventViewOpen(false);
      setCalendarEventEditOpen(true);
    } catch (error) {
      console.error('Error handling edit click:', error);
    }
  }, []);

  const handleEditSuccess = useCallback(() => {
    try {
      setCalendarEventEditOpen(false);
      setCalendarEventViewOpen(false);
      handleEventSuccess?.();
    } catch (error) {
      console.error('Error handling edit success:', error);
    }
  }, [handleEventSuccess]);

  const handleViewClose = useCallback(() => {
    try {
      setCalendarEventViewOpen(false);
    } catch (error) {
      console.error('Error handling view close:', error);
    }
  }, []);

  const handleDeleteClick = useCallback((e: MouseEvent) => {
    try {
      e.stopPropagation();
      setDeleteDialogOpen(true);
    } catch (error) {
      console.error('Error handling delete click:', error);
    }
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    try {
      await deleteEvent.mutateAsync({
        event_id: safeEvent.id,
        calendar_id: safeEvent.calendar_id,
        case_id: safeEvent.case_id
      });
      setDeleteDialogOpen(false);
      setCalendarEventViewOpen(false);
      handleEventSuccess?.();
    } catch (error) {
      console.error('Error handling delete confirmation:', error);
    }
  }, [deleteEvent, safeEvent.id, safeEvent.calendar_id, safeEvent.case_id, handleEventSuccess]);

  const handleEventClick = useCallback(() => {
    try {
      setCalendarEventViewOpen(true);
    } catch (error) {
      console.error('Error handling event click:', error);
    }
  }, []);

  return (
    <>
      <div
        className="rounded-sm hover:shadow-sm transition-shadow duration-200 h-full cursor-pointer"
        style={{
          backgroundColor,
          minHeight: view === 'month' ? '14px' : '22px',
          padding: view === 'month' ? '0 2px' : '1px 4px'
        }}
        onClick={handleEventClick}
      >
        {renderEventContent()}
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          try {
            setDeleteDialogOpen(open);
          } catch (error) {
            console.error('Error handling delete dialog change:', error);
          }
        }}
        onConfirm={handleDeleteConfirm}
        text="Event"
      />

      {/* View Dialog */}
      <Dialog
        open={calendarEventViewOpen}
        onOpenChange={(open) => {
          try {
            setCalendarEventViewOpen(open);
          } catch (error) {
            console.error('Error handling view dialog change:', error);
          }
        }}
      >
        <DialogContent className="p-0 sm:max-w-[448px]" hideCloseButton>
          <DialogHeader className="sr-only">
            <DialogTitle>Event Details</DialogTitle>
          </DialogHeader>
          <div className="max-h-[70vh] overflow-y-auto px-4 py-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {/* Header */}
            <div className="flex justify-between items-start">
              <h2 className="text-xl font-semibold text-gray-900">{safeEvent.title}</h2>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleEditClick}
                  className="hover:bg-gray-100"
                >
                  <Edit className="h-4 w-4 text-green-500" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDeleteClick}
                  className="hover:bg-gray-100"
                >
                  <Trash2 className="h-4 w-4 text-red-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleViewClose}
                  className="hover:bg-gray-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Date and Time */}
            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <div>{dateDisplay}</div>
                <div>{safeEvent.is_all_day ? 'All day' : timeDisplay}</div>
              </div>
            </div>

            {/* Case Name */}
            {safeEvent.case_id && safeEvent.case_name && (
              <div className="flex items-center gap-2 text-gray-600">
                <Briefcase className="h-4 w-4 text-gray-500" />
                <span>{safeEvent.case_name}</span>
              </div>
            )}

            {/* Google Meet Section */}
            {meetingLink && (
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Video className="h-4 w-4 text-blue-600" />
                  <span className="text-blue-600">Google Meet</span>
                </div>
                <Button
                  variant="outline"
                  className="w-full justify-start gap-2"
                  onClick={() => window.open(meetingLink, '_blank')}
                >
                  <Video className="h-4 w-4" />
                  Join with Google Meet
                </Button>
              </div>
            )}

            {/* Invite Link Button */}
            {/* <Button
              variant="outline"
              className="w-full justify-start gap-2"
              onClick={() => {/* Add copy link functionality }
            >
              <Copy className="h-4 w-4" />
              Invite via link
            </Button> */}

            <Separator />

            {/* Participants */}
            {safeEvent.participants && safeEvent.participants.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="h-4 w-4" />
                  <span>{safeEvent.participants.length} guests</span>
                </div>
                <div className="space-y-2">
                  {safeEvent.participants.map((participant) => (
                    <div key={participant.email} className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                        {participant.name?.[0] || participant.email[0].toUpperCase()}
                      </div>
                      <div className="text-sm">
                        <div className="text-gray-900">{participant.email}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Location */}
            {safeEvent.location && (
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{safeEvent.location}</span>
              </div>
            )}

            {/* Description */}
            {safeEvent.description && (
              <div className="flex items-start gap-2 text-gray-600">
                <FileText className="h-4 w-4 mt-1" />
                <CKEditorViewer content={safeEvent.description} minHeight="100px" />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <AddEditEvent
        mode="edit"
        eventId={safeEvent.id}
        defaultValues={eventData}
        onSuccess={handleEditSuccess}
        open={calendarEventEditOpen}
        onOpenChange={(open) => {
          try {
            setCalendarEventEditOpen(open);
          } catch (error) {
            console.error('Error handling edit dialog change:', error);
          }
        }}
        hideButton={true}
      />
    </>
  );
};