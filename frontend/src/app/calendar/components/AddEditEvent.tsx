"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    Di<PERSON>Header,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Footer,
    Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit, Plus, Loader2, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { useCreateEvent, useUpdateEvent } from "@/services/emailCalenderIntegrationService";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { EventFormData } from '@/type/emailCalender';
import dynamic from 'next/dynamic';
import CaseSelectionDialog from '@/components/CaseOverview/components/CaseSelectionDialog';
import { CaseResponse } from "@/type/dashboard";
import { useToast } from "@/hooks/use-toast";
import { useCases, useCase } from "@/services/caseService";
import { MultiSelect } from "@/components/ui/multi-select";
import { useOrganizationUsers } from "@/services/organizationService";

// Dynamically import CKEditor with SSR disabled
const CustomCKEditor = dynamic(
    () => import('@/components/CKEditor'),
    { ssr: false }
);

// Add CKEditor type
interface CKEditorInstance {
    destroy: () => void;
    getData: () => string;
}

interface LocalParticipant {
    email: string;
    name?: string | undefined;
    status?: string | undefined;
}

interface AddEditEventProps {
    mode: 'create' | 'edit';
    eventId?: string;
    defaultValues?: EventFormData;
    onSuccess?: () => void;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
    hideButton?: boolean;
}

const eventFormSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().optional(),
    location: z.string().optional(),
    is_all_day: z.boolean().optional().default(false),
    start_time: z.number().optional(),
    end_time: z.number().optional(),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    case_id: z.string().optional(),
    participants: z.array(z.object({
        email: z.string().email("Invalid email format"),
        name: z.string().optional(),
        status: z.string().optional()
    })).optional().default([])
}).refine((data) => {
    // For all-day events, validate start_date is present
    if (data.is_all_day) {
        return !!data.start_date;
    }

    // For regular events, validate that end time is after start time and at least 30 minutes apart
    if (data.start_time && data.end_time) {
        const thirtyMinutesInSeconds = 30 * 60;
        return data.end_time > data.start_time &&
            (data.end_time - data.start_time) >= thirtyMinutesInSeconds;
    }

    return false;
}, {
    message: "Event must have valid start and end times or dates",
    path: ["end_time"]
});

type EventFormValues = z.infer<typeof eventFormSchema>;

type TransformedEventData = Omit<EventFormValues, 'start_time' | 'end_time' | 'start_date' | 'end_date'> & {
    description: string;
    location: string;
    participants: Array<{
        email: string;
        name?: string;
        status?: string;
    }>;
    is_all_day: boolean;
    start_time?: number;
    end_time?: number;
    start_date?: string;
    end_date?: string;
};

export function AddEditEvent({
    mode = 'create',
    eventId,
    defaultValues,
    onSuccess,
    open: controlledOpen,
    onOpenChange: controlledOnOpenChange,
    hideButton = false,
}: AddEditEventProps) {
    // State management
    const [internalOpen, setInternalOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [caseLoading, setCaseLoading] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [caseSelectionOpen, setCaseSelectionOpen] = useState(false);
    const [isEditorReady, setIsEditorReady] = useState(false);
    const [selectedCase, setSelectedCase] = useState<CaseResponse | null>(null);
    const [searchValue, setSearchValue] = useState('');
    const [showAddExternal, setShowAddExternal] = useState(false);
    const [newParticipant, setNewParticipant] = useState<{ email: string; name: string }>({ email: '', name: '' });
    const [participantError, setParticipantError] = useState<string>('');

    // Refs and Hooks
    const editorRef = useRef<CKEditorInstance | null>(null);
    const { toast } = useToast();
    const { data: organizationUsers = [] } = useOrganizationUsers();
    const { data: casesData } = useCases({ search: '', page: 1, pageSize: 10 });

    // Add direct case fetching for edit mode
    const { data: directCaseData, isLoading: isDirectCaseLoading } = useCase(
        mode === 'edit' && defaultValues?.case_id ? defaultValues.case_id : ''
    );

    const createEventMutation = useCreateEvent();
    const updateEventMutation = useUpdateEvent();

    // Add new hook to track initial load
    const initialLoadRef = useRef(false);

    // Default values setup
    const defaultFormValues: EventFormValues = useMemo(() => {
        const now = new Date();
        const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowStr = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD

        if (mode === 'edit' && defaultValues) {
            return {
                title: defaultValues.title || "",
                description: defaultValues.description || "",
                location: defaultValues.location || "",
                is_all_day: defaultValues.is_all_day || false,
                start_time: defaultValues.is_all_day ? undefined : (defaultValues.start_time || Math.floor(Date.now() / 1000)),
                end_time: defaultValues.is_all_day ? undefined : (defaultValues.end_time || Math.floor(Date.now() / 1000) + 3600),
                start_date: defaultValues.is_all_day ? (defaultValues.start_date || today) : undefined,
                end_date: defaultValues.is_all_day ? (defaultValues.end_date || tomorrowStr) : undefined,
                case_id: defaultValues.case_id,
                participants: defaultValues.participants?.map(p => ({
                    email: p.email,
                    name: p.name || undefined,
                    status: p.status || 'pending'
                })) || []
            };
        }

        return {
            title: "",
            description: "",
            location: "",
            is_all_day: false,
            start_time: Math.floor(Date.now() / 1000),
            end_time: Math.floor(Date.now() / 1000) + 3600,
            start_date: undefined,
            end_date: undefined,
            case_id: undefined,
            participants: []
        };
    }, [mode, defaultValues]);

    // Form setup
    const form = useForm<EventFormValues>({
        resolver: zodResolver(eventFormSchema),
        defaultValues: defaultFormValues,
        mode: 'onChange'
    });

    // Dialog control
    const isControlled = controlledOpen !== undefined;
    const isOpen = isControlled ? controlledOpen : internalOpen;
    const setIsOpen = useCallback((value: boolean) => {
        if (isControlled) {
            controlledOnOpenChange?.(value);
        } else {
            setInternalOpen(value);
        }
    }, [isControlled, controlledOnOpenChange]);

    // Memoize the case loading callback
    const loadInitialCase = useCallback(async (caseId: string | undefined) => {
        if (!caseId) return;

        try {
            setCaseLoading(true);

            // First try to use the directly fetched case data
            if (directCaseData && directCaseData.id === caseId) {
                setSelectedCase(directCaseData);
                form.setValue('case_id', directCaseData.id, {
                    shouldDirty: false,
                    shouldValidate: true
                });
                return;
            }

            // Then try to find the case in the paginated results
            const caseFromList = casesData?.results?.find(c => c.id === caseId);
            if (caseFromList) {
                setSelectedCase(caseFromList);
                form.setValue('case_id', caseFromList.id, {
                    shouldDirty: false,
                    shouldValidate: true
                });
                return;
            }

            // If we have case_id from the event but no case data yet, use a fallback display name
            if (defaultValues?.case_id) {
                // Just set the case ID in the form
                form.setValue('case_id', defaultValues.case_id, {
                    shouldDirty: false,
                    shouldValidate: true
                });

                // Show a loading state in the UI
                setCaseLoading(true);

                // Create a simple display object for the UI
                // This is just for display purposes until the real case data loads
                const displayName = defaultValues?.case_name || `Case ${defaultValues.case_id}`;

                // Update the UI with a simplified case object
                const element = document.querySelector('input[placeholder="Selected case will appear here..."]') as HTMLInputElement;
                if (element) {
                    element.value = displayName;
                }
            }
        } catch (error) {
            console.error('Error loading initial case:', error);
            toast({
                title: "Error",
                description: "Failed to load case details. Please try again.",
                variant: "destructive"
            });
        } finally {
            setCaseLoading(false);
        }
    }, [casesData, directCaseData, form, toast, defaultValues]);

    useEffect(() => {
        loadInitialCase(defaultValues?.case_id);
    }, [defaultValues]);

    // Update the effect to handle initial load
    useEffect(() => {
        if (!initialLoadRef.current && defaultValues?.case_id) {
            initialLoadRef.current = true;
            loadInitialCase(defaultValues.case_id);
        }
    }, [defaultValues?.case_id, loadInitialCase]);

    useEffect(() => {
        if (directCaseData && defaultValues?.case_id === directCaseData.id) {
            setSelectedCase(directCaseData);
            setCaseLoading(false);
        }
    }, [directCaseData, defaultValues?.case_id]);

    // Reset initialLoadRef when dialog closes
    useEffect(() => {
        if (!isOpen) {
            initialLoadRef.current = false;
        }
    }, [isOpen]);

    // Memoize the case selection handler
    const handleCaseSelect = useCallback(async (selectedCaseData: CaseResponse) => {
        try {
            setCaseLoading(true);
            setSelectedCase(selectedCaseData);
            form.setValue('case_id', selectedCaseData.id, {
                shouldDirty: true,
                shouldValidate: true
            });
            setCaseSelectionOpen(false);
        } catch (error) {
            console.error('Error selecting case:', error);
            toast({
                title: "Error",
                description: "Failed to select case. Please try again.",
                variant: "destructive"
            });
        } finally {
            setCaseLoading(false);
        }
    }, [form, toast]);

    // Effects
    useEffect(() => {
        let timeoutId: NodeJS.Timeout;

        if (isOpen) {
            setIsEditorReady(false);
            timeoutId = setTimeout(() => {
                setIsEditorReady(true);
            }, 100);
        } else {
            setIsEditorReady(false);
        }

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [isOpen]);

    useEffect(() => {
        if (isOpen) {
            form.reset(defaultFormValues);
            setIsEditorReady(false);
            const timeoutId = setTimeout(() => {
                setIsEditorReady(true);
            }, 100);

            return () => clearTimeout(timeoutId);
        }
    }, [isOpen, defaultFormValues, form]);

    // Cleanup effect for CKEditor
    useEffect(() => {
        return () => {
            if (editorRef.current) {
                try {
                    editorRef.current.destroy();
                } catch (error) {
                    console.warn('CKEditor cleanup warning:', error);
                }
                editorRef.current = null;
            }
        };
    }, []);

    const handleOpenChange = (newOpen: boolean) => {
        if (!newOpen && form.formState.isDirty) {
            setShowUnsavedAlert(true);
        } else {
            setIsOpen(newOpen);
        }
    };

    const handleClose = useCallback(() => {
        setIsOpen(false);
        form.reset(defaultFormValues);
    }, [setIsOpen, form, defaultFormValues]);

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const formatDateForInput = (timestamp: number): string => {
        try {
            const date = new Date(timestamp * 1000);
            if (isNaN(date.getTime())) {
                return '';
            }

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        } catch (error) {
            console.error('Error formatting date:', error);
            return '';
        }
    };

    const handleDateChange = (field: 'start_time' | 'end_time', value: string) => {
        try {
            const date = new Date(value);
            if (isNaN(date.getTime())) {
                return;
            }
            const timestamp = Math.floor(date.getTime() / 1000);
            form.setValue(field, timestamp, {
                shouldValidate: true
            });
        } catch (error) {
            console.error('Error handling date change:', error);
        }
    };

    const onSubmit = async (data: EventFormValues) => {
        try {
            setLoading(true);

            // Transform participants to match API format
            let transformedData: TransformedEventData = {
                ...data,
                description: data.description || '',
                location: data.location || '',
                participants: data.participants?.map(p => ({
                    email: p.email,
                    name: p.name || undefined,
                    status: p.status || 'pending'
                })) || []
            };

            // Handle all-day events
            if (data.is_all_day) {
                // For all-day events, we need start_date and end_date
                if (!data.start_date) {
                    toast({
                        title: "Error",
                        description: "Start date is required for all-day events",
                        variant: "destructive"
                    });
                    setLoading(false);
                    return;
                }

                // Make sure we have the is_all_day flag and date fields
                transformedData = {
                    ...transformedData,
                    is_all_day: true,
                    start_date: data.start_date,
                    end_date: data.end_date,
                    // Remove time fields for all-day events
                    start_time: undefined,
                    end_time: undefined
                };
            } else {
                // For regular events, we need start_time and end_time
                if (!data.start_time || !data.end_time) {
                    toast({
                        title: "Error",
                        description: "Start time and end time are required for regular events",
                        variant: "destructive"
                    });
                    setLoading(false);
                    return;
                }

                // Make sure we have the time fields and remove date fields
                transformedData = {
                    ...transformedData,
                    is_all_day: false,
                    start_time: data.start_time,
                    end_time: data.end_time,
                    // Remove date fields for regular events
                    start_date: undefined,
                    end_date: undefined
                };
            }

            if (mode === 'edit' && eventId) {
                await updateEventMutation.mutateAsync({
                    event_id: eventId,
                    ...transformedData
                });
                toast({
                    title: "Success",
                    description: "Event updated successfully"
                });
            } else {
                await createEventMutation.mutateAsync(transformedData);
                toast({
                    title: "Success",
                    description: "Event created successfully"
                });
            }

            // Reset form and local state
            form.reset({
                ...defaultFormValues,
                case_id: undefined,
                participants: []
            });
            setSelectedCase(null);
            setNewParticipant({ email: '', name: '' });
            setSearchValue('');
            setShowAddExternal(false);
            setParticipantError('');

            // Call onSuccess callback first to trigger parent refetch
            onSuccess?.();
            // Then close the dialog
            handleClose();
        } catch (error) {
            console.error('Error submitting event:', error);
            toast({
                title: "Error",
                description: "Failed to save event. Please try again.",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const validateCustomEmail = (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim());
    };

    const handleMultiSelectParticipantChange = (selectedValues: string[]) => {
        const currentParticipants = form.getValues('participants') || [];
        const customParticipants = currentParticipants.filter(
            (participant) => !organizationUsers.some(user => user.email === participant.email)
        );

        const selectedOrgParticipants: LocalParticipant[] = selectedValues
            .map(value => {
                const [email, name] = value.split('|');
                const orgUser = organizationUsers.find(user => user.email === email);
                return {
                    email,
                    name: orgUser ? `${orgUser.first_name || ''} ${orgUser.last_name || ''}`.trim() || undefined : name || undefined,
                    status: 'pending'
                };
            });

        form.setValue('participants', [...selectedOrgParticipants, ...customParticipants], { shouldValidate: true });
    };

    const handleAddParticipant = (emailToAdd?: string) => {
        const email = (emailToAdd || newParticipant.email).trim();
        setParticipantError('');

        if (!email || !validateCustomEmail(email)) {
            setParticipantError('Please enter a valid email address');
            return;
        }

        const currentParticipants = form.getValues('participants') || [];
        if (currentParticipants.some((p) => p.email.toLowerCase() === email.toLowerCase())) {
            setParticipantError('This email is already added to participants');
            return;
        }

        if (organizationUsers.some(user => user.email.toLowerCase() === email.toLowerCase())) {
            setParticipantError('This email belongs to an organization member. Please use the organization members dropdown above.');
            return;
        }

        const newParticipantData: LocalParticipant = {
            email: email,
            name: emailToAdd ? undefined : (newParticipant.name || undefined),
            status: 'pending'
        };

        form.setValue('participants', [...currentParticipants, newParticipantData], { shouldValidate: true });

        setNewParticipant({ email: '', name: '' });
        setSearchValue('');
        setShowAddExternal(false);
    };

    const handleRemoveParticipant = (email: string) => {
        const currentParticipants = form.getValues('participants') || [];
        form.setValue(
            'participants',
            currentParticipants.filter((p) => p.email !== email),
            { shouldValidate: true }
        );
    };

    // const handleMultiSelectParticipantChange = (selectedValues: string[]) => {
    //     const currentParticipants = form.getValues('participants') || [];

    //     // Keep all external participants (not organization users)
    //     const customParticipants = currentParticipants.filter(
    //         (participant) => !organizationUsers.some(user => user.email === participant.email)
    //     );

    //     // Get the emails of selected organization users
    //     const selectedOrgEmails = selectedValues.map(value => value.split('|')[0]);

    //     // Create participant objects for selected organization users
    //     const selectedOrgParticipants: LocalParticipant[] = selectedValues
    //         .map(value => {
    //             const [email, name] = value.split('|');
    //             const orgUser = organizationUsers.find(user => user.email === email);
    //             return {
    //                 email,
    //                 name: orgUser ? `${orgUser.first_name || ''} ${orgUser.last_name || ''}`.trim() || undefined : name || undefined,
    //                 status: 'pending'
    //             };
    //         });

    //     form.setValue('participants', [...selectedOrgParticipants, ...customParticipants], { shouldValidate: true });
    // };

    // Memoize the current participants
    const currentParticipants = useMemo(() => form.getValues('participants') || [], [form.watch('participants')]);

    // Memoize getParticipantOptions
    const participantOptionValues = useMemo(() => {
        // Get the current participants from the form
        const formParticipants = form.getValues('participants') || [];

        // Filter to only include organization users
        const orgParticipants = formParticipants.filter(
            (participant) => organizationUsers.some(user => user.email === participant.email)
        );

        // Map to the format expected by the MultiSelect component
        return orgParticipants.map((participant) => {
            const orgUser = organizationUsers.find(user => user.email === participant.email);
            if (!orgUser) return { label: participant.email, value: participant.email };

            const displayName = orgUser.first_name
                ? `${orgUser.first_name} ${orgUser.last_name || ''}`
                : orgUser.email;

            return {
                label: `${displayName} (${orgUser.email})${orgUser.role ? ` - ${orgUser.role}` : ''}`,
                value: `${orgUser.email}|${displayName}`.trim()
            };
        });
    }, [form.watch('participants'), organizationUsers]);

    // Update participantOptions to use memoized currentParticipants
    const participantOptions = useMemo(() => {
        const currentParticipantsList = form.getValues('participants') || [];
        return organizationUsers
            .filter(user => !currentParticipantsList.some((p) => p.email === user.email))
            .filter(user => {
                if (!searchValue) return true;

                const searchLower = searchValue.toLowerCase().trim();
                const displayName = (user.first_name
                    ? `${user.first_name} ${user.last_name || ''}`
                    : user.email).toLowerCase();
                const email = user.email.toLowerCase();
                const role = (user.role || '').toLowerCase();

                if (email === searchLower) return true;

                const searchTerms = searchLower.split(' ').filter(Boolean);

                if (searchTerms.length === 0) return true;

                return searchTerms.some(term => {
                    const nameMatches = displayName.split(' ')
                        .filter(Boolean)
                        .some(namePart => namePart.toLowerCase().includes(term));

                    const [emailUser, emailDomain] = email.split('@');

                    const emailMatches =
                        email.includes(term) ||
                        (emailUser && emailUser.includes(term)) ||
                        (emailDomain && emailDomain.includes(term)) ||
                        (term.includes('@') && email.includes(term));

                    const roleMatches = role.includes(term);

                    return nameMatches || emailMatches || roleMatches;
                });
            })
            .map(user => {
                const displayName = user.first_name
                    ? `${user.first_name} ${user.last_name || ''}`
                    : user.email;

                return {
                    label: `${displayName} (${user.email})${user.role ? ` - ${user.role}` : ''}`,
                    value: `${user.email}|${displayName}`.trim()
                };
            });
    }, [organizationUsers, searchValue, form]);

    const handleSearchChange = (value: string) => {
        setSearchValue(value);

        const trimmedValue = value.trim();
        const isValidEmail = validateCustomEmail(trimmedValue);
        const userExists = organizationUsers.some(user =>
            user.email.toLowerCase() === trimmedValue.toLowerCase()
        );
        const isAlreadyParticipant = form.getValues('participants').some(p =>
            p.email.toLowerCase() === trimmedValue.toLowerCase()
        );

        setShowAddExternal(!!(trimmedValue && isValidEmail && !userExists && !isAlreadyParticipant));
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    {!hideButton && (
                        mode === 'edit' ? (
                            <Button variant="link" size="sm">
                                <Edit className="h-4 w-4 text-green-600" />
                            </Button>
                        ) : (
                            <Button variant="link" className="text-green-600">
                                <Plus className="h-4 w-4" />
                                Event
                            </Button>
                        )
                    )}
                </DialogTrigger>

                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            {mode === 'edit' ? 'Edit Event' : 'Create New Event'}
                        </DialogTitle>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="space-y-6 max-h-[80vh] overflow-y-auto pr-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                {/* Title */}
                                <FormField
                                    control={form.control}
                                    name="title"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="after:content-['*'] after:text-red-500 after:ml-0.5">
                                                Event Title
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    placeholder="Enter event title"
                                                    className="bg-white"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Case Selection */}
                                <div className="grid gap-2">
                                    <Label className="text-sm">Case</Label>
                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Selected case will appear here..."
                                            value={caseLoading ? "Loading case..." : (selectedCase?.case_name || '')}
                                            readOnly
                                            className={`${caseLoading ? 'bg-gray-100 animate-pulse' : 'bg-gray-50'}`}
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => setCaseSelectionOpen(true)}
                                            disabled={caseLoading}
                                        >
                                            {caseLoading ? (
                                                <>
                                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                    Loading...
                                                </>
                                            ) : selectedCase ? (
                                                'Change Case'
                                            ) : (
                                                'Select Case'
                                            )}
                                        </Button>
                                    </div>
                                </div>

                                {/* All Day Event Checkbox */}
                                <FormField
                                    control={form.control}
                                    name="is_all_day"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                                            <FormControl>
                                                <Checkbox
                                                    checked={field.value}
                                                    onCheckedChange={(checked) => {
                                                        field.onChange(checked);
                                                        // When switching to all-day, set default dates
                                                        if (checked) {
                                                            const now = new Date();
                                                            const today = now.toISOString().split('T')[0];
                                                            const tomorrow = new Date(now);
                                                            tomorrow.setDate(tomorrow.getDate() + 1);
                                                            const tomorrowStr = tomorrow.toISOString().split('T')[0];

                                                            form.setValue('start_date', today);
                                                            form.setValue('end_date', tomorrowStr);
                                                        }
                                                    }}
                                                />
                                            </FormControl>
                                            <div className="space-y-1 leading-none">
                                                <FormLabel>All-day event</FormLabel>
                                                <FormDescription>
                                                    Event will be shown as an all-day event in the calendar
                                                </FormDescription>
                                            </div>
                                        </FormItem>
                                    )}
                                />

                                {/* Date and Time */}
                                <div className="grid gap-2">
                                    <Label className="text-sm after:content-['*'] after:text-red-500 after:ml-0.5">
                                        Date and Time
                                    </Label>

                                    {form.watch('is_all_day') ? (
                                        // All-day event date inputs
                                        <div className="grid grid-cols-2 gap-4">
                                            <FormField
                                                control={form.control}
                                                name="start_date"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>Start Date</FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="date"
                                                                value={field.value || ''}
                                                                onChange={(e) => {
                                                                    field.onChange(e.target.value);
                                                                }}
                                                                className="bg-white"
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                            <FormField
                                                control={form.control}
                                                name="end_date"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>End Date (exclusive)</FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="date"
                                                                value={field.value || ''}
                                                                onChange={(e) => {
                                                                    field.onChange(e.target.value);
                                                                }}
                                                                className="bg-white"
                                                            />
                                                        </FormControl>
                                                        <FormDescription>
                                                            The end date is exclusive (not included in the event)
                                                        </FormDescription>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    ) : (
                                        // Regular event datetime inputs
                                        <div className="grid grid-cols-2 gap-4">
                                            <FormField
                                                control={form.control}
                                                name="start_time"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>Start</FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="datetime-local"
                                                                value={formatDateForInput(field.value || 0)}
                                                                onChange={(e) => handleDateChange('start_time', e.target.value)}
                                                                className="bg-white"
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                            <FormField
                                                control={form.control}
                                                name="end_time"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>End</FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="datetime-local"
                                                                value={formatDateForInput(field.value || 0)}
                                                                onChange={(e) => handleDateChange('end_time', e.target.value)}
                                                                className="bg-white"
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    )}
                                </div>

                                {/* Location */}
                                <FormField
                                    control={form.control}
                                    name="location"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Location</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    placeholder="Add location"
                                                    className="bg-white"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Description */}
                                <FormField
                                    control={form.control}
                                    name="description"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Description</FormLabel>
                                            <FormControl>
                                                <div className="bg-white border rounded-md">
                                                    {isEditorReady && (
                                                        <CustomCKEditor
                                                            initialValue={field.value || ''}
                                                            onChange={(value) => field.onChange(value)}
                                                            className="min-h-[200px]"
                                                            placeholder="Add description..."
                                                        />
                                                    )}
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Organization Members MultiSelect */}
                                <div className="grid gap-2">
                                    <Label className="text-sm">Add Organization Members</Label>
                                    <div className={`grid ${showAddExternal ? 'grid-cols-2' : 'grid-cols-1'} gap-2`}>
                                        <MultiSelect
                                            options={participantOptions}
                                            onValueChange={handleMultiSelectParticipantChange}
                                            value={participantOptionValues.map((option: { value: string }) => option.value)}
                                            placeholder={participantOptions.length ? "Search..." : "No member available"}
                                            className="w-full bg-white"
                                            onSearch={handleSearchChange}
                                            hideSelectedValues={true}
                                        />

                                        {showAddExternal && searchValue && (
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                className="w-full h-full whitespace-nowrap"
                                                onClick={() => handleAddParticipant(searchValue)}
                                            >
                                                Add {searchValue} as External
                                            </Button>
                                        )}
                                    </div>
                                </div>

                                {/* Participants List */}
                                {currentParticipants.length > 0 && (
                                    <div className="mt-2 space-y-2">
                                        <Label>Selected Participants</Label>
                                        <div className="space-y-2">
                                            {currentParticipants.map((participant) => (
                                                <div key={participant.email} className="flex items-center justify-between bg-secondary p-2 rounded-md">
                                                    <div>
                                                        <div className="font-medium flex items-center gap-2">
                                                            {participant.email}
                                                            {organizationUsers.some(user => {
                                                                const orgUser = organizationUsers.find(u => u.email === participant.email);
                                                                return user.email === participant.email && orgUser?.role && (
                                                                    <span className="text-xs px-2 py-0.5 bg-blue-100 rounded-full text-blue-700">
                                                                        {orgUser.role}
                                                                    </span>
                                                                );
                                                            })}
                                                        </div>
                                                        {participant.name && (
                                                            <div className="text-sm text-muted-foreground">{participant.name}</div>
                                                        )}
                                                        {organizationUsers.some(user => user.email === participant.email) && (
                                                            <div className="text-xs text-blue-500">Organization Member</div>
                                                        )}
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleRemoveParticipant(participant.email)}
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* External Participants */}
                                <div className="grid gap-2">
                                    <Label className="text-sm">External Participants</Label>
                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Email"
                                            value={newParticipant.email}
                                            onChange={(e) => {
                                                setNewParticipant({ ...newParticipant, email: e.target.value });
                                                setParticipantError('');
                                            }}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter' && newParticipant.email) {
                                                    e.preventDefault();
                                                    handleAddParticipant();
                                                }
                                            }}
                                            type="email"
                                            className="bg-white"
                                        />
                                        <Input
                                            placeholder="Name (optional)"
                                            value={newParticipant.name}
                                            onChange={(e) => setNewParticipant({ ...newParticipant, name: e.target.value })}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter' && newParticipant.email) {
                                                    e.preventDefault();
                                                    handleAddParticipant();
                                                }
                                            }}
                                            className="bg-white"
                                        />
                                        <Button
                                            type="button"
                                            onClick={() => handleAddParticipant()}
                                            variant="outline"
                                            size="icon"
                                            disabled={!newParticipant.email || !validateCustomEmail(newParticipant.email)}
                                            title="Add Participant"
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                    {participantError && (
                                        <p className="text-sm text-red-500">{participantError}</p>
                                    )}
                                </div>
                            </div>

                            <DialogFooter>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        if (form.formState.isDirty) {
                                            setShowUnsavedAlert(true);
                                        } else {
                                            handleClose();
                                        }
                                    }}
                                    type="button"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={loading}>
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Saving...
                                        </>
                                    ) : mode === 'edit' ? (
                                        'Update Event'
                                    ) : (
                                        'Create Event'
                                    )}
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />

            <CaseSelectionDialog
                open={caseSelectionOpen}
                onOpenChange={setCaseSelectionOpen}
                onCaseSelect={handleCaseSelect}
                selectedCaseId={form.watch('case_id')}
            />
        </>
    );
}

export default AddEditEvent;
