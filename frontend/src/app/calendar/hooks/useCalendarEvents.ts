import { useState, useEffect, useCallback, useMemo } from 'react';
import { NylasEvent } from '@/type/emailCalender';
import { fetchUserNylasEvents } from '@/services/emailCalenderIntegrationService';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, startOfDay, endOfDay } from 'date-fns';
import { View } from 'react-big-calendar';
import { formatDateForInput } from '@/utils/dateUtils';

interface UseCalendarEventsProps {
  selectedUsers: Array<{ id: string; color: string }>;
  date: Date;
  view: View;
}

interface DateRange {
  start: Date;
  end: Date;
}

export const useCalendarEvents = ({ selectedUsers, date, view }: UseCalendarEventsProps) => {
  const [events, setEvents] = useState<NylasEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize the date range calculation
  const getDateRangeForView = useCallback((currentDate: Date, currentView: View): DateRange => {
    switch (currentView) {
      case 'month': {
        return {
          start: startOfMonth(currentDate),
          end: endOfMonth(currentDate)
        };
      }
      case 'week':
      case 'work_week': {
        return {
          start: startOfWeek(currentDate),
          end: endOfWeek(currentDate)
        };
      }
      case 'day': {
        return {
          start: startOfDay(currentDate),
          end: endOfDay(currentDate)
        };
      }
      case 'agenda': {
        // For agenda view, return a 2-week range
        const start = startOfDay(currentDate);
        const end = new Date(currentDate);
        end.setDate(end.getDate() + 14); // 2 weeks range
        end.setHours(23, 59, 59);
        return {
          start,
          end
        };
      }
      default: {
        return {
          start: startOfDay(currentDate),
          end: endOfDay(currentDate)
        };
      }
    }
  }, []);

  // Memoize the current date range
  const dateRange = useMemo(() => getDateRangeForView(date, view), [date, view, getDateRangeForView]);

  // Memoize the event processing function
  const processEvents = useCallback((newEvents: NylasEvent[], isDay: boolean) => {
    return newEvents.map((event, index) => {
      // Handle all-day events differently
      if (event.is_all_day || (event.start_date && event.end_date)) {
        // For all-day events, use the date strings to create Date objects
        let start: Date;
        let end: Date;

        if (event.start_date) {
          // Create date from start_date string using formatDateForInput to prevent timezone issues
          const formattedStartDate = formatDateForInput(event.start_date);
          start = new Date(formattedStartDate.split('-').reverse().join('-') + "T00:00:00");
        } else if (event.start_time) {
          // Fallback to start_time if available
          start = new Date(event.start_time * 1000);
        } else {
          // Last resort fallback
          start = new Date();
          console.warn('All-day event missing start date:', event.id);
        }

        if (event.end_date) {
          // Create date from end_date string using formatDateForInput to prevent timezone issues
          const formattedEndDate = formatDateForInput(event.end_date);
          const rawEndDate = new Date(formattedEndDate.split('-').reverse().join('-') + "T00:00:00");

          // Check if the end date is exactly one day after the start date
          const startDateObj = new Date(start);
          const oneDayAfterStart = new Date(startDateObj);
          oneDayAfterStart.setDate(oneDayAfterStart.getDate() + 1);

          if (rawEndDate.toISOString().split('T')[0] === oneDayAfterStart.toISOString().split('T')[0]) {
            // This is a single-day event, so set end to be the same as start
            end = new Date(start);
            end.setHours(23, 59, 59, 999);
          } else {
            // This is a multi-day event, so subtract one day from the end date
            end = new Date(rawEndDate);
            end.setDate(end.getDate() - 1);
            end.setHours(23, 59, 59, 999);
          }
        } else if (event.end_time) {
          // Fallback to end_time if available
          end = new Date(event.end_time * 1000);
        } else {
          // Last resort fallback - set to same day as start
          end = new Date(start);
          end.setHours(23, 59, 59, 999);
          console.warn('All-day event missing end date:', event.id);
        }

        return {
          ...event,
          title: event.title || '',
          start,
          end,
          is_all_day: true
        };
      } else {
        // Handle regular events with start_time and end_time
        if (!event.start_time || !event.end_time) {
          console.warn('Event missing required time properties:', event.id);
          return null;
        }

        const offsetMinutes = isDay ? (index % 3) : 0;
        const startTime = event.start_time * 1000 + offsetMinutes * 60000;
        const endTime = event.end_time * 1000 + offsetMinutes * 60000;

        return {
          ...event,
          title: event.title || '',
          start: new Date(startTime),
          end: new Date(endTime),
          start_time: Math.floor(startTime / 1000),
          end_time: Math.floor(endTime / 1000)
        };
      }
    }).filter(Boolean) as NylasEvent[]; // Filter out any null events
  }, []);

  const fetchEvents = useCallback(async () => {
    if (!selectedUsers.length) {
      setEvents([]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Skip fetching for agenda view as it uses its own data source
      if (view === 'agenda') {
        setEvents([]);
        return;
      }

      const newEvents: NylasEvent[] = [];

      // Fetch events for each selected user
      await Promise.all(selectedUsers.map(async (user) => {
        try {
          const userEvents = await fetchUserNylasEvents(user.id, dateRange.start, view);
          const eventsWithColor = userEvents.map(event => ({
            ...event,
            color: user.color
          }));
          newEvents.push(...eventsWithColor);
        } catch (error) {
          console.error(`Failed to fetch events for user ${user.id}:`, error);
          // Continue with other users even if one fails
        }
      }));

      // Process and set events
      const processedEvents = processEvents(newEvents, view === 'day');
      setEvents(processedEvents);
    } catch (error) {
      console.error('Failed to fetch calendar events:', error);
      setError('Failed to load calendar events');
      setEvents([]);
    } finally {
      setIsLoading(false);
    }
  }, [selectedUsers, view, dateRange, processEvents]);

  // Effect to fetch events when dependencies change
  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  return {
    events,
    isLoading,
    error,
    refetchEvents: fetchEvents // Return the memoized fetch function
  };
};