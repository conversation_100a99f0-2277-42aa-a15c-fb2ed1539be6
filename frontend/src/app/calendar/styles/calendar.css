/* Calendar Container */
.rbc-calendar {
  font-family: 'Roboto', sans-serif;
  border-radius: 8px;
  background-color: white;
}

/* Header Cells */
.rbc-header {
  padding: 8px 4px;
  font-weight: 500;
  font-size: 0.875rem;
  color: #70757a;
  border-bottom: 1px solid #e0e0e0;
  text-transform: uppercase;
}

/* Month View */
.rbc-month-view {
  border: none;
  background-color: white;
}

.rbc-month-row {
  border-color: #e0e0e0;
}

.rbc-day-bg {
  background-color: white;
}

.rbc-off-range-bg {
  background-color: #f8f9fa;
}

/* Today highlighting - specific to views */
.rbc-month-view .rbc-today {
  background-color: #1a73e8 !important;
  opacity: 0.04;
}

.rbc-date-cell.rbc-now {
  color: #1a73e8;
  font-weight: 500;
}

/* Week/Day View */
.rbc-time-view {
  border: none;
  background-color: white;
}

.rbc-time-header {
  border-color: #e0e0e0;
}

.rbc-time-content {
  border-color: #e0e0e0;
}

.rbc-timeslot-group {
  border-color: #e0e0e0;
}

.rbc-time-gutter {
  font-size: 0.75rem;
  color: #70757a;
}

/* Time Slots */
.rbc-time-slot {
  color: #70757a;
  font-size: 0.75rem;
}

/* Current Time Indicator */
.rbc-current-time-indicator {
  background-color: #db372d;
  height: 2px;
}

/* Event Styles */
.rbc-event {
  background-color: transparent !important;
  border: none;
  padding: 2px 8px;
  margin: 1px 0;
  color: white;
  border-radius: 4px;
  font-size: 0.8125rem;
}

.rbc-event-content {
  height: 100%;
  display: flex;
  align-items: center;
}

/* Selection Highlight */
/* .rbc-slot-selection {
  background-color: rgba(3, 155, 229, 0.1);
}

.rbc-slot-selecting {
  background-color: rgba(3, 155, 229, 0.2);
} */

/* Date Cell */
.rbc-date-cell {
  padding: 8px;
  font-size: 0.875rem;
  /* color: #3c4043; */
  text-align: center;
}

/* Off Range Dates */
.rbc-off-range {
  color: #70757a;
}

/* Selected Cell */
.rbc-selected-cell {
  background-color: rgba(3, 155, 229, 0.1);
}

/* Row Height */
.rbc-month-row {
  min-height: 120px;
}

/* Time Column */
.rbc-time-column {
  background-color: white;
}

/* All Day Events */
.rbc-allday-cell {
  background-color: #f8f9fa;
  border-color: #e0e0e0;
  max-height: 40px;
}

/* Toolbar Overrides */
.rbc-toolbar {
  margin-bottom: 20px;
  padding: 8px;
}

.rbc-toolbar button {
  color: #3c4043;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  background-color: white;
}

.rbc-toolbar button:hover {
  background-color: #f6f9fe;
  color: #1a73e8;
}

.rbc-toolbar button.rbc-active {
  background-color: #e8f0fe;
  color: #1a73e8;
  border-color: #1a73e8;
}

/* Event Overlapping */
.rbc-event.rbc-selected {
  background-color: #039be5 !important;
}

.rbc-event:focus {
  outline: none;
}

.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {
  border: none;
}

.rbc-day-slot .rbc-event:hover{
  /* background-color: #db372d; */
  filter: brightness(0.95);
  cursor: pointer;
}

.rbc-event-label{
  display: none;
}