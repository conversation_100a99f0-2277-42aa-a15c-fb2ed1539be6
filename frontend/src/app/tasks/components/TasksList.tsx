"use client";
import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Trash2,
  Edit,
  Calendar,
  User2,
  Users,
  Clock,
  CheckCircle2,
  Circle,
  AlertCircle,
  ChevronDown,
  Briefcase,
  ExternalLink
} from "lucide-react";
import Link from "next/link";
import { Task, TaskStatus, TaskStatusConfig, TaskPriority } from "@/type/case-management/noteTaskTypes";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  updateTask,
  deleteTask,
} from "@/services/case-management/noteTaskService";
import RichTextViewer from "@/components/ui/RichTextViewer";
import RichTextEditor from "@/components/ui/RichTextEditor";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface TasksListProps {
  tasks: Task[];
}

export function TasksList({ tasks }: TasksListProps) {
  const queryClient = useQueryClient();
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [editDescription, setEditDescription] = useState("");
  const [editPriority, setEditPriority] = useState<TaskPriority | undefined>();

  const updateTaskMutation = useMutation({
    mutationFn: (taskData: Task) => updateTask(taskData),
    onSuccess: () => {
      // Invalidate all task-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      queryClient.invalidateQueries({ queryKey: ["filtered-tasks"] });
      queryClient.invalidateQueries({ queryKey: ["tasks-filtered"] });
      queryClient.invalidateQueries({ queryKey: ["all-tasks"] });

      // Force a refetch of the current page
      window.dispatchEvent(new CustomEvent('task-updated'));
    },
  });

  const deleteTaskMutation = useMutation({
    mutationFn: (taskId: string) => deleteTask(taskId),
    onSuccess: () => {
      // Invalidate all task-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      queryClient.invalidateQueries({ queryKey: ["filtered-tasks"] });
      queryClient.invalidateQueries({ queryKey: ["tasks-filtered"] });
      queryClient.invalidateQueries({ queryKey: ["all-tasks"] });

      // Force a refetch of the current page
      window.dispatchEvent(new CustomEvent('task-updated'));
    },
  });

  const handleToggleComplete = (task: Task) => {
    updateTaskMutation.mutate({
      ...task,
      status: task.status === TaskStatus.COMPLETED ? TaskStatus.PENDING : TaskStatus.COMPLETED,
    });
  };

  const handleDeleteTask = (taskId: number) => {
    if (confirm("Are you sure you want to delete this task?")) {
      deleteTaskMutation.mutate(taskId.toString());
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setEditTitle(task.title);
    setEditDescription(task.description || "");
    setEditPriority(task.priority);
  };

  const handleSaveEdit = () => {
    if (!editingTask) return;

    updateTaskMutation.mutate({
      ...editingTask,
      title: editTitle,
      description: editDescription,
      priority: editPriority,
    });

    setEditingTask(null);
  };

  const handleStatusChange = (task: Task, newStatus: TaskStatus) => {
    updateTaskMutation.mutate({
      ...task,
      status: newStatus,
    });
  };

  const handlePriorityChange = (task: Task, newPriority: TaskPriority) => {
    updateTaskMutation.mutate({
      ...task,
      priority: newPriority,
    });
  };

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return <Circle className="h-4 w-4 text-yellow-500" />;
      case TaskStatus.IN_PROGRESS:
        return <Clock className="h-4 w-4 text-blue-500" />;
      case TaskStatus.COMPLETED:
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case TaskStatus.REOPENED:
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority?: TaskPriority) => {
    switch (priority) {
      case TaskPriority.LOW:
        return "text-green-500 bg-green-50";
      case TaskPriority.MEDIUM:
        return "text-blue-500 bg-blue-50";
      case TaskPriority.HIGH:
        return "text-orange-500 bg-orange-50";
      case TaskPriority.URGENT:
        return "text-red-500 bg-red-50";
      case TaskPriority.CRITICAL:
        return "text-purple-500 bg-purple-50";
      default:
        return "text-gray-500 bg-gray-50";
    }
  };

  const getPriorityLabel = (priority?: TaskPriority) => {
    if (!priority) return "Normal";

    return priority.charAt(0).toUpperCase() + priority.slice(1);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "No due date";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <>
      <ul className="space-y-3">
        {tasks.map((task) => (
          <li
            key={task.id}
            className={`flex flex-col p-4 rounded-lg transition-colors border ${
              task.status === TaskStatus.COMPLETED
                ? "bg-muted/30 border-muted"
                : "bg-background hover:bg-muted/10 border-border"
            }`}
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-start gap-3">
                <Checkbox
                  checked={task.status === TaskStatus.COMPLETED}
                  onCheckedChange={() => handleToggleComplete(task)}
                  className="mt-1"
                />
                <div>
                  <h3 className={`text-base font-medium ${task.status === TaskStatus.COMPLETED ? "line-through text-muted-foreground" : ""}`}>
                    {task.title}
                  </h3>

                  {/* Case Badge */}
                  {task.case && (
                    <div className="flex items-center gap-1 mt-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="flex items-center gap-1 bg-blue-50 hover:bg-blue-100 transition-colors">
                              <Briefcase className="h-3 w-3 text-blue-500" />
                              <Link
                                href={`/dashboard/case-view/${task.case}`}
                                className="text-xs text-blue-600 hover:underline flex items-center"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {task.case_name || `Case ${task.case}`}
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </Link>
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Click to view case details</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}

                  {task.description && (
                    <div className="mt-1 text-sm text-muted-foreground">
                      <RichTextViewer data={task.description} />
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-1">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEditTask(task)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDeleteTask(task.id)}>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="flex flex-wrap gap-3 mt-2 ml-10">
              {/* Status Badge */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Badge
                          variant="outline"
                          className="flex items-center gap-1 cursor-pointer hover:bg-muted"
                        >
                          {getStatusIcon(task.status)}
                          <span>{TaskStatusConfig[task.status]?.label || task.status}</span>
                          <ChevronDown className="h-3 w-3 ml-1" />
                        </Badge>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        {Object.entries(TaskStatusConfig).map(([status, config]) => (
                          <DropdownMenuItem
                            key={status}
                            onClick={() => handleStatusChange(task, status as TaskStatus)}
                            className="flex items-center gap-2"
                          >
                            {getStatusIcon(status as TaskStatus)}
                            <span>{config.label}</span>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Change status</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Priority Badge */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Badge
                          variant="outline"
                          className={`flex items-center gap-1 cursor-pointer hover:bg-muted ${task.priority ? getPriorityColor(task.priority) : ''}`}
                        >
                          <span>{task.priority ? getPriorityLabel(task.priority) : 'Normal'}</span>
                          <ChevronDown className="h-3 w-3 ml-1" />
                        </Badge>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onClick={() => handlePriorityChange(task, TaskPriority.LOW)}
                          className={`flex items-center gap-2 ${getPriorityColor(TaskPriority.LOW)}`}
                        >
                          <span>{getPriorityLabel(TaskPriority.LOW)}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handlePriorityChange(task, TaskPriority.MEDIUM)}
                          className={`flex items-center gap-2 ${getPriorityColor(TaskPriority.MEDIUM)}`}
                        >
                          <span>{getPriorityLabel(TaskPriority.MEDIUM)}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handlePriorityChange(task, TaskPriority.HIGH)}
                          className={`flex items-center gap-2 ${getPriorityColor(TaskPriority.HIGH)}`}
                        >
                          <span>{getPriorityLabel(TaskPriority.HIGH)}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handlePriorityChange(task, TaskPriority.URGENT)}
                          className={`flex items-center gap-2 ${getPriorityColor(TaskPriority.URGENT)}`}
                        >
                          <span>{getPriorityLabel(TaskPriority.URGENT)}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handlePriorityChange(task, TaskPriority.CRITICAL)}
                          className={`flex items-center gap-2 ${getPriorityColor(TaskPriority.CRITICAL)}`}
                        >
                          <span>{getPriorityLabel(TaskPriority.CRITICAL)}</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Change priority</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Due Date */}
              {task.due_date && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(task.due_date)}</span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Due date</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {/* Assigned To */}
              {task.assigned_to && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="flex items-center gap-1">
                        <User2 className="h-3 w-3" />
                        <span>{task.assigned_to.name}</span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Assigned to</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {/* Tagged Users */}
              {task.tagged_users && task.tagged_users.length > 0 && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span>{task.tagged_users.length} tagged</span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Tagged users: {task.tagged_users.map(u => u.name).join(', ')}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {/* Created By */}
              {task.created_by && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="flex items-center gap-1 text-muted-foreground">
                        <span>Created by {task.created_by.name}</span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Created on {format(new Date(task.created_at), "MMM dd, yyyy")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </li>
        ))}
      </ul>

      {/* Edit Task Dialog */}
      <Dialog open={!!editingTask} onOpenChange={(open) => !open && setEditingTask(null)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[70vh]">
            <div className="space-y-4 py-4 pr-4">
              <div className="space-y-2">
                <label htmlFor="title" className="text-sm font-medium">
                  Title
                </label>
                <Input
                  id="title"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  placeholder="Task title"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">
                  Description
                </label>
                <RichTextEditor
                  value={editDescription}
                  onChange={setEditDescription}
                  placeholder="Task description"
                  className="min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="priority" className="text-sm font-medium">
                  Priority
                </label>
                <Select
                  value={editPriority || ""}
                  onValueChange={(value) => setEditPriority(value as TaskPriority)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaskPriority.LOW}>Low</SelectItem>
                    <SelectItem value={TaskPriority.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={TaskPriority.HIGH}>High</SelectItem>
                    <SelectItem value={TaskPriority.URGENT}>Urgent</SelectItem>
                    <SelectItem value={TaskPriority.CRITICAL}>Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </ScrollArea>
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setEditingTask(null)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
