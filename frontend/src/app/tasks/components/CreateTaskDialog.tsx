"use client";
import { useState } from "react";
import { useCases } from "@/services/caseService";
import caseManagementService from "@/services/caseManagementService";
import { ChevronsUpDown, Calendar as CalendarIcon, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { TaskStatus, TaskStatusConfig, TaskPriority } from "@/type/case-management/noteTaskTypes";
import { useCreateTaskMutationWithAllFields } from "@/services/case-management/noteTaskService";
import RichTextEditor from "@/components/ui/RichTextEditor";



const formSchema = z.object({
  case: z.object({
    id: z.string(),
    case_name: z.string(),
  }).nullable(),
  assigned_to: z.object({
    id: z.number(),
    name: z.string(),
  }).nullable(),
  description: z.string()
    .min(1, "Description is required"),
  title: z.string().min(1, "Title is required"),
  status: z.string().default(TaskStatus.PENDING),
  priority: z.string().default("medium"),
  due_date: z.string().optional(),
  tagged_user_ids: z.array(z.number()).default([]),
})

interface CreateTaskDialogProps {
  open: boolean;
  onClose: () => void;
}

export function CreateTaskDialog({ open, onClose }: CreateTaskDialogProps) {
  const [searchCaseQuery, setSearchCaseQuery] = useState("");
  const [searchUserQuery, setSearchUserQuery] = useState("");
  const [searchTaggedUsersQuery, setSearchTaggedUsersQuery] = useState("");
  const [openCasePopover, setOpenCasePopover] = useState(false);
  const [openUserPopover, setOpenUserPopover] = useState(false);
  const [openTaggedUsersPopover, setOpenTaggedUsersPopover] = useState(false);
  const [openDatePopover, setOpenDatePopover] = useState(false);
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      case: null,
      assigned_to: null,
      description: "",
      title: "",
      status: TaskStatus.PENDING,
      priority: "medium",
      due_date: "",
      tagged_user_ids: [],
    },
  })

  const { data: casesData } = useCases(
    { search: searchCaseQuery },
    true
  );

  const { data: users } = caseManagementService.useAvailableUsers(
    form.watch("case")?.id || '',
    undefined
  );

  const createTaskMutation = useCreateTaskMutationWithAllFields();

  function onSubmit(values: z.infer<typeof formSchema>) {
    if (!values.case) {
      form.setError("case", { message: "Please select a case" });
      return;
    }

    createTaskMutation.mutate({
      case: values.case.id,
      assigned_to_id: values.assigned_to?.id,
      description: values.description,
      status: values.status as TaskStatus,
      title: values.title,
      priority: values.priority as TaskPriority,
      due_date: values.due_date || undefined,
      tagged_user_ids: values.tagged_user_ids,
    }, {
      onSuccess: () => {
        handleClose();
      }
    });
  }

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = form.formState.isDirty;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        onClose();
      }
    }
  };

  const handleClose = () => {
    onClose();
    form.reset();
  };

  const handleCancelClick = () => {
    const isDirty = form.formState.isDirty;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Task</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <ScrollArea className="max-h-[70vh] pr-4">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter task title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

              <FormField
                control={form.control}
                name="case"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Case</FormLabel>
                    <FormControl>
                      <Popover open={openCasePopover} onOpenChange={setOpenCasePopover}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openCasePopover}
                            className="w-full justify-between"
                          >
                            {field.value
                              ? `${field.value.id} - ${field.value.case_name}`
                              : "Select case..."}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[400px] p-0">
                          <div className="p-2">
                            <Input
                              placeholder="Search case..."
                              value={searchCaseQuery}
                              onChange={(e) => setSearchCaseQuery(e.target.value)}
                              className="mb-2"
                            />
                            <div className="max-h-[300px] overflow-y-auto">
                              {(casesData?.results || []).length === 0 ? (
                                <div className="p-2 text-sm text-muted-foreground">
                                  No case found.
                                </div>
                              ) : (
                                (casesData?.results || []).map((case_) => (
                                  <div
                                    key={case_.id}
                                    className={cn(
                                      "flex items-center px-2 py-1 cursor-pointer hover:bg-accent",
                                      field.value?.id === case_.id && "bg-accent"
                                    )}
                                    onClick={() => {
                                      form.setValue("case", case_);
                                      setOpenCasePopover(false);
                                    }}
                                  >
                                    {case_.id} - {case_.case_name}
                                  </div>
                                ))
                              )}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="assigned_to"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign To</FormLabel>
                    <FormControl>
                      <Popover open={openUserPopover} onOpenChange={setOpenUserPopover}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openUserPopover}
                            className="w-full justify-between"
                          >
                            {field.value ? field.value.name : "Assign to..."}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[400px] p-0">
                          <div className="p-2">
                            <Input
                              placeholder="Search user..."
                              value={searchUserQuery}
                              onChange={(e) => setSearchUserQuery(e.target.value)}
                              className="mb-2"
                            />
                            <div className="max-h-[300px] overflow-y-auto">
                              {(users || []).length === 0 ? (
                                <div className="p-2 text-sm text-muted-foreground">
                                  No user found.
                                </div>
                              ) : (
                                (users || []).map((user) => (
                                  <div
                                    key={user.id}
                                    className={cn(
                                      "flex items-center px-2 py-1 cursor-pointer hover:bg-accent",
                                      field.value?.id === user.id && "bg-accent"
                                    )}
                                    onClick={() => {
                                      form.setValue("assigned_to", user);
                                      setOpenUserPopover(false);
                                    }}
                                  >
                                    {user.name}
                                  </div>
                                ))
                              )}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <RichTextEditor
                        value={field.value ||""}
                        onChange={(value) => {
                          field.onChange(value);
                        }}
                        placeholder="Enter task description"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status Field */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(TaskStatusConfig).map(([status, config]) => (
                          <SelectItem key={status} value={status}>
                            <div className="flex items-center gap-2">
                              <span className={config.color}>{config.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority Field */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="regular">Regular</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date Field */}
              <FormField
                control={form.control}
                name="due_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover open={openDatePopover} onOpenChange={setOpenDatePopover}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(new Date(field.value), "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={(date: Date | undefined) => {
                            field.onChange(date ? format(date, "yyyy-MM-dd") : "");
                            setOpenDatePopover(false);
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tagged Users Field */}
              <FormField
                control={form.control}
                name="tagged_user_ids"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tagged Users</FormLabel>
                    <Popover open={openTaggedUsersPopover} onOpenChange={setOpenTaggedUsersPopover}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openTaggedUsersPopover}
                            className="w-full justify-between"
                          >
                            {field.value && field.value.length > 0
                              ? `${field.value.length} user${field.value.length > 1 ? 's' : ''} tagged`
                              : "Tag users..."}
                            <Users className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[400px] p-0">
                        <div className="p-2">
                          <Input
                            placeholder="Search users..."
                            value={searchTaggedUsersQuery}
                            onChange={(e) => setSearchTaggedUsersQuery(e.target.value)}
                            className="mb-2"
                          />
                          <ScrollArea className="h-[200px]">
                            {(users || []).length === 0 ? (
                              <div className="p-2 text-sm text-muted-foreground">
                                No users found.
                              </div>
                            ) : (
                              (users || [])
                                .filter(user =>
                                  searchTaggedUsersQuery === "" ||
                                  user.name.toLowerCase().includes(searchTaggedUsersQuery.toLowerCase())
                                )
                                .map((user) => {
                                  const isSelected = field.value.includes(user.id);
                                  return (
                                    <div
                                      key={user.id}
                                      className="flex items-center space-x-2 p-2 hover:bg-muted"
                                    >
                                      <Checkbox
                                        checked={isSelected}
                                        onCheckedChange={(checked) => {
                                          const newValue = [...field.value];
                                          if (checked) {
                                            if (!newValue.includes(user.id)) {
                                              newValue.push(user.id);
                                            }
                                          } else {
                                            const index = newValue.indexOf(user.id);
                                            if (index !== -1) {
                                              newValue.splice(index, 1);
                                            }
                                          }
                                          field.onChange(newValue);
                                        }}
                                        id={`user-${user.id}`}
                                      />
                                      <Label
                                        htmlFor={`user-${user.id}`}
                                        className="cursor-pointer flex-grow"
                                      >
                                        {user.name}
                                      </Label>
                                    </div>
                                  );
                                })
                            )}
                          </ScrollArea>
                        </div>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

                </div>
              </ScrollArea>

              <DialogFooter className="mt-4">
                <Button variant="outline" onClick={handleCancelClick} type="button">
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={(e) => {
                    form.handleSubmit((values) => onSubmit(values))(e);
                  }}>
                  Create Task
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleClose}
        onCancel={handleClose}
      />
    </>
  );
}
