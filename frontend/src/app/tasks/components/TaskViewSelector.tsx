"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  User2,
  Users,
  PenSquare,
  ListFilter,
  CheckCircle2,
  Clock,
  Circle,
  AlertCircle,
  Info
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { TaskStatus } from "@/type/case-management/noteTaskTypes";

interface TaskViewSelectorProps {
  onViewChange: (view: 'my_tasks' | 'tagged' | 'created' | 'all') => void;
  onStatusFilterChange: (statuses: TaskStatus[]) => void;
  onExcludeClosedCasesChange: (exclude: boolean) => void;
  selectedView: 'my_tasks' | 'tagged' | 'created' | 'all';
  selectedStatuses: TaskStatus[];
  excludeClosedCases: boolean;
}

export default function TaskViewSelector({
  onViewChange,
  onStatusFilterChange,
  onExcludeClosedCasesChange,
  selectedView,
  selectedStatuses,
  excludeClosedCases
}: TaskViewSelectorProps) {
  const [isStatusFilterOpen, setIsStatusFilterOpen] = useState(false);

  const handleStatusToggle = (status: TaskStatus) => {
    if (selectedStatuses.includes(status)) {
      onStatusFilterChange(selectedStatuses.filter(s => s !== status));
    } else {
      onStatusFilterChange([...selectedStatuses, status]);
    }
  };

  const getStatusFilterLabel = () => {
    if (selectedStatuses.length === 0) {
      return "All Statuses";
    } else if (selectedStatuses.length === 1) {
      return `Status: ${selectedStatuses[0]}`;
    } else {
      return `${selectedStatuses.length} Statuses`;
    }
  };

  // View type descriptions
  const viewDescriptions = {
    my_tasks: "Tasks where you are assigned or tagged",
    tagged: "Tasks where you are tagged but not assigned",
    created: "Tasks you created",
    all: "All tasks you are involved with (assigned, tagged, or created)"
  };

  return (
    <div className="flex flex-col gap-4 mb-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <Tabs
            defaultValue="my_tasks"
            value={selectedView}
            onValueChange={(value) => onViewChange(value as 'my_tasks' | 'tagged' | 'created' | 'all')}
            className="w-full sm:w-auto"
          >
            <TabsList className="grid grid-cols-4 w-full sm:w-auto">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="my_tasks" className="flex items-center gap-1">
                      <User2 className="h-4 w-4" />
                      <span className="hidden sm:inline">My Tasks</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show tasks where you are assigned or tagged</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="tagged" className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span className="hidden sm:inline">Tagged</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show tasks where you are tagged but not assigned</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="created" className="flex items-center gap-1">
                      <PenSquare className="h-4 w-4" />
                      <span className="hidden sm:inline">Created</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show tasks you created</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="all" className="flex items-center gap-1">
                      <ListFilter className="h-4 w-4" />
                      <span className="hidden sm:inline">All</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show all tasks you are involved with (assigned, tagged, or created)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TabsList>
          </Tabs>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-[300px]">
                <p><strong>My Tasks:</strong> Tasks where you are assigned or tagged</p>
                <p><strong>Tagged:</strong> Tasks where you are tagged but not assigned</p>
                <p><strong>Created:</strong> Tasks you created</p>
                <p><strong>All:</strong> All tasks you are involved with</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <DropdownMenu open={isStatusFilterOpen} onOpenChange={setIsStatusFilterOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                {getStatusFilterLabel()}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={() => onStatusFilterChange([])}
                  className="flex items-center justify-between"
                >
                  <span>All Statuses</span>
                  {selectedStatuses.length === 0 && <CheckCircle2 className="h-4 w-4" />}
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => handleStatusToggle(TaskStatus.PENDING)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-yellow-500" />
                    <span>Pending</span>
                  </div>
                  {selectedStatuses.includes(TaskStatus.PENDING) && <CheckCircle2 className="h-4 w-4" />}
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => handleStatusToggle(TaskStatus.IN_PROGRESS)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <span>In Progress</span>
                  </div>
                  {selectedStatuses.includes(TaskStatus.IN_PROGRESS) && <CheckCircle2 className="h-4 w-4" />}
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => handleStatusToggle(TaskStatus.COMPLETED)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span>Completed</span>
                  </div>
                  {selectedStatuses.includes(TaskStatus.COMPLETED) && <CheckCircle2 className="h-4 w-4" />}
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => handleStatusToggle(TaskStatus.REOPENED)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span>Reopened</span>
                  </div>
                  {selectedStatuses.includes(TaskStatus.REOPENED) && <CheckCircle2 className="h-4 w-4" />}
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant={excludeClosedCases ? "default" : "outline"}
            onClick={() => onExcludeClosedCasesChange(!excludeClosedCases)}
            className="flex items-center gap-2"
          >
            {excludeClosedCases ? "Excluding Closed Cases" : "Include Closed Cases"}
          </Button>
        </div>
      </div>

      {/* Informational alert about the current view */}
      <Alert className="bg-blue-50 border-blue-200 shadow-sm">
        <Info className="h-4 w-4 text-blue-500" />
        <AlertDescription className="text-sm text-blue-700">
          {viewDescriptions[selectedView]}
        </AlertDescription>
      </Alert>
    </div>
  );
}
