"use client";


import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { User2, Users, PenSquare, ListFilter, Info } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { TaskStatus } from "@/type/case-management/noteTaskTypes";

interface TaskViewSelectorProps {
  onViewChange: (view: 'my_tasks' | 'tagged' | 'created' | 'all') => void;
  onStatusFilterChange: (statuses: TaskStatus[]) => void;
  onExcludeClosedCasesChange: (exclude: boolean) => void;
  selectedView: 'my_tasks' | 'tagged' | 'created' | 'all';
  selectedStatuses: TaskStatus[];
  excludeClosedCases: boolean;
}

export default function TaskViewSelectorSimple({
  onViewChange,
  selectedView,
  excludeClosedCases,
  onExcludeClosedCasesChange
}: TaskViewSelectorProps) {
  // View type descriptions
  const viewDescriptions = {
    my_tasks: "Tasks where you are assigned or tagged",
    tagged: "Tasks where you are tagged but not assigned",
    created: "Tasks you created",
    all: "All tasks you are involved with (assigned, tagged, or created)"
  };

  return (
    <div className="flex flex-col gap-4 mb-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <Tabs
            defaultValue="my_tasks"
            value={selectedView}
            onValueChange={(value) => onViewChange(value as 'my_tasks' | 'tagged' | 'created' | 'all')}
            className="w-full sm:w-auto"
          >
            <TabsList className="grid grid-cols-4 w-full sm:w-auto">
              <TabsTrigger value="my_tasks" className="flex items-center gap-1">
                <User2 className="h-4 w-4" />
                <span className="hidden sm:inline">My Tasks</span>
              </TabsTrigger>
              <TabsTrigger value="tagged" className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">Tagged</span>
              </TabsTrigger>
              <TabsTrigger value="created" className="flex items-center gap-1">
                <PenSquare className="h-4 w-4" />
                <span className="hidden sm:inline">Created</span>
              </TabsTrigger>
              <TabsTrigger value="all" className="flex items-center gap-1">
                <ListFilter className="h-4 w-4" />
                <span className="hidden sm:inline">All</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <Button
            variant={excludeClosedCases ? "default" : "outline"}
            onClick={() => onExcludeClosedCasesChange(!excludeClosedCases)}
            className="flex items-center gap-2"
          >
            {excludeClosedCases ? "Excluding Closed Cases" : "Include Closed Cases"}
          </Button>
        </div>
      </div>

      {/* Informational alert about the current view */}
      <Alert className="bg-blue-50 border-blue-200 shadow-sm">
        <Info className="h-4 w-4 text-blue-500" />
        <AlertDescription className="text-sm text-blue-700">
          {viewDescriptions[selectedView]}
        </AlertDescription>
      </Alert>
    </div>
  );
}
