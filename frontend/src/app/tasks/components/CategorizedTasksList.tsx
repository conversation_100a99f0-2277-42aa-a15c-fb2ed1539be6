"use client";
import { useState } from "react";
import { Task, TaskStatus, TaskStatusConfig, TaskPriority } from "@/type/case-management/noteTaskTypes";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useQuickStatusUpdateMutation, useBulkUpdateTasksMutation } from "@/services/case-management/taskEnhancedService";
import { format, isToday, isPast, addDays, isWithinInterval } from "date-fns";
import {
  CheckCircle2,
  Clock,
  AlertCircle,
  Circle,
  Calendar,
  User2,
  ChevronDown,
  ArrowUpCircle,
  ArrowDownCircle,
  Alert<PERSON>riangle,
  CheckSquare,
  Briefcase,
  ExternalLink
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Link from "next/link";

interface CategorizedTasksListProps {
  tasks: {
    overdue: Task[];
    today: Task[];
    this_week: Task[];
    later: Task[];
    no_due_date: Task[];
  };
  summary: {
    total: number;
    pending: number;
    in_progress: number;
    completed: number;
    reopened: number;
  };
}

// Helper function to get status icon
const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case TaskStatus.PENDING:
      return <Circle className="h-4 w-4 text-yellow-500" />;
    case TaskStatus.IN_PROGRESS:
      return <Clock className="h-4 w-4 text-blue-500" />;
    case TaskStatus.COMPLETED:
      return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    case TaskStatus.REOPENED:
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Circle className="h-4 w-4" />;
  }
};

// Helper function to get priority icon and color
const getPriorityBadge = (priority?: TaskPriority) => {
  switch (priority) {
    case TaskPriority.CRITICAL:
      return (
        <Badge
          variant="outline"
          className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200 px-2 py-1 rounded-md"
        >
          <ArrowUpCircle className="h-3 w-3 text-red-500" />
          <span>Critical</span>
        </Badge>
      );
    case TaskPriority.URGENT:
      return (
        <Badge
          variant="outline"
          className="flex items-center gap-1 bg-orange-50 text-orange-700 border-orange-200 px-2 py-1 rounded-md"
        >
          <AlertTriangle className="h-3 w-3 text-orange-500" />
          <span>Urgent</span>
        </Badge>
      );
    case TaskPriority.HIGH:
      return (
        <Badge
          variant="outline"
          className="flex items-center gap-1 bg-amber-50 text-amber-700 border-amber-200 px-2 py-1 rounded-md"
        >
          <ArrowUpCircle className="h-3 w-3 text-amber-500" />
          <span>High</span>
        </Badge>
      );
    case TaskPriority.MEDIUM:
      return (
        <Badge
          variant="outline"
          className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200 px-2 py-1 rounded-md"
        >
          <span>Medium</span>
        </Badge>
      );
    case TaskPriority.LOW:
      return (
        <Badge
          variant="outline"
          className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200 px-2 py-1 rounded-md"
        >
          <ArrowDownCircle className="h-3 w-3 text-green-500" />
          <span>Low</span>
        </Badge>
      );
    default:
      return null;
  }
};

// Helper function to format date with relative indicators
const formatTaskDate = (dateString?: string) => {
  if (!dateString) return null;

  const date = new Date(dateString);
  const formattedDate = format(date, "MMM d, yyyy");

  if (isToday(date)) {
    return <span className="text-orange-500 font-medium">Today - {formattedDate}</span>;
  } else if (isPast(date)) {
    return <span className="text-red-500 font-medium">Overdue - {formattedDate}</span>;
  } else if (isWithinInterval(date, { start: addDays(new Date(), 1), end: addDays(new Date(), 2) })) {
    return <span className="text-yellow-500 font-medium">Soon - {formattedDate}</span>;
  }

  return <span>{formattedDate}</span>;
};

export default function CategorizedTasksList({ tasks, summary }: CategorizedTasksListProps) {
  const [selectedTasks, setSelectedTasks] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState<string>("overdue");

  const quickStatusUpdateMutation = useQuickStatusUpdateMutation();
  const bulkUpdateTasksMutation = useBulkUpdateTasksMutation();

  // Handle task selection
  const handleTaskSelection = (taskId: number) => {
    setSelectedTasks(prev =>
      prev.includes(taskId)
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    );
  };

  // Handle select all tasks in a category
  const handleSelectAllInCategory = (categoryTasks: Task[]) => {
    const categoryTaskIds = categoryTasks.map(task => task.id);

    // If all tasks in the category are already selected, deselect them
    if (categoryTaskIds.every(id => selectedTasks.includes(id))) {
      setSelectedTasks(prev => prev.filter(id => !categoryTaskIds.includes(id)));
    } else {
      // Otherwise, add all tasks in the category to the selection
      setSelectedTasks(prev => [...new Set([...prev, ...categoryTaskIds])]);
    }
  };

  // Handle quick status update
  const handleStatusChange = (task: Task, newStatus: TaskStatus) => {
    quickStatusUpdateMutation.mutate({ taskId: task.id, status: newStatus });
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = (status: TaskStatus) => {
    if (selectedTasks.length === 0) return;

    bulkUpdateTasksMutation.mutate({
      taskIds: selectedTasks,
      updates: { status }
    });
  };

  // Get current category tasks
  const currentCategoryTasks = tasks[activeTab as keyof typeof tasks] || [];

  // Check if all tasks in the current category are selected
  const areAllCurrentCategoryTasksSelected =
    currentCategoryTasks.length > 0 &&
    currentCategoryTasks.every(task => selectedTasks.includes(task.id));

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium text-yellow-500">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium text-blue-500">In Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.in_progress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium text-green-500">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.completed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium text-red-500">Reopened</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.reopened}</div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {selectedTasks.length > 0 && (
        <div className="bg-muted p-4 rounded-lg flex items-center justify-between">
          <div>
            <span className="font-medium">{selectedTasks.length} tasks selected</span>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkStatusUpdate(TaskStatus.IN_PROGRESS)}
            >
              <Clock className="h-4 w-4 mr-2" />
              Mark In Progress
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkStatusUpdate(TaskStatus.COMPLETED)}
            >
              <CheckSquare className="h-4 w-4 mr-2" />
              Mark Complete
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedTasks([])}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* Tabs for Task Categories */}
      <Tabs defaultValue="overdue" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 mb-4">
          <TabsTrigger value="overdue" className="relative text-red-500">
            Overdue
          </TabsTrigger>
          <TabsTrigger value="today" className="text-orange-500">
            Today
          </TabsTrigger>
          <TabsTrigger value="this_week" className="text-blue-500">
            This Week
          </TabsTrigger>
          <TabsTrigger value="later">
            Later
          </TabsTrigger>
          <TabsTrigger value="no_due_date">
            No Due Date
          </TabsTrigger>
        </TabsList>

        {/* Task Lists for Each Category */}
        {Object.entries(tasks).map(([category, categoryTasks]) => (
          <TabsContent key={category} value={category} className="mt-0">
            {categoryTasks.length === 0 ? (
              <div className="text-center py-8 bg-muted/30 rounded-lg">
                <p className="text-muted-foreground">No tasks in this category</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={areAllCurrentCategoryTasksSelected}
                      onCheckedChange={() => handleSelectAllInCategory(categoryTasks)}
                    />
                    <span className="text-sm font-medium">
                      Select All ({categoryTasks.length})
                    </span>
                  </div>
                </div>

                <ScrollArea className="h-[calc(100vh-350px)]">
                  <div className="space-y-4 pr-4">
                    {categoryTasks.map((task) => (
                      <Card
                        key={task.id}
                        className={`transition-all duration-200 shadow-sm hover:shadow-md ${
                          task.status === TaskStatus.COMPLETED
                            ? "bg-muted/30 border-muted"
                            : "bg-background hover:bg-slate-50 border-border"
                        } ${task.case ? 'cursor-pointer' : ''} relative overflow-hidden group`}
                        onClick={(e) => {
                          // Only navigate if clicking on the card itself, not on interactive elements
                          const target = e.target as HTMLElement;
                          if (
                            target.closest('button') ||
                            target.closest('a') ||
                            target.closest('input') ||
                            target.closest('[role="dialog"]') ||
                            target.closest('[data-state="open"]') ||
                            target.closest('[role="menuitem"]')
                          ) {
                            return; // Don't navigate if clicking on interactive elements
                          }

                          // Navigate to case view if task has a case
                          if (task.case) {
                            window.location.href = `/dashboard/case-view/${task.case}`;
                          }
                        }}
                      >
                        {/* Status indicator bar */}
                        <div className={`absolute left-0 top-0 bottom-0 w-1 ${
                          task.status === TaskStatus.PENDING ? 'bg-yellow-500' :
                          task.status === TaskStatus.IN_PROGRESS ? 'bg-blue-500' :
                          task.status === TaskStatus.COMPLETED ? 'bg-green-500' :
                          'bg-red-500'
                        }`} />

                        <CardContent className="p-4 pl-6">
                          <div className="flex items-start gap-3">
                            <Checkbox
                              checked={selectedTasks.includes(task.id)}
                              onCheckedChange={() => handleTaskSelection(task.id)}
                              className="mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className={`text-base font-medium ${
                                    task.status === TaskStatus.COMPLETED
                                      ? "line-through text-muted-foreground"
                                      : ""
                                  }`}>
                                    {task.title}
                                  </h3>

                                  {/* Case Badge - Only show if task has case or case_name */}
                                  {(task.case || task.case_name) && (
                                    <div className="flex items-center gap-1 mt-1">
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Badge
                                              variant="outline"
                                              className="flex items-center gap-1 bg-blue-50 hover:bg-blue-100 transition-colors border-blue-200 text-blue-700 px-2 py-1 rounded-md"
                                            >
                                              <Briefcase className="h-3 w-3 text-blue-500" />
                                              <Link
                                                href={`/dashboard/case-view/${task.case}`}
                                                className="text-xs text-blue-600 hover:underline flex items-center"
                                                onClick={(e) => e.stopPropagation()}
                                              >
                                                {task.case_details?.name || task.case_details?.case_name || task.case_name || `Case ${task.case}`}
                                                <ExternalLink className="h-3 w-3 ml-1" />
                                              </Link>
                                            </Badge>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Click to view case details</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  )}
                                </div>
                                {getPriorityBadge(task.priority)}
                              </div>

                              {task.description && (
                                <div className="mt-2 text-sm text-muted-foreground line-clamp-2 bg-slate-50 p-2 rounded-md border border-slate-100">
                                  {task.description}
                                </div>
                              )}

                              <div className="flex flex-wrap gap-2 mt-3">
                                {/* Status Badge */}
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Badge
                                            variant="outline"
                                            className="flex items-center gap-1 cursor-pointer hover:bg-muted px-2 py-1 h-7 transition-all"
                                          >
                                            {getStatusIcon(task.status)}
                                            <span>{TaskStatusConfig[task.status]?.label || task.status}</span>
                                            <ChevronDown className="h-3 w-3 ml-1" />
                                          </Badge>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent>
                                          {Object.entries(TaskStatusConfig).map(([status, config]) => (
                                            <DropdownMenuItem
                                              key={status}
                                              onClick={() => handleStatusChange(task, status as TaskStatus)}
                                              className="flex items-center gap-2"
                                            >
                                              {getStatusIcon(status as TaskStatus)}
                                              <span>{config.label}</span>
                                            </DropdownMenuItem>
                                          ))}
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Change status</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>

                                {/* Due Date */}
                                {task.due_date && (
                                  <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 h-7 bg-slate-50 border-slate-200">
                                    <Calendar className="h-3 w-3 text-slate-500" />
                                    {formatTaskDate(task.due_date)}
                                  </Badge>
                                )}

                                {/* Assigned To */}
                                {task.assigned_to && (
                                  <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 h-7 bg-slate-50 border-slate-200">
                                    <User2 className="h-3 w-3 text-slate-500" />
                                    {task.assigned_to.name || task.assigned_to.email}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
