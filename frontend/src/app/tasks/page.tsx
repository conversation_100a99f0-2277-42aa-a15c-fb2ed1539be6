"use client";
import { useState, useEffect } from "react";
import { Task, TaskStatus } from "@/type/case-management/noteTaskTypes";
import { CreateTaskDialog } from "./components/CreateTaskDialog";
import { TasksList } from "./components/TasksList";
import CategorizedTasksList from "./components/CategorizedTasksList";
import TaskViewSelector from "./components/TaskViewSelector";
import TaskViewSelectorSimple from "./components/TaskViewSelector.simple";
import { useFilteredTasksQuery } from "@/services/case-management/noteTaskService";
import { useCategorizedTasksQuery } from "@/services/case-management/taskEnhancedService";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { EmptyState } from "@/components/ui/empty-state";
import { NotebookPen } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function TasksPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  // When no case_id is provided, for_dashboard=true ensures we only get tasks
  // assigned to or tagged with the current user
  const [filters, setFilters] = useState({
    for_dashboard: true,
    page: 1,
    page_size: pageSize
  });

  // Update filters when page or pageSize changes
  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      page: currentPage,
      page_size: pageSize
    }));
  }, [currentPage, pageSize]);

  // Listen for task updates and refetch data
  useEffect(() => {
    const handleTaskUpdate = () => {
      // Force a refetch of the data
      setFilters(prev => ({ ...prev }));
    };

    window.addEventListener('task-updated', handleTaskUpdate);

    return () => {
      window.removeEventListener('task-updated', handleTaskUpdate);
    };
  }, []);

  const { data, isLoading } = useFilteredTasksQuery(filters);

  const tasks = data?.pages.flatMap(page => page.results) || [];
  const totalCount = data?.pages[0]?.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Group tasks by case and include case details
  const groupedTasks = tasks.reduce((acc: Record<string, { tasks: Task[], caseName: string, caseId: string, caseStatus: string }>, task: Task) => {
    if (!acc[task.case]) {
      // Get the case name from case_details, with better fallbacks
      const caseName =
        task.case_details?.case_name ||
        task.case_details?.name ||
        task.case_details?.client_name ||
        task.case_details?.name_of_client ||
        `Case ${task.case}`;

      // Get case status
      const caseStatus = task.case_details?.status || 'Unknown';

      acc[task.case] = {
        tasks: [],
        caseName: caseName,
        caseId: task.case,
        caseStatus: caseStatus
      };
    }
    acc[task.case].tasks.push(task);
    return acc;
  }, {});

  // Define handler functions
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (value: string) => {
    setPageSize(Number(value));
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const handleViewChange = (view: 'my_tasks' | 'tagged' | 'created' | 'all') => {
    setSelectedView(view);
  };

  const handleStatusFilterChange = (statuses: TaskStatus[]) => {
    setSelectedStatuses(statuses);
  };

  const handleExcludeClosedCasesChange = (exclude: boolean) => {
    setExcludeClosedCases(exclude);
  };

  // Add state for view mode
  const [viewMode, setViewMode] = useState<'enhanced' | 'legacy'>('enhanced');

  // Add state for enhanced view filters
  const [selectedView, setSelectedView] = useState<'my_tasks' | 'tagged' | 'created' | 'all'>('my_tasks');
  const [selectedStatuses, setSelectedStatuses] = useState<TaskStatus[]>([]);
  const [excludeClosedCases, setExcludeClosedCases] = useState(true);

  // Prepare filters for enhanced view
  const enhancedFilters: {
    exclude_closed_cases: boolean;
    status?: string;
  } = {
    exclude_closed_cases: excludeClosedCases
  };

  // Add status filter if any statuses are selected
  if (selectedStatuses.length > 0) {
    enhancedFilters.status = selectedStatuses.join(',');
  }

  // Fetch data for enhanced view
  const {
    data: enhancedData,
    isLoading: isEnhancedLoading,
    error: enhancedError
  } = useCategorizedTasksQuery(selectedView, enhancedFilters);

  return (
    <div className="p-6 bg-slate-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <NotebookPen className="h-5 w-5" />
          <h1 className="text-xl font-semibold">Tasks {enhancedData?.summary?.total ? `(${enhancedData.summary.total})` : ''}</h1>
        </div>
        <div className="flex gap-2">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'enhanced' | 'legacy')}>
            <TabsList>
              <TabsTrigger value="enhanced">Enhanced View</TabsTrigger>
              <TabsTrigger value="legacy">Legacy View</TabsTrigger>
            </TabsList>
          </Tabs>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            Create Task
          </Button>
        </div>
      </div>

      {viewMode === 'enhanced' ? (
        <>
          {/* Try to use the full component first, fall back to simple version if it fails */}
          {(() => {
            try {
              return (
                <TaskViewSelector
                  onViewChange={handleViewChange}
                  onStatusFilterChange={handleStatusFilterChange}
                  onExcludeClosedCasesChange={handleExcludeClosedCasesChange}
                  selectedView={selectedView}
                  selectedStatuses={selectedStatuses}
                  excludeClosedCases={excludeClosedCases}
                />
              );
            } catch (error) {
              console.error("Error rendering TaskViewSelector:", error);
              return (
                <TaskViewSelectorSimple
                  onViewChange={handleViewChange}
                  onStatusFilterChange={handleStatusFilterChange}
                  onExcludeClosedCasesChange={handleExcludeClosedCasesChange}
                  selectedView={selectedView}
                  selectedStatuses={selectedStatuses}
                  excludeClosedCases={excludeClosedCases}
                />
              );
            }
          })()}

          {isEnhancedLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          ) : enhancedError ? (
            <div className="text-center py-8">
              <EmptyState
                icon={NotebookPen}
                title="Error loading tasks"
                description={`There was an error loading tasks. Please try again later. ${enhancedError instanceof Error ? enhancedError.message : ''}`}
              />
            </div>
          ) : !enhancedData ||
              (!enhancedData.overdue?.length &&
               !enhancedData.today?.length &&
               !enhancedData.this_week?.length &&
               !enhancedData.later?.length &&
               !enhancedData.no_due_date?.length) ? (
            <EmptyState
              icon={NotebookPen}
              title="No tasks found"
              description="Create a new task to get started."
            />
          ) : (
            <CategorizedTasksList
              tasks={{
                overdue: enhancedData.overdue || [],
                today: enhancedData.today || [],
                this_week: enhancedData.this_week || [],
                later: enhancedData.later || [],
                no_due_date: enhancedData.no_due_date || []
              }}
              summary={enhancedData.summary || {
                total: 0,
                pending: 0,
                in_progress: 0,
                completed: 0,
                reopened: 0
              }}
            />
          )}
        </>
      ) : (
        <>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          ) : tasks.length === 0 ? (
            <EmptyState
              icon={NotebookPen}
              title="No tasks found"
              description="Create a new task to get started."
            />
          ) : (
            Object.entries(groupedTasks).map(([caseId, caseData]) => (
              <div
                key={caseId}
                className="mb-6 rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden"
              >
                <div className="flex items-center justify-between p-4 bg-muted/30 border-b">
                  <div className="flex flex-col">
                    <Link
                      href={`/dashboard/case-view/${caseId}`}
                      className="text-lg font-semibold hover:underline text-primary flex items-center gap-2"
                    >
                      <span>Case {caseId}</span>
                      <Badge variant="outline" className={`text-xs ${caseData.caseStatus.toLowerCase() === 'closed' ? 'bg-gray-100 text-gray-600' : 'bg-green-100 text-green-600'}`}>
                        {caseData.caseStatus}
                      </Badge>
                    </Link>
                    {caseData.caseName !== `Case ${caseId}` && (
                      <span className="text-sm text-muted-foreground font-normal mt-1">{caseData.caseName}</span>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {caseData.tasks.length} {caseData.tasks.length === 1 ? 'task' : 'tasks'}
                  </div>
                </div>
                <div className="p-4">
                  <TasksList tasks={caseData.tasks} />
                </div>
              </div>
            ))
          )}

          {/* Pagination Controls */}
          {!isLoading && totalCount > 0 && (
            <div className="flex items-center justify-between py-4 border-t mt-6">
              <div className="flex-1 max-w-[400px]">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600 whitespace-nowrap">Items per page:</span>
                  <Select
                    value={pageSize.toString()}
                    onValueChange={handlePageSizeChange}
                  >
                    <SelectTrigger className="w-[70px]">
                      <SelectValue placeholder={pageSize} />
                    </SelectTrigger>
                    <SelectContent>
                      {[10, 25, 50, 100].map((size) => (
                        <SelectItem key={size} value={size.toString()}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600 whitespace-nowrap">
                    {`${((currentPage - 1) * pageSize) + 1}-${Math.min(currentPage * pageSize, totalCount)} of ${totalCount}`}
                  </span>
                </div>
              </div>

              <div className="flex-1 max-w-[400px]">
                <Pagination>
                  <PaginationContent className="flex justify-end">
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>

                    {[...Array(totalPages)].map((_, index) => {
                      const page = index + 1;
                      if (
                        page === 1 ||
                        page === totalPages ||
                        (page >= currentPage - 1 && page <= currentPage + 1)
                      ) {
                        return (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => handlePageChange(page)}
                              isActive={page === currentPage}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      } else if (
                        page === currentPage - 2 ||
                        page === currentPage + 2
                      ) {
                        return (
                          <PaginationItem key={page}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        );
                      }
                      return null;
                    })}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </>
      )}

      <CreateTaskDialog
        open={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
      />
    </div>
  );
}
