import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    useSourceDetails,
    useCreateSourceDetail,
    useUpdateSourceDetail,
    useDeleteSourceDetail,
    useSourceTags,
    useCreateSourceTag,
    useUpdateSourceTag,
    useDeleteSourceTag,
} from '@/services/organizationService';
import { toast } from '@/hooks/use-toast';
import { SourceDetail, SourceTag } from '@/services/organizationService';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

export function IntakeSourceCustomization() {
    // State for source details
    const [isAddSourceDetailOpen, setIsAddSourceDetailOpen] = useState(false);
    const [editingSourceDetail, setEditingSourceDetail] = useState<SourceDetail | null>(null);
    const [newSourceDetailName, setNewSourceDetailName] = useState('');
    const [newSourceDetailDescription, setNewSourceDetailDescription] = useState('');
    const [showSourceDetailAlert, setShowSourceDetailAlert] = useState(false);

    // State for source tags
    const [isAddSourceTagOpen, setIsAddSourceTagOpen] = useState(false);
    const [editingSourceTag, setEditingSourceTag] = useState<SourceTag | null>(null);
    const [newSourceTagName, setNewSourceTagName] = useState('');
    const [newSourceTagDescription, setNewSourceTagDescription] = useState('');
    const [showSourceTagAlert, setShowSourceTagAlert] = useState(false);

    // Fetch data
    const { data: sourceDetails, isLoading: isLoadingDetails } = useSourceDetails();
    const { data: sourceTags, isLoading: isLoadingTags } = useSourceTags();

    // Mutations
    const { mutate: createSourceDetail } = useCreateSourceDetail();
    const { mutate: updateSourceDetail } = useUpdateSourceDetail();
    const { mutate: deleteSourceDetail } = useDeleteSourceDetail();
    const { mutate: createSourceTag } = useCreateSourceTag();
    const { mutate: updateSourceTag } = useUpdateSourceTag();
    const { mutate: deleteSourceTag } = useDeleteSourceTag();

    // Source Detail handlers
    const handleSourceDetailOpenChange = (newOpen: boolean) => {
        const isDirty = newSourceDetailName !== '' || newSourceDetailDescription !== '';
        if (!newOpen && isDirty) {
            setShowSourceDetailAlert(true);
        } else {
            if (!newOpen) {
                handleSourceDetailClose();
            } else {
                setIsAddSourceDetailOpen(true);
            }
        }
    };

    const handleSourceDetailClose = () => {
        setIsAddSourceDetailOpen(false);
        setNewSourceDetailName('');
        setNewSourceDetailDescription('');
    };

    const handleSourceDetailDiscardChanges = () => {
        setShowSourceDetailAlert(false);
        handleSourceDetailClose();
    };

    const handleSourceDetailContinueEditing = () => {
        setShowSourceDetailAlert(false);
    };

    const handleCreateSourceDetail = () => {
        if (!newSourceDetailName.trim()) {
            toast({
                title: "Error",
                description: "Source name is required",
                variant: "destructive",
            });
            return;
        }

        createSourceDetail({
            name: newSourceDetailName.trim(),
            description: newSourceDetailDescription.trim() || undefined,
        }, {
            onSuccess: () => {
                setIsAddSourceDetailOpen(false);
                setNewSourceDetailName('');
                setNewSourceDetailDescription('');
            }
        });
    };

    const handleUpdateSourceDetail = () => {
        if (!editingSourceDetail || !newSourceDetailName.trim()) {
            toast({
                title: "Error",
                description: "Source name is required",
                variant: "destructive",
            });
            return;
        }

        updateSourceDetail({
            id: editingSourceDetail.id,
            data: {
                name: newSourceDetailName.trim(),
                description: newSourceDetailDescription.trim() || undefined,
            }
        }, {
            onSuccess: () => {
                setEditingSourceDetail(null);
                setNewSourceDetailName('');
                setNewSourceDetailDescription('');
            }
        });
    };

    // Source Tag handlers
    const handleSourceTagOpenChange = (newOpen: boolean) => {
        const isDirty = newSourceTagName !== '' || newSourceTagDescription !== '';
        if (!newOpen && isDirty) {
            setShowSourceTagAlert(true);
        } else {
            if (!newOpen) {
                handleSourceTagClose();
            } else {
                setIsAddSourceTagOpen(true);
            }
        }
    };

    const handleSourceTagClose = () => {
        setIsAddSourceTagOpen(false);
        setNewSourceTagName('');
        setNewSourceTagDescription('');
    };

    const handleSourceTagDiscardChanges = () => {
        setShowSourceTagAlert(false);
        handleSourceTagClose();
    };

    const handleSourceTagContinueEditing = () => {
        setShowSourceTagAlert(false);
    };

    const handleCreateSourceTag = () => {
        if (!newSourceTagName.trim()) {
            toast({
                title: "Error",
                description: "Tag name is required",
                variant: "destructive",
            });
            return;
        }

        createSourceTag({
            name: newSourceTagName.trim(),
            description: newSourceTagDescription.trim() || undefined,
        }, {
            onSuccess: () => {
                setIsAddSourceTagOpen(false);
                setNewSourceTagName('');
                setNewSourceTagDescription('');
            }
        });
    };

    const handleUpdateSourceTag = () => {
        if (!editingSourceTag || !newSourceTagName.trim()) {
            toast({
                title: "Error",
                description: "Tag name is required",
                variant: "destructive",
            });
            return;
        }

        updateSourceTag({
            id: editingSourceTag.id,
            data: {
                name: newSourceTagName.trim(),
                description: newSourceTagDescription.trim() || undefined,
            }
        }, {
            onSuccess: () => {
                setEditingSourceTag(null);
                setNewSourceTagName('');
                setNewSourceTagDescription('');
            }
        });
    };

    if (isLoadingDetails || isLoadingTags) {
        return <div>Loading...</div>;
    }

    return (
        <>
            <Tabs defaultValue="sources" className="space-y-4">
                <TabsList>
                    <TabsTrigger value="sources">Source Details</TabsTrigger>
                    <TabsTrigger value="tags">Source Tags</TabsTrigger>
                </TabsList>

                <TabsContent value="sources">
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <h3 className="text-lg font-medium">Intake Sources</h3>
                            <Dialog open={isAddSourceDetailOpen} onOpenChange={handleSourceDetailOpenChange}>
                                <DialogTrigger asChild>
                                    <Button>Add New Source Details</Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Create New Source</DialogTitle>
                                    </DialogHeader>
                                    <div className="space-y-4 py-4">
                                        <div className="space-y-2">
                                            <label>Name</label>
                                            <Input
                                                value={newSourceDetailName}
                                                onChange={(e) => setNewSourceDetailName(e.target.value)}
                                                placeholder="Enter source name"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <label>Description</label>
                                            <Textarea
                                                value={newSourceDetailDescription}
                                                onChange={(e) => setNewSourceDetailDescription(e.target.value)}
                                                placeholder="Enter source description (optional)"
                                            />
                                        </div>
                                        <Button onClick={handleCreateSourceDetail} className="w-full">
                                            Create Source
                                        </Button>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>

                        <Card>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {sourceDetails?.map((source) => (
                                        <TableRow key={source.id}>
                                            <TableCell>{source.name}</TableCell>
                                            <TableCell>{source.description || "—"}</TableCell>
                                            <TableCell className="text-right space-x-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => {
                                                        setEditingSourceDetail(source);
                                                        setNewSourceDetailName(source.name);
                                                        setNewSourceDetailDescription(source.description || '');
                                                    }}
                                                >
                                                    Edit
                                                </Button>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => deleteSourceDetail(source.id)}
                                                >
                                                    Delete
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="tags">
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <h3 className="text-lg font-medium">Source Tags</h3>
                            <Dialog open={isAddSourceTagOpen} onOpenChange={handleSourceTagOpenChange}>
                                <DialogTrigger asChild>
                                    <Button>Add New Source Tag</Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Create New Tag</DialogTitle>
                                    </DialogHeader>
                                    <div className="space-y-4 py-4">
                                        <div className="space-y-2">
                                            <label>Name</label>
                                            <Input
                                                value={newSourceTagName}
                                                onChange={(e) => setNewSourceTagName(e.target.value)}
                                                placeholder="Enter tag name"
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <label>Description</label>
                                            <Textarea
                                                value={newSourceTagDescription}
                                                onChange={(e) => setNewSourceTagDescription(e.target.value)}
                                                placeholder="Enter tag description (optional)"
                                            />
                                        </div>
                                        <Button onClick={handleCreateSourceTag} className="w-full">
                                            Create Tag
                                        </Button>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>

                        <Card>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {sourceTags?.map((tag) => (
                                        <TableRow key={tag.id}>
                                            <TableCell>{tag.name}</TableCell>
                                            <TableCell>{tag.description || "—"}</TableCell>
                                            <TableCell className="text-right space-x-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => {
                                                        setEditingSourceTag(tag);
                                                        setNewSourceTagName(tag.name);
                                                        setNewSourceTagDescription(tag.description || '');
                                                    }}
                                                >
                                                    Edit
                                                </Button>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => deleteSourceTag(tag.id)}
                                                >
                                                    Delete
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>

            {/* Edit Source Dialog */}
            <Dialog open={!!editingSourceDetail} onOpenChange={(open) => !open && setEditingSourceDetail(null)}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Source</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <label>Name</label>
                            <Input
                                value={newSourceDetailName}
                                onChange={(e) => setNewSourceDetailName(e.target.value)}
                                placeholder="Enter source name"
                            />
                        </div>
                        <div className="space-y-2">
                            <label>Description</label>
                            <Textarea
                                value={newSourceDetailDescription}
                                onChange={(e) => setNewSourceDetailDescription(e.target.value)}
                                placeholder="Enter source description (optional)"
                            />
                        </div>
                        <Button onClick={handleUpdateSourceDetail} className="w-full">
                            Update Source
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Edit Tag Dialog */}
            <Dialog open={!!editingSourceTag} onOpenChange={(open) => !open && setEditingSourceTag(null)}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Tag</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <label>Name</label>
                            <Input
                                value={newSourceTagName}
                                onChange={(e) => setNewSourceTagName(e.target.value)}
                                placeholder="Enter tag name"
                            />
                        </div>
                        <div className="space-y-2">
                            <label>Description</label>
                            <Textarea
                                value={newSourceTagDescription}
                                onChange={(e) => setNewSourceTagDescription(e.target.value)}
                                placeholder="Enter tag description (optional)"
                            />
                        </div>
                        <Button onClick={handleUpdateSourceTag} className="w-full">
                            Update Tag
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showSourceDetailAlert}
                onOpenChange={setShowSourceDetailAlert}
                onConfirm={handleSourceDetailDiscardChanges}
                onCancel={handleSourceDetailContinueEditing}
            />

            <UnsavedChangesAlert
                open={showSourceTagAlert}
                onOpenChange={setShowSourceTagAlert}
                onConfirm={handleSourceTagDiscardChanges}
                onCancel={handleSourceTagContinueEditing}
            />
        </>
    );
}
