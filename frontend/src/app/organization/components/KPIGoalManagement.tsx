'use client';

import { useState } from 'react';
import { useBulkCreateOrganizationKPIGoals, useDeleteOrganizationKPIGoal, useUpdateOrganizationKPIGoal, useCreateOrganizationKPIGoal, useOrganizationKPIGoals } from '@/services/orgAPIs';
import { KPIGoal, KPIGoalCreateRequest, KPIInterval, KPIMetricType } from '@/type/case-management/orgTypes';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, Plus, Pencil, Trash2 } from "lucide-react";
import { KPIType } from '@/services/organizationService';

// Helper function to format KPI type display names
const getKPIDisplayName = (kpiType: string): string => {
    return kpiType
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

export function KPIGoalManagement() {
    const { data: kpiGoals, isLoading } = useOrganizationKPIGoals();
    const { mutate: createGoal } = useCreateOrganizationKPIGoal();
    const { mutate: updateGoal } = useUpdateOrganizationKPIGoal();
    const { mutate: deleteGoal } = useDeleteOrganizationKPIGoal();
    const { mutate: bulkCreateGoals } = useBulkCreateOrganizationKPIGoals();

    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isBulkCreateDialogOpen, setIsBulkCreateDialogOpen] = useState(false);
    const [editingGoal, setEditingGoal] = useState<KPIGoal | null>(null);
    const [newGoal, setNewGoal] = useState<KPIGoalCreateRequest>({
        kpi_type: '',
        interval: KPIInterval.MONTHLY,
        target_value: 0,
        metric_type: KPIMetricType.COUNT,
        is_active: true
    });
    const [bulkGoals, setBulkGoals] = useState<KPIGoalCreateRequest[]>([{
        kpi_type: '',
        interval: KPIInterval.MONTHLY,
        target_value: 0,
        metric_type: KPIMetricType.COUNT,
        is_active: true
    }]);

    const handleCreate = async () => {
        try {
            await createGoal(newGoal);
            setIsCreateDialogOpen(false);
            setNewGoal({
                kpi_type: '',
                interval: KPIInterval.MONTHLY,
                target_value: 0,
                metric_type: KPIMetricType.COUNT,
                is_active: true
            });
        } catch (error) {
            console.error('Failed to create KPI goal:', error);
        }
    };

    const handleUpdate = async (id: string, data: Partial<KPIGoalCreateRequest>) => {
        try {
            await updateGoal({ id: parseInt(id), data });
            setEditingGoal(null);
        } catch (error) {
            console.error('Failed to update KPI goal:', error);
        }
    };

    const handleDelete = async (id: string) => {
        try {
            await deleteGoal(id);
        } catch (error) {
            console.error('Failed to delete KPI goal:', error);
        }
    };

    const handleBulkCreate = async () => {
        try {
            await bulkCreateGoals({ goals: bulkGoals });
            setIsBulkCreateDialogOpen(false);
            setBulkGoals([{
                kpi_type: '',
                interval: KPIInterval.MONTHLY,
                target_value: 0,
                metric_type: KPIMetricType.COUNT,
                is_active: true
            }]);
        } catch (error) {
            console.error('Failed to bulk create KPI goals:', error);
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">KPI Goals</h2>
                <div className="space-x-2">
                    <Dialog open={isBulkCreateDialogOpen} onOpenChange={setIsBulkCreateDialogOpen}>
                        <DialogTrigger asChild>
                            <Button variant="outline">
                                <Plus className="h-4 w-4 mr-2" />
                                Bulk Create
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Bulk Create KPI Goals</DialogTitle>
                            </DialogHeader>
                            <ScrollArea className="h-[400px]">
                                <div className="space-y-4">
                                    {bulkGoals.map((goal, index) => (
                                        <div key={index} className="space-y-4 p-4 border rounded-lg">
                                            <div className="flex justify-between items-center">
                                                <h3 className="font-medium">Goal {index + 1}</h3>
                                                {index > 0 && (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => {
                                                            const newGoals = [...bulkGoals];
                                                            newGoals.splice(index, 1);
                                                            setBulkGoals(newGoals);
                                                        }}
                                                    >
                                                        Remove
                                                    </Button>
                                                )}
                                            </div>
                                            <div className="space-y-2">
                                                <Label>KPI Type</Label>
                                                <Select
                                                    value={goal.kpi_type}
                                                    onValueChange={(value) => {
                                                        const newGoals = [...bulkGoals];
                                                        newGoals[index] = { ...goal, kpi_type: value };
                                                        setBulkGoals(newGoals);
                                                    }}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select KPI type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.values(KPIType).map((type) => (
                                                            <SelectItem key={type} value={type}>
                                                                {getKPIDisplayName(type)}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div className="space-y-2">
                                                <Label>Interval</Label>
                                                <Select
                                                    value={goal.interval}
                                                    onValueChange={(value) => {
                                                        const newGoals = [...bulkGoals];
                                                        newGoals[index] = { ...goal, interval: value as KPIInterval };
                                                        setBulkGoals(newGoals);
                                                    }}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.values(KPIInterval).map((interval) => (
                                                            <SelectItem key={interval} value={interval}>
                                                                {interval}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div className="space-y-2">
                                                <Label>Target Value</Label>
                                                <Input
                                                    type="number"
                                                    value={goal.target_value}
                                                    onChange={(e) => {
                                                        const newGoals = [...bulkGoals];
                                                        newGoals[index] = { ...goal, target_value: Number(e.target.value) };
                                                        setBulkGoals(newGoals);
                                                    }}
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label>Metric Type</Label>
                                                <Select
                                                    value={goal.metric_type}
                                                    onValueChange={(value) => {
                                                        const newGoals = [...bulkGoals];
                                                        newGoals[index] = { ...goal, metric_type: value as KPIMetricType };
                                                        setBulkGoals(newGoals);
                                                    }}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.values(KPIMetricType).map((type) => (
                                                            <SelectItem key={type} value={type}>
                                                                {type}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    checked={goal.is_active}
                                                    onCheckedChange={(checked) => {
                                                        const newGoals = [...bulkGoals];
                                                        newGoals[index] = { ...goal, is_active: checked };
                                                        setBulkGoals(newGoals);
                                                    }}
                                                />
                                                <Label>Active</Label>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>
                            <div className="flex justify-between">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setBulkGoals([...bulkGoals, {
                                            kpi_type: '',
                                            interval: KPIInterval.MONTHLY,
                                            target_value: 0,
                                            metric_type: KPIMetricType.COUNT,
                                            is_active: true
                                        }]);
                                    }}
                                >
                                    Add Another Goal
                                </Button>
                                <Button onClick={handleBulkCreate}>
                                    Create Goals
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                    <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Create Goal
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Create KPI Goal</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label>KPI Type</Label>
                                    <Select
                                        value={newGoal.kpi_type}
                                        onValueChange={(value) => setNewGoal({ ...newGoal, kpi_type: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select KPI type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.values(KPIType).map((type) => (
                                                <SelectItem key={type} value={type}>
                                                    {getKPIDisplayName(type)}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label>Interval</Label>
                                    <Select
                                        value={newGoal.interval}
                                        onValueChange={(value) => setNewGoal({ ...newGoal, interval: value as KPIInterval })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.values(KPIInterval).map((interval) => (
                                                <SelectItem key={interval} value={interval}>
                                                    {interval}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label>Target Value</Label>
                                    <Input
                                        type="number"
                                        value={newGoal.target_value}
                                        onChange={(e) => setNewGoal({ ...newGoal, target_value: Number(e.target.value) })}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label>Metric Type</Label>
                                    <Select
                                        value={newGoal.metric_type}
                                        onValueChange={(value) => setNewGoal({ ...newGoal, metric_type: value as KPIMetricType })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.values(KPIMetricType).map((type) => (
                                                <SelectItem key={type} value={type}>
                                                    {type}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={newGoal.is_active}
                                        onCheckedChange={(checked) => setNewGoal({ ...newGoal, is_active: checked })}
                                    />
                                    <Label>Active</Label>
                                </div>
                                <Button onClick={handleCreate}>
                                    Create Goal
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>KPI Goals List</CardTitle>
                    <CardDescription>Manage your organization&apos;s KPI goals</CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>KPI Type</TableHead>
                                <TableHead>Interval</TableHead>
                                <TableHead>Target Value</TableHead>
                                <TableHead>Metric Type</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {kpiGoals?.map((goal) => (
                                <TableRow key={goal.id}>
                                    <TableCell>{getKPIDisplayName(goal.kpi_type)}</TableCell>
                                    <TableCell>{goal.interval}</TableCell>
                                    <TableCell>{goal.target_value}</TableCell>
                                    <TableCell>{goal.metric_type}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded-full text-xs ${
                                            goal.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                        }`}>
                                            {goal.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex space-x-2">
                                            <Dialog open={editingGoal?.id === goal.id} onOpenChange={(open) => !open && setEditingGoal(null)}>
                                                <DialogTrigger asChild>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => setEditingGoal(goal)}
                                                    >
                                                        <Pencil className="h-4 w-4" />
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>Edit KPI Goal</DialogTitle>
                                                    </DialogHeader>
                                                    <div className="space-y-4">
                                                        <div className="space-y-2">
                                                            <Label>KPI Type</Label>
                                                            <Select
                                                                value={editingGoal?.kpi_type || ''}
                                                                onValueChange={(value) => setEditingGoal({ ...editingGoal!, kpi_type: value })}
                                                            >
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder="Select KPI type" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {Object.values(KPIType).map((type) => (
                                                                        <SelectItem key={type} value={type}>
                                                                            {getKPIDisplayName(type)}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        </div>
                                                        <div className="space-y-2">
                                                            <Label>Interval</Label>
                                                            <Select
                                                                value={editingGoal?.interval}
                                                                onValueChange={(value) => setEditingGoal({ ...editingGoal!, interval: value as KPIInterval })}
                                                            >
                                                                <SelectTrigger>
                                                                    <SelectValue />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {Object.values(KPIInterval).map((interval) => (
                                                                        <SelectItem key={interval} value={interval}>
                                                                            {interval}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        </div>
                                                        <div className="space-y-2">
                                                            <Label>Target Value</Label>
                                                            <Input
                                                                type="number"
                                                                value={editingGoal?.target_value || 0}
                                                                onChange={(e) => setEditingGoal({ ...editingGoal!, target_value: Number(e.target.value) })}
                                                            />
                                                        </div>
                                                        <div className="space-y-2">
                                                            <Label>Metric Type</Label>
                                                            <Select
                                                                value={editingGoal?.metric_type}
                                                                onValueChange={(value) => setEditingGoal({ ...editingGoal!, metric_type: value as KPIMetricType })}
                                                            >
                                                                <SelectTrigger>
                                                                    <SelectValue />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {Object.values(KPIMetricType).map((type) => (
                                                                        <SelectItem key={type} value={type}>
                                                                            {type}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <Switch
                                                                checked={editingGoal?.is_active}
                                                                onCheckedChange={(checked) => setEditingGoal({ ...editingGoal!, is_active: checked })}
                                                            />
                                                            <Label>Active</Label>
                                                        </div>
                                                        <Button
                                                            onClick={() => handleUpdate(goal.id.toString(), editingGoal!)}
                                                        >
                                                            Update Goal
                                                        </Button>
                                                    </div>
                                                </DialogContent>
                                            </Dialog>
                                            <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                    <AlertDialogHeader>
                                                        <AlertDialogTitle>Delete KPI Goal</AlertDialogTitle>
                                                        <AlertDialogDescription>
                                                            Are you sure you want to delete this KPI goal? This action cannot be undone.
                                                        </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                        <AlertDialogAction
                                                            onClick={() => handleDelete(goal.id.toString())}
                                                            className="bg-red-600 hover:bg-red-700"
                                                        >
                                                            Delete
                                                        </AlertDialogAction>
                                                    </AlertDialogFooter>
                                                </AlertDialogContent>
                                            </AlertDialog>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    );
} 