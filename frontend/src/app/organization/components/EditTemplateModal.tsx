'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useUpdateTemplateMutation, Template } from '@/services/templateManagementService';
import { Loader2 } from 'lucide-react';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

interface EditTemplateModalProps {
    template: Template;
    isOpen: boolean;
    onClose: () => void;
}

export function EditTemplateModal({ template, isOpen, onClose }: EditTemplateModalProps) {
    const [name, setName] = useState(template.name);
    const [description, setDescription] = useState(template.description);
    const [contextType, setContextType] = useState(template.context_type);
    const [useInCase, setUseInCase] = useState(template.use_in_case);
    const [useInLead, setUseInLead] = useState(template.use_in_lead);
    const [files, setFiles] = useState<File[]>([]);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const updateMutation = useUpdateTemplateMutation();

    useEffect(() => {
        setName(template.name);
        setDescription(template.description);
        setContextType(template.context_type);
        setUseInCase(template.use_in_case);
        setUseInLead(template.use_in_lead);
        setFiles([]);
    }, [template]);

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = name !== template.name || 
                       description !== template.description || 
                       contextType !== template.context_type ||
                       useInCase !== template.use_in_case ||
                       useInLead !== template.use_in_lead ||
                       files.length > 0;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            }
        }
    };

    const handleClose = () => {
        onClose();
        setFiles([]);
    };

    const handleCancelClick = () => {
        const isDirty = name !== template.name || 
                       description !== template.description || 
                       contextType !== template.context_type ||
                       useInCase !== template.use_in_case ||
                       useInLead !== template.use_in_lead ||
                       files.length > 0;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const fileList = e.target.files;
        if (fileList && fileList.length > 0) {
            setFiles(Array.from(fileList));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name || !contextType) return;

        try {
            // If files are selected, upload them sequentially
            if (files.length > 0) {
                for (const file of files) {
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('name', name);
                    formData.append('description', description || '');
                    formData.append('context_type', contextType);
                    formData.append('use_in_case', useInCase.toString());
                    formData.append('use_in_lead', useInLead.toString());

                    await updateMutation.mutateAsync({ id: template.id, formData });
                }
            } else {
                // If no files were selected, just update the template details
                const formData = new FormData();
                formData.append('name', name);
                formData.append('description', description || '');
                formData.append('context_type', contextType);
                formData.append('use_in_case', useInCase.toString());
                formData.append('use_in_lead', useInLead.toString());
                await updateMutation.mutateAsync({ id: template.id, formData });
            }

            onClose();
        } catch (error) {
            console.error('Failed to update template:', error);
        }
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={handleOpenChange}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Edit Template</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Template Name</label>
                            <Input
                                required
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                placeholder="Enter template name"
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Description</label>
                            <Textarea
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Enter template description"
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Context Type</label>
                            <Select required value={contextType} onValueChange={setContextType}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select context type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="attorney_lien">Attorney Lien</SelectItem>
                                    <SelectItem value="client">Client</SelectItem>
                                    <SelectItem value="client_adjuster">Client Adjuster</SelectItem>
                                    <SelectItem value="client_employer">Client Employer</SelectItem>
                                    <SelectItem value="client_health_insurance">Client Health Insurance</SelectItem>
                                    <SelectItem value="client_insurance">Client Insurance</SelectItem>
                                    <SelectItem value="court">Court</SelectItem>
                                    <SelectItem value="defendant">Defendant</SelectItem>
                                    <SelectItem value="defendant_adjuster">Defendant Adjuster</SelectItem>
                                    <SelectItem value="defendant_insurance">Defendant Insurance</SelectItem>
                                    <SelectItem value="defendant_legal_representation">Defendant Legal Representation</SelectItem>
                                    <SelectItem value="expert_witness">Expert Witness</SelectItem>
                                    <SelectItem value="health_provider">Health Provider</SelectItem>
                                    <SelectItem value="judge">Judge</SelectItem>
                                    <SelectItem value="mediator">Mediator</SelectItem>
                                    <SelectItem value="misc_lien">Misc Lien</SelectItem>
                                    <SelectItem value="other_party">Other Party</SelectItem>
                                    <SelectItem value="other_plaintiff">Other Plaintiff</SelectItem>
                                    <SelectItem value="police_agency">Police Agency</SelectItem>
                                    <SelectItem value="settlement_advance">Settlement Advance</SelectItem>
                                    <SelectItem value="sub_out">Sub Out</SelectItem>
                                    <SelectItem value="witness">Witness</SelectItem>
                                    <SelectItem value="client_correspondence">Client Correspondence</SelectItem>
                                    <SelectItem value="litigation">Litigation</SelectItem>
                                </SelectContent>

                            </Select>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Template Usage</label>
                            <div className="flex gap-4">
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        id="use_in_case"
                                        checked={useInCase}
                                        onChange={(e) => setUseInCase(e.target.checked)}
                                        className="h-4 w-4 rounded border-gray-300"
                                    />
                                    <label htmlFor="use_in_case" className="text-sm text-gray-600">
                                        Use in Case
                                    </label>
                                </div>
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        id="use_in_lead"
                                        checked={useInLead}
                                        onChange={(e) => setUseInLead(e.target.checked)}
                                        className="h-4 w-4 rounded border-gray-300"
                                    />
                                    <label htmlFor="use_in_lead" className="text-sm text-gray-600">
                                        Use in Lead
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Template Files (Optional)</label>
                            <div className="border-2 border-dashed rounded-lg p-4 hover:border-gray-400 transition-colors">
                                <Input
                                    type="file"
                                    accept=".doc,.docx"
                                    multiple
                                    onChange={handleFileChange}
                                    className="w-full"
                                />
                                <p className="text-sm text-gray-500 mt-2 text-center">
                                    Drag and drop files here or click to select files
                                </p>
                            </div>
                            {files.length > 0 && (
                                <div className="text-sm text-green-600 mt-2">
                                    Selected files:
                                    <ul className="list-disc pl-5 mt-1">
                                        {files.map((file, i) => (
                                            <li key={i}>{file.name}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end gap-3">
                            <Button type="button" variant="outline" onClick={handleCancelClick}>
                                Cancel
                            </Button>
                            <Button 
                                type="submit" 
                                disabled={updateMutation.isPending}
                                className="bg-green-600 hover:bg-green-700"
                            >
                                {updateMutation.isPending ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        {files.length > 0 ? 'Uploading Files...' : 'Updating...'}
                                    </>
                                ) : (
                                    'Update Template'
                                )}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    );
}
