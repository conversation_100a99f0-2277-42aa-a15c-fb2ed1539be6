'use client';

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, FileDown } from 'lucide-react';
import { useTemplateRequiredVarsMutation, useRenderTemplateMutation } from '@/services/templateManagementService';
import { useToast } from "@/hooks/use-toast";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

interface RenderTemplateModalProps {
    templateId: number;
    templateName: string;
}

export interface FormValues {
    [key: string]: string;
}

export function RenderTemplateModal({ templateId, templateName }: RenderTemplateModalProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [formValues, setFormValues] = useState<FormValues>({});
    const [isLoading, setIsLoading] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const { toast } = useToast();

    const requiredVarsMutation = useTemplateRequiredVarsMutation();
    const renderTemplateMutation = useRenderTemplateMutation();

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = Object.values(formValues).some(value => value !== '');
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsOpen(true);
            }
        }
    };

    const handleClose = () => {
        setIsOpen(false);
        setFormValues({});
    };

    const handleCancelClick = () => {
        const isDirty = Object.values(formValues).some(value => value !== '');
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleInputChange = (variable: string, value: string) => {
        setFormValues(prevValues => {
            const newValues = { ...prevValues };
            newValues[variable] = value;
            return newValues;
        });
    };

    const handleRender = async () => {
        // Validate all required fields are filled
        const emptyFields = Object.entries(formValues)
            .filter(([key]) => !formValues[key])
            .map(([key]) => key);

        if (emptyFields.length > 0) {
            toast({
                title: "Validation Error",
                description: `Please fill in all required fields: ${emptyFields.join(', ')}`,
                variant: "destructive"
            });
            return;
        }

        setIsLoading(true);
        try {
            const payload = {
                templateId: Number(templateId),
                variables: formValues,
                name: templateName || 'rendered-template'
            };

            const {data: blob, file_name} = await renderTemplateMutation.mutateAsync(payload);

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = decodeURIComponent(file_name);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

            setIsOpen(false);
            toast({
                title: "Success",
                description: "Template rendered and downloaded successfully",
            });
        } catch (error) {
            toast({
                title: "Error",
                description: `Failed to render template: ${error}`,
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    <div className="flex items-center w-full px-2 py-1.5 text-sm cursor-pointer">
                        <FileDown className="h-4 w-4 mr-2" />
                        Render
                    </div>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Render Template: {templateName}</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        {requiredVarsMutation.data?.required_variables.map((variable) => (
                            <div key={variable} className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor={variable} className="text-right">
                                    {variable}
                                </Label>
                                <Input
                                    id={variable}
                                    value={formValues[variable] || ''}
                                    onChange={(e) => handleInputChange(variable, e.target.value)}
                                    className="col-span-3"
                                    placeholder={`Enter ${variable}`}
                                />
                            </div>
                        ))}
                    </div>
                    <div className="flex justify-end gap-3">
                        <Button
                            variant="outline"
                            onClick={handleCancelClick}
                            disabled={isLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleRender}
                            disabled={isLoading || Object.values(formValues).some(value => !value)}
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Rendering...
                                </>
                            ) : (
                                'Render & Download'
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleClose}
                onCancel={handleClose}
            />
        </>
    );
}
