'use client';

import { useState, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { useCreateTemplateMutation } from '@/services/templateManagementService';
import { Loader2, Plus } from 'lucide-react';
import { TemplateFormItem, TemplateFormData } from './TemplateFormItem';
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast"
import { AxiosError } from 'axios';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

const initialTemplateData: TemplateFormData = {
    name: '',
    description: '',
    contextType: '',
    file: null,
    use_in_case: true,
    use_in_lead: false,
};

export function CreateTemplateModal() {
    const [isOpen, setIsOpen] = useState(false);
    const [templates, setTemplates] = useState<TemplateFormData[]>([]);
    const [progress, setProgress] = useState({ current: 0, total: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [currentUploadIndex, setCurrentUploadIndex] = useState<number>(0);
    const [isUploading, setIsUploading] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const createMutation = useCreateTemplateMutation();
    const { toast } = useToast();

    const handleRemoveTemplate = (index: number) => {
        setTemplates(templates.filter((_, i) => i !== index));
    };

    const handleTemplateChange = (index: number, data: Partial<TemplateFormData>) => {
        setTemplates(templates.map((template, i) => 
            i === index ? { ...template, ...data } : template
        ));
    };

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);

        const files = Array.from(e.dataTransfer.files).filter(file => 
            file.type === 'application/msword' || 
            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        );
        
        if (files.length === 0) return;

        // Create one template per file
        const newTemplates = files.map(file => ({
            ...initialTemplateData,
            file,
            name: file.name.replace(/\.[^/.]+$/, '')
        }));
        setTemplates(prev => [...prev, ...newTemplates]);
    }, []);

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []).filter(file => 
            file.type === 'application/msword' || 
            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        );
        
        if (files.length === 0) return;

        // Create one template per file
        const newTemplates = files.map(file => ({
            ...initialTemplateData,
            file,
            name: file.name.replace(/\.[^/.]+$/, '')
        }));
        setTemplates(prev => [...prev, ...newTemplates]);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        const validTemplates = templates.filter(template => 
            template.file && template.contextType && template.name
        );

        if (validTemplates.length === 0) {
            toast({
                title: "Validation Error",
                description: "Please ensure all templates have a file, context type, and name",
                variant: "destructive"
            });
            return;
        }

        setIsUploading(true);
        setProgress({ current: 0, total: validTemplates.length });
        setCurrentUploadIndex(0);

        let successCount = 0;
        let failureCount = 0;

        try {
            for (let i = 0; i < validTemplates.length; i++) {
                const template = validTemplates[i];
                setCurrentUploadIndex(i);

                const formData = new FormData();
                formData.append('file', template.file!);
                formData.append('name', template.name);
                formData.append('description', template.description);
                formData.append('context_type', template.contextType);
                formData.append('use_in_case', template.use_in_case.toString());
                formData.append('use_in_lead', template.use_in_lead.toString());
                try {
                    await createMutation.mutateAsync(formData);
                    successCount++;
                } catch (error) {
                    failureCount++;
                    // Handle duplicate name error
                    const axiosError = error as AxiosError<{ non_field_errors: string[] }>;
                    if (axiosError.response?.status === 400) {
                        toast({
                            title: `Failed to upload "${template.name}"`,
                            description: axiosError.response.data.non_field_errors[0],
                            variant: "destructive"
                        });
                    } else {
                        toast({
                            title: `Failed to upload "${template.name}"`,
                            description: "An unexpected error occurred, please keep with unique names",
                            variant: "destructive"
                        });
                    }
                    console.error(`Failed to upload template ${template.name}:`, error);
                }
                setProgress(prev => ({ ...prev, current: i + 1 }));
            }

            // Show summary toast only if there were successful uploads
            if (successCount > 0) {
                toast({
                    title: "Upload Complete",
                    description: `Successfully uploaded ${successCount} template${successCount !== 1 ? 's' : ''}${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
                    variant: successCount === validTemplates.length ? "default" : "destructive"
                });
            }

            if (successCount === validTemplates.length) {
                setIsOpen(false);
                setTemplates([]);
            }
        } finally {
            setIsUploading(false);
            setProgress({ current: 0, total: 0 });
            setCurrentUploadIndex(0);
        }
    };

    const progressPercentage = isUploading 
        ? Math.round((progress.current / progress.total) * 100) 
        : 0;

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = templates.length > 0;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsOpen(true);
            }
        }
    };

    const handleClose = () => {
        setIsOpen(false);
        setTemplates([]);
        setProgress({ current: 0, total: 0 });
        setCurrentUploadIndex(0);
        setIsUploading(false);
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    <Button className="bg-green-600 hover:bg-green-700 text-white min-w-[120px]">
                        <Plus className="w-4 h-4 mr-2" />
                        New Template
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Create New Templates</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div
                            className={cn(
                                "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                                "hover:border-gray-400 hover:bg-gray-50",
                                isDragging ? "border-green-500 bg-green-50" : "border-gray-300",
                            )}
                            onDragOver={(e) => {
                                e.preventDefault();
                                setIsDragging(true);
                            }}
                            onDragLeave={() => setIsDragging(false)}
                            onDrop={handleDrop}
                        >
                            <div className="flex flex-col items-center gap-2">
                                <div className="rounded-full bg-gray-100 p-3">
                                    <Plus className="h-6 w-6 text-gray-600" />
                                </div>
                                <div className="text-sm">
                                    <span className="font-semibold text-gray-900">
                                        Click to upload
                                    </span>{" "}
                                    or drag and drop
                                </div>
                                <p className="text-xs text-gray-500">
                                    Word documents only (*.docx)
                                </p>
                                <input
                                    type="file"
                                    multiple
                                    accept=".docx"
                                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                    onChange={handleFileSelect}
                                />
                            </div>
                        </div>

                        <div className="space-y-4">
                            {templates.length > 0 ? (
                                templates.map((template, index) => (
                                    <div key={index} className="rounded-lg border border-gray-200 p-4 shadow-sm">
                                        <TemplateFormItem
                                            data={template}
                                            index={index}
                                            onChange={handleTemplateChange}
                                            onRemove={handleRemoveTemplate}
                                        />
                                    </div>
                                ))
                            ) : (
                                <p className="text-center text-gray-500 text-sm">
                                    No templates added yet. Upload files to create templates.
                                </p>
                            )}
                        </div>

                        {isUploading && (
                            <div className="space-y-2 bg-gray-50 rounded-lg p-4">
                                <Progress value={progressPercentage} className="h-2" />
                                <p className="text-sm text-center text-gray-600">
                                    Creating template {progress.current + 1} of {progress.total}
                                </p>
                                <p className="text-xs text-center text-gray-500">
                                    Currently uploading: {templates[currentUploadIndex]?.name || ''}
                                </p>
                            </div>
                        )}

                        <div className="flex justify-end gap-3 pt-4 border-t">
                            <Button 
                                type="button" 
                                variant="outline" 
                                onClick={() => setIsOpen(false)}
                                disabled={isUploading}
                            >
                                Cancel
                            </Button>
                            <Button 
                                type="submit"
                                disabled={isUploading || templates.length === 0}
                                className="bg-green-600 hover:bg-green-700 text-white font-medium"
                                onClick={handleSubmit}
                            >
                                {isUploading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Creating Templates...
                                    </>
                                ) : (
                                    'Create Templates'
                                )}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    );
}
