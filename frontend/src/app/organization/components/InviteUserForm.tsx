'use client';

import { useState } from 'react';
import { useCreateInvite } from '@/services/organizationService';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { UserPlus } from "lucide-react";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

export function InviteUserForm() {
    const [open, setOpen] = useState(false);
    const [email, setEmail] = useState('');
    const [role, setRole] = useState<'admin' | 'general'>('general');
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const createInvite = useCreateInvite();

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = email !== '' || role !== 'general';
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setOpen(true);
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        setEmail('');
        setRole('general');
    };

    const handleCancelClick = () => {
        const isDirty = email !== '' || role !== 'general';
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleSubmit = async () => {
        try {
            await createInvite.mutateAsync({ email, role });
            handleClose();
        } catch (error) {
            console.error('Failed to send invitation:', error);
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                    <Button className="gap-2">
                        <UserPlus className="w-4 h-4" />
                        Invite User
                    </Button>
                </DialogTrigger>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Invite User</DialogTitle>
                        <DialogDescription>
                            Send an invitation to join your organization.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Email</label>
                            <Input
                                type="email"
                                placeholder="<EMAIL>"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Role</label>
                            <Select value={role} onValueChange={(value: 'admin' | 'general') => setRole(value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="general">General</SelectItem>
                                    <SelectItem value="admin">Admin</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={handleCancelClick}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            disabled={!email.trim() || createInvite.isPending}
                        >
                            Send Invitation
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleClose}
                onCancel={handleClose}
            />
        </>
    );
} 