'use client';

import { useState } from 'react';
import { useCurrentOrganization, useUpdateOrganization } from '@/services/organizationService';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Building2, Phone, Globe, MapPin, Edit } from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";

export function OrganizationDetails() {
    const [isEditing, setIsEditing] = useState(false);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const { data: organization, isLoading } = useCurrentOrganization();
    const updateOrganization = useUpdateOrganization();

    const [formData, setFormData] = useState({
        name: '',
        profile: {
            address: '',
            phone: '',
            website: '',
            
            description: ''
        }
    });

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = formData.name !== organization?.name || 
                       formData.profile.address !== organization?.profile?.address ||
                       formData.profile.phone !== organization?.profile?.phone ||
                       formData.profile.website !== organization?.profile?.website ||
                       formData.profile.description !== organization?.profile?.description;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                handleEdit();
            }
        }
    };

    const handleClose = () => {
        setIsEditing(false);
        setFormData({
            name: '',
            profile: {
                address: '',
                phone: '',
                website: '',
                description: ''
            }
        });
    };

    const handleCancelClick = () => {
        const isDirty = formData.name !== organization?.name || 
                       formData.profile.address !== organization?.profile?.address ||
                       formData.profile.phone !== organization?.profile?.phone ||
                       formData.profile.website !== organization?.profile?.website ||
                       formData.profile.description !== organization?.profile?.description;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const handleEdit = () => {
        setFormData({
            name: organization?.name || '',
            profile: {
                address: organization?.profile?.address || '',
                phone: organization?.profile?.phone || '',
                website: organization?.profile?.website || '',
                description: organization?.profile?.description || ''
            }
        });
        setIsEditing(true);
    };

    const handleSubmit = async () => {
        try {
            await updateOrganization.mutateAsync(formData);
            handleClose();
        } catch (error) {
            console.error('Failed to update organization:', error);
        }
    };

    if (isLoading) {
        return <div>Loading organization details...</div>;
    }

    if (!organization) {
        return <div>No organization details found</div>;
    }

    return (
        <>
            <Card className="p-6">
                <div className="flex justify-between items-start mb-6">
                    <div className="flex items-center gap-2">
                        <Building2 className="h-5 w-5 text-gray-500" />
                        <h2 className="text-xl font-bold">{organization.name}</h2>
                    </div>
                    <Button onClick={handleEdit} variant="outline" size="sm" className="gap-2">
                        <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                    </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-600">
                            <MapPin className="h-4 w-4" />
                            <span className="font-medium">Address</span>
                        </div>
                        <p className="text-sm text-gray-600">
                            {organization.profile?.address || 'No address provided'}
                        </p>
                    </div>

                    <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-600">
                            <Phone className="h-4 w-4" />
                            <span className="font-medium">Phone</span>
                        </div>
                        <p className="text-sm text-gray-600">
                            {organization.profile?.phone || 'No phone number provided'}
                        </p>
                    </div>

                    <div className="space-y-2">
                        <div className="flex items-center gap-2 text-gray-600">
                            <Globe className="h-4 w-4" />
                            <span className="font-medium">Website</span>
                        </div>
                        <p className="text-sm text-gray-600">
                            {organization.profile?.website ? (
                                <a 
                                    href={organization.profile.website}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-[#060216]-600 hover:underline"
                                >
                                    {organization.profile.website}
                                </a>
                            ) : (
                                'No website provided'
                            )}
                        </p>
                    </div>

                    <div className="md:col-span-2 space-y-2">
                        <div className="flex items-center gap-2 text-gray-600">
                            <span className="font-medium">Description</span>
                        </div>
                        <p className="text-sm text-gray-600">
                            {organization.profile?.description || 'No description provided'}
                        </p>
                    </div>
                </div>
            </Card>

            <Dialog open={isEditing} onOpenChange={handleOpenChange}>
                <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>Edit Organization Details</DialogTitle>
                        <DialogDescription>
                            Update your organization&apos;s information. Click save when you&apos;re done.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">Organization Name</Label>
                            <Input
                                id="name"
                                value={formData.name}
                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="address">Address</Label>
                            <Input
                                id="address"
                                value={formData.profile.address}
                                onChange={(e) => setFormData({
                                    ...formData,
                                    profile: { ...formData.profile, address: e.target.value }
                                })}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phone">Phone Number</Label>
                            <Input
                                id="phone"
                                value={formData.profile.phone}
                                onChange={(e) => setFormData({
                                    ...formData,
                                    profile: { ...formData.profile, phone: e.target.value }
                                })}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="website">Website</Label>
                            <Input
                                id="website"
                                type="url"
                                value={formData.profile.website}
                                onChange={(e) => setFormData({
                                    ...formData,
                                    profile: { ...formData.profile, website: e.target.value }
                                })}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                value={formData.profile.description}
                                onChange={(e) => setFormData({
                                    ...formData,
                                    profile: { ...formData.profile, description: e.target.value }
                                })}
                                rows={4}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={handleCancelClick}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            disabled={updateOrganization.isPending}
                        >
                            Save Changes
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleClose}
                onCancel={handleClose}
            />
        </>
    );
} 