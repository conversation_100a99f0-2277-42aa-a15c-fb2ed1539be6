'use client';

import { useState } from 'react';
import { usePendingInvites, useRevokeInvite, useResendInvite } from '@/services/organizationService';
import { Button } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { XCircle, RefreshCw } from "lucide-react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export function PendingInvites() {
    const [inviteToRevoke, setInviteToRevoke] = useState<number | null>(null);
    const { data: invites = [], isLoading } = usePendingInvites();
    const revokeInvite = useRevokeInvite();
    const resendInvite = useResendInvite();

    const handleRevokeInvite = async () => {
        if (inviteToRevoke === null) return;
        
        try {
            await revokeInvite.mutateAsync(inviteToRevoke);
            setInviteToRevoke(null);
        } catch (error) {
            console.error('Failed to revoke invitation:', error);
        }
    };

    const handleResendInvite = async (inviteId: number) => {
        try {
            await resendInvite.mutateAsync(inviteId);
        } catch (error) {
            console.error('Failed to resend invitation:', error);
        }
    };

    if (isLoading) {
        return <div>Loading pending invites...</div>;
    }

    if (invites.length === 0) {
        return (
            <div className="text-center py-6 text-gray-500">
                No pending invitations
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="bg-white rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Email</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead>Expires</TableHead>
                            <TableHead>Sent</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {invites.map((invite) => (
                            <TableRow key={invite.id}>
                                <TableCell className="font-medium">{invite.email}</TableCell>
                                <TableCell>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                        invite.role === 'admin' 
                                            ? 'bg-blue-100 text-[#060216]-700'
                                            : 'bg-gray-100 text-gray-700'
                                    }`}>
                                        {invite.role}
                                    </span>
                                </TableCell>
                                <TableCell>{format(new Date(invite.expires_at), 'MMM d, yyyy')}</TableCell>
                                <TableCell>{format(new Date(invite.created_at), 'MMM d, yyyy')}</TableCell>
                                <TableCell className="text-right space-x-2">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleResendInvite(invite.id)}
                                        disabled={resendInvite.isPending}
                                        className="text-[#060216]-600 hover:text-[#060216]-700 hover:bg-blue-50"
                                    >
                                        <RefreshCw className="w-4 h-4 mr-2" />
                                        Resend
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setInviteToRevoke(invite.id)}
                                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                        <XCircle className="w-4 h-4 mr-2" />
                                        Revoke
                                    </Button>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <AlertDialog open={inviteToRevoke !== null} onOpenChange={() => setInviteToRevoke(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Revoke Invitation</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to revoke this invitation? 
                            The user will no longer be able to join the organization with this invite.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                            onClick={handleRevokeInvite}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            Revoke
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
} 