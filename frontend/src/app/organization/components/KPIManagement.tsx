"use client";

import { useState, useMemo } from "react";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useCaseKPIsQuery, useLeadKPIsQuery, KPI, KPIMetric } from '@/services/kpiService';
import { useOrganizationUsers, OrganizationUser } from '@/services/organizationService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { filterKPIsByRole, hasIntakeRole } from '@/utils/kpiFilters';
import { useUserTags } from "@/services/organizationService";

// Reuse the KPI formatting functions from dashboard
const formatKPIValue = (metric: KPIMetric) => {
  switch (metric.type) {
    case 'monetary':
      return new Intl.NumberFormat('en-US', { 
        style: 'currency', 
        currency: 'USD',
        maximumFractionDigits: 0 
      }).format(metric.value);
    case 'percentage':
      return `${metric.value}%`;
    case 'time':
      if (metric.unit === 'days') {
        return `${metric.value} days`;
      }
      return metric.value;
    default:
      return metric.value.toLocaleString();
  }
};

const getKPILabel = (kpiType: string): string => {
  const labels: Record<string, string> = {
    case_demand_ready: 'Demand Ready',
    case_closed: 'Cases Closed',
    cases_touched: 'Cases Touched',
    treatment_period: 'Treatment Period',
    avg_case_age: 'Average Case Age',
    care_calls_percentage: 'Care Calls',
    notes_entered: 'Notes Entered',
    three_plus_providers: '3+ Providers',
    cases_in_negotiation: 'In Negotiation',
    total_attorney_fees: 'Attorney Fees',
    depositions_taken: 'Depositions',
    mediations_arb_trials: 'Mediations/Trials',
    hearings: 'Hearings'
  };
  return labels[kpiType] || kpiType.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
};

const getKPIDescription = (kpi: KPI): string => {
  const { metrics, additional_metrics } = kpi;
  
  switch (kpi.kpi_type) {
    case 'avg_case_age':
      if (additional_metrics?.breakdown) {
        return additional_metrics.breakdown.human_readable;
      }
      break;
    case 'notes_entered':
      if (additional_metrics?.average_notes_per_case) {
        return `Avg ${additional_metrics.average_notes_per_case.toFixed(1)} notes per case`;
      }
      break;
    case 'care_calls_percentage':
      if (additional_metrics?.status === 'not_implemented') {
        return 'Feature not implemented';
      }
      break;
  }

  if (metrics.rate) {
    return `${formatKPIValue(metrics.rate)} of total cases`;
  }

  return '';
};

const KPICard = ({ kpi, showScope = true }: { kpi: KPI; showScope?: boolean }) => {
  const { metrics } = kpi;
  const label = getKPILabel(kpi.kpi_type);
  const description = getKPIDescription(kpi);
  const value = formatKPIValue(metrics.primary);
  const progress = metrics.rate ? metrics.rate.value : 
    (metrics.primary.value / metrics.total_items) * 100;

  const getScopeStyle = (scope: string) => {
    switch (scope) {
      case 'personal':
        return 'bg-green-100 text-green-800';
      case 'user':
        return 'bg-blue-100 text-blue-800';
      case 'organization':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScopeLabel = (scope: string) => {
    switch (scope) {
      case 'personal':
        return 'Personal';
      case 'user':
        return 'User Specific';
      case 'organization':
        return 'Organization';
      default:
        return scope;
    }
  };

  return (
    <Card className="p-6">
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-lg font-medium text-gray-900">{label}</h3>
        {showScope && (
          <span className={`px-2 py-1 rounded-full text-xs ${getScopeStyle(kpi.scope)}`}>
            {getScopeLabel(kpi.scope)}
          </span>
        )}
      </div>
      <div className="space-y-4">
        <div className="flex justify-between items-baseline">
          <p className="text-3xl font-bold">{value}</p>
          <p className="text-sm text-gray-500">
            Total: {metrics.total_items}
          </p>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-500"
            style={{ width: `${Math.min(progress, 100)}%` }}
          />
        </div>
        {description && (
          <p className="text-sm text-gray-600">{description}</p>
        )}
      </div>
    </Card>
  );
};

export function KPIManagement() {
  const [activeTab, setActiveTab] = useState<'cases' | 'leads'>('cases');
  const [selectedUser, setSelectedUser] = useState<string>('my-kpis');
  
  // Get user's organization roles (tags)
  const { data: userTags = [] } = useUserTags();

  // Fetch organization users
  const { data: orgUsers, isLoading: isLoadingUsers } = useOrganizationUsers();

  // Helper function to format user display name
  const getUserDisplayName = (user: OrganizationUser): string => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user.email;
  };

  // Determine API parameters based on selected user
  const kpiParams = useMemo(() => {
    if (selectedUser === 'org-wide') {
      return { org_wide: true }; // Organization view
    }
    if (selectedUser === 'my-kpis') {
      return {}; // Default behavior: returns requesting user's KPIs
    }
    return { user_id: selectedUser }; // For specific user views
  }, [selectedUser]);

  // Fetch KPIs based on selection
  const {
    data: caseKPIs,
    isLoading: isLoadingCaseKPIs,
  } = useCaseKPIsQuery(kpiParams);

  const {
    data: leadKPIs,
    isLoading: isLoadingLeadKPIs,
  } = useLeadKPIsQuery(kpiParams);

  const isLoading = isLoadingCaseKPIs || isLoadingLeadKPIs || isLoadingUsers;

  // Combine KPIs based on active tab
  const currentKPIs = useMemo(() => {
    if (activeTab === 'cases' && caseKPIs) {
      // Filter KPIs based on user's organization roles
      const filteredKPIs = filterKPIsByRole(caseKPIs as unknown as Record<string, KPI>, userTags);
      
      // Convert filtered KPIs to array format
      return Object.entries(filteredKPIs)
        .filter(([, kpi]) => {
          // Only show KPIs that have data
          return kpi.metrics && kpi.metrics.total_items > 0;
        })
        .map(([key, kpi]) => ({
          ...kpi,
          id: key,
          // Add display properties
          display_name: getKPILabel(kpi.kpi_type),
          description: getKPIDescription(kpi)
        }));
    } else if (activeTab === 'leads' && leadKPIs) {
      // Only show lead KPIs to users with Intake role
      if (!hasIntakeRole(userTags)) {
        return [];
      }

      // Handle both array and object formats for lead KPIs
      const leadKpiArray = Array.isArray(leadKPIs) ? leadKPIs :
        leadKPIs.lead_kpis ? leadKPIs.lead_kpis :
        Object.entries(leadKPIs).map(([key, value]) => ({ ...value, id: key }));
      
      // Filter and format lead KPIs
      return leadKpiArray
        .filter(kpi => kpi.metrics && kpi.metrics.total_items >= 0)
        .map((kpi, index) => ({
          ...kpi,
          id: kpi.id || `lead-${index}`,
          // Add display properties
          display_name: getKPILabel(kpi.kpi_type),
          description: getKPIDescription(kpi)
        }));
    }
    return [];
  }, [activeTab, caseKPIs, leadKPIs, userTags]);

  return (
    <div className="space-y-6">
      {/* User dropdown selection */}
      <div className="w-full mb-6">
        <label htmlFor="user-select" className="block text-sm font-medium text-gray-700 mb-2">
          Select User to View KPIs
        </label>
        <Select
          value={selectedUser}
          onValueChange={setSelectedUser}
        >
          <SelectTrigger id="user-select" className="w-full md:w-[350px]">
            <SelectValue placeholder="Select User" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="my-kpis">My KPIs</SelectItem>
            <SelectItem value="org-wide">Organization-wide KPIs</SelectItem>
            <SelectItem value="divider" disabled>
              ───────────────
            </SelectItem>
            {orgUsers?.map((user: OrganizationUser) => (
              <SelectItem key={user.id} value={String(user.id)}>
                {getUserDisplayName(user)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Tabs 
        defaultValue="cases" 
        className="w-full"
        onValueChange={(value) => setActiveTab(value as 'cases' | 'leads')}
      >
        <div className="flex justify-between items-center mb-6">
          <TabsList>
            <TabsTrigger value="cases">Cases</TabsTrigger>
            {hasIntakeRole(userTags) && (
              <TabsTrigger value="leads">Leads</TabsTrigger>
            )}
          </TabsList>
        </div>

        <TabsContent value="cases" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array(6).fill(0).map((_, i) => (
                <Card key={i} className="p-6 animate-pulse">
                  <div className="space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-2/3" />
                    <div className="h-8 bg-gray-200 rounded" />
                    <div className="h-2 bg-gray-200 rounded" />
                  </div>
                </Card>
              ))}
            </div>
          ) : currentKPIs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentKPIs.map((kpi) => (
                <KPICard key={kpi.id} kpi={kpi} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              No KPIs available for your role
            </div>
          )}
        </TabsContent>

        {hasIntakeRole(userTags) && (
          <TabsContent value="leads" className="space-y-6">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array(6).fill(0).map((_, i) => (
                  <Card key={i} className="p-6 animate-pulse">
                    <div className="space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-2/3" />
                      <div className="h-8 bg-gray-200 rounded" />
                      <div className="h-2 bg-gray-200 rounded" />
                    </div>
                  </Card>
                ))}
              </div>
            ) : currentKPIs.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {currentKPIs.map((kpi) => (
                  <KPICard key={kpi.id} kpi={kpi} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                No lead KPIs available
              </div>
            )}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
} 