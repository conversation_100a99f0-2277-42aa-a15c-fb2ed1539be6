'use client';

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { X } from 'lucide-react';

export interface TemplateFormData {
    name: string;
    description: string;
    contextType: string;
    file: File | null;
    use_in_case: boolean;
    use_in_lead: boolean;
}

interface TemplateFormItemProps {
    data: TemplateFormData;
    index: number;
    onChange: (index: number, data: Partial<TemplateFormData>) => void;
    onRemove: (index: number) => void;
}

export function TemplateFormItem({ data, index, onChange, onRemove }: TemplateFormItemProps) {

    return (
        <div className="space-y-4 p-4 border rounded-lg relative">
            <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute top-2 right-2 text-gray-500 hover:text-red-600"
                onClick={() => onRemove(index)}
            >
                <X className="h-4 w-4" />
            </Button>

            <div className="space-y-2">
                <label className="text-sm font-medium">Template Name</label>
                <Input
                    required
                    value={data.name}
                    onChange={(e) => onChange(index, { name: e.target.value })}
                    placeholder="Enter template name"
                />
            </div>

            <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Textarea
                    value={data.description}
                    onChange={(e) => onChange(index, { description: e.target.value })}
                    placeholder="Enter template description"
                />
            </div>

            <div className="space-y-2">
                <label className="text-sm font-medium">Context Type</label>
                <Select 
                    required 
                    value={data.contextType} 
                    onValueChange={(value) => onChange(index, { contextType: value })}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="Select context type" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="attorney_lien">Attorney Lien</SelectItem>
                        <SelectItem value="client">Client</SelectItem>
                        <SelectItem value="client_adjuster">Client Adjuster</SelectItem>
                        <SelectItem value="client_employer">Client Employer</SelectItem>
                        <SelectItem value="client_health_insurance">Client Health Insurance</SelectItem>
                        <SelectItem value="client_insurance">Client Insurance</SelectItem>
                        <SelectItem value="court">Court</SelectItem>
                        <SelectItem value="defendant">Defendant</SelectItem>
                        <SelectItem value="defendant_adjuster">Defendant Adjuster</SelectItem>
                        <SelectItem value="defendant_insurance">Defendant Insurance</SelectItem>
                        <SelectItem value="defendant_legal_representation">Defendant Legal Representation</SelectItem>
                        <SelectItem value="expert_witness">Expert Witness</SelectItem>
                        <SelectItem value="health_provider">Health Provider</SelectItem>
                        <SelectItem value="judge">Judge</SelectItem>
                        <SelectItem value="mediator">Mediator</SelectItem>
                        <SelectItem value="misc_lien">Misc Lien</SelectItem>
                        <SelectItem value="other_party">Other Party</SelectItem>
                        <SelectItem value="other_plaintiff">Other Plaintiff</SelectItem>
                        <SelectItem value="police_agency">Police Agency</SelectItem>
                        <SelectItem value="settlement_advance">Settlement Advance</SelectItem>
                        <SelectItem value="sub_out">Sub Out</SelectItem>
                        <SelectItem value="witness">Witness</SelectItem>
                        <SelectItem value="client_correspondence">Client Correspondence</SelectItem>
                        <SelectItem value="litigation">Litigation</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div className="space-y-2">
                <label className="text-sm font-medium">Template Usage</label>
                <div className="flex gap-4">
                    <div className="flex items-center gap-2">
                        <input
                            type="checkbox"
                            id={`use_in_case_${index}`}
                            checked={data.use_in_case}
                            onChange={(e) => onChange(index, { use_in_case: e.target.checked })}
                            className="h-4 w-4 rounded border-gray-300"
                        />
                        <label htmlFor={`use_in_case_${index}`} className="text-sm text-gray-600">
                            Use in Case
                        </label>
                    </div>
                    <div className="flex items-center gap-2">
                        <input
                            type="checkbox"
                            id={`use_in_lead_${index}`}
                            checked={data.use_in_lead}
                            onChange={(e) => onChange(index, { use_in_lead: e.target.checked })}
                            className="h-4 w-4 rounded border-gray-300"
                        />
                        <label htmlFor={`use_in_lead_${index}`} className="text-sm text-gray-600">
                            Use in Lead
                        </label>
                    </div>
                </div>
            </div>

            <div className="space-y-2">
                <label className="text-sm font-medium">Template File</label>
                <Input
                    required={!data.file}
                    type="file"
                    accept=".doc,.docx"
                    onChange={(e) => {
                        const file = e.target.files?.[0] || null;
                        onChange(index, { file });
                    }}
                    className="w-full"
                />
                {data.file && (
                    <div className="text-sm text-green-600">
                        Selected file: {data.file.name}
                    </div>
                )}
            </div>
        </div>
    );
} 