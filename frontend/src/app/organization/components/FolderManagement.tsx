import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Search } from "lucide-react";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { 
    useOrganizationFolders,
    useSectionConnectedOptions,
    useCreateFolder,
    useUpdateFolder,
    useDeleteFolder,
    OrganizationFolder
} from '@/services/organizationService';
import { toast } from '@/hooks/use-toast';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

export function FolderManagement() {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [editingFolder, setEditingFolder] = useState<OrganizationFolder | null>(null);
    const [deletingFolder, setDeletingFolder] = useState<OrganizationFolder | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [newFolderName, setNewFolderName] = useState('');
    const [newFolderDescription, setNewFolderDescription] = useState('');
    const [newFolderEnabled, setNewFolderEnabled] = useState(true);
    const [newFolderSections, setNewFolderSections] = useState<string[]>([]);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const { data: folders, isLoading } = useOrganizationFolders(searchQuery);
    const { data: sectionOptions } = useSectionConnectedOptions();
    const { mutate: createFolder } = useCreateFolder();
    const { mutate: updateFolder } = useUpdateFolder();
    const { mutate: deleteFolder } = useDeleteFolder();

    const handleCreateFolder = () => {
        if (!newFolderName.trim()) {
            toast({
                title: "Error",
                description: "Folder name is required",
                variant: "destructive",
            });
            return;
        }

        createFolder({
            name: newFolderName.trim(),
            description: newFolderDescription.trim(),
            is_enabled: newFolderEnabled,
            sections_connected: newFolderSections,
        }, {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                resetForm();
            }
        });
    };

    const handleUpdateFolder = () => {
        if (!editingFolder || !newFolderName.trim()) {
            toast({
                title: "Error",
                description: "Folder name is required",
                variant: "destructive",
            });
            return;
        }

        updateFolder({
            id: editingFolder.id,
            data: {
                name: newFolderName.trim(),
                description: newFolderDescription.trim(),
                is_enabled: newFolderEnabled,
                sections_connected: newFolderSections,
            }
        }, {
            onSuccess: () => {
                setEditingFolder(null);
                resetForm();
            }
        });
    };

    const handleDeleteFolder = () => {
        if (deletingFolder) {
            deleteFolder(deletingFolder.id, {
                onSuccess: () => {
                    setDeletingFolder(null);
                }
            });
        }
    };

    const openEditDialog = (folder: OrganizationFolder) => {
        setEditingFolder(folder);
        setNewFolderName(folder.name);
        setNewFolderDescription(folder.description);
        setNewFolderEnabled(folder.is_enabled);
        setNewFolderSections(folder.sections_connected || []);
    };

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = newFolderName !== '' || 
                       newFolderDescription !== '' || 
                       !newFolderEnabled ||
                       newFolderSections.length > 0;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsAddDialogOpen(true);
            }
        }
    };

    const handleClose = () => {
        setIsAddDialogOpen(false);
        resetForm();
    };

    const handleCancelClick = () => {
        const isDirty = newFolderName !== '' || 
                       newFolderDescription !== '' || 
                       !newFolderEnabled ||
                       newFolderSections.length > 0;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const resetForm = () => {
        setNewFolderName('');
        setNewFolderDescription('');
        setNewFolderEnabled(true);
        setNewFolderSections([]);
    };

    const handleSectionSelect = (value: string) => {
        if (!newFolderSections.includes(value)) {
            setNewFolderSections([...newFolderSections, value]);
        }
    };

    const removeSection = (sectionToRemove: string) => {
        setNewFolderSections(newFolderSections.filter(section => section !== sectionToRemove));
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    return (
        <>
            <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Folder Management</h3>
                    <Dialog open={isAddDialogOpen} onOpenChange={handleOpenChange}>
                        <DialogTrigger asChild>
                            <Button>Add New Folder</Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Create New Folder</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <label>Name</label>
                                    <Input
                                        value={newFolderName}
                                        onChange={(e) => setNewFolderName(e.target.value)}
                                        placeholder="Enter folder name"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label>Description</label>
                                    <Textarea
                                        value={newFolderDescription}
                                        onChange={(e) => setNewFolderDescription(e.target.value)}
                                        placeholder="Enter folder description"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label>Section</label>
                                    <p className="text-sm text-muted-foreground">
                                        When users upload documents in a specific section (e.g., Medical Treatment), files will be automatically organized in this folder.
                                    </p>
                                    <div className="space-y-2">
                                        <Select onValueChange={handleSectionSelect}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select sections" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {sectionOptions && Object.entries(sectionOptions).map(([key, value]) => (
                                                    <SelectItem 
                                                        key={key} 
                                                        value={key}
                                                        disabled={newFolderSections.includes(key)}
                                                    >
                                                        {value.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <div className="flex flex-wrap gap-2">
                                            {newFolderSections.map((section) => (
                                                <Badge key={section} variant="secondary" className="flex items-center gap-1">
                                                    {sectionOptions && sectionOptions[section]?.name.split('_')
                                                        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                                        .join(' ')}
                                                    <button
                                                        onClick={() => removeSection(section)}
                                                        className="ml-1 hover:text-destructive"
                                                    >
                                                        <X className="h-3 w-3" />
                                                    </button>
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={newFolderEnabled}
                                        onCheckedChange={setNewFolderEnabled}
                                    />
                                    <label>Enabled</label>
                                </div>
                                <Button onClick={handleCreateFolder} className="w-full">
                                    Create Folder
                                </Button>
                                <Button variant="outline" onClick={handleCancelClick}>
                                    Cancel
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="flex gap-2">
                    <div className="relative flex-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search folders..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-8"
                        />
                    </div>
                </div>

                <Card>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Description</TableHead>
                                <TableHead>Section</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {folders?.map((folder) => (
                                <TableRow key={folder.id}>
                                    <TableCell>{folder.name}</TableCell>
                                    <TableCell>{folder.description || "—"}</TableCell>
                                    <TableCell>
                                        {sectionOptions && folder.sections_connected && folder.sections_connected.length > 0 
                                            ? folder.sections_connected
                                                .map(section => sectionOptions[section]?.name)
                                                .filter(Boolean)
                                                .map(name => name.split('_')
                                                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                                    .join(' '))
                                                .join(', ')
                                            : "—"
                                        }
                                    </TableCell>
                                    <TableCell>{folder.is_enabled ? 'Enabled' : 'Disabled'}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(folder)}
                                        >
                                            Edit
                                        </Button>
                                        <Button
                                            variant="destructive"
                                            size="sm"
                                            onClick={() => setDeletingFolder(folder)}
                                        >
                                            Delete
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </Card>

                <Dialog open={!!editingFolder} onOpenChange={(open) => !open && setEditingFolder(null)}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Edit Folder</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div className="space-y-2">
                                <label>Name</label>
                                <Input
                                    value={newFolderName}
                                    onChange={(e) => setNewFolderName(e.target.value)}
                                    placeholder="Enter folder name"
                                />
                            </div>
                            <div className="space-y-2">
                                <label>Description</label>
                                <Textarea
                                    value={newFolderDescription}
                                    onChange={(e) => setNewFolderDescription(e.target.value)}
                                    placeholder="Enter folder description"
                                />
                            </div>
                            <div className="space-y-2">
                                <label>Section</label>
                                <p className="text-sm text-muted-foreground">
                                    When users upload documents in a specific section (e.g., Medical Treatment), files will be automatically organized in this folder.
                                </p>
                                <div className="space-y-2">
                                    <Select onValueChange={handleSectionSelect}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select sections" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {sectionOptions && Object.entries(sectionOptions).map(([key, value]) => (
                                                <SelectItem 
                                                    key={key} 
                                                    value={key}
                                                    disabled={newFolderSections.includes(key)}
                                                >
                                                    {value.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <div className="flex flex-wrap gap-2">
                                        {newFolderSections.map((section) => (
                                            <Badge key={section} variant="secondary" className="flex items-center gap-1">
                                                {sectionOptions && sectionOptions[section]?.name.split('_')
                                                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                                    .join(' ')}
                                                <button
                                                    onClick={() => removeSection(section)}
                                                    className="ml-1 hover:text-destructive"
                                                >
                                                    <X className="h-3 w-3" />
                                                </button>
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Switch
                                    checked={newFolderEnabled}
                                    onCheckedChange={setNewFolderEnabled}
                                />
                                <label>Enabled</label>
                            </div>
                            <Button onClick={handleUpdateFolder} className="w-full">
                                Update Folder
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>

                <AlertDialog open={!!deletingFolder} onOpenChange={(open) => !open && setDeletingFolder(null)}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                                This action cannot be undone.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                                onClick={handleDeleteFolder}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                                Delete
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleClose}
                onCancel={handleClose}
            />
        </>
    );
} 