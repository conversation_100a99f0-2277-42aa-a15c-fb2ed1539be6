'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { useTemplatesListQuery, useDownloadTemplateMutation, Template } from "@/services/templateManagementService";
import { Loader2 } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CreateTemplateModal } from './CreateTemplateModal';
import { EditTemplateModal } from './EditTemplateModal';
import { DeleteTemplateDialog } from './DeleteTemplateDialog';
import { useDeleteTemplateMutation } from '@/services/templateManagementService';
import { RenderTemplateModal } from './RenderTemplateModal';

export function TemplatesManagement() {
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedContext, setSelectedContext] = useState<string>("all");
    const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
    const [deletingTemplate, setDeletingTemplate] = useState<Template | null>(null);

    // Query templates with filters
    const { data: templates, isLoading } = useTemplatesListQuery({
        context_type: selectedContext !== "all" ? selectedContext : undefined,
        search: searchQuery || undefined,
    });

    // Download mutation
    const downloadMutation = useDownloadTemplateMutation();

    // Add delete mutation
    const deleteMutation = useDeleteTemplateMutation();

    // Handle template download
    const handleDownload = async (template: Template) => {
        try {
            const blob = await downloadMutation.mutateAsync(template.id);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = template.name + '.docx'; // or use template.file_name if available
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Download failed:', error);
        }
    };

    // Handle delete
    const handleDelete = async () => {
        if (!deletingTemplate) return;
        
        try {
            await deleteMutation.mutateAsync(deletingTemplate.id);
            setDeletingTemplate(null);
        } catch (error) {
            console.error('Delete failed:', error);
        }
    };

    return (
        <div className="space-y-6 p-6 bg-white rounded-lg shadow">
            <div className="flex items-center justify-between gap-4 flex-wrap">
                <div className="flex-1 min-w-[200px]">
                    <Input
                        placeholder="Search templates..."
                        className="max-w-sm"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
                <Select 
                    value={selectedContext} 
                    onValueChange={setSelectedContext}
                >
                    <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="All Contexts" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Contexts</SelectItem>
                        <SelectItem value="attorney_lien">Attorney Lien</SelectItem>
                        <SelectItem value="client">Client</SelectItem>
                        <SelectItem value="client_adjuster">Client Adjuster</SelectItem>
                        <SelectItem value="client_employer">Client Employer</SelectItem>
                        <SelectItem value="client_health_insurance">Client Health Insurance</SelectItem>
                        <SelectItem value="client_insurance">Client Insurance</SelectItem>
                        <SelectItem value="court">Court</SelectItem>
                        <SelectItem value="defendant">Defendant</SelectItem>
                        <SelectItem value="defendant_adjuster">Defendant Adjuster</SelectItem>
                        <SelectItem value="defendant_insurance">Defendant Insurance</SelectItem>
                        <SelectItem value="defendant_legal_representation">Defendant Legal Representation</SelectItem>
                        <SelectItem value="expert_witness">Expert Witness</SelectItem>
                        <SelectItem value="health_provider">Health Provider</SelectItem>
                        <SelectItem value="judge">Judge</SelectItem>
                        <SelectItem value="mediator">Mediator</SelectItem>
                        <SelectItem value="misc_lien">Misc Lien</SelectItem>
                        <SelectItem value="other_party">Other Party</SelectItem>
                        <SelectItem value="other_plaintiff">Other Plaintiff</SelectItem>
                        <SelectItem value="police_agency">Police Agency</SelectItem>
                        <SelectItem value="settlement_advance">Settlement Advance</SelectItem>
                        <SelectItem value="sub_out">Sub Out</SelectItem>
                        <SelectItem value="witness">Witness</SelectItem>
                        <SelectItem value="client_correspondence">Client Correspondence</SelectItem>
                        <SelectItem value="litigation">Litigation</SelectItem>
                    </SelectContent>
                </Select>
                <CreateTemplateModal />
            </div>

            <div className="rounded-md border border-gray-200">
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gray-50">
                            <TableHead className="w-[80px] font-semibold">ID</TableHead>
                            <TableHead className="font-semibold">Template Name</TableHead>
                            <TableHead className="font-semibold">Context</TableHead>
                            <TableHead className="font-semibold text-center">Case</TableHead>
                            <TableHead className="font-semibold text-center">Lead</TableHead>
                            <TableHead className="text-right font-semibold">Last Modified</TableHead>
                            <TableHead className="w-[100px] text-right font-semibold">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan={7} className="text-center py-4">
                                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                                </TableCell>
                            </TableRow>
                        ) : !templates || templates.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={7} className="text-center py-4 text-gray-500">
                                    No templates found
                                </TableCell>
                            </TableRow>
                        ) : (
                            templates.map((template) => (
                                <TableRow 
                                    key={template.id}
                                    className="hover:bg-gray-50 transition-colors"
                                >
                                    <TableCell className="font-medium">#{template.id}</TableCell>
                                    <TableCell>{template.name}</TableCell>
                                    <TableCell>
                                        <span className="px-2 py-1 rounded-full text-sm bg-gray-100">
                                            {template.context_type}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-center">
                                        <span className={template.use_in_case ? "text-green-600 font-bold" : "text-red-600 font-bold"}>
                                            {template.use_in_case ? "✓" : "✗"}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-center">
                                        <span className={template.use_in_lead ? "text-green-600 font-bold" : "text-red-600 font-bold"}>
                                            {template.use_in_lead ? "✓" : "✗"}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-right text-gray-600">
                                        {new Date(template.updated_at).toLocaleDateString()}
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                    <svg
                                                        className="h-4 w-4 text-gray-600"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                            d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                                                        />
                                                    </svg>
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                                    <RenderTemplateModal
                                                        templateId={template.id}
                                                        templateName={template.name}
                                                    />
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => handleDownload(template)}>
                                                    Download
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => setEditingTemplate(template)}>
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem 
                                                    onClick={() => setDeletingTemplate(template)}
                                                    className="text-red-600"
                                                >
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>
            </div>

            {editingTemplate && (
                <EditTemplateModal
                    template={editingTemplate}
                    isOpen={!!editingTemplate}
                    onClose={() => setEditingTemplate(null)}
                />
            )}

            {deletingTemplate && (
                <DeleteTemplateDialog
                    isOpen={!!deletingTemplate}
                    isDeleting={deleteMutation.isPending}
                    templateName={deletingTemplate.name}
                    onConfirm={handleDelete}
                    onCancel={() => setDeletingTemplate(null)}
                />
            )}
        </div>
    );
} 