'use client';

import { useState, useEffect } from 'react';
import { useOrganizationCaseStatuses, useCreateOrganizationCaseStatus, useUpdateOrganizationCaseStatus, useDeleteOrganizationCaseStatus, useCreateOrganizationCaseChecklist, useUpdateOrganizationCaseChecklist, useDeleteOrganizationCaseChecklist, useBulkCreateChecklist, useUpdateCaseStatusOrder, useUpdateCaseChecklistOrder, KPIType } from '@/services/organizationService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, Edit2, GripVertical, ChartBar } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import type { OrganizationCaseStatus, OrganizationCaseStatusChecklist, UpdateStatusOrderRequest } from '@/services/organizationService';
import { useQueryClient } from '@tanstack/react-query';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Add KPI type options component
const KPITypeSelect = ({ value, onChange }: { value?: KPIType; onChange: (value: KPIType | undefined) => void }) => {
    // Initialize with "none" if no value is provided
    const [selectedValue, setSelectedValue] = useState<string>(() => {
        if (!value) return "none";
        return Object.values(KPIType).includes(value) ? value : "none";
    });

    // Reset selectedValue when value prop changes
    useEffect(() => {
        setSelectedValue(!value ? "none" : Object.values(KPIType).includes(value) ? value : "none");
    }, [value]);

    const handleChange = (val: string) => {
        setSelectedValue(val);
        // Only pass a valid KPIType value or undefined to the parent
        if (val === "none") {
            onChange(undefined);
        } else if (Object.values(KPIType).includes(val as KPIType)) {
            onChange(val as KPIType);
        } else {
            onChange(undefined);
        }
    };

    return (
        <div className="space-y-2">
            <Label htmlFor="kpi_type">KPI Tracking</Label>
            <input type="hidden" name="kpi_type" value={selectedValue} />
            <Select 
                value={selectedValue} 
                onValueChange={handleChange}
            >
                <SelectTrigger id="kpi_type">
                    <SelectValue placeholder="Select KPI type (optional)" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {Object.values(KPIType).map((type) => (
                        <SelectItem key={type} value={type}>
                            {getKPIDisplayName(type)}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
};

// Add a helper function to format KPI type display name
const getKPIDisplayName = (kpiType: string): string => {
    const displayNames: Record<string, string> = {
        total_cases_in_negotiation: "Cases in Negotiation",
        case_demand_ready: "Demand Ready",
        case_closed: "Cases Closed",
        cases_touched: "Cases Touched",
        treatment_period: "Treatment Period",
        avg_case_age: "Average Case Age",
        notes_entered: "Notes Entered",
        three_plus_providers_count: "3+ Providers",
        total_attorney_fees: "Attorney Fees",
        depositions_taken: "Depositions Taken",
        mediations_arb_trials: "Mediations/Trials",
        hearings: "Hearings"
    };
    return displayNames[kpiType] || kpiType.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
};

// Add a helper function to get KPI badge color
const getKPIBadgeStyle = (kpiType: string): string => {
    const colors: Record<string, string> = {
        total_cases_in_negotiation: "bg-purple-100 text-purple-800",
        case_demand_ready: "bg-blue-100 text-blue-800",
        case_closed: "bg-green-100 text-green-800",
        cases_touched: "bg-yellow-100 text-yellow-800",
        treatment_period: "bg-orange-100 text-orange-800",
        avg_case_age: "bg-red-100 text-red-800",
        notes_entered: "bg-indigo-100 text-indigo-800",
        three_plus_providers_count: "bg-pink-100 text-pink-800",
        total_attorney_fees: "bg-emerald-100 text-emerald-800",
        depositions_taken: "bg-cyan-100 text-cyan-800",
        mediations_arb_trials: "bg-teal-100 text-teal-800",
        hearings: "bg-violet-100 text-violet-800"
    };
    return colors[kpiType] || "bg-gray-100 text-gray-800";
};

// Sortable Status Card Component
function SortableStatusCard({ status, onEdit, onDelete, onSelect, isSelected }: {
    status: OrganizationCaseStatus;
    onEdit: (id: string) => void;
    onDelete: (id: string) => void;
    onSelect: (status: OrganizationCaseStatus) => void;
    isSelected: boolean;
}) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: status.id?.toString() || '' });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    return (
        <Card
            ref={setNodeRef}
            style={style}
            className={`${isSelected ? 'cursor-pointer ring-2 ring-primary' : 'cursor-pointer'} ${isDragging ? 'shadow-lg' : ''}`}
            onClick={() => onSelect(status)}
        >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-2">
                    <div {...attributes} {...listeners} className="cursor-grab">
                        <GripVertical className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div className="flex flex-col gap-2">
                        <CardTitle className="text-sm font-medium">
                            {status.display_name}
                        </CardTitle>
                        {status.kpi_type && (
                            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getKPIBadgeStyle(status.kpi_type)}`}>
                                <ChartBar className="w-3 h-3 mr-1" />
                                {getKPIDisplayName(status.kpi_type)}
                            </div>
                        )}
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation();
                            if (status.id) {
                                onEdit(status.id.toString());
                            }
                        }}
                    >
                        <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation();
                            if (status.id) {
                                onDelete(status.id.toString());
                            }
                        }}
                    >
                        <Trash2 className="w-4 h-4" />
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
                <p className="text-sm text-muted-foreground">{status.description}</p>
                {!status.is_active && (
                    <p className="text-xs text-muted-foreground mt-2 italic">Status is inactive</p>
                )}
            </CardContent>
        </Card>
    );
}

// Sortable Checklist Item Component
function SortableChecklistItem({ item, onEdit, onDelete }: {
    item: OrganizationCaseStatusChecklist;
    onEdit: (id: string) => void;
    onDelete: (id: string) => void;
}) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: item.id?.toString() || '' });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    return (
        <Card
            ref={setNodeRef}
            style={style}
            className={`${isDragging ? 'shadow-lg' : ''}`}
        >
            <CardContent className="flex items-center justify-between p-4">
                <div className="flex items-center gap-4 flex-1">
                    <div {...attributes} {...listeners} className="cursor-grab">
                        <GripVertical className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <div className="flex-1">
                        <div className="flex flex-col gap-2">
                            <p className="font-medium">
                                {item.item_name}
                            </p>
                            {item.kpi_type && (
                                <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium w-fit ${getKPIBadgeStyle(item.kpi_type)}`}>
                                    <ChartBar className="w-3 h-3 mr-1" />
                                    {getKPIDisplayName(item.kpi_type)}
                                </div>
                            )}
                            <p className="text-sm text-muted-foreground">{item.description}</p>
                            {item.is_required && (
                                <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-50 text-red-700 w-fit">
                                    Required
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => item.id && onEdit(item.id.toString())}
                    >
                        <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => item.id && onDelete(item.id.toString())}
                    >
                        <Trash2 className="w-4 h-4" />
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}

export function CaseStatusManagement() {
    const [search, setSearch] = useState('');
    const [selectedStatus, setSelectedStatus] = useState<OrganizationCaseStatus | null>(null);
    const [isAddingStatus, setIsAddingStatus] = useState(false);
    const [isAddingChecklist, setIsAddingChecklist] = useState(false);
    const [editingStatusId, setEditingStatusId] = useState<string | null>(null);
    const [editingChecklistId, setEditingChecklistId] = useState<string | null>(null);
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const { data: statuses, isLoading } = useOrganizationCaseStatuses(search, 'order');
    const createStatus = useCreateOrganizationCaseStatus();
    const { mutateAsync: updateStatusMutation } = useUpdateOrganizationCaseStatus();
    const deleteStatus = useDeleteOrganizationCaseStatus();
    const createChecklist = useCreateOrganizationCaseChecklist();
    const updateChecklist = useUpdateOrganizationCaseChecklist();
    const deleteChecklist = useDeleteOrganizationCaseChecklist();
    const { toast } = useToast();
    const queryClient = useQueryClient();
    const bulkCreateChecklist = useBulkCreateChecklist();
    const updateStatusOrder = useUpdateCaseStatusOrder();
    const updateChecklistOrder = useUpdateCaseChecklistOrder();
    // DnD sensors configuration
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = async (event: DragEndEvent) => {
        const { active, over } = event;
        
        if (!over || active.id === over.id || !statuses) {
            return;
        }

        const oldIndex = statuses.findIndex((status) => status.id?.toString() === active.id);
        const newIndex = statuses.findIndex((status) => status.id?.toString() === over.id);
        
        if (oldIndex === -1 || newIndex === -1) {
            return;
        }

        const reorderedStatuses = arrayMove(statuses, oldIndex, newIndex);
        
        console.log("reorderedStatuses : ", reorderedStatuses);

        const reorderedStatusesWithOrder: UpdateStatusOrderRequest = {
            orders: reorderedStatuses.map((status, index) => ({
                id: status.id as number,
                order: index
            }))
        };

        
        updateStatusOrder.mutateAsync(reorderedStatusesWithOrder);
        // Update UI only for now
        try {
            // Update the UI optimistically
            queryClient.setQueryData(['organization-case-statuses', search, 'order'], reorderedStatuses);
            
            // Comment out backend updates for now
            /* 
            for (let i = 0; i < reorderedStatuses.length; i++) {
                const status = reorderedStatuses[i];
                if (status.id) {
                    await updateStatusMutation({
                        id: status.id.toString(),
                        data: {
                            organization_status: status.id
                        }
                    });
                }
            }
            
            await queryClient.invalidateQueries({ 
                queryKey: ['organization-case-statuses']
            });
            */
        } catch (error) {
            console.error('Error updating status order:', error);
            toast({
                title: "Error",
                description: "Failed to update status order",
                variant: "destructive"
            });
            // Revert the optimistic update on error
            queryClient.invalidateQueries({ 
                queryKey: ['organization-case-statuses']
            });
        }
    };

    const handleCreateStatus = async (formData: FormData, kpiType?: KPIType) => {
        try {
            const newStatus = {
                name: formData.get('name') as string,
                display_name: formData.get('display_name') as string,
                description: formData.get('description') as string || undefined,
                legacy_status: undefined,  // Optional, not including in form
                order: statuses?.length || 0,
                is_active: true,
                ...(kpiType && Object.values(KPIType).includes(kpiType) && { kpi_type: kpiType }) // Only include if valid KPIType
            };
            await createStatus.mutateAsync(newStatus);
            setIsAddingStatus(false);
        } catch (error) {
            console.error('Error creating status:', error);
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to create case status',
                variant: "destructive"
            });
        }
    };

    const handleUpdateStatus = async (id: string) => {
        try {
            const formElement = document.querySelector('form') as HTMLFormElement;
            const formData = new FormData(formElement);
            const status = statuses?.find(s => s.id?.toString() === id);
            if (!status?.id) return;
            
            const kpiType = formData.get('kpi_type') as string;
            const validKpiType = Object.values(KPIType).includes(kpiType as KPIType) ? kpiType as KPIType : undefined;
            
            const updatedStatus = {
                organization_status: status.id,
                name: formData.get('name') as string,
                display_name: formData.get('display_name') as string,
                description: formData.get('description') as string || undefined,
                is_active: formData.get('is_active') === 'on',
                ...(validKpiType && { kpi_type: validKpiType }) // Only include if valid KPIType
            };
            await updateStatusMutation({ id, data: updatedStatus });
            setEditingStatusId(null);
        } catch (error) {
            console.error('Error updating status:', error);
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to update status',
                variant: "destructive"
            });
        }
    };

    const handleDeleteStatus = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this status?')) {
            try {
                await deleteStatus.mutateAsync(id);
                setSelectedStatus(null);
            } catch (error) {
                console.error('Error deleting status:', error);
            }
        }
    };

    const handleCreateChecklist = async (statusId: number, formData: FormData, kpiType?: KPIType) => {
        try {
            const newChecklist = {
                status: statusId,
                item_name: formData.get('item_name') as string,
                description: formData.get('description') as string || undefined,
                is_required: formData.get('is_required') === 'on',
                order: selectedStatus?.checklist_items?.length || 0,
                ...(kpiType && Object.values(KPIType).includes(kpiType) && { kpi_type: kpiType }) // Only include if valid KPIType
            };
            await createChecklist.mutateAsync(newChecklist);
            setIsAddingChecklist(false);
            
            // Refetch the statuses
            await queryClient.invalidateQueries({ 
                queryKey: ['organization-case-statuses']
            });

            // Wait for the refetch to complete and update selected status
            const updatedStatuses = await queryClient.getQueryData<OrganizationCaseStatus[]>(['organization-case-statuses', search, 'order']);
            const updatedStatus = updatedStatuses?.find(status => status.id === statusId);
            if (updatedStatus) {
                setSelectedStatus(updatedStatus);
            }
        } catch (error) {
            console.error('Error creating checklist:', error);
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to create checklist item',
                variant: "destructive"
            });
        }
    };

    const handleUpdateChecklist = async (id: string, data: Partial<OrganizationCaseStatusChecklist>) => {
        try {
            // Only include kpi_type if it's a valid KPIType value
            const { kpi_type, ...restData } = data;
            const hasValidKpiType = kpi_type && Object.values(KPIType).includes(kpi_type as KPIType);
            const dataToUpdate = hasValidKpiType ? data : restData;

            await updateChecklist.mutateAsync({ id, data: dataToUpdate });
            
            // Refetch the statuses
            await queryClient.invalidateQueries({ 
                queryKey: ['organization-case-statuses']
            });

            // Wait for the refetch to complete and update selected status
            if (selectedStatus?.id) {
                const updatedStatuses = await queryClient.getQueryData<OrganizationCaseStatus[]>(['organization-case-statuses', search, 'order']);
                const updatedStatus = updatedStatuses?.find(status => status.id === selectedStatus.id);
                if (updatedStatus) {
                    setSelectedStatus(updatedStatus);
                }
            }
        } catch (error) {
            console.error('Error updating checklist:', error);
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to update checklist item',
                variant: "destructive"
            });
        }
    };

    const handleDeleteChecklist = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this checklist item?')) {
            try {
                await deleteChecklist.mutateAsync(id);
                
                // Refetch the statuses
                await queryClient.invalidateQueries({ 
                    queryKey: ['organization-case-statuses']
                });

                // Wait for the refetch to complete and update selected status
                if (selectedStatus?.id) {
                    const updatedStatuses = await queryClient.getQueryData<OrganizationCaseStatus[]>(['organization-case-statuses', search, 'order']);
                    const updatedStatus = updatedStatuses?.find(status => status.id === selectedStatus.id);
                    if (updatedStatus) {
                        setSelectedStatus(updatedStatus);
                    }
                }
            } catch (error) {
                console.error('Error deleting checklist:', error);
                toast({
                    title: "Error",
                    description: error instanceof Error ? error.message : 'Failed to delete checklist item',
                    variant: "destructive"
                });
            }
        }
    };

    const handleChecklistDragEnd = async (event: DragEndEvent) => {
        console.log("handleChecklistDragEnd : ", event);
        const { active, over } = event;
        
        if (!over || active.id === over.id || !selectedStatus?.checklist_items) {
            return;
        }

        const oldIndex = selectedStatus.checklist_items.findIndex(
            (item) => item.id?.toString() === active.id
        );
        const newIndex = selectedStatus.checklist_items.findIndex(
            (item) => item.id?.toString() === over.id
        );
        
        if (oldIndex === -1 || newIndex === -1) {
            return;
        }

        const reorderedItems = arrayMove(selectedStatus.checklist_items, oldIndex, newIndex);

        console.log("reorderedItems : ", reorderedItems);

        const reorderedItemsWithOrder: UpdateStatusOrderRequest = {
            orders: reorderedItems.map((item, index) => ({
                id: item.id as number,
                order: index
            }))
        };

        updateChecklistOrder.mutateAsync(reorderedItemsWithOrder);
        // Update UI only for now
        try {
            // Update the UI optimistically
            const updatedStatus = {
                ...selectedStatus,
                checklist_items: reorderedItems
            };
            queryClient.setQueryData(
                ['organization-case-statuses', search, 'order'],
                (old: OrganizationCaseStatus[] | undefined) => 
                    old?.map(status => 
                        status.id === selectedStatus.id ? updatedStatus : status
                    )
            );
            setSelectedStatus(updatedStatus);
            
            // Comment out backend updates for now
            /*
            for (let i = 0; i < reorderedItems.length; i++) {
                const item = reorderedItems[i];
                if (item.id) {
                    await updateChecklist.mutateAsync({
                        id: item.id.toString(),
                        data: {
                            order: i,
                            status: selectedStatus.id as number,
                            item_name: item.item_name,
                            description: item.description,
                            is_required: item.is_required
                        }
                    });
                }
            }
            
            await queryClient.invalidateQueries({ 
                queryKey: ['organization-case-statuses']
            });
            */
        } catch (error) {
            console.error('Error updating checklist order:', error);
            toast({
                title: "Error",
                description: "Failed to update checklist order",
                variant: "destructive"
            });
            // Revert the optimistic update on error
            queryClient.invalidateQueries({ 
                queryKey: ['organization-case-statuses']
            });
        }
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>, statusId: number) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            const fileContent = await file.text();
            const jsonData = JSON.parse(fileContent);
            
            // Ensure the data is an array
            const checklists = Array.isArray(jsonData) ? jsonData : [jsonData];
            
            // Add order if not present
            const checklistsWithOrder = checklists.map((item, index) => ({
                ...item,
                order: item.order ?? index
            }));

            await bulkCreateChecklist.mutateAsync({
                status: statusId,
                checklists: checklistsWithOrder
            });

            // Reset the file input
            event.target.value = '';
        } catch (error) {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to process JSON file',
                variant: "destructive"
            });
        }
    };

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = isAddingStatus || isAddingChecklist || editingStatusId || editingChecklistId;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsAddingStatus(true);
            }
        }
    };

    const handleClose = () => {
        setIsAddingStatus(false);
        setEditingStatusId(null);
        setIsAddingChecklist(false);
        setEditingChecklistId(null);
        // Reset form data
        const formElement = document.querySelector('form') as HTMLFormElement;
        if (formElement) {
            formElement.reset();
            // Reset all input values to empty
            formElement.querySelectorAll('input, textarea').forEach((element) => {
                const input = element as HTMLInputElement | HTMLTextAreaElement;
                if (input.type !== 'hidden') {
                    input.value = '';
                    // Also reset the defaultValue to prevent it from retaining old values
                    input.defaultValue = '';
                }
            });
            // Reset KPI type select to "none"
            const kpiTypeInput = formElement.querySelector('input[name="kpi_type"]') as HTMLInputElement;
            if (kpiTypeInput) {
                kpiTypeInput.value = 'none';
            }
            // Reset switches to default state
            formElement.querySelectorAll('[role="switch"]').forEach((element) => {
                const switchElement = element as HTMLButtonElement;
                switchElement.setAttribute('aria-checked', 'false');
            });
            // Reset any Select components
            formElement.querySelectorAll('[role="combobox"]').forEach((element) => {
                const selectElement = element as HTMLElement;
                const textContent = selectElement.querySelector('[class*="SelectValue"]');
                if (textContent) {
                    textContent.textContent = 'Select KPI type (optional)';
                }
            });
        }
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-4">
                <Input
                    placeholder="Search statuses..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="max-w-sm"
                />
                <Button onClick={() => setIsAddingStatus(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Status
                </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Status List with DnD */}
                <div className="space-y-4">
                    <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                    >
                        <SortableContext
                            items={statuses?.map(s => s.id?.toString() || '') || []}
                            strategy={verticalListSortingStrategy}
                        >
                            {statuses?.map((status) => (
                                <SortableStatusCard
                                    key={status.id}
                                    status={status}
                                    onEdit={(id) => setEditingStatusId(id)}
                                    onDelete={handleDeleteStatus}
                                    onSelect={setSelectedStatus}
                                    isSelected={selectedStatus?.id === status.id}
                                />
                            ))}
                        </SortableContext>
                    </DndContext>
                </div>

                {/* Checklist Management with DnD */}
                {selectedStatus && (
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">Checklist Items for {selectedStatus.display_name}</h3>
                            <div className="flex items-center gap-2">
                                <div className="relative">
                                    <Input
                                        type="file"
                                        accept=".json"
                                        className="hidden"
                                        id="checklist-upload"
                                        onChange={(e) => selectedStatus.id && handleFileUpload(e, selectedStatus.id)}
                                    />
                                    <Button
                                        variant="outline"
                                        className="hidden"
                                        onClick={() => document.getElementById('checklist-upload')?.click()}
                                    >
                                        Upload JSON
                                    </Button>
                                </div>
                                <Button onClick={() => {
                                    setIsAddingChecklist(true);
                                    setEditingChecklistId(null);
                                    // Reset form data when adding new item
                                    const formElement = document.querySelector('form') as HTMLFormElement;
                                    if (formElement) {
                                        formElement.reset();
                                        // Reset all input values to empty
                                        formElement.querySelectorAll('input, textarea').forEach((element) => {
                                            const input = element as HTMLInputElement | HTMLTextAreaElement;
                                            if (input.type !== 'hidden') {
                                                input.value = '';
                                                // Also reset the defaultValue to prevent it from retaining old values
                                                input.defaultValue = '';
                                            }
                                        });
                                        // Reset KPI type select to "none"
                                        const kpiTypeInput = formElement.querySelector('input[name="kpi_type"]') as HTMLInputElement;
                                        if (kpiTypeInput) {
                                            kpiTypeInput.value = 'none';
                                        }
                                        // Reset switches to default state
                                        formElement.querySelectorAll('[role="switch"]').forEach((element) => {
                                            const switchElement = element as HTMLButtonElement;
                                            switchElement.setAttribute('aria-checked', 'false');
                                        });
                                        // Reset any Select components
                                        formElement.querySelectorAll('[role="combobox"]').forEach((element) => {
                                            const selectElement = element as HTMLElement;
                                            const textContent = selectElement.querySelector('[class*="SelectValue"]');
                                            if (textContent) {
                                                textContent.textContent = 'Select KPI type (optional)';
                                            }
                                        });
                                    }
                                }}>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add Item
                                </Button>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <DndContext
                                sensors={sensors}
                                collisionDetection={closestCenter}
                                onDragEnd={handleChecklistDragEnd}
                            >
                                <SortableContext
                                    items={selectedStatus.checklist_items?.map(item => item.id?.toString() || '') || []}
                                    strategy={verticalListSortingStrategy}
                                >
                                    {selectedStatus.checklist_items?.map((item) => (
                                        <SortableChecklistItem
                                            key={item.id}
                                            item={item}
                                            onEdit={setEditingChecklistId}
                                            onDelete={handleDeleteChecklist}
                                        />
                                    ))}
                                </SortableContext>
                            </DndContext>
                        </div>
                    </div>
                )}
            </div>

            {/* Add/Edit Status Dialog */}
            <Dialog open={isAddingStatus || !!editingStatusId} onOpenChange={handleOpenChange}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {isAddingStatus ? 'Add New Status' : 'Edit Status'}
                        </DialogTitle>
                    </DialogHeader>
                    <form
                        onSubmit={(e) => {
                            e.preventDefault();
                            const formData = new FormData(e.currentTarget);
                            const kpiType = formData.get('kpi_type') as KPIType | undefined;
                            
                            if (editingStatusId) {
                                handleUpdateStatus(editingStatusId);
                            } else {
                                handleCreateStatus(formData, kpiType);
                            }
                        }}
                        className="space-y-4"
                    >
                        <div className="space-y-2">
                            <Label htmlFor="name">Internal Name</Label>
                            <Input
                                id="name"
                                name="name"
                                defaultValue={editingStatusId ? statuses?.find(s => s.id?.toString() === editingStatusId)?.name : ''}
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="display_name">Display Name</Label>
                            <Input
                                id="display_name"
                                name="display_name"
                                defaultValue={editingStatusId ? statuses?.find(s => s.id?.toString() === editingStatusId)?.display_name : ''}
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                name="description"
                                defaultValue={editingStatusId ? statuses?.find(s => s.id?.toString() === editingStatusId)?.description : ''}
                            />
                        </div>
                        {editingStatusId && (
                            <div className="flex items-center gap-2">
                                <Switch
                                    name="is_active"
                                    defaultChecked={statuses?.find(s => s.id?.toString() === editingStatusId)?.is_active}
                                />
                                <Label>Active</Label>
                            </div>
                        )}
                        <KPITypeSelect
                            value={editingStatusId ? statuses?.find(s => s.id?.toString() === editingStatusId)?.kpi_type : undefined}
                            onChange={() => {
                                // KPI type will be handled in form submission
                            }}
                        />
                        <div className="flex justify-end gap-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleClose}
                            >
                                Cancel
                            </Button>
                            <Button type="submit">
                                {isAddingStatus ? 'Create' : 'Update'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* Add/Edit Checklist Item Dialog */}
            <Dialog open={isAddingChecklist || !!editingChecklistId} onOpenChange={(open) => {
                if (!open) {
                    handleClose();
                } else {
                    if (!editingChecklistId) {
                        setIsAddingChecklist(true);
                        // Reset form state when opening for a new item
                        const formElement = document.querySelector('form') as HTMLFormElement;
                        if (formElement) {
                            formElement.reset();
                            // Reset all input values to empty
                            formElement.querySelectorAll('input, textarea').forEach((element) => {
                                const input = element as HTMLInputElement | HTMLTextAreaElement;
                                if (input.type !== 'hidden') {
                                    input.value = '';
                                    // Also reset the defaultValue to prevent it from retaining old values
                                    input.defaultValue = '';
                                }
                            });
                            // Reset KPI type select to "none"
                            const kpiTypeInput = formElement.querySelector('input[name="kpi_type"]') as HTMLInputElement;
                            if (kpiTypeInput) {
                                kpiTypeInput.value = 'none';
                            }
                            // Reset switches to default state
                            formElement.querySelectorAll('[role="switch"]').forEach((element) => {
                                const switchElement = element as HTMLButtonElement;
                                switchElement.setAttribute('aria-checked', 'false');
                            });
                            // Reset any Select components
                            formElement.querySelectorAll('[role="combobox"]').forEach((element) => {
                                const selectElement = element as HTMLElement;
                                const textContent = selectElement.querySelector('[class*="SelectValue"]');
                                if (textContent) {
                                    textContent.textContent = 'Select KPI type (optional)';
                                }
                            });
                        }
                    }
                }
            }}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {isAddingChecklist ? 'Add Checklist Item' : 'Edit Checklist Item'} for {selectedStatus?.display_name}
                        </DialogTitle>
                    </DialogHeader>
                    <form
                        key={isAddingChecklist ? 'new-item' : editingChecklistId} // Force form re-render with new key
                        onSubmit={(e) => {
                            e.preventDefault();
                            const formData = new FormData(e.currentTarget);
                            const kpiType = formData.get('kpi_type') as KPIType | undefined;
                            
                            if (editingChecklistId) {
                                const updatedChecklist = {
                                    item_name: formData.get('item_name') as string,
                                    description: formData.get('description') as string || undefined,
                                    is_required: formData.get('is_required') === 'on',
                                    kpi_type: kpiType
                                };
                                handleUpdateChecklist(editingChecklistId, updatedChecklist);
                                setEditingChecklistId(null);
                            } else if (selectedStatus?.id) {
                                handleCreateChecklist(selectedStatus.id, formData, kpiType);
                            }
                            handleClose();
                        }}
                        className="space-y-4"
                    >
                        <div className="space-y-2">
                            <Label htmlFor="item_name">Item Name *</Label>
                            <Input 
                                id="item_name" 
                                name="item_name" 
                                placeholder="Enter checklist item name"
                                defaultValue={editingChecklistId ? 
                                    selectedStatus?.checklist_items?.find(item => item.id?.toString() === editingChecklistId)?.item_name : 
                                    ''}
                                required 
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea 
                                id="description" 
                                name="description"
                                placeholder="Enter description (optional)"
                                defaultValue={editingChecklistId ? 
                                    selectedStatus?.checklist_items?.find(item => item.id?.toString() === editingChecklistId)?.description : 
                                    ''}
                            />
                        </div>
                        <div className="flex items-center gap-2">
                            <Switch 
                                id="is_required"
                                name="is_required"
                                defaultChecked={editingChecklistId ? 
                                    selectedStatus?.checklist_items?.find(item => item.id?.toString() === editingChecklistId)?.is_required : 
                                    false} // Set default to false for new items
                            />
                            <Label htmlFor="is_required">Required for status completion</Label>
                        </div>
                        <KPITypeSelect
                            value={editingChecklistId ? 
                                selectedStatus?.checklist_items?.find(item => item.id?.toString() === editingChecklistId)?.kpi_type : 
                                undefined}
                            onChange={() => {
                                // KPI type will be handled in form submission
                            }}
                        />
                        <div className="flex justify-end gap-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleClose}
                            >
                                Cancel
                            </Button>
                            <Button type="submit">
                                {isAddingChecklist ? 'Add Checklist Item' : 'Update Checklist Item'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </div>
    );
}
