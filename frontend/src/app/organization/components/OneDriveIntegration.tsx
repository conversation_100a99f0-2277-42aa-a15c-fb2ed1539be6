'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useConnectOneDrive, useDisconnectOneDrive, useOneDriveStatus, useConnectOneDrivePersonal, useDisconnectOneDrivePersonal, useOneDrivePersonalStatus } from "@/services/organizationService";
import { Cloud, CloudOff } from "lucide-react";

export function OneDriveIntegration() {
    const { data: status, isLoading } = useOneDriveStatus();
    const { data: personalStatus, isLoading: isPersonalLoading } = useOneDrivePersonalStatus();
    const connectOneDrive = useConnectOneDrive();
    const disconnectOneDrive = useDisconnectOneDrive();
    const connectOneDrivePersonal = useConnectOneDrivePersonal();
    const disconnectOneDrivePersonal = useDisconnectOneDrivePersonal();

    if (isLoading || isPersonalLoading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>OneDrive Integration</CardTitle>
                    <CardDescription>Loading integration status...</CardDescription>
                </CardHeader>
            </Card>
        );
    }

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader>
                    <CardTitle>OneDrive Business Integration</CardTitle>
                    <CardDescription>
                        Connect your organization to OneDrive Business for seamless file management
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            {status?.is_connected ? (
                                <>
                                    <Cloud className="h-6 w-6 text-green-500" />
                                    <div>
                                        <p className="font-medium">Connected to OneDrive Business</p>
                                        <p className="text-sm text-gray-500">Your organization is linked to OneDrive Business</p>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <CloudOff className="h-6 w-6 text-gray-400" />
                                    <div>
                                        <p className="font-medium">Not Connected</p>
                                        <p className="text-sm text-gray-500">Connect to enable OneDrive Business integration</p>
                                    </div>
                                </>
                            )}
                        </div>
                        {status?.is_connected ? (
                            <Button
                                variant="destructive"
                                onClick={() => disconnectOneDrive.mutate()}
                                disabled={disconnectOneDrive.isPending}
                            >
                                {disconnectOneDrive.isPending ? "Disconnecting..." : "Disconnect"}
                            </Button>
                        ) : (
                            <Button
                                onClick={() => connectOneDrive.mutate()}
                                disabled={connectOneDrive.isPending}
                            >
                                {connectOneDrive.isPending ? "Connecting..." : "Connect to OneDrive Business"}
                            </Button>
                        )}
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>OneDrive Personal Integration</CardTitle>
                    <CardDescription>
                        Connect your personal OneDrive account for additional file management options
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            {personalStatus?.is_connected ? (
                                <>
                                    <Cloud className="h-6 w-6 text-green-500" />
                                    <div>
                                        <p className="font-medium">Connected to OneDrive Personal</p>
                                        <p className="text-sm text-gray-500">Your personal OneDrive is linked</p>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <CloudOff className="h-6 w-6 text-gray-400" />
                                    <div>
                                        <p className="font-medium">Not Connected</p>
                                        <p className="text-sm text-gray-500">Connect to enable OneDrive Personal integration</p>
                                    </div>
                                </>
                            )}
                        </div>
                        {personalStatus?.is_connected ? (
                            <Button
                                variant="destructive"
                                onClick={() => disconnectOneDrivePersonal.mutate()}
                                disabled={disconnectOneDrivePersonal.isPending}
                            >
                                {disconnectOneDrivePersonal.isPending ? "Disconnecting..." : "Disconnect"}
                            </Button>
                        ) : (
                            <Button
                                onClick={() => connectOneDrivePersonal.mutate()}
                                disabled={connectOneDrivePersonal.isPending}
                            >
                                {connectOneDrivePersonal.isPending ? "Connecting..." : "Connect to OneDrive Personal"}
                            </Button>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
} 