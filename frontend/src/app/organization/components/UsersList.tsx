'use client';

import { useState, useCallback, useEffect } from 'react';
import { useOrganizationUsers, useRemoveOrganizationUser, useUserTags, useUpdateUserTags, useTerminateOrganizationUser } from '@/services/organizationService';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { UserX } from "lucide-react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MultiSelect } from "@/components/ui/multi-select";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import debounce from 'lodash/debounce';

export function UsersList() {
    const [search, setSearch] = useState('');
    const [userToRemove, setUserToRemove] = useState<number | null>(null);
    const [transferToUserId, setTransferToUserId] = useState<number | null>(null);
    const { data: users = [], isLoading } = useOrganizationUsers();
    const { data: tags = [] } = useUserTags();
    const removeUser = useRemoveOrganizationUser();
    const terminateUser = useTerminateOrganizationUser();
    const updateUserTags = useUpdateUserTags();

    // Create a debounced version of the update function
    const debouncedUpdateTags = useCallback(
        debounce(async (userId: number, selectedTags: string[]) => {
            try {
                await updateUserTags.mutateAsync({
                    userId,
                    data: { tag_ids: selectedTags.map(id => parseInt(id)) }
                });
            } catch (error) {
                console.error('Failed to update user tags:', error);
            }
        }, 2000), // Wait 2 seconds after last change before making API call
        [updateUserTags]
    );

    // Clean up the debounced function on component unmount
    useEffect(() => {
        return () => {
            debouncedUpdateTags.cancel();
        };
    }, [debouncedUpdateTags]);

    // Replace the existing handleUpdateTags with this version
    const handleUpdateTags = (userId: number, selectedTags: string[]) => {
        debouncedUpdateTags(userId, selectedTags);
    };

    const handleRemoveUser = async () => {
        if (userToRemove === null) return;

        try {
            if (transferToUserId) {
                await terminateUser.mutateAsync({
                    user_id: userToRemove,
                    transfer_to_user_id: transferToUserId
                });
            } else {
                await removeUser.mutateAsync(userToRemove);
            }
            setUserToRemove(null);
            setTransferToUserId(null);
        } catch (error) {
            console.error('Failed to remove user:', error);
        }
    };

    const filteredUsers = users.filter(user => {
        const searchLower = search.toLowerCase();
        const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim();
        return fullName.toLowerCase().includes(searchLower) ||
            user.email.toLowerCase().includes(searchLower);
    });

    // Transform tags for MultiSelect options
    const tagOptions = tags.map(tag => ({
        label: tag.name,
        value: tag.id?.toString() || '',
    }));

    if (isLoading) {
        return <div>Loading users...</div>;
    }

    if (users.length === 0) {
        return (
            <div className="text-center py-6 text-gray-500">
                No users found
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center space-x-2">
                <Input
                    placeholder="Search users..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            <div className="bg-white rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>System Role</TableHead>
                            <TableHead>Organization Role</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredUsers.map((user) => (
                            <TableRow key={user.id}>
                                <TableCell className="font-medium">
                                    {user.first_name || user.last_name
                                        ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                                        : '—'}
                                </TableCell>
                                <TableCell>{user.email}</TableCell>
                                <TableCell>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${user.role === 'admin'
                                            ? 'bg-blue-100 text-[#060216]-700'
                                            : 'bg-gray-100 text-gray-700'
                                        }`}>
                                        {user.role}
                                    </span>
                                </TableCell>
                                <TableCell>
                                    <MultiSelect
                                        options={tagOptions}
                                        defaultValue={user.tags.map(tag => tag.id?.toString() || '')}
                                        onValueChange={(values) => handleUpdateTags(user.id, values)}
                                        placeholder="Select roles"
                                        className="min-w-[200px]"
                                    />
                                </TableCell>
                                <TableCell className="text-right">
                                    {user.role !== 'admin' && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setUserToRemove(user.id)}
                                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                        >
                                            <UserX className="w-4 h-4 mr-2" />
                                            Remove
                                        </Button>
                                    )}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <AlertDialog open={userToRemove !== null} onOpenChange={() => setUserToRemove(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Remove User</AlertDialogTitle>
                        <AlertDialogDescription className="space-y-4">
                            <p>Are you sure you want to remove this user from the organization?
                                This action cannot be undone.</p>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">
                                    Transfer user&apos;s data to (optional):
                                </label>
                                <Select
                                    value={transferToUserId?.toString() || ''}
                                    onValueChange={(value) => setTransferToUserId(Number(value))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a user" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {users
                                            .filter(u => u.id !== userToRemove)
                                            .map(user => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.first_name || user.last_name
                                                        ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                                                        : user.email}
                                                </SelectItem>
                                            ))
                                        }
                                    </SelectContent>
                                </Select>
                            </div>
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={() => {
                            setUserToRemove(null);
                            setTransferToUserId(null);
                        }}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleRemoveUser}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            Remove
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}