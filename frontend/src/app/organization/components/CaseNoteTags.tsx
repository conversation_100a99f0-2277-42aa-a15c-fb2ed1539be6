import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    useCaseNoteTags,
    useCreateCaseNoteTag,
    useUpdateCaseNoteTag,
    useDeleteCaseNoteTag
} from '@/services/organizationService';
import { CaseNoteTag } from '@/services/organizationService';
import { toast } from '@/hooks/use-toast';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import RichTextEditor from "@/components/ui/RichTextEditor";



export function CaseNoteTags() {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [editingTag, setEditingTag] = useState<CaseNoteTag | null>(null);
    const [newTagName, setNewTagName] = useState('');
    const [newTagDescription, setNewTagDescription] = useState("");
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);

    const { data: tags, isLoading } = useCaseNoteTags();
    const { mutate: createTag } = useCreateCaseNoteTag();
    const { mutate: updateTag } = useUpdateCaseNoteTag();
    const { mutate: deleteTag } = useDeleteCaseNoteTag();

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = newTagName !== '' || newTagDescription !== '';
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsAddDialogOpen(true);
            }
        }
    };

    const handleClose = () => {
        setIsAddDialogOpen(false);
        setNewTagName('');
        setNewTagDescription("");
    };

    const handleDiscardChanges = () => {
        setShowUnsavedAlert(false);
        handleClose();
    };

    const handleContinueEditing = () => {
        setShowUnsavedAlert(false);
    };

    const handleCreateTag = () => {
        if (!newTagName.trim()) {
            toast({
                title: "Error",
                description: "Tag name is required",
                variant: "destructive",
            });
            return;
        }

        createTag({
            name: newTagName.trim(),
            description: newTagDescription || "",
            is_default: false,
        }, {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                setNewTagName('');
                setNewTagDescription("");
            }
        });
    };

    const handleUpdateTag = () => {
        if (!editingTag || !newTagName.trim()) {
            toast({
                title: "Error",
                description: "Tag name is required",
                variant: "destructive",
            });
            return;
        }

        updateTag({
            id: editingTag.id!,
            data: {
                name: newTagName.trim(),
                description: newTagDescription || "",
            }
        }, {
            onSuccess: () => {
                setEditingTag(null);
                setNewTagName('');
                setNewTagDescription("");
            }
        });
    };

    const handleDeleteTag = (tag: CaseNoteTag) => {
        deleteTag(tag.id!);
    };

    const openEditDialog = (tag: CaseNoteTag) => {
        setEditingTag(tag);
        setNewTagName(tag.name);
        setNewTagDescription("");
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    return (
        <>
            <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Case Note Tags</h3>
                    <Dialog open={isAddDialogOpen} onOpenChange={handleOpenChange}>
                        <DialogTrigger asChild>
                            <Button>Add New Tag</Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Create New Tag</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <label>Name</label>
                                    <Input
                                        value={newTagName}
                                        onChange={(e) => setNewTagName(e.target.value)}
                                        placeholder="Enter tag name"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label>Description</label>
                                    <RichTextEditor
                                        value={newTagDescription || ""}
                                        onChange={(value) => {
                                            setNewTagDescription(value);
                                        }}
                                        placeholder="Enter tag description (optional)"
                                    />
                                </div>
                                <Button onClick={handleCreateTag} className="w-full">
                                    Create Tag
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                <Card>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Description</TableHead>
                                <TableHead>Default</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {tags?.map((tag) => (
                                <TableRow key={tag.id}>
                                    <TableCell>{tag.name}</TableCell>
                                    <TableCell>{tag.description || "—"}</TableCell>
                                    <TableCell>{tag.is_default ? 'Yes' : 'No'}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(tag)}
                                            disabled={tag.is_default}
                                        >
                                            Edit
                                        </Button>
                                        <Button
                                            variant="destructive"
                                            size="sm"
                                            onClick={() => handleDeleteTag(tag)}
                                            disabled={tag.is_default}
                                        >
                                            Delete
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </Card>

                {/* Edit Dialog */}
                <Dialog open={!!editingTag} onOpenChange={(open) => !open && setEditingTag(null)}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Edit Tag</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div className="space-y-2">
                                <label>Name</label>
                                <Input
                                    value={newTagName}
                                    onChange={(e) => setNewTagName(e.target.value)}
                                    placeholder="Enter tag name"
                                />
                            </div>
                            <div className="space-y-2">
                                <label>Description</label>
                                <RichTextEditor
                                    value={newTagDescription || ""}
                                    onChange={(value) => {
                                        setNewTagDescription(value);
                                    }}
                                    placeholder="Enter tag description (optional)"
                                />
                            </div>
                            <Button onClick={handleUpdateTag} className="w-full">
                                Update Tag
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>

            <UnsavedChangesAlert
                open={showUnsavedAlert}
                onOpenChange={setShowUnsavedAlert}
                onConfirm={handleDiscardChanges}
                onCancel={handleContinueEditing}
            />
        </>
    );
} 