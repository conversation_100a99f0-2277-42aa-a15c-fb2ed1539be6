import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import {
    Di<PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { 
    useUserTags, 
    useCreateUserTag, 
    useUpdateUserTag, 
    useDeleteUserTag 
} from '@/services/organizationService';
import { UserTag } from '@/services/organizationService';
import { toast } from '@/hooks/use-toast';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert";
import { 
    PlatformSection, 
    AdminSubsection,
    getPlatformSectionsWithDisplayNames, 
    getSubsectionsWithDisplayNames,
    getAdminSectionsWithDisplayNames,
    getDisplayName
} from '@/type/platformSections';
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from "@/components/ui/hover-card";
import { ChevronDown, ChevronRight } from "lucide-react";

export function RoleManagement() {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [editingRole, setEditingRole] = useState<UserTag | null>(null);
    const [newRoleName, setNewRoleName] = useState('');
    const [newRoleDescription, setNewRoleDescription] = useState('');
    const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
    const [expandedRoles, setExpandedRoles] = useState<number[]>([]);
    
    // New state for platform sections
    const [selectedPlatformSections, setSelectedPlatformSections] = useState<PlatformSection[]>([]);
    const [selectedPlatformSubsections, setSelectedPlatformSubsections] = useState<Record<string, string[]>>({});
    const [selectedAdminSections, setSelectedAdminSections] = useState<AdminSubsection[]>([]);

    const { data: roles, isLoading } = useUserTags();
    const { mutate: createRole } = useCreateUserTag();
    const { mutate: updateRole } = useUpdateUserTag();
    const { mutate: deleteRole } = useDeleteUserTag();

    const handleCreateRole = () => {
        if (!newRoleName.trim()) {
            toast({
                title: "Error",
                description: "Role name is required",
                variant: "destructive",
            });
            return;
        }

        createRole({
            name: newRoleName.trim(),
            description: newRoleDescription.trim(),
            platform_sections: selectedPlatformSections,
            platform_subsections: selectedPlatformSubsections,
            admin_sections: selectedAdminSections
        }, {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                resetForm();
            }
        });
    };

    const handleUpdateRole = () => {
        if (!editingRole || !newRoleName.trim()) {
            toast({
                title: "Error",
                description: "Role name is required",
                variant: "destructive",
            });
            return;
        }

        updateRole({
            id: editingRole.id!,
            data: {
                name: newRoleName.trim(),
                description: newRoleDescription.trim(),
                platform_sections: selectedPlatformSections,
                platform_subsections: selectedPlatformSubsections,
                admin_sections: selectedAdminSections
            }
        }, {
            onSuccess: () => {
                setEditingRole(null);
                resetForm();
            }
        });
    };

    const handleDeleteRole = (role: UserTag) => {
        if (window.confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
            deleteRole(role.id!);
        }
    };

    const openEditDialog = (role: UserTag) => {
        setEditingRole(role);
        setNewRoleName(role.name);
        setNewRoleDescription(role.description || '');
        setSelectedPlatformSections(role.platform_sections as PlatformSection[] || []);
        setSelectedPlatformSubsections(role.platform_subsections || {});
        setSelectedAdminSections(role.admin_sections as AdminSubsection[] || []);
    };

    const handleOpenChange = (newOpen: boolean) => {
        const isDirty = newRoleName !== '' || newRoleDescription !== '' || 
                        selectedPlatformSections.length > 0 || 
                        Object.keys(selectedPlatformSubsections).length > 0 || 
                        selectedAdminSections.length > 0;
        if (!newOpen && isDirty) {
            setShowUnsavedAlert(true);
        } else {
            if (!newOpen) {
                handleClose();
            } else {
                setIsAddDialogOpen(true);
            }
        }
    };

    const handleClose = () => {
        setIsAddDialogOpen(false);
        resetForm();
    };

    const resetForm = () => {
        setNewRoleName('');
        setNewRoleDescription('');
        setSelectedPlatformSections([]);
        setSelectedPlatformSubsections({});
        setSelectedAdminSections([]);
    };

    const handleCancelClick = () => {
        const isDirty = newRoleName !== '' || newRoleDescription !== '' || 
                        selectedPlatformSections.length > 0 || 
                        Object.keys(selectedPlatformSubsections).length > 0 || 
                        selectedAdminSections.length > 0;
        if (isDirty) {
            setShowUnsavedAlert(true);
        } else {
            handleClose();
        }
    };

    const togglePlatformSection = (section: string) => {
        setSelectedPlatformSections(prev => {
            if (prev.includes(section as PlatformSection)) {
                // Remove section and its subsections
                const newSubsections = { ...selectedPlatformSubsections };
                delete newSubsections[section];
                setSelectedPlatformSubsections(newSubsections);
                return prev.filter(s => s !== section);
            } else {
                return [...prev, section as PlatformSection];
            }
        });
    };

    const togglePlatformSubsection = (section: string, subsection: string) => {
        setSelectedPlatformSubsections(prev => {
            const sectionSubsections = prev[section] || [];
            const newSubsections = { ...prev };
            
            if (sectionSubsections.includes(subsection)) {
                newSubsections[section] = sectionSubsections.filter(s => s !== subsection);
            } else {
                newSubsections[section] = [...sectionSubsections, subsection];
            }
            
            return newSubsections;
        });
    };

    const toggleAdminSection = (section: string) => {
        setSelectedAdminSections(prev => {
            if (prev.includes(section as AdminSubsection)) {
                return prev.filter(s => s !== section);
            } else {
                return [...prev, section as AdminSubsection];
            }
        });
    };

    const toggleExpandedRole = (roleId: number) => {
        setExpandedRoles(prev => {
            if (prev.includes(roleId)) {
                return prev.filter(id => id !== roleId);
            } else {
                return [...prev, roleId];
            }
        });
    };

    const getPlatformSectionsDisplay = (role: UserTag) => {
        if (!role.platform_sections || role.platform_sections.length === 0) {
            return <Badge variant="outline" className="text-muted-foreground">No access</Badge>;
        }

        const sections = role.platform_sections as PlatformSection[];
        const displayNames = sections.map(section => getDisplayName(section));
        
        return (
            <div className="flex flex-wrap gap-1">
                {displayNames.map((name, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                        {name}
                    </Badge>
                ))}
            </div>
        );
    };

    const getAdminSectionsDisplay = (role: UserTag) => {
        if (!role.admin_sections || role.admin_sections.length === 0) {
            return <Badge variant="outline" className="text-muted-foreground">No access</Badge>;
        }

        const sections = role.admin_sections as AdminSubsection[];
        const displayNames = sections.map(section => getDisplayName(section));
        
        return (
            <div className="flex flex-wrap gap-1">
                {displayNames.map((name, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                        {name}
                    </Badge>
                ))}
            </div>
        );
    };

    const getDetailedAccessInfo = (role: UserTag) => {
        const platformSections = role.platform_sections as PlatformSection[] || [];
        const platformSubsections = role.platform_subsections || {};
        const adminSections = role.admin_sections as AdminSubsection[] || [];
        
        return (
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Platform Access</h4>
                    {platformSections.length === 0 ? (
                        <p className="text-sm text-muted-foreground">No platform access</p>
                    ) : (
                        <div className="space-y-2">
                            {platformSections.map(section => {
                                const sectionName = getDisplayName(section);
                                const subsections = platformSubsections[section] || [];
                                
                                return (
                                    <div key={section} className="border rounded-md p-2">
                                        <div className="font-medium">{sectionName}</div>
                                        {subsections.length > 0 ? (
                                            <div className="ml-4 mt-1">
                                                <div className="text-xs text-muted-foreground mb-1">Subsections:</div>
                                                <div className="flex flex-wrap gap-1">
                                                    {subsections.map(subsection => (
                                                        <Badge key={subsection} variant="outline" className="text-xs">
                                                            {getDisplayName(subsection)}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="text-xs text-muted-foreground ml-4 mt-1">
                                                Full access to all subsections
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>
                
                <div>
                    <h4 className="font-medium mb-2">Admin Access</h4>
                    {adminSections.length === 0 ? (
                        <p className="text-sm text-muted-foreground">No admin access</p>
                    ) : (
                        <div className="flex flex-wrap gap-1">
                            {adminSections.map(section => (
                                <Badge key={section} variant="secondary" className="text-xs">
                                    {getDisplayName(section)}
                                </Badge>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        );
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    return (
        <>
            <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">User Roles</h3>
                    <Dialog open={isAddDialogOpen} onOpenChange={handleOpenChange}>
                        <DialogTrigger asChild>
                            <Button>Add New Role</Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                            <DialogHeader>
                                <DialogTitle>Create New Role</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <label>Name</label>
                                    <Input
                                        value={newRoleName}
                                        onChange={(e) => setNewRoleName(e.target.value)}
                                        placeholder="Enter role name"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label>Description</label>
                                    <Textarea
                                        value={newRoleDescription}
                                        onChange={(e) => setNewRoleDescription(e.target.value)}
                                        placeholder="Enter role description (optional)"
                                    />
                                </div>
                                
                                <Tabs defaultValue="platform">
                                    <TabsList className="grid w-full grid-cols-2">
                                        <TabsTrigger value="platform">Platform Access</TabsTrigger>
                                        <TabsTrigger value="admin">Admin Access</TabsTrigger>
                                    </TabsList>
                                    <TabsContent value="platform" className="space-y-4">
                                        <div className="space-y-2">
                                            <h4 className="font-medium">Platform Sections</h4>
                                            <ScrollArea className="h-[200px] border rounded-md p-4">
                                                <div className="space-y-2">
                                                    {getPlatformSectionsWithDisplayNames().map((section) => (
                                                        <div key={section.value} className="space-y-2">
                                                            <div className="flex items-center space-x-2">
                                                                <Checkbox 
                                                                    id={`section-${section.value}`}
                                                                    checked={selectedPlatformSections.includes(section.value as PlatformSection)}
                                                                    onCheckedChange={() => togglePlatformSection(section.value)}
                                                                />
                                                                <Label htmlFor={`section-${section.value}`}>{section.label}</Label>
                                                            </div>
                                                            
                                                            {selectedPlatformSections.includes(section.value as PlatformSection) && (
                                                                <div className="ml-6 space-y-2">
                                                                    {getSubsectionsWithDisplayNames(section.value as PlatformSection).map((subsection) => (
                                                                        <div key={subsection.value} className="flex items-center space-x-2">
                                                                            <Checkbox 
                                                                                id={`subsection-${section.value}-${subsection.value}`}
                                                                                checked={selectedPlatformSubsections[section.value]?.includes(subsection.value) || false}
                                                                                onCheckedChange={() => togglePlatformSubsection(section.value, subsection.value)}
                                                                            />
                                                                            <Label htmlFor={`subsection-${section.value}-${subsection.value}`}>{subsection.label}</Label>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                            <Separator className="my-2" />
                                                        </div>
                                                    ))}
                                                </div>
                                            </ScrollArea>
                                        </div>
                                    </TabsContent>
                                    <TabsContent value="admin" className="space-y-4">
                                        <div className="space-y-2">
                                            <h4 className="font-medium">Admin Sections</h4>
                                            <ScrollArea className="h-[200px] border rounded-md p-4">
                                                <div className="space-y-2">
                                                    {getAdminSectionsWithDisplayNames().map((section) => (
                                                        <div key={section.value} className="flex items-center space-x-2">
                                                            <Checkbox 
                                                                id={`admin-${section.value}`}
                                                                checked={selectedAdminSections.includes(section.value as AdminSubsection)}
                                                                onCheckedChange={() => toggleAdminSection(section.value)}
                                                            />
                                                            <Label htmlFor={`admin-${section.value}`}>{section.label}</Label>
                                                        </div>
                                                    ))}
                                                </div>
                                            </ScrollArea>
                                        </div>
                                    </TabsContent>
                                </Tabs>
                                
                                <div className="flex space-x-2">
                                    <Button onClick={handleCreateRole} className="w-full">
                                        Create Role
                                    </Button>
                                    <Button variant="outline" onClick={handleCancelClick}>
                                        Cancel
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                <Card>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[30px]"></TableHead>
                                <TableHead>Name</TableHead>
                                <TableHead>Description</TableHead>
                                <TableHead>Platform Access</TableHead>
                                <TableHead>Admin Access</TableHead>
                                <TableHead>Default</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {roles?.map((role) => (
                                <>
                                    <TableRow key={role.id} className="cursor-pointer" onClick={() => toggleExpandedRole(role.id!)}>
                                        <TableCell>
                                            {expandedRoles.includes(role.id!) ? (
                                                <ChevronDown className="h-4 w-4" />
                                            ) : (
                                                <ChevronRight className="h-4 w-4" />
                                            )}
                                        </TableCell>
                                        <TableCell className="font-medium">{role.name}</TableCell>
                                        <TableCell>{role.description || "—"}</TableCell>
                                        <TableCell>
                                            <HoverCard>
                                                <HoverCardTrigger asChild>
                                                    <div className="cursor-pointer">
                                                        {getPlatformSectionsDisplay(role)}
                                                    </div>
                                                </HoverCardTrigger>
                                                <HoverCardContent className="w-80">
                                                    <div className="space-y-2">
                                                        <h4 className="font-medium">Platform Access Details</h4>
                                                        <div className="text-sm">
                                                            {role.platform_sections?.length 
                                                                ? `${role.platform_sections.length} sections` 
                                                                : "No platform access"}
                                                        </div>
                                                    </div>
                                                </HoverCardContent>
                                            </HoverCard>
                                        </TableCell>
                                        <TableCell>
                                            <HoverCard>
                                                <HoverCardTrigger asChild>
                                                    <div className="cursor-pointer">
                                                        {getAdminSectionsDisplay(role)}
                                                    </div>
                                                </HoverCardTrigger>
                                                <HoverCardContent className="w-80">
                                                    <div className="space-y-2">
                                                        <h4 className="font-medium">Admin Access Details</h4>
                                                        <div className="text-sm">
                                                            {role.admin_sections?.length 
                                                                ? `${role.admin_sections.length} sections` 
                                                                : "No admin access"}
                                                        </div>
                                                    </div>
                                                </HoverCardContent>
                                            </HoverCard>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant={role.is_default ? "default" : "outline"}>
                                                {role.is_default ? 'Yes' : 'No'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell className="text-right space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    openEditDialog(role);
                                                }}
                                            >
                                                Edit
                                            </Button>
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteRole(role);
                                                }}
                                            >
                                                Delete
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                    {expandedRoles.includes(role.id!) && (
                                        <TableRow>
                                            <TableCell colSpan={7} className="bg-muted/50">
                                                <div className="p-4">
                                                    {getDetailedAccessInfo(role)}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </>
                            ))}
                        </TableBody>
                    </Table>
                </Card>

                {/* Edit Dialog */}
                <Dialog open={!!editingRole} onOpenChange={(open) => !open && setEditingRole(null)}>
                    <DialogContent className="max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>Edit Role</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div className="space-y-2">
                                <label>Name</label>
                                <Input
                                    value={newRoleName}
                                    onChange={(e) => setNewRoleName(e.target.value)}
                                    placeholder="Enter role name"
                                />
                            </div>
                            <div className="space-y-2">
                                <label>Description</label>
                                <Textarea
                                    value={newRoleDescription}
                                    onChange={(e) => setNewRoleDescription(e.target.value)}
                                    placeholder="Enter role description (optional)"
                                />
                            </div>
                            
                            <Tabs defaultValue="platform">
                                <TabsList className="grid w-full grid-cols-2">
                                    <TabsTrigger value="platform">Platform Access</TabsTrigger>
                                    <TabsTrigger value="admin">Admin Access</TabsTrigger>
                                </TabsList>
                                <TabsContent value="platform" className="space-y-4">
                                    <div className="space-y-2">
                                        <h4 className="font-medium">Platform Sections</h4>
                                        <ScrollArea className="h-[200px] border rounded-md p-4">
                                            <div className="space-y-2">
                                                {getPlatformSectionsWithDisplayNames().map((section) => (
                                                    <div key={section.value} className="space-y-2">
                                                        <div className="flex items-center space-x-2">
                                                            <Checkbox 
                                                                id={`edit-section-${section.value}`}
                                                                checked={selectedPlatformSections.includes(section.value as PlatformSection)}
                                                                onCheckedChange={() => togglePlatformSection(section.value)}
                                                            />
                                                            <Label htmlFor={`edit-section-${section.value}`}>{section.label}</Label>
                                                        </div>
                                                        
                                                        {selectedPlatformSections.includes(section.value as PlatformSection) && (
                                                            <div className="ml-6 space-y-2">
                                                                {getSubsectionsWithDisplayNames(section.value as PlatformSection).map((subsection) => (
                                                                    <div key={subsection.value} className="flex items-center space-x-2">
                                                                        <Checkbox 
                                                                            id={`edit-subsection-${section.value}-${subsection.value}`}
                                                                            checked={selectedPlatformSubsections[section.value]?.includes(subsection.value) || false}
                                                                            onCheckedChange={() => togglePlatformSubsection(section.value, subsection.value)}
                                                                        />
                                                                        <Label htmlFor={`edit-subsection-${section.value}-${subsection.value}`}>{subsection.label}</Label>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                        <Separator className="my-2" />
                                                    </div>
                                                ))}
                                            </div>
                                        </ScrollArea>
                                    </div>
                                </TabsContent>
                                <TabsContent value="admin" className="space-y-4">
                                    <div className="space-y-2">
                                        <h4 className="font-medium">Admin Sections</h4>
                                        <ScrollArea className="h-[200px] border rounded-md p-4">
                                            <div className="space-y-2">
                                                {getAdminSectionsWithDisplayNames().map((section) => (
                                                    <div key={section.value} className="flex items-center space-x-2">
                                                        <Checkbox 
                                                            id={`edit-admin-${section.value}`}
                                                            checked={selectedAdminSections.includes(section.value as AdminSubsection)}
                                                            onCheckedChange={() => toggleAdminSection(section.value)}
                                                        />
                                                        <Label htmlFor={`edit-admin-${section.value}`}>{section.label}</Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </ScrollArea>
                                    </div>
                                </TabsContent>
                            </Tabs>
                            
                            <Button onClick={handleUpdateRole} className="w-full">
                                Update Role
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>

                <UnsavedChangesAlert
                    open={showUnsavedAlert}
                    onOpenChange={setShowUnsavedAlert}
                    onConfirm={handleClose}
                    onCancel={handleClose}
                />
            </div>
        </>
    );
}
