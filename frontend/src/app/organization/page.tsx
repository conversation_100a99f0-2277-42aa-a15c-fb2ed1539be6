'use client';

import { OrganizationDetails } from './components/OrganizationDetails';
import { InviteUserForm } from './components/InviteUserForm';
import { UsersList } from './components/UsersList';
import { PendingInvites } from './components/PendingInvites';
import { CaseStatusManagement } from './components/CaseStatusManagement';
import { OneDriveIntegration } from './components/OneDriveIntegration';
import { CaseNoteTags } from './components/CaseNoteTags';
import { RoleManagement } from './components/RoleManagement';
import { TemplatesManagement } from './components/TemplatesManagement';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { IntakeSourceCustomization } from './components/IntakeSourceCustomization';
import { FolderManagement } from './components/FolderManagement';
import { KPIGoalManagement } from './components/KPIGoalManagement';
import { KPIManagement } from './components/KPIManagement';

const queryClient = new QueryClient();

export default function OrganizationPage() {
    return (
        <QueryClientProvider client={queryClient}>
            <div className="p-8 space-y-8">
                <div>
                    <h1 className="text-2xl font-bold mb-2">Organization Management</h1>
                    <p className="text-gray-500">
                        Manage your organization settings, users, and invitations
                    </p>
                </div>

                <OrganizationDetails />

                <Tabs defaultValue="users" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="users">Users</TabsTrigger>
                        <TabsTrigger value="pending">Pending Invites</TabsTrigger>
                        <TabsTrigger value="case-statuses">Case Statuses</TabsTrigger>
                        <TabsTrigger value="integrations">Integrations</TabsTrigger>
                        <TabsTrigger value="tags">Tags</TabsTrigger>
                        <TabsTrigger value="roles">Organization Roles management</TabsTrigger>
                        <TabsTrigger value="templates">Templates</TabsTrigger>
                        <TabsTrigger value="sources">Intake Sources</TabsTrigger>
                        <TabsTrigger value="folders">Folders</TabsTrigger>
                        <TabsTrigger value="kpi-goals">KPI Goals</TabsTrigger>
                        <TabsTrigger value="kpi-analytics">KPI Analytics</TabsTrigger>
                    </TabsList>
                    <TabsContent value="users">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Users & Invitations</h2>
                            <InviteUserForm />
                        </div>
                        <UsersList />
                    </TabsContent>
                    <TabsContent value="pending">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Pending Invitations</h2>
                        </div>
                        <PendingInvites />
                    </TabsContent>
                    <TabsContent value="case-statuses">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Case Status Management</h2>
                        </div>
                        <CaseStatusManagement />
                    </TabsContent>
                    <TabsContent value="integrations">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Integrations</h2>
                        </div>
                        <OneDriveIntegration />
                    </TabsContent>
                    <TabsContent value="tags">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Tags Management</h2>
                        </div>
                        <CaseNoteTags />
                    </TabsContent>
                    <TabsContent value="roles">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Roles Management</h2>
                        </div>
                        <RoleManagement />
                    </TabsContent>
                    <TabsContent value="templates">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Templates Management</h2>
                        </div>
                        <TemplatesManagement />
                    </TabsContent>
                    <TabsContent value="sources">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Intake Source Customization</h2>
                        </div>
                        <IntakeSourceCustomization />
                    </TabsContent>
                    <TabsContent value="folders">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">Folder Management</h2>
                        </div>
                        <FolderManagement />
                    </TabsContent>
                    <TabsContent value="kpi-goals">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">KPI Goals Management</h2>
                        </div>
                        <KPIGoalManagement />
                    </TabsContent>
                    <TabsContent value="kpi-analytics">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-bold">KPI Tracking & Analytics</h2>
                        </div>
                        <KPIManagement />
                    </TabsContent>
                </Tabs>
            </div>
        </QueryClientProvider>
    );
} 