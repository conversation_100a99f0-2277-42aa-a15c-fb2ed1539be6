"use client";

import React, { useState, use<PERSON>emo, use<PERSON><PERSON>back, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import {
  MapPin,
  ChevronLeft,
  ChevronRight,
  Users,
  Download,
  Columns,
  Plus,
  List,
  Trash2,
  Search,
  Navigation,
  CopyPlus,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AddEditContact } from "../components/Contacts/AddEditContact";
import { AddEditEngagement } from "../components/Engagements/AddEditEngagement";
import { PartnerEngagementsDialog } from "../components/Engagements/PartnerEngagementsDialog";
import { LinkMedicalProviderDialog } from "../components/Contacts/LinkMedicalProviderDialog";
import { ProfilePhotoUpload } from "../components/Contacts/ProfilePhotoUpload";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MailLink } from "@/components/ui/mail-link";
import { PhoneLink } from "@/components/ui/phone-link";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import {
  usePartnerContacts,
  useDeletePartnerContact,
  useUpdatePartnerRelationshipStatus,
} from "@/services/marketingService";
import { useOrganizationUsersQuery } from "@/services/orgAPIs";
import { PartnerContact, RelationshipStatusLabels } from "@/type/marketing";
import {
  useLoadScript,
  GoogleMap,
  MarkerF,
  InfoWindowF,
  MarkerClusterer,
  Autocomplete,
} from "@react-google-maps/api";

// Add this type definition near the top of the file with other types
interface ContactWithDistance extends PartnerContact {
  distance?: number;
}

// Add styles to ensure dropdowns render correctly
const dropdownOverridesStyle = `
  /* Override z-index for select dropdown content */
  .SelectContent {
    z-index: 100 !important;
  }

  /* Ensure the popover is above the sidebar */
  [data-radix-popper-content-wrapper] {
    z-index: 100 !important;
  }
`;

export default function ContactsPage() {
  const [viewMode, setViewMode] = useState<"list" | "map">("list");
  const [page, setPage] = useState(1);
  const [selectedAssignee, setSelectedAssignee] = useState<string>("all");
  const [selectedProvider, setSelectedProvider] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(() => {
    // Try to load saved preferences from localStorage
    const savedColumns = localStorage.getItem('contactTableColumns');
    if (savedColumns) {
      try {
        return JSON.parse(savedColumns);
      } catch (e) {
        console.error('Failed to parse saved column preferences:', e);
      }
    }
    // Default columns if no saved preferences
    return [
      "photo",
      "name",
      "contact",
      "business",
      "type",
      "status",
      "location",
      "metrics",
      "medical_provider",
      "actions",
    ];
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<number | null>(null);
  const [selectedMarker, setSelectedMarker] =
    useState<ContactWithDistance | null>(null);
  const [mapCenter, setMapCenter] = useState({ lat: 39.8283, lng: -98.5795 }); // Center of US
  const [mapZoom, setMapZoom] = useState(4);

  // Add this state to track geocoding status
  const [geocodingStatus, setGeocodingStatus] = useState<{
    isLoading: boolean;
    error: string | null;
  }>({ isLoading: false, error: null });

  // Add state to track contacts being geocoded
  const [contactsBeingGeocoded, setContactsBeingGeocoded] = useState<number>(0);

  // Add these new state variables
  const [searchAddress, setSearchAddress] = useState("");
  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [nearbySearchRadius, setNearbySearchRadius] = useState<number>(10); // miles
  const [nearbyContacts, setNearbyContacts] = useState<
    Array<PartnerContact & { distance?: number }>
  >([]);
  const [isSearchingNearby, setIsSearchingNearby] = useState(false);
  const [duplicateContact, setDuplicateContact] = useState<PartnerContact | null>(null);

  // Add these new state variables near the top of the component
  const [isMapDataReady, setIsMapDataReady] = useState(false);
  const [mapLoadingState, setMapLoadingState] = useState<{
    isLoading: boolean;
    error: string | null;
    message: string;
  }>({
    isLoading: true,
    error: null,
    message: "Loading..."
  });

  // Create filters object based on selected values
  const filters = useMemo(() => ({
    search: searchQuery,
    assigned_to: selectedAssignee === "unassigned" ? null :
      selectedAssignee !== "all" ? selectedAssignee : undefined,
    is_provider:
      selectedProvider !== "all" ? selectedProvider === "true" : undefined,
  }), [searchQuery, selectedAssignee, selectedProvider]);

  // Use a larger page size when in map view
  const pageSize = useMemo(() => viewMode === "map" ? 1000 : 10, [viewMode]);

  // Fetch contacts using the API
  const { data, isLoading, error } = usePartnerContacts(page, filters, pageSize);
  const deleteContact = useDeletePartnerContact();
  const updateRelationshipStatus = useUpdatePartnerRelationshipStatus();

  // Load Google Maps script
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
    libraries: ["places"],
  });

  // Memoize map options for performance
  const mapOptions = useMemo(
    () => ({
      disableDefaultUI: false,
      clickableIcons: true,
      scrollwheel: true,
    }),
    []
  );

  // Callback for when the map loads
  const onMapLoad = useCallback(
    (map: google.maps.Map) => {
      // If we have contacts with coordinates, fit the map to show all markers
      if (
        data?.results?.length &&
        data.results.some((contact) => contact.latitude && contact.longitude)
      ) {
        const bounds = new google.maps.LatLngBounds();
        data.results.forEach((contact) => {
          if (contact.latitude && contact.longitude) {
            bounds.extend({ lat: contact.latitude, lng: contact.longitude });
          }
        });
        map.fitBounds(bounds);
      }
    },
    [data?.results]
  );

  // First, let's define the calculateDistance function
  const calculateDistance = useCallback(
    (lat1: number, lon1: number, lat2: number, lon2: number): number => {
      const R = 3958.8; // Earth's radius in miles
      const dLat = ((lat2 - lat1) * Math.PI) / 180;
      const dLon = ((lon2 - lon1) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c; // Distance in miles
    },
    []
  );

  // Then define findNearbyContacts
  const findNearbyContacts = useCallback(
    (lat: number, lng: number, radiusMiles: number) => {
      if (!data?.results?.length) return;

      setIsSearchingNearby(true);

      // Filter contacts that have coordinates
      const contactsWithCoordinates = data.results.filter(
        (contact) => contact.latitude && contact.longitude
      );

      // Calculate distance for each contact and filter by radius
      const contactsWithDistance = contactsWithCoordinates.map((contact) => {
        const distance = calculateDistance(
          lat,
          lng,
          contact.latitude || 0,
          contact.longitude || 0
        );

        return {
          ...contact,
          distance,
        };
      });

      // Sort by distance and filter by radius
      const nearby = contactsWithDistance
        .filter((contact) => (contact.distance || 0) <= radiusMiles)
        .sort((a, b) => (a.distance || 0) - (b.distance || 0));

      setNearbyContacts(nearby);
      setIsSearchingNearby(false);

      // If we found nearby contacts, select the closest one
      if (nearby.length > 0) {
        setSelectedMarker(nearby[0]);
      }
    },
    [data, calculateDistance]
  );

  // Now define onPlaceSelected which depends on findNearbyContacts
  const onPlaceSelected = useCallback(() => {
    if (autocomplete) {
      const place = autocomplete.getPlace();

      if (place.geometry?.location) {
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();

        // Update map center to the selected location
        setMapCenter({ lat, lng });
        setMapZoom(13); // Zoom to neighborhood level

        // Update the search address with the formatted address from Google
        if (place.formatted_address) {
          setSearchAddress(place.formatted_address);
        }

        // Find nearby contacts
        findNearbyContacts(lat, lng, nearbySearchRadius);
      }
    }
  }, [autocomplete, nearbySearchRadius, findNearbyContacts]);

  // Finally define handleRadiusChange which also depends on findNearbyContacts
  const handleRadiusChange = useCallback(
    (value: string) => {
      const newRadius = Number(value);
      setNearbySearchRadius(newRadius);

      // If we have a place selected, re-run the search with the new radius
      if (autocomplete?.getPlace()?.geometry?.location) {
        const place = autocomplete.getPlace();
        const lat = place.geometry!.location!.lat();
        const lng = place.geometry!.location!.lng();
        findNearbyContacts(lat, lng, newRadius);
      }
    },
    [autocomplete, findNearbyContacts]
  );

  // Function to geocode addresses
  const geocodeAddresses = useCallback(async () => {
    if (!isLoaded || !data?.results?.length) {
      return;
    }

    if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
      console.error("[Map Debug] Google Maps API key is missing");
      setMapLoadingState({
        isLoading: false,
        error: "Google Maps API key is missing. Please check your environment variables.",
        message: ""
      });
      return;
    }

    const contactsToGeocode = data.results.filter(
      (contact) =>
        (!contact.latitude || !contact.longitude) &&
        contact.street1 &&
        contact.city &&
        contact.state
    );


    if (contactsToGeocode.length === 0) {
      setIsMapDataReady(true);
      return;
    }

    setContactsBeingGeocoded(contactsToGeocode.length);
    const geocoder = new google.maps.Geocoder();
    const updatedResults = [...data.results];
    let successCount = 0;
    let errorCount = 0;
    let firstError = null;

    // Start background geocoding process
    (async () => {
      for (const contact of contactsToGeocode) {
        const index = updatedResults.findIndex((c) => c.id === contact.id);
        if (index === -1) continue;

        const address = `${contact.street1} ${contact.street2 ? contact.street2 + " " : ""}${contact.city}, ${contact.state} ${contact.zip_code}`;

        try {
          await new Promise((resolve) => setTimeout(resolve, 300));
          const result = await new Promise<google.maps.GeocoderResult[]>(
            (resolve, reject) => {
              geocoder.geocode({ address }, (results, status) => {
                if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
                  resolve(results as google.maps.GeocoderResult[]);
                } else {
                  reject(status);
                }
              });
            }
          );

          if (result && result[0]?.geometry?.location) {
            const lat = result[0].geometry.location.lat();
            const lng = result[0].geometry.location.lng();

            updatedResults[index] = {
              ...updatedResults[index],
              latitude: lat,
              longitude: lng,
            };
            successCount++;

            if (data) {
              data.results = [...updatedResults];
              setIsMapDataReady(true);
            }
          }
        } catch (error) {
          console.error(`[Map Debug] Error geocoding address for ${contact.first_name} ${contact.last_name}:`, error);
          errorCount++;

          if (!firstError) {
            firstError = error;
          }

          if (error === google.maps.GeocoderStatus.REQUEST_DENIED) {
            break;
          }
        }

        // Update loading state with progress
        setMapLoadingState({
          isLoading: true,
          error: null,
          message: `Geocoding contacts: ${successCount}/${contactsToGeocode.length} complete`
        });
      }


      let errorMessage = null;
      if (errorCount > 0) {
        if (firstError === google.maps.GeocoderStatus.REQUEST_DENIED) {
          errorMessage = "Google Maps API key error. Please check your API key configuration and billing status.";
        } else {
          errorMessage = `Failed to geocode ${errorCount} addresses`;
        }
      }

      setMapLoadingState({
        isLoading: false,
        error: errorMessage,
        message: errorMessage ? "" : "Map data ready"
      });

      setContactsBeingGeocoded(0);
      setIsMapDataReady(true);
    })();

    return;
  }, [data, isLoaded]);

  // Trigger geocoding when map view is selected and Google Maps is loaded
  useEffect(() => {
    if (viewMode === "map" && isLoaded && data?.results) {
      
      setMapLoadingState({
        isLoading: true,
        error: null,
        message: "Preparing contact data for map..."
      });

      // Count contacts with coordinates
      const contactsWithCoords = data.results.filter(
        contact => contact.latitude && contact.longitude
      );

      if (contactsWithCoords.length > 0) {
        setIsMapDataReady(true);
        setMapLoadingState({
          isLoading: false,
          error: null,
          message: "Map data ready"
        });
      }

      // Start geocoding for contacts without coordinates
      geocodeAddresses();
    }
  }, [viewMode, isLoaded, data?.results, geocodeAddresses]);

  const handleValueChange = (value: string) => {
    setViewMode(value as "list" | "map");
  };

  // Fetch organization users for assignee filter
  const { data: organizationUsers } = useOrganizationUsersQuery();

  // Assignee options
  const assigneeOptions = useMemo(() => {
    const baseOptions = [
      { value: "all", label: "All Assignees" },
      // { value: "unassigned", label: "Unassigned" },
    ];

    // Add organization users if available
    if (organizationUsers && organizationUsers.length > 0) {
      return [
        ...baseOptions,
        ...organizationUsers.map(user => ({
          value: user.id.toString(),
          label: user.name || user.email
        }))
      ];
    }

    return baseOptions;
  }, [organizationUsers]);

  // Provider type options
  const providerOptions = [
    { value: "all", label: "All Types" },
    { value: "true", label: "Provider" },
    { value: "false", label: "Non-Provider" },
  ];

  // Column options
  const columnOptions = [
    { value: "all", label: "All Columns" },
    { value: "photo", label: "Photo" },
    { value: "name", label: "Name" },
    { value: "contact", label: "Contact Info" },
    { value: "business", label: "Business" },
    { value: "type", label: "Type" },
    { value: "status", label: "Status" },
    { value: "location", label: "Location" },
    { value: "metrics", label: "Metrics" },
    { value: "medical_provider", label: "Medical Provider" },
    { value: "assigned_to", label: "Assigned To" },
    { value: "created_by", label: "Created By" },
    { value: "actions", label: "Actions" },
  ];

  const handleColumnToggle = (value: string) => {
    if (value === "all") {
      const allColumnValues = columnOptions
        .filter((opt) => opt.value !== "all")
        .map((opt) => opt.value);
      const areAllVisible = allColumnValues.every((col) =>
        visibleColumns.includes(col)
      );

      if (areAllVisible) {
        setVisibleColumns(["name", "actions"]); // Keep minimum required columns
      } else {
        setVisibleColumns(allColumnValues);
      }
    } else {
      setVisibleColumns((prev) =>
        prev.includes(value)
          ? prev.filter((col) => col !== value)
          : [...prev, value]
      );
    }
  };

  const handleExportCSV = () => {
    // Export functionality to be implemented
    // alert("Export functionality will be implemented here");
  };

  const handleDeleteClick = (contactId: number) => {
    setContactToDelete(contactId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (contactToDelete) {
      try {
        await deleteContact.mutateAsync(contactToDelete);
        setContactToDelete(null);
        setDeleteDialogOpen(false);
      } catch (error) {
        console.error("Error deleting contact:", error);
      }
    }
  };

  // Add handler for duplicate contact
  const handleDuplicateContact = (contact: PartnerContact) => {
    setDuplicateContact(contact);
  };

  console.log(handleDuplicateContact)

  // Update the test coordinates function to be more robust
  const setTestCoordinates = useCallback(() => {
    if (!data?.results?.length) return;

    console.log("Setting test coordinates for contacts");

    // Create a copy of the data
    const updatedResults = [...data.results];

    // Set test coordinates for each contact
    updatedResults.forEach((contact, index) => {
      // Generate slightly different coordinates for each contact
      const latOffset = (index * 0.01) % 0.2;
      const lngOffset = (index * 0.015) % 0.3;

      updatedResults[index] = {
        ...contact,
        latitude: 47.6062 + latOffset, // Seattle area
        longitude: -122.3321 + lngOffset,
      };
    });

    // Update the data
    if (data) {
      // Make sure we're creating a new reference to trigger re-renders
      data.results = [...updatedResults];
    }

    // Reset geocoding status to clear any errors
    setGeocodingStatus({ isLoading: false, error: null });

    // Force a re-render
    setMapCenter({ lat: 47.6062, lng: -122.3321 });
    setMapZoom(12);

    console.log("Test coordinates set for", updatedResults.length, "contacts");

    // Force component to re-render
    setSelectedMarker(null);
  }, [data, setGeocodingStatus, setMapCenter, setMapZoom, setSelectedMarker]);

  // Add this function to update map center and zoom when clicking on a marker
  const handleMarkerClick = (contact: PartnerContact) => {
    if (contact.latitude && contact.longitude) {
      setMapCenter({ lat: contact.latitude, lng: contact.longitude });
      setMapZoom(14); // Zoom in when clicking a marker
    }
    setSelectedMarker(contact);
  };

  // Add this function to get marker icon with fallback
  const getMarkerIcon = useCallback((contactType: string) => {
    // Use standard Google Maps icons as fallbacks
    return {
      url:
        contactType === "MEDICAL"
          ? "https://maps.google.com/mapfiles/ms/icons/blue-dot.png"
          : "https://maps.google.com/mapfiles/ms/icons/red-dot.png",
      scaledSize: new google.maps.Size(30, 30),
    };
  }, []);

  // Update the renderMap function to include the address search
  const renderMap = () => {
    if (loadError) {
      console.error("Google Maps load error:", loadError);
      return (
        <div className="flex flex-col items-center justify-center h-[500px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <div className="text-red-500 mb-2">Error loading maps</div>
          <p className="text-gray-500 text-center max-w-md">
            There was an error loading Google Maps. Please check your API key
            and try again.
          </p>
          <p className="text-xs text-gray-400 mt-2">{loadError.toString()}</p>
        </div>
      );
    }

    if (!isLoaded || !isMapDataReady || mapLoadingState.isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-[500px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-400 mb-4"></div>
          <p className="text-gray-500 text-center">{mapLoadingState.message || "Loading map data..."}</p>
          {contactsBeingGeocoded > 0 && (
            <p className="text-sm text-gray-400 mt-2">
              Geocoding {contactsBeingGeocoded} addresses in background...
            </p>
          )}
        </div>
      );
    }
    
    if (geocodingStatus.error && geocodingStatus.error.includes("API key")) {
      return (
        <div className="flex flex-col items-center justify-center h-[500px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <div className="text-red-500 mb-2">Geocoding Error</div>
          <p className="text-gray-500 text-center max-w-md mb-4">
            {geocodingStatus.error}
          </p>
          <Button onClick={setTestCoordinates} variant="outline">
            Use Test Coordinates Instead
          </Button>
        </div>
      );
    }

    // Filter contacts that have coordinates
    const contactsWithCoordinates = data?.results?.filter(
      (contact: PartnerContact) => contact.latitude && contact.longitude
    ) || [];

    if (contactsWithCoordinates.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-[500px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <MapPin className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            No Mappable Contacts
          </h3>
          <p className="text-gray-500 text-center max-w-md">
            None of your contacts have valid addresses that could be mapped.
            Please update your contacts with complete address information.
          </p>
        </div>
      );
    }

    return (
      <div className="h-[500px] rounded-lg overflow-hidden border border-gray-200 relative">
        {/* Add loading overlay */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gray-50/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p className="text-gray-700">Loading Map...</p>
            </div>
          </div>
        )}

        {/* Add the address search bar */}
        <div className="absolute top-4 left-4 z-10 w-[350px] bg-white rounded-md shadow-md">
          <div className="p-3">
            <h3 className="text-sm font-medium mb-2">
              Find Contacts Near Address
            </h3>
            <div className="flex gap-2 mb-2">
              {isLoaded ? (
                <Autocomplete
                  onLoad={(autocomplete) => setAutocomplete(autocomplete)}
                  onPlaceChanged={onPlaceSelected}
                  restrictions={{ country: "us" }}
                >
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Enter an address to find nearby contacts"
                      className="pl-8"
                      value={searchAddress}
                      onChange={(e) => setSearchAddress(e.target.value)}
                    />
                  </div>
                </Autocomplete>
              ) : (
                <Input
                  type="text"
                  placeholder="Loading address search..."
                  disabled
                  className="pl-8"
                />
              )}
              <Select
                value={nearbySearchRadius.toString()}
                onValueChange={handleRadiusChange}
              >
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="Radius" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 miles</SelectItem>
                  <SelectItem value="10">10 miles</SelectItem>
                  <SelectItem value="25">25 miles</SelectItem>
                  <SelectItem value="50">50 miles</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Show nearby contacts results */}
            {isSearchingNearby ? (
              <div className="text-sm text-gray-500">
                Searching nearby contacts...
              </div>
            ) : nearbyContacts.length > 0 ? (
              <div className="max-h-[200px] overflow-y-auto">
                <div className="text-xs text-gray-500 mb-1">
                  Found {nearbyContacts.length} contacts within{" "}
                  {nearbySearchRadius} miles
                </div>
                <div className="space-y-2">
                  {nearbyContacts.slice(0, 5).map((contact) => (
                    <div
                      key={contact.id}
                      className="p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                      onClick={() => {
                        if (contact.latitude && contact.longitude) {
                          setMapCenter({
                            lat: contact.latitude,
                            lng: contact.longitude,
                          });
                          setSelectedMarker(contact);
                        }
                      }}
                    >
                      <div className="flex justify-between">
                        <div className="font-medium">{`${contact.first_name} ${contact.last_name}`}</div>
                        <div className="text-sm text-gray-500">
                          {contact.distance?.toFixed(1)} miles
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {contact.business_name}
                      </div>
                      <div className="text-xs text-gray-500 flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {contact.city}, {contact.state}
                      </div>
                    </div>
                  ))}
                  {nearbyContacts.length > 5 && (
                    <div className="text-xs text-center text-gray-500">
                      + {nearbyContacts.length - 5} more contacts
                    </div>
                  )}
                </div>
              </div>
            ) : searchAddress ? (
              <div className="text-sm text-gray-500">
                No contacts found within {nearbySearchRadius} miles
              </div>
            ) : null}
          </div>
        </div>

        <GoogleMap
          options={mapOptions}
          zoom={mapZoom}
          center={mapCenter}
          mapContainerClassName="w-full h-full"
          onLoad={onMapLoad}
        >
          {/* Existing map content */}
          {contactsWithCoordinates.length === 0 && (
            <div className="absolute top-0 left-0 bg-white p-2 z-10 text-xs">
              No contacts with coordinates to display
            </div>
          )}

          {/* Show a notification when geocoding is happening in the background */}
          {contactsBeingGeocoded > 0 && (
            <div className="absolute top-0 right-0 bg-white p-2 z-10 text-xs flex items-center">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-2"></div>
              Geocoding {contactsBeingGeocoded} addresses in background...
            </div>
          )}

          <MarkerClusterer
            options={{
              imagePath:
                "https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",
              gridSize: 50,
              minimumClusterSize: 3,
              zoomOnClick: true,
            }}
          >
            {(clusterer) => (
              <>
                {contactsWithCoordinates.map((contact: PartnerContact) => (
                  <MarkerF
                    key={contact.id}
                    position={{
                      lat: contact.latitude ?? 0,
                      lng: contact.longitude ?? 0,
                    }}
                    onClick={() => handleMarkerClick(contact)}
                    icon={getMarkerIcon(contact.partner_type)}
                    clusterer={clusterer}
                    animation={
                      nearbyContacts.some((c) => c.id === contact.id)
                        ? google.maps.Animation.BOUNCE
                        : undefined
                    }
                  />
                ))}
              </>
            )}
          </MarkerClusterer>

          {/* Show a marker for the searched location */}
          {searchAddress && autocomplete?.getPlace()?.geometry?.location && (
            <MarkerF
              position={{
                lat: autocomplete.getPlace().geometry!.location!.lat(),
                lng: autocomplete.getPlace().geometry!.location!.lng(),
              }}
              icon={{
                url: "https://maps.google.com/mapfiles/ms/icons/blue-dot.png",
                scaledSize: new google.maps.Size(40, 40),
              }}
            />
          )}

          {selectedMarker &&
            selectedMarker.latitude &&
            selectedMarker.longitude && (
              <InfoWindowF
                position={{
                  lat: selectedMarker.latitude,
                  lng: selectedMarker.longitude,
                }}
                onCloseClick={() => setSelectedMarker(null)}
              >
                <div className="p-2 max-w-xs">
                  <h3 className="font-medium text-gray-900">{`${selectedMarker.first_name} ${selectedMarker.last_name}`}</h3>
                  <p className="text-sm text-gray-600">
                    {selectedMarker.business_name}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {selectedMarker.street1}
                    {selectedMarker.street2
                      ? `, ${selectedMarker.street2}`
                      : ""}
                    , {selectedMarker.city}, {selectedMarker.state}{" "}
                    {selectedMarker.zip_code}
                  </p>
                  {/* Show distance if available */}
                  {selectedMarker?.distance !== undefined && (
                    <p className="text-sm font-medium text-blue-600 mt-1">
                      <Navigation className="h-3 w-3 inline mr-1" />
                      {selectedMarker.distance.toFixed(1)} miles away
                    </p>
                  )}
                  <div className="mt-2 flex gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <AddEditContact
                        isEdit={true}
                        initialData={selectedMarker}
                      >
                        Edit
                      </AddEditContact>
                    </Button>
                    <Button variant="outline" size="sm">
                      <PartnerEngagementsDialog
                        partnerId={selectedMarker.id}
                        partnerName={`${selectedMarker.first_name} ${selectedMarker.last_name}`}
                      />
                      Engagements
                    </Button>
                  </div>
                </div>
              </InfoWindowF>
            )}
        </GoogleMap>

        {/* Enhanced debug panel */}
        <div className="absolute bottom-4 right-4 bg-white p-2 rounded shadow text-xs z-10">
          <p className="font-bold">Map Info:</p>
          <p>Contacts on map: {contactsWithCoordinates.length}</p>
          <p>
            Map center: {mapCenter.lat.toFixed(4)}, {mapCenter.lng.toFixed(4)}
          </p>
          <p>Map zoom: {mapZoom}</p>
          {nearbyContacts.length > 0 && (
            <p>Nearby contacts: {nearbyContacts.length}</p>
          )}
        </div>
      </div>
    );
  };

  // Add this function to filter contacts based on search query
  const getFilteredContacts = useCallback(() => {
    if (!data?.results || !searchQuery) return [];

    return data.results.filter((contact: PartnerContact) => {
      const searchLower = searchQuery.toLowerCase();
      const addressString = `${contact.street1} ${contact.street2 || ''} ${contact.city} ${contact.state} ${contact.zip_code}`.toLowerCase();
      const nameString = `${contact.first_name} ${contact.last_name}`.toLowerCase();
      const businessString = contact.business_name?.toLowerCase() || '';

      return addressString.includes(searchLower) ||
        nameString.includes(searchLower) ||
        businessString.includes(searchLower);
    });
  }, [data?.results, searchQuery]);

  // Add click outside handler to close search results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showSearchResults) {
        const searchContainer = document.querySelector('.search-container');
        if (searchContainer && !searchContainer.contains(event.target as Node)) {
          setShowSearchResults(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSearchResults]);

  // Add effect to save column preferences
  useEffect(() => {
    localStorage.setItem('contactTableColumns', JSON.stringify(visibleColumns));
  }, [visibleColumns]);

  // Initialize the query client
  const queryClient = useQueryClient();

  // Add effect to handle contact updates
  useEffect(() => {
    const handleContactUpdated = () => {
      // Refetch the data when a contact is updated
      queryClient.invalidateQueries({ queryKey: ['partners'] });
    };

    // Listen for the custom event
    window.addEventListener('contact-updated', handleContactUpdated);

    // Clean up the event listener
    return () => {
      window.removeEventListener('contact-updated', handleContactUpdated);
    };
  }, [queryClient]);

  // Add this effect to inject the style overrides
  useEffect(() => {
    // Create a style element and append the overrides
    const styleEl = document.createElement('style');
    styleEl.textContent = dropdownOverridesStyle;
    document.head.appendChild(styleEl);

    // Clean up when component unmounts
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on initial load
    checkIsMobile();

    // Check on window resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <main className="flex-1 min-w-0 bg-white p-6 ml-0">
      <h1 className="text-2xl font-semibold mb-6 mt-6">Contacts</h1>

      <div className="space-y-6">
        <Tabs
          value={viewMode}
          onValueChange={handleValueChange}
          className="w-full"
        >
          <div className="relative w-full">
            <div className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white to-transparent pointer-events-none" />
            <div className="absolute left-0 top-0 h-full w-8 bg-gradient-to-r from-white to-transparent pointer-events-none" />
            <TabsList className="w-full">
              <TabsTrigger value="list">
                <span className="mr-2">
                  <List className="h-4 w-4" />
                </span>{" "}
                List View
              </TabsTrigger>
              <TabsTrigger value="map">
                <span className="mr-2">
                  <MapPin className="h-4 w-4" />
                </span>{" "}
                Map View
              </TabsTrigger>
            </TabsList>
          </div>
          <div className="w-full h-[calc(100vh-235px)] overflow-y-auto scroll-smooth scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 mt-6">
            <TabsContent value="list" className="px-3">
              <div className="space-y-4">
                <div className={`${isMobile ? 'flex flex-col' : 'flex items-center'} gap-2`}>
                  <Input
                    type="search"
                    placeholder={`Search ${data?.count || 0} contacts(Name, Business)...`}
                    className={`${isMobile ? 'w-full' : 'max-w-xs'}`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Select
                    value={selectedAssignee}
                    onValueChange={setSelectedAssignee}
                  >
                    <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <SelectValue placeholder="Select Assignee" />
                      </div>
                    </SelectTrigger>
                    <SelectContent searchable>
                      {assigneeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={selectedProvider}
                    onValueChange={setSelectedProvider}
                  >
                    <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
                      <SelectValue placeholder="Provider Type" />
                    </SelectTrigger>
                    <SelectContent searchable>
                      {providerOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={visibleColumns.join(",")}
                    onValueChange={(value) => handleColumnToggle(value)}
                  >
                    <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
                      <div className="flex items-center gap-2">
                        <Columns className="h-4 w-4" />
                        <span>{visibleColumns.length} column(s)</span>
                      </div>
                    </SelectTrigger>
                    <SelectContent searchable>
                      {columnOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value}
                          className="flex items-center gap-2"
                        >
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              id={`column-${option.value}`}
                              aria-label={`Toggle ${option.label} column`}
                              checked={
                                option.value === "all"
                                  ? columnOptions
                                    .filter((opt) => opt.value !== "all")
                                    .every((opt) =>
                                      visibleColumns.includes(opt.value)
                                    )
                                  : visibleColumns.includes(option.value)
                              }
                              className="h-4 w-4"
                              onChange={() => { }}
                            />
                            <label htmlFor={`column-${option.value}`}>
                              {option.label}
                            </label>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <div className={`flex items-center justify-end ${isMobile ? 'w-full mt-2' : ''}`}>
                    <AddEditContact />
                    <Button variant="link" onClick={handleExportCSV}>
                      <Download className="h-4 w-4 text-green-600" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-4 bg-white rounded-lg shadow">
                  {isLoading ? (
                    <div className="space-y-3">
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                    </div>
                  ) : error ? (
                    <div className="text-center py-4 text-red-500">
                      Error loading contacts. Please try again later.
                    </div>
                  ) : !data?.results.length ? (
                    <div className="text-center py-4 text-gray-500">
                      No contacts found. Add some contacts to get started.
                    </div>
                  ) : (
                    <>
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-gray-50">
                              {visibleColumns.includes("photo") && (
                                <TableHead className="w-[60px] py-4">
                                  PHOTO
                                </TableHead>
                              )}
                              {visibleColumns.includes("name") && (
                                <TableHead className="w-[200px] py-4">
                                  NAME
                                </TableHead>
                              )}
                              {visibleColumns.includes("contact") && (
                                <TableHead className="w-[250px] py-4">
                                  CONTACT
                                </TableHead>
                              )}
                              {visibleColumns.includes("business") && (
                                <TableHead className="w-[200px] py-4">
                                  BUSINESS
                                </TableHead>
                              )}
                              {visibleColumns.includes("type") && (
                                <TableHead>TYPE</TableHead>
                              )}
                              {visibleColumns.includes("status") && (
                                <TableHead>STATUS</TableHead>
                              )}
                              {visibleColumns.includes("location") && (
                                <TableHead>LOCATION</TableHead>
                              )}
                              {visibleColumns.includes("metrics") && (
                                <TableHead>METRICS</TableHead>
                              )}
                              {visibleColumns.includes("medical_provider") && (
                                <TableHead className="w-[200px] py-4">
                                  MEDICAL PROVIDER
                                </TableHead>
                              )}
                              {visibleColumns.includes("assigned_to") && (
                                <TableHead className="w-[200px] py-4">
                                  ASSIGN TO
                                </TableHead>
                              )}
                              {visibleColumns.includes("created_by") && (
                                <TableHead className="w-[200px] py-4">
                                  CREATED BY
                                </TableHead>
                              )}
                              {visibleColumns.includes("actions") && (
                                <TableHead className="w-[100px]"></TableHead>
                              )}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {data.results.map((contact: PartnerContact) => (
                              <TableRow
                                key={contact.id}
                                className="hover:bg-gray-50/50"
                              >
                                {visibleColumns.includes("photo") && (
                                  <TableCell className="w-[60px]">
                                    <ProfilePhotoUpload
                                      partnerId={contact.id}
                                      currentPhotoUrl={contact.profile_photo ? contact.profile_photo_url : null}
                                      name={`${contact.first_name} ${contact.last_name}`}
                                    />
                                  </TableCell>
                                )}
                                {visibleColumns.includes("name") && (
                                  <TableCell>
                                    <div className="flex flex-col">
                                      <span className="font-medium">
                                        {`${contact.first_name} ${contact.last_name}`}
                                      </span>
                                      <span className="text-sm text-gray-500">
                                        {contact.role}
                                      </span>
                                    </div>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("contact") && (
                                  <TableCell>
                                    <div className="flex flex-col gap-1">
                                      <MailLink email={contact.email} />
                                      <PhoneLink phone={contact.phone} />
                                      {contact.phone_ext && (
                                        <span className="text-sm text-gray-500">
                                          Ext: {contact.phone_ext}
                                        </span>
                                      )}
                                    </div>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("business") && (
                                  <TableCell>
                                    <div className="flex flex-col">
                                      <span className="font-medium">
                                        {contact.business_name}
                                      </span>
                                      {contact.website && (
                                        <a
                                          href={contact.website}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-sm text-blue-600 hover:underline truncate max-w-[200px]"
                                        >
                                          {contact.website.replace(
                                            /^https?:\/\//,
                                            ""
                                          )}
                                        </a>
                                      )}
                                    </div>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("type") && (
                                  <TableCell>
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                      {contact.partner_type}
                                    </span>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("status") && (
                                  <TableCell>
                                    <Select
                                      value={contact.relationship_status}
                                      onValueChange={(newStatus) => {
                                        updateRelationshipStatus.mutate({
                                          partnerId: contact.id,
                                          relationshipStatus: newStatus
                                        });
                                      }}
                                      disabled={updateRelationshipStatus.isPending}
                                    >
                                      <SelectTrigger className="h-8 w-[180px] px-2">
                                        <SelectValue>
                                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${contact.relationship_status === 'ACTIVE' || contact.relationship_status === 'STRONG'
                                              ? "bg-green-100 text-green-800"
                                              : contact.relationship_status === 'DEVELOPING'
                                                ? "bg-blue-100 text-blue-800"
                                                : contact.relationship_status === 'INACTIVE'
                                                  ? "bg-red-100 text-red-800"
                                                  : contact.relationship_status === 'PROSPECT'
                                                    ? "bg-purple-100 text-purple-800"
                                                    : "bg-gray-100 text-gray-800"
                                            }`}>
                                            {RelationshipStatusLabels[contact.relationship_status]}
                                          </span>
                                        </SelectValue>
                                      </SelectTrigger>
                                      <SelectContent>
                                        {Object.entries(RelationshipStatusLabels).map(([value, label]) => (
                                          <SelectItem key={value} value={value}>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${value === 'ACTIVE' || value === 'STRONG'
                                                ? "bg-green-100 text-green-800"
                                                : value === 'DEVELOPING'
                                                  ? "bg-blue-100 text-blue-800"
                                                  : value === 'INACTIVE'
                                                    ? "bg-red-100 text-red-800"
                                                    : value === 'PROSPECT'
                                                      ? "bg-purple-100 text-purple-800"
                                                      : "bg-gray-100 text-gray-800"
                                              }`}>
                                              {label}
                                            </span>
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("location") && (
                                  <TableCell>
                                    <div className="flex flex-col">
                                      <span className="text-sm">
                                        {contact.city}, {contact.state}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        {contact.zip_code}
                                      </span>
                                    </div>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("metrics") && (
                                  <TableCell>
                                    <div className="flex flex-col text-sm">
                                      <span>
                                        Referrals:{" "}
                                        {contact.successful_referrals}/
                                        {contact.total_referrals}
                                      </span>
                                      <span className="text-green-600">
                                        Revenue: $
                                        {contact.total_revenue_generated?.toLocaleString()}
                                      </span>
                                    </div>
                                  </TableCell>
                                )}
                                {visibleColumns.includes(
                                  "medical_provider"
                                ) && (
                                    <TableCell>
                                      {contact.medical_provider ? (
                                        <div className="flex flex-col">
                                          <span className="font-medium">
                                            {contact.medical_provider.company}
                                          </span>
                                          <span className="text-xs text-gray-500">
                                            {contact.medical_provider.specialties}
                                          </span>
                                        </div>
                                      ) : (
                                        <span className="text-gray-400 text-sm">
                                          Not linked
                                        </span>
                                      )}
                                    </TableCell>
                                  )}
                                {visibleColumns.includes(
                                  "assigned_to"
                                ) && (
                                    <TableCell>
                                      {contact.assigned_to ? (
                                        <div className="flex flex-col">
                                          <span className="font-medium">
                                            {contact.assigned_to.name}
                                          </span>
                                        </div>
                                      ) : "-"}
                                    </TableCell>
                                  )}
                                {visibleColumns.includes("created_by") && (
                                  <TableCell>
                                    <div className="flex flex-col">
                                      <span className="font-medium">
                                        {contact.created_by?.name || 'System'}
                                      </span>
                                      <span className="text-xs text-muted-foreground">
                                        {contact.created_at ? new Date(contact.created_at).toLocaleDateString() : "-"}
                                      </span>
                                    </div>
                                  </TableCell>
                                )}
                                {visibleColumns.includes("actions") && (
                                  <TableCell>
                                    <div className="flex items-center justify-end gap-2">
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <AddEditContact
                                              isEdit={true}
                                              initialData={contact}
                                            />
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Edit Contact</p>
                                          </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <AddEditEngagement
                                              contactId={contact.id.toString()}
                                            >
                                              <Plus className="h-4 w-4 text-green-600" />
                                            </AddEditEngagement>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Add Engagement</p>
                                          </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <PartnerEngagementsDialog
                                              partnerId={contact.id}
                                              partnerName={`${contact.first_name} ${contact.last_name}`}
                                            />
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>View Engagements</p>
                                          </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <LinkMedicalProviderDialog
                                              partnerId={contact.id}
                                              partnerName={`${contact.first_name} ${contact.last_name}`}
                                              currentProvider={
                                                contact.medical_provider
                                              }
                                            />
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>
                                              {contact.medical_provider
                                                ? "Update Medical Provider"
                                                : "Link Medical Provider"}
                                            </p>
                                          </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <AddEditContact
                                              initialData={{
                                                ...contact,
                                                first_name: contact.first_name,
                                                last_name: contact.last_name,
                                                email: contact.email,
                                                phone: contact.phone,
                                                phone_ext: contact.phone_ext,
                                                role: contact.role,
                                                business_name: contact.business_name,
                                                partner_type: contact.partner_type,
                                                relationship_status: contact.relationship_status,
                                                website: contact.website,
                                                instagram: contact.instagram,
                                                specialties: contact.specialties,
                                                street1: contact.street1,
                                                street2: contact.street2,
                                                city: contact.city,
                                                state: contact.state,
                                                zip_code: contact.zip_code,
                                                accepts_liens: contact.accepts_liens,
                                                no_records_service: contact.no_records_service,
                                                do_not_use: contact.do_not_use,
                                                notes: contact.notes,
                                                is_active: contact.is_active,
                                                assigned_to_id: contact.assigned_to?.id ? Number(contact.assigned_to.id) : undefined,
                                              }}
                                            >
                                              <Button
                                                variant="ghost"
                                                size="icon"
                                                className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                              >
                                                <CopyPlus className="h-4 w-4" />
                                              </Button>
                                            </AddEditContact>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Duplicate Contact</p>
                                          </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                                              onClick={() =>
                                                handleDeleteClick(contact.id)
                                              }
                                            >
                                              <Trash2 className="h-4 w-4" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Delete Contact</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  </TableCell>
                                )}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>

                      <div className="flex items-center justify-between p-4 border-t">
                        <div className="text-sm text-gray-500">
                          Showing {data.results.length} of {data.count} results
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPage((p) => Math.max(1, p - 1))}
                            disabled={!data.previous}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <span className="text-sm">
                            Page {page} of {Math.ceil(data.count / 10)}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPage((p) => p + 1)}
                            disabled={!data.next}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="map" className="px-3">
              <div className="space-y-4">
                <div className={`${isMobile ? 'flex flex-col' : 'flex items-center'} gap-4`}>
                  <div className="relative flex-1 maxw-xs">
                    <Input
                      type="search"
                      placeholder={`Search ${data?.count || 0} contacts...`}
                      className={`${isMobile ? 'w-full' : 'max-w-xs'}`}
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                        setShowSearchResults(true);
                      }}
                      onFocus={() => setShowSearchResults(true)}
                    />
                    {/* Search Results Dropdown */}
                    {showSearchResults && searchQuery && (
                      <div className="absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 max-h-[300px] overflow-y-auto">
                        {getFilteredContacts().length > 0 ? (
                          getFilteredContacts().map((contact) => (
                            <div
                              key={contact.id}
                              className="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                              onClick={() => {
                                if (contact.latitude && contact.longitude) {
                                  setMapCenter({
                                    lat: contact.latitude,
                                    lng: contact.longitude
                                  });
                                  setMapZoom(15);
                                  setSelectedMarker(contact);
                                  setShowSearchResults(false);
                                }
                              }}
                            >
                              <div className="font-medium text-sm">
                                {contact.first_name} {contact.last_name}
                                {contact.business_name && (
                                  <span className="text-gray-500"> - {contact.business_name}</span>
                                )}
                              </div>
                              <div className="text-xs text-gray-500">
                                {contact.street1}
                                {contact.street2 && `, ${contact.street2}`}
                                {`, ${contact.city}, ${contact.state} ${contact.zip_code}`}
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            No matching contacts found
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <Select
                    value={selectedAssignee}
                    onValueChange={setSelectedAssignee}
                  >
                    <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <SelectValue placeholder="Select Assignee" />
                      </div>
                    </SelectTrigger>
                    <SelectContent searchable>
                      {assigneeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={selectedProvider}
                    onValueChange={setSelectedProvider}
                  >
                    <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
                      <SelectValue placeholder="Provider Type" />
                    </SelectTrigger>
                    <SelectContent searchable>
                      {providerOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* <Select
                    value={visibleColumns.join(",")}
                    onValueChange={(value) => handleColumnToggle(value)}
                  >
                    <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
                      <div className="flex items-center gap-2">
                        <Columns className="h-4 w-4" />
                        <span>{visibleColumns.length} column(s)</span>
                      </div>
                    </SelectTrigger>
                    <SelectContent searchable>
                      {columnOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value}
                          className="flex items-center gap-2"
                        >
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              id={`column-${option.value}`}
                              aria-label={`Toggle ${option.label} column`}
                              checked={
                                option.value === "all"
                                  ? columnOptions
                                      .filter((opt) => opt.value !== "all")
                                      .every((opt) =>
                                        visibleColumns.includes(opt.value)
                                      )
                                  : visibleColumns.includes(option.value)
                              }
                              className="h-4 w-4"
                              onChange={() => {}}
                            />
                            <label htmlFor={`column-${option.value}`}>
                              {option.label}
                            </label>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select> */}

                  <div className={`flex items-center justify-end ${isMobile ? 'w-full mt-2' : ''}`}>
                    <AddEditContact />
                    <Button variant="link" onClick={handleExportCSV}>
                      <Download className="h-4 w-4 text-green-600" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-4">
                  {renderMap()}
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        text="Contact"
      />
      {duplicateContact && (
        <AddEditContact
          initialData={{
            ...duplicateContact,
            first_name: duplicateContact.first_name,
            last_name: duplicateContact.last_name,
            email: duplicateContact.email,
            phone: duplicateContact.phone,
            phone_ext: duplicateContact.phone_ext,
            role: duplicateContact.role,
            business_name: duplicateContact.business_name,
            partner_type: duplicateContact.partner_type,
            relationship_status: duplicateContact.relationship_status,
            website: duplicateContact.website,
            instagram: duplicateContact.instagram,
            specialties: duplicateContact.specialties,
            street1: duplicateContact.street1,
            street2: duplicateContact.street2,
            city: duplicateContact.city,
            state: duplicateContact.state,
            zip_code: duplicateContact.zip_code,
            accepts_liens: duplicateContact.accepts_liens,
            no_records_service: duplicateContact.no_records_service,
            do_not_use: duplicateContact.do_not_use,
            notes: duplicateContact.notes,
            is_active: duplicateContact.is_active,
            assigned_to_id: duplicateContact.assigned_to?.id ? Number(duplicateContact.assigned_to.id) : undefined,
          }}
        >
          <div className="hidden" />
        </AddEditContact>
      )}
    </main>
  );
}