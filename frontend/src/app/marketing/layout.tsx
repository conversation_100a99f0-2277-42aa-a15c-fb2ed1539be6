import { MarketingSidebar } from "./components/MarketingSidebar/MarketingSidebar";

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="h-[calc(100vh-64px)] flex flex-col relative" style={{ backgroundColor: 'rgba(236, 236, 238, 0.40)' }}>
      <div className="flex-1 flex min-h-0">
        <MarketingSidebar />
        <div className="flex-1 overflow-auto w-full">{children}</div>
      </div>
    </div>
  );
} 