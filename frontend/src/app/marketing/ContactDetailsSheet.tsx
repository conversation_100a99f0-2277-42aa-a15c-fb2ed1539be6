import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { ExternalLink, Building2, Mail, MapPin, Phone, Briefcase, ClipboardList } from "lucide-react";
import { PartnerContact } from "@/type/marketing";
import { useDeletePartnerContact } from "@/services/marketingService";
import { Badge } from "@/components/ui/badge";

interface ContactDetailsSheetProps {
    contact: PartnerContact;
}

export default function ContactDetailsSheet({ contact }: ContactDetailsSheetProps) {
    const deleteContact = useDeletePartnerContact();

    const handleDelete = async () => {
        if (window.confirm('Are you sure you want to delete this contact?')) {
            await deleteContact.mutateAsync(contact.id);
        }
    };

    return (
        <Sheet>
            <SheetTrigger asChild>
                <Button
                    variant="ghost"
                    className="flex items-center gap-2 text-emerald-700 hover:text-emerald-800 hover:bg-emerald-50"
                >
                    <span>View Details</span>
                    <ExternalLink className="h-4 w-4" />
                </Button>
            </SheetTrigger>
            <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
                <SheetHeader className="border-b pb-5">
                    <SheetTitle className="text-xl font-bold mt-4">
                        <div className="flex items-center gap-4">
                            <div className="h-12 w-12 rounded-full bg-emerald-100 flex items-center justify-center">
                                <span className="text-sm font-bold text-emerald-700">
                                    {`${contact.first_name[0]}${contact.last_name[0]}`}
                                </span>
                            </div>
                            <div className="space-y-1">
                                <h3 className="font-bold text-base">{`${contact.first_name} ${contact.last_name}`}</h3>
                                <div className="flex items-center gap-2">
                                    <Badge variant="secondary" className="font-normal">
                                        {contact.role || 'Other Provider'}
                                    </Badge>
                                    <Badge 
                                        variant={contact.is_active ? "default" : "secondary"}
                                        className="font-normal"
                                    >
                                        {contact.relationship_status}
                                    </Badge>
                                </div>
                            </div>
                            <div className="ml-auto flex gap-2">
                                {contact.phone && (
                                    <Button 
                                        size="icon" 
                                        variant="secondary" 
                                        className="rounded-full bg-emerald-100 hover:bg-emerald-200"
                                        onClick={() => window.open(`tel:${contact.phone}`)}
                                    >
                                        <Phone className="h-5 w-5 text-emerald-700" />
                                    </Button>
                                )}
                                {contact.email && (
                                    <Button 
                                        size="icon" 
                                        variant="secondary" 
                                        className="rounded-full bg-emerald-100 hover:bg-emerald-200"
                                        onClick={() => window.open(`mailto:${contact.email}`)}
                                    >
                                        <Mail className="h-5 w-5 text-emerald-700" />
                                    </Button>
                                )}
                            </div>
                        </div>
                    </SheetTitle>
                </SheetHeader>

                <div className="mt-6 space-y-6">
                    {/* Business Information */}
                    <div className="space-y-4">
                        <h3 className="text-sm font-semibold text-gray-700">Business Information</h3>
                        <div className="grid grid-cols-1 gap-4 bg-gray-50 p-4 rounded-lg">
                            {contact.business_name && (
                                <div className="flex items-center space-x-3">
                                    <Building2 className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <span className="text-sm font-medium">Practice/Business</span>
                                        <p className="text-sm text-gray-600">{contact.business_name}</p>
                                    </div>
                                </div>
                            )}
                            {contact.role && (
                                <div className="flex items-center space-x-3">
                                    <Briefcase className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <span className="text-sm font-medium">Role</span>
                                        <p className="text-sm text-gray-600">{contact.role}</p>
                                    </div>
                                </div>
                            )}
                            {(contact.city || contact.state) && (
                                <div className="flex items-center space-x-3">
                                    <MapPin className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <span className="text-sm font-medium">Location</span>
                                        <p className="text-sm text-gray-600">
                                            {[contact.city, contact.state].filter(Boolean).join(', ')}
                                            {contact.zip_code && ` ${contact.zip_code}`}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Contact Information */}
                    <div className="space-y-4">
                        <h3 className="text-sm font-semibold text-gray-700">Contact Information</h3>
                        <div className="grid grid-cols-1 gap-4 bg-gray-50 p-4 rounded-lg">
                            {contact.phone && (
                                <div className="flex items-center space-x-3">
                                    <Phone className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <span className="text-sm font-medium">Phone</span>
                                        <p className="text-sm text-gray-600">{contact.phone}</p>
                                    </div>
                                </div>
                            )}
                            {contact.email && (
                                <div className="flex items-center space-x-3">
                                    <Mail className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <span className="text-sm font-medium">Email</span>
                                        <p className="text-sm text-gray-600">{contact.email}</p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Notes */}
                    {contact.notes && (
                        <div className="space-y-4">
                            <h3 className="text-sm font-semibold text-gray-700">Additional Notes</h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="flex items-start space-x-3">
                                    <ClipboardList className="h-5 w-5 text-gray-500 mt-0.5" />
                                    <p className="text-sm text-gray-600">{contact.notes}</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2 pt-4 mt-6 border-t">
                        <Button 
                            variant="destructive" 
                            onClick={handleDelete}
                            disabled={deleteContact.isPending}
                            className="w-full"
                        >
                            {deleteContact.isPending ? 'Deleting...' : 'Delete Contact'}
                        </Button>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    );
}
