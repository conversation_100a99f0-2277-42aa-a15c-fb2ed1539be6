"use client";

import { LeadsTable } from "../components/Leads/Table";
import { useState, useEffect } from "react";

export default function LeadsPage() {
  const [isMobile, setIsMobile] = useState(false);
  console.log(isMobile);
  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Check on initial load
    checkIsMobile();
    
    // Check on window resize
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <div className="flex-1 overflow-auto p-6">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Leads</h1>
      </div>
      <LeadsTable />
    </div>
  );
} 