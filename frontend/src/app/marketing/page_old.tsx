"use client";

import ContactsTable from './components/ContactsTable';
import FilterSection from './components/FilterSection';
import Header from './components/Header';
import { usePartnerContacts } from '@/services/marketingService';
import { Skeleton } from "@/components/ui/skeleton";
import { useState, useEffect } from "react";

export default function MarketingPage() {
  const { data, isLoading } = usePartnerContacts();
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Check on initial load
    checkIsMobile();
    
    // Check on window resize
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);
  
  // Create a map URL with location
  // const getMapUrl = () => {
  //   if (!data?.results?.length) return '';
    
  //   // Get the first location with valid address
  //   const location = data.results.find(contact => contact.location)?.location;
  //   if (!location) return '';

  //   // Create public map URL
  //   return `https://www.google.com/maps/embed/v1/place?q=${encodeURIComponent(location)}`;
  // };

  return (
    <main className="flex min-h-screen flex-col items-center p-8">
      <Header />
      <FilterSection />
      
      <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} gap-4 w-full mt-8`}>
        <div className={isMobile ? 'w-full' : 'w-1/2'}>
          <div className="w-full">
            <ContactsTable />
          </div>
        </div>
        
        <div className={isMobile ? 'w-full mt-4' : 'w-1/2'}>
          {isLoading ? (
            <Skeleton className="w-full h-[450px]" />
          ) : data?.results?.length ? (
            <iframe 
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d26359195.17562375!2d-113.7239559!3d36.2473834!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x54eab584e432360b%3A0x1c3bb99243deb742!2sUnited%20States!5e0!3m2!1sen!2sus!4v1701835087432!5m2!1sen!2sus"
              width="100%" 
              height="450" 
              style={{ border: 0, borderRadius: '0.5rem' }} 
              allowFullScreen 
              loading="lazy" 
              referrerPolicy="no-referrer-when-downgrade"
            />
          ) : (
            <div className="w-full h-[450px] flex items-center justify-center border rounded-lg">
              No locations to display
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
