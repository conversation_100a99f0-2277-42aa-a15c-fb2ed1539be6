'use client';

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Star,
  Building,
} from "lucide-react";

interface PartnerEngagementAnalysisProps {
  partnerAnalytics: {
    total_partners: number;
    active_partners: number;
    status_breakdown: Array<{
      relationship_status: string;
      count: number;
      percentage: number;
    }>;
    type_distribution: Array<{
      partner_type: string;
      count: number;
      total_revenue: number | null;
    }>;
    partner_engagement: Array<{
      partner_id: number;
      partner__business_name: string;
      engagement_count: number;
      completed_engagements: number;
      revenue_generated: number | null;
      referrals_generated: number | null;
    }>;
    partner_improvements: Array<{
      partner_id: number;
      partner__business_name: string;
      avg_success_rating: number;
      engagement_count: number;
    }>;
    total_revenue: number;
    total_referrals: number;
  };
}

export function PartnerEngagementAnalysis({ partnerAnalytics }: PartnerEngagementAnalysisProps) {
  // Sort partners by engagement count (descending)
  const sortedPartners = [...partnerAnalytics.partner_engagement]
    .sort((a, b) => b.engagement_count - a.engagement_count);

  // Sort partners by success rating (descending)
  const sortedByRating = [...partnerAnalytics.partner_improvements]
    .sort((a, b) => b.avg_success_rating - a.avg_success_rating);

  // Get rating stars based on success rating
  const getRatingStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
          />
        ))}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Partner Engagement Analysis</CardTitle>
        <CardDescription>
          Analysis of user&apos;s interactions with {partnerAnalytics.total_partners} partners
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="partners">Top Partners</TabsTrigger>
            <TabsTrigger value="types">Partner Types</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-blue-700">Total Partners</h3>
                  <Users className="h-5 w-5 text-blue-700" />
                </div>
                <p className="text-2xl font-bold text-blue-700">{partnerAnalytics.total_partners}</p>
                <p className="text-sm text-blue-600 mt-1">
                  {partnerAnalytics.active_partners} active ({Math.round((partnerAnalytics.active_partners / partnerAnalytics.total_partners) * 100)}%)
                </p>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-green-700">Total Engagements</h3>
                  <Building className="h-5 w-5 text-green-700" />
                </div>
                <p className="text-2xl font-bold text-green-700">
                  {partnerAnalytics.partner_engagement.reduce((sum, p) => sum + p.engagement_count, 0)}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  {partnerAnalytics.partner_engagement.reduce((sum, p) => sum + p.completed_engagements, 0)} completed
                </p>
              </div>

              <div className="p-4 bg-purple-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-purple-700">Avg. Success Rating</h3>
                  <Star className="h-5 w-5 text-purple-700" />
                </div>
                <p className="text-2xl font-bold text-purple-700">
                  {partnerAnalytics.partner_improvements.length > 0
                    ? (partnerAnalytics.partner_improvements.reduce((sum, p) => sum + p.avg_success_rating, 0) /
                       partnerAnalytics.partner_improvements.length).toFixed(1)
                    : 'N/A'}
                </p>
                <div className="mt-1">
                  {partnerAnalytics.partner_improvements.length > 0 &&
                    getRatingStars(partnerAnalytics.partner_improvements.reduce((sum, p) => sum + p.avg_success_rating, 0) /
                                  partnerAnalytics.partner_improvements.length)}
                </div>
              </div>
            </div>

            {/* Partner Status Breakdown */}
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-3">Partner Status Breakdown</h3>
              <div className="space-y-3">
                {partnerAnalytics.status_breakdown.map((status, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{status.relationship_status}</span>
                      <span className="text-sm font-medium">{status.count} ({Math.round(status.percentage)}%)</span>
                    </div>
                    <Progress value={status.percentage} className="h-2" />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Top Partners Tab */}
          <TabsContent value="partners" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Most Engaged Partners</h3>
              <div className="space-y-3">
                {sortedPartners.slice(0, 5).map((partner, index) => (
                  <div key={partner.partner_id} className="p-3 border border-gray-100 rounded-md">
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{partner.partner__business_name || `Partner ${partner.partner_id}`}</p>
                        <div className="flex flex-wrap gap-x-4 mt-1 text-xs text-gray-500">
                          <span>{partner.engagement_count} engagements</span>
                          <span>{partner.completed_engagements} completed</span>
                          {partner.revenue_generated !== null && (
                            <span>${partner.revenue_generated.toLocaleString()} revenue</span>
                          )}
                          {partner.referrals_generated !== null && (
                            <span>{partner.referrals_generated} referrals</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="mt-2">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Completion Rate</span>
                        <span>{Math.round((partner.completed_engagements / partner.engagement_count) * 100)}%</span>
                      </div>
                      <Progress
                        value={(partner.completed_engagements / partner.engagement_count) * 100}
                        className="h-1.5"
                      />
                    </div>
                  </div>
                ))}

                {sortedPartners.length === 0 && (
                  <div className="text-center py-6 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No partner engagement data available</p>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4 mt-6">
              <h3 className="text-sm font-medium">Highest Rated Partners</h3>
              <div className="space-y-3">
                {sortedByRating.slice(0, 5).map((partner) => (
                  <div key={partner.partner_id} className="p-3 border border-gray-100 rounded-md">
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                        <Star className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{partner.partner__business_name || `Partner ${partner.partner_id}`}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500">{partner.avg_success_rating.toFixed(1)} avg rating</span>
                          {getRatingStars(partner.avg_success_rating)}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">
                        {partner.engagement_count} engagements
                      </div>
                    </div>
                  </div>
                ))}

                {sortedByRating.length === 0 && (
                  <div className="text-center py-6 text-gray-500">
                    <Star className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No partner rating data available</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Partner Types Tab */}
          <TabsContent value="types" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Partner Type Distribution</h3>

              {/* Pie Chart Visualization */}
              <div className="relative h-[200px] w-full flex items-center justify-center">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-32 h-32 rounded-full border-8 border-gray-100 flex items-center justify-center">
                    <span className="text-lg font-bold">{partnerAnalytics.total_partners}</span>
                  </div>

                  {/* Pie Chart Segments */}
                  {partnerAnalytics.type_distribution.map((type, index) => {
                    const percentage = (type.count / partnerAnalytics.total_partners) * 100;
                    const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-yellow-500', 'bg-red-500'];
                    const color = colors[index % colors.length];

                    // Position segments around the circle
                    const angle = (index / partnerAnalytics.type_distribution.length) * 360;
                    const radians = (angle - 90) * (Math.PI / 180);
                    const radius = 80;
                    const x = Math.cos(radians) * radius;
                    const y = Math.sin(radians) * radius;

                    return (
                      <div
                        key={index}
                        className={`absolute w-6 h-6 rounded-full ${color} flex items-center justify-center text-white text-xs font-bold`}
                        style={{ transform: `translate(${x}px, ${y}px)` }}
                      >
                        {Math.round(percentage)}%
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Type Legend */}
              <div className="grid grid-cols-2 gap-3">
                {partnerAnalytics.type_distribution.map((type, index) => {
                  const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-yellow-500', 'bg-red-500'];
                  const color = colors[index % colors.length];

                  return (
                    <div key={index} className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${color}`}></div>
                      <span className="text-sm">{type.partner_type}</span>
                      <span className="text-xs text-gray-500">({type.count})</span>
                    </div>
                  );
                })}
              </div>

              {/* Type Details */}
              <div className="space-y-3 mt-4">
                {partnerAnalytics.type_distribution.map((type, index) => (
                  <div key={index} className="p-3 border border-gray-100 rounded-md">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">{type.partner_type}</span>
                      <Badge variant="outline">
                        {type.count} partners
                      </Badge>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mb-2">
                      <span>{Math.round((type.count / partnerAnalytics.total_partners) * 100)}% of total</span>
                      {type.total_revenue !== null && (
                        <span>${type.total_revenue.toLocaleString()} revenue</span>
                      )}
                    </div>
                    <Progress
                      value={(type.count / partnerAnalytics.total_partners) * 100}
                      className="h-1.5"
                    />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
