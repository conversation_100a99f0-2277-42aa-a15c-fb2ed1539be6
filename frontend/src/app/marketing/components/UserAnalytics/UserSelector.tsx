'use client';

import { useState } from 'react';
import { useOrganizationUsers } from '@/services/organizationService';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface UserSelectorProps {
  onUserSelect: (userId: number) => void;
  selectedUserId?: number;
}

export function UserSelector({ onUserSelect, selectedUserId }: UserSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const { data: users, isLoading, error } = useOrganizationUsers(searchTerm);

  // Handle user selection
  const handleUserSelect = (userId: string) => {
    onUserSelect(parseInt(userId));
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-600 rounded-md">
        <p>Error loading users. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="text"
            placeholder="Search users..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select
          value={selectedUserId?.toString()}
          onValueChange={handleUserSelect}
        >
          <SelectTrigger className="w-[240px]">
            <SelectValue placeholder="Select a user" />
          </SelectTrigger>
          <SelectContent>
            {users && users.length > 0 ? (
              users.map((user) => (
                <SelectItem key={user.id} value={user.id.toString()}>
                  {user.first_name} {user.last_name}
                </SelectItem>
              ))
            ) : (
              <div className="p-2 text-center text-gray-500">No users found</div>
            )}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
