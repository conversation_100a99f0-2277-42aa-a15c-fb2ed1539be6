'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { useEngagements } from '@/services/marketingService';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Calendar,
  Clock,
  MapPin,
  Star,
  Users,
  FileText,
  ArrowRight,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";

interface EngagementTimelineProps {
  userId: number;
  startDate: string;
  endDate: string;
}

export function EngagementTimeline({ userId, startDate, endDate }: EngagementTimelineProps) {
  const [page, setPage] = useState(1);
  
  // Fetch engagements for the selected user
  const { data, isLoading, error } = useEngagements(page, {
    created_by: userId.toString(),
    start_date: startDate,
    end_date: endDate,
    date_field: 'date', // Filter by engagement date
  });

  // Get status icon based on engagement status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'NO_SHOW':
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
      default:
        return <Calendar className="h-4 w-4 text-blue-500" />;
    }
  };

  // Get rating stars based on success rating
  const getRatingStars = (rating: number | undefined) => {
    if (!rating) return 'Not rated';
    
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star 
            key={star} 
            className={`h-4 w-4 ${star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
          />
        ))}
      </div>
    );
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      console.error('Error formatting date:', e);
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-600 rounded-md">
        <p>Error loading engagements. Please try again.</p>
      </div>
    );
  }

  if (!data || data.results.length === 0) {
    return (
      <div className="text-center py-8 bg-gray-50 rounded-lg">
        <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-2" />
        <h3 className="text-lg font-medium text-gray-700">No Engagements Found</h3>
        <p className="text-gray-500 mt-2">
          No engagements were found for this user in the selected time period.
        </p>
      </div>
    );
  }

  // Sort engagements by date (newest first)
  const sortedEngagements = [...data.results].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Engagement Timeline</CardTitle>
        <CardDescription>
          Showing {sortedEngagements.length} engagements from {format(new Date(startDate), 'MMM d, yyyy')} to {format(new Date(endDate), 'MMM d, yyyy')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {sortedEngagements.map((engagement) => (
            <Accordion key={engagement.id} type="single" collapsible className="border rounded-md">
              <AccordionItem value={`engagement-${engagement.id}`} className="border-none">
                <AccordionTrigger className="px-4 py-2 hover:bg-gray-50">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-full bg-blue-50">
                      {getStatusIcon(engagement.status)}
                    </div>
                    <div>
                      <div className="font-medium">{engagement.engagement_type_name}</div>
                      <div className="text-sm text-gray-500">
                        {formatDate(engagement.date)} • {engagement.partner_name}
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge variant={engagement.status === 'COMPLETED' ? 'success' : 
                                        engagement.status === 'CANCELLED' ? 'destructive' : 'outline'}>
                          {engagement.status}
                        </Badge>
                        {engagement.success_rating && (
                          <div className="flex items-center gap-1">
                            <span className="text-sm text-gray-500">Rating:</span>
                            {getRatingStars(engagement.success_rating)}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-start gap-2">
                        <Users className="h-4 w-4 text-gray-500 mt-0.5" />
                        <div>
                          <div className="text-sm font-medium">Attendees</div>
                          <div className="text-sm text-gray-500">
                            {engagement.attendees_names?.join(', ') || 'None recorded'}
                          </div>
                        </div>
                      </div>
                      
                      {engagement.location && (
                        <div className="flex items-start gap-2">
                          <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                          <div>
                            <div className="text-sm font-medium">Location</div>
                            <div className="text-sm text-gray-500">{engagement.location}</div>
                          </div>
                        </div>
                      )}
                      
                      {engagement.duration && (
                        <div className="flex items-start gap-2">
                          <Clock className="h-4 w-4 text-gray-500 mt-0.5" />
                          <div>
                            <div className="text-sm font-medium">Duration</div>
                            <div className="text-sm text-gray-500">{engagement.duration} minutes</div>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-3">
                      {engagement.notes && (
                        <div className="flex items-start gap-2">
                          <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                          <div>
                            <div className="text-sm font-medium">Notes</div>
                            <div className="text-sm text-gray-500 line-clamp-3">{engagement.notes}</div>
                          </div>
                        </div>
                      )}
                      
                      {engagement.outcomes && (
                        <div className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-gray-500 mt-0.5" />
                          <div>
                            <div className="text-sm font-medium">Outcomes</div>
                            <div className="text-sm text-gray-500 line-clamp-3">{engagement.outcomes}</div>
                          </div>
                        </div>
                      )}
                      
                      {(engagement.expected_referrals || engagement.actual_referrals) && (
                        <div className="flex items-start gap-2">
                          <ArrowRight className="h-4 w-4 text-gray-500 mt-0.5" />
                          <div>
                            <div className="text-sm font-medium">Referrals</div>
                            <div className="text-sm text-gray-500">
                              Expected: {engagement.expected_referrals || 0} • 
                              Actual: {engagement.actual_referrals || 0}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4 flex justify-end">
                    <Button variant="outline" size="sm" asChild>
                      <a href={`/marketing/engagements/${engagement.id}`} target="_blank" rel="noopener noreferrer">
                        View Full Details
                      </a>
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ))}
        </div>
        
        {data.count > data.results.length && (
          <div className="mt-4 flex justify-center">
            <Button 
              variant="outline" 
              onClick={() => setPage(page + 1)}
              disabled={!data.next}
            >
              Load More
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
