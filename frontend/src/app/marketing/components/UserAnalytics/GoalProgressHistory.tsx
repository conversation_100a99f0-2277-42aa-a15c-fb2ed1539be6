'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Target,
  TrendingUp,
  TrendingDown,
  Minus,
  Calendar,
  CheckCircle,
} from "lucide-react";

interface GoalProgressHistoryProps {
  goalTrends: Array<{
    month: string;
    avg_completion: number;
    total_goals: number;
  }>;
  activeGoals: Array<{
    id: number;
    marketing_goal__name: string | null;
    engagement_goal__name: string | null;
    target_value: number;
    current_progress: number;
    current_period_end: string;
    progress_percentage: number;
  }>;
}

export function GoalProgressHistory({ goalTrends, activeGoals }: GoalProgressHistoryProps) {
  const [selectedGoalId, setSelectedGoalId] = useState<string>('all');

  // If no goal trends data is available, show a message
  if (!goalTrends || goalTrends.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Goal Progress History</CardTitle>
          <CardDescription>Historical progress for user goals</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-gray-400 mb-2" />
            <h3 className="text-lg font-medium text-gray-700">No Goal History</h3>
            <p className="text-gray-500 mt-2">
              No historical goal data is available for this time period.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM yyyy');
    } catch (e) {
      console.error('Error formatting date:', e);
      return dateString;
    }
  };

  // Get trend icon based on progress change
  const getTrendIcon = (current: number, previous: number | undefined) => {
    if (!previous) return <Minus className="h-4 w-4 text-gray-500" />;
    
    if (current > previous) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (current < previous) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    } else {
      return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div>
            <CardTitle className="text-lg">Goal Progress History</CardTitle>
            <CardDescription>Historical progress for user goals</CardDescription>
          </div>
          
          <Select value={selectedGoalId} onValueChange={setSelectedGoalId}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Goals" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Goals</SelectItem>
              {activeGoals.map((goal) => (
                <SelectItem key={goal.id} value={goal.id.toString()}>
                  {goal.marketing_goal__name || goal.engagement_goal__name || `Goal ${goal.id}`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Overall Trend Chart */}
          <div>
            <h3 className="text-sm font-medium mb-3">Overall Completion Trend</h3>
            <div className="h-[150px] w-full relative">
              {/* Chart background grid */}
              <div className="absolute inset-0 grid grid-cols-1 grid-rows-4 gap-0">
                {[0, 1, 2, 3].map((i) => (
                  <div key={i} className="border-t border-gray-100 w-full h-full"></div>
                ))}
              </div>
              
              {/* Chart bars */}
              <div className="absolute inset-0 flex items-end justify-between">
                {goalTrends.map((trend, index) => {
                  const height = `${Math.min(trend.avg_completion, 100)}%`;
                  const prevTrend = index > 0 ? goalTrends[index - 1] : undefined;
                  
                  return (
                    <div key={index} className="flex flex-col items-center w-full">
                      <div className="text-xs text-gray-500 mb-1">
                        {getTrendIcon(trend.avg_completion, prevTrend?.avg_completion)}
                      </div>
                      <div 
                        className="w-4/5 bg-blue-500 rounded-t-sm"
                        style={{ height }}
                      ></div>
                      <div className="text-xs text-gray-500 mt-1 truncate w-full text-center">
                        {formatDate(trend.month)}
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* Y-axis labels */}
              <div className="absolute -left-6 inset-y-0 flex flex-col justify-between text-xs text-gray-500">
                <div>100%</div>
                <div>75%</div>
                <div>50%</div>
                <div>25%</div>
                <div>0%</div>
              </div>
            </div>
          </div>
          
          {/* Monthly Progress Details */}
          <div>
            <h3 className="text-sm font-medium mb-3">Monthly Progress Details</h3>
            <div className="space-y-3">
              {goalTrends.map((trend, index) => {
                const prevTrend = index > 0 ? goalTrends[index - 1] : undefined;
                const change = prevTrend 
                  ? trend.avg_completion - prevTrend.avg_completion 
                  : 0;
                
                return (
                  <div key={index} className="p-3 border border-gray-100 rounded-md">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">{formatDate(trend.month)}</span>
                      </div>
                      <Badge variant={trend.avg_completion >= 100 ? 'success' : 'outline'}>
                        {trend.avg_completion >= 100 ? (
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-3 w-3" />
                            <span>Completed</span>
                          </div>
                        ) : (
                          `${Math.round(trend.avg_completion)}% Complete`
                        )}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Total Goals:</span>{' '}
                        <span className="font-medium">{trend.total_goals}</span>
                      </div>
                      
                      {prevTrend && (
                        <div className="flex items-center gap-1">
                          <span className="text-gray-500">Change:</span>{' '}
                          <span className={`font-medium ${
                            change > 0 ? 'text-green-600' : 
                            change < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {change > 0 ? '+' : ''}{change.toFixed(1)}%
                          </span>
                          {getTrendIcon(trend.avg_completion, prevTrend.avg_completion)}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
