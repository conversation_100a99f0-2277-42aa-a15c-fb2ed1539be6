'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { useMarketingUserAnalytics } from '@/services/marketing-goals-api';
import { UserSelector } from './UserSelector';
import { EngagementTimeline } from './EngagementTimeline';
import { GoalProgressHistory } from './GoalProgressHistory';
import { PartnerEngagementAnalysis } from './PartnerEngagementAnalysis';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Target,
  Calendar,
  Activity,
  UserCheck,
  Award,
  TrendingUp,
  Users,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { GoalCategory } from '@/type/marketing-goals';

export function UserAnalyticsDashboard() {
  const [userId, setUserId] = useState<number | undefined>(undefined);
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('month');

  // Calculate date range based on time range
  const today = new Date();
  const startDate = new Date(today);

  switch(timeRange) {
    case 'month':
      startDate.setMonth(today.getMonth() - 1);
      break;
    case 'quarter':
      startDate.setMonth(today.getMonth() - 3);
      break;
    case 'year':
      startDate.setFullYear(today.getFullYear() - 1);
      break;
  }

  // Fetch user analytics data
  const { data: analytics, isLoading, error } = useMarketingUserAnalytics({
    user_id: userId || 0,
    start_date: format(startDate, 'yyyy-MM-dd'),
    end_date: format(today, 'yyyy-MM-dd'),
  });

  // Handle user selection
  const handleUserSelect = (selectedUserId: number) => {
    setUserId(selectedUserId);
  };

  // Performance score color based on category
  const getScoreColor = (category: string) => {
    switch (category) {
      case 'Outstanding':
        return 'text-green-600';
      case 'Excellent':
        return 'text-green-500';
      case 'Very Good':
        return 'text-blue-500';
      case 'Good':
        return 'text-blue-400';
      case 'Satisfactory':
        return 'text-yellow-500';
      case 'Needs Improvement':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  // Goal category icon and color
  const getCategoryIconAndColor = (category: GoalCategory) => {
    switch (category) {
      case GoalCategory.REFERRAL:
        return { icon: <Target className="h-4 w-4" />, color: 'bg-blue-100 text-blue-600' };
      case GoalCategory.ENGAGEMENT:
        return { icon: <Calendar className="h-4 w-4" />, color: 'bg-green-100 text-green-600' };
      case GoalCategory.REVENUE:
        return { icon: <Activity className="h-4 w-4" />, color: 'bg-purple-100 text-purple-600' };
      case GoalCategory.PARTNER:
        return { icon: <UserCheck className="h-4 w-4" />, color: 'bg-orange-100 text-orange-600' };
      default:
        return { icon: <Target className="h-4 w-4" />, color: 'bg-gray-100 text-gray-600' };
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">Select User</h2>
        <UserSelector onUserSelect={handleUserSelect} selectedUserId={userId} />

        <div className="mt-4 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {userId ? 'Viewing analytics for selected user' : 'Select a user to view their analytics'}
          </div>
          <div className="flex gap-2">
            <Badge
              variant={timeRange === 'month' ? 'default' : 'outline'}
              className="cursor-pointer"
              onClick={() => setTimeRange('month')}
            >
              Month
            </Badge>
            <Badge
              variant={timeRange === 'quarter' ? 'default' : 'outline'}
              className="cursor-pointer"
              onClick={() => setTimeRange('quarter')}
            >
              Quarter
            </Badge>
            <Badge
              variant={timeRange === 'year' ? 'default' : 'outline'}
              className="cursor-pointer"
              onClick={() => setTimeRange('year')}
            >
              Year
            </Badge>
          </div>
        </div>
      </div>

      {!userId && (
        <div className="bg-white p-8 rounded-lg shadow text-center">
          <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-700">Select a User</h3>
          <p className="text-gray-500 mt-2">
            Please select a user from the dropdown above to view their marketing analytics.
          </p>
        </div>
      )}

      {userId && isLoading && (
        <div className="space-y-4">
          <Skeleton className="h-[200px] w-full rounded-lg" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-[300px] w-full rounded-lg" />
            <Skeleton className="h-[300px] w-full rounded-lg" />
          </div>
        </div>
      )}

      {userId && error && (
        <div className="bg-red-50 p-6 rounded-lg shadow text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-red-700">Error Loading Analytics</h3>
          <p className="text-red-600 mt-2">
            There was an error loading the analytics data. Please try again later.
          </p>
        </div>
      )}

      {userId && analytics && (
        <div className="space-y-6">
          {/* Performance Score Card */}
          <Card className="shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl">Performance Score</CardTitle>
              <CardDescription>Overall marketing performance evaluation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row items-center justify-between">
                <div className="flex flex-col items-center mb-4 md:mb-0">
                  <div className="relative w-32 h-32 flex items-center justify-center">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      <circle
                        className="text-gray-200"
                        strokeWidth="8"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                      />
                      <circle
                        className="text-blue-600"
                        strokeWidth="8"
                        strokeDasharray={251.2}
                        strokeDashoffset={251.2 - (251.2 * analytics.performance_score.total_score) / 100}
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                      />
                    </svg>
                    <div className="absolute flex flex-col items-center justify-center">
                      <span className="text-3xl font-bold">{Math.round(analytics.performance_score.total_score)}</span>
                      <span className={`text-sm font-medium ${getScoreColor(analytics.performance_score.category)}`}>
                        {analytics.performance_score.category}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                  {Object.entries(analytics.performance_score.component_scores).map(([key, value]) => {
                    const formattedKey = key.split('_').map(word =>
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ');

                    return (
                      <div key={key} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{formattedKey}</span>
                          <span className="text-sm font-medium">{Math.round(value as number)}%</span>
                        </div>
                        <Progress value={value as number} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analytics Tabs */}
          <Tabs defaultValue="goals" className="w-full">
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="goals">Goal Analytics</TabsTrigger>
              <TabsTrigger value="engagements">Engagement Analytics</TabsTrigger>
              <TabsTrigger value="partners">Partner Analytics</TabsTrigger>
              <TabsTrigger value="detailed">Detailed Analysis</TabsTrigger>
            </TabsList>

            {/* Goals Tab */}
            <TabsContent value="goals" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Total Goals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{analytics.goal_analytics.overall_metrics.total_goals}</div>
                      <Target className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Completed Goals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{analytics.goal_analytics.overall_metrics.completed_goals}</div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Avg. Completion</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{Math.round(analytics.goal_analytics.overall_metrics.avg_completion)}%</div>
                      <Award className="h-8 w-8 text-purple-500" />
                    </div>
                    <Progress
                      value={analytics.goal_analytics.overall_metrics.avg_completion}
                      className="h-2 mt-2"
                    />
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Goals by Category */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Goals by Category</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analytics.goal_analytics.goals_by_category.map((category: {
                        marketing_goal__category: GoalCategory;
                        total: number;
                        completed: number;
                        avg_progress: number;
                      }, index: number) => {
                        const { icon, color } = getCategoryIconAndColor(category.marketing_goal__category);
                        return (
                          <div key={index} className="flex items-start gap-3">
                            <div className={`p-2 rounded-full ${color.split(' ')[0]}`}>
                              <span className={color.split(' ')[1]}>{icon}</span>
                            </div>
                            <div className="flex-1">
                              <p className="text-sm font-medium">{category.marketing_goal__category || 'Unknown'}</p>
                              <div className="flex justify-between mt-1">
                                <p className="text-xs text-gray-600">{category.completed} of {category.total} completed</p>
                                <p className="text-xs font-medium text-green-600">{Math.round(category.avg_progress)}%</p>
                              </div>
                              <Progress
                                value={Number(category.avg_progress || 0)}
                                className="h-1.5 mt-1"
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>

                {/* Active Goals */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Active Goals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
                      {analytics.goal_analytics.active_goals.map((goal: {
                        id: number;
                        marketing_goal__name: string | null;
                        engagement_goal__name: string | null;
                        target_value: number;
                        current_progress: number;
                        current_period_end: string;
                        progress_percentage: number;
                      }) => (
                        <div key={goal.id} className="p-3 border border-gray-100 rounded-md">
                          <div className="flex justify-between items-center mb-1">
                            <p className="text-sm font-medium">{goal.marketing_goal__name || 'Custom Goal'}</p>
                            <Badge variant="outline" className={goal.progress_percentage >= 100 ? 'bg-green-50 text-green-600' : 'bg-blue-50 text-blue-600'}>
                              {goal.progress_percentage >= 100 ? 'Completed' : 'In Progress'}
                            </Badge>
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 mb-2">
                            <span>Target: {goal.target_value}</span>
                            <span>Current: {goal.current_progress}</span>
                            <span>Due: {new Date(goal.current_period_end).toLocaleDateString()}</span>
                          </div>
                          <Progress
                            value={Math.min(Number(goal.progress_percentage || 0), 100)}
                            className="h-1.5"
                          />
                        </div>
                      ))}

                      {analytics.goal_analytics.active_goals.length === 0 && (
                        <div className="text-center py-6 text-gray-500">
                          <Target className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                          <p>No active goals found</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Engagements Tab */}
            <TabsContent value="engagements" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Total Engagements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{analytics.engagement_analytics.created_metrics.total_engagements}</div>
                      <Calendar className="h-8 w-8 text-blue-500" />
                    </div>
                    <div className="flex justify-between mt-2 text-xs text-gray-500">
                      <span>Completed: {analytics.engagement_analytics.created_metrics.completed_engagements}</span>
                      <span>Cancelled: {analytics.engagement_analytics.created_metrics.cancelled_engagements}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Avg. Success Rating</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{(analytics.engagement_analytics.created_metrics.avg_success_rating || 0).toFixed(1)}</div>
                      <Award className="h-8 w-8 text-yellow-500" />
                    </div>
                    <div className="flex mt-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <span key={star} className={`text-lg ${star <= Math.round(analytics.engagement_analytics.created_metrics.avg_success_rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`}>
                          ★
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">ROI</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{Math.round(analytics.engagement_analytics.created_metrics.roi || 0)}%</div>
                      <TrendingUp className="h-8 w-8 text-green-500" />
                    </div>
                    <div className="flex justify-between mt-2 text-xs text-gray-500">
                      <span>Revenue: ${(analytics.engagement_analytics.created_metrics.total_revenue || 0).toLocaleString()}</span>
                      <span>Cost: ${(analytics.engagement_analytics.created_metrics.total_cost || 0).toLocaleString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Engagement Types */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Engagement Types</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
                      {analytics.engagement_analytics.engagement_types.map((type: {
                        engagement_type__name: string;
                        engagement_type__category: string;
                        count: number;
                        completed: number;
                        avg_success: number | null;
                        total_revenue: number | null;
                        total_cost: number | null;
                        roi: number;
                      }, index: number) => (
                        <div key={index} className="p-3 border border-gray-100 rounded-md">
                          <div className="flex justify-between items-center mb-1">
                            <p className="text-sm font-medium">{type.engagement_type__name}</p>
                            <Badge variant="outline" className="text-xs">
                              {type.engagement_type__category}
                            </Badge>
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 mb-2">
                            <span>{type.count} engagements</span>
                            <span>{type.completed} completed</span>
                            <span>Avg Rating: {type.avg_success?.toFixed(1) || 'N/A'}</span>
                          </div>
                          <Progress
                            value={type.count ? (type.completed / type.count) * 100 : 0}
                            className="h-1.5"
                          />
                        </div>
                      ))}

                      {analytics.engagement_analytics.engagement_types.length === 0 && (
                        <div className="text-center py-6 text-gray-500">
                          <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                          <p>No engagement types found</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Referrals */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Referral Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-4 bg-gray-50 rounded-md">
                        <div className="text-sm text-gray-500 mb-1">Expected Referrals</div>
                        <div className="text-2xl font-bold">{analytics.engagement_analytics.created_metrics.expected_referrals}</div>
                      </div>

                      <div className="p-4 bg-gray-50 rounded-md">
                        <div className="text-sm text-gray-500 mb-1">Actual Referrals</div>
                        <div className="text-2xl font-bold">{analytics.engagement_analytics.created_metrics.actual_referrals}</div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium">Conversion Rate</span>
                        <span className="text-sm font-medium">
                          {Math.round(analytics.engagement_analytics.created_metrics.referral_conversion_rate || 0)}%
                        </span>
                      </div>
                      <Progress
                        value={analytics.engagement_analytics.created_metrics.referral_conversion_rate || 0}
                        className="h-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Partners Tab */}
            <TabsContent value="partners" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Total Partners</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{analytics.partner_analytics.total_partners}</div>
                      <Users className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Active Partners</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold">{analytics.partner_analytics.active_partners}</div>
                      <UserCheck className="h-8 w-8 text-green-500" />
                    </div>
                    <Progress
                      value={analytics.partner_analytics.total_partners ? (analytics.partner_analytics.active_partners / analytics.partner_analytics.total_partners) * 100 : 0}
                      className="h-2 mt-2"
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Partner Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {analytics.partner_analytics.status_breakdown.map((status: {
                        relationship_status: string;
                        count: number;
                        percentage: number;
                      }, index: number) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="text-sm">{status.relationship_status}</span>
                          <span className="text-sm font-medium">{status.count} ({Math.round(status.percentage)}%)</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Partner Engagement */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Partner Engagement</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 font-medium">Partner</th>
                          <th className="text-center py-2 font-medium">Engagements</th>
                          <th className="text-center py-2 font-medium">Completed</th>
                          <th className="text-center py-2 font-medium">Revenue</th>
                          <th className="text-center py-2 font-medium">Referrals</th>
                        </tr>
                      </thead>
                      <tbody>
                        {analytics.partner_analytics.partner_engagement.map((partner: {
                          partner_id: number;
                          partner__business_name: string;
                          engagement_count: number;
                          completed_engagements: number;
                          revenue_generated: number | null;
                          referrals_generated: number | null;
                        }) => (
                          <tr key={partner.partner_id} className="border-b hover:bg-gray-50">
                            <td className="py-2">{partner.partner__business_name}</td>
                            <td className="text-center py-2">{partner.engagement_count}</td>
                            <td className="text-center py-2">{partner.completed_engagements}</td>
                            <td className="text-center py-2">${(partner.revenue_generated || 0).toLocaleString()}</td>
                            <td className="text-center py-2">{partner.referrals_generated}</td>
                          </tr>
                        ))}

                        {analytics.partner_analytics.partner_engagement.length === 0 && (
                          <tr>
                            <td colSpan={5} className="py-4 text-center text-gray-500">
                              No partner engagement data available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            {/* Detailed Analysis Tab */}
            <TabsContent value="detailed" className="space-y-6">
            {/* Engagement Timeline */}
            <EngagementTimeline
              userId={userId}
              startDate={format(startDate, 'yyyy-MM-dd')}
              endDate={format(today, 'yyyy-MM-dd')}
            />

            {/* Goal Progress History */}
            {analytics.performance_trends.goal_trends && analytics.performance_trends.goal_trends.length > 0 ? (
              <GoalProgressHistory
                goalTrends={analytics.performance_trends.goal_trends}
                activeGoals={analytics.goal_analytics.active_goals}
              />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Goal Progress History</CardTitle>
                  <CardDescription>Historical progress for user goals</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                    <h3 className="text-lg font-medium text-gray-700">No Goal History</h3>
                    <p className="text-gray-500 mt-2">
                      No historical goal data is available for this time period.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Partner Engagement Analysis */}
            <PartnerEngagementAnalysis partnerAnalytics={analytics.partner_analytics} />
          </TabsContent>
          </Tabs>

          {/* Team Comparison */}
          {analytics.comparative_analytics && (
            <Card className="shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl">Team Comparison</CardTitle>
                <CardDescription>Performance compared to team averages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Organization Averages */}
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">Organization Averages</h3>
                    <div className="space-y-3">
                      {Object.entries(analytics.comparative_analytics.organization_averages).map(([key, value]) => {
                        const formattedKey = key.split('_').map(word =>
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ');

                        return (
                          <div key={key} className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm">{formattedKey}</span>
                              <span className="text-sm font-medium">{typeof value === 'number' ? value.toFixed(1) : 0}</span>
                            </div>
                            <Progress value={Number(value) || 0} className="h-1.5" />
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* User Percentiles */}
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">Your Performance Percentiles</h3>
                    <div className="space-y-3">
                      {Object.entries(analytics.comparative_analytics.user_percentiles).map(([key, value]) => {
                        const formattedKey = key.replace('_percentile', '').split('_').map(word =>
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ');

                        return (
                          <div key={key} className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm">{formattedKey}</span>
                              <span className="text-sm font-medium">{typeof value === 'number' ? `${value}th Percentile` : '0th Percentile'}</span>
                            </div>
                            <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                              <div
                                style={{ width: `${Number(value) || 0}%` }}
                                className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"
                              ></div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Top Performers */}
                {analytics.comparative_analytics.top_performers.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium mb-3">Top Performers</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {analytics.comparative_analytics.top_performers.map((performer) => (
                        <div key={performer.id} className="p-3 border border-gray-100 rounded-md flex items-center gap-3">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-sm font-semibold text-blue-600">{performer.id}</span>
                          </div>
                          <div>
                            <p className="text-sm font-medium">{performer.first_name} {performer.last_name}</p>
                            <p className="text-xs text-gray-500">Goal Completion: {performer.goal_completion}%</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
