import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import ContactDetailsSheet from '../ContactDetailsSheet';
import { usePartnerContacts } from "@/services/marketingService";
import { Skeleton } from "@/components/ui/skeleton";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Mail, Phone } from "lucide-react";


export default function ContactsTable() {
  const [page, setPage] = useState(1);
  const { data, isLoading, error } = usePartnerContacts(page);

  if (isLoading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-4 text-red-500">
        Error loading contacts. Please try again later.
      </div>
    );
  }

  const contacts = data?.results || [];
  const totalPages = Math.ceil((data?.count || 0) / 10);

  if (contacts.length === 0) {
    return (
      <div className="text-center py-4 text-gray-500">
        No contacts found. Add some contacts to get started.
      </div>
    );
  }

  return (
    <div className="space-y-4 bg-white rounded-lg shadow">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="w-[300px] py-4">CONTACT</TableHead>
              <TableHead>PRACTICE/BUSINESS</TableHead>
              <TableHead>PROVIDER TYPE</TableHead>
              <TableHead>LOCATION</TableHead>
              <TableHead>ASSIGNED TO</TableHead>
              <TableHead>STATUS</TableHead>
              <TableHead className="text-right">ACTIONS</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {contacts.map((contact) => (
              <TableRow
                key={contact.id}
                className="hover:bg-gray-50/50"
              >
                <TableCell className="py-4">
                  <div className="flex items-start gap-3">
                    <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-emerald-700">
                        {`${contact.first_name[0]}${contact.last_name[0]}`}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="font-medium">{`${contact.first_name} ${contact.last_name}`}</span>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        {contact.email && (
                          <div className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            <span>{contact.email}</span>
                          </div>
                        )}
                        {contact.phone && (
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            <span>{contact.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-sm">{contact.business_name || '—'}</span>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary" className="font-normal">
                    {contact.role || 'Other Provider'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="text-sm">{contact.city && contact.state ? `${contact.city}, ${contact.state}` : '—'}</span>
                </TableCell>
                <TableCell>
                  <span className="text-sm">{contact.assigned_to?.name || '—'}</span>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={contact.is_active ? "default" : "secondary"}
                    className="font-normal"
                  >
                    {contact.relationship_status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <ContactDetailsSheet contact={contact} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between p-4 border-t">
        <div className="text-sm text-gray-500">
          Showing {contacts.length} of {data?.count || 0} results
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => p + 1)}
            disabled={!data?.next}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

    </div>
  );
} 