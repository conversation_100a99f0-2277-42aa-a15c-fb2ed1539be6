"use client";

import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  BarChart3,
  Users,
  MessageSquare,
  LayoutDashboard,
  Target,
  ListTodo,
  Settings,
  ChevronLeft,
  ChevronRight,
  Menu
} from "lucide-react";

interface MarketingSidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function MarketingSidebar({ className, ...props }: MarketingSidebarProps) {
  const [isOpen, setIsOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsOpen(false);
      } else {
        setIsOpen(true);
      }
    };

    // Check on initial load
    checkIsMobile();

    // Check on window resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const sections = [
    {
      title: "Calendar",
      items: [
        { icon: Calendar, label: "Full Calendar", href: "/calendar" },
      ]
    },
    {
      title: "Marketing",
      items: [
        { icon: LayoutDashboard, label: "Dashboard", href: "/marketing" },
        { icon: Users, label: "Contacts", href: "/marketing/contacts" },
        { icon: MessageSquare, label: "Engagements", href: "/marketing/engagements" },
        // { icon: UserSearch, label: "Leads", href: "/marketing/leads" },
        // { icon: FileText, label: "Reports", href: "/marketing/reports" },
      ]
    },
    {
      title: "Admin",
      items: [
        { icon: BarChart3, label: "Dashboard", href: "/marketing/admin" },
        { icon: Users, label: "User Analytics", href: "/marketing/user-analytics" },
        { icon: Target, label: "User Goals", href: "/marketing/admin/user-goals" },
        { icon: ListTodo, label: "Engagement Types", href: "/marketing/admin/engagement-types" },
        { icon: Settings, label: "Marketing Goals", href: "/marketing/admin/marketing-goals" },
      ]
    }
  ];

  return (
    <>
      {/* Mobile Hamburger Menu Button - Only visible on mobile */}
      <Button
        variant={isOpen ? "link" : "ghost"}
        className={cn(
          "fixed top-15 z-50 md:hidden",
          isOpen ? "left-[230px]" : "left-3 shadow-md rounded-md border bg-white"
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <ChevronLeft className="h-5 w-5 text-green-600" /> : <Menu className="h-5 w-5 text-green-600" />}
      </Button>

      {/* Backdrop for mobile when sidebar is open */}
      {isOpen && isMobile && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar Container */}
      <div className="relative flex h-full">
        <div
          className={cn(
            "flex-shrink-0 bg-white z-40",
            isMobile && "fixed inset-y-0 left-0",
            !isOpen && !isMobile && "w-[48px]",
            !isOpen && isMobile && "w-0",
            "transition-all duration-300 ease-in-out"
          )}
        >
          {/* Desktop Collapsed State Toggle - Only visible on desktop */}
          {!isOpen && !isMobile && (
            <Button
              variant="link"
              size="icon"
              className="absolute top-4 left-2 z-10 bg-white shadow-sm rounded-md border hidden md:flex"
              onClick={() => setIsOpen(true)}
            >
              <ChevronRight className="h-4 w-4 text-green-600" />
            </Button>
          )}

          <div
            className={cn(
              "bg-white border-r h-full relative transition-all duration-300 ease-in-out",
              isOpen ? "w-[280px]" : "w-0 border-transparent",
              className
            )}
            {...props}
          >
            <div className="flex flex-col h-full">
              {/* Header (only shown on desktop) */}
              {!isMobile && (
                <div className="p-4 flex items-center justify-end">
                  <Button
                    variant="link"
                    onClick={() => setIsOpen(false)}
                    className={cn("", !isOpen && "hidden")}
                  >
                    <ChevronLeft className="h-4 w-4 text-green-600" />
                  </Button>
                </div>
              )}

              {/* Mobile Navigation Header Spacing */}
              {isMobile && <div className="h-16" />}

              {/* Navigation Sections */}
              <nav className={cn("flex-1 overflow-auto px-2", !isOpen && "hidden")}>
                {sections.map((section) => (
                  <div key={section.title} className="mb-6 mt-6">
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 mb-2">
                      {section.title}
                    </h3>
                    {section.items.map((item) => (
                      <Button
                        key={item.label}
                        variant="link"
                        className="w-full justify-start gap-3 mb-1"
                        asChild
                        onClick={() => isMobile && setIsOpen(false)}
                      >
                        <a href={item.href}>
                          <item.icon className="h-4 w-4" />
                          {item.label}
                        </a>
                      </Button>
                    ))}
                    {section.title !== "Admin" && <Separator className="my-4" />}
                  </div>
                ))}
              </nav>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}