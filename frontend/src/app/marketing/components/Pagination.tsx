import {
    Pa<PERSON>ation,
    PaginationContent,
    Pa<PERSON><PERSON><PERSON><PERSON>psis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface PaginationControlsProps {
    currentPage: number;
    pageSize: number;
    totalItems: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (value: string) => void;
}

export default function PaginationControls({
    currentPage,
    pageSize,
    totalItems,
    onPageChange,
    onPageSizeChange
}: PaginationControlsProps) {
    const totalPages = Math.ceil(totalItems / pageSize);

    return (
        <div className="flex items-center justify-between py-4">
            <div className="flex-1 max-w-[400px]">
                <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 whitespace-nowrap">Items per page:</span>
                    <Select
                        value={pageSize.toString()}
                        onValueChange={onPageSizeChange}
                    >
                        <SelectTrigger className="w-[70px]">
                            <SelectValue placeholder={pageSize} />
                        </SelectTrigger>
                        <SelectContent>
                            {[10, 20, 25, 100].map((size) => (
                                <SelectItem key={size} value={size.toString()}>
                                    {size}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    <span className="text-sm text-gray-600 whitespace-nowrap">
                        {`${((currentPage - 1) * pageSize) + 1}-${Math.min(currentPage * pageSize, totalItems)} of ${totalItems}`}
                    </span>
                </div>
            </div>

            <div className="flex-1 max-w-[400px]">
                <Pagination>
                    <PaginationContent className="flex justify-end">
                        <PaginationItem>
                            <PaginationPrevious
                                onClick={() => onPageChange(currentPage - 1)}
                                className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                            />
                        </PaginationItem>

                        {[...Array(totalPages)].map((_, index) => {
                            const page = index + 1;
                            if (
                                page === 1 ||
                                page === totalPages ||
                                (page >= currentPage - 1 && page <= currentPage + 1)
                            ) {
                                return (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            onClick={() => onPageChange(page)}
                                            isActive={page === currentPage}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            } else if (
                                page === currentPage - 2 ||
                                page === currentPage + 2
                            ) {
                                return (
                                    <PaginationItem key={page}>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                );
                            }
                            return null;
                        })}

                        <PaginationItem>
                            <PaginationNext
                                onClick={() => onPageChange(currentPage + 1)}
                                className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                            />
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>
            </div>
        </div>
    );
} 