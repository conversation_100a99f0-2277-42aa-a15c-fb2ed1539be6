"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DateRangeFilterProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  dateField: string;
  onDateFieldChange: (field: string) => void;
  disabled?: boolean;
}

export function DateRangeFilter({
  dateRange,
  onDateRangeChange,
  dateField,
  onDateFieldChange,
  disabled = false,
}: DateRangeFilterProps) {
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on initial load
    checkIsMobile();

    // Check on window resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const dateFieldOptions = [
    { value: "date", label: "Engagement Date" },
    { value: "follow_up_date", label: "Follow-up Date" },
    { value: "created_at", label: "Created Date" },
  ];

  return (
    <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex gap-2 items-center'}`}>
      <Select
        value={dateField}
        onValueChange={onDateFieldChange}
        disabled={disabled}
      >
        <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
          <SelectValue placeholder="Select Date Field" />
        </SelectTrigger>
        <SelectContent>
          {dateFieldOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              `${isMobile ? 'w-full' : 'w-[230px]'} justify-start text-left font-normal`,
              !dateRange && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange?.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, "MMM dd, yyyy")} -{" "}
                  {format(dateRange.to, "MMM dd, yyyy")}
                </>
              ) : (
                format(dateRange.from, "MMM dd, yyyy")
              )
            ) : (
              <span>Select date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            selected={dateRange}
            onSelect={onDateRangeChange}
            numberOfMonths={isMobile ? 1 : 2}
          />
          <div className="p-3 border-t border-border">
            <div className="grid grid-cols-2 gap-2 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Set date range to last 7 days
                  const today = new Date();
                  const sevenDaysAgo = new Date();
                  sevenDaysAgo.setDate(today.getDate() - 7);
                  onDateRangeChange({
                    from: sevenDaysAgo,
                    to: today,
                  });
                }}
              >
                Last 7 Days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Set date range to last 30 days
                  const today = new Date();
                  const thirtyDaysAgo = new Date();
                  thirtyDaysAgo.setDate(today.getDate() - 30);
                  onDateRangeChange({
                    from: thirtyDaysAgo,
                    to: today,
                  });
                }}
              >
                Last 30 Days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Set date range to this month
                  const today = new Date();
                  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                  onDateRangeChange({
                    from: firstDayOfMonth,
                    to: today,
                  });
                }}
              >
                This Month
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Set date range to last month
                  const today = new Date();
                  const firstDayLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                  const lastDayLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                  onDateRangeChange({
                    from: firstDayLastMonth,
                    to: lastDayLastMonth,
                  });
                }}
              >
                Last Month
              </Button>
            </div>
            <div className="flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDateRangeChange(undefined)}
              >
                Clear
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
