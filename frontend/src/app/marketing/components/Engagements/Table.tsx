"use client";

import { useState, use<PERSON>em<PERSON> } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Download, Edit, Filter, Loader2, Trash2 } from "lucide-react";
import { AddEditEngagement } from './AddEditEngagement';
import { Rating } from "@/components/ui/rating";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { useEngagements, useDeleteEngagement, useEngagementTypesByCategory, useUpdateEngagementRating, useUpdateEngagementStatus } from '@/services/marketingService';
import { formatDate } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { Engagement, PartnerContact } from "@/type/marketing";
import { DateRangeFilter } from "./DateRangeFilter";
import { UserSelector } from "./UserSelector";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";

export function EngagementsTable() {
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [selectedDateRange, setSelectedDateRange] = useState("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [engagementToDelete, setEngagementToDelete] = useState<number | null>(null);
  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [userField, setUserField] = useState("users");
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [dateField, setDateField] = useState("date");
  const { toast } = useToast();

  // Create filters object based on selected values
  const filters = useMemo(() => {
    type FilterObj = {
      search?: string;
      status?: string;
      engagement_type?: string | number;
      start_date?: string;
      end_date?: string;
      date_field?: string;
      created_by_users?: number[];
      attendees?: number[];
      users?: number[];
      meeting_start_date?: string;
      meeting_end_date?: string;
      followup_start_date?: string;
      followup_end_date?: string;
      reminder_start_date?: string;
      reminder_end_date?: string;
      [key: string]: string | number | number[] | undefined;
    };

    const filterObj: FilterObj = {};

    // Add search filter
    if (searchQuery.trim()) {
      filterObj.search = searchQuery;
    }

    // Add status filter
    if (selectedStatus !== "all") {
      filterObj.status = selectedStatus;
    }

    // Add type filter
    if (selectedType !== "all") {
      filterObj.engagement_type = selectedType;
    }

    // Add user filter based on selected user field
    if (selectedUsers.length > 0) {
      filterObj[userField] = selectedUsers;
    }

    // Add date range filter from predefined options
    if (selectedDateRange !== "all") {
      const now = new Date();
      switch (selectedDateRange) {
        case "today":
          filterObj.start_date = now.toISOString().split('T')[0];
          filterObj.end_date = now.toISOString().split('T')[0];
          break;
        case "this_week":
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          filterObj.start_date = weekStart.toISOString().split('T')[0];
          filterObj.end_date = now.toISOString().split('T')[0];
          break;
        case "this_month":
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
          filterObj.start_date = monthStart.toISOString().split('T')[0];
          filterObj.end_date = now.toISOString().split('T')[0];
          break;
        // Add more date range options as needed
      }
    }
    // Add custom date range filter
    else if (dateRange?.from) {
      // Format dates for API
      const formatDateForAPI = (date: Date) => format(date, "yyyy-MM-dd");

      // Set the appropriate date field based on the selected date field
      if (dateField === "date") {
        filterObj.meeting_start_date = formatDateForAPI(dateRange.from);
        if (dateRange.to) {
          filterObj.meeting_end_date = formatDateForAPI(dateRange.to);
        }
      } else if (dateField === "follow_up_date") {
        filterObj.followup_start_date = formatDateForAPI(dateRange.from);
        if (dateRange.to) {
          filterObj.followup_end_date = formatDateForAPI(dateRange.to);
        }
      } else if (dateField === "created_at") {
        filterObj.start_date = formatDateForAPI(dateRange.from);
        if (dateRange.to) {
          filterObj.end_date = formatDateForAPI(dateRange.to);
        }
      }
    }

    return filterObj;
  }, [searchQuery, selectedStatus, selectedType, selectedDateRange, selectedUsers, userField, dateRange, dateField]);

  // Fetch data using the filters
  const { data: engagements, isLoading, error } = useEngagements(page, filters);
  const { data: engagementTypes } = useEngagementTypesByCategory();
  const deleteEngagement = useDeleteEngagement();

  // Add the rating update mutation at the component level
  const updateRating = useUpdateEngagementRating();

  // Add the status update mutation
  const updateStatus = useUpdateEngagementStatus();

  // Status options
  const statusOptions = [
    { value: "all", label: "All Statuses" },
    { value: "PLANNED", label: "Planned" },
    { value: "COMPLETED", label: "Completed" },
    { value: "CANCELLED", label: "Cancelled" },
    { value: "RESCHEDULED", label: "Rescheduled" },
    { value: "NO_SHOW", label: "No Show" }
  ];

  // Date range options
  const dateRangeOptions = [
    { value: "all", label: "All Time" },
    { value: "today", label: "Today" },
    { value: "this_week", label: "This Week" },
    { value: "this_month", label: "This Month" }
  ];

  // Get flattened engagement types for filter
  const typeOptions = useMemo(() => {
    const options = [{ value: "all", label: "All Types" }];

    if (engagementTypes) {
      Object.values(engagementTypes).flat().forEach(type => {
        options.push({
          value: type.id.toString(),
          label: type.name
        });
      });
    }

    return options;
  }, [engagementTypes]);


  // No longer need to look up partner name as it's included in the engagement object

  // Helper function to get engagement type name
  const getEngagementTypeName = (typeId: number) => {
    if (!engagementTypes) return "Unknown";
    for (const types of Object.values(engagementTypes)) {
      const type = types.find(t => t.id === typeId);
      if (type) return type.name;
    }
    return "Unknown";
  };

  const handleDeleteClick = async (engagementId: number) => {
    try {
      await deleteEngagement.mutateAsync(engagementId);
      toast({
        title: "Success",
        description: "Engagement deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting engagement:", error); // noqa: B107
      toast({
        title: "Error",
        description: "Failed to delete engagement",
        variant: "destructive",
      });
    }
  };

  const handleExportCSV = () => {
    if (!engagements?.results) return;

    // Convert engagements data to CSV format
    const csvContent = engagements.results.map((engagement: Engagement) => ({
      Contact: engagement.partner_name || 'Unknown',
      PartnerType: engagement.partner_type_display || engagement.partner_type || '',
      Type: engagement.engagement_type_name || getEngagementTypeName(engagement.engagement_type),
      Status: engagement.status,
      Date: engagement.date,
      Duration: engagement.duration || '',
      Location: engagement.location || '',
      Notes: engagement.notes || '',
      Outcomes: engagement.outcomes || '',
      SuccessRating: engagement.success_rating || '',
      CreatedAt: engagement.created_at,
      CreatedBy: engagement.created_by?.name || 'System'
    }));

    // Create CSV string
    const headers = Object.keys(csvContent[0]);
    const csvString = [
      headers.join(','),
      ...csvContent.map(row => headers.map(header => {
        const value = row[header as keyof typeof row];
        const escapedValue = String(value).replace(/"/g, '""');
        return `"${escapedValue}"`;
      }).join(','))
    ].join('\n');

    // Create and trigger download
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `engagements_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={() => {
          if (engagementToDelete) {
            handleDeleteClick(engagementToDelete);
            setEngagementToDelete(null);
          }
        }}
        text="Engagement"
      />

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 flex flex-wrap gap-4 items-center justify-between">
          <div className="flex flex-1 gap-4 items-center flex-wrap">
            <div className="relative max-w-[300px]">
              <Input
                placeholder="Search engagements..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-[300px] pr-8"
              />
              {searchQuery && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  onClick={() => setSearchQuery("")}
                  aria-label="Clear search"
                >
                  ×
                </button>
              )}
            </div>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                {typeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedDateRange} onValueChange={setSelectedDateRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                {dateRangeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              className={`gap-2 ${advancedFiltersOpen ? 'bg-gray-100 border-gray-400' : ''}`}
              onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}
            >
              <Filter className="h-4 w-4" />
              {advancedFiltersOpen ? "Hide Advanced" : "Advanced Filters"}
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              variant="link"
              className="text-red-600 p-0 decoration-none"
              onClick={() => {
                setSearchQuery("");
                setSelectedStatus("all");
                setSelectedType("all");
                setSelectedDateRange("all");
                setSelectedUsers([]);
                setDateRange(undefined);
                setDateField("date");
              }}
            >
              Clear
            </Button>
            <AddEditEngagement />
            <Button variant="ghost" size="icon" onClick={handleExportCSV}>
              <Download className="h-4 w-4 text-green-600" />
            </Button>
          </div>

          {/* Advanced Filters Section */}
          {advancedFiltersOpen && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h2 className="text-lg font-medium mb-4">Advanced Filters</h2>
              <div className="flex flex-wrap gap-6">
                {/* User Filter */}
                <div className="flex-1 min-w-[300px]">

                  <h3 className="text-sm font-medium mb-2">Filter by Users</h3>
                  <p className="text-xs text-muted-foreground mb-2">
                    Select users to filter by their role in engagements
                  </p>
                  <UserSelector
                    selectedUsers={selectedUsers}
                    onSelectionChange={setSelectedUsers}
                    placeholder="Select users..."
                    userField={userField}
                    onUserFieldChange={setUserField}
                  />
                </div>

                {/* Date Range Filter */}
                <div className="flex-1 min-w-[300px]">
                  <h3 className="text-sm font-medium mb-2">Filter by Date Range</h3>
                  <p className="text-xs text-muted-foreground mb-2">
                    Select a date field and range to filter by
                  </p>
                  <DateRangeFilter
                    dateRange={dateRange}
                    onDateRangeChange={setDateRange}
                    dateField={dateField}
                    onDateFieldChange={setDateField}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="border-t">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50/50">
                <TableHead className="w-[180px]">CONTACT</TableHead>
                <TableHead className="w-[180px]">BUSINESS NAME</TableHead>
                <TableHead className="w-[150px]">TYPE</TableHead>
                <TableHead className="w-[100px]">STATUS</TableHead>
                <TableHead className="w-[100px]">DATE</TableHead>
                <TableHead className="w-[120px]">FOLLOW-UP</TableHead>
                <TableHead className="w-[120px]">LOCATION</TableHead>
                {/* <TableHead className="w-[120px]">EMPLOYEE</TableHead> */}
                <TableHead className="w-[120px]">ATTENDEES</TableHead>
                <TableHead className="w-[120px]">RATING</TableHead>
                <TableHead className="w-[200px]">NOTES</TableHead>
                <TableHead className="w-[150px]">OUTCOMES</TableHead>
                <TableHead className="w-[120px]">CREATED BY</TableHead>
                <TableHead className="w-[100px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={12} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading engagements...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={12} className="text-center py-8 text-red-500">
                    Error loading engagements. Please try again.
                  </TableCell>
                </TableRow>
              ) : !engagements?.results?.length ? (
                <TableRow>
                  <TableCell colSpan={12} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center text-sm text-gray-500">
                      <p>No engagements found</p>
                      <p>Add some engagements to get started</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                engagements.results.map((engagement: Engagement) => (
                  <TableRow key={engagement.id}>
                    <TableCell className="font-medium">
                      {(engagement?.partner as PartnerContact)?.first_name} {(engagement?.partner as PartnerContact)?.last_name}
                    </TableCell>
                    <TableCell className="font-medium">
                      {(engagement?.partner as PartnerContact)?.business_name || "-"}
                    </TableCell>
                    <TableCell>
                      {engagement?.engagement_type_name}
                    </TableCell>
                    <TableCell>
                      <Select
                        value={engagement.status}
                        onValueChange={(newStatus) => {
                          updateStatus.mutate({
                            engagementId: engagement.id,
                            status: newStatus
                          });
                        }}
                        disabled={updateStatus.isPending}
                      >
                        <SelectTrigger className="h-8 w-[130px] px-2">
                          <SelectValue>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${engagement.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                engagement.status === 'PLANNED' ? 'bg-blue-100 text-blue-800' :
                                  engagement.status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                                    engagement.status === 'RESCHEDULED' ? 'bg-yellow-100 text-yellow-800' :
                                      engagement.status === 'NO_SHOW' ? 'bg-gray-100 text-gray-800' :
                                        'bg-gray-100 text-gray-800'
                              }`}>
                              {engagement.status}
                            </span>
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {statusOptions.filter(option => option.value !== "all").map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${option.value === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                  option.value === 'PLANNED' ? 'bg-blue-100 text-blue-800' :
                                    option.value === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                                      option.value === 'RESCHEDULED' ? 'bg-yellow-100 text-yellow-800' :
                                        option.value === 'NO_SHOW' ? 'bg-gray-100 text-gray-800' :
                                          'bg-gray-100 text-gray-800'
                                }`}>
                                {option.label.replace("All Statuses", "")}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{formatDate(engagement.date)}</TableCell>
                    <TableCell>
                      {engagement.follow_up_date ? formatDate(engagement.follow_up_date) : "-"}
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate block max-w-[100px]">
                              {engagement.location || engagement.virtual_meeting_platform || "-"}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p><strong>Type:</strong> {engagement.location_type || "Not specified"}</p>
                            {engagement.location && <p><strong>Location:</strong> {engagement.location}</p>}
                            {engagement.virtual_meeting_platform && (
                              <>
                                <p><strong>Platform:</strong> {engagement.virtual_meeting_platform}</p>
                                {engagement.virtual_meeting_link && (
                                  <p><strong>Link:</strong> {engagement.virtual_meeting_link}</p>
                                )}
                              </>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate block max-w-[100px]">
                              {engagement.attendees_names?.length
                                ? `${engagement.attendees_names[0]} ${engagement.attendees_names.length > 1 ? `+${engagement.attendees_names.length - 1}` : ''}`
                                : "-"}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            {engagement.attendees_names?.length ? (
                              <ul className="list-disc pl-4 space-y-1">
                                {engagement.attendees_names.map((name, index) => (
                                  <li key={index}>{name}</li>
                                ))}
                              </ul>
                            ) : (
                              // <p>No attendees</p>
                              <p>No attendees</p>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <Rating
                        value={engagement.success_rating || 0}
                        onChange={(value) => updateRating.mutate({
                          engagementId: engagement.id,
                          rating: value
                        })}
                        disabled={updateRating.isPending}
                      />
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate block max-w-[180px]">
                              {engagement.notes || "-"}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-[300px]">
                            <p>{engagement.notes || "No notes available"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate block max-w-[130px]">
                              {engagement.outcomes || "-"}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-[300px]">
                            <p><strong>Outcomes:</strong> {engagement.outcomes || "None"}</p>
                            {engagement.next_steps && (
                              <p><strong>Next Steps:</strong> {engagement.next_steps}</p>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{engagement.created_by?.name || 'System'}</span>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(engagement.created_at)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <AddEditEngagement isEdit={true} initialData={engagement}>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4 text-green-600" />
                          </Button>
                        </AddEditEngagement>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => {
                            setEngagementToDelete(engagement.id);
                            setDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        <div className="p-4 flex items-center justify-between border-t">
          <div className="text-sm text-gray-500">
            Showing {engagements?.results?.length || 0} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {page} of {Math.ceil((engagements?.count || 0) / 10)}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => p + 1)}
              disabled={!engagements?.next}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}