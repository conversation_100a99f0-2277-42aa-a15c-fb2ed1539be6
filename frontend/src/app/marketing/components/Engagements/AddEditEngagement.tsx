"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Footer,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Edit, Plus } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { Rating } from "@/components/ui/rating";
import { Engagement } from "@/type/marketing";
import { usePartnerContacts, useEngagementTypesByCategory, useCreateEngagement, useUpdateEngagement } from '@/services/marketingService';
import { OrganizationUser, useOrganizationUsers } from '@/services/organizationService';
import { AxiosError } from "axios";
import { RequiredLabel } from "@/components/ui/required-label";
import { formatDateToUS } from "@/lib/utils";
import { format as formatDate } from "date-fns";

const engagementFormSchema = z.object({
    partner: z.number().min(1, "Partner is required"),
    engagement_type: z.number().optional(),
    status: z.string().optional(),
    date: z.string().optional(),
    notes: z.string().optional(),
    success_rating: z.number().optional(),
    attendees: z.array(z.number()).optional(),
    // Optional fields
    duration: z.number().optional(),
    follow_up_date: z.string().optional(),
    location_type: z.string().optional(),
    location: z.string().optional(),
    virtual_meeting_link: z.string().optional(),
    virtual_meeting_platform: z.string().optional(),
    agenda: z.string().optional(),
    follow_up_notes: z.string().optional(),
    outcomes: z.string().optional(),
    next_steps: z.string().optional(),
    // Additional fields from Engagement interface
    reminder_sent: z.boolean().optional(),
    reminder_sent_date: z.string().optional(),
    budgeted_cost: z.number().optional(),
    actual_cost: z.number().optional(),
    cost_notes: z.string().optional(),
    metrics_notes: z.string().optional(),
    expected_referrals: z.number().optional(),
    actual_referrals: z.number().optional(),
    expected_revenue: z.number().optional(),
    actual_revenue: z.number().optional()
});

type EngagementFormValues = z.infer<typeof engagementFormSchema>;

interface AddEditEngagementProps {
    contactId?: string;
    isEdit?: boolean;
    initialData?: Engagement;
    children?: React.ReactNode;
}


// Define status options
const STATUS_OPTIONS = [
    { value: "PLANNED", label: "Planned" },
    { value: "COMPLETED", label: "Completed" },
    { value: "CANCELLED", label: "Cancelled" },
    { value: "RESCHEDULED", label: "Rescheduled" },
    { value: "NO_SHOW", label: "No Show" }
] as const;

// Helper function to format date for API submission
const formatDateForAPI = (dateString: string | undefined): string => {
    if (!dateString) return '';
    try {
        // If the date is already in yyyy-MM-dd format, return it as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            return dateString;
        }

        // Otherwise, parse and format it
        return formatDate(new Date(dateString), 'yyyy-MM-dd');
    } catch (error) {
        console.error('Error formatting date:', error);
        return '';
    }
};

export function AddEditEngagement({
    contactId,
    isEdit = false,
    initialData,
    children,
}: AddEditEngagementProps) {
    const [open, setOpen] = useState(false);
    const { toast } = useToast();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [page, setPage] = useState(1);
    const [searchTerm, setSearchTerm] = useState("");
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

    // Debug logs for props and state
    console.log('AddEditEngagement Props:', { isEdit, initialData, contactId });

    // Add debounce effect for search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 300);
        return () => clearTimeout(timer);
    }, [searchTerm]);

    // Reset page when search term changes
    useEffect(() => {
        setPage(1);
    }, [debouncedSearchTerm]);

    // Fetch data using hooks with search parameter
    const { data: partnersData, isLoading: isLoadingPartners } = usePartnerContacts(
        page,
        { search: searchTerm }
    );
    const { data: engagementTypesByCategory } = useEngagementTypesByCategory();
    const [userSearchTerm, setUserSearchTerm] = useState("");
    const { data: users } = useOrganizationUsers(userSearchTerm);
    const createEngagement = useCreateEngagement();
    const updateEngagement = useUpdateEngagement(initialData?.id || 0);

    // Debug log for update engagement hook
    console.log('Update engagement hook initialized with ID:', initialData?.id);

    // Debug log for engagement ID
    console.log('Engagement ID for update:', initialData?.id);

    // Flatten engagement types for easier use in select
    const engagementTypes = engagementTypesByCategory ?
        Object.values(engagementTypesByCategory).flat() : [];

    // Helper function to format user name
    const formatUserName = (user?: OrganizationUser) => {
        if (!user) return "Unknown";
        return user.first_name && user.last_name ?
            `${user.first_name} ${user.last_name}` :
            user.email || user.username || "Unknown";
    };

    const form = useForm<EngagementFormValues>({
        resolver: zodResolver(engagementFormSchema),
        defaultValues: {
            partner: typeof initialData?.partner === 'object' ? initialData.partner.id : (initialData?.partner || (contactId ? Number(contactId) : 0)),
            engagement_type: initialData?.engagement_type || 0,
            status: initialData?.status || "",
            date: initialData?.date || "",
            notes: initialData?.notes || "",
            success_rating: initialData?.success_rating || 0,
            attendees: initialData?.attendees || [],
            duration: initialData?.duration || 0,
            follow_up_date: initialData?.follow_up_date || "",
            location_type: initialData?.location_type || "",
            location: initialData?.location || "",
            virtual_meeting_link: initialData?.virtual_meeting_link || "",
            virtual_meeting_platform: initialData?.virtual_meeting_platform || "",
            agenda: initialData?.agenda || "",
            follow_up_notes: initialData?.follow_up_notes || "",
            outcomes: initialData?.outcomes || "",
            next_steps: initialData?.next_steps || "",
            reminder_sent: initialData?.reminder_sent || false,
            reminder_sent_date: initialData?.reminder_sent_date || "",
            budgeted_cost: initialData?.budgeted_cost || 0,
            actual_cost: initialData?.actual_cost || 0,
            cost_notes: initialData?.cost_notes || "",
            metrics_notes: initialData?.metrics_notes || "",
            expected_referrals: initialData?.expected_referrals || 0,
            actual_referrals: initialData?.actual_referrals || 0,
            expected_revenue: initialData?.expected_revenue || 0,
            actual_revenue: initialData?.actual_revenue || 0
        },
    });

    // Reset form when initialData changes
    useEffect(() => {
        if (initialData && open) {
            console.log('Resetting form with initialData:', initialData);
            // Ensure partner is always a number
            const partnerId = typeof initialData.partner === 'object'
                ? initialData.partner.id
                : (initialData.partner || (contactId ? parseInt(contactId) : 0));

            // Format dates to ensure they display correctly in the date input fields
            // Date inputs require yyyy-MM-dd format
            const formatDateForInput = (dateString: string | undefined): string => {
                if (!dateString) return "";
                // If already in yyyy-MM-dd format, return as is
                if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                    return dateString;
                }
                try {
                    // Parse and format to yyyy-MM-dd
                    return formatDate(new Date(dateString), 'yyyy-MM-dd');
                } catch (error) {
                    console.error('Error formatting date for input:', error);
                    return "";
                }
            };

            const formValues = {
                partner: partnerId,
                engagement_type: initialData.engagement_type || 0,
                status: initialData.status || "",
                date: formatDateForInput(initialData.date),
                notes: initialData.notes || "",
                success_rating: initialData.success_rating || 0,
                attendees: initialData.attendees || [],
                duration: initialData.duration || 0,
                follow_up_date: formatDateForInput(initialData.follow_up_date),
                location_type: initialData.location_type || "",
                location: initialData.location || "",
                virtual_meeting_link: initialData.virtual_meeting_link || "",
                virtual_meeting_platform: initialData.virtual_meeting_platform || "",
                agenda: initialData.agenda || "",
                follow_up_notes: initialData.follow_up_notes || "",
                outcomes: initialData.outcomes || "",
                next_steps: initialData.next_steps || "",
                reminder_sent: initialData.reminder_sent || false,
                reminder_sent_date: formatDateForInput(initialData.reminder_sent_date),
                budgeted_cost: initialData.budgeted_cost || 0,
                actual_cost: initialData.actual_cost || 0,
                cost_notes: initialData.cost_notes || "",
                metrics_notes: initialData.metrics_notes || "",
                expected_referrals: initialData.expected_referrals || 0,
                actual_referrals: initialData.actual_referrals || 0,
                expected_revenue: initialData.expected_revenue || 0,
                actual_revenue: initialData.actual_revenue || 0
            };
            console.log('Form reset values:', formValues);
            form.reset(formValues);
        }
    }, [initialData, form, contactId, open]);

    // Load more results when scrolling
    const handleLoadMore = useCallback(() => {
        if (partnersData?.next) {
            setPage(prevPage => prevPage + 1);
        }
    }, [partnersData?.next]);

    const onSubmitForm = async (data: EngagementFormValues) => {
        try {
            if (isSubmitting) return;
            setIsSubmitting(true);

            console.log('Formatting data for submission');
            // Format the data for submission, ensuring no null values
            const formattedData = {
                partner: data.partner,
                engagement_type: data.engagement_type || undefined,
                status: data.status || undefined,
                date: data.date ? formatDateForAPI(data.date) : undefined,
                notes: data.notes || undefined,
                success_rating: data.success_rating || undefined,
                attendees: data.attendees || [],
                duration: data.duration || undefined,
                follow_up_date: data.follow_up_date ? formatDateForAPI(data.follow_up_date) : undefined,
                location_type: data.location_type || undefined,
                location: data.location || undefined,
                virtual_meeting_link: data.virtual_meeting_link || undefined,
                virtual_meeting_platform: data.virtual_meeting_platform || undefined,
                agenda: data.agenda || undefined,
                follow_up_notes: data.follow_up_notes || undefined,
                outcomes: data.outcomes || undefined,
                next_steps: data.next_steps || undefined,
                reminder_sent: data.reminder_sent || undefined,
                reminder_sent_date: data.reminder_sent_date ? formatDateForAPI(data.reminder_sent_date) : undefined,
                budgeted_cost: data.budgeted_cost || undefined,
                actual_cost: data.actual_cost || undefined,
                cost_notes: data.cost_notes || undefined,
                metrics_notes: data.metrics_notes || undefined,
                expected_referrals: data.expected_referrals || undefined,
                actual_referrals: data.actual_referrals || undefined,
                expected_revenue: data.expected_revenue || undefined,
                actual_revenue: data.actual_revenue || undefined
            };

            console.log('Sending to API:', formattedData);

            if (isEdit && initialData?.id) {
                console.log('Updating existing engagement with ID:', initialData.id);
                try {
                    const updateData = {
                        ...formattedData,
                        id: initialData.id
                    };
                    console.log('Calling updateEngagement.mutateAsync with:', updateData);
                    const result = await updateEngagement.mutateAsync(updateData);
                    console.log('Update successful, result:', result);
                    // Close dialog and reset form after a short delay to ensure UI updates
                    setTimeout(() => {
                        setOpen(false);
                        form.reset();
                    }, 500);
                    toast({
                        title: "Success",
                        description: "Engagement updated successfully"
                    });
                } catch (error) {
                    console.error('Update error:', error);
                    const axiosError = error as AxiosError<{ detail?: string }>;
                    if (axiosError.response?.data?.detail) {
                        console.error('API error response:', axiosError.response.data);
                    }
                    const errorMsg = error instanceof Error ? error.message : 'Failed to update engagement';
                    toast({
                        title: "Error",
                        description: errorMsg,
                        variant: "destructive"
                    });
                }
            } else {
                try {
                    await createEngagement.mutateAsync(formattedData);
                    // Close dialog and reset form after a short delay to ensure UI updates
                    setTimeout(() => {
                        setOpen(false);
                        form.reset();
                    }, 500);
                    toast({
                        title: "Success",
                        description: "Engagement created successfully"
                    });
                } catch (error) {
                    console.error('Create error:', error);
                    const errorMsg = error instanceof Error ? error.message : 'Failed to create engagement';
                    toast({
                        title: "Error",
                        description: errorMsg,
                        variant: "destructive"
                    });
                }
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    // Add form validation logging
    useEffect(() => {
        const subscription = form.watch((value) => {
            console.log('Form values changed:', value);
        });
        return () => subscription.unsubscribe();
    }, [form]);



    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {children || (
                    isEdit ? (
                        <Button
                            variant="link"
                            className="text-green-600"
                            size="sm"
                        >
                            <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                        </Button>
                    ) : (
                        <Button
                            variant="link"
                            className="text-green-600"
                        >
                            <Plus className="h-4 w-4" />
                            Add Engagement
                        </Button>
                    )
                )}
            </DialogTrigger>

            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle>
                        {isEdit ? "Edit Engagement" : "New Engagement"}
                    </DialogTitle>
                    {isEdit && initialData?.created_by && (
                        <div className="text-sm text-gray-500">
                            Created by {initialData.created_by.name} on {formatDateToUS(initialData.created_at)}
                        </div>
                    )}
                </DialogHeader>

                <Form {...form}>
                    <form
                        onSubmit={(e) => {
                            e.preventDefault();
                            console.log('Form submission started');
                            form.handleSubmit(
                                (data) => {
                                    console.log('Form validation passed, calling onSubmitForm with:', data);
                                    // Ensure dates are in the correct format
                                    if (data.date) {
                                        data.date = formatDateForAPI(data.date);
                                    }
                                    if (data.follow_up_date) {
                                        data.follow_up_date = formatDateForAPI(data.follow_up_date);
                                    }
                                    if (data.reminder_sent_date) {
                                        data.reminder_sent_date = formatDateForAPI(data.reminder_sent_date);
                                    }
                                    onSubmitForm(data);
                                },
                                (errors) => {
                                    console.error('Form validation failed:', errors);
                                }
                            )(e);
                        }}
                        className="space-y-6"
                    >
                        <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-6">
                            {/* Contact Selection with Search */}
                            <FormField
                                control={form.control}
                                name="partner"
                                render={({ field }) => (
                                    <FormItem>
                                        <RequiredLabel>Contact</RequiredLabel>
                                        <div className="space-y-2">
                                            <Select
                                                onValueChange={(value) => field.onChange(Number(value))}
                                                value={field.value?.toString()}
                                            >
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select contact" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent
                                                    className="max-h-[300px] overflow-auto"
                                                    searchable
                                                    onSearchChange={(value) => {
                                                        setSearchTerm(value);
                                                        setPage(1); // Reset page when search changes
                                                    }}
                                                    onScroll={(e) => {
                                                        const target = e.target as HTMLDivElement;
                                                        if (target.scrollTop + target.clientHeight >= target.scrollHeight - 20) {
                                                            handleLoadMore();
                                                        }
                                                    }}
                                                >
                                                    {isLoadingPartners ? (
                                                        <div className="p-2 text-center text-gray-500">Loading contacts...</div>
                                                    ) : partnersData?.results?.length === 0 ? (
                                                        <div className="p-2 text-center text-gray-500">No contacts found</div>
                                                    ) : (
                                                        <>
                                                            {partnersData?.results?.map((partner) => (
                                                                <SelectItem
                                                                    key={partner.id}
                                                                    value={partner.id.toString()}
                                                                    className="hover:bg-blue-50"
                                                                >
                                                                    {`${partner.first_name} ${partner.last_name}`}
                                                                    {partner.business_name && (
                                                                        <span className="text-gray-500"> - {partner.business_name}</span>
                                                                    )}
                                                                </SelectItem>
                                                            ))}
                                                            {partnersData?.next && (
                                                                <div className="p-2 text-center text-blue-500 cursor-pointer" onClick={handleLoadMore}>
                                                                    Load more contacts...
                                                                </div>
                                                            )}
                                                        </>
                                                    )}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Engagement Type */}
                            <FormField
                                control={form.control}
                                name="engagement_type"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Type</FormLabel>
                                        <Select
                                            onValueChange={(value) => field.onChange(Number(value))}
                                            value={field.value?.toString()}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select type" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent searchable>
                                                {engagementTypes.map((type) => (
                                                    <SelectItem
                                                        key={type.id}
                                                        value={type.id.toString()}
                                                    >
                                                        {type.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Status */}
                            <FormField
                                control={form.control}
                                name="status"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Status</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value || ""}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select status" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent searchable>
                                                {field.value && (
                                                    <Button
                                                        variant="ghost"
                                                        className="mt-2 w-full justify-center text-sm"
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            field.onChange("");
                                                        }}
                                                    >
                                                        Clear Selection
                                                    </Button>
                                                )}
                                                {STATUS_OPTIONS.map((status) => (
                                                    <SelectItem
                                                        key={status.value}
                                                        value={status.value}
                                                    >
                                                        {status.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Date */}
                            <FormField
                                control={form.control}
                                name="date"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Date</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="date"
                                                {...field}
                                                value={field.value || ""}
                                                onChange={(e) => {
                                                    console.log('Date changed to:', e.target.value);
                                                    field.onChange(e.target.value);
                                                }}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Rating */}
                            <FormField
                                control={form.control}
                                name="success_rating"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Rating</FormLabel>
                                        <FormControl>
                                            <Rating
                                                value={field.value || 0}
                                                onChange={field.onChange}
                                                size="lg"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Notes */}
                            <FormField
                                control={form.control}
                                name="notes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Notes</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                {...field}
                                                placeholder="Enter notes about the engagement"
                                                className="min-h-[100px]"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Attendees */}
                            <FormField
                                control={form.control}
                                name="attendees"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Attendees</FormLabel>
                                        <Select
                                            onValueChange={(value) => {
                                                const userId = Number(value);
                                                const currentAttendees = field.value || [];
                                                if (!currentAttendees.includes(userId)) {
                                                    field.onChange([...currentAttendees, userId]);
                                                }
                                            }}
                                            value=""
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Add attendees..." />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent searchable onSearchChange={(value) => setUserSearchTerm(value)}>
                                                {field.value && field.value.length > 0 && (
                                                    <Button
                                                        variant="ghost"
                                                        className="mt-2 w-full justify-center text-sm"
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            field.onChange([]);
                                                        }}
                                                    >
                                                        Clear All Attendees
                                                    </Button>
                                                )}
                                                {users?.filter((user) => !(field.value || []).includes(user.id)).map((user) => (
                                                    <SelectItem
                                                        key={user.id}
                                                        value={user.id.toString()}
                                                    >
                                                        {formatUserName(user)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {/* Display selected attendees */}
                                        <div className="mt-2 space-y-2">
                                            {field.value?.map((attendeeId) => {
                                                const attendee = users?.find(u => u.id === attendeeId);
                                                if (!attendee) return null;
                                                return (
                                                    <div key={attendeeId} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                                                        <span>{formatUserName(attendee)}</span>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => {
                                                                const newAttendees = field.value?.filter(id => id !== attendeeId) || [];
                                                                field.onChange(newAttendees);
                                                            }}
                                                        >
                                                            ×
                                                        </Button>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Follow-up Date */}
                            <FormField
                                control={form.control}
                                name="follow_up_date"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Follow-up Date</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="date"
                                                {...field}
                                                value={field.value || ""}
                                                onChange={(e) => {
                                                    console.log('Follow-up date changed to:', e.target.value);
                                                    field.onChange(e.target.value);
                                                }}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Location Type */}
                            <FormField
                                control={form.control}
                                name="location_type"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Location Type</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value || ""}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select location type" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent searchable>
                                                {field.value && (
                                                    <Button
                                                        variant="ghost"
                                                        className="mt-2 w-full justify-center text-sm"
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            field.onChange("");
                                                        }}
                                                    >
                                                        Clear Selection
                                                    </Button>
                                                )}
                                                <SelectItem value="VIRTUAL">Virtual</SelectItem>
                                                <SelectItem value="IN_PERSON">In Person</SelectItem>
                                                <SelectItem value="PHONE">Phone</SelectItem>
                                                <SelectItem value="OUR_OFFICE">Our Office</SelectItem>
                                                <SelectItem value="PARTNER_OFFICE">Partner&apos;s Office</SelectItem>
                                                <SelectItem value="OTHER">Other Location</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Location */}
                            <FormField
                                control={form.control}
                                name="location"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Location</FormLabel>
                                        <FormControl>
                                            <Input
                                                {...field}
                                                placeholder="Enter location"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Virtual Meeting Platform */}
                            <FormField
                                control={form.control}
                                name="virtual_meeting_platform"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Virtual Meeting Platform</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value || ""}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select platform" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent searchable>
                                                {field.value && (
                                                    <Button
                                                        variant="ghost"
                                                        className="mt-2 w-full justify-center text-sm"
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            field.onChange("");
                                                        }}
                                                    >
                                                        Clear Selection
                                                    </Button>
                                                )}
                                                <SelectItem value="ZOOM">Zoom</SelectItem>
                                                <SelectItem value="TEAMS">Microsoft Teams</SelectItem>
                                                <SelectItem value="MEET">Google Meet</SelectItem>
                                                <SelectItem value="OTHER">Other</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Virtual Meeting Link */}
                            <FormField
                                control={form.control}
                                name="virtual_meeting_link"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Virtual Meeting Link</FormLabel>
                                        <FormControl>
                                            <Input
                                                {...field}
                                                placeholder="Enter meeting link"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Outcomes */}
                            <FormField
                                control={form.control}
                                name="outcomes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Outcomes</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                {...field}
                                                placeholder="Enter engagement outcomes"
                                                className="min-h-[100px]"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Next Steps */}
                            <FormField
                                control={form.control}
                                name="next_steps"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Next Steps</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                {...field}
                                                placeholder="Enter next steps"
                                                className="min-h-[100px]"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <DialogFooter>
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setOpen(false);
                                    form.reset();
                                }}
                                type="button"
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isEdit ? "Update" : "Add"} Engagement
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}