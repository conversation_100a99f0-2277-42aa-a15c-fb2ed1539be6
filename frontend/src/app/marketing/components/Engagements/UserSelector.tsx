"use client";

import { useState, useEffect, useRef } from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { OrganizationUser, useOrganizationUsers } from "@/services/organizationService";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";

interface UserSelectorProps {
  selectedUsers: number[];
  onSelectionChange: (selectedIds: number[]) => void;
  placeholder?: string;
  disabled?: boolean;
  userField: string;
  onUserFieldChange: (field: string) => void;
}

export function UserSelector({
  selectedUsers,
  onSelectionChange,
  placeholder = "Select users...",
  disabled = false,
  userField,
  onUserFieldChange,
}: UserSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // User field options
  const userFieldOptions = [
    { value: "users", label: "Any Role" },
    { value: "created_by_users", label: "Created By" },
    { value: "attendees", label: "Attendees" },
  ];

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on initial load
    checkIsMobile();

    // Check on window resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch users with search
  const { data: users, isLoading } = useOrganizationUsers(searchTerm);

  // Format user name for display
  const formatUserName = (user: OrganizationUser) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    } else if (user.email) {
      return user.email;
    } else {
      return user.username;
    }
  };

  // Handle user selection
  const toggleUser = (userId: number) => {
    if (selectedUsers.includes(userId)) {
      onSelectionChange(selectedUsers.filter(id => id !== userId));
    } else {
      onSelectionChange([...selectedUsers, userId]);
    }
  };

  // Get selected user names for display
  const getSelectedUserNames = () => {
    if (!users) return [];

    return selectedUsers
      .map(id => {
        const user = users.find(u => u.id === id);
        return user ? formatUserName(user) : null;
      })
      .filter(Boolean) as string[];
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-2 items-center">
        <Select value={userField} onValueChange={onUserFieldChange} disabled={disabled}>
          <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[180px]'}`}>
            <SelectValue placeholder="Select User Role" />
          </SelectTrigger>
          <SelectContent>
            {userFieldOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="relative" ref={dropdownRef}>
        {/* Dropdown Trigger Button */}
        <Button
          variant="outline"
          className={`${isMobile ? 'w-full' : 'w-full'} justify-between h-10 px-3 py-2 text-sm`}
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled}
        >
          <div className="flex items-center gap-1 overflow-hidden">
            {selectedUsers.length > 0 ? (
              <span className="truncate">
                {getSelectedUserNames().length > 1
                  ? `${getSelectedUserNames()[0]} +${selectedUsers.length - 1}`
                  : getSelectedUserNames()[0]}
              </span>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <div className="flex items-center">
            {selectedUsers.length > 0 && (
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelectionChange([]);
                }}
                className="h-auto p-0 mr-1"
                disabled={disabled}
              >
                <X className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">Clear selection</span>
              </Button>
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>

        {/* Dropdown Content */}
        {isOpen && (
          <div className="absolute z-50 mt-1 w-full bg-popover rounded-md border shadow-md">
            {/* Search Input */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search users..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* User List */}
            <div className="max-h-[200px] overflow-y-auto p-1">
              {isLoading ? (
                <div className="p-2 space-y-2">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ) : users && users.length > 0 ? (
                users.map(user => (
                  <div
                    key={user.id}
                    className={`flex items-center justify-between px-2 py-1.5 text-sm rounded-sm cursor-pointer ${
                      selectedUsers.includes(user.id) ? 'bg-accent' : 'hover:bg-accent/50'
                    }`}
                    onClick={() => toggleUser(user.id)}
                  >
                    <span>{formatUserName(user)}</span>
                    {selectedUsers.includes(user.id) && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-muted-foreground">
                  No users found
                </div>
              )}
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div className="border-t p-2">
                <div className="text-xs font-medium mb-1 text-muted-foreground">Selected:</div>
                <div className="flex flex-wrap gap-1">
                  {getSelectedUserNames().map((name, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {name}
                      <Button
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleUser(selectedUsers[index]);
                        }}
                        className="h-auto p-0 ml-1"
                        disabled={disabled}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
