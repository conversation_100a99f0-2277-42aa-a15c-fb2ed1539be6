"use client";

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { List } from "lucide-react";
import { usePartnerEngagements } from '@/services/marketingService';
import { format } from 'date-fns';
import { Skeleton } from "@/components/ui/skeleton";

interface PartnerEngagementsDialogProps {
  partnerId: number;
  partnerName: string;
}

export function PartnerEngagementsDialog({ partnerId, partnerName }: PartnerEngagementsDialogProps) {
  const [open, setOpen] = React.useState(false);

  // Only fetch engagements when the dialog is open
  const { data, isLoading, error } = usePartnerEngagements(partnerId, {
    enabled: open
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
        >
          <List className="h-4 w-4 text-green-600" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Engagements for {partnerName}</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">
            Error loading engagements. Please try again later.
          </div>
        ) : !data?.results?.length ? (
          <div className="text-center py-4 text-gray-500">
            No engagements found for this partner.
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>DATE</TableHead>
                <TableHead>TYPE</TableHead>
                <TableHead>STATUS</TableHead>
                <TableHead>LOCATION</TableHead>
                <TableHead>NOTES</TableHead>
                <TableHead>OUTCOMES</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.results.map((engagement) => (
                <TableRow key={engagement.id}>
                  <TableCell>{format(new Date(engagement.date), 'MMM d, yyyy')}</TableCell>
                  <TableCell>{engagement.engagement_type}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      engagement.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                      engagement.status === 'SCHEDULED' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {engagement.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    {engagement.location_type === 'VIRTUAL' ? (
                      <span className="text-blue-600">{engagement.virtual_meeting_platform}</span>
                    ) : (
                      engagement.location
                    )}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">{engagement.notes}</TableCell>
                  <TableCell className="max-w-xs truncate">{engagement.outcomes}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </DialogContent>
    </Dialog>
  );
}