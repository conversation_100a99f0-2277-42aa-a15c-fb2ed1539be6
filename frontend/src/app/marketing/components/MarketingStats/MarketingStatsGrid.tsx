"use client";

import React from 'react';
import { MarketingStatsCard } from './MarketingStatsCard';
import { useMarketingDashboard } from '@/services/marketingService';
import { format } from 'date-fns';
import {
  Users,
  UserCheck,
  Calendar,
  CheckCircle,
  Clock,
  Activity,
  UserPlus,
  BarChart
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export function MarketingStatsGrid() {
  // Get current date and 30 days ago for initial date range
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const { data: dashboardData, isLoading } = useMarketingDashboard(
    format(thirtyDaysAgo, 'yyyy-MM-dd'),
    format(today, 'yyyy-MM-dd')
  );

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ))}
      </div>
    );
  }

  if (!dashboardData) {
    return <div>No data available</div>;
  }

  const { partner_stats, engagement_stats, recent_activity } = dashboardData;

  // Calculate engagement completion rate
  const completionRate = Math.round(
    (engagement_stats.engagement_status['COMPLETED'] || 0) / 
    engagement_stats.total_engagements * 100
  );

  return (
    <div className="space-y-8">
      {/* Partner Statistics */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Partner Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MarketingStatsCard
            title="Total Partners"
            value={partner_stats.total_partners}
            icon={Users}
          />
          <MarketingStatsCard
            title="Active Partners"
            value={partner_stats.active_partners}
            icon={UserCheck}
            description="Currently active partners"
          />
          <MarketingStatsCard
            title="New Partners"
            value={recent_activity.new_partners}
            icon={UserPlus}
            description="Added in last 30 days"
          />
          <MarketingStatsCard
            title="Partner Activity Rate"
            value={`${Math.round((partner_stats.active_partners / partner_stats.total_partners) * 100)}%`}
            icon={Activity}
            description="Active vs Total Partners"
          />
        </div>
      </div>

      {/* Engagement Statistics */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Engagement Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MarketingStatsCard
            title="Total Engagements"
            value={engagement_stats.total_engagements}
            icon={Calendar}
            description="In selected period"
          />
          <MarketingStatsCard
            title="Completed Engagements"
            value={recent_activity.completed_engagements}
            icon={CheckCircle}
            description="Successfully completed"
          />
          <MarketingStatsCard
            title="Upcoming Engagements"
            value={engagement_stats.upcoming_engagements}
            icon={Clock}
            description="Planned for future"
          />
          <MarketingStatsCard
            title="Completion Rate"
            value={`${completionRate}%`}
            icon={BarChart}
            description="Engagement success rate"
          />
        </div>
      </div>

      {/* Engagement Types Breakdown */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Engagement Types</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Object.entries(engagement_stats.engagement_types).map(([type, count]) => (
            <MarketingStatsCard
              key={type}
              title={type}
              value={count}
              description="Total engagements"
              className="bg-gray-50"
            />
          ))}
        </div>
      </div>
    </div>
  );
} 