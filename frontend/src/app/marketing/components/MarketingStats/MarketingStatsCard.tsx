"use client";

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MarketingStatsCardProps {
  title: string;
  value: string | number;
  icon?: LucideIcon;
  description?: string;
  trend?: number;
  className?: string;
}

export function MarketingStatsCard({ 
  title, 
  value, 
  icon: Icon,
  description,
  trend,
  className 
}: MarketingStatsCardProps) {
  return (
    <div className={cn(
      "bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow",
      className
    )}>
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <div className="flex items-baseline mt-2">
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
            {trend !== undefined && (
              <span className={cn(
                "ml-2 text-sm font-medium",
                trend >= 0 ? "text-green-600" : "text-red-600"
              )}>
                {trend >= 0 ? "+" : ""}{trend}%
              </span>
            )}
          </div>
          {description && (
            <p className="mt-1 text-sm text-gray-500">{description}</p>
          )}
        </div>
        {Icon && (
          <div className="p-2 bg-gray-50 rounded-lg">
            <Icon className="h-5 w-5 text-gray-600" />
          </div>
        )}
      </div>
    </div>
  );
} 