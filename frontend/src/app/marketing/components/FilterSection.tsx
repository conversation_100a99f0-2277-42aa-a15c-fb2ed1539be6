import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Filter, MapPin, User2 } from "lucide-react";
import { useEffect, useState } from "react";

export default function FilterSection() {
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Check on initial load
    checkIsMobile();
    
    // Check on window resize
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <div className={`w-full mt-8 ${isMobile ? 'flex flex-col gap-3' : 'flex justify-between gap-3'}`}>
      <div className={`${isMobile ? 'flex flex-col' : 'flex items-center'} gap-3`}>
        <Button
          variant="outline"
          className={`flex items-center gap-2 rounded-full border-emerald-700 text-emerald-700 hover:bg-emerald-50 ${isMobile ? 'w-full justify-start' : ''}`}
        >
          <Filter className="h-4 w-4" />
          All Filter
        </Button>

        <Button
          variant="outline"
          className={`flex items-center gap-2 rounded-full border-gray-400/50 text-gray-600 hover:bg-gray-50 ${isMobile ? 'w-full justify-start' : ''}`}
        >
          <User2 className="h-4 w-4" />
          Role
        </Button>

        <Button
          variant="outline"
          className={`flex items-center gap-2 rounded-full border-gray-400/50 text-gray-600 hover:bg-gray-50 ${isMobile ? 'w-full justify-start' : ''}`}
        >
          <MapPin className="h-4 w-4" />
          Location
        </Button>
      </div>

      <div className={`${isMobile ? 'w-full mt-3' : 'ml-auto'}`}>
        <Input
          type="search"
          placeholder="Search"
          className={`${isMobile ? 'w-full' : 'w-[280px]'} rounded-lg bg-white/16 border-gray-400/25`}
        />
      </div>
    </div>
  );
} 