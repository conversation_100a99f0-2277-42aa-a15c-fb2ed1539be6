import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useCreatePartnerContact } from '@/services/marketingService';
import { useState } from 'react';
import { PartnerContact } from '@/type/marketing';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const roleOptions = [
  "Medical Provider",
  "Chiropractor",
  "Physical Therapist",
  "Auto Repair Shop",
  "Body Shop",
  "Insurance Agent",
  "Medical Specialist",
  "Orthopedic Surgeon",
  "Pain Management",
  "Neurologist",
  "Primary Care Physician",
  "Urgent Care",
  "Hospital",
  "Towing Service",
  "Other Medical Provider",
  "Other Service Provider"
];

const formFields = [
  { id: 'name', label: 'Full Name', placeholder: 'Enter full name', required: true },
  { id: 'company_name', label: 'Practice/Business Name', placeholder: 'Enter business name' },
  { 
    id: 'role', 
    label: 'Provider Type', 
    placeholder: 'Select provider type', 
    type: 'select',
    options: roleOptions 
  },
  { id: 'title', label: 'Title/Position', placeholder: 'Enter title' },
  { id: 'email', label: 'Email', placeholder: 'Enter email address', type: 'email' },
  { id: 'phone', label: 'Phone', placeholder: 'Enter phone number' },
  { id: 'location', label: 'Location', placeholder: 'Enter location' },
  { 
    id: 'notes', 
    label: 'Notes', 
    placeholder: 'Enter any additional notes about the referral relationship', 
    type: 'textarea' 
  },
];

export default function AddContactSheet() {
  const createContact = useCreatePartnerContact();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<PartnerContact>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createContact.mutateAsync(formData);
      setOpen(false);
      setFormData({});
    } catch (error) {
      console.error('Failed to create contact:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button className="flex items-center gap-2 bg-[#1C7B35] text-white px-5 py-1.5 rounded-full">
          <Plus size={16} />
          Add Provider Contact
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
        <SheetHeader className="border-b pb-5">
          <SheetTitle className="text-xl font-bold mt-4">Add New Provider Contact</SheetTitle>
        </SheetHeader>
        
        <div className="flex flex-col h-[calc(100vh-120px)]">
          <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto py-6">
            <div className="space-y-4 pr-2">
              {formFields.map(({ id, label, placeholder, type = 'text', required, options }) => (
                <div key={id} className="space-y-2">
                  <Label htmlFor={id}>
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  {type === 'select' ? (
                    <Select
                      value={formData[id as keyof PartnerContact]?.toString()}
                      onValueChange={(value) => handleInputChange(id, value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={placeholder} />
                      </SelectTrigger>
                      <SelectContent>
                        {(options || roleOptions).map((option) => (
                          <SelectItem key={option} value={option}>
                            {option === 'true' ? 'Primary' : option === 'false' ? 'Secondary' : option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : type === 'textarea' ? (
                    <textarea
                      id={id}
                      className="w-full min-h-[100px] p-2 border rounded-md"
                      placeholder={placeholder}
                      onChange={(e) => handleInputChange(id, e.target.value)}
                      value={formData[id as keyof PartnerContact]?.toString() || ''}
                    />
                  ) : (
                    <Input 
                      id={id}
                      type={type} 
                      placeholder={placeholder}
                      required={required}
                      onChange={(e) => handleInputChange(id, e.target.value)}
                      value={formData[id as keyof PartnerContact]?.toString() || ''}
                    />
                  )}
                </div>
              ))}
            </div>
          </form>
          
          <div className="border-t pt-4 mt-auto">
            <Button 
              onClick={handleSubmit}
              className="w-full bg-[#1C7B35]"
              disabled={createContact.isPending}
            >
              {createContact.isPending ? 'Adding...' : 'Add Contact'}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
} 