"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Trash2,
  Download,
} from "lucide-react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import { AddEditLead } from "./AddEditLead";
import type { LeadFormValues } from "./AddEditLead";

// Static data for demonstration
const MOCK_DATA = [
  {
    id: 1,
    lead: "<PERSON>",
    contact: "<PERSON>",
    recruiter: "<PERSON>",
    createdAt: "04-02-2024",
    isProvider: true
  },
  {
    id: 2,
    lead: "<PERSON>",
    contact: "<PERSON>",
    recruiter: "<PERSON>",
    createdAt: "04-01-2024",
    isProvider: false
  }
];

// const STATUS_COLORS = {
//   "New": "bg-blue-100 text-blue-800",
//   "In Progress": "bg-yellow-100 text-yellow-800",
//   "Qualified": "bg-green-100 text-green-800",
//   "Lost": "bg-red-100 text-red-800",
// };

export function LeadsTable() {
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [idToDelete, setIdToDelete] = useState<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Check on initial load
    checkIsMobile();
    
    // Check on window resize
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);
  
  console.log(`☄️☄️ idToDelete ----------------------->☄️☄️`, idToDelete);
  const handleDelete = (id: number) => {
    setIdToDelete(id);
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    // Handle delete logic here
    setShowDeleteDialog(false);
    setIdToDelete(null);
  };

  const handleSubmit = (data: LeadFormValues) => {
    console.log('Form submitted:', data);
    // Handle form submission
  };

  const handleExportCSV = () => {
    // Convert mock data to CSV format
    const csvContent = MOCK_DATA.map(lead => ({
      Lead: lead.lead,
      Contact: lead.contact,
      Recruiter: lead.recruiter,
      CreatedAt: lead.createdAt,
      IsProvider: lead.isProvider ? 'Yes' : 'No'
    }));

    // Create CSV string
    const headers = Object.keys(csvContent[0]);
    const csvString = [
      headers.join(','),
      ...csvContent.map(row => headers.map(header => row[header as keyof typeof row]).join(','))
    ].join('\n');

    // Create and trigger download
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `leads_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <div className={`p-4 ${isMobile ? 'flex flex-col' : 'flex flex-wrap'} gap-4 ${isMobile ? '' : 'items-center justify-between'}`}>
        <div className={`${isMobile ? 'flex flex-col' : 'flex flex-1'} gap-4 ${isMobile ? '' : 'items-center'} min-w-[300px]`}>
          <Input
            placeholder="Search leads..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`${isMobile ? 'w-full' : 'max-w-[300px]'}`}
          />
          <Select>
            <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
              <SelectValue placeholder="Provider Only" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="yes">Yes</SelectItem>
              <SelectItem value="no">No</SelectItem>
            </SelectContent>
          </Select>
          <Select>
            <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
              <SelectValue placeholder="Recruiter" />
            </SelectTrigger>
            <SelectContent searchable>
              <SelectItem value="josh">Josh Wilson</SelectItem>
              <SelectItem value="amanda">Amanda Lee</SelectItem>
            </SelectContent>
          </Select>
          <Select>
            <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
              <SelectValue placeholder="Contact" />
            </SelectTrigger>
            <SelectContent searchable>
              <SelectItem value="sarah">Sarah Johnson</SelectItem>
              <SelectItem value="emily">Emily Davis</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className={`flex gap-2 ${isMobile ? 'w-full' : ''}`}>
          <AddEditLead onSubmit={handleSubmit} />
          <Button variant="ghost" size="icon" onClick={handleExportCSV}>
            <Download className="h-4 w-4 text-green-600" />
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        {isMobile ? (
          // Mobile card view
          <div className="px-4 pb-4 space-y-4">
            {MOCK_DATA.map((row) => (
              <div key={row.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">{row.lead}</h3>
                  <div className="flex gap-2">
                    <AddEditLead 
                      isEdit 
                      initialData={row}
                      onSubmit={handleSubmit}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleDelete(row.id)}
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-500">Contact:</span> {row.contact}
                  </div>
                  <div>
                    <span className="text-gray-500">Recruiter:</span> {row.recruiter}
                  </div>
                  <div>
                    <span className="text-gray-500">Created:</span> {row.createdAt}
                  </div>
                  <div>
                    <span className="text-gray-500">Provider:</span> {row.isProvider ? 'Yes' : 'No'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Desktop table view
          <table className="w-full">
            <thead>
              <tr className="border-y bg-gray-50/50">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">LEAD</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">CONTACT</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">RECRUITER</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">CREATED AT</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500"></th>
              </tr>
            </thead>
            <tbody>
              {MOCK_DATA.map((row) => (
                <tr key={row.id} className="border-b">
                  <td className="px-4 py-3 text-sm">{row.lead}</td>
                  <td className="px-4 py-3 text-sm">{row.contact}</td>
                  <td className="px-4 py-3 text-sm">{row.recruiter}</td>
                  <td className="px-4 py-3 text-sm">{row.createdAt}</td>
                  <td className="px-4 py-3">
                    <div className="flex gap-2">
                      <AddEditLead 
                        isEdit 
                        initialData={row}
                        onSubmit={handleSubmit}
                      />
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleDelete(row.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={confirmDelete}
        text="Lead"
      />
    </div>
  );
} 