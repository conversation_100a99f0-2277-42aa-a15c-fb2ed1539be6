"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Plus, Edit } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";

// Demo contacts for the select dropdown
const DEMO_CONTACTS = [
    { id: "1", name: "<PERSON>" },
    { id: "2", name: "<PERSON>" },
    { id: "3", name: "<PERSON>" },
] as const;

const leadFormSchema = z.object({
    name: z.string().min(1, "Name is required"),
    contactId: z.string().min(1, "Contact is required"),
});

export type LeadFormValues = z.infer<typeof leadFormSchema>;

interface Lead {
    id: number;
    lead: string;
    contact: string;
    recruiter: string;
    createdAt: string;
    isProvider: boolean;
}

interface AddEditLeadProps {
    isEdit?: boolean;
    onSubmit?: (data: LeadFormValues) => void;
    initialData?: Lead;
    children?: React.ReactNode;
}

export function AddEditLead({
    isEdit = false,
    onSubmit,
    // initialData,
    children,
}: AddEditLeadProps) {
    const [open, setOpen] = useState(false);
    const { toast } = useToast();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [isMobile, setIsMobile] = useState(false);

    // Detect if viewport is mobile size
    useEffect(() => {
        const checkIsMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };
        
        // Check on initial load
        checkIsMobile();
        
        // Check on window resize
        window.addEventListener('resize', checkIsMobile);
        
        // Cleanup
        return () => window.removeEventListener('resize', checkIsMobile);
    }, []);

    const filteredContacts = DEMO_CONTACTS.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const form = useForm<LeadFormValues>({
        resolver: zodResolver(leadFormSchema),
        defaultValues: {
            name: "",
            contactId: "",
        },
    });

    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
    };

    const onSubmitForm = async (data: LeadFormValues) => {
        try {
            if (isSubmitting) return;
            setIsSubmitting(true);

            // Handle form submission here
            onSubmit?.(data);
            form.reset();
            setOpen(false);

            toast({
                title: isEdit ? "Lead Updated" : "Lead Created",
                description: isEdit
                    ? "The lead has been updated successfully."
                    : "A new lead has been created successfully.",
            });
        } catch (error) {
            console.error("Error submitting form:", error);
            toast({
                title: "Error",
                description: "Failed to save lead. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {children || (
                    isEdit ? (
                        <Button
                            variant="link"
                        >
                            <Edit className="h-4 w-4 text-green-600" />
                        </Button>
                    ) : (
                        <Button 
                            variant={isMobile ? "default" : "link"} 
                            className={`${isMobile ? 'w-full' : 'text-green-600'}`}
                        >
                            <Plus className="h-4 w-4 mr-1" />
                            Add Lead
                        </Button>
                    )
                )}
            </DialogTrigger>

            <DialogContent className={`${isMobile ? 'w-[95%] max-w-none' : 'max-w-2xl'}`}>
                <DialogHeader>
                    <DialogTitle>
                        {isEdit ? "Edit Lead" : "New Lead"}
                    </DialogTitle>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmitForm)} className="space-y-6">
                        <div className="space-y-4">
                            {/* Name Field */}
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Name</FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="Enter lead name"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Contact Selection */}
                            <FormField
                                control={form.control}
                                name="contactId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Contact</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select contact" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent searchable onSearchChange={handleSearchChange}>
                                                {filteredContacts.length > 0 ? (
                                                    filteredContacts.map((contact) => (
                                                        <SelectItem
                                                            key={contact.id}
                                                            value={contact.id}
                                                            className="hover:bg-blue-50"
                                                        >
                                                            {contact.name}
                                                        </SelectItem>
                                                    ))
                                                ) : (
                                                    <div className="relative flex items-center justify-center py-2 px-2 text-sm text-gray-500">
                                                        {searchTerm
                                                            ? `No contacts found for "${searchTerm}"`
                                                            : "No contacts found"}
                                                    </div>
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <DialogFooter className={isMobile ? 'flex-col space-y-2' : ''}>
                            <Button 
                                type="button" 
                                variant="outline" 
                                onClick={() => setOpen(false)}
                                className={isMobile ? 'w-full' : ''}
                            >
                                Cancel
                            </Button>
                            <Button 
                                type="submit" 
                                disabled={isSubmitting}
                                className={isMobile ? 'w-full' : ''}
                            >
                                {isSubmitting ? "Saving..." : isEdit ? "Update Lead" : "Create Lead"}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 