"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Download, 
  BarChart2,
  Calendar,
  Users,
  TrendingUp,
  Target,
  Filter,
  ChevronUp,
  ChevronDown
} from "lucide-react";

type ReportType = "Performance" | "Activity" | "Analytics" | "Productivity";

interface ReportData {
  id: number;
  reportName: string;
  type: ReportType;
  metric: "Conversion" | "Engagement" | "Source" | "Productivity";
  value: string;
  trend: "up" | "down";
  trendValue: string;
  period: string;
  createdBy: string;
  lastUpdated: string;
}

// Static data for demonstration
const MOCK_DATA: ReportData[] = [
  {
    id: 1,
    reportName: "Lead Conversion Rate",
    type: "Performance",
    metric: "Conversion",
    value: "24.5%",
    trend: "up",
    trendValue: "+2.3%",
    period: "Last 30 days",
    createdBy: "josh",
    lastUpdated: "2025-04-02",
  },
  {
    id: 2,
    reportName: "Engagement Summary",
    type: "Activity",
    metric: "Engagement",
    value: "156",
    trend: "up",
    trendValue: "+12.8%",
    period: "This Month",
    createdBy: "admin",
    lastUpdated: "2025-04-01",
  },
  {
    id: 3,
    reportName: "Source Attribution",
    type: "Analytics",
    metric: "Source",
    value: "Website (45%)",
    trend: "down",
    trendValue: "-3.1%",
    period: "Last Quarter",
    createdBy: "josh",
    lastUpdated: "2025-03-30",
  },
  {
    id: 4,
    reportName: "Team Performance",
    type: "Performance",
    metric: "Productivity",
    value: "89%",
    trend: "up",
    trendValue: "+5.7%",
    period: "This Quarter",
    createdBy: "admin",
    lastUpdated: "2025-03-29",
  },
];

const REPORT_TYPE_ICONS: Record<ReportType, typeof BarChart2> = {
  "Performance": BarChart2,
  "Activity": Calendar,
  "Analytics": TrendingUp,
  "Productivity": Target,
};

const METRIC_COLORS: Record<ReportData['metric'], string> = {
  "Conversion": "text-purple-600",
  "Engagement": "text-blue-600",
  "Source": "text-green-600",
  "Productivity": "text-orange-600",
};

export function ReportsTable() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Check on initial load
    checkIsMobile();
    
    // Check on window resize
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Leads</p>
              <h3 className="text-2xl font-semibold">2,456</h3>
            </div>
            <Users className="h-8 w-8 text-blue-500" />
          </div>
          <div className="mt-2 flex items-center text-sm text-green-600">
            <ChevronUp className="h-4 w-4" />
            <span>12% vs last month</span>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Conversion Rate</p>
              <h3 className="text-2xl font-semibold">24.5%</h3>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
          <div className="mt-2 flex items-center text-sm text-green-600">
            <ChevronUp className="h-4 w-4" />
            <span>2.3% vs last month</span>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Engagements</p>
              <h3 className="text-2xl font-semibold">156</h3>
            </div>
            <Calendar className="h-8 w-8 text-purple-500" />
          </div>
          <div className="mt-2 flex items-center text-sm text-green-600">
            <ChevronUp className="h-4 w-4" />
            <span>8.1% vs last month</span>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Team Performance</p>
              <h3 className="text-2xl font-semibold">89%</h3>
            </div>
            <Target className="h-8 w-8 text-orange-500" />
          </div>
          <div className="mt-2 flex items-center text-sm text-green-600">
            <ChevronUp className="h-4 w-4" />
            <span>5.7% vs last month</span>
          </div>
        </div>
      </div>

      {/* Reports Table */}
      <div className="bg-white rounded-lg shadow">
        <div className={`p-4 ${isMobile ? 'flex flex-col' : 'flex flex-wrap'} gap-4 ${isMobile ? '' : 'items-center justify-between'}`}>
          <div className={`${isMobile ? 'flex flex-col' : 'flex flex-1'} gap-4 ${isMobile ? '' : 'items-center'} min-w-[300px]`}>
            <Input
              placeholder="Search reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={`${isMobile ? 'w-full' : 'max-w-[300px]'}`}
            />
            <Select>
              <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
                <SelectValue placeholder="Report Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="performance">Performance</SelectItem>
                <SelectItem value="activity">Activity</SelectItem>
                <SelectItem value="analytics">Analytics</SelectItem>
                <SelectItem value="productivity">Productivity</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className={`flex gap-2 ${isMobile ? 'mt-2 flex-nowrap' : ''}`}>
            <Button variant="default" className={isMobile ? 'flex-1' : ''}>+ Create Report</Button>
            <Button variant="outline">
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="outline">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-y bg-gray-50/50">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">REPORT NAME</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">TYPE</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">METRIC</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">VALUE</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">TREND</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">PERIOD</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">CREATED BY</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">LAST UPDATED</th>
              </tr>
            </thead>
            <tbody>
              {MOCK_DATA.map((row) => {
                const IconComponent = REPORT_TYPE_ICONS[row.type as ReportType] || BarChart2;
                return (
                  <tr key={row.id} className="border-b">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium">{row.reportName}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">{row.type}</td>
                    <td className="px-4 py-3">
                      <span className={`text-sm ${METRIC_COLORS[row.metric as ReportData['metric']]}`}>
                        {row.metric}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm font-medium">{row.value}</td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-1">
                        {row.trend === "up" ? (
                          <ChevronUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className={`text-sm ${
                          row.trend === "up" ? "text-green-600" : "text-red-600"
                        }`}>
                          {row.trendValue}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">{row.period}</td>
                    <td className="px-4 py-3 text-sm">{row.createdBy}</td>
                    <td className="px-4 py-3 text-sm">{row.lastUpdated}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        <div className="p-4 flex items-center justify-between border-t">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Row per page</span>
            <Select>
              <SelectTrigger className="w-[70px]">
                <SelectValue>10</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm" disabled>
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
} 