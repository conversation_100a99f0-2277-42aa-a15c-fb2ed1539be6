"use client";

import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Target,
  Settings,
  Calendar,
  Activity,
  UserCheck,
  ChevronUp,
  ChevronDown,
  BarChart,
  TrendingUp,
  Award,
  Building,
  Clock
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useMarketingAnalyticsDashboard } from "@/services/marketing-goals-api";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { GoalCategory } from "@/type/marketing-goals";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

export function AdminDashboard() {
  const [timeFilter, setTimeFilter] = useState("month");

  // Calculate date range based on time filter
  const today = new Date();
  let startDate = new Date(today);

  switch(timeFilter) {
    case "today":
      startDate = new Date(today.setHours(0, 0, 0, 0));
      break;
    case "week":
      startDate.setDate(today.getDate() - 7);
      break;
    case "month":
      startDate.setMonth(today.getMonth() - 1);
      break;
    case "quarter":
      startDate.setMonth(today.getMonth() - 3);
      break;
    default:
      startDate.setMonth(today.getMonth() - 1);
  }

  // Fetch dashboard data
  const { data: dashboardData, isLoading, error } = useMarketingAnalyticsDashboard({
    start_date: format(startDate, 'yyyy-MM-dd'),
    end_date: format(today, 'yyyy-MM-dd')
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white p-4 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-8 w-16" />
                </div>
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
              <Skeleton className="mt-2 h-4 w-32" />
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-64 w-full rounded-lg" />
          <Skeleton className="h-64 w-full rounded-lg" />
        </div>
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <p className="text-red-500 font-medium">Failed to load dashboard data</p>
          <p className="text-gray-500 mt-2">Please try again later</p>
          <Button className="mt-4" onClick={() => window.location.reload()}>Refresh</Button>
        </div>
      </div>
    );
  }

  // Extract data from API response
  const { goals_summary, partner_metrics, engagement_analytics, performance_trends } = dashboardData;

  // Prepare metrics for display with better null handling
  const METRICS = [
    {
      id: 1,
      title: "Total Goals",
      value: goals_summary.overall_metrics?.total_goals || 0,
      trend: goals_summary.overall_metrics?.avg_completion > 0 ? "up" : "neutral",
      trendValue: `${goals_summary.overall_metrics?.completed_goals || 0} completed`,
      trendPercentage: Math.round(goals_summary.overall_metrics?.avg_completion || 0),
      icon: Target,
      color: "text-blue-500",
      tooltip: "Total number of marketing goals across all users"
    },
    {
      id: 2,
      title: "Active Partners",
      value: partner_metrics?.active_partners || 0,
      trend: partner_metrics?.active_partners < partner_metrics?.total_partners ? "neutral" : "up",
      trendValue: `${partner_metrics?.total_partners || 0} total`,
      trendPercentage: partner_metrics?.total_partners ?
        Math.round((partner_metrics.active_partners / partner_metrics.total_partners) * 100) : 0,
      icon: UserCheck,
      color: "text-green-500",
      tooltip: "Number of active marketing partners"
    },
    {
      id: 3,
      title: "Engagements",
      value: engagement_analytics.metrics?.total_engagements || 0,
      trend: (engagement_analytics.metrics?.avg_success_rating || 0) > 3 ? "up" :
             (engagement_analytics.metrics?.avg_success_rating || 0) > 0 ? "down" : "neutral",
      trendValue: engagement_analytics.metrics?.avg_success_rating ?
                 `${engagement_analytics.metrics.avg_success_rating.toFixed(1)} avg rating` : "No ratings",
      trendPercentage: engagement_analytics.metrics?.avg_success_rating ?
                      Math.round((engagement_analytics.metrics.avg_success_rating / 5) * 100) : 0,
      icon: Calendar,
      color: "text-purple-500",
      tooltip: "Total number of partner engagements"
    },
    {
      id: 4,
      title: "Revenue",
      value: engagement_analytics.metrics?.total_revenue ?
             `$${engagement_analytics.metrics.total_revenue.toLocaleString()}` : "$0",
      trend: engagement_analytics.metrics?.total_revenue > (engagement_analytics.metrics?.total_cost || 0) ?
             "up" : engagement_analytics.metrics?.total_revenue ? "down" : "neutral",
      trendValue: engagement_analytics.metrics?.total_cost ?
                 `$${engagement_analytics.metrics.total_cost.toLocaleString()} cost` : "No cost data",
      trendPercentage: (engagement_analytics.metrics?.total_revenue && engagement_analytics.metrics?.total_cost) ?
                       Math.round(((engagement_analytics.metrics.total_revenue - engagement_analytics.metrics.total_cost) /
                       engagement_analytics.metrics.total_cost) * 100) : 0,
      icon: Activity,
      color: "text-orange-500",
      tooltip: "Total revenue generated from marketing activities"
    },
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {METRICS.map((metric) => (
          <div key={metric.id} className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-1">
                  <p className="text-sm text-gray-500">{metric.title}</p>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="cursor-help text-gray-400 hover:text-gray-600">ⓘ</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-sm">{metric.tooltip}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <h3 className="text-2xl font-semibold">{metric.value}</h3>
              </div>
              <div className={`h-10 w-10 rounded-full flex items-center justify-center ${metric.color.replace('text-', 'bg-').replace('500', '100')}`}>
                <metric.icon className={`h-5 w-5 ${metric.color}`} />
              </div>
            </div>
            <div className="mt-3">
              <div className="flex items-center justify-between mb-1">
                <div className={`flex items-center text-sm ${
                  metric.trend === "up" ? "text-green-600" :
                  metric.trend === "down" ? "text-red-600" : "text-gray-500"
                }`}>
                  {metric.trend === "up" ? (
                    <ChevronUp className="h-4 w-4 mr-1" />
                  ) : metric.trend === "down" ? (
                    <ChevronDown className="h-4 w-4 mr-1" />
                  ) : null}
                  <span>{metric.trendValue}</span>
                </div>
                {metric.trendPercentage !== undefined && (
                  <span className={`text-xs font-medium ${
                    metric.trend === "up" ? "text-green-600" :
                    metric.trend === "down" ? "text-red-600" : "text-gray-500"
                  }`}>
                    {metric.trendPercentage > 0 ? `+${metric.trendPercentage}%` : `${metric.trendPercentage}%`}
                  </span>
                )}
              </div>
              <div className="w-full bg-gray-100 rounded-full h-1.5">
                <div
                  className={`h-1.5 rounded-full ${
                    metric.trend === "up" ? "bg-green-500" :
                    metric.trend === "down" ? "bg-red-500" : "bg-gray-300"
                  }`}
                  style={{ width: `${Math.min(Math.max(metric.trendPercentage || 0, 0), 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Time Filter */}
      <div className="flex justify-end mb-4">
        <Select value={timeFilter} onValueChange={setTimeFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Partner Metrics Section */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Partner Metrics</h2>
          <Badge variant="outline" className="text-blue-600 bg-blue-50">
            {partner_metrics?.total_partners || 0} Total Partners
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Partner Type Distribution */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Partner Type Distribution</CardTitle>
              <CardDescription>Breakdown of partners by type</CardDescription>
            </CardHeader>
            <CardContent>
              {partner_metrics?.type_distribution?.length > 0 ? (
                <div className="space-y-3">
                  {partner_metrics.type_distribution.map((type, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{type.partner_type || 'Unknown'}</span>
                        <span className="text-sm text-gray-500">{type.count} partners</span>
                      </div>
                      <Progress
                        value={Math.round((type.count / (partner_metrics.total_partners || 1)) * 100)}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center text-gray-500">
                  <Building className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No partner type data available</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Top Partners */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Top Partners</CardTitle>
              <CardDescription>Partners with most engagements</CardDescription>
            </CardHeader>
            <CardContent>
              {partner_metrics?.top_partners?.length > 0 ? (
                <div className="space-y-3">
                  {partner_metrics.top_partners.slice(0, 3).map((partner, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded-md">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {partner.partner_name || 'Partner'}
                        </p>
                        <div className="flex gap-4 mt-1 text-xs text-gray-500">
                          <span>{partner.engagement_count || 0} engagements</span>
                          {partner.total_revenue !== null && (
                            <span>${partner.total_revenue?.toLocaleString() || 0} revenue</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center text-gray-500">
                  <UserCheck className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No partner data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Goals Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Goal Categories */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Goals by Category</h2>
          </div>
          <div className="space-y-4">
            {(goals_summary.goals_by_category || []).length > 0 ? (
              goals_summary.goals_by_category.map((category, index) => (
                <div key={index} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded-md">
                  <div className={`p-2 rounded-full ${
                    category.marketing_goal__category === GoalCategory.REFERRAL ? "bg-blue-100" :
                    category.marketing_goal__category === GoalCategory.ENGAGEMENT ? "bg-green-100" :
                    category.marketing_goal__category === GoalCategory.REVENUE ? "bg-purple-100" : "bg-orange-100"
                  }`}>
                    {category.marketing_goal__category === GoalCategory.REFERRAL ? <Target className="h-4 w-4 text-blue-600" /> :
                     category.marketing_goal__category === GoalCategory.ENGAGEMENT ? <Calendar className="h-4 w-4 text-green-600" /> :
                     category.marketing_goal__category === GoalCategory.REVENUE ? <Activity className="h-4 w-4 text-purple-600" /> :
                     <Settings className="h-4 w-4 text-orange-600" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{category.marketing_goal__category || 'Unknown'}</p>
                    <div className="flex justify-between mt-1">
                      <p className="text-sm text-gray-600">{category.completed || 0} of {category.total || 0} completed</p>
                      <p className="text-sm font-medium text-green-600">{Math.round(category.avg_progress || 0)}%</p>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${Math.round(category.avg_progress || 0)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="py-6 text-center text-gray-500">
                <Target className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>No goal categories available</p>
              </div>
            )}
          </div>
        </div>

        {/* Personal Goals */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Personal Goals</h2>
          </div>
          <div className="space-y-4">
            {(goals_summary.personal_goals || []).length > 0 ? (
              goals_summary.personal_goals.map((goal, index) => (
                <div key={index} className="p-3 border border-gray-100 rounded-md hover:bg-gray-50">
                  <div className="flex justify-between items-center mb-1">
                    <p className="text-sm font-medium">{goal.marketing_goal__name || 'Custom Goal'}</p>
                    <Badge variant="outline" className={goal.progress_percentage >= 100 ? 'bg-green-50 text-green-600' : 'bg-blue-50 text-blue-600'}>
                      {goal.progress_percentage >= 100 ? 'Completed' : 'In Progress'}
                    </Badge>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mb-2">
                    <span>Target: {goal.target_value}</span>
                    <span>Current: {goal.current_progress}</span>
                    <span>Due: {new Date(goal.current_period_end).toLocaleDateString()}</span>
                  </div>
                  <Progress
                    value={Math.min(goal.progress_percentage || 0, 100)}
                    className="h-2"
                  />
                </div>
              ))
            ) : (
              <div className="py-6 text-center text-gray-500">
                <Award className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>No personal goals available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Engagement Analytics Section */}
      <div className="bg-white rounded-lg shadow p-4 mt-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Engagement Analytics</h2>
          <Badge variant="outline" className="text-purple-600 bg-purple-50">
            {engagement_analytics.metrics?.total_engagements || 0} Total Engagements
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Engagement Type Breakdown */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Engagement Types</CardTitle>
              <CardDescription>Breakdown by engagement category</CardDescription>
            </CardHeader>
            <CardContent>
              {engagement_analytics.type_breakdown?.length > 0 ? (
                <div className="space-y-3">
                  {engagement_analytics.type_breakdown.map((type, index) => (
                    <div key={index} className="p-3 border border-gray-100 rounded-md">
                      <div className="flex justify-between items-center mb-1">
                        <p className="text-sm font-medium">{type.engagement_type__name || 'Unknown'}</p>
                        <Badge variant="outline" className="text-xs">
                          {type.engagement_type__category || 'Other'}
                        </Badge>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mb-2">
                        <span>{type.count} engagements</span>
                        <span>Avg Rating: {type.avg_success?.toFixed(1) || 'N/A'}</span>
                      </div>
                      <Progress
                        value={Math.round((type.count / (engagement_analytics.metrics?.total_engagements || 1)) * 100)}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No engagement type data available</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Engagements */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Upcoming Engagements</CardTitle>
              <CardDescription>Scheduled engagements</CardDescription>
            </CardHeader>
            <CardContent>
              {engagement_analytics.upcoming_engagements?.length > 0 ? (
                <div className="space-y-3">
                  {engagement_analytics.upcoming_engagements.map((engagement, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded-md">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                        <Calendar className="h-5 w-5 text-purple-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {engagement.engagement_type__name || 'Engagement'}
                        </p>
                        <div className="flex gap-4 mt-1 text-xs text-gray-500">
                          <span>Date: {new Date(engagement.date).toLocaleDateString()}</span>
                          <span>Partner: {engagement.partner__business_name || 'Unknown'}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center text-gray-500">
                  <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No upcoming engagements</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Trends Section */}
      <div className="bg-white rounded-lg shadow p-4 mt-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Performance Trends</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Year over Year Comparison */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Year over Year</CardTitle>
              <CardDescription>Performance comparison</CardDescription>
            </CardHeader>
            <CardContent>
              {performance_trends?.year_over_year ? (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-500">Current Year</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-xs">Revenue</span>
                        <span className="text-xs font-medium">
                          ${performance_trends.year_over_year.current_year.total_revenue?.toLocaleString() || 0}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-xs">Engagements</span>
                        <span className="text-xs font-medium">
                          {performance_trends.year_over_year.current_year.total_engagements}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-xs">New Partners</span>
                        <span className="text-xs font-medium">
                          {performance_trends.year_over_year.current_year.new_partners}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-500">Growth</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-xs">Revenue</span>
                        <span className={`text-xs font-medium ${performance_trends.year_over_year.growth_rates.total_revenue >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {performance_trends.year_over_year.growth_rates.total_revenue >= 0 ? '+' : ''}
                          {performance_trends.year_over_year.growth_rates.total_revenue}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-xs">Engagements</span>
                        <span className={`text-xs font-medium ${performance_trends.year_over_year.growth_rates.total_engagements >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {performance_trends.year_over_year.growth_rates.total_engagements >= 0 ? '+' : ''}
                          {performance_trends.year_over_year.growth_rates.total_engagements}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-xs">New Partners</span>
                        <span className={`text-xs font-medium ${performance_trends.year_over_year.growth_rates.new_partners >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {performance_trends.year_over_year.growth_rates.new_partners >= 0 ? '+' : ''}
                          {performance_trends.year_over_year.growth_rates.new_partners}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="py-6 text-center text-gray-500">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No year-over-year data available</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Monthly Revenue Trends */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Monthly Trends</CardTitle>
              <CardDescription>Revenue and engagement activity</CardDescription>
            </CardHeader>
            <CardContent>
              {engagement_analytics.monthly_trends?.length > 0 ? (
                <div className="space-y-3">
                  {engagement_analytics.monthly_trends.map((month, index) => (
                    <div key={index} className="p-3 border border-gray-100 rounded-md">
                      <div className="flex justify-between items-center mb-1">
                        <p className="text-sm font-medium">
                          {new Date(month.month).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {month.count} engagements
                        </Badge>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mb-2">
                        <span>Revenue: ${month.revenue?.toLocaleString() || 0}</span>
                        <span>Cost: ${month.cost?.toLocaleString() || 0}</span>
                        <span>ROI: {month.revenue && month.cost ? Math.round(((month.revenue - month.cost) / month.cost) * 100) : 0}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center text-gray-500">
                  <BarChart className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No monthly trend data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}