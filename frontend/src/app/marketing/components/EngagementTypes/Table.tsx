"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Trash2,
  Download,
  Filter,
} from "lucide-react";
import { useEngagementTypes, useDeleteEngagementType } from "@/services/marketingService";
import { EngagementType } from "@/type/marketing";
import { Skeleton } from "@/components/ui/skeleton";
import { AddEditEngagementType } from './AddEditEngagementType';
import { useToast } from "@/hooks/use-toast";


const STATUS_COLORS = {
  "active": "bg-green-100 text-green-800",
  "inactive": "bg-gray-100 text-gray-800",
  "draft": "bg-yellow-100 text-yellow-800",
};

export function EngagementTypesTable() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [isMobile, setIsMobile] = useState(false);
  const { toast } = useToast();

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on initial load
    checkIsMobile();

    // Check on window resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Fetch engagement types
  const { data, isLoading, error } = useEngagementTypes();

  // Add delete mutation
  const deleteMutation = useDeleteEngagementType();

  const handleDeleteClick = async (typeId: number) => {
    try {
      await deleteMutation.mutateAsync(typeId);
      toast({
        title: "Type Deleted",
        description: "The engagement type has been deleted successfully.",
      });
    } catch (error) {
      console.error("Error deleting type:", error);
      toast({
        title: "Error",
        description: "Failed to delete engagement type. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter the data based on search query and filters
  const filteredTypes = data?.filter((type: EngagementType) => {
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch =
      type.name.toLowerCase().includes(searchLower) ||
      type.description?.toLowerCase().includes(searchLower) ||
      type.category.toLowerCase().includes(searchLower);

    const matchesCategory = selectedCategory === "all" || type.category.toLowerCase() === selectedCategory.toLowerCase();
    const matchesStatus = selectedStatus === "all" || (type.is_active === (selectedStatus === "active"));

    return matchesSearch && matchesCategory && matchesStatus;
  }) || [];

  // Get unique categories for filter
  const categories = Array.from(new Set(data?.map((type: EngagementType) => type.category) || []));

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading engagement types. Please try again later.
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className={`p-4 ${isMobile ? 'flex flex-col' : 'flex flex-wrap'} gap-4 ${isMobile ? '' : 'items-center justify-between'}`}>
        <div className="flex flex-col w-full sm:flex-row sm:flex-1 gap-3 sm:items-center sm:flex-wrap">
            <Input
              placeholder="Search engagement types..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={'w-full sm:max-w-[250px]'}
            />
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category.toLowerCase()}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[200px]'}`}>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className={`flex gap-2 justify-end ${isMobile ? 'w-full mt-2' : ''}`}>
            <AddEditEngagementType isEdit={false} />
            <Button variant="link" className="text-green-600 p-0">
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="link" className="text-green-600 p-0">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          {isMobile ? (
            // Mobile card view
            <div className="px-4 pb-4 space-y-4">
              {isLoading ? (
                // Loading state for mobile
                Array(3).fill(0).map((_, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-4">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-20 w-full" />
                    <div className="flex justify-end">
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </div>
                ))
              ) : (
                filteredTypes.map((type: EngagementType) => (
                  <div key={type.id} className="border rounded-lg p-4 space-y-2">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">{type.name}</h3>
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${type.is_active ? STATUS_COLORS.active : STATUS_COLORS.inactive
                          }`}
                      >
                        {type.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div>
                        <span className="text-gray-500">Category:</span> {type.category}
                      </div>
                      {type.description && (
                        <div>
                          <span className="text-gray-500">Description:</span> {type.description}
                        </div>
                      )}
                      <div>
                        <span className="text-gray-500">Duration:</span> {type.default_duration ? `${type.default_duration} min` : 'N/A'}
                      </div>
                      <div>
                        <span className="text-gray-500">Follow-up:</span> {type.requires_follow_up ? "Required" : "Not Required"}
                      </div>
                    </div>

                    <div className="flex justify-end items-center">
                      <AddEditEngagementType
                        isEdit={true}
                        initialData={type}
                      />
                      <Button
                        variant="link"
                        className="text-red-600 p-0"
                        onClick={() => handleDeleteClick(type.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          ) : (
            // Desktop table view
            <table className="w-full">
              <thead>
                <tr className="border-y bg-gray-50/50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">NAME</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">CATEGORY</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">DESCRIPTION</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">DURATION</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">FOLLOW-UP</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">STATUS</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  // Loading state
                  Array(3).fill(0).map((_, index) => (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-3"><Skeleton className="h-6 w-32" /></td>
                      <td className="px-4 py-3"><Skeleton className="h-6 w-24" /></td>
                      <td className="px-4 py-3"><Skeleton className="h-6 w-40" /></td>
                      <td className="px-4 py-3"><Skeleton className="h-6 w-20" /></td>
                      <td className="px-4 py-3"><Skeleton className="h-6 w-24" /></td>
                      <td className="px-4 py-3"><Skeleton className="h-6 w-20" /></td>
                      <td className="px-4 py-3"><Skeleton className="h-6 w-20" /></td>
                    </tr>
                  ))
                ) : (
                  filteredTypes.map((type: EngagementType) => (
                    <tr key={type.id} className="border-b">
                      <td className="px-4 py-3">{type.name}</td>
                      <td className="px-4 py-3">{type.category}</td>
                      <td className="px-4 py-3">{type.description || "—"}</td>
                      <td className="px-4 py-3">{type.default_duration ? `${type.default_duration} min` : 'N/A'}</td>
                      <td className="px-4 py-3">
                        {type.requires_follow_up ? "Required" : "Not Required"}
                      </td>
                      <td className="px-4 py-3">
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${type.is_active ? STATUS_COLORS.active : STATUS_COLORS.inactive
                            }`}
                        >
                          {type.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex gap-2">
                          <AddEditEngagementType
                            isEdit={true}
                            initialData={type}
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteClick(type.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          )}
        </div>

        <div className="p-4 border-t">
          <div className="text-sm text-gray-500">
            {data ? (
              `Showing ${filteredTypes.length} of ${data.length} results`
            ) : (
              "Loading..."
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 