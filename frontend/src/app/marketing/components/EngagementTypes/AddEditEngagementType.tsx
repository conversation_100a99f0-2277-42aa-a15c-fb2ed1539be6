"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Edit, Plus } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ENGAGEMENT_CATEGORIES, EngagementType } from "@/type/marketing";
import { useCreateEngagementType, useUpdateEngagementType } from "@/services/marketingService";
import { RequiredLabel } from "@/components/ui/required-label";

const engagementTypeFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  default_duration: z.number().min(0).optional(),
  is_active: z.boolean().default(true),
  requires_follow_up: z.boolean().default(false),
  typical_cost: z.number().min(0).optional(),
  success_metrics: z.string().optional(),
});

type EngagementTypeFormValues = z.infer<typeof engagementTypeFormSchema>;

interface AddEditEngagementTypeProps {
  isEdit?: boolean;
  initialData?: EngagementType;
  children?: React.ReactNode;
}

export function AddEditEngagementType({
  isEdit = false,
  initialData,
  children,
}: AddEditEngagementTypeProps) {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect if viewport is mobile size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Check on initial load
    checkIsMobile();
    
    // Check on window resize
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Add mutations
  const createMutation = useCreateEngagementType();
  const updateMutation = useUpdateEngagementType(initialData?.id || 0);

  const form = useForm<EngagementTypeFormValues>({
    resolver: zodResolver(engagementTypeFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      category: initialData?.category || "",
      default_duration: initialData?.default_duration ? Number(initialData.default_duration) : 0,
      is_active: initialData?.is_active ?? true,
      requires_follow_up: initialData?.requires_follow_up ?? false,
      typical_cost: initialData?.typical_cost ? Number(initialData.typical_cost) : 0,
      success_metrics: initialData?.success_metrics || "",
    },
  });

  // Effect to set form values when initialData changes or dialog opens
  useEffect(() => {
    if (initialData && open) {
      form.reset({
        name: initialData.name || "",
        description: initialData.description || "",
        category: initialData.category || "",
        default_duration: initialData.default_duration ? Number(initialData.default_duration) : 0,
        is_active: initialData.is_active ?? true,
        requires_follow_up: initialData.requires_follow_up ?? false,
        typical_cost: initialData.typical_cost ? Number(initialData.typical_cost) : 0,
        success_metrics: initialData.success_metrics || "",
      });
    }
  }, [initialData, open, form]);

  const onSubmit = async (data: EngagementTypeFormValues) => {
    try {
      if (isSubmitting) return;
      setIsSubmitting(true);

      // Ensure numeric fields are properly typed
      const formattedData = {
        ...data,
        typical_cost: Number(data.typical_cost),
        default_duration: Number(data.default_duration)
      };

      if (isEdit && initialData) {
        await updateMutation.mutateAsync(formattedData);
      } else {
        await createMutation.mutateAsync(formattedData);
      }

      toast({
        title: isEdit ? "Type Updated" : "Type Created",
        description: isEdit
          ? "The engagement type has been updated successfully."
          : "A new engagement type has been created successfully.",
      });

      setOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description: "Failed to save engagement type. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          isEdit ? (
            <Button
              variant="link"
            >
              <Edit className="h-4 w-4 text-green-600" />
            </Button>
          ) : (
            <Button 
              variant="link" 
              className='text-green-600'
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Type
            </Button>
          )
        )}
      </DialogTrigger>

      <DialogContent className={`${isMobile ? 'w-[95%] max-w-none' : 'max-w-2xl'}`}>
        <DialogHeader>
          <DialogTitle>
            {isEdit ? "Edit Engagement Type" : "New Engagement Type"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-6">
              {/* Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Name</RequiredLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter type name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Category */}
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Category</RequiredLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent searchable>
                        {ENGAGEMENT_CATEGORIES.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                        {/* {formData.category && (
                          <Button
                            variant="ghost"
                            className="mt-2 w-full justify-center text-sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleChange("category", "");
                            }}
                          >
                            Clear Selection
                          </Button>
                        )} */}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter description"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Duration and Cost */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="default_duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Duration (minutes)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          placeholder="Enter duration in minutes"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="typical_cost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Typical Cost ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          placeholder="Enter typical cost"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Success Metrics */}
              <FormField
                control={form.control}
                name="success_metrics"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Success Metrics</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter success metrics"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Requires Follow-up */}
              <FormField
                control={form.control}
                name="requires_follow_up"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Requires Follow-up
                      </FormLabel>
                      <div className="text-sm text-gray-500">
                        Determine if this type requires follow-up action
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Active Status */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Active Status
                      </FormLabel>
                      <div className="text-sm text-gray-500">
                        Determine if this type is currently active
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className={isMobile ? 'flex-col space-y-2' : ''}>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                className={isMobile ? 'w-full' : ''}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className={isMobile ? 'w-full' : ''}
              >
                {isSubmitting ? "Saving..." : isEdit ? "Update Type" : "Create Type"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 