"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Download, Trash2, Loader2 } from "lucide-react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import {
  useMarketingGoals,
  useDeleteMarketingGoal
} from "@/services/marketing-goals-api";
import {
  GoalCategory,
  GoalCategoryLabels,
  GoalMetricType,
  GoalMetricTypeLabels,
  GoalRecurrenceLabels,
  MarketingGoal
} from "@/type/marketing-goals";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { AddEditCustomGoal } from "../CustomGoals/AddEditCustomGoal";
import { useIsMobile } from "@/hooks/use-mobile";

export function CustomGoalsTable() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all_categories");
  const [selectedMetricType, setSelectedMetricType] = useState<string>("all_types");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [goalToDelete, setGoalToDelete] = useState<number | null>(null);
  const isMobile = useIsMobile();

  // Prepare filters for API call
  const filters: { category?: GoalCategory; metric_type?: GoalMetricType; is_active?: boolean } = {};
  if (selectedCategory !== "all_categories") {
    filters.category = selectedCategory as GoalCategory;
  }
  if (selectedMetricType !== "all_types") {
    filters.metric_type = selectedMetricType as GoalMetricType;
  }

  // Fetch marketing goals from API
  const {
    data: goalsData,
    isLoading: isLoadingGoals,
    isError: isErrorGoals,
    refetch: refetchGoals
  } = useMarketingGoals(filters);

  // Delete mutation
  const { mutate: deleteGoal } = useDeleteMarketingGoal();

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (goalToDelete) {
      deleteGoal(goalToDelete, {
        onSuccess: () => {
          setDeleteDialogOpen(false);
          setGoalToDelete(null);
          refetchGoals();
          toast({
            title: "Success",
            description: "Marketing goal deleted successfully",
          });
        },
        onError: () => {
          toast({
            title: "Error",
            description: "Failed to delete marketing goal",
            variant: "destructive",
          });
        }
      });
    }
  };

  const handleDeleteClick = (goalId: number) => {
    setGoalToDelete(goalId);
    setDeleteDialogOpen(true);
  };

  // Export to CSV
  const handleExportCSV = () => {
    if (!goalsData) return;

    const csvContent = goalsData.map((goal: MarketingGoal) => ({
      Name: goal.name,
      Category: GoalCategoryLabels[goal.category],
      MetricType: GoalMetricTypeLabels[goal.metric_type],
      DefaultTarget: goal.default_target,
      Recurrence: GoalRecurrenceLabels[goal.recurrence],
      IsActive: goal.is_active ? "Yes" : "No",
      Description: goal.description || "",
      CreatedAt: format(new Date(goal.created_at), 'MM/dd/yyyy')
    }));

    if (csvContent.length === 0) return;

    const headers = Object.keys(csvContent[0]);
    const csvString = [
      headers.join(','),
      ...csvContent.map(row => headers.map(header => {
        const value = row[header as keyof typeof row];
        const escapedValue = String(value).replace(/"/g, '""');
        return `"${escapedValue}"`;
      }).join(','))
    ].join('\n');

    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `marketing_goals_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Filter goals based on search query
  const filteredGoals = goalsData?.filter((goal: MarketingGoal) => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      goal.name.toLowerCase().includes(searchLower) ||
      goal.description?.toLowerCase().includes(searchLower) ||
      GoalCategoryLabels[goal.category].toLowerCase().includes(searchLower)
    );
  }) || [];

  // Render mobile card view
  const renderMobileCardView = (goal: MarketingGoal) => (
    <div key={goal.id} className="p-4 border-b last:border-b-0">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium text-base">{goal.name}</h3>
        <div className="flex items-center gap-1">
          <AddEditCustomGoal
            isEdit={true}
            goal={goal}
            onSuccess={refetchGoals}
          />
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={() => handleDeleteClick(goal.id)}
          >
            <Trash2 className="h-4 w-4 text-red-600" />
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span className="text-gray-500">Category:</span>
          <p>{GoalCategoryLabels[goal.category]}</p>
        </div>
        <div>
          <span className="text-gray-500">Metric Type:</span>
          <p>{GoalMetricTypeLabels[goal.metric_type]}</p>
        </div>
        <div>
          <span className="text-gray-500">Default Target:</span>
          <p>{goal.default_target}</p>
        </div>
        <div>
          <span className="text-gray-500">Recurrence:</span>
          <p>{GoalRecurrenceLabels[goal.recurrence]}</p>
        </div>
        <div>
          <span className="text-gray-500">Status:</span>
          <span className={`px-2 py-1 rounded-full text-xs inline-block mt-1 ${goal.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
            {goal.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
        <div>
          <span className="text-gray-500">Created:</span>
          <p>{format(new Date(goal.created_at), 'MM/dd/yyyy')}</p>
        </div>
        <div className="col-span-2">
          <span className="text-gray-500">Created By:</span>
          <p>{goal.created_by?.name || 'System'}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        text="Marketing Goal"
      />

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 flex flex-col sm:flex-row flex-wrap gap-4">
          <div className="flex flex-col w-full sm:flex-row sm:flex-1 gap-3 sm:items-center sm:flex-wrap">
            <Input
              placeholder="Search goals..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:max-w-[250px]"
            />
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Select Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_categories">All Categories</SelectItem>
                {Object.entries(GoalCategoryLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedMetricType} onValueChange={setSelectedMetricType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Select Metric Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_types">All Metric Types</SelectItem>
                {Object.entries(GoalMetricTypeLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2 mt-2 sm:mt-0 justify-end">
            <AddEditCustomGoal isEdit={false} onSuccess={refetchGoals} />
            <Button
              variant="ghost"
              size="icon"
              onClick={handleExportCSV}
              disabled={!goalsData?.length}
            >
              <Download className="h-4 w-4 text-green-600" />
            </Button>
          </div>
        </div>

        <div className="border-t">
          {isMobile ? (
            <div className="overflow-auto">
              {isLoadingGoals ? (
                <div className="text-center py-8">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Loading marketing goals...</span>
                  </div>
                </div>
              ) : isErrorGoals ? (
                <div className="text-center py-8 text-red-500">
                  Error loading marketing goals. Please try again.
                </div>
              ) : filteredGoals.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No marketing goals found.
                </div>
              ) : (
                filteredGoals.map(renderMobileCardView)
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50/50">
                    <TableHead className="w-[200px]">NAME</TableHead>
                    <TableHead className="w-[150px]">CATEGORY</TableHead>
                    <TableHead className="w-[150px]">METRIC TYPE</TableHead>
                    <TableHead className="w-[150px]">DEFAULT TARGET</TableHead>
                    <TableHead className="w-[150px]">RECURRENCE</TableHead>
                    <TableHead className="w-[100px]">STATUS</TableHead>
                    <TableHead className="w-[150px]">CREATED AT</TableHead>
                    <TableHead className="w-[120px]">CREATED BY</TableHead>
                    <TableHead className="w-[100px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingGoals ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin mr-2" />
                          <span>Loading marketing goals...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : isErrorGoals ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-red-500">
                        Error loading marketing goals. Please try again.
                      </TableCell>
                    </TableRow>
                  ) : filteredGoals.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                        No marketing goals found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredGoals.map((goal) => (
                      <TableRow key={goal.id}>
                        <TableCell className="font-medium">{goal.name}</TableCell>
                        <TableCell>{GoalCategoryLabels[goal.category]}</TableCell>
                        <TableCell>{GoalMetricTypeLabels[goal.metric_type]}</TableCell>
                        <TableCell>{goal.default_target}</TableCell>
                        <TableCell>{GoalRecurrenceLabels[goal.recurrence]}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${goal.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                            {goal.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </TableCell>
                        <TableCell>{format(new Date(goal.created_at), 'MM/dd/yyyy')}</TableCell>
                        <TableCell>{goal.created_by?.name || 'System'}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <AddEditCustomGoal
                              isEdit={true}
                              goal={goal}
                              onSuccess={refetchGoals}
                            />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteClick(goal.id)}
                            >
                              <Trash2 className="h-4 w-4 text-red-600" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>

        <div className="p-4 border-t">
          <div className="text-sm text-gray-500">
            {goalsData ? (
              `Showing ${filteredGoals.length} of ${goalsData.length} results`
            ) : (
              "Loading..."
            )}
          </div>
        </div>
      </div>
    </div>
  );
}