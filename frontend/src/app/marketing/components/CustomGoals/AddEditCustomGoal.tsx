"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormDescription,
} from "@/components/ui/form";
import { Edit, Plus, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import {
    useCreateMarketingGoal,
    useUpdateMarketingGoal
} from "@/services/marketing-goals-api";
import {
    MarketingGoal,
    GoalCategory,
    GoalCategoryLabels,
    GoalMetricType,
    GoalMetricTypeLabels,
    GoalRecurrence,
    GoalRecurrenceLabels
} from "@/type/marketing-goals";
import { Checkbox } from "@/components/ui/checkbox";

const marketingGoalFormSchema = z.object({
    name: z.string().min(1, "Name is required"),
    category: z.string().optional(),
    metric_type: z.string().optional(),
    default_target: z.string().optional(),
    recurrence: z.string().optional(),
    is_active: z.boolean().default(true),
    description: z.string().optional().default(""),
});

type MarketingGoalFormValues = z.infer<typeof marketingGoalFormSchema>;

interface AddEditCustomGoalProps {
    isEdit?: boolean;
    goal?: MarketingGoal;
    onSuccess?: () => void;
}

export function AddEditCustomGoal({
    isEdit = false,
    goal,
    onSuccess,
}: AddEditCustomGoalProps) {
    const [open, setOpen] = useState(false);
    const { toast } = useToast();

    // Create and update mutations
    const createGoal = useCreateMarketingGoal();
    const updateGoal = useUpdateMarketingGoal(goal?.id || 0);

    const isSubmitting = createGoal.isPending || updateGoal.isPending;

    const form = useForm<MarketingGoalFormValues>({
        resolver: zodResolver(marketingGoalFormSchema),
        defaultValues: {
            name: "",
            category: GoalCategory.ENGAGEMENT,
            metric_type: GoalMetricType.COUNT,
            default_target: "",
            recurrence: GoalRecurrence.NONE,
            is_active: true,
            description: "",
        },
    });

    // Set form values when editing
    useEffect(() => {
        if (isEdit && goal && open) {
            form.reset({
                name: goal.name,
                category: goal.category,
                metric_type: goal.metric_type,
                default_target: goal.default_target.toString(),
                recurrence: goal.recurrence,
                is_active: goal.is_active,
                description: goal.description || "",
            });
        }
    }, [isEdit, goal, open]); // eslint-disable-line react-hooks/exhaustive-deps

    const onSubmitForm = async (data: MarketingGoalFormValues) => {
        try {
            if (isSubmitting) return;

            const formattedData = {
                name: data.name,
                category: data.category as GoalCategory || GoalCategory.ENGAGEMENT,
                metric_type: data.metric_type as GoalMetricType || GoalMetricType.COUNT,
                default_target: data.default_target ? parseFloat(data.default_target) : 0,
                recurrence: data.recurrence as GoalRecurrence || GoalRecurrence.NONE,
                is_active: data.is_active,
                description: data.description,
            };

            if (isEdit && goal) {
                updateGoal.mutate(
                    formattedData,
                    {
                        onSuccess: () => {
                            toast({
                                title: "Goal Updated",
                                description: "The marketing goal has been updated successfully.",
                            });
                            form.reset();
                            setOpen(false);
                            onSuccess?.();
                        },
                        onError: (error) => {
                            console.error("Error updating goal:", error);
                            toast({
                                title: "Error",
                                description: "Failed to update marketing goal. Please try again.",
                                variant: "destructive",
                            });
                        }
                    }
                );
            } else {
                createGoal.mutate(
                    formattedData,
                    {
                        onSuccess: () => {
                            toast({
                                title: "Goal Created",
                                description: "A new marketing goal has been created successfully.",
                            });
                            form.reset();
                            setOpen(false);
                            onSuccess?.();
                        },
                        onError: (error) => {
                            console.error("Error creating goal:", error);
                            toast({
                                title: "Error",
                                description: "Failed to create marketing goal. Please try again.",
                                variant: "destructive",
                            });
                        }
                    }
                );
            }
        } catch (error) {
            console.error("Error submitting form:", error);
            toast({
                title: "Error",
                description: "Failed to save marketing goal. Please try again.",
                variant: "destructive",
            });
        }
    };

    return (
        <Dialog open={open} onOpenChange={(newOpen) => {
            setOpen(newOpen);
            if (!newOpen) {
                form.reset();
            }
        }}>
            <DialogTrigger asChild>
                {isEdit ? (
                    <Button
                        variant="link"
                    >
                        <Edit className="h-4 w-4 text-green-600" />
                    </Button>
                ) : (
                    <Button
                        variant="link"
                        className="text-green-600"
                    >
                        <Plus className="h-4 w-4 text-green-600" />
                        Add Marketing Goal
                    </Button>
                )}
            </DialogTrigger>

            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>
                        {isEdit ? "Edit Marketing Goal" : "Add Marketing Goal"}
                    </DialogTitle>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmitForm)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Enter goal name" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="category"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Category</FormLabel>
                                    <Select
                                        onValueChange={field.onChange}
                                        value={field.value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select category" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent searchable>
                                            {Object.entries(GoalCategoryLabels).map(([value, label]) => (
                                                <SelectItem key={value} value={value}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="metric_type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Metric Type</FormLabel>
                                    <Select
                                        onValueChange={field.onChange}
                                        value={field.value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select metric type" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent searchable>
                                            {Object.entries(GoalMetricTypeLabels).map(([value, label]) => (
                                                <SelectItem key={value} value={value}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="default_target"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Default Target</FormLabel>
                                    <FormControl>
                                        <Input {...field} type="number" placeholder="Enter default target value" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="recurrence"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Recurrence</FormLabel>
                                    <Select
                                        onValueChange={field.onChange}
                                        value={field.value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select recurrence" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent searchable>
                                            {Object.entries(GoalRecurrenceLabels).map(([value, label]) => (
                                                <SelectItem key={value} value={value}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="is_active"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                        <FormLabel>Active</FormLabel>
                                        <FormDescription>
                                            This goal will be available for assignment to users
                                        </FormDescription>
                                    </div>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Description</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            {...field}
                                            placeholder="Enter goal description"
                                            className="resize-none"
                                            rows={3}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <DialogFooter>
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setOpen(false);
                                    form.reset();
                                }}
                                type="button"
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                {isEdit ? "Update" : "Add"} Goal
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
