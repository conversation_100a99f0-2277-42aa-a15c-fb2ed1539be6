"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Edit, Plus } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { PhoneNumberInput } from "@/components/ui/phone-number-input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { PartnerType, PartnerTypeLabels, RelationshipStatus, RelationshipStatusLabels } from "@/type/marketing";
import { USStates, USStatesLabels } from "@/constants/commont";
import { OrganizationUser, useOrganizationUsers } from "@/services/organizationService";
import { useCreatePartnerContact, useUpdatePartnerContact } from '@/services/marketingService';
import { User } from "@/type/user";
import { RequiredLabel } from "@/components/ui/required-label";

const contactFormSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  phone: z.string().optional(),
  phone_ext: z.string().optional(),
  role: z.string().optional(),
  business_name: z.string().optional(),
  partner_type: z.nativeEnum(PartnerType).optional(),
  relationship_status: z.nativeEnum(RelationshipStatus).optional(),
  website: z.string().optional(),
  instagram: z.string().optional(),
  specialties: z.string().optional(),

  // Address Information
  street1: z.string().optional(),
  street2: z.string().optional(),
  city: z.string().optional(),
  state: z.nativeEnum(USStates).optional(),
  zip_code: z.string().optional(),

  // Medical Provider Specific
  accepts_liens: z.boolean().default(false),
  no_records_service: z.boolean().default(false),
  do_not_use: z.boolean().default(false),

  // Additional Information
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
  assigned_to_id: z.number().optional(),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

interface Contact extends ContactFormValues {
  id: number;
  organization: number;
  assigned_to?: User | null;
  total_referrals: number;
  successful_referrals: number;
  last_engagement_date?: string;
  next_engagement_date?: string;
  average_case_value?: number;
  total_revenue_generated?: number;
  created_at: string;
  updated_at: string;
  photo_url?: string;
}

interface AddEditContactProps {
  isEdit?: boolean;
  initialData?: Contact;
  children?: React.ReactNode;
}

export function AddEditContact({
  isEdit = false,
  initialData,
  children,
}: AddEditContactProps) {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userSearchTerm, setUserSearchTerm] = useState("");
  const { data: users } = useOrganizationUsers(userSearchTerm);
  // Photo upload is now handled separately

  // Initialize mutations - always initialize both to follow hooks rules
  const createContact = useCreatePartnerContact();
  const updateContact = useUpdatePartnerContact(initialData?.id || 0);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      phone_ext: "",
      role: "",
      business_name: "",
      partner_type: PartnerType.MEDICAL,
      relationship_status: RelationshipStatus.ACTIVE,
      website: "",
      instagram: "",
      specialties: "",
      street1: "",
      street2: "",
      city: "",
      state: USStates.WA,
      zip_code: "",
      accepts_liens: false,
      no_records_service: false,
      do_not_use: false,
      notes: "",
      is_active: true,
      assigned_to_id: undefined,
    },
  });

  // Effect to set form values when initialData changes or dialog opens
  useEffect(() => {
    if (initialData && open) {
      // Ensure all fields are properly typed and defaulted
      const formData = {
        first_name: initialData.first_name || "",
        last_name: initialData.last_name || "",
        email: initialData.email || "",
        phone: initialData.phone || "",
        phone_ext: initialData.phone_ext || "",
        role: initialData.role || "",
        business_name: initialData.business_name || "",
        partner_type: initialData.partner_type || PartnerType.MEDICAL,
        relationship_status: initialData.relationship_status || RelationshipStatus.ACTIVE,
        website: initialData.website || "",
        instagram: initialData.instagram || "",
        specialties: initialData.specialties || "",
        street1: initialData.street1 || "",
        street2: initialData.street2 || "",
        city: initialData.city || "",
        state: initialData.state || USStates.WA,
        zip_code: initialData.zip_code || "",
        accepts_liens: initialData.accepts_liens || false,
        no_records_service: initialData.no_records_service || false,
        do_not_use: initialData.do_not_use || false,
        notes: initialData.notes || "",
        is_active: initialData.is_active ?? true,
        assigned_to_id: initialData.assigned_to?.id ? Number(initialData.assigned_to.id) : undefined,
      };

      console.log("Setting form data with assigned_to_id:", formData.assigned_to_id); // Debug log
      form.reset(formData);
    } else if (!open) {
      // Reset to default values when closing
      form.reset({
        first_name: "",
        last_name: "",
        email: "",
        phone: "",
        phone_ext: "",
        role: "",
        business_name: "",
        partner_type: PartnerType.MEDICAL,
        relationship_status: RelationshipStatus.ACTIVE,
        website: "",
        instagram: "",
        specialties: "",
        street1: "",
        street2: "",
        city: "",
        state: USStates.WA,
        zip_code: "",
        accepts_liens: false,
        no_records_service: false,
        do_not_use: false,
        notes: "",
        is_active: true,
        assigned_to_id: undefined,
      });
    }
  }, [initialData, open, form]);

  // Helper function to format user name
  const formatUserName = (user?: OrganizationUser) => {
    if (!user) return "Unassigned";
    return user.first_name && user.last_name
      ? `${user.first_name} ${user.last_name}`
      : user.email;
  };

  const onSubmitForm = async (data: ContactFormValues) => {
    try {
      if (isSubmitting) return;
      setIsSubmitting(true);

      if (isEdit && initialData) {
        // Update existing contact
        await updateContact?.mutateAsync(data);
      } else {
        // Create new contact
        await createContact.mutateAsync(data);
      }

      // Close the dialog
      setOpen(false);

      // Add a small delay to ensure the query client has time to process the invalidation
      setTimeout(() => {
        // Force a window refresh to ensure the UI updates
        window.dispatchEvent(new CustomEvent('contact-updated'));
      }, 100);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description: "Failed to save contact. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Photo upload functionality has been moved to a separate component

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          isEdit ? (
            <Button
              variant="link"
              className="text-green-600"
            >
              <Edit className="h-4 w-4 text-green-600" />
            </Button>
          ) : (
            <Button
              variant="link"
              className="text-green-600"
            >
              <Plus className="h-4 w-4 stroke-[1.5]" />
              Add Contact
            </Button>
          )
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px] md:max-w-[800px] lg:max-w-[900px] h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-4">
          <DialogTitle>
            {isEdit ? "Edit Contact" : "Add Contact"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmitForm)} className="flex flex-col h-full">
            <div className="flex-1 overflow-y-auto px-6">
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>First Name</RequiredLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter First Name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="last_name"
                    render={({ field }) => (
                      <FormItem>
                        <RequiredLabel>Last Name</RequiredLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter Last Name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" placeholder="Enter Email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-3 sm:grid-cols-3 gap-2">
                  <div className="col-span-2">
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone</FormLabel>
                          <FormControl>
                            <PhoneNumberInput
                              value={field.value || ""}
                              onChange={(value) => field.onChange(value)}
                              placeholder="Enter Phone"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="phone_ext"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ext.</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Ext." />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Business Information */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="business_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter Business Name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter Role" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="partner_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Partner Type</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || PartnerType.MEDICAL}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Partner Type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PartnerType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {PartnerTypeLabels[type]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="relationship_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Relationship Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || RelationshipStatus.ACTIVE}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(RelationshipStatus).map((status) => (
                            <SelectItem key={status} value={status}>
                              {RelationshipStatusLabels[status]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Web Presence */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter Website URL" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="instagram"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Instagram</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter Instagram" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Address Information */}
              <FormField
                control={form.control}
                name="street1"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Street Address</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter Street Address" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="street2"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Street Address Line 2</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter Street Address Line 2" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter City" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || USStates.WA}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select State" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(USStates).map((state) => (
                            <SelectItem key={state} value={state}>
                              {USStatesLabels[state]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="zip_code"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-2 md:col-span-1">
                      <FormLabel>ZIP Code</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter ZIP Code" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Medical Provider Specific */}
              <div className="space-y-4 border rounded-lg p-3 md:p-4">
                <h3 className="font-medium mb-2">Medical Provider Settings</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
                  <FormField
                    control={form.control}
                    name="accepts_liens"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="!mt-0">Accepts Liens</FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="no_records_service"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="!mt-0">No Records Service</FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="do_not_use"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="!mt-0">Do Not Use</FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Specialties */}
              <FormField
                control={form.control}
                name="specialties"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Specialties</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter specialties"
                        className="min-h-[60px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter Notes"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Photo upload functionality has been moved to a separate component */}

              {/* Active Status */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Active Status
                      </FormLabel>
                      <div className="text-sm text-gray-500">
                        Determine if this contact is currently active
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

                {/* Assigned To */}
                <FormField
                  control={form.control}
                  name="assigned_to_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assigned To</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(value === "unassigned" ? undefined : Number(value))}
                        value={field.value?.toString() || "unassigned"}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue>
                              {field.value
                                ? users?.find(user => user.id === field.value)?.email ||
                                  initialData?.assigned_to?.email ||
                                  "Select Assignee"
                                : "Unassigned"
                              }
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent searchable onSearchChange={(value) => setUserSearchTerm(value)}>
                          <SelectItem value="unassigned">Unassigned</SelectItem>
                          {users?.map((user: OrganizationUser) => (
                            <SelectItem key={user.id} value={user.id.toString()}>
                              {formatUserName(user)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0 p-6 mt-4 mb-10 border-t bg-white">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                type="button"
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
                {isEdit ? "Update" : "Add"} Contact
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}