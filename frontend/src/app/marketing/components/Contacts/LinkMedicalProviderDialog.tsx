"use client";

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Link2, Unlink } from "lucide-react";
import { useAvailableMedicalProviders, useLinkMedicalProvider } from '@/services/marketingService';
import { Skeleton } from "@/components/ui/skeleton";
import { MedicalProvider } from '@/type/marketing';

interface LinkMedicalProviderDialogProps {
  partnerId: number;
  partnerName: string;
  currentProvider?: MedicalProvider | null;
}

export function LinkMedicalProviderDialog({ 
  partnerId, 
  partnerName,
  currentProvider 
}: LinkMedicalProviderDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedProviderId, setSelectedProviderId] = React.useState<string>("");
  
  const { data: providers, isLoading } = useAvailableMedicalProviders();
  const linkMutation = useLinkMedicalProvider(partnerId);

  const handleLink = async () => {
    try {
      await linkMutation.mutateAsync({ 
        medical_provider_id: selectedProviderId ? parseInt(selectedProviderId) : null 
      });
      setOpen(false);
    } catch (error) {
      console.error('Error linking medical provider:', error);
    }
  };

  const handleUnlink = async () => {
    try {
      await linkMutation.mutateAsync({ medical_provider_id: null });
      setOpen(false);
    } catch (error) {
      console.error('Error unlinking medical provider:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
        >
          {currentProvider ? (
            <Unlink className="h-4 w-4 text-orange-600" />
          ) : (
            <Link2 className="h-4 w-4 text-blue-600" />
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {currentProvider ? 
              `Update Medical Provider for ${partnerName}` : 
              `Link Medical Provider to ${partnerName}`
            }
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {currentProvider && (
            <div className="text-sm text-gray-500">
              Currently linked to: <span className="font-medium text-gray-900">{currentProvider.company}</span>
              <div className="text-xs">Specialties: {currentProvider.specialties}</div>
            </div>
          )}

          {isLoading ? (
            <Skeleton className="h-10 w-full" />
          ) : !providers?.length ? (
            <div className="text-center py-2 text-gray-500">
              No medical providers available.
            </div>
          ) : (
            <Select value={selectedProviderId} onValueChange={setSelectedProviderId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a medical provider" />
              </SelectTrigger>
              <SelectContent>
                {providers.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id.toString()}>
                    <div className="flex flex-col">
                      <span>{provider.company}</span>
                      <span className="text-xs text-gray-500">{provider.specialties}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          <div className="flex justify-end gap-3">
            {currentProvider && (
              <Button
                variant="destructive"
                onClick={handleUnlink}
                disabled={linkMutation.isPending}
              >
                Unlink Provider
              </Button>
            )}
            <Button
              onClick={handleLink}
              disabled={!selectedProviderId || linkMutation.isPending}
            >
              {currentProvider ? 'Update Link' : 'Link Provider'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 