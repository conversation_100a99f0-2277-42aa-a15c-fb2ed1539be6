"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, X, Camera } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useUploadProfilePhoto, useDownloadProfilePhoto } from "@/services/profilePhotoService";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Constants for file validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

interface ProfilePhotoUploadProps {
  partnerId: number;
  currentPhotoUrl?: string | null;
  name: string;
}

export function ProfilePhotoUpload({ partnerId, currentPhotoUrl, name }: ProfilePhotoUploadProps) {
  const [open, setOpen] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(currentPhotoUrl || null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Initialize the upload mutation
  const uploadPhoto = useUploadProfilePhoto(partnerId);

  // Check if the contact has a profile photo (based on the URL from the API)
  const hasProfilePhoto = !!currentPhotoUrl;

  // Get the download URL for the profile photo only if it exists
  const { data: photoData, isLoading: isLoadingPhoto } = useDownloadProfilePhoto(
    partnerId,
    hasProfilePhoto
  );

  // Update photoPreview when currentPhotoUrl or photoData changes
  useEffect(() => {
    // Prioritize the fresh URL from the API if available
    if (photoData?.url) {
      setPhotoPreview(photoData.url);
    } else if (currentPhotoUrl) {
      setPhotoPreview(currentPhotoUrl);
    } else {
      setPhotoPreview(null);
    }
  }, [currentPhotoUrl, photoData?.url]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast({
        title: "Error",
        description: "Image size should be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    // Validate file type
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      toast({
        title: "Error",
        description: "Only .jpg, .jpeg, .png and .webp formats are supported",
        variant: "destructive",
      });
      return;
    }

    // Create a preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setPhotoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Store the file for upload
    setSelectedFile(file);
  };

  const handleRemovePhoto = () => {
    setPhotoPreview(null);
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "Error",
        description: "Please select a file to upload",
        variant: "destructive",
      });
      return;
    }

    try {
      await uploadPhoto.mutateAsync(selectedFile);
      setOpen(false); // Close dialog on success
    } catch (error) {
      console.error("Error uploading photo:", error);
    }
  };

  // Render the initials for the avatar when no photo is available
  const renderInitials = () => {
    if (!name) return "";
    const nameParts = name.split(" ");
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
    }
    return name[0].toUpperCase();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 p-0 rounded-full overflow-hidden relative group"
              >
                {isLoadingPhoto ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-emerald-700"></div>
                  </div>
                ) : photoPreview ? (
                  <>
                    <img
                      src={photoPreview}
                      alt={name}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          const fallback = document.createElement('span');
                          fallback.className = 'text-sm font-medium text-emerald-700';
                          fallback.textContent = renderInitials();
                          parent.appendChild(fallback);
                        }
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Camera className="h-4 w-4 text-white" />
                    </div>
                  </>
                ) : (
                  <>
                    <span className="text-sm font-medium text-emerald-700">
                      {renderInitials()}
                    </span>
                    <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Camera className="h-4 w-4 text-white" />
                    </div>
                  </>
                )}
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Update Profile Photo</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Profile Photo</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center gap-4 py-4">
          <div className="relative h-32 w-32 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center border">
            {isLoadingPhoto ? (
              <div className="flex flex-col items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-700 mb-2"></div>
                <span className="text-xs text-gray-500">Loading photo...</span>
              </div>
            ) : photoPreview ? (
              <>
                <img
                  src={photoPreview}
                  alt="Profile preview"
                  className="h-full w-full object-cover"
                />
                <button
                  type="button"
                  onClick={handleRemovePhoto}
                  className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-full m-1 hover:bg-red-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center text-gray-400">
                <Camera className="h-8 w-8 mb-2" />
                <span className="text-xs">No photo</span>
              </div>
            )}
          </div>

          <div className="w-full max-w-xs">
            <input
              type="file"
              accept={ACCEPTED_IMAGE_TYPES.join(",")}
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="w-full"
            >
              <Upload className="h-4 w-4 mr-2" />
              Select Photo
            </Button>
            <p className="text-xs text-gray-500 mt-2 text-center">
              Supported formats: JPG, PNG, WebP. Max size: 5MB
            </p>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || uploadPhoto.isPending || isLoadingPhoto}
          >
            {uploadPhoto.isPending ? "Uploading..." : "Upload Photo"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
