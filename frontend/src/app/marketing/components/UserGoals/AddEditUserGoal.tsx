"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Footer,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Edit, Plus, Loader2 } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import {
    useMarketingGoals,
    useCreateUserMarketingGoal,
    useUpdateUserMarketingGoal
} from "@/services/marketing-goals-api";
import { OrganizationUser, useOrganizationUsers } from "@/services/organizationService";
import {
    UserMarketingGoal,
    GoalStatus,
    MarketingGoal
} from "@/type/marketing-goals";
import { format } from "date-fns";
import { useEngagementTypes } from "@/services/marketingService";
import { EngagementType } from "@/type/marketing";
import { RequiredLabel } from "@/components/ui/required-label";

const userGoalFormSchema = z.object({
    user_id: z.string().min(1, "User is required"),
    goal_id: z.string().min(1, "Goal is required"),
    target_value: z.string().min(1, "Target value is required"),
    status: z.string().min(1, "Status is required"),
    current_period_start: z.string().min(1, "Start date is required"),
    current_period_end: z.string().min(1, "End date is required"),
    is_recurring: z.boolean().default(false),
    recurrence_end_date: z.string().optional().default(""),
    notes: z.string().optional(),
    type: z.enum(["engagement","goal"])
});

type UserGoalFormValues = z.infer<typeof userGoalFormSchema>;

interface AddEditUserGoalProps {
    isEdit?: boolean;
    userGoal?: UserMarketingGoal;
    onSuccess?: () => void;
}

// Custom hook for debounced value
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]);

    return debouncedValue;
}

export function AddEditUserGoal({
    isEdit = false,
    userGoal,
    onSuccess,
}: AddEditUserGoalProps) {
    const [open, setOpen] = useState(false);
    const { toast } = useToast();
    const [showRecurrenceEndDate, setShowRecurrenceEndDate] = useState(false);
    const [userSearchTerm, setUserSearchTerm] = useState("");

    // Use the custom debounce hook
    const debouncedSearchTerm = useDebounce(userSearchTerm, 300);

    // Memoize the search handler
    const handleSearchChange = useCallback((value: string) => {
        // Prevent form submission/refresh when typing in search
        // by stopping event propagation
        setUserSearchTerm(value);
    }, []);

    // Helper function to format user name - memoized to prevent unnecessary re-renders
    const formatUserName = useCallback((user?: OrganizationUser) => {
        if (!user) return "Unknown";
        return user.first_name && user.last_name ?
            `${user.first_name} ${user.last_name}` :
            user.email || user.username || "Unknown";
    }, []);

    // Fetch data
    const { data: marketingGoals, isLoading: isLoadingGoals } = useMarketingGoals();
    const { data: engagementType, isLoading: isLoadingEngagementType } = useEngagementTypes();

    // Use a separate query for user search to prevent form resets
    const { data: users, isLoading: isLoadingUsers } = useOrganizationUsers(debouncedSearchTerm, {
        // Add staleTime to prevent unnecessary refetches
        staleTime: 60000, // 1 minute
        // Use placeholderData instead of keepPreviousData in newer React Query versions
        placeholderData: (previousData) => previousData
    });

    // Create and update mutations
    const { mutate: createUserGoal, isPending: isCreating } = useCreateUserMarketingGoal();
    const { mutate: updateUserGoal, isPending: isUpdating } = useUpdateUserMarketingGoal(userGoal?.id || 0);

    const isSubmitting = isCreating || isUpdating;

    const form = useForm<UserGoalFormValues>({
        resolver: zodResolver(userGoalFormSchema),
        defaultValues: {
            user_id: "",
            goal_id: "",
            target_value: "",
            status: GoalStatus.ACTIVE,
            current_period_start: format(new Date(), 'yyyy-MM-dd'),
            current_period_end: format(new Date(new Date().setMonth(new Date().getMonth() + 1)), 'yyyy-MM-dd'),
            is_recurring: false,
            recurrence_end_date: "",
            type: "engagement",
            notes: "",
        },
    });

    // Effect for setting initial form values - only run when editing and form is opened
    useEffect(() => {
        if (isEdit && userGoal && open) {
            // Determine the goal type and ID
            let goalType: "goal" | "engagement" = 'goal';
            let goalId = '';

            if (userGoal.marketing_goal) {
                goalType = 'goal';
                goalId = `goal:${userGoal.marketing_goal.id}`;
            } else if (userGoal.engagement_goal || userGoal.engagement_type) {
                goalType = 'engagement';
                goalId = `engagement:${userGoal.engagement_goal?.id || userGoal.engagement_type?.id}`;
            }

            console.log('Setting form values:', {
                goalType,
                goalId,
                userGoal
            });

            form.reset({
                user_id: userGoal.user.id.toString(),
                goal_id: goalId.split(':')[1], // Store just the ID number
                target_value: userGoal.target_value.toString(),
                status: userGoal.status,
                current_period_start: format(new Date(userGoal.current_period_start), 'yyyy-MM-dd'),
                current_period_end: format(new Date(userGoal.current_period_end), 'yyyy-MM-dd'),
                is_recurring: userGoal.is_recurring,
                recurrence_end_date: userGoal.recurrence_end_date ? format(new Date(userGoal.recurrence_end_date), 'yyyy-MM-dd') : "",
                notes: userGoal.notes || "",
                type: goalType
            });
            setShowRecurrenceEndDate(userGoal.is_recurring);
        }
    }, [isEdit, userGoal, open, form]);

    // Watch is_recurring to show/hide recurrence_end_date field
    const isRecurring = form.watch("is_recurring");
    useEffect(() => {
        setShowRecurrenceEndDate(isRecurring);
    }, [isRecurring]);

    const onSubmitForm = async (data: UserGoalFormValues) => {
        try {
            if (isSubmitting) return;

            // Log the form data for debugging
            console.log('Form data:', data);

            const formattedData = {
                user_id: parseInt(data.user_id),
                goal_id: parseInt(data.goal_id),
                type: data.type,
                target_value: parseFloat(data.target_value),
                status: data.status as GoalStatus,
                current_period_start: data.current_period_start,
                current_period_end: data.current_period_end,
                is_recurring: data.is_recurring,
                recurrence_end_date: data.is_recurring ? data.recurrence_end_date : undefined,
                notes: data.notes,
            };

            // Log the formatted data being sent to the API
            console.log('Sending to API:', formattedData);

            // Ensure we're sending the correct data structure based on the goal type
            // For engagement goals, we need to ensure the correct ID is being used
            if (formattedData.type === 'engagement') {
                // For engagement goals, ensure we're using the correct ID format
                console.log('Processing engagement goal with ID:', formattedData.goal_id);

                // Make sure the engagement ID exists before submitting
                const engagementExists = engagementType?.some(
                    (engagement: EngagementType) => engagement.id === formattedData.goal_id
                );

                if (!engagementExists) {
                    console.error(`Engagement with ID ${formattedData.goal_id} not found in available engagements`);
                    toast({
                        title: "Error",
                        description: `Engagement type with ID ${formattedData.goal_id} does not exist. Please select a valid engagement type.`,
                        variant: "destructive",
                    });
                    return;
                }

                // Log the selected engagement for debugging
                const selectedEngagement = engagementType?.find(
                    (engagement: EngagementType) => engagement.id === formattedData.goal_id
                );
                console.log('Selected engagement:', selectedEngagement);
            } else {
                // For marketing goals, ensure we're using the correct ID format
                console.log('Processing marketing goal with ID:', formattedData.goal_id);

                // Make sure the goal ID exists before submitting
                const goalExists = marketingGoals?.some(
                    (goal: MarketingGoal) => goal.id === formattedData.goal_id
                );

                if (!goalExists) {
                    console.error(`Goal with ID ${formattedData.goal_id} not found in available goals`);
                    toast({
                        title: "Error",
                        description: `Marketing goal with ID ${formattedData.goal_id} does not exist. Please select a valid goal.`,
                        variant: "destructive",
                    });
                    return;
                }

                // Log the selected goal for debugging
                const selectedGoal = marketingGoals?.find(
                    (goal: MarketingGoal) => goal.id === formattedData.goal_id
                );
                console.log('Selected goal:', selectedGoal);
            }

            if (isEdit && userGoal) {
                updateUserGoal(
                    { id: userGoal.id, ...formattedData },
                    {
                        onSuccess: () => {
                            toast({
                                title: "Goal Updated",
                                description: "The user goal has been updated successfully.",
                            });
                            form.reset();
                            setOpen(false);
                            onSuccess?.();
                        },
                        onError: (error) => {
                            console.error("Error updating goal:", error);
                            toast({
                                title: "Error",
                                description: "Failed to update user goal. Please try again.",
                                variant: "destructive",
                            });
                        }
                    }
                );
            } else {
                createUserGoal(
                    formattedData,
                    {
                        onSuccess: () => {
                            toast({
                                title: "Goal Created",
                                description: "A new user goal has been created successfully.",
                            });
                            form.reset();
                            setOpen(false);
                            onSuccess?.();
                        },
                        onError: (error) => {
                            console.error("Error creating goal:", error);

                            // Try to extract more detailed error information
                            let errorMessage = "Failed to create user goal. Please try again.";

                            // Check if it's an Axios error with response data
                            // @ts-expect-error - Handling Axios error which might have a cause with response data
                            if (error.cause?.response?.data) {
                                // @ts-expect-error - Accessing Axios error response data
                                const responseData = error.cause.response.data;
                                console.error("API error response:", responseData);

                                // Try to extract specific error messages
                                if (responseData.goal_id) {
                                    errorMessage = `Goal error: ${responseData.goal_id}`;
                                } else if (responseData.detail) {
                                    errorMessage = responseData.detail;
                                } else if (typeof responseData === 'string') {
                                    errorMessage = responseData;
                                } else {
                                    // Try to stringify the entire response
                                    try {
                                        errorMessage = JSON.stringify(responseData);
                                    } catch (e) {
                                        console.error("Error stringifying response:", e);
                                    }
                                }
                            }

                            toast({
                                title: "Error",
                                description: errorMessage,
                                variant: "destructive",
                            });
                        }
                    }
                );
            }
        } catch (error) {
            console.error("Error submitting form:", error);
            toast({
                title: "Error",
                description: "Failed to save user goal. Please try again.",
                variant: "destructive",
            });
        }
    };

    return (
        <Dialog open={open} onOpenChange={(newOpen) => {
            setOpen(newOpen);
            if (!newOpen) {
                form.reset();
                setUserSearchTerm(""); // Reset search term when dialog closes
            }
        }}>
            <DialogTrigger asChild>
                {isEdit ? (
                    <Button
                        variant="link"
                    >
                        <Edit className="h-4 w-4 text-green-600" />
                    </Button>
                ) : (
                    <Button
                        variant="link"
                        className="text-green-600"
                    >
                        <Plus className="h-4 w-4 text-green-600" />
                        Add User Goal
                    </Button>
                )}
            </DialogTrigger>

            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>
                        {isEdit ? "Edit User Goal" : "Add User Goal"}
                    </DialogTitle>
                </DialogHeader>

                {(isLoadingGoals || isLoadingUsers || isLoadingEngagementType) ? (
                    <div className="flex justify-center items-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin mr-2" />
                        <span>Loading...</span>
                    </div>
                ) : (
                    <Form {...form}>
                        <form onSubmit={(e) => {
                            // Only submit if it's a real form submission, not from search interactions
                            const target = e.target as HTMLFormElement;
                            const isRealSubmit = target.querySelector('button[type="submit"]:focus');

                            if (isRealSubmit) {
                                form.handleSubmit(onSubmitForm)(e);
                            } else {
                                e.preventDefault();
                            }
                        }} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="user_id"
                                render={({ field }) => (
                                    <FormItem>
                                        <RequiredLabel>User</RequiredLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select user" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent
                                                searchable
                                                onSearchChange={handleSearchChange}
                                                // Prevent propagation of events from the search input
                                                onClick={(e) => e.stopPropagation()}
                                                onKeyDown={(e) => {
                                                    // Prevent form submission on Enter key in search
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                    }
                                                }}
                                            >
                                                {isLoadingUsers ? (
                                                    <div className="p-2 text-center text-gray-500">
                                                        <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                                                        Searching...
                                                    </div>
                                                ) : users?.length === 0 ? (
                                                    <div className="p-2 text-center text-gray-500">
                                                        No users found
                                                    </div>
                                                ) : (
                                                    users?.map((user: OrganizationUser) => (
                                                        <SelectItem
                                                            key={user.id}
                                                            value={user.id.toString()}
                                                        >
                                                            {formatUserName(user)}
                                                        </SelectItem>
                                                    ))
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="goal_id"
                                render={({ field }) => (
                                    <FormItem>
                                        <RequiredLabel>Goal</RequiredLabel>
                                        <Select
                                            onValueChange={(value) => {
                                                // Parse the value to extract type and id
                                                const [type, idStr] = value.split(':');
                                                const id = parseInt(idStr);

                                                console.log(`Selected ${type} with ID ${id}`);

                                                // Set both goal_id and type separately in different fields
                                                form.setValue("goal_id", id.toString());
                                                form.setValue("type", type === 'goal' ? 'goal' : 'engagement');
                                            }}
                                            // Construct the value based on current type and goal_id
                                            value={field.value ? `${form.getValues('type')}:${field.value}` : ''}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select goal">
                                                        {/* Show the selected goal/engagement name */}
                                                        {form.getValues('type') === 'goal' 
                                                            ? marketingGoals?.find(g => g.id.toString() === field.value)?.name
                                                            : engagementType?.find(e => e.id.toString() === field.value)?.name}
                                                    </SelectValue>
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent
                                                searchable
                                                onClick={(e) => e.stopPropagation()}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                    }
                                                }}
                                            >
                                                {/* Marketing Goals Group */}
                                                <div className="px-2 py-1.5 text-sm font-semibold text-gray-500 bg-gray-50">
                                                    Marketing Goals
                                                </div>
                                                {marketingGoals?.filter((goal: MarketingGoal) => goal.is_active).map((goal: MarketingGoal) => (
                                                    <SelectItem
                                                        key={`goal-${goal.id}`}
                                                        value={`goal:${goal.id}`}
                                                        className="pl-4"
                                                    >
                                                        {goal.name}
                                                    </SelectItem>
                                                ))}

                                                {/* Engagement Types Group */}
                                                <div className="px-2 py-1.5 text-sm font-semibold text-gray-500 bg-gray-50 mt-2">
                                                    Engagement Types
                                                </div>
                                                {engagementType?.filter((engagement: EngagementType) => engagement.is_active).map((engagement: EngagementType) => (
                                                    <SelectItem
                                                        key={`engagement-${engagement.id}`}
                                                        value={`engagement:${engagement.id}`}
                                                        className="pl-4"
                                                    >
                                                        {engagement.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="target_value"
                                render={({ field }) => (
                                    <FormItem>
                                        <RequiredLabel>Target Value</RequiredLabel>
                                        <FormControl>
                                            <Input {...field} type="number" placeholder="Enter target value" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="current_period_start"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Start Date</FormLabel>
                                            <FormControl>
                                                <Input {...field} type="date" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="current_period_end"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>End Date</FormLabel>
                                            <FormControl>
                                                <Input {...field} type="date" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="status"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Status</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select status" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent
                                                searchable
                                                onClick={(e) => e.stopPropagation()}
                                                onKeyDown={(e) => {
                                                    // Prevent form submission on Enter key in search
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                    }
                                                }}
                                            >
                                                <SelectItem value={GoalStatus.ACTIVE}>Active</SelectItem>
                                                <SelectItem value={GoalStatus.COMPLETED}>Completed</SelectItem>
                                                <SelectItem value={GoalStatus.ARCHIVED}>Archived</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="is_recurring"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                                        <FormControl>
                                            <input
                                                type="checkbox"
                                                checked={field.value}
                                                onChange={field.onChange}
                                                className="h-4 w-4 mt-1"
                                            />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                            <FormLabel>Recurring Goal</FormLabel>
                                            <p className="text-sm text-gray-500">This goal will automatically renew after the end date</p>
                                        </div>
                                    </FormItem>
                                )}
                            />

                            {showRecurrenceEndDate && (
                                <FormField
                                    control={form.control}
                                    name="recurrence_end_date"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Recurrence End Date</FormLabel>
                                            <FormControl>
                                                <Input {...field} type="date" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            )}

                            <FormField
                                control={form.control}
                                name="notes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Notes</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="Additional notes" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <DialogFooter>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setOpen(false);
                                        form.reset();
                                    }}
                                    type="button"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    {isEdit ? "Update" : "Add"} Goal
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                )}
            </DialogContent>
        </Dialog>
    );
}