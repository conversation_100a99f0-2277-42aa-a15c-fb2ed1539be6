"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Download, Trash2, Loader2 } from "lucide-react";
import { AddEditUserGoal } from './AddEditUserGoal';
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import {
  useUserMarketingGoals,
  useDeleteUserMarketingGoal,
  useUpdateUserGoalStatus
} from "@/services/marketing-goals-api";
import {
  GoalCategory,
  GoalStatus,
  GoalCategoryLabels,
  GoalStatusLabels
} from "@/type/marketing-goals";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

export function UserGoalsTable() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<string>("all_users");
  const [selectedCategory, setSelectedCategory] = useState<string>("all_categories");
  const [selectedStatus, setSelectedStatus] = useState<string>("all_statuses");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [goalToDelete, setGoalToDelete] = useState<number | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Prepare filters for API call
  const filters: { category?: GoalCategory; status?: GoalStatus; user_id?: number } = {};
  if (selectedCategory !== "all_categories") {
    filters.category = selectedCategory as GoalCategory;
  }
  if (selectedStatus !== "all_statuses") {
    filters.status = selectedStatus as GoalStatus;
  }
  if (selectedUser !== "all_users") {
    filters.user_id = parseInt(selectedUser);
  }

  // Fetch user goals from API
  const {
    data: userGoalsData,
    isLoading: isLoadingUserGoals,
    isError: isErrorUserGoals,
    refetch: refetchUserGoals
  } = useUserMarketingGoals(page, filters);

  // Delete mutation
  const { mutate: deleteUserGoal, isPending: isDeleting } = useDeleteUserMarketingGoal();

  // Status update mutation
  const { mutate: updateStatus } = useUpdateUserGoalStatus();

  const handleStatusChange = (goalId: number, newStatus: GoalStatus) => {
    updateStatus(
      { goalId, status: newStatus },
      {
        onSuccess: () => {
          refetchUserGoals();
        }
      }
    );
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (goalToDelete) {
      deleteUserGoal(goalToDelete, {
        onSuccess: () => {
          setDeleteDialogOpen(false);
          setGoalToDelete(null);
          refetchUserGoals();
          toast({
            title: "Success",
            description: "User goal deleted successfully",
          });
        },
        onError: () => {
          toast({
            title: "Error",
            description: "Failed to delete user goal",
            variant: "destructive",
          });
        }
      });
    }
  };

  const handleDeleteClick = (goalId: number) => {
    setGoalToDelete(goalId);
    setDeleteDialogOpen(true);
  };

  // Export to CSV
  const handleExportCSV = () => {
    if (!userGoalsData?.results) return;

    const csvContent = userGoalsData.results.map(goal => {
      // Determine if this is a marketing goal or engagement goal
      const isMarketingGoal = !!goal.marketing_goal;
      const isEngagementGoal = !!goal.engagement_goal || goal.type === 'engagement' || goal.type_of_goal === 'ENGAGEMENT_GOAL';

      // Safely get the goal name and category
      const goalName = isMarketingGoal ? goal.marketing_goal?.name :
                      isEngagementGoal && goal.engagement_goal ? goal.engagement_goal?.name :
                      isEngagementGoal && goal.engagement_type ? goal.engagement_type?.name :
                      'Unknown Goal';

      // Get category - for engagement goals, we might need to handle differently
      const category = isMarketingGoal ? GoalCategoryLabels[goal.marketing_goal?.category || 'OTHER'] :
                      isEngagementGoal && goal.engagement_goal?.category ? goal.engagement_goal.category :
                      isEngagementGoal ? 'Engagement' : 'Other';

      return {
        User: goal.user?.name || 'Unknown User',
        Goal: goalName,
        Category: category,
        Target: goal.target_value,
        Progress: goal.current_progress,
        Completion: `${goal.completion_percentage}%`,
        Status: goal.status,
        PeriodStart: format(new Date(goal.current_period_start), 'MM/dd/yyyy'),
        PeriodEnd: format(new Date(goal.current_period_end), 'MM/dd/yyyy'),
        CreatedAt: format(new Date(goal.created_at), 'MM/dd/yyyy')
      };
    });

    if (csvContent.length === 0) return;

    const headers = Object.keys(csvContent[0]);
    const csvString = [
      headers.join(','),
      ...csvContent.map(row => headers.map(header => {
        const value = row[header as keyof typeof row];
        const escapedValue = String(value).replace(/"/g, '""');
        return `"${escapedValue}"`;
      }).join(','))
    ].join('\n');

    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `user_goals_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Filter user goals based on search query
  const filteredGoals = userGoalsData?.results?.filter(goal => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();

    // Safely access properties with null checks
    const userName = goal.user?.name || '';

    // Handle both marketing goals and engagement goals
    // For engagement goals, marketing_goal will be null
    const goalName = goal.marketing_goal?.name ||
                    goal.engagement_goal?.name ||
                    goal.engagement_type?.name ||
                    '';

    return (
      userName.toLowerCase().includes(searchLower) ||
      goalName.toLowerCase().includes(searchLower) ||
      goal.target_value.toString().includes(searchLower) ||
      goal.current_progress.toString().includes(searchLower)
    );
  }) || [];

  // Get unique users and goals for filters
  const uniqueUsers = userGoalsData?.results ? Array.from(
    new Set(userGoalsData.results
      .filter(item => item.user && item.user.user_id) // Filter out items with null user
      .map(item => item.user.user_id))
  ).map(userId => {
    const user = userGoalsData.results.find(item => item.user?.user_id === userId)?.user;
    return {
      id: userId,
      name: user?.name || 'Unknown User'
    };
  }) : [];

  return (
    <div className="space-y-6">
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        text="User Goal"
      />

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 flex flex-wrap gap-4 items-center justify-between">
        <div className="flex flex-col w-full sm:flex-row sm:flex-1 gap-3 sm:items-center sm:flex-wrap">
            <Input
              placeholder="Search goals(User and Goal)..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:max-w-[250px]"
            />
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_categories">All Categories</SelectItem>
                {Object.entries(GoalCategoryLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_statuses">All Statuses</SelectItem>
                <SelectItem value={GoalStatus.ACTIVE}>Active</SelectItem>
                <SelectItem value={GoalStatus.COMPLETED}>Completed</SelectItem>
                <SelectItem value={GoalStatus.ARCHIVED}>Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedUser} onValueChange={setSelectedUser}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select User" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_users">All Users</SelectItem>
                {uniqueUsers.map((user) => (
                  <SelectItem key={user.id || 'unknown'} value={user.id?.toString() || 'unknown'}>
                    {user.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <AddEditUserGoal isEdit={false} onSuccess={refetchUserGoals} />
            <Button
              variant="ghost"
              size="icon"
              onClick={handleExportCSV}
              disabled={!userGoalsData?.results?.length}
            >
              <Download className="h-4 w-4 text-green-600" />
            </Button>
          </div>
        </div>

        <div className="border-t">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50/50">
                <TableHead className="w-[180px]">USER</TableHead>
                <TableHead className="w-[180px]">GOAL</TableHead>
                <TableHead className="w-[120px]">CATEGORY</TableHead>
                <TableHead className="w-[100px]">TARGET</TableHead>
                <TableHead className="w-[100px]">PROGRESS</TableHead>
                <TableHead className="w-[100px]">COMPLETION</TableHead>
                <TableHead className="w-[100px]">STATUS</TableHead>
                <TableHead className="w-[120px]">END DATE</TableHead>
                <TableHead className="w-[120px]">CREATED BY</TableHead>
                <TableHead className="w-[100px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoadingUserGoals ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading user goals...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : isErrorUserGoals ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-red-500">
                    Error loading user goals. Please try again.
                  </TableCell>
                </TableRow>
              ) : filteredGoals.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                    No user goals found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredGoals.map((goal) => {
                  // Determine if this is a marketing goal or engagement goal
                  const isMarketingGoal = !!goal.marketing_goal;
                  const isEngagementGoal = !!goal.engagement_goal || goal.type === 'engagement' || goal.type_of_goal === 'ENGAGEMENT_GOAL';

                  // Safely get the goal name and category
                  const goalName = isMarketingGoal ? goal.marketing_goal?.name :
                                  isEngagementGoal && goal.engagement_goal ? goal.engagement_goal?.name :
                                  isEngagementGoal && goal.engagement_type ? goal.engagement_type?.name :
                                  'Unknown Goal';

                  // Get category - for engagement goals, we might need to handle differently
                  const category = isMarketingGoal ? GoalCategoryLabels[goal.marketing_goal?.category || 'OTHER'] :
                                  isEngagementGoal && goal.engagement_goal?.category ? goal.engagement_goal.category :
                                  isEngagementGoal ? 'Engagement' : 'Other';

                  return (
                    <TableRow key={goal.id}>
                      <TableCell className="font-medium">
                        {goal.user?.name || 'Unknown User'}
                      </TableCell>
                      <TableCell>{goalName}</TableCell>
                      <TableCell>{category}</TableCell>
                      <TableCell>{goal.target_value}</TableCell>
                      <TableCell>{goal.current_progress}</TableCell>
                      <TableCell>{`${goal.completion_percentage}%`}</TableCell>
                      <TableCell>
                        <Select
                          value={goal.status}
                          onValueChange={(value) => handleStatusChange(goal.id, value as GoalStatus)}
                        >
                          <SelectTrigger className={`w-[120px] h-7 text-xs ${
                            goal.status === GoalStatus.ACTIVE ? 'bg-green-100 text-green-800' :
                            goal.status === GoalStatus.COMPLETED ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            <SelectValue>{GoalStatusLabels[goal.status]}</SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            {Object.values(GoalStatus).map((status) => (
                              <SelectItem key={status} value={status}>
                                {GoalStatusLabels[status]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{format(new Date(goal.current_period_end), 'MM/dd/yyyy')}</TableCell>
                      <TableCell>{goal.created_by?.name || 'System'}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <AddEditUserGoal
                            isEdit={true}
                            userGoal={goal}
                            onSuccess={refetchUserGoals}
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteClick(goal.id)}
                            disabled={isDeleting}
                          >
                            <Trash2 className="h-4 w-4 text-red-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>

        <div className="p-4 flex items-center justify-between border-t">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Rows per page</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => setPageSize(parseInt(value))}
            >
              <SelectTrigger className="w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-gray-500">
              {userGoalsData ? (
                `Showing ${filteredGoals.length} of ${userGoalsData.count} results`
              ) : (
                "Loading..."
              )}
            </span>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={!userGoalsData?.previous || isLoadingUserGoals}
              onClick={() => setPage(prev => Math.max(prev - 1, 1))}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={!userGoalsData?.next || isLoadingUserGoals}
              onClick={() => setPage(prev => prev + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}