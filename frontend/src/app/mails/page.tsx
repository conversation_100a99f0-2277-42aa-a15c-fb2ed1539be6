"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Plus, Loader2 } from 'lucide-react'
import ComposeDialog from './components/composeMail';
import { useToast } from "@/hooks/use-toast"
import emailService, { checkIntegrationStatus, connectNylasCalendar, exchangeAuthCode } from '@/services/emailCalenderIntegrationService'
import EmailList from './components/emailList'
import LabelSection from './components/labelSection'
import { useSearchParams, useRouter } from 'next/navigation'
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useCases } from "@/services/caseService";
import { format } from "date-fns";
import SignatureListDialog from '../settings/signatures/components/SignatureListDialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const EMAILS_PER_PAGE = 20;

export default function EmailPage() {
  const { toast } = useToast()
  const router = useRouter()
  const user = useSelector((state: RootState) => state.auth.user);
  const [isIntegrated, setIsIntegrated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const searchParams = useSearchParams()
  const folder = searchParams?.get('folder') ?? null
  const labelId = searchParams?.get('label') ?? null
  const page = Number(searchParams?.get('page')) || 1
  const searchQuery = searchParams?.get('search') ?? ''
  const [searchInput, setSearchInput] = useState(searchQuery)
  const [selectedEmailIds, setSelectedEmailIds] = useState<string[]>([])
  const attachEmailMutation = emailService.useAttachEmailToCase()
  const [unreadFilter, setUnreadFilter] = useState<boolean | undefined>(
    searchParams?.has('unread') ? searchParams?.get('unread') === 'true' : undefined
  )
  const [startDate, setStartDate] = useState<string>(searchParams?.get('start_date') ?? '')
  const [endDate, setEndDate] = useState<string>(searchParams?.get('end_date') ?? '')
  const [caseDialogOpen, setCaseDialogOpen] = useState(false);
  const [searchCaseParams, setSearchCaseParams] = useState({
    search: "",
    pageSize: 10,
    page: 1
  });
  const [isSignatureDialogOpen, setIsSignatureDialogOpen] = useState(false)

  // Get labels first
  const { data: labels } = emailService.useLabels()

  // Then use it in other queries
  const { data: emailData, isLoading: isLoadingEmails } = folder
    ? emailService.useLabelMessages(
      labels?.find(l => l.name.toLowerCase() === folder.toLowerCase())?.id || '',
      {
        page,
        limit: EMAILS_PER_PAGE,
        search: searchQuery,
        unread: unreadFilter,
        start_date: startDate || undefined,
        end_date: endDate || undefined
      }
    )
    : labelId
      ? emailService.useLabelMessages(
        labelId,
        {
          page,
          limit: EMAILS_PER_PAGE,
          search: searchQuery,
          unread: unreadFilter,
          start_date: startDate || undefined,
          end_date: endDate || undefined
        }
      )
      : emailService.useLabelMessages(
        labels?.find(l => l.name === 'Inbox')?.id || '',
        {
          page,
          limit: EMAILS_PER_PAGE,
          search: searchQuery,
          unread: unreadFilter,
          start_date: startDate || undefined,
          end_date: endDate || undefined
        }
      );

  // Update title based on current view
  const currentLabel = labels?.find(l =>
    labelId ? l.id === labelId : l.name.toLowerCase() === (folder || 'inbox').toLowerCase()
  );
  const title = currentLabel?.name || 'Inbox';

  const { data: casesData, isLoading: isLoadingCases } = useCases(searchCaseParams, true);

  const handleSearch = (e: FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams?.toString() || '');

    if (searchInput) {
      params.set('search', searchInput);
    } else {
      params.delete('search');
    }

    if (unreadFilter !== undefined) {
      params.set('unread', unreadFilter.toString());
    } else {
      params.delete('unread');
    }

    if (startDate) {
      params.set('start_date', startDate);
    } else {
      params.delete('start_date');
    }

    if (endDate) {
      params.set('end_date', endDate);
    } else {
      params.delete('end_date');
    }

    params.set('page', '1'); // Reset to first page on new search
    router.push(`/mails?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('page', newPage.toString());
    router.push(`/mails?${params.toString()}`);
  };

  const handleAuthCode = useCallback(async (code: string) => {
    try {
      setIsLoading(true);
      const response = await exchangeAuthCode(code);

      if (response.status === 'success') {
        const status = await checkIntegrationStatus();
        setIsIntegrated(status?.is_active || false);

        window.history.replaceState({}, document.title, window.location.pathname);

        toast({
          title: "Success",
          description: "Email integration completed successfully",
          variant: "default"
        });
      }
    } catch (error) {
      console.error('Error handling auth code:', error);
      toast({
        title: "Error",
        description: "Failed to complete email integration",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');

    if (code) {
      handleAuthCode(code);
    } else {
      checkIntegrationStatus().then(status => {
        setIsIntegrated(status?.is_active || false);
        setIsLoading(false);
      });
    }
  }, [handleAuthCode]);

  const handleAttachToCase = async (caseId: string) => {
    try {
      if (selectedEmailIds.length === 0) {
        toast({
          title: "Error",
          description: "Please select at least one email first",
          variant: "destructive"
        });
        return;
      }

      // Attach all selected emails to the case
      await Promise.all(
        selectedEmailIds.map(emailId =>
          attachEmailMutation.mutateAsync({
            messageId: emailId,
            caseId,
            userEmail: user?.email
          })
        )
      );

      setCaseDialogOpen(false);
      setSelectedEmailIds([]); // Clear selection after attaching
      toast({
        title: "Success",
        description: "Emails attached to case successfully",
      });
    } catch (error) {
      console.error("Error attaching emails to case:", error);
      toast({
        title: "Error",
        description: "Failed to attach emails to case",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>
  }

  if (!isIntegrated) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h1 className="text-2xl font-bold mb-4">Email Integration Required</h1>
        <p className="mb-4">Please connect your email to continue</p>
        <Button onClick={connectNylasCalendar}>
          Connect Email
        </Button>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r p-4 flex flex-col">
        <div className="mb-6 space-y-2">
          <ComposeDialog hideButton={false} />
        </div>

        {/* Labels Section */}
        <LabelSection />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <div className="bg-white border-b p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-semibold">{title}</h1>
              <span className="text-sm text-muted-foreground">
                {emailData?.messages?.length || 0} messages
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setIsSignatureDialogOpen(true)}
              >
                Manage Signatures
              </Button>
              <Button
                variant="outline"
                disabled={selectedEmailIds.length === 0}
                onClick={() => setCaseDialogOpen(true)}
              >
                Attach to Case ({selectedEmailIds.length})
              </Button>
            </div>
          </div>

          {/* Case Selection Dialog */}
          <Dialog open={caseDialogOpen} onOpenChange={setCaseDialogOpen}>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle className="text-[22px] font-bold font-Manrope text-[#060216]">Select Case</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center">
                    <Search className="w-4 h-4 inline-block mr-2" />
                    <Input
                      className="bg-white"
                      placeholder="Search by case name, client name, or date"
                      onChange={(e) => {
                        setSearchCaseParams(prev => ({
                          ...prev,
                          search: e.target.value
                        }));
                      }}
                    />
                  </div>
                </div>

                <div className="max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-[#060216]/20 scrollbar-track-[#060216]/5">
                  {isLoadingCases ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-[#060216]/50" />
                    </div>
                  ) : (
                    <Table>
                      <TableHeader className="sticky top-0 bg-white z-10">
                        <TableRow className="bg-[#060216]/5">
                          <TableHead className="w-[300px] text-[#060216]/50">Case Name</TableHead>
                          <TableHead className="text-[#060216]/50">Client</TableHead>
                          <TableHead className="text-[#060216]/50">Created By</TableHead>
                          <TableHead className="text-[#060216]/50">Date of Loss</TableHead>
                          <TableHead className="text-[#060216]/50">Status</TableHead>
                          <TableHead className="w-[100px]"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {casesData?.results?.map((caseItem) => (
                          <TableRow
                            key={caseItem.id}
                            className="cursor-pointer hover:bg-[#060216]/5"
                          >
                            <TableCell className="font-medium text-[#060216]">
                              {caseItem.case_name}
                            </TableCell>
                            <TableCell className="text-[#060216]">
                              {caseItem.name_of_client}
                            </TableCell>
                            <TableCell className="text-[#060216]">
                              {`${caseItem.created_by.name}`}
                            </TableCell>
                            <TableCell className="text-[#060216]">
                              {caseItem.accident_date ?
                                format(new Date(caseItem.accident_date), 'MM/dd/yyyy')
                                : "—"}
                            </TableCell>
                            <TableCell className="text-[#060216]">
                              {caseItem.status}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleAttachToCase(caseItem.id.toString())}
                              >
                                <Plus className="h-4 w-4 text-[#060216]" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCaseDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex flex-col gap-4">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search emails..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-9"
                />
              </div>
              <Button type="submit" variant="secondary">
                Search
              </Button>
            </div>

            <div className="flex gap-4 items-center">
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-600">Unread:</label>
                <select
                  className="text-sm border rounded-md p-1"
                  value={unreadFilter === undefined ? '' : unreadFilter.toString()}
                  onChange={(e) => {
                    const value = e.target.value;
                    setUnreadFilter(value === '' ? undefined : value === 'true');
                  }}
                >
                  <option value="">All</option>
                  <option value="true">Unread only</option>
                  <option value="false">Read only</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-600">From:</label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="text-sm"
                />
              </div>

              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-600">To:</label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="text-sm"
                />
              </div>
            </div>
          </form>
        </div>

        <div className="flex-1 overflow-auto p-4">
          {isLoadingEmails ? (
            <div className="flex items-center justify-center h-full">
              Loading...
            </div>
          ) : (
            <EmailList
              emails={emailData?.messages || []}
              currentPage={page}
              limit={EMAILS_PER_PAGE}
              onPageChange={handlePageChange}
              onEmailSelect={setSelectedEmailIds}
            />
          )}
        </div>
      </div>

      <SignatureListDialog
        open={isSignatureDialogOpen}
        onOpenChange={setIsSignatureDialogOpen}
      />
    </div>
  );
}
