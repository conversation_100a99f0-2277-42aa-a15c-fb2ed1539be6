import { useState } from 'react'
import { Avatar } from "@/components/ui/avatar"
import { format } from 'date-fns'
import { EmailMessage } from '@/type/emailCalender'
import { Star } from 'lucide-react'
import { MoreHorizontal } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import emailService from '@/services/emailCalenderIntegrationService'
import { Button } from '@/components/ui/button'
import EmailDetail from './emailDetail'
import { cn } from '@/lib/utils'
import { Paperclip } from 'lucide-react'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface EmailListProps {
  emails: EmailMessage[]
  currentPage: number
  limit: number
  onPageChange: (page: number) => void
  onEmailSelect: (emailIds: string[]) => void
}

export default function EmailList({ emails, currentPage, limit, onPageChange, onEmailSelect }: EmailListProps) {
  const { data: labels } = emailService.useLabels()
  const applyLabelMutation = emailService.useApplyLabel()
  const [selectedEmailId, setSelectedEmailId] = useState<string | null>(null)
  const { data: selectedEmail } = emailService.useEmail(selectedEmailId || '')
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])

  // Check if next page might exist based on current results
  const hasNextPage = emails.length === limit
  const hasPreviousPage = currentPage > 1

  const handleCheckboxChange = (emailId: string) => {
    const newSelectedEmails = selectedEmails.includes(emailId)
      ? selectedEmails.filter(id => id !== emailId)
      : [...selectedEmails, emailId]
    
    setSelectedEmails(newSelectedEmails)
    onEmailSelect(newSelectedEmails)
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="divide-y">
          {emails.map((email) => (
            <div 
              key={email.id} 
              className={cn(
                "px-6 py-3 hover:bg-gray-50 cursor-pointer flex items-start gap-4 transition-colors",
                !email.unread && "bg-blue-50/30",
                selectedEmails.includes(email.id) && "bg-blue-50",
                "relative group"
              )}
              onClick={() => {
                setSelectedEmailId(email.id);
              }}
            >
              <div className="flex items-center gap-2 text-gray-400">
                <input 
                  type="checkbox" 
                  className="rounded border-gray-300"
                  checked={selectedEmails.includes(email.id)}
                  onClick={(e) => e.stopPropagation()}
                  onChange={() => handleCheckboxChange(email.id)}
                />
                <Star 
                  className={cn(
                    "h-4 w-4 cursor-pointer transition-colors",
                    email.starred ? "text-yellow-400 fill-yellow-400" : "text-gray-400 hover:text-yellow-400"
                  )}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              
              <Avatar className="h-9 w-9 shrink-0">
                <span className="text-xs font-medium">
                  {email.sender?.name?.[0] || email.sender?.email?.[0]?.toUpperCase()}
                </span>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <h3 className={cn(
                      "text-sm truncate",
                      email.unread && "font-semibold text-gray-900",
                      !email.unread && "text-gray-600"
                    )}>
                      {email.sender?.name || email.sender?.email}
                    </h3>
                    <div className="flex items-baseline gap-2">
                      <h4 className={cn(
                        "text-sm truncate",
                        email.unread && "font-medium text-gray-800",
                        !email.unread && "text-gray-600"
                      )}>
                        {email.subject}
                      </h4>
                      {email.files && email.files.length > 0 && (
                        <Paperclip className="h-3 w-3 text-gray-400 shrink-0" />
                      )}
                    </div>
                    <p className="text-sm text-gray-500 line-clamp-1 mt-0.5">
                      {email.snippet}
                    </p>
                  </div>
                  <div className="flex flex-col items-end gap-1.5 shrink-0">
                    <span className="text-xs text-gray-500 whitespace-nowrap">
                      {format(new Date(email.date * 1000), 'MMM d, h:mm a')}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[180px]">
                        {labels?.map(label => (
                          <DropdownMenuItem
                            key={label.id}
                            onClick={(e) => {
                              e.stopPropagation();
                              applyLabelMutation.mutate({
                                messageId: email.id,
                                labelId: label.id
                              });
                            }}
                          >
                            <span 
                              className="w-2 h-2 rounded-full mr-2"
                              style={{ backgroundColor: label.color || '#CBD5E1' }} 
                            />
                            <span className="flex-1 truncate">{label.display_name}</span>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          ))}
          {emails.length === 0 && (
            <div className="px-6 py-8 text-center text-gray-500">
              No emails found
            </div>
          )}
        </div>
      </div>

      {/* Pagination Controls */}
      {emails.length > 0 && (
        <div className="mt-4 flex items-center justify-between px-2">
          <div className="text-sm text-gray-500">
            Page {currentPage}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={!hasPreviousPage}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            {hasNextPage && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage + 1)}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      )}

      <EmailDetail 
        email={selectedEmail || null}
        open={!!selectedEmailId}
        onOpenChange={(open) => !open && setSelectedEmailId(null)}
      />
    </>
  )
} 