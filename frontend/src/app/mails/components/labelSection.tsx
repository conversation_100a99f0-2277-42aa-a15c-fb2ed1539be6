import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, X } from "lucide-react"
import { Label } from "@/type/emailCalender"
import emailService from '@/services/emailCalenderIntegrationService'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { ChromePicker } from 'react-color'
import { useRouter } from 'next/navigation'
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"

// Helper to get folder icon color
const getFolderColor = (name: string): string => {
  switch (name.toLowerCase()) {
    case 'inbox': return '#3b82f6' // blue
    case 'sent items': return '#10b981' // green
    case 'drafts': return '#f59e0b' // amber
    case 'junk email': return '#ef4444' // red
    case 'deleted items': return '#6b7280' // gray
    case 'archive': return '#8b5cf6' // purple
    default: return '#CBD5E1' // slate
  }
}

// Add type for folder order
const FOLDER_ORDER: { [key: string]: number } = {
  'Inbox': 1,
  'Starred': 2,
  'Sent Items': 3,
  'Drafts': 4,
  'Junk Email': 5,
  'Deleted Items': 6,
  'Archive': 7,
  'Outbox': 8,
  'Conversation History': 9,
};

export default function LabelSection() {
  const router = useRouter()
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [newLabelColor, setNewLabelColor] = useState('#CBD5E1')
  const [newLabelName, setNewLabelName] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false)

  const { data: labels } = emailService.useLabels()
  const createLabelMutation = emailService.useCreateLabel()
  const deleteLabelMutation = emailService.useDeleteLabel()

  // Sort and filter system folders
  const systemFolders = labels
    ?.filter(label => label.system_folder)
    .sort((a, b) => {
      const orderA = FOLDER_ORDER[a.name] || 999;
      const orderB = FOLDER_ORDER[b.name] || 999;
      return orderA - orderB;
    }) || [];

  // Filter non-system folders (custom labels)
  const customLabels = labels
    ?.filter(label => !label.system_folder)
    .sort((a, b) => a.name.localeCompare(b.name)) || [];

  // Add special folders section at the top
  const specialFolders = systemFolders
    .filter(folder => ['Inbox', 'Starred', 'Archive'].includes(folder.name));
  
  // Regular system folders
  const regularFolders = systemFolders
    .filter(folder => !['Inbox', 'Starred', 'Archive'].includes(folder.name));

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = newLabelName !== '' || newLabelColor !== '#CBD5E1';
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      if (!newOpen) {
        handleClose();
      } else {
        setIsDialogOpen(true);
      }
    }
  };

  const handleClose = () => {
    setIsDialogOpen(false);
    setNewLabelName('');
    setNewLabelColor('#CBD5E1');
  };

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false);
    handleClose();
  };

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false);
  };

  const handleCreateLabel = async () => {
    await createLabelMutation.mutateAsync({
      name: newLabelName,
      color: newLabelColor
    })
    setIsDialogOpen(false)
    setNewLabelName('')
    setNewLabelColor('#CBD5E1')
  }

  const handleFolderClick = (folder: Label) => {
    router.push(`/mails?label=${folder.id}`);
  }

  return (
    <>
      <div className="space-y-6">
        {/* Special Folders (Inbox, Starred, Archive) */}
        <div className="space-y-1">
          {specialFolders.map((folder) => (
            <Button 
              key={folder.id}
              variant="ghost" 
              className="w-full justify-start gap-2 h-8 px-2"
              onClick={() => handleFolderClick(folder)}
            >
              <span 
                className="w-2 h-2 rounded-full" 
                style={{ backgroundColor: getFolderColor(folder.name) }} 
              />
              <span className="flex-1 text-left">{folder.name}</span>
              {folder.unread_count > 0 && (
                <span className="text-xs text-muted-foreground">
                  {folder.unread_count}
                </span>
              )}
            </Button>
          ))}
        </div>

        {/* System Folders */}
        <div>
          <div className="text-sm font-medium text-muted-foreground mb-2">
            Folders
          </div>
          <div className="space-y-1">
            {regularFolders.map((folder) => (
              <Button 
                key={folder.id}
                variant="ghost" 
                className="w-full justify-start gap-2 h-8 px-2"
                onClick={() => handleFolderClick(folder)}
              >
                <span 
                  className="w-2 h-2 rounded-full" 
                  style={{ backgroundColor: getFolderColor(folder.name) }} 
                />
                <span className="flex-1 text-left">{folder.name}</span>
                {folder.unread_count > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {folder.unread_count}
                  </span>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Custom Labels */}
        <div>
          <div className="flex items-center justify-between text-sm font-medium text-muted-foreground mb-2">
            <span>Labels</span>
            <div className="flex gap-1">
              <Dialog open={isDialogOpen} onOpenChange={handleOpenChange}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-5 w-5">
                    <Plus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Label</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 pt-4">
                    <Input
                      placeholder="Label name"
                      value={newLabelName}
                      onChange={(e) => setNewLabelName(e.target.value)}
                    />
                    <div>
                      <div 
                        className="w-8 h-8 rounded cursor-pointer border"
                        style={{ backgroundColor: newLabelColor }}
                        onClick={() => setShowColorPicker(!showColorPicker)}
                      />
                      {showColorPicker && (
                        <div className="absolute mt-2 z-50">
                          <ChromePicker
                            color={newLabelColor}
                            onChange={(color) => setNewLabelColor(color.hex)}
                          />
                        </div>
                      )}
                    </div>
                    <Button 
                      onClick={handleCreateLabel}
                      disabled={!newLabelName || createLabelMutation.isPending}
                    >
                      Create Label
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <div className="space-y-1">
            {customLabels.map((label) => (
              <div key={label.id} className="flex items-center">
                <Button 
                  variant="ghost" 
                  className="w-full justify-start gap-2 h-8 px-2"
                  onClick={() => handleFolderClick(label)}
                >
                  <span 
                    className="w-2 h-2 rounded-full" 
                    style={{ backgroundColor: label.color || '#CBD5E1' }} 
                  />
                  <span className="flex-1 text-left">{label.name}</span>
                  {label.unread_count > 0 && (
                    <span className="text-xs text-muted-foreground">
                      {label.unread_count}
                    </span>
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => deleteLabelMutation.mutate(label.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  )
} 