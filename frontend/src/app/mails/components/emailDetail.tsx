import { useEffect, useState } from 'react'
import { Di<PERSON>, DialogContent } from "@/components/ui/dialog"
import { EmailMessage } from "@/type/emailCalender"
import { format } from "date-fns"
import { Avatar } from "@/components/ui/avatar"
import { X, Paperclip, Download, Reply, ReplyAll, Forward } from "lucide-react"
import { Button } from "@/components/ui/button"
import emailService from '@/services/emailCalenderIntegrationService'
import { useQueryClient } from '@tanstack/react-query';
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import { useComposeMail } from './composeMail'

interface EmailDetailProps {
  email: EmailMessage | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface ExtendedEmailMessage extends EmailMessage {
  thread_id?: string;
}

export default function EmailDetail({ email, open, onOpenChange }: EmailDetailProps) {
  const queryClient = useQueryClient();
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false);
  const { openComposer } = useComposeMail();
  const [emailThread, setEmailThread] = useState<ExtendedEmailMessage[]>([]);
  const [isLoadingThread, setIsLoadingThread] = useState(false);

  useEffect(() => {
    const loadEmailThread = async () => {
      if ((email as ExtendedEmailMessage)?.thread_id) {
        setIsLoadingThread(true);
        try {
          setEmailThread([email as ExtendedEmailMessage]);
        } catch (error) {
          console.error('Failed to load email thread:', error);
        }
        setIsLoadingThread(false);
      } else {
        setEmailThread(email ? [email as ExtendedEmailMessage] : []);
      }
    };

    if (open && email) {
      loadEmailThread();
    }
  }, [email, open]);

  // Mark email as read when opened
  useEffect(() => {
    if (open && email && email.unread) {
      emailService.markAsRead(email.id, false)
        .then(() => {
          // Invalidate both the email list and single email queries
          queryClient.invalidateQueries({ queryKey: ['emails'] });
          queryClient.invalidateQueries({ queryKey: ['email', email.id] });
        })
        .catch(error => console.error('Failed to mark email as read:', error));
    }
  }, [open, email?.id, email?.unread, queryClient]);

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = false;
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true);
    } else {
      onOpenChange(newOpen);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleCancelClick = () => {
    const isDirty = false;
    if (isDirty) {
      setShowUnsavedAlert(true);
    } else {
      handleClose();
    }
  };

  const handleReply = () => {
    if (email) {
      const replyTo = email.sender?.email || '';
      const subject = email.subject.startsWith('Re:') ? email.subject : `Re: ${email.subject}`;
      const body = `\n\nOn ${format(new Date(email.date * 1000), 'MMM d, yyyy, h:mm a')}, ${email.sender?.name || email.sender?.email} wrote:\n> ${email.body?.replace(/\n/g, '\n> ')}`;

      // Update email data in compose dialog
      const composeData = new CustomEvent('compose-email', {
        detail: { subject, body }
      });
      window.dispatchEvent(composeData);

      openComposer(replyTo);
      handleClose();
    }
  };

  const handleReplyAll = () => {
    if (email) {
      const allRecipients = [
        email.sender?.email || '',
        ...(email.recipients?.map(r => r.email) || []),
        ...(email.cc?.map(r => r.email) || [])
      ].filter(e => e !== email.sender?.email).join(', ');
      const subject = email.subject.startsWith('Re:') ? email.subject : `Re: ${email.subject}`;
      const body = `\n\nOn ${format(new Date(email.date * 1000), 'MMM d, yyyy, h:mm a')}, ${email.sender?.name || email.sender?.email} wrote:\n> ${email.body?.replace(/\n/g, '\n> ')}`;

      // Update email data in compose dialog
      const composeData = new CustomEvent('compose-email', {
        detail: { subject, body }
      });
      window.dispatchEvent(composeData);

      openComposer(allRecipients);
      handleClose();
    }
  };

  const handleForward = () => {
    if (email) {
      const subject = email.subject.startsWith('Fwd:') ? email.subject : `Fwd: ${email.subject}`;
      const body = `\n\n---------- Forwarded message ---------\nFrom: ${email.sender?.name || email.sender?.email} <${email.sender?.email}>\nDate: ${format(new Date(email.date * 1000), 'MMM d, yyyy, h:mm a')}\nSubject: ${email.subject}\nTo: ${email.recipients?.map(r => r.email).join(', ')}\n\n${email.body}`;

      // Update email data in compose dialog
      const composeData = new CustomEvent('compose-email', {
        detail: { subject, body }
      });
      window.dispatchEvent(composeData);

      openComposer();
      handleClose();
    }
  };

  if (!email) return null;

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
          {/* Header - Fixed height */}
          <div className="flex justify-between items-center px-6 py-3 border-b bg-white sticky top-0 z-10">
            <div className="flex-1 min-w-0 pr-4">
              <h2 className="text-lg font-semibold truncate">{email.subject}</h2>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{format(new Date(email.date * 1000), 'MMMM d, yyyy')}</span>
                <span>•</span>
                <span>{format(new Date(email.date * 1000), 'h:mm a')}</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleReply}>
                <Reply className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handleReplyAll}>
                <ReplyAll className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handleForward}>
                <Forward className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handleCancelClick}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Scrollable container */}
          <div className="overflow-y-auto">
            {isLoadingThread ? (
              <div className="flex items-center justify-center py-4">
                Loading thread...
              </div>
            ) : (
              emailThread.map((threadEmail, index) => (
                <div key={threadEmail.id} className={`border-b ${index === 0 ? '' : 'pt-6'}`}>
                  {/* Sender Info */}
                  <div className="px-6 py-4 bg-gray-50/80">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-12 w-12">
                        <span className="text-base font-medium">
                          {threadEmail.sender?.name?.[0] || threadEmail.sender?.email?.[0]?.toUpperCase()}
                        </span>
                      </Avatar>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-baseline justify-between">
                          <div>
                            <div className="font-medium text-lg">
                              {threadEmail.sender?.name || threadEmail.sender?.email}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {threadEmail.sender?.email}
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(threadEmail.date * 1000), 'MMM d, h:mm a')}
                          </div>
                        </div>

                        <div className="mt-3 space-y-1 text-sm text-muted-foreground">
                          <div className="flex gap-2">
                            <span className="w-8 text-gray-500">To:</span>
                            <span className="flex-1">
                              {threadEmail.recipients.map(r => r.name || r.email).join(", ")}
                            </span>
                          </div>

                          {threadEmail.cc && threadEmail.cc.length > 0 && (
                            <div className="flex gap-2">
                              <span className="w-8 text-gray-500">Cc:</span>
                              <span className="flex-1">
                                {threadEmail.cc.map(r => r.name || r.email).join(", ")}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Email Body */}
                  <div className="px-6 py-4">
                    <div
                      className="prose prose-sm max-w-none prose-headings:font-semibold prose-a:text-[#060216]-600"
                      dangerouslySetInnerHTML={{ __html: threadEmail.body || '' }}
                    />
                  </div>

                  {/* Attachments */}
                  {threadEmail.files && threadEmail.files.length > 0 && (
                    <div className="px-6 py-4 bg-gray-50/80 border-t">
                      <div className="flex items-center gap-2 text-sm font-medium text-gray-600 mb-3">
                        <Paperclip className="h-4 w-4" />
                        <span>{threadEmail.files.length} Attachment{threadEmail.files.length !== 1 ? 's' : ''}</span>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        {threadEmail.files.map((file, fileIndex) => (
                          <div
                            key={fileIndex}
                            className="flex items-center justify-between p-2 bg-white rounded border"
                          >
                            <div className="flex items-center gap-2 truncate">
                              <Paperclip className="h-4 w-4 text-gray-400" />
                              <span className="text-sm truncate">{file.filename}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => window.open(file.download_url || '', '_blank')}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleClose}
        onCancel={() => setShowUnsavedAlert(false)}
      />
    </>
  );
} 