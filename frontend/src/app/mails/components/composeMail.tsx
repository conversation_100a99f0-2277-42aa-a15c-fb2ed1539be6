import { useState, createContext, useContext, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Mail, Paperclip, X } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import emailService from '@/services/emailCalenderIntegrationService'
import { UnsavedChangesAlert } from "@/components/ui/unsaved-changes-alert"
import { useCases } from '@/services/caseService'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

/**
 * Context to expose email composer functionality to other components
 */
interface ComposeMailContextType {
  openComposer: (initialEmail?: string, caseId?: string, initialBcc?: string) => void;
}

const ComposeMailContext = createContext<ComposeMailContextType | undefined>(undefined);

/**
 * Hook to use compose mail functionality from any component
 */
export const useComposeMail = () => {
  const context = useContext(ComposeMailContext);
  if (!context) {
    throw new Error("useComposeMail must be used within a ComposeMailProvider");
  }
  return context;
};

/**
 * Provider component for compose mail functionality
 */
export function ComposeMailProvider({ children, hideButton = false, initialCaseId = '' }: {
  children: React.ReactNode,
  hideButton?: boolean,
  initialCaseId?: string
}) {
  const [composeState, setComposeState] = useState({
    open: false,
    initialEmail: '',
    caseId: initialCaseId || '',
    initialBcc: ''
  });

  const openComposer = (initialEmail = '', caseId = '', initialBcc = '') => {
    setComposeState({ open: true, initialEmail, caseId, initialBcc });
  };

  return (
    <ComposeMailContext.Provider value={{ openComposer }}>
      {children}
      <ComposeDialog
        open={composeState.open}
        initialEmail={composeState.initialEmail}
        initialCaseId={composeState.caseId}
        initialBcc={composeState.initialBcc}
        onOpenChange={(open) => setComposeState(prev => ({ ...prev, open }))}
        hideButton={hideButton}
      />
    </ComposeMailContext.Provider>
  );
}

interface ComposeDialogProps {
  open?: boolean;
  initialEmail?: string;
  initialCaseId?: string;
  initialBcc?: string;
  onOpenChange?: (open: boolean) => void;
  hideButton?: boolean;
}

export default function ComposeDialog({
  open: externalOpen,
  initialEmail = '',
  initialCaseId = '',
  initialBcc = '',
  onOpenChange: externalOnOpenChange,
  hideButton = false
}: ComposeDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const [showUnsavedAlert, setShowUnsavedAlert] = useState(false)
  const [emailData, setEmailData] = useState({
    to: initialEmail,
    cc: '',
    bcc: initialBcc,
    subject: '',
    body: '',
    case_id: initialCaseId
  })

  // Use either internal or external state based on what's provided
  const open = externalOpen !== undefined ? externalOpen : internalOpen;
  const setOpen = (newOpen: boolean) => {
    if (externalOnOpenChange) {
      externalOnOpenChange(newOpen);
    } else {
      setInternalOpen(newOpen);
    }
  };

  const { data: casesData } = useCases()
  const sendEmailMutation = emailService.useSendEmail()

  // Listen for compose-email event
  useEffect(() => {
    const handleComposeEmail = (event: CustomEvent) => {
      const { subject, body } = event.detail;
      setEmailData(prev => ({
        ...prev,
        subject: subject || prev.subject,
        body: body || prev.body
      }));
    };

    window.addEventListener('compose-email', handleComposeEmail as EventListener);
    return () => {
      window.removeEventListener('compose-email', handleComposeEmail as EventListener);
    };
  }, []);

  // Generate case BCC email from case ID
  const generateCaseBccEmail = (caseId: string) => {
    if (!caseId) return '';
    const selectedCase = casesData?.results.find(c => c.id === caseId);
    if (!selectedCase) return '';
    return `${selectedCase.id.replace(/^CASE-/, '').toUpperCase()}@BCC.ALPHALAW.IO`;
  };

  // Update to field when initialEmail changes and set BCC for case
  useEffect(() => {
    if (open) {
      const updates: Partial<typeof emailData> = {};

      if (initialEmail) {
        updates.to = initialEmail;
      }

      if (initialBcc) {
        updates.bcc = initialBcc;
      }

      if (initialCaseId) {
        updates.case_id = initialCaseId;
        const caseBcc = generateCaseBccEmail(initialCaseId);
        if (caseBcc) {
          // If we have both initialBcc and case BCC, combine them
          if (initialBcc) {
            updates.bcc = `${initialBcc}, ${caseBcc}`;
          } else {
            updates.bcc = caseBcc;
          }
        }
      }

      if (Object.keys(updates).length > 0) {
        setEmailData(prev => ({ ...prev, ...updates }));
      }
    }
  }, [initialEmail, initialCaseId, initialBcc, open, casesData?.results]);

  // Update BCC when case_id changes
  useEffect(() => {
    if (emailData.case_id) {
      const caseBcc = generateCaseBccEmail(emailData.case_id);
      if (caseBcc) {
        // Don't overwrite existing BCC values
        setEmailData(prev => {
          // Check if case BCC is already included
          if (prev.bcc && prev.bcc.includes(caseBcc)) {
            return prev;
          }

          // Add case BCC to existing BCC values
          const newBcc = prev.bcc ? `${prev.bcc}, ${caseBcc}` : caseBcc;
          return { ...prev, bcc: newBcc };
        });
      }
    }
  }, [emailData.case_id, casesData?.results]);

  const handleClose = () => {
    setOpen(false)
    resetForm()
  }

  const handleDiscardChanges = () => {
    setShowUnsavedAlert(false)
    handleClose()
  }

  const handleContinueEditing = () => {
    setShowUnsavedAlert(false)
  }

  const handleOpenChange = (newOpen: boolean) => {
    const isDirty = emailData.to !== '' || emailData.subject !== '' || emailData.body !== '' || files.length > 0
    if (!newOpen && isDirty) {
      setShowUnsavedAlert(true)
    } else {
      if (!newOpen) {
        handleClose()
      } else {
        setOpen(true)
      }
    }
  }

  const handleSend = async () => {
    const formData = {
      to: emailData.to.split(',').map(email => email.trim()),
      cc: emailData.cc ? emailData.cc.split(',').map(email => email.trim()) : [],
      bcc: emailData.bcc ? emailData.bcc.split(',').map(email => email.trim()) : [],
      subject: emailData.subject,
      body: emailData.body,
      attachments: files,
      ...(emailData.case_id && { case_id: emailData.case_id })
    };

    console.log("Sending email with data:", formData); // Log the formData for debugging

    try {
      const response = await sendEmailMutation.mutateAsync(formData);
      console.log("Email sent successfully:", response); // Log the response from the backend

      // Close the popup immediately after sending
      setOpen(false);
      resetForm();

      // Trigger a refresh of the "Sent" tab
      const labels = emailService.useLabels(); // Assuming useLabels fetches the labels
      const sentLabelId = labels?.data?.find(l => l.name.toLowerCase() === 'sent')?.id;
      if (sentLabelId) {
        const EMAILS_PER_PAGE = 20; // Define the constant with an appropriate value
        emailService.useLabelMessages(sentLabelId, { page: 1, limit: EMAILS_PER_PAGE });
      }
    } catch (error) {
      console.error("Failed to send email:", error); // Log the error for debugging
    }
  };

  const resetForm = () => {
    setEmailData({
      to: '',
      cc: '',
      bcc: '',
      subject: '',
      body: '',
      case_id: ''
    })
    setFiles([]);
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles) {
      setFiles(prev => [...prev, ...Array.from(selectedFiles)])
    }
    e.target.value = ''
  }

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index))
  }

  // Create a button component used for opening the compose dialog
  const ComposeButton = () => (
    <Button className="w-full">
      <Mail className="mr-2 h-4 w-4" />
      Compose
    </Button>
  );

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        {!externalOpen && !hideButton && (
          <DialogTrigger asChild>
            <ComposeButton />
          </DialogTrigger>
        )}
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>New Message</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            {/* Case Selection */}
            <Select
              value={emailData.case_id}
              onValueChange={(value) => setEmailData({ ...emailData, case_id: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a case" />
              </SelectTrigger>
              <SelectContent>
                {casesData?.results.map((caseItem) => (
                  <SelectItem key={caseItem.id} value={caseItem.id}>
                    {caseItem.case_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              placeholder="To"
              value={emailData.to}
              onChange={(e) => setEmailData({ ...emailData, to: e.target.value })}
            />
            <Input
              placeholder="Cc"
              value={emailData.cc}
              onChange={(e) => setEmailData({ ...emailData, cc: e.target.value })}
            />
            <Input
              placeholder="Bcc"
              value={emailData.bcc}
              onChange={(e) => setEmailData({ ...emailData, bcc: e.target.value })}
            />
            <Input
              placeholder="Subject"
              value={emailData.subject}
              onChange={(e) => setEmailData({ ...emailData, subject: e.target.value })}
            />
            <Textarea
              placeholder="Write your message here..."
              className="min-h-[200px]"
              value={emailData.body}
              onChange={(e) => setEmailData({ ...emailData, body: e.target.value })}
            />

            {/* File Attachments */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  multiple
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('file-upload')?.click()}
                  className="w-fit"
                >
                  <Paperclip className="mr-2 h-4 w-4" />
                  Attach Files
                </Button>
                {files.length > 0 && (
                  <span className="text-sm text-muted-foreground">
                    {files.length} file{files.length > 1 ? 's' : ''} attached
                  </span>
                )}
              </div>

              {/* Show selected files */}
              {files.length > 0 && (
                <div className="space-y-1">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-secondary p-2 rounded text-sm">
                      <div className="flex items-center gap-2 truncate">
                        <Paperclip className="h-3 w-3 text-muted-foreground" />
                        <span className="truncate">{file.name}</span>
                        <span className="text-xs text-muted-foreground">
                          ({(file.size / 1024).toFixed(1)} KB)
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => handleOpenChange(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSend}
                disabled={!emailData.to || !emailData.subject || sendEmailMutation.isPending}
              >
                {sendEmailMutation.isPending ? 'Sending...' : 'Send'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <UnsavedChangesAlert
        open={showUnsavedAlert}
        onOpenChange={setShowUnsavedAlert}
        onConfirm={handleDiscardChanges}
        onCancel={handleContinueEditing}
      />
    </>
  )
}