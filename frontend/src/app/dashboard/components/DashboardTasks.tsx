"use client";
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  NotebookPen,
  CheckCircle2,
  Clock,
  Circle,
  AlertCircle,
  Calendar,
  CalendarDays,
  User2,
  ChevronDown,
  Filter,
  Users,
  ListFilter,
  Briefcase,
  ExternalLink
} from "lucide-react";
import Link from "next/link";
import { Task, TaskStatus, TaskStatusConfig, TaskPriority } from "@/type/case-management/noteTaskTypes";
import { useCategorizedTasksQuery, useQuickStatusUpdateMutation } from "@/services/case-management/taskEnhancedService";
import { format, isToday, isPast, addDays, isWithinInterval } from "date-fns";
import { EmptyState } from "@/components/ui/empty-state";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function DashboardTasks() {
  // State for filters
  const [viewType, setViewType] = useState<'my_tasks' | 'tagged' | 'all'>('my_tasks');
  const [timeFilter, setTimeFilter] = useState<'all' | 'overdue' | 'today' | 'this_week'>('all');
  // Priority and completed filters removed for dashboard
  const priorityFilter = 'all';
  const showCompletedTasks = false;

  // Prepare filters for the query
  const filters: {
    exclude_closed_cases: boolean;
    status?: string;
    priority?: TaskPriority;
  } = {
    exclude_closed_cases: true
  };

  // Add status filter to exclude completed tasks if needed
  if (!showCompletedTasks) {
    filters.status = [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.REOPENED].join(',');
  }

  // Add priority filter if selected
  if (priorityFilter !== 'all') {
    filters.priority = priorityFilter;
  }

  // Enhanced tasks query
  const {
    data: enhancedData,
    isLoading,
    error
  } = useCategorizedTasksQuery(viewType, filters);

  // Quick status update mutation
  const quickStatusUpdateMutation = useQuickStatusUpdateMutation();

  // Get tasks based on time filter
  const getFilteredTasks = () => {
    if (!enhancedData) return [];

    if (timeFilter === 'all') {
      // Combine all categories, prioritizing overdue and today's tasks
      const allTasks = [
        ...(enhancedData.overdue || []),
        ...(enhancedData.today || []),
        ...(enhancedData.this_week || []),
        ...(enhancedData.later || []),
        ...(enhancedData.no_due_date || [])
      ];

      // Sort by priority and due date
      return allTasks.sort((a, b) => {
        // First by priority (critical first)
        const priorityOrder = {
          critical: 0,
          urgent: 1,
          high: 2,
          regular: 3,
          medium: 4,
          low: 5
        };

        const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 999;
        const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 999;

        if (aPriority !== bPriority) return aPriority - bPriority;

        // Then by due date (overdue first)
        if (a.due_date && b.due_date) {
          return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
        }

        // Tasks with due dates come before those without
        if (a.due_date && !b.due_date) return -1;
        if (!a.due_date && b.due_date) return 1;

        return 0;
      }).slice(0, 4); // Limit to 4 tasks for dashboard
    }

    // Return specific category
    return (enhancedData[timeFilter] || []).slice(0, 4);
  };

  const tasks = getFilteredTasks();

  // Calculate total count of non-completed tasks
  const totalCount = error ? 0 : (
    (enhancedData?.summary?.total || 0) -
    (showCompletedTasks ? 0 : (enhancedData?.summary?.completed || 0))
  );

  // Helper function to get status icon
  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return <Circle className="h-4 w-4 text-yellow-500" />;
      case TaskStatus.IN_PROGRESS:
        return <Clock className="h-4 w-4 text-blue-500" />;
      case TaskStatus.COMPLETED:
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case TaskStatus.REOPENED:
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  // Helper function to format date with relative indicators
  const formatTaskDate = (dateString?: string) => {
    if (!dateString) return null;

    const date = new Date(dateString);
    const formattedDate = format(date, "MMM d, yyyy");

    if (isToday(date)) {
      return <span className="text-orange-500 font-medium">Today - {formattedDate}</span>;
    } else if (isPast(date)) {
      return <span className="text-red-500 font-medium">Overdue - {formattedDate}</span>;
    } else if (isWithinInterval(date, { start: addDays(new Date(), 1), end: addDays(new Date(), 2) })) {
      return <span className="text-yellow-500 font-medium">Soon - {formattedDate}</span>;
    }

    return <span>{formattedDate}</span>;
  };

  // Handle status change
  const handleStatusChange = (task: Task, newStatus: TaskStatus) => {
    quickStatusUpdateMutation.mutate({ taskId: task.id, status: newStatus });
  };



  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <NotebookPen className="h-4 w-4" />
          <h2 className="text-sm font-medium text-[#060216]">Tasks {totalCount > 0 ? `(${totalCount})` : ''}</h2>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="text-xs h-8">
                <Filter className="h-3 w-3 mr-1" />
                Filters
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {/* View Type Filter */}
              <DropdownMenuLabel>View Type</DropdownMenuLabel>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuCheckboxItem
                      checked={viewType === 'my_tasks'}
                      onCheckedChange={() => setViewType('my_tasks')}
                    >
                      <User2 className="h-4 w-4 mr-2" />
                      My Tasks
                    </DropdownMenuCheckboxItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Tasks where you are assigned or tagged</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuCheckboxItem
                      checked={viewType === 'tagged'}
                      onCheckedChange={() => setViewType('tagged')}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Tagged Tasks
                    </DropdownMenuCheckboxItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Tasks where you are tagged but not assigned</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuCheckboxItem
                      checked={viewType === 'all'}
                      onCheckedChange={() => setViewType('all')}
                    >
                      <ListFilter className="h-4 w-4 mr-2" />
                      All Tasks
                    </DropdownMenuCheckboxItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>All tasks you are involved with (assigned, tagged, or created)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Priority and Completed filters removed for dashboard */}
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="link" asChild className="text-green-600 p-0 h-auto">
            <Link href="/tasks">View All</Link>
          </Button>
        </div>
      </div>

      {/* Removed informational alert about the current view */}

      {/* Time Filter Tabs */}
      <Tabs
        value={timeFilter}
        onValueChange={(value: string) => setTimeFilter(value as 'all' | 'overdue' | 'today' | 'this_week')}
        className="w-full"
      >
        <TabsList className="grid grid-cols-4 w-full bg-slate-100 p-1 rounded-lg">
          <TabsTrigger value="all" className="text-sm font-medium rounded-md">
            All
          </TabsTrigger>
          <TabsTrigger value="overdue" className="text-sm font-medium rounded-md">
            <span className="flex items-center gap-1">
              <AlertCircle className="h-3 w-3 text-red-500" />
              <span>Overdue</span>
            </span>
          </TabsTrigger>
          <TabsTrigger value="today" className="text-sm font-medium rounded-md">
            <span className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-orange-500" />
              <span>Today</span>
            </span>
          </TabsTrigger>
          <TabsTrigger value="this_week" className="text-sm font-medium rounded-md">
            <span className="flex items-center gap-1">
              <CalendarDays className="h-3 w-3 text-blue-500" />
              <span>This Week</span>
            </span>
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-3">
          {Array(4).fill(0).map((_, i) => (
            <Card key={i} className="p-4 bg-white shadow-sm rounded-lg h-auto min-h-[160px] flex-shrink-0">
              <div className="flex flex-col h-full justify-between">
                <div className="space-y-4">
                  <div className="flex justify-between items-start">
                    <div className="h-5 bg-gray-200 rounded animate-pulse w-2/3" />
                    <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
                  </div>
                  <div className="h-6 bg-gray-200 rounded animate-pulse w-3/4" />
                </div>
                <div className="mt-auto pt-4 space-y-3">
                  <div className="flex gap-2">
                    <div className="h-6 bg-gray-200 rounded animate-pulse w-24" />
                    <div className="h-6 bg-gray-200 rounded animate-pulse w-24" />
                  </div>
                  <div className="flex justify-end">
                    <div className="h-6 bg-gray-200 rounded animate-pulse w-28" />
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : error ? (
        <EmptyState
          icon={NotebookPen}
          title="Error loading tasks"
          description="There was an error loading tasks. Please try again later."
        />
      ) : !tasks?.length ? (
        <EmptyState
          icon={NotebookPen}
          title="No tasks"
          description="There are no active tasks matching your filters."
        />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-4">
          {tasks.slice(0, 4).map((task: Task) => (
            <Card
              key={task.id}
              className={`transition-all duration-200 shadow-sm hover:shadow-md rounded-lg h-auto min-h-[130px] relative ${task.status === TaskStatus.COMPLETED
                ? "bg-muted/30 border-muted"
                : "bg-white hover:bg-slate-50 border-border"
                } relative overflow-hidden group`}
            // Removed cursor-pointer and onClick navigation
            >
              {/* Status indicator bar */}
              <div className={`absolute left-0 top-0 bottom-0 w-1 ${task.status === TaskStatus.PENDING ? 'bg-yellow-500' :
                task.status === TaskStatus.IN_PROGRESS ? 'bg-blue-500' :
                  task.status === TaskStatus.COMPLETED ? 'bg-green-500' :
                    'bg-red-500'
                }`} />

              <div className="flex flex-col h-full p-4 pl-6">
                <div className="flex flex-col space-y-2">
                  {/* Case Badge - Moved to top */}
                  {(task.case || task.case_name) && (
                    <div className="flex items-center gap-1 mb-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="flex items-center gap-1 bg-blue-50 hover:bg-blue-100 transition-colors border-blue-200 text-blue-700 px-2 py-1 rounded-md">
                              <Briefcase className="h-3 w-3 text-blue-500" />
                              <Link
                                href={`/dashboard/case-view/${task.case}`}
                                className="text-xs text-blue-600 hover:underline flex items-center truncate max-w-[180px]"
                              >
                                {task.case_details?.name || task.case_details?.case_name || task.case_name || `Case ${task.case}`}
                                <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                              </Link>
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Click to view case details</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}

                  {/* Title Row - Priority badge removed */}
                  <div className="flex items-start justify-between">
                    <h3 className="text-sm font-medium pr-2">{task.title}</h3>
                  </div>
                </div>

                <div className="mt-auto pt-4 flex flex-col gap-3">
                  {/* Due Date, Status and Assigned To */}
                  <div className="flex flex-wrap gap-2 justify-between w-full">
                    <div className="flex flex-wrap gap-2">
                      {/* Due Date Badge */}
                      {task.due_date && (
                        <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 h-6 bg-slate-50 border-slate-200 text-xs">
                          <Calendar className="h-3 w-3 text-slate-500" />
                          {formatTaskDate(task.due_date)}
                        </Badge>
                      )}

                      {/* Status Badge with Dropdown - Combined */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Badge 
                            variant="outline" 
                            className="flex items-center gap-1 px-2 py-1 h-6 bg-slate-50 border-slate-200 text-xs cursor-pointer hover:bg-slate-100"
                          >
                            {getStatusIcon(task.status)}
                            <span className="text-xs">{TaskStatusConfig[task.status]?.label || task.status}</span>
                            <ChevronDown className="h-3 w-3 ml-1" />
                          </Badge>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          {Object.entries(TaskStatusConfig).map(([status, config]) => (
                            <DropdownMenuItem
                              key={status}
                              onClick={() => handleStatusChange(task, status as TaskStatus)}
                              className="flex items-center gap-2"
                            >
                              {getStatusIcon(status as TaskStatus)}
                              <span>{config.label}</span>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>

                      {/* Assigned To Badge */}
                      {task.assigned_to && (
                        <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 h-6 bg-slate-50 border-slate-200 text-xs">
                          <User2 className="h-3 w-3 text-slate-500" />
                          <span className="truncate max-w-[120px]">{task.assigned_to.name || task.assigned_to.email}</span>
                        </Badge>
                      )}
                    </div>


                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
