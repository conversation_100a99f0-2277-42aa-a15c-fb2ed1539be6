"use client";

import React from 'react'
import KpiSection from '@/app/leads/components/leads/kpis'
import { Filters } from "@/app/leads/components/leads/Filters"
import LeadsTable from '@/app/leads/components/leads/LeadsTable';
import CreateLeadDialog from './components/leads/createDialog';
import { Button } from '@/components/ui/button';
import { UserPlus } from 'lucide-react';
import { Network } from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';


export default function LeadsPage() {
    const { pagination } = useSelector((state: RootState) => state.leads);

    return (
        <div className="p-8 flex flex-col gap-8 bg-[#f5f5f5]/0">
            <div className="flex justify-between items-center h-9">
                <h1 className="text-[22px] font-bold font-Manrope text-[#060216]">
                    Lead Statistics
                </h1>

                <div className="flex gap-3">
                    {false && <>  {/* TODO: Add automation and referrals */}
                        <Button
                            variant="outline"
                            className="bg-[#1C7B35]/[0.08] text-[#1C7B35] border-[#1C7B35] hover:bg-[#1C7B35]/[0.12] font-bold rounded-full"
                        >
                            <Network className="w-5 h-5 mr-2" />
                            Automation
                        </Button>

                        <Button
                            variant="outline"
                            className="bg-[#1C7B35]/[0.08] text-[#1C7B35] border-[#1C7B35] hover:bg-[#1C7B35]/[0.12] font-bold rounded-full"
                        >
                            <UserPlus className="w-5 h-5 mr-2" />
                            Referrals
                        </Button>
                    </>
                    }

                    <CreateLeadDialog />
                </div>
            </div>
            <KpiSection />
            <div className="flex flex-col gap-4">
                <h3 className="text-base text-[#060216]/50 font-bold">MY LEADS</h3>
                <Filters />
            </div>
            <div className="flex flex-col gap-4">
                <h3 className="text-sm text-[#060216]/50 font-bold">
                    Showing {pagination.count} leads
                </h3>
                <LeadsTable />
            </div>
        </div>
    )
}
