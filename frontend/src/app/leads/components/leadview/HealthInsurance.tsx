'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { HealthInsuranceForm } from "./HealthInsuranceForm";
import {
  useLeadHealthInsuranceListQuery,
  useCreateLeadHealthInsuranceMutation,
  useUpdateLeadHealthInsuranceMutation,
  useDeleteLeadHealthInsuranceMutation
} from "@/services/leads/leadService";
import type { HealthInsuranceFormValues } from "@/app/case/health-insurance/types";
import { ExtendedHealthInsuranceResponse } from '@/type/lead/leadTypes';
import RichTextViewer from '@/components/ui/RichTextViewer';
import { CopyNumber } from '@/components/ui/copy-number';
import { ManageEmailTemplate } from '@/components/ManageEmailTemplate';
import { TemplateContextType } from '@/store/slices/templatesSlice';
import { z } from "zod";

interface HealthInsuranceProps {
  leadId: string;
  isReadOnly: boolean;
}

export default function HealthInsurance({ leadId, isReadOnly }: HealthInsuranceProps) {
  const { data: healthInsuranceList, isLoading, error } = useLeadHealthInsuranceListQuery(leadId);
  const [entries, setEntries] = useState<ExtendedHealthInsuranceResponse[]>([]);

  const createMutation = useCreateLeadHealthInsuranceMutation(leadId);
  const updateMutation = useUpdateLeadHealthInsuranceMutation(leadId);
  const deleteMutation = useDeleteLeadHealthInsuranceMutation(leadId);

  useEffect(() => {
    if (healthInsuranceList && Array.isArray(healthInsuranceList)) {
      setEntries(healthInsuranceList.map(insurance => ({
        ...insurance,
        isEditing: false
      })));
    }
  }, [healthInsuranceList, leadId]);

  const handleAdd = () => {
    setEntries(prev => [{
      lead: leadId,
      no_insurance: false,
      isEditing: true
    }, ...prev]);
  };

  const handleEdit = (id: number) => {
    setEntries(entries.map(entry =>
      entry.id === id ? { ...entry, isEditing: true } : entry
    ));
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteMutation.mutateAsync(id);
      setEntries(entries => entries.filter(entry => entry.id !== id));
    } catch (error) {
      console.error('Error deleting health insurance:', error);
    }
  };

  const handleSubmit = async (values: HealthInsuranceFormValues, entryId?: number) => {
    try {
      const formattedValues = {
        lead: leadId,
        no_insurance: values.noInsurance,
        insurance_company: values.insuranceCompany ? parseInt(values.insuranceCompany) : undefined,
        subrogation_company: values.subrogationCompany ? parseInt(values.subrogationCompany) : undefined,
        representative: values.representative ? parseInt(values.representative) : undefined,
        insured: values.insured,
        group_number: values.groupNumber,
        member_number: values.memberNumber,
        policy_number: values.policyNumber,
        total_lien: values.totalLien,
        adjusted_lien: values.adjustedLien,
        file_number: values.fileNumber,
        plan_type: values.planType,
        erisa: values.erisa,
        medicare: values.medicare,
        note: values.note || "",
        final_lien: values.final_lien,
        final_amount: values.final_amount,
        final_lien_date: values.final_lien_date,
      };

      if (entryId) {
        await updateMutation.mutateAsync({
          ...formattedValues,
          id: entryId,
        });
      } else {
        await createMutation.mutateAsync(formattedValues);
      }

      // Reset editing state or remove temporary entry
      setEntries(entries => entries.map(entry =>
        entry.id === entryId ? { ...entry, isEditing: false } : entry
      ).filter(entry => entry.id || entry.isEditing === false));

    } catch (error) {
      console.error('Error saving health insurance:', error);
    }
  };

  const handleCancel = (entryId?: number) => {
    if (entryId) {
      setEntries(entries => entries.map(entry =>
        entry.id === entryId ? { ...entry, isEditing: false } : entry
      ));
    } else {
      setEntries(entries => entries.filter(entry => entry.id));
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center text-gray-500">Loading health insurance information...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center text-red-500">Error loading health insurance information. Please try again later.</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-[#060216]">Health Insurance Information</h2>
          <p className="text-sm text-gray-500 mt-1">
            {entries.length} {entries.length === 1 ? 'record' : 'records'} found
          </p>
        </div>
        {!isReadOnly && (
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={handleAdd}
          >
            <Plus className="h-4 w-4" />
            Add Health Insurance
          </Button>
        )}
      </div>

      <div className="space-y-8">
        {entries.map((entry) => (
          <div key={entry.id || 'new'} className="bg-white p-6 rounded-lg border border-gray-100">
            {entry.isEditing ? (
              <HealthInsuranceForm
                defaultValues={{
                  noInsurance: entry.no_insurance,
                  insuranceCompany: entry.insurance_company?.id?.toString(),
                  subrogationCompany: entry.subrogation_company?.id?.toString(),
                  representative: entry.representative?.id?.toString(),
                  insured: entry.insured || undefined,
                  groupNumber: entry.group_number || undefined,
                  memberNumber: entry.member_number || undefined,
                  policyNumber: entry.policy_number || undefined,
                  totalLien: entry.total_lien || undefined,
                  adjustedLien: entry.adjusted_lien || undefined,
                  fileNumber: entry.file_number || undefined,
                  planType: entry.plan_type || undefined,
                  erisa: entry.erisa || undefined,
                  medicare: entry.medicare || undefined,
                  note: entry.note || undefined,
                  final_lien: entry.final_lien ?? false,
                  final_amount: entry.final_amount || undefined,
                  final_lien_date: entry.final_lien_date || undefined,
                }}
                isEdit={!!entry.id}
                onSubmit={(values) => handleSubmit(values, entry.id)}
                isSubmitting={createMutation.isPending || updateMutation.isPending}
                onCancel={() => handleCancel(entry.id)}
                isHeightNeeded={false}
              />
            ) : (
              <>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">
                    {entry.insurance_company ? 'Insurance Details' : 'No Insurance Record'}
                  </h3>
                  {!isReadOnly && (
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(entry.id!)}
                        className="h-8 w-8"
                      >
                        <Edit className="h-4 w-4 text-green-600 cursor-pointer" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => entry.id && handleDelete(entry.id)}
                        className="h-8 w-8"
                      >
                        <Trash className="h-4 w-4 text-red-600 cursor-pointer" />
                      </Button>
                      <ManageEmailTemplate 
                        leadId={leadId}
                        healthInsuranceId={entry.id?.toString()}
                        contextType={"client_health_insurance" as TemplateContextType}
                        className="h-8 w-8"
                      />
                    </div>
                  )}
                </div>

                {entry.no_insurance ? (
                  <p className="text-sm text-gray-500">No Insurance</p>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <p className="text-gray-500">Insurance Company</p>
                        <p>{entry.insurance_company?.name || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Insured</p>
                        <p>{entry.insured || '—'}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mt-6">
                      <div>
                        <p className="text-gray-500">Group Number</p>
                        <p>{entry.group_number || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Member Number</p>
                        <p>{entry.member_number || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Policy Number</p>
                        <p>{entry.policy_number ? <CopyNumber value={entry.policy_number} /> : '—'}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mt-6">
                      <div>
                        <p className="text-gray-500">Total Lien</p>
                        <p>{entry.total_lien || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Adjusted Lien</p>
                        <p>{entry.adjusted_lien || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">File Number</p>
                        <p>{entry.file_number ? <CopyNumber value={entry.file_number} /> : '—'}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mt-6">
                      <div>
                        <p className="text-gray-500">Plan Type</p>
                        <p>{entry.plan_type || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">ERISA</p>
                        <p>{entry.erisa || '—'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Medicare</p>
                        <p>{entry.medicare || '—'}</p>
                      </div>
                    </div>

                    <div className="mt-6 bg-gray-50/50 p-6 rounded-lg">
                      <h4 className="text-base font-semibold mb-4">Subrogation Information</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-gray-500">Subrogation Company</p>
                          <p>{entry.subrogation_company?.name || '—'}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Representative</p>
                          <p>{entry.representative?.first_name || '—'} {entry.representative?.last_name || ''}</p>
                        </div>
                      </div>
                    </div>

                    {entry.note && (
                      <div className="mt-6">
                        <p className="text-gray-500">Note</p>
                        <p className="mt-1">{entry.note ? <RichTextViewer data={entry.note} /> : '—'}</p>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export const formSchema = z
    .object({
        leadId: z.string().optional(),
        noInsurance: z.boolean(),
        insuranceCompany: z.string().optional(),
        insured: z.string().optional(),
        groupNumber: z.string().optional(),
        memberNumber: z.string().optional(),
        policyNumber: z.string().optional(),
        subrogationCompany: z.string().optional(),
        representative: z.string().optional(),
        totalLien: z.string().optional(),
        adjustedLien: z.string().optional(),
        fileNumber: z.string().optional(),
        planType: z.string().optional(),
        erisa: z.string().optional(),
        medicare: z.string().optional(),
        note: z.string().optional(),
        final_lien: z.boolean(),
    }) 