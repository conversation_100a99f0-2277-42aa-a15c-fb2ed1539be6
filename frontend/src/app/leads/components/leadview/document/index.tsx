"use client"

import React from 'react'
import Documentation from './documentation'
import { Separator } from '@/components/ui/separator'
import { UploadedDocuments } from './UploadedDocuments'
import { LeadDocument } from '@/type/leadDocumentTypes'

const DocumentList = ({ leadId, documents, isLoading, isReadOnly }: { leadId: string; documents: LeadDocument[]; isLoading: boolean, isReadOnly: boolean }) => {
  console.log(isLoading);

  return (
    <div className="flex flex-col gap-8">
      <Documentation leadId={leadId} isReadOnly={isReadOnly} />
      <Separator className="w-full" />
      <UploadedDocuments leadId={leadId} documents={documents} isReadOnly={isReadOnly} />
    </div>
  )
}

export default DocumentList 