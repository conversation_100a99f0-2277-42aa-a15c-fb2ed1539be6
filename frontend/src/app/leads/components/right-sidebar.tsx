"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Suspense } from "react"
import { LeadTasks } from "@/components/LeadTasks/LeadTasks"
import { LeadNotes } from "@/components/LeadNotes/LeadNotes"
import { LeadChat } from "@/components/LeadChat/LeadChat"
import ActivityFeed from "@/app/leads/components/leadview/ActivityFeed"
import { Sidebar, SidebarContent } from "@/components/ui/sidebar"

interface RightSidebarProps {
    leadId: string;
}

export function RightSidebar({ leadId }: RightSidebarProps) {
    return (
        <Sidebar className="h-full border-none">
        <SidebarContent className="p-0">
            <Tabs defaultValue="chat" className="h-full flex flex-col">
                <div className="flex-none border-b bg-white">
                    <TabsList className="w-full justify-start px-2 pt-2">
                        <TabsTrigger value="chat">Chat</TabsTrigger>
                        <TabsTrigger value="notes">Notes</TabsTrigger>
                        <TabsTrigger value="tasks">Tasks</TabsTrigger>
                        <TabsTrigger value="activity">Activity</TabsTrigger>
                    </TabsList>
                </div>
                        <TabsContent value="chat" className="flex-1 p-0 m-0 overflow-hidden">
                            <Suspense fallback={<div className="p-4">Loading chat...</div>}>
                                <LeadChat leadId={leadId} />
                            </Suspense>
                        </TabsContent>
                        <TabsContent value="notes" className="flex-1 p-0 m-0 overflow-hidden">
                            <Suspense fallback={<div className="p-4">Loading notes...</div>}>
                                <LeadNotes leadId={leadId} />
                            </Suspense>
                        </TabsContent>
                        <TabsContent value="tasks" className="flex-1 p-0 m-0 overflow-hidden">
                            <Suspense fallback={<div className="p-4">Loading tasks...</div>}>
                                <LeadTasks leadId={leadId} />
                            </Suspense>
                        </TabsContent>
                        <TabsContent value="activity" className="flex-1 p-0 m-0 overflow-hidden">
                            <Suspense fallback={<div className="p-4">Loading activity...</div>}>
                                <ActivityFeed leadId={leadId} activities={undefined} isLoading={false} />
                            </Suspense>
                        </TabsContent>
                       </Tabs>
            </SidebarContent>
        </Sidebar>
    )
} 