import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useCreateLinkedLeadMutation } from "@/services/leads/linkLeadService";

interface CreateLinkedLeadDialogProps {
  leadId: string;
  children?: React.ReactNode;
  onSuccess?: () => void;
}

interface LinkedLeadFormData {
  first_name: string;
  last_name: string;
}

const initialFormData: LinkedLeadFormData = {
  first_name: "",
  last_name: "",
};

const CreateLinkedLeadDialog: React.FC<CreateLinkedLeadDialogProps> = ({ leadId, children, onSuccess }) => {
  const [formData, setFormData] = React.useState<LinkedLeadFormData>(initialFormData);
  const [errors, setErrors] = React.useState<Record<string, string>>({});
  const [open, setOpen] = React.useState(false);

  const createLinkedLeadMutation = useCreateLinkedLeadMutation(leadId);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.first_name) newErrors.first_name = "First name is required";
    if (!formData.last_name) newErrors.last_name = "Last name is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    },
    []
  );

  const handleSubmit = React.useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) return;

      try {
        await createLinkedLeadMutation.mutateAsync(formData);
        setFormData(initialFormData);
        setErrors({});
        setOpen(false);
        onSuccess?.();
      } catch (error) {
        console.error('Error creating linked lead:', error);
      }
    },
    [formData, createLinkedLeadMutation, onSuccess]
  );

  const handleClose = () => {
    setOpen(false);
    setFormData(initialFormData);
    setErrors({});
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create Linked Lead</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>First Name *</Label>
              <Input
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                placeholder="Enter First Name"
                className={errors.first_name ? "border-red-500" : ""}
              />
              {errors.first_name && (
                <p className="text-sm text-red-500">{errors.first_name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Last Name *</Label>
              <Input
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                placeholder="Enter Last Name"
                className={errors.last_name ? "border-red-500" : ""}
              />
              {errors.last_name && (
                <p className="text-sm text-red-500">{errors.last_name}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <DialogClose asChild>
              <Button
                variant="outline"
                type="button"
                onClick={handleClose}
              >
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={createLinkedLeadMutation.isPending}>
              {createLinkedLeadMutation.isPending ? "Creating..." : "Create Linked Lead"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateLinkedLeadDialog; 