'use client';

import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation';
import LeadStatus from '@/app/leads/components/leadview/LeadStatus';
import { Card } from '@/components/ui/card';
import CreateLeadStep from '@/app/leads/components/leadCreate/CreateLeadStep';
import CreateLeadInfoTabs from '@/app/leads/components/leadCreate/CreateLeadInfoTabs';
import CreateLeadHeader from '../components/leadCreate/CreateLeadHeader';

export default function CreateLead() {
    const router = useRouter();

    return (
        <div className="p-8 flex flex-col gap-8 bg-[#f5f5f5]/0">
            <div className="p-8 flex flex-col gap-8 bg-[#f5f5f5]/0">
                {/* go back button   */}
                <Button
                    variant="ghost"
                    className="hover:bg-transparent p-0 justify-start"
                    onClick={() => router.back()}
                >
                    <ArrowLeft className="w-5 h-5 mr-2" />
                    Back
                </Button>
                <Card className="flex flex-col gap-2 p-2">
                    {/* head */}
                    <CreateLeadHeader />
                    {/* lead statics */}
                    <LeadStatus caseId={null} />
                    {/* lead step */}
                    <CreateLeadStep />
                </Card>
                <Card className="flex flex-col gap-2 p-4">
                    <CreateLeadInfoTabs />
                </Card>
            </div>
        </div>
    );
}
