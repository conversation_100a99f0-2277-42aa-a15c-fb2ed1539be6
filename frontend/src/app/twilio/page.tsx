"use client"

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import twilioService from '@/services/twilioIntegrationService';
import { Phone, MessageSquare } from 'lucide-react';

export default function TwilioPage() {
  const { toast } = useToast();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [message, setMessage] = useState('');
  const [callType, setCallType] = useState('message');

  // Hooks from the service
  const callMutation = twilioService.useMakeCall();
  const smsMutation = twilioService.useSendSMS();

  const handleCall = async () => {
    if (!phoneNumber) {
      toast({
        title: "Error",
        description: "Please provide a phone number",
        variant: "destructive",
      });
      return;
    }

    if (callType === 'message' && !message) {
      toast({
        title: "Error",
        description: "Please provide a message to be read",
        variant: "destructive",
      });
      return;
    }

    try {
      const twimlUrl = callType === 'menu' 
        ? 'https://handler.twilio.com/twiml/menu'  // This should be your actual menu TwiML URL
        : callType === 'hold' 
          ? 'http://twimlets.com/holdmusic?Bucket=com.twilio.music.ambient'
          : undefined;

      await callMutation.mutateAsync({ 
        to: phoneNumber, 
        message: message,
        twiml_url: twimlUrl
      });
      setMessage('');
      setPhoneNumber('');
    } catch (_error) {
      console.error('Error making call:', _error);
    }
  };

  const handleSMS = async () => {
    if (!phoneNumber || !message) {
      toast({
        title: "Error",
        description: "Please provide both phone number and message",
        variant: "destructive",
      });
      return;
    }

    try {
      await smsMutation.mutateAsync({ to_number: phoneNumber, message });
      setMessage('');
      setPhoneNumber('');
    } catch (_error) {
      console.error('Error sending SMS:', _error);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Communication Center</h1>
      
      <Tabs defaultValue="call" className="max-w-2xl">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="call">
            <Phone className="w-4 h-4 mr-2" />
            Make Call
          </TabsTrigger>
          <TabsTrigger value="sms">
            <MessageSquare className="w-4 h-4 mr-2" />
            Send SMS
          </TabsTrigger>
        </TabsList>

        <TabsContent value="call">
          <Card>
            <CardHeader>
              <CardTitle>Make a Phone Call</CardTitle>
              <CardDescription>
                Enter the recipient&apos;s phone number and select a call type
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="Phone Number (e.g., +1234567890)"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>

              <div className="space-y-4">
                <Label>Call Type</Label>
                <RadioGroup 
                  value={callType} 
                  onValueChange={setCallType}
                  className="grid grid-cols-1 gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="message" id="message" />
                    <Label htmlFor="message" className="font-normal">
                      Read Message
                      <p className="text-sm text-muted-foreground">
                        The system will read out your message to the recipient
                      </p>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="menu" id="menu" />
                    <Label htmlFor="menu" className="font-normal">
                      Interactive Menu
                      <p className="text-sm text-muted-foreground">
                        Present options: 1 for appointments, 2 for case status, 3 for attorney
                      </p>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="hold" id="hold" />
                    <Label htmlFor="hold" className="font-normal">
                      Hold Music
                      <p className="text-sm text-muted-foreground">
                        Play ambient hold music during the call
                      </p>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {callType === 'message' && (
                <div className="space-y-2">
                  <Textarea
                    placeholder="Message to be read..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
              )}

              <Button 
                className="w-full" 
                onClick={handleCall}
                disabled={callMutation.isPending}
              >
                {callMutation.isPending ? "Initiating Call..." : "Make Call"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sms">
          <Card>
            <CardHeader>
              <CardTitle>Send SMS</CardTitle>
              <CardDescription>
                Enter the recipient&apos;s phone number and your message
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="Phone Number (e.g., +1234567890)"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Textarea
                  placeholder="Type your message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
              <Button 
                className="w-full" 
                onClick={handleSMS}
                disabled={smsMutation.isPending}
              >
                {smsMutation.isPending ? "Sending..." : "Send SMS"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 