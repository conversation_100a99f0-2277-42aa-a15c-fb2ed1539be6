"use client";

import React, { useState } from 'react';
import { useNotifications } from '@/hooks/use-notifications';
import { NotificationFilters } from '@/services/notificationService';
import { Button } from '@/components/ui/button';
import { Loader2, Bell, Filter, X } from 'lucide-react';
import { NotificationList } from './components/NotificationList';
import { NotificationFilterBar } from './components/NotificationFilterBar';

export default function NotificationsPage() {
  const [filters, setFilters] = useState<NotificationFilters>({
    is_archived: false,
  });

  const {
    notifications,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useNotifications(filters);

  const handleFilterChange = (newFilters: NotificationFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleClearFilters = () => {
    setFilters({ is_archived: false });
  };

  // Check if there are active filters beyond the default is_archived
  const hasActiveFilters = () => {
    const { is_archived, ...otherFilters } = filters;
    return Object.keys(otherFilters).length > 0;
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex items-center">
          <Bell className="mr-2 h-5 w-5" />
          <h1 className="text-2xl font-bold">Notifications</h1>
        </div>

        <div className="flex items-center gap-2">
          {hasActiveFilters() && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              className="flex items-center gap-1"
            >
              <X className="h-4 w-4" />
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      <NotificationFilterBar
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      {isLoading && notifications.length === 0 ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md my-6">
          Error loading notifications: {error}
        </div>
      ) : (
        <NotificationList
          notifications={notifications}
          fetchNextPage={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
        />
      )}
    </div>
  );
}
