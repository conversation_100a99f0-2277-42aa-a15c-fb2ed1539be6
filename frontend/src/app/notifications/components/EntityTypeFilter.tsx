import React from 'react';
import { FilterButton } from '@/components/ui/filter-button';
import { 
  FileText, 
  CheckSquare, 
  MessageSquare, 
  File, 
  User, 
  Mail,
  Bell
} from 'lucide-react';

interface EntityTypeFilterProps {
  selectedEntity: string | null;
  onEntityChange: (entity: string | null) => void;
}

// Entity types with their corresponding notification types
export const ENTITY_TYPES = [
  {
    id: 'all',
    label: 'All',
    icon: <Bell className="h-4 w-4" />,
    color: '#6366f1',
    notificationTypes: []
  },
  {
    id: 'case',
    label: 'Cases',
    icon: <FileText className="h-4 w-4" />,
    color: '#4f46e5',
    notificationTypes: ['case_update', 'case_status_changed']
  },
  {
    id: 'task',
    label: 'Tasks',
    icon: <CheckSquare className="h-4 w-4" />,
    color: '#10b981',
    notificationTypes: ['task_assigned', 'task_updated', 'task_created', 'task_status_changed', 'task_tagged']
  },
  {
    id: 'lead',
    label: 'Leads',
    icon: <User className="h-4 w-4" />,
    color: '#f59e0b',
    notificationTypes: ['lead_status_changed', 'lead_assigned', 'lead_update', 'lead_task_created', 'lead_task_status_changed', 'lead_task_tagged']
  },
  {
    id: 'note',
    label: 'Notes',
    icon: <MessageSquare className="h-4 w-4" />,
    color: '#8b5cf6',
    notificationTypes: ['note_created', 'note_tagged', 'lead_note_created']
  },
  {
    id: 'document',
    label: 'Documents',
    icon: <File className="h-4 w-4" />,
    color: '#ef4444',
    notificationTypes: ['document_uploaded']
  },
  {
    id: 'message',
    label: 'Messages',
    icon: <Mail className="h-4 w-4" />,
    color: '#0ea5e9',
    notificationTypes: ['incoming_message']
  }
];

export const EntityTypeFilter: React.FC<EntityTypeFilterProps> = ({ 
  selectedEntity, 
  onEntityChange 
}) => {
  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2">Filter by Entity</h3>
      <div className="flex flex-wrap gap-2">
        {ENTITY_TYPES.map((entity) => (
          <FilterButton
            key={entity.id}
            isActive={selectedEntity === entity.id}
            dotColor={entity.color}
            onClick={() => onEntityChange(entity.id === 'all' ? null : entity.id)}
            className="flex items-center gap-2"
          >
            {entity.icon}
            {entity.label}
          </FilterButton>
        ))}
      </div>
    </div>
  );
};
