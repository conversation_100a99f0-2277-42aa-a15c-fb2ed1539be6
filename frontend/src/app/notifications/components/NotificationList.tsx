import React from 'react';
import {
  Notification,
  useMarkNotificationReadMutation,
  useMarkNotificationUnreadMutation,
  useArchiveNotificationMutation,
  useNotificationGroupsQuery
} from '@/services/notificationService';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, ExternalLink, Bell, CheckSquare, FileText, MessageSquare, Mail, Tag, Pencil, CheckCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';
import { ENTITY_TYPES } from './EntityTypeFilter';

interface NotificationListProps {
  notifications: Notification[];
  fetchNextPage: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage: boolean;
}

export const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage
}) => {
  if (notifications.length === 0) {
    return (
      <div className="text-center p-12 bg-gray-50 rounded-lg border border-gray-200">
        <Bell className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
        <p className="text-gray-500">
          You don&apos;t have any notifications matching your filters.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {notifications.map(notification => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}

      {hasNextPage && (
        <div className="flex justify-center pt-4">
          <Button
            variant="outline"
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
          >
            {isFetchingNextPage ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading more...
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

interface NotificationItemProps {
  notification: Notification;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification }) => {
  const router = useRouter();
  const [expanded, setExpanded] = React.useState(false); // State for toggling expanded view
  const markAsRead = useMarkNotificationReadMutation();
  const markAsUnread = useMarkNotificationUnreadMutation();
  const archiveNotification = useArchiveNotificationMutation();
  const [isRead, setIsRead] = React.useState(notification.is_read); // Local state for is_read

  const handleMarkAsReadOrUnread = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isRead) {
      markAsUnread.mutate(notification.id, {
        onSuccess: () => setIsRead(false), // Update local state on success
      });
    } else {
      markAsRead.mutate(notification.id, {
        onSuccess: () => setIsRead(true), // Update local state on success
      });
    }
  };

  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation();
    archiveNotification.mutate({
      id: notification.id,
      isArchived: notification.is_archived,
    });
  };

  const handleNavigate = () => {
    // Mark as read when navigating
    if (!isRead) {
      markAsRead.mutate(notification.id, {
        onSuccess: () => setIsRead(true),
      });
    }

    // Navigate to case if case_id exists
    if (notification.case_id) {
      router.push(`/dashboard/case-view/${notification.case_id}`);
    }
  };

  // Get notification group
  const { data: notificationGroups } = useNotificationGroupsQuery();

  const getNotificationGroup = () => {
    if (!notificationGroups) return null;

    const notificationType = notification.notification_type.toLowerCase();

    // Find the group that contains this notification type
    const group = notificationGroups.find(g =>
      g.types.includes(notificationType || '')
    );

    return group ? group : null;
  };

  // Get entity badge based on notification type or content object
  const getEntityBadge = (notification: Notification) => {
    // First try to get the notification group
    const group = getNotificationGroup();
    if (group) {
      // Get color based on group
      const getGroupColor = () => {
        switch (group.id) {
          case 'case': return '#4f46e5'; // indigo
          case 'task': return '#10b981'; // green
          case 'note': return '#8b5cf6'; // purple
          case 'lead': return '#f59e0b'; // amber
          case 'document': return '#ef4444'; // red
          case 'message': return '#0ea5e9'; // blue
          case 'system': return '#6b7280'; // gray
          case 'deadline': return '#dc2626'; // red
          default: return '#6b7280'; // gray
        }
      };

      return (
        <Badge
          variant="outline"
          className="text-xs flex items-center gap-1"
          style={{ borderColor: getGroupColor(), color: getGroupColor() }}
        >
          {getIcon()}
          <span>{group.label}</span>
        </Badge>
      );
    }

    // If no group found, check if we have a content_object with a type
    if (notification.content_object?.type) {
      const entityType = notification.content_object.type;

      // Find matching entity in ENTITY_TYPES
      const entity = ENTITY_TYPES.find(e => e.id === entityType);
      if (entity) {
        return (
          <Badge
            variant="outline"
            className="text-xs flex items-center gap-1"
            style={{ borderColor: entity.color, color: entity.color }}
          >
            {React.cloneElement(entity.icon as React.ReactElement, { className: 'h-3.5 w-3.5' })}
            <span>{entity.label}</span>
          </Badge>
        );
      }
    }

    // Default badge if no match found
    return (
      <Badge variant="outline" className="text-xs flex items-center gap-1">
        <Bell className="h-3.5 w-3.5" />
        <span>System</span>
      </Badge>
    );
  };

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.notification_type.toLowerCase()) {
      case "incoming_message":
        return <Mail className="h-4 w-4 text-blue-500" />;
      case "case_update":
      case "case_status_changed":
        return <FileText className="h-4 w-4 text-indigo-500" />;
      case "task_assigned":
      case "lead_task_created":
        return <CheckSquare className="h-4 w-4 text-green-500" />;
      case "task_updated":
      case "task_created":
      case "lead_task_status_changed":
        return <Pencil className="h-4 w-4 text-orange-500" />;
      case "task_status_changed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "task_tagged":
      case "note_tagged":
      case "lead_task_tagged":
        return <Tag className="h-4 w-4 text-purple-500" />;
      case "note_created":
      case "lead_note_created":
        return <MessageSquare className="h-4 w-4 text-teal-500" />;
      case "lead_communication_added":
        return <MessageSquare className="h-4 w-4 text-teal-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card
      className={`${!isRead ? 'border-l-4 border-l-primary' : ''} hover:shadow-md transition-shadow cursor-pointer mb-2`}
      onClick={handleNavigate}
    >

      <div className="p-3">
        <div className="flex items-start gap-3">
          <div className="mt-0.5 flex-shrink-0 bg-gray-50 p-1.5 rounded-full">
            {getIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <h3 className="text-sm font-medium">{notification.title}</h3>
                <div className="flex items-center gap-1">
                  <Badge variant={
                    notification.priority === 'HIGH' ? 'destructive' :
                      notification.priority === 'MEDIUM' ? 'default' :
                        'outline'
                  } className="text-xs">
                    {notification.priority}
                  </Badge>
                  {notification.requires_action && (
                    <Badge variant="secondary" className="text-xs">Action</Badge>
                  )}
                </div>
              </div>
              <span className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
              </span>
            </div>

            <p className={`text-sm text-gray-700 mt-1 ${expanded ? '' : 'line-clamp-2'}`}>
              {notification.message}
            </p>

            {notification.message.length > 100 && (
              <Button
                variant="link"
                size="sm"
                className="p-0 h-auto mt-0.5 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  setExpanded(!expanded);
                }}
              >
                {expanded ? 'Show Less' : 'Show More'}
              </Button>
            )}

            <div className="flex justify-between items-center mt-2">
              <div className="flex items-center gap-2">
                {getEntityBadge(notification)}

                {/* Entity Context */}
                {(notification.content_object || notification.case_id) && (
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    {notification.content_object ? (
                      <>
                        <span>{notification.content_object.type.charAt(0).toUpperCase() + notification.content_object.type.slice(1)}:</span>
                        <span className="truncate max-w-[200px]">{notification.content_object.str}</span>
                      </>
                    ) : notification.case_id ? (
                      <>
                        <span>Case:</span>
                        <span>#{notification.case_id}</span>
                      </>
                    ) : null}
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={handleMarkAsReadOrUnread}
                  disabled={markAsRead.isPending || markAsUnread.isPending}
                >
                  {(markAsRead.isPending || markAsUnread.isPending) ? (
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  ) : null}
                  {isRead ? 'Mark as Unread' : 'Mark as Read'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={handleArchive}
                  disabled={archiveNotification.isPending}
                >
                  {archiveNotification.isPending ? (
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  ) : null}
                  {notification.is_archived ? 'Unarchive' : 'Archive'}
                </Button>
                {notification.case_id && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-xs flex items-center gap-1"
                    onClick={handleNavigate}
                  >
                    <ExternalLink className="h-3.5 w-3.5" />
                    View Case
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};
