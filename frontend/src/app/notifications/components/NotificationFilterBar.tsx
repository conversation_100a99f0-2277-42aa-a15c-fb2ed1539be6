import React, { useState, useEffect } from 'react';
import { NotificationFilters, NotificationType, NotificationPriority } from '@/services/notificationService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import { FilterButton } from '@/components/ui/filter-button';
import {
  Bell,
  ChevronDown,
  AlertTriangle,
  Clock,
  Calendar as CalendarIcon,
  Search,
  X,
  Archive
} from 'lucide-react';
import {
  useNotificationTypesQuery,
  useNotificationPrioritiesQuery,
  useNotificationGroupsQuery
} from '@/services/notificationService';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { DateRange } from 'react-day-picker';

interface NotificationFilterBarProps {
  filters: NotificationFilters;
  onFilterChange: (filters: NotificationFilters) => void;
}

export const NotificationFilterBar: React.FC<NotificationFilterBarProps> = ({
  filters,
  onFilterChange
}) => {
  const { data: notificationTypes } = useNotificationTypesQuery();
  const { data: priorities } = useNotificationPrioritiesQuery();
  const { data: notificationGroups } = useNotificationGroupsQuery();
  const [searchQuery, setSearchQuery] = useState('');
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    filters.start_date && filters.end_date
      ? {
          from: new Date(filters.start_date),
          to: new Date(filters.end_date)
        }
      : undefined
  );

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery) {
        onFilterChange({ ...filters, search_query: searchQuery });
      } else if (filters.search_query) {
        const { search_query, ...rest } = filters;
        onFilterChange(rest);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const handleReadStatusChange = (value: string) => {
    if (value === 'all') {
      const { is_read, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, is_read: value === 'unread' ? false : true });
    }
  };

  const handlePriorityChange = (value: string) => {
    if (value === 'all') {
      const { priority, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, priority: value });
    }
  };

  const handleTypeChange = (value: string) => {
    if (value === 'all') {
      const { notification_type, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, notification_type: value });
    }
  };

  const handleGroupChange = (value: string) => {
    if (value === 'all') {
      const { notification_type, ...rest } = filters;
      onFilterChange(rest);
    } else {
      // Find the group and get its types
      const group = notificationGroups?.find(g => g.id === value);
      if (group && group.types.length > 0) {
        // Join the types with commas for the API
        onFilterChange({ ...filters, notification_type: group.types.join(',') });
      }
    }
  };

  const handleRequiresActionChange = () => {
    if (filters.requires_action) {
      const { requires_action, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, requires_action: true });
    }
  };

  const handleArchivedStatusChange = (value: string) => {
    if (value === 'all') {
      const { is_archived, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, is_archived: value === 'archived' });
    }
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);

    if (range?.from) {
      const start_date = format(range.from, 'yyyy-MM-dd');
      const end_date = range.to ? format(range.to, 'yyyy-MM-dd') : start_date;
      onFilterChange({ ...filters, start_date, end_date });
    } else {
      const { start_date, end_date, ...rest } = filters;
      onFilterChange(rest);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    if (filters.search_query) {
      const { search_query, ...rest } = filters;
      onFilterChange(rest);
    }
  };

  // Get display names for selected filters
  const getSelectedTypeName = () => {
    if (!filters.notification_type || !notificationTypes) return 'All Types';
    const selected = notificationTypes.find((t: NotificationType) => t.value === filters.notification_type);
    return selected ? selected.label : 'All Types';
  };

  const getSelectedGroupName = () => {
    if (!filters.notification_type || !notificationGroups) return 'All Types';

    // Check if the current notification_type is a comma-separated list
    if (filters.notification_type.includes(',')) {
      // Find the group that contains all these types
      const types = filters.notification_type.split(',');
      const group = notificationGroups.find(g =>
        types.every(type => g.types.includes(type)) &&
        types.length === g.types.length
      );
      return group ? group.label : 'Custom Filter';
    } else {
      // It's a single type, find which group it belongs to
      const group = notificationGroups.find(g => g.types.includes(filters.notification_type || ''));
      return group ? group.label : 'Custom Filter';
    }
  };

  const getSelectedPriorityName = () => {
    if (!filters.priority || !priorities) return 'All Priorities';
    const selected = priorities.find((p: NotificationPriority) => p.value === filters.priority);
    return selected ? selected.label : 'All Priorities';
  };

  const getReadStatusName = () => {
    if (filters.is_read === undefined) return 'All';
    return filters.is_read ? 'Read' : 'Unread';
  };

  const getArchivedStatusName = () => {
    if (filters.is_archived === undefined) return 'All';
    return filters.is_archived ? 'Archived' : 'Not Archived';
  };

  const getSelectedGroupValue = () => {
    if (!filters.notification_type || !notificationGroups) return 'all';

    // Check if the current notification_type is a comma-separated list
    if (filters.notification_type.includes(',')) {
      // Find the group that contains all these types
      const types = filters.notification_type.split(',');
      const group = notificationGroups.find(g =>
        types.every(type => g.types.includes(type)) &&
        types.length === g.types.length
      );
      return group ? group.id : 'all';
    } else {
      // It's a single type, find which group it belongs to
      const group = notificationGroups.find(g => g.types.includes(filters.notification_type || ''));
      return group ? group.id : 'all';
    }
  };

  return (
    <div className="mb-6 space-y-4">
      {/* Search Bar */}
      <div className="relative w-full">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
        <Input
          placeholder="Search notifications..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-10 h-10 w-full"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 text-gray-400 hover:text-gray-600"
            onClick={clearSearch}
          >
            <X size={16} />
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {/* Read Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <Clock className="mr-2 h-4 w-4" />
              Status: {getReadStatusName()}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuRadioGroup
              value={filters.is_read === undefined ? 'all' : filters.is_read ? 'read' : 'unread'}
              onValueChange={handleReadStatusChange}
            >
              <DropdownMenuRadioItem value="all">All</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="unread">Unread</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="read">Read</DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Priority Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Priority: {getSelectedPriorityName()}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuRadioGroup
              value={filters.priority || 'all'}
              onValueChange={handlePriorityChange}
            >
              <DropdownMenuRadioItem value="all">All Priorities</DropdownMenuRadioItem>
              {priorities?.map(priority => (
                <DropdownMenuRadioItem key={priority.value} value={priority.value}>
                  {priority.label}
                </DropdownMenuRadioItem>
              ))}
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Group Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <Bell className="mr-2 h-4 w-4" />
              Type: {getSelectedGroupName()}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuRadioGroup
              value={getSelectedGroupValue()}
              onValueChange={handleGroupChange}
            >
              <DropdownMenuRadioItem value="all">All Types</DropdownMenuRadioItem>
              {notificationGroups?.map(group => (
                <DropdownMenuRadioItem key={group.id} value={group.id}>
                  {group.label}
                </DropdownMenuRadioItem>
              ))}
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Archived Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <Archive className="mr-2 h-4 w-4" />
              Archived: {getArchivedStatusName()}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuRadioGroup
              value={filters.is_archived === undefined ? 'all' : filters.is_archived ? 'archived' : 'not_archived'}
              onValueChange={handleArchivedStatusChange}
            >
              <DropdownMenuRadioItem value="all">All</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="not_archived">Not Archived</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="archived">Archived</DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Date Range Picker */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, 'MMM d, yyyy')} - {format(dateRange.to, 'MMM d, yyyy')}
                  </>
                ) : (
                  format(dateRange.from, 'MMM d, yyyy')
                )
              ) : (
                "Date Range"
              )}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              selected={dateRange}
              onSelect={handleDateRangeChange}
              numberOfMonths={2}
              className="rounded-md border"
            />
            <div className="p-3 border-t border-border flex justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDateRangeChange(undefined)}
              >
                Clear
              </Button>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Set date range to last 7 days
                    const today = new Date();
                    const sevenDaysAgo = new Date();
                    sevenDaysAgo.setDate(today.getDate() - 7);
                    handleDateRangeChange({
                      from: sevenDaysAgo,
                      to: today,
                    });
                  }}
                >
                  Last 7 Days
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Set date range to last 30 days
                    const today = new Date();
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(today.getDate() - 30);
                    handleDateRangeChange({
                      from: thirtyDaysAgo,
                      to: today,
                    });
                  }}
                >
                  Last 30 Days
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Requires Action Filter */}
        <FilterButton
          isActive={!!filters.requires_action}
          onClick={handleRequiresActionChange}
          dotColor="#ef4444"
        >
          Requires Action
        </FilterButton>
      </div>
    </div>
  );
};
