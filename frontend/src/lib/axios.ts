import axios, { AxiosResponse, AxiosError, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosInstance } from 'axios';
import { store, logout } from '@/store';
import { setTokens } from '@/store/slices/authSlice';
import Cookies from 'js-cookie';
import { toast } from '@/hooks/use-toast';

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  _retry?: boolean;
}

interface ValidationError {
  [key: string]: string[];
}

// Utility function to format validation errors
const formatValidationErrors = (errors: ValidationError): string => {
  return Object.entries(errors)
    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
    .join('\n');
};

// Utility function to handle different types of errors
const handleApiError = (error: AxiosError) => {
  if (!error.response) {
    // Network error
    // toast({
    //   title: "Network Error",
    //   description: "Please check your internet connection and try again.",
    //   variant: "destructive",
    // });
    return;
  }

  const { status, data, config } = error.response;
  const url = config?.url || '';

  // Check if this is a 404 from the adjuster API
  if (status === 404 &&( url.includes('/adjusters/') || url.includes('/emails/labels/') || url.includes('/mediator-details/') || url.includes('/case-contents/') || url.includes('/calendar/agenda/'))) {
    // Don't show toast for 404s from adjuster endpoints
    return;
  }

  switch (status) {
    case 400:
      // Handle validation errors
      if (typeof data === 'object' && data !== null) {
        const validationErrors = data as ValidationError;
        toast({
          title: "Validation Error",
          description: formatValidationErrors(validationErrors),
          variant: "destructive",
        });
      } else {
        toast({
          title: "Bad Request",
          description: "The request was invalid. Please try again.",
          variant: "destructive",
        });
      }
      break;

    case 401:
      // Don't show toast for 401 as it's handled by refresh token logic
      break;

    case 403:
      toast({
        title: "Access Denied",
        description: "You don't have permission to perform this action.",
        variant: "destructive",
      });
      break;

    case 404:
      toast({
        title: "Not Found",
        description: "The requested resource was not found.",
        variant: "destructive",
      });
      break;

    case 409:
      toast({
        title: "Conflict",
        description: "This operation conflicts with the current state. The resource might already exist.",
        variant: "destructive",
      });
      break;

    case 500:
      toast({
        title: "Server Error",
        description: "Something went wrong on our end. Please try again later.",
        variant: "destructive",
      });
      break;

    default:
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
  }
};

const axiosInstance: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_DJANGO_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to prevent multiple refresh token requests
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: AxiosError) => void;
}> = [];

const processQueue = (error: AxiosError | null, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token as string);
    }
  });
  failedQueue = [];
};

// git add src/components/CaseOverview/CaseOverview.tsx src/components/CaseOverview/DemandGeneration.tsx src/components/CaseOverview/DemandReview.tsx src/components/DemandView/DemandGeneration.tsx src/components/DemandView/DemandReview.tsx src/components/CaseOverview/LitigationTabs.tsx src/components/CaseOverview/PreLitigationTabs.tsx  src/components/CaseOverview/components/CaseDetail.tsx src/components/CaseOverview/components/CheckList.tsx

const getRefreshToken = (): string | null => {
  // Try to get refresh token from store first
  const storeRefreshToken = store.getState().auth.refreshToken;
  if (storeRefreshToken) {
    return storeRefreshToken;
  }
  
  // If not in store, try to get from cookies
  const cookieRefreshToken = Cookies.get('refreshToken');
  return cookieRefreshToken || null;
};

// Add new helper function to get access token
const getAccessToken = (): string | null => {
  // Try to get access token from store first
  const storeAccessToken = store.getState().auth.accessToken;
  if (storeAccessToken) {
    return storeAccessToken;
  }
  
  // If not in store, try to get from cookies
  const cookieAccessToken = Cookies.get('accessToken');
  return cookieAccessToken || null;
};

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;

    // Handle token refresh logic first
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          })
          .catch((err) => Promise.reject(err));
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = getRefreshToken();
      
      if (!refreshToken) {
        processQueue(new AxiosError('No refresh token available'));
        isRefreshing = false;
        return Promise.reject(error);
      }

      try {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_DJANGO_URL}/users/token/refresh/`,
          { refresh: refreshToken }
        );

        const { access: newAccessToken, refresh: newRefreshToken } = response.data;
        
        store.dispatch(
          setTokens({
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
          })
        );
        
        Cookies.set('accessToken', newAccessToken, { 
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });
        
        Cookies.set('refreshToken', newRefreshToken, { 
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });
        
        originalRequest.headers = originalRequest.headers || {};
        originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
        
        processQueue(null, newAccessToken);
        isRefreshing = false;

        return axiosInstance(originalRequest);
      } catch (refreshError) {
        store.dispatch(logout());
        processQueue(new AxiosError('Token refresh failed'));
        isRefreshing = false;
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors with toasts
    handleApiError(error);
    return Promise.reject(error);
  }
);

// Update request interceptor to check cookies as well
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getAccessToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

export default axiosInstance;