import React, { createContext, useContext, useState } from 'react';

interface PanelWidthContextType {
  panelWidth: number;
  setPanelWidth: (width: number) => void;
}

const PanelWidthContext = createContext<PanelWidthContextType | undefined>(undefined);

export function PanelWidthProvider({ children }: { children: React.ReactNode }) {
  const [panelWidth, setPanelWidth] = useState(0);

  return (
    <PanelWidthContext.Provider value={{ panelWidth, setPanelWidth }}>
      {children}
    </PanelWidthContext.Provider>
  );
}

export function usePanelWidth() {
  const context = useContext(PanelWidthContext);
  if (context === undefined) {
    throw new Error('usePanelWidth must be used within a PanelWidthProvider');
  }
  return context;
} 