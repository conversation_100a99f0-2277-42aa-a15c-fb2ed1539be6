import { createContext, useContext, useState, ReactNode, useEffect, useRef, useCallback } from 'react';
import { Document } from '@/type/doocument';

interface DocumentContextType {
  selectedDocuments: Document[];
  activeDocumentId: string | null;
  setSelectedDocuments: (docs: Document[]) => void;
  setActiveDocumentId: (id: string | null) => void;
  handleOpenDocument: (doc: Document) => void;
  isDemandGeneratorActive: boolean;
  setIsDemandGeneratorActive: (active: boolean) => void;
  getDocumentUrl: (id: string) => string | null;
  refreshDocumentUrl: (id: string, url: string) => void;
  registerDocumentElement: (id: string, element: HTMLElement) => void;
}

interface DocumentState {
  id: string;
  url: string;
  element?: HTMLElement;
  mediaElement?: HTMLIFrameElement | HTMLEmbedElement;
  lastAccessed: number;
  retryCount?: number;
  lastError?: string;
}

const DocumentContext = createContext<DocumentContextType | undefined>(undefined);

export function DocumentProvider({ children }: { children: ReactNode }) {
  const [selectedDocuments, setSelectedDocuments] = useState<Document[]>([]);
  const [activeDocumentId, setActiveDocumentId] = useState<string | null>(null);
  const [isDemandGeneratorActive, setIsDemandGeneratorActive] = useState(false);
  
  const documentStates = useRef<Map<string, DocumentState>>(new Map());
  const stableUrls = useRef<Map<string, string>>(new Map());

  const registerDocumentElement = useCallback((id: string, element: HTMLElement) => {
    const mediaElement = element.querySelector('iframe, embed') as HTMLIFrameElement | HTMLEmbedElement | null;
    const state: DocumentState = { 
      id, 
      url: stableUrls.current.get(id) || '',
      element,
      mediaElement: mediaElement || undefined,
      lastAccessed: Date.now() 
    };
    
    documentStates.current.set(id, state);

    const url = stableUrls.current.get(id);
    if (url && state.mediaElement) {
      state.mediaElement.src = url;
      state.url = url;
    }
  }, []);

  const setDocumentUrl = useCallback((id: string, url: string) => {
    const now = Date.now();
    const existingState = documentStates.current.get(id);
    const state: DocumentState = existingState || { 
      id, 
      url,
      lastAccessed: now,
      retryCount: 0
    };
    
    state.url = url;
    state.lastAccessed = now;
    state.retryCount = 0;
    state.lastError = undefined;
    documentStates.current.set(id, state);
    stableUrls.current.set(id, url);

    if (state.mediaElement) {
      try {
        state.mediaElement.src = url;
      } catch (err) {
        console.error('Error setting URL:', err);
        state.lastError = err instanceof Error ? err.message : 'Failed to set URL';
        state.retryCount = (state.retryCount || 0) + 1;
      }
    }
  }, []);

  const getDocumentUrl = useCallback((id: string): string | null => {
    const state = documentStates.current.get(id);
    if (state) {
      state.lastAccessed = Date.now();
      return state.url;
    }
    return stableUrls.current.get(id) || null;
  }, []);

  const handleOpenDocument = useCallback((data: Document) => {
    if (data.url) {
      setDocumentUrl(data.id, data.url);
    }

    setSelectedDocuments(prevDocs => {
      const exists = prevDocs.some(doc => doc.id === data.id);
      if (!exists) {
        setActiveDocumentId(data.id);
        setIsDemandGeneratorActive(false);
        
        queueMicrotask(() => {
          const container = document.querySelector('.tabs-scroll-container');
          if (container) {
            container.scrollTo({
              left: container.scrollWidth,
              behavior: 'smooth'
            });
          }
        });

        return [...prevDocs, data];
      }

      if (data.url) {
        return prevDocs.map(doc => 
          doc.id === data.id ? { ...doc, url: data.url } : doc
        );
      }

      return prevDocs;
    });
  }, [setDocumentUrl]);

  const refreshDocumentUrl = useCallback((id: string, url: string) => {
    setDocumentUrl(id, url);
    
    setSelectedDocuments(prevDocs => 
      prevDocs.map(doc => 
        doc.id === id ? { ...doc, url } : doc
      )
    );
  }, [setDocumentUrl]);

  useEffect(() => {
    if (selectedDocuments.length === 0) {
      setActiveDocumentId(null);
      documentStates.current.clear();
      stableUrls.current.clear();
    }
  }, [selectedDocuments]);

  useEffect(() => {
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000;

    const syncUrls = () => {
      selectedDocuments.forEach(doc => {
        const state = documentStates.current.get(doc.id);
        if (state?.mediaElement && state.url) {
          try {
            if (state.mediaElement.src !== state.url) {
              state.mediaElement.src = state.url;
              state.retryCount = 0;
              state.lastError = undefined;
            }
          } catch (err) {
            console.error('Error syncing URL:', err);
            state.lastError = err instanceof Error ? err.message : 'Failed to sync URL';
            state.retryCount = (state.retryCount || 0) + 1;

            if (state.retryCount < MAX_RETRIES) {
              setTimeout(() => {
                try {
                  state.mediaElement!.src = state.url;
                  state.retryCount = 0;
                  state.lastError = undefined;
                } catch (retryErr) {
                  console.error('Retry failed:', retryErr);
                }
              }, RETRY_DELAY * Math.pow(2, state.retryCount));
            }
          }
        }
      });
    };

    window.addEventListener('focus', syncUrls);
    window.addEventListener('resize', syncUrls);
    
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
          const element = mutation.target as HTMLElement;
          const docId = Array.from(documentStates.current.entries())
            .find(([, state]) => state.mediaElement === element)?.[0];
          
          if (docId) {
            const state = documentStates.current.get(docId);
            if (state && state.url && element.getAttribute('src') !== state.url) {
              try {
                element.setAttribute('src', state.url);
              } catch (err) {
                console.error('Error restoring URL:', err);
              }
            }
          }
        }
      });
    });

    observer.observe(document.body, { 
      childList: true, 
      subtree: true,
      attributes: true,
      attributeFilter: ['src']
    });

    return () => {
      window.removeEventListener('focus', syncUrls);
      window.removeEventListener('resize', syncUrls);
      observer.disconnect();
    };
  }, [selectedDocuments]);
  
  useEffect(() => {
    if (selectedDocuments.length > 0) {
      const interval = setInterval(() => {
        selectedDocuments.forEach(doc => {
          const state = documentStates.current.get(doc.id);
          if (state?.mediaElement && state.url && state.mediaElement.src !== state.url) {
            state.mediaElement.src = state.url;
          }
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [selectedDocuments]);

  return (
    <DocumentContext.Provider value={{
      selectedDocuments,
      activeDocumentId,
      setSelectedDocuments,
      setActiveDocumentId,
      handleOpenDocument,
      isDemandGeneratorActive,
      setIsDemandGeneratorActive,
      getDocumentUrl,
      refreshDocumentUrl,
      registerDocumentElement
    }}>
      {children}
    </DocumentContext.Provider>
  );
}

export function useDocumentContext() {
  const context = useContext(DocumentContext);
  if (context === undefined) {
    throw new Error('useDocumentContext must be used within a DocumentProvider');
  }
  return context;
} 