import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DamagesSection, DemandToSettleSection, DocumentData, FutureTreatment, InjuriesAndTreatmentsSection, LossOfHouseholdServices, LossOfWages, PainAndSuffering, TreatmentExpense, DocumentHeader } from '@/type/documentData';

interface ReasoningAIState {
  documentData: DocumentData | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: ReasoningAIState = {
  documentData: null,
  isLoading: false,
  error: null,
};

const reasoningAISlice = createSlice({
  name: 'reasoningAI',
  initialState,
  reducers: {
    setDocumentData: (state, action: PayloadAction<DocumentData | null>) => {
      console.log("\n\n\n\n", action.payload, "\n\n\n\n");
      state.documentData = action.payload;
      if (!state.documentData?.header && action.payload) {
        state.documentData!.header = {
          date: '',
          title: '',
          recipient: '',
          phone: '',
          insured: '',
          driver: '',
          client: '',
          lossDate: '',
          claimNo: '',
        };
      }
    },
    updateIntroductionSection: (state, action: PayloadAction<string>) => {
      if (state.documentData) {
        state.documentData.introduction_section = action.payload;
      }
    },
    updateFactLiabilitySection: (state, action: PayloadAction<string>) => {
      if (state.documentData) {
        state.documentData.fact_and_liability_section = action.payload;
      }
    },
    updateInjuriesTreatmentsSection: (state, action: PayloadAction<InjuriesAndTreatmentsSection>) => {
      if (state.documentData) {
        state.documentData.injuries_and_treatments_section = action.payload;
      }
    },
    updateDamagesSection: (state, action: PayloadAction<DamagesSection>) => {
      if (state.documentData) {
        state.documentData.damages_section = action.payload;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    updateLossOfHouseholdServices: (state, action: PayloadAction<LossOfHouseholdServices>) => {
      if (state.documentData?.damages_section) {
        state.documentData.damages_section.loss_of_household_services = action.payload;
      }
    },
    updateLossOfWages: (state, action: PayloadAction<LossOfWages>) => {
      if (state.documentData?.damages_section) {
        state.documentData.damages_section.loss_of_wages = action.payload;
      }
    },
    updatePainAndSuffering: (state, action: PayloadAction<PainAndSuffering>) => {
      if (state.documentData?.damages_section) {
        state.documentData.damages_section.pain_and_suffering = action.payload;
      }
    },
    updateFutureTreatments: (state, action: PayloadAction<FutureTreatment[]>) => {
      if (state.documentData?.damages_section) {
        state.documentData.damages_section.future_treatments = action.payload;
      }
    },
    updatePastExpenses: (state, action: PayloadAction<{ [key: string]: TreatmentExpense }>) => {
      if (state.documentData?.damages_section) {
        state.documentData.damages_section.treatment_expenses = action.payload;
      }
    },
    updateDemandToSettleSection: (state, action: PayloadAction<DemandToSettleSection>) => {
      if (state.documentData) {
        state.documentData.demand_to_settle_section = action.payload;
      }
    },
    updateDocumentHeader: (state, action: PayloadAction<DocumentHeader>) => {
      if (state.documentData) {
        state.documentData.header = action.payload;
      }
    },
  },
});

export const {
  setDocumentData,
  updateIntroductionSection,
  updateFactLiabilitySection,
  updateInjuriesTreatmentsSection,
  updateDamagesSection,
  setLoading,
  setError,
  updateLossOfHouseholdServices,
  updateLossOfWages,
  updatePainAndSuffering,
  updateFutureTreatments,
  updatePastExpenses,
  updateDemandToSettleSection,
  updateDocumentHeader,
} = reasoningAISlice.actions;

export default reasoningAISlice.reducer;
