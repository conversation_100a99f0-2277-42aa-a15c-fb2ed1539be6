import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { User } from '@/type/user';

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  selectedNavigation: string;
  isDocumentSidebarVisible: boolean;
  isTabsPanelCollapsed: boolean;
}

const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  selectedNavigation: '/dashboard',
  isDocumentSidebarVisible: true,
  isTabsPanelCollapsed: true,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
    },
    setTokens: (state, action: PayloadAction<{ accessToken: string | null; refreshToken: string | null }>) => {
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
    },
    setSelectedNavigation: (state, action: PayloadAction<string>) => {
      state.selectedNavigation = action.payload;
    },
    toggleDocumentSidebar: (state) => {
      state.isDocumentSidebarVisible = !state.isDocumentSidebarVisible;
    },
    setDocumentSidebarVisibility: (state, action: PayloadAction<boolean>) => {
      state.isDocumentSidebarVisible = action.payload;
    },
    toggleTabsPanel: (state) => {
      state.isTabsPanelCollapsed = !state.isTabsPanelCollapsed;
    },
    setTabsPanelCollapsed: (state, action: PayloadAction<boolean>) => {
      state.isTabsPanelCollapsed = action.payload;
    },
  },
});

export const { setUser, setTokens, setSelectedNavigation, toggleDocumentSidebar, setDocumentSidebarVisibility, toggleTabsPanel, setTabsPanelCollapsed } = authSlice.actions;
export default authSlice.reducer; 