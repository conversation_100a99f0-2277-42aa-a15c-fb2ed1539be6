import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Import Template type from service
import { Template } from '@/services/templateManagementService';

export type TemplateContextType = 
  | 'all'
  | 'attorney_lien'
  | 'client'
  | 'client_adjuster'
  | 'client_employer'
  | 'client_health_insurance'
  | 'client_insurance'
  | 'court'
  | 'defendant'
  | 'defendant_adjuster'
  | 'defendant_insurance'
  | 'defendant_legal_representation'
  | 'expert_witness'
  | 'health_provider'
  | 'judge'
  | 'mediator'
  | 'misc_lien'
  | 'other_party'
  | 'other_plaintiff'
  | 'police_agency'
  | 'settlement_advance'
  | 'sub_out'
  | 'witness'
  | 'client_correspondence'
  | 'litigation';

interface TemplatesState {
  selectedContextType: TemplateContextType;
  searchQuery: string;
  templates: Template[];
  loading: boolean;
  error: string | null;
  filteredTemplates: Template[];
}

const initialState: TemplatesState = {
  selectedContextType: 'all',
  searchQuery: '',
  templates: [],
  loading: false,
  error: null,
  filteredTemplates: [],
};

const templatesSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {
    setSelectedContextType: (state, action: PayloadAction<TemplateContextType>) => {
      state.selectedContextType = action.payload;
      // Filter templates based on context type
      state.filteredTemplates = state.templates?.filter(template => 
        state.selectedContextType === 'all' || template.context_type === state.selectedContextType
      )?.filter(template =>
        state.searchQuery === '' || 
        template.name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(state.searchQuery.toLowerCase())
      );
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      // Filter templates based on search query
      state.filteredTemplates = state.templates?.filter(template =>
        state.selectedContextType === 'all' || template.context_type === state.selectedContextType
      )?.filter(template =>
        state.searchQuery === '' || 
        template.name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(state.searchQuery.toLowerCase())
      );

    },
    setTemplatesLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setTemplatesError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setTemplates: (state, action: PayloadAction<Template[]>) => {
      state.templates = action.payload;
    },
  },
});

export const { 
  setSelectedContextType, 
  setSearchQuery, 
  setTemplatesLoading, 
  setTemplatesError, 
  setTemplates 
} = templatesSlice.actions;

export default templatesSlice.reducer; 