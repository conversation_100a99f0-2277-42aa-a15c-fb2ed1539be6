import { TwilioMessage } from '@/type/twilio';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '..';

interface ConversationState {
  conversations: TwilioMessage[] | null;
  loading: boolean;
  error: string | null;
  lastUpdated: {
    [key: string]: string;
  };
}

const initialState: ConversationState = {
  conversations: null,
  loading: false,
  error: null,
  lastUpdated: {}
};

const conversationSlice = createSlice({
  name: 'conversation',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    updateConversation: (state, action: PayloadAction<{ 
      data: TwilioMessage[] | undefined
      }>) => {
      const { data } = action.payload;
      
      if(data && state.conversations === null) {
        state.conversations = data;
      } else if (data && state.conversations !== null) {
        if(JSON.stringify(state.conversations) !== JSON.stringify(data)) {
          state.conversations = data;
        }
      }
    },
  }
});

export const { 
  setLoading, 
  setError, 
  updateConversation, 
} = conversationSlice.actions;

export default conversationSlice.reducer;

// Selectors
export const selectConversation = (state: RootState, conversationId: string) => 
  state.conversation.conversations?.[Number(conversationId)];

export const selectLastUpdated = (state: RootState, conversationId: string) => 
  state.conversation.lastUpdated[conversationId];

export const selectIsLoading = (state: RootState) => state.conversation.loading;
export const selectError = (state: RootState) => state.conversation.error; 