import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Lead, LeadStatistics } from '@/type/lead/leadTypes';
import { PaginatedResponse } from '@/type/dashboard';

interface LeadState {
    leads: Lead[];
    selectedLead: Lead | null;
    statistics: LeadStatistics | null;
    isLoading: boolean;
    pagination: {
        count: number;
        next: string | null;
        previous: string | null;
    };
    search: string;
    source: string;
    status: string;
    assignee: string;
    currentPage: number;
    pageSize: number;
}

const initialState: LeadState = {
    leads: [],
    selectedLead: null,
    statistics: null,
    isLoading: false,
    pagination: {
        count: 0,
        next: null,
        previous: null,
    },
    search: '',
    source: '',
    status: '',
    assignee: '',
    currentPage: 1,
    pageSize: 10,
};

const leadSlice = createSlice({
    name: 'leads',
    initialState,
    reducers: {
        setLeads: (state, action: PayloadAction<PaginatedResponse<Lead>>) => {
            state.leads = action.payload.results;
            state.pagination = {
                count: action.payload.count,
                next: action.payload.next,
                previous: action.payload.previous,
            };
        },
        setSelectedLead: (state, action: PayloadAction<Lead | null>) => {
            if (action.payload === null) {
                state.selectedLead = null;
            } else if (state.selectedLead) {
                state.selectedLead = {
                    ...state.selectedLead,
                    ...action.payload
                };
            } else {
                state.selectedLead = action.payload;
            }
        },
        setStatistics: (state, action: PayloadAction<LeadStatistics | null>) => {
            state.statistics = action.payload;
        },
        setIsLoading: (state, action: PayloadAction<boolean>) => {
            state.isLoading = action.payload;
        },
        updateLead: (state, action: PayloadAction<Lead>) => {
            const index = state.leads.findIndex(lead => lead.id === action.payload.id);
            if (index !== -1) {
                state.leads[index] = action.payload;
            }
            if (state.selectedLead?.id === action.payload.id) {
                state.selectedLead = action.payload;
            }
        },
        addLead: (state, action: PayloadAction<Lead>) => {
            state.leads.unshift(action.payload);
        },
        removeLead: (state, action: PayloadAction<string>) => {
            state.leads = state.leads.filter(lead => lead.id !== action.payload);
            if (state.selectedLead?.id === action.payload) {
                state.selectedLead = null;
            }
        },
        setSearch: (state, action: PayloadAction<string>) => {
            state.search = action.payload;
        },
        setSource: (state, action: PayloadAction<string>) => {
            state.source = action.payload;
        },
        setStatus: (state, action: PayloadAction<string>) => {
            state.status = action.payload;
        },
        setAssignee: (state, action: PayloadAction<string>) => {
            state.assignee = action.payload;
        },
        setCurrentPage: (state, action: PayloadAction<number>) => {
            state.currentPage = action.payload;
        },
        setPageSize: (state, action: PayloadAction<number>) => {
            state.pageSize = action.payload;
        },
    },
});

export const {
    setLeads,
    setSelectedLead,
    setStatistics,
    setIsLoading,
    updateLead,
    addLead,
    removeLead,
    setSearch,
    setSource,
    setStatus,
    setAssignee,
    setCurrentPage,
    setPageSize,
} = leadSlice.actions;

export default leadSlice.reducer;
