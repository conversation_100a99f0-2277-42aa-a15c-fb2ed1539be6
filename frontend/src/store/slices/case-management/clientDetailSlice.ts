import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ClientBasicDetails, ClientDetailState, ClientContactDetails } from '@/type/case-management/clientDetailTypes';

const initialState: ClientDetailState = {
    clientBasicDetails: {
        data: null,
        isLoading: false,
        error: null,
    },
    clientContactDetails: {
        data: null,
        isLoading: false,
        error: null,
    },
};

const clientDetailSlice = createSlice({
    name: 'clientDetail',
    initialState,
    reducers: {

        // Client Basic Details
        setClientBasicDetails: (state, action: PayloadAction<ClientBasicDetails | null>) => {
            state.clientBasicDetails.data = action.payload;
        },
        setIsLoading: (state, action: PayloadAction<boolean>) => {
            state.clientBasicDetails.isLoading = action.payload;
        },
        setError: (state, action: PayloadAction<string | null>) => {
            state.clientBasicDetails.error = action.payload;
        },
        updateClientBasicDetails: (state, action: PayloadAction<Partial<ClientBasicDetails>>) => {
            if (state.clientBasicDetails) {
                state.clientBasicDetails = {
                    ...state.clientBasicDetails,
                    ...action.payload
                };
            }
        },
        clearClientDetails: (state) => {
            state.clientBasicDetails.data = null;
            state.clientBasicDetails.error = null;
            state.clientBasicDetails.isLoading = false;
        },

        // Client Contact Details
        setClientContactDetails: (state, action: PayloadAction<ClientContactDetails | null>) => {
            state.clientContactDetails.data = action.payload;
        },
        setContactDetailsLoading: (state, action: PayloadAction<boolean>) => {
            state.clientContactDetails.isLoading = action.payload;
        },
        setContactDetailsError: (state, action: PayloadAction<string | null>) => {
            state.clientContactDetails.error = action.payload;
        },
        updateClientContactDetails: (state, action: PayloadAction<Partial<ClientContactDetails>>) => {
            if (state.clientContactDetails.data) {
                state.clientContactDetails.data = {
                    ...state.clientContactDetails.data,
                    ...action.payload
                };
            }
        },
        clearClientContactDetails: (state) => {
            state.clientContactDetails.data = null;
            state.clientContactDetails.error = null;
            state.clientContactDetails.isLoading = false;
        },
    },
});

export const {
    setClientBasicDetails,
    setIsLoading,
    setError,
    updateClientBasicDetails,
    clearClientDetails,
    setClientContactDetails,
    setContactDetailsLoading,
    setContactDetailsError,
    updateClientContactDetails,
    clearClientContactDetails,
} = clientDetailSlice.actions;

export default clientDetailSlice.reducer;
