import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CaseResponse, CaseStatistics, CaseStatisticsResponse } from '@/type/dashboard';

interface CaseState {
  all_cases: CaseResponse[];
  global_search_results: CaseResponse[];
  statistics: CaseStatistics | null;
  isLoading: boolean;
  showOrganizationCases: boolean;
}

const initialState: CaseState = {
  all_cases: [],
  global_search_results: [],
  statistics: null,
  isLoading: false,
  showOrganizationCases: false,
};

const caseSlice = createSlice({
  name: 'cases',
  initialState,
  reducers: {
    setShowOrganizationCases: (state, action: PayloadAction<boolean>) => {
      state.showOrganizationCases = action.payload;
    },
    setCases: (state, action: PayloadAction<CaseResponse[]>) => {
      state.all_cases = action.payload;
    },
    setGlobalSearchResults: (state, action: PayloadAction<CaseResponse[]>) => {
      state.global_search_results = action.payload;
    },
    setStatistics: (state, action: PayloadAction<CaseStatisticsResponse | null>) => {
      state.statistics = action.payload?.statistics ?? null;
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

export const { setShowOrganizationCases, setCases, setGlobalSearchResults, setStatistics, setIsLoading } = caseSlice.actions;
export default caseSlice.reducer;