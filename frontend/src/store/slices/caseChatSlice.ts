import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  contextFiles?: string[];
}

interface ChatState {
  messages: Record<string, ChatMessage[]>; // Keyed by caseId
  currentMessage: string;
  isStreaming: boolean;
}

const initialState: ChatState = {
  messages: {},
  currentMessage: '',
  isStreaming: false,
};

const caseChatSlice = createSlice({
  name: 'caseChat',
  initialState,
  reducers: {
    addMessage: (
      state,
      action: PayloadAction<{ caseId: string; message: ChatMessage }>
    ) => {
      const { caseId, message } = action.payload;
      if (!state.messages[caseId]) {
        state.messages[caseId] = [];
      }
      state.messages[caseId].push(message);
    },
    updateCurrentMessage: (state, action: PayloadAction<string>) => {
      state.currentMessage = action.payload;
    },
    setIsStreaming: (state, action: PayloadAction<boolean>) => {
      state.isStreaming = action.payload;
    },
    clearChatHistory: (state, action: PayloadAction<string>) => {
      state.messages[action.payload] = [];
    },
    removeLastMessage: (state, action: PayloadAction<string>) => {
      const caseId = action.payload;
      if (state.messages[caseId]?.length > 0) {
        state.messages[caseId].pop();
      }
    },
  },
});

export const { addMessage, updateCurrentMessage, setIsStreaming, clearChatHistory, removeLastMessage } =
  caseChatSlice.actions;
export default caseChatSlice.reducer; 