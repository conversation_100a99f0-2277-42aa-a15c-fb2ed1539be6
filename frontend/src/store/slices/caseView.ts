import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DocumentFile } from '@/type/doocument';
import { CaseFullDetails } from '@/type/dashboard';


interface CaseViewState {
  allDocuments: DocumentFile[];
  selectedDocument?: DocumentFile;
  isLoading: boolean;
  error: string | null;
  selectedCaseDetail: CaseFullDetails | undefined;
}

const initialState: CaseViewState = {
  allDocuments: [],
  selectedDocument: undefined,
  isLoading: false,
  error: null,
  selectedCaseDetail: undefined,
};

const caseViewSlice = createSlice({
  name: 'caseView',
  initialState,
  reducers: {
    setAllDocuments: (state, action: PayloadAction<DocumentFile[]>) => {
      state.allDocuments = action.payload;
    },
    setSelectedDocument: (state, action: PayloadAction<DocumentFile | undefined>) => {
      state.selectedDocument = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    resetCaseView: () => {
      return initialState;
    },
    setSelectedCaseDetail: (state, action: PayloadAction<CaseFullDetails | undefined>) => {
      state.selectedCaseDetail = action.payload;
    },
  },
});

export const {
  setAllDocuments,
  setSelectedDocument,
  setLoading,
  setError,
  resetCaseView,
  setSelectedCaseDetail,
} = caseViewSlice.actions;

export default caseViewSlice.reducer;
