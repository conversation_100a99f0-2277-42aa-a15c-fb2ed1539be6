import { DocumentWithCategories } from '@/type/doocument';
import { createCategoryFileMapping } from '@/utils/extractionUtils';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ExtractionState {
  documentCategories: DocumentWithCategories[];
  categoryFileMapping: Record<string, string[]>;
  isLoading: boolean;
  error: string | null;
}

const initialState: ExtractionState = {
  documentCategories: [],
  categoryFileMapping: {},
  isLoading: false,
  error: null,
};

const extractionSlice = createSlice({
  name: 'extraction',
  initialState,
  reducers: {
    setDocumentCategories: (state, action: PayloadAction<DocumentWithCategories[]>) => {
      state.documentCategories = action.payload;
      state.categoryFileMapping = createCategoryFileMapping(action.payload);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setDocumentCategories, setLoading, setError } = extractionSlice.actions;
export default extractionSlice.reducer;
