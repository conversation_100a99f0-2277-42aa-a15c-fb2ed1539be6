import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Notification } from '@/services/notificationService';

interface NotificationState {
  notifications: Notification[];
  lastNotificationIds: string[];
  newNotification: Notification | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: NotificationState = {
  notifications: [],
  lastNotificationIds: [],
  newNotification: null,
  isLoading: false,
  error: null,
};

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    setNotifications: (state, action: PayloadAction<Notification[]>) => {
      // Get current notification IDs
      const currentIds = action.payload.map(notification => notification.id);
      
      // If we have previous notifications
      if (state.lastNotificationIds.length > 0) {
        // Find new notifications (ones that weren't in the previous set)
        const newNotifications = action.payload.filter(
          notification => !state.lastNotificationIds.includes(notification.id)
        );
        
        // If there are new notifications, set the newest one for toast display
        if (newNotifications.length > 0) {
          // Sort by created_at to get the newest one
          const sortedNewNotifications = [...newNotifications].sort(
            (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          
          state.newNotification = sortedNewNotifications[0];
        } else {
          state.newNotification = null;
        }
      }
      
      // Update the state
      state.notifications = action.payload;
      state.lastNotificationIds = currentIds;
    },
    clearNewNotification: (state) => {
      state.newNotification = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { 
  setNotifications, 
  clearNewNotification,
  setLoading,
  setError
} = notificationSlice.actions;

export default notificationSlice.reducer; 