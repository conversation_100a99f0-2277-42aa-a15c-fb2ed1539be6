import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from './customStorage';
import { combineReducers } from 'redux';
import authReducer from '@/store/slices/authSlice';
import caseReducer from '@/store/slices/caseSlice';
import reasoningAIReducer from '@/store/slices/reasoningAISlice';
import caseViewReducer from '@/store/slices/caseView';
import caseChatReducer from '@/store/slices/caseChatSlice';
import extractionReducer from '@/store/slices/extractionSlice';
import leadSlice from '@/store/slices/lead/leadSlice';
import clientDetailReducer from '@/store/slices/case-management/clientDetailSlice';
import templatesReducer from '@/store/slices/templatesSlice';
import { QueryClient } from '@tanstack/react-query';
import conversationReducer from '@/store/slices/conversationSlice';
import notificationReducer from '@/store/slices/notificationSlice';

export const queryClient = new QueryClient();

const persistConfig = {
  key: 'root',
  storage,
};

const rootReducer = combineReducers({
  auth: authReducer,
  cases: caseReducer,
  reasoningAI: reasoningAIReducer,
  caseView: caseViewReducer,
  caseChat: caseChatReducer,
  extraction: extractionReducer,
  leads: leadSlice,
  clientDetail: clientDetailReducer,
  templates: templatesReducer,
  conversation: conversationReducer,
  notification: notificationReducer,
});

const rootReducerWithReset = (
  state: ReturnType<typeof rootReducer> | undefined,
  action: { type: string; payload?: unknown }
) => {
  if (action.type === 'LOGOUT') {
    localStorage.clear();
    sessionStorage.clear();
    state = undefined;

    queryClient.clear();
  }
  return rootReducer(state, action);
};

const persistedReducer = persistReducer(persistConfig, rootReducerWithReset);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const logout = () => ({ type: 'LOGOUT' });

export { setUser, setTokens, setSelectedNavigation } from './slices/authSlice';
export { selectConversation, selectLastUpdated,selectIsLoading,selectError } from './slices/conversationSlice'