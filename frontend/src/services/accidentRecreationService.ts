import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/case-management/cases';

export interface AccidentRecreationVideoResponse {
  accident_recreation_video_url: string;
  created_at?: string;
  updated_at?: string;
}

export interface AccidentRecreationVideoRequest {
  accident_recreation_video_url: string;
}

export const useAccidentRecreationVideoQuery = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['accidentRecreationVideo', caseId],
    queryFn: async () => {
      const { data } = await axios.get<AccidentRecreationVideoResponse>(
        `${BASE_URL}/${caseId}/accident_recreation_video/`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    },
    enabled: !!accessToken && !!caseId
  });
};

export const useCreateAccidentRecreationVideoMutation = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ caseId, videoData }: { caseId: string; videoData: AccidentRecreationVideoRequest }) => {
      const { data } = await axios.post<AccidentRecreationVideoResponse>(
        `${BASE_URL}/${caseId}/accident_recreation_video/`,
        videoData,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['accidentRecreationVideo', variables.caseId] });
      toast({ title: "Success", description: "Video added successfully" });
    }
  });
};

export const useUpdateAccidentRecreationVideoMutation = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ caseId, videoData }: { caseId: string; videoData: AccidentRecreationVideoRequest }) => {
      const { data } = await axios.put<AccidentRecreationVideoResponse>(
        `${BASE_URL}/${caseId}/accident_recreation_video/`,
        videoData,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['accidentRecreationVideo', variables.caseId] });
      toast({ title: "Success", description: "Video updated successfully" });
    }
  });
}; 