import { useMutation, UseMutationResult, use<PERSON><PERSON><PERSON>, UseQueryResult, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

// Types
export interface Notification {
    id: string;
    title: string;
    message: string;
    notification_type: string;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    level: 'CASE' | 'ORGANIZATION';
    is_read: boolean;
    is_archived: boolean;
    requires_action: boolean;
    created_at: string;
    expires_at?: string;
    case_id: string;
    organization_id: string;
    content_object?: {
        id: string;
        type: string;
        str: string;
    };
    // case?: {
    //     id: string;
    //     case_name: string;
    // };
    // organization?: {
    //     id: string;
    //     name: string;
    // };
}

export interface NotificationFilters {
    is_read?: boolean;
    is_archived?: boolean;
    priority?: string;
    requires_action?: boolean;
    notification_type?: string;
    search_query?: string;
    start_date?: string;
    end_date?: string;
}

export interface NotificationType {
    value: string;
    label: string;
}

export interface NotificationPriority {
    value: string;
    label: string;
}

export interface NotificationGroup {
    id: string;
    label: string;
    types: string[];
}

export interface ActivityFilters {
    search_query?: string;
    [key: string]: string | number | boolean | undefined;
}

export interface ListResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    unread_count?: number;
    results: T[];
}

export interface UnreadCount {
    unread_count: number;
    requires_action_count: number;
}

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/notifications';

/**
 * Hook to fetch notifications list with filters
 */
export const useNotificationsListQuery = (filters: NotificationFilters = {}) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<Notification>, Error>({
        queryKey: ['notifications', filters],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<Notification>>(`${BASE_URL}/`, {
                params: {
                    ...filters,
                    page: pageParam,
                    page_size: 50 // Load 50 notifications per page
                },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        getNextPageParam: (lastPage) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                const page = url.searchParams.get('page');
                return page ? parseInt(page) : undefined;
            }
            return undefined;
        },
        initialPageParam: 1,
        refetchInterval: 60000,
        refetchIntervalInBackground: true
    });
};

/**
 * Hook to fetch unread notification count
 */
export const useUnreadCountQuery = (): UseQueryResult<UnreadCount> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['notifications', 'unread-count'],
        queryFn: async () => {
            const response = await axios.get<UnreadCount>(`${BASE_URL}/unread-count/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        }
    });
};

/**
 * Hook to mark a notification as read
 */
export const useMarkNotificationReadMutation = (): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (notificationId: string) => {
            await axios.post(`${BASE_URL}/${notificationId}/mark-read/`, null, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
            toast({
                title: "Success",
                description: "Notification marked as read",
            });
        }
    });
};

/**
 * Hook to mark a notification as unread
 */
export const useMarkNotificationUnreadMutation = (): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (notificationId: string) => {
            await axios.post(`${BASE_URL}/${notificationId}/mark-unread/`, null, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
            toast({
                title: "Success",
                description: "Notification marked as unread",
            });
        }
    });
};

/**
 * Hook to mark all notifications as read
 */
export const useMarkAllNotificationsReadMutation = (): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async () => {
            await axios.post(`${BASE_URL}/mark-all-read/`, null, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
            toast({
                title: "Success",
                description: "All notifications marked as read",
            });
        }
    });
};

/**
 * Hook to archive a notification
 */
export const useArchiveNotificationMutation = (): UseMutationResult<void, Error, { id: string, isArchived: boolean }> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async ({ id, isArchived }: { id: string, isArchived: boolean }) => {
            // If already archived, unarchive it, otherwise archive it
            const endpoint = isArchived ? 'unarchive' : 'archive';
            await axios.post(`${BASE_URL}/${id}/${endpoint}/`, null, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
            toast({
                title: "Success",
                description: variables.isArchived ? "Notification unarchived" : "Notification archived",
            });
        }
    });
};

/**
 * Hook to fetch case activities
 */
export const useCaseActivitiesQuery = (caseId: string, searchQuery?: string) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    const filters: ActivityFilters = {
        ...(searchQuery && { search_query: searchQuery })
    };

    return useInfiniteQuery<ListResponse<Notification>, Error>({
        queryKey: ['case-activities', caseId, searchQuery],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<Notification>>(
                `${BASE_URL}/cases/${caseId}/activities/`,
                {
                    params: {
                        ...filters,
                        page: pageParam
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        getNextPageParam: (lastPage) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                const page = url.searchParams.get('page');
                return page ? parseInt(page) : undefined;
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to fetch organization activities
 */
export const useOrganizationActivitiesQuery = (organizationId: string, filters: ActivityFilters = {}) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<Notification>, Error>({
        queryKey: ['organization-activities', organizationId, filters],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<Notification>>(
                `${BASE_URL}/organization/activities/`,
                {
                    params: {
                        organization_id: organizationId,
                        ...filters,
                        page: pageParam
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        getNextPageParam: (lastPage) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                const page = url.searchParams.get('page');
                return page ? parseInt(page) : undefined;
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to fetch notification types
 */
export const useNotificationTypesQuery = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery<NotificationType[]>({
        queryKey: ['notification-types'],
        queryFn: async () => {
            const response = await axios.get<NotificationType[]>(`${BASE_URL}/types/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        }
    });
};

/**
 * Hook to fetch notification priorities
 */
export const useNotificationPrioritiesQuery = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery<NotificationPriority[]>({
        queryKey: ['notification-priorities'],
        queryFn: async () => {
            const response = await axios.get<NotificationPriority[]>(`${BASE_URL}/priorities/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        }
    });
};

/**
 * Hook to fetch notification groups
 */
export const useNotificationGroupsQuery = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['notification-groups'],
        queryFn: async () => {
            const response = await axios.get<NotificationGroup[]>(`${BASE_URL}/groups/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        }
    });
};