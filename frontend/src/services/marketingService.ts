import { useQuery, useMutation, useQuery<PERSON>lient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
  PaginatedResponse,
  PartnerContact,
  EngagementType,
  Engagement,
  MapData,
  Statistics,
  DashboardResponse,
  MedicalProvider,
  LinkMedicalProviderResponse
} from '@/type/marketing';
import { AxiosError } from 'axios';

interface PartnerFilters {
  search?: string;
  assigned_to?: string | null;
  is_provider?: boolean;
  is_active?: boolean;
  partner_type?: string;
  relationship_status?: string;
  city?: string;
  state?: string;
}

interface EngagementFilters {
  search?: string;
  created_by?: string | number | null;
  created_by_users?: (string | number)[];
  attendees?: (string | number)[];
  users?: (string | number)[];
  partner?: string | number | null;
  partner_id?: string | number | null;
  engagement_type?: string | number;
  status?: string;
  start_date?: string;
  end_date?: string;
  date_field?: string; // Field to filter by date (date, follow_up_date, created_at)
  meeting_start_date?: string;
  meeting_end_date?: string;
  followup_start_date?: string;
  followup_end_date?: string;
  reminder_start_date?: string;
  reminder_end_date?: string;
  user_field?: string; // Field to filter by user type (created_by, attendees, users)
}

interface QueryParams extends Record<string, string | number | string[] | number[] | undefined> {
  page?: number;
  search?: string;
  status?: string;
  engagement_type?: string | number;
  start_date?: string;
  end_date?: string;
  partner_id?: string | number;
  created_by?: string | number;
  created_by_users?: string | number[];
  attendees?: string | number[];
  users?: string | number[];
  meeting_start_date?: string;
  meeting_end_date?: string;
  followup_start_date?: string;
  followup_end_date?: string;
  reminder_start_date?: string;
  reminder_end_date?: string;
  date_field?: string;
  user_field?: string;
}

interface PartnerQueryParams extends Record<string, string | number | boolean | undefined> {
  page?: number;
  page_size?: number;
  search?: string;
  assigned_to?: string;
  is_provider?: boolean;
  is_active?: boolean;
  partner_type?: string;
  relationship_status?: string;
  city?: string;
  state?: string;
}

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/marketing-management';

// Partner Contacts
export const usePartnerContacts = (
  page: number = 1,
  filters?: PartnerFilters,
  pageSize: number = 10
): UseQueryResult<PaginatedResponse<PartnerContact>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['partners', page, filters, pageSize],
    queryFn: async () => {
      try {
        // Prepare params - ensure all values are properly formatted for the API
        const params: PartnerQueryParams = { page, page_size: pageSize };

        // Add filters if they exist
        if (filters) {
          // Only add defined filters
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== '') {
              // Handle null values specially - they represent "unassigned"
              if (value === null) {
                params[key] = 'null'; // Send 'null' as a string to the API
              } else {
                params[key] = value;
              }
            }
          });
        }

        console.log('Making API request with params:', params);

        const { data } = await axios.get<PaginatedResponse<PartnerContact>>(
          `${BASE_URL}/partners/`,
          {
            params,
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch partners';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

export const useCreatePartnerContact = (): UseMutationResult<
  PartnerContact,
  Error,
  Partial<PartnerContact>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<PartnerContact>) => {
      const response = await axios.post<PartnerContact>(
        `${BASE_URL}/partners/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all partner-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['partners'] });

      // Force an immediate refetch of the current page
      queryClient.refetchQueries({ queryKey: ['partners'] });

      toast({
        title: "Success",
        description: "Partner created successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create partner';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdatePartnerContact = (partnerId: number): UseMutationResult<
  PartnerContact,
  Error,
  Partial<PartnerContact>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<PartnerContact>) => {
      const response = await axios.patch<PartnerContact>(
        `${BASE_URL}/partners/${partnerId}/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all partner-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['partners'] });

      // Force an immediate refetch of the current page
      queryClient.refetchQueries({ queryKey: ['partners'] });

      toast({
        title: "Success",
        description: "Partner updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update partner';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useDeletePartnerContact = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (partnerId: number) => {
      await axios.delete(
        `${BASE_URL}/partners/${partnerId}/`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
    },
    onSuccess: () => {
      // Invalidate all partner-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['partners'] });

      // Force an immediate refetch of the current page
      queryClient.refetchQueries({ queryKey: ['partners'] });

      toast({
        title: "Success",
        description: "Partner deleted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete partner';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Engagement Types
export const useEngagementTypes = (): UseQueryResult<EngagementType[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['engagement-types'],
    queryFn: async () => {
      try {
        const { data } = await axios.get<EngagementType[]>(
          `${BASE_URL}/engagement-types/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch engagement types';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

export const useEngagementTypesByCategory = (): UseQueryResult<Record<string, EngagementType[]>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['engagement-types-by-category'],
    queryFn: async () => {
      try {
        const { data } = await axios.get<Record<string, EngagementType[]>>(
          `${BASE_URL}/engagement-types/by_category/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch engagement types';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

export const useCreateEngagementType = (): UseMutationResult<
  EngagementType,
  Error,
  Partial<EngagementType>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<EngagementType>) => {
      const response = await axios.post<EngagementType>(
        `${BASE_URL}/engagement-types/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagement-types'] });
      toast({
        title: "Success",
        description: "Engagement type created successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create engagement type';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateEngagementType = (typeId: number): UseMutationResult<
  EngagementType,
  Error,
  Partial<EngagementType>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<EngagementType>) => {
      const response = await axios.patch<EngagementType>(
        `${BASE_URL}/engagement-types/${typeId}/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagement-types'] });
      toast({
        title: "Success",
        description: "Engagement type updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update engagement type';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useDeleteEngagementType = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (typeId: number) => {
      await axios.delete(
        `${BASE_URL}/engagement-types/${typeId}/`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagement-types'] });
      toast({
        title: "Success",
        description: "Engagement type deleted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete engagement type';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Engagements
export const useEngagements = (
  page: number = 1,
  filters?: EngagementFilters
): UseQueryResult<PaginatedResponse<Engagement>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  // Log the filters being used
  console.log('useEngagements hook called with filters:', { page, ...filters });

  return useQuery({
    queryKey: ['engagements', page, filters],
    queryFn: async () => {
      try {
        // Prepare params - ensure all values are properly formatted for the API
        const params: QueryParams = { page };

        // Add filters if they exist
        if (filters) {
          // Only add defined filters
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== '') {
              // Handle null values specially - they represent "unassigned"
              if (value === null) {
                params[key] = 'null'; // Send 'null' as a string to the API
              }
              // Handle array values
              else if (Array.isArray(value)) {
                if (value.length > 0) {
                  // Format array values as [1,2,3]
                  if (key === 'users' || key === 'created_by_users' || key === 'attendees') {
                    params[key] = `[${value.join(',')}]`;
                  } else {
                    params[key] = value.join(',');
                  }
                }
              }
              // Handle partner_id alias for partner
              else if (key === 'partner') {
                params['partner_id'] = value;
              }
              // Handle all other values
              else {
                params[key] = value;
              }
            }
          });
        }

        console.log('Making API request with params:', params);

        const { data } = await axios.get<PaginatedResponse<Engagement>>(
          `${BASE_URL}/engagements/`,
          {
            params,
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );

        console.log('API response received:', { count: data.count, results: data.results.length });
        return data;
      } catch (error) {
        console.error('Error fetching engagements:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch engagements';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    refetchOnWindowFocus: false // Prevent refetching when window regains focus
  });
};

export const useCreateEngagement = (): UseMutationResult<
  Engagement,
  Error,
  Partial<Engagement>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<Engagement>) => {
      const response = await axios.post<Engagement>(
        `${BASE_URL}/engagements/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all engagement-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['engagements'] });
      queryClient.invalidateQueries({ queryKey: ['partner-engagements'] });
      queryClient.invalidateQueries({ queryKey: ['engagements-calendar'] });

      toast({
        title: "Success",
        description: "Engagement created successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create engagement';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateEngagement = (engagementId: number): UseMutationResult<
  Engagement,
  Error,
  Partial<Engagement>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<Engagement>) => {
      console.log('Updating engagement with ID:', engagementId);
      console.log('Update data:', data);

      // Ensure we have a valid ID
      if (!engagementId || engagementId <= 0) {
        console.error('Invalid engagement ID for update:', engagementId);
        throw new Error('Invalid engagement ID for update');
      }

      // Log the full request details
      console.log('Making PATCH request to:', `${BASE_URL}/engagements/${engagementId}/`);
      console.log('With headers:', { Authorization: `Bearer ${accessToken ? accessToken.substring(0, 10) + '...' : 'missing'}` });
      console.log('With data:', data);

      try {
        const response = await axios.patch<Engagement>(
          `${BASE_URL}/engagements/${engagementId}/`,
          data,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );

        console.log('PATCH request successful, response:', response.data);
        return response.data;
      } catch (error) {
        console.error('PATCH request failed:', error);
        const axiosError = error as AxiosError<{ detail?: string }>;
        if (axiosError.response) {
          console.error('Error response:', axiosError.response.status, axiosError.response.data);
        }
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate all engagement-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['engagements'] });
      queryClient.invalidateQueries({ queryKey: ['partner-engagements'] });
      queryClient.invalidateQueries({ queryKey: ['engagements-calendar'] });

      toast({
        title: "Success",
        description: "Engagement updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error updating engagement:', error);

      // Extract more detailed error information if available
      let errorMessage = 'Failed to update engagement';
      if (error instanceof Error) {
        errorMessage = error.message;
        console.error('Error details:', {
          message: error.message,
          stack: error.stack
        });
      }

      // Check for axios error with response data
      const axiosError = error as AxiosError<{ detail?: string }>;
      if (axiosError.response?.data?.detail) {
        errorMessage = axiosError.response.data.detail;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useDeleteEngagement = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (engagementId: number) => {
      await axios.delete(
        `${BASE_URL}/engagements/${engagementId}/`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagements'] });
      toast({
        title: "Success",
        description: "Engagement deleted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete engagement';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Mutation for updating just the rating of an engagement
export const useUpdateEngagementRating = (): UseMutationResult<
  Engagement,
  Error,
  { engagementId: number; rating: number }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ engagementId, rating }: { engagementId: number; rating: number }) => {
      console.log(`Updating rating for engagement ${engagementId} to ${rating}`);

      const response = await axios.patch<Engagement>(
        `${BASE_URL}/engagements/${engagementId}/`,
        { success_rating: rating },
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagements'] });
      toast({
        title: "Success",
        description: "Rating updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error updating rating:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update rating';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Mutation for updating just the status of an engagement
export const useUpdateEngagementStatus = (): UseMutationResult<
  Engagement,
  Error,
  { engagementId: number; status: string }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ engagementId, status }: { engagementId: number; status: string }) => {
      console.log(`Updating status for engagement ${engagementId} to ${status}`);

      const response = await axios.patch<Engagement>(
        `${BASE_URL}/engagements/${engagementId}/`,
        { status },
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagements'] });
      toast({
        title: "Success",
        description: "Status updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error updating status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update status';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Mutation for updating just the relationship status of a partner contact
export const useUpdatePartnerRelationshipStatus = (): UseMutationResult<
  PartnerContact,
  Error,
  { partnerId: number; relationshipStatus: string }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ partnerId, relationshipStatus }: { partnerId: number; relationshipStatus: string }) => {
      console.log(`Updating relationship status for partner ${partnerId} to ${relationshipStatus}`);

      const response = await axios.patch<PartnerContact>(
        `${BASE_URL}/partners/${partnerId}/`,
        { relationship_status: relationshipStatus },
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['partners'] });
      toast({
        title: "Success",
        description: "Relationship status updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error updating relationship status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update relationship status';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Calendar Data
export const useEngagementCalendar = (
  startDate: string,
  endDate: string
): UseQueryResult<Engagement[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['engagements-calendar', startDate, endDate],
    queryFn: async () => {
      try {
        const { data } = await axios.get<Engagement[]>(
          `${BASE_URL}/engagements/calendar/`,
          {
            params: { start_date: startDate, end_date: endDate },
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch calendar data';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

// Map Data
export const usePartnerMapData = (): UseQueryResult<MapData[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['partner-map-data'],
    queryFn: async () => {
      try {
        const { data } = await axios.get<MapData[]>(
          `${BASE_URL}/partners/map_data/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch map data';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false
  });
};

// Statistics
export const useMarketingStatistics = (): UseQueryResult<Statistics> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['marketing-statistics'],
    queryFn: async () => {
      try {
        const { data } = await axios.get<Statistics>(
          `${BASE_URL}/partners/statistics/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch statistics';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false
  });
};

// Dashboard
export const useMarketingDashboard = (
  startDate?: string,
  endDate?: string
): UseQueryResult<DashboardResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['marketing-dashboard', startDate, endDate],
    queryFn: async () => {
      try {
        const { data } = await axios.get<DashboardResponse>(
          `${BASE_URL}/partners/dashboard/`,
          {
            params: {
              start_date: startDate,
              end_date: endDate
            },
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch dashboard data';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false
  });
};

// Medical Providers
export const useAvailableMedicalProviders = (): UseQueryResult<MedicalProvider[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['available-medical-providers'],
    queryFn: async () => {
      try {
        const { data } = await axios.get<MedicalProvider[]>(
          `${BASE_URL}/partners/available_medical_providers/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch medical providers';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

export const useLinkMedicalProvider = (partnerId: number): UseMutationResult<
  LinkMedicalProviderResponse,
  Error,
  { medical_provider_id: number | null }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { medical_provider_id: number | null }) => {
      const response = await axios.post<LinkMedicalProviderResponse>(
        `${BASE_URL}/partners/${partnerId}/link_medical_provider/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['partners'] });
      queryClient.invalidateQueries({ queryKey: ['available-medical-providers'] });

      toast({
        title: "Success",
        description: data.message,
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to link medical provider';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Partner Engagements
export const usePartnerEngagements = (
  partnerId: string | number,
  options?: { enabled?: boolean }
): UseQueryResult<PaginatedResponse<Engagement>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['partner-engagements', partnerId],
    queryFn: async () => {
      try {
        const { data } = await axios.get<PaginatedResponse<Engagement>>(
          `${BASE_URL}/engagements/`,
          {
            params: { partner_id: partnerId } as QueryParams,
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch partner engagements';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    // Only enable the query if explicitly enabled (default to false)
    enabled: options?.enabled ?? false
  });
};