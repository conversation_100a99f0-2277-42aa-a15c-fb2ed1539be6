// marketing-goals-api.ts - API functions for marketing goals

import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { PaginatedResponse } from '@/type/marketing';

import {
  MarketingGoal,
  UserMarketingGoal,
  UserMarketingGoalHistory,
  GoalProgressUpdate,
  AnalyticsDashboardResponse,
  GoalCategory,
  GoalStatus,
  UserAnalyticsResponse
} from '@/type/marketing-goals';


interface GoalFilters {
  category?: GoalCategory;
  is_active?: boolean;
}

interface UserGoalFilters {
  status?: GoalStatus;
  user_id?: number;
  category?: GoalCategory;
  start_date?: string;
  end_date?: string;
}

interface GoalHistoryFilters {
  user_id?: number;
  goal_id?: number;
  start_date?: string;
  end_date?: string;
}

interface AnalyticsDashboardFilters {
  start_date?: string;
  end_date?: string;
}

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/marketing-management';

// Marketing Goals
export const useMarketingGoals = (
  filters?: GoalFilters
): UseQueryResult<MarketingGoal[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['marketing-goals', filters],
    queryFn: async () => {
      try {
        const { data } = await axios.get<MarketingGoal[]>(
          `${BASE_URL}/goals/`,
          {
            params: { ...filters },
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch marketing goals';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

export const useCreateMarketingGoal = (): UseMutationResult<
  MarketingGoal,
  Error,
  Partial<MarketingGoal>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<MarketingGoal>) => {
      const response = await axios.post<MarketingGoal>(
        `${BASE_URL}/goals/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marketing-goals'] });
      toast({
        title: "Success",
        description: "Marketing goal created successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create marketing goal';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateMarketingGoal = (goalId: number): UseMutationResult<
  MarketingGoal,
  Error,
  Partial<MarketingGoal>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<MarketingGoal>) => {
      const response = await axios.patch<MarketingGoal>(
        `${BASE_URL}/goals/${goalId}/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marketing-goals'] });
      toast({
        title: "Success",
        description: "Marketing goal updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update marketing goal';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useDeleteMarketingGoal = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (goalId: number) => {
      await axios.delete(
        `${BASE_URL}/goals/${goalId}/`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marketing-goals'] });
      toast({
        title: "Success",
        description: "Marketing goal deleted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete marketing goal';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// User Marketing Goals
export const useUserMarketingGoals = (
  page: number = 1,
  filters?: UserGoalFilters
): UseQueryResult<PaginatedResponse<UserMarketingGoal>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['user-marketing-goals', page, filters],
    queryFn: async () => {
      try {
        const { data } = await axios.get<PaginatedResponse<UserMarketingGoal>>(
          `${BASE_URL}/user-goals/`,
          {
            params: { page, ...filters },
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user marketing goals';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

export const useCreateUserMarketingGoal = (): UseMutationResult<
  UserMarketingGoal,
  Error,
  Partial<UserMarketingGoal>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<UserMarketingGoal>) => {
      const response = await axios.post<UserMarketingGoal>(
        `${BASE_URL}/user-goals/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-marketing-goals'] });
      toast({
        title: "Success",
        description: "User marketing goal created successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create user marketing goal';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateUserMarketingGoal = (goalId: number): UseMutationResult<
  UserMarketingGoal,
  Error,
  Partial<UserMarketingGoal>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Partial<UserMarketingGoal>) => {
      const response = await axios.patch<UserMarketingGoal>(
        `${BASE_URL}/user-goals/${goalId}/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-marketing-goals'] });
      toast({
        title: "Success",
        description: "User marketing goal updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user marketing goal';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useDeleteUserMarketingGoal = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (goalId: number) => {
      await axios.delete(
        `${BASE_URL}/user-goals/${goalId}/`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-marketing-goals'] });
      toast({
        title: "Success",
        description: "User marketing goal deleted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete user marketing goal';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateGoalProgress = (goalId: number): UseMutationResult<
  UserMarketingGoal,
  Error,
  GoalProgressUpdate
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: GoalProgressUpdate) => {
      const response = await axios.post<UserMarketingGoal>(
        `${BASE_URL}/user-goals/${goalId}/update_progress/`,
        data,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-marketing-goals'] });
      toast({
        title: "Success",
        description: "Goal progress updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update goal progress';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateUserGoalStatus = (): UseMutationResult<
  UserMarketingGoal,
  Error,
  { goalId: number; status: GoalStatus }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { goalId: number; status: GoalStatus }) => {
      const response = await axios.patch<UserMarketingGoal>(
        `${BASE_URL}/user-goals/${data.goalId}/`,
        { status: data.status },
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-marketing-goals'] });
      toast({
        title: "Success",
        description: "Goal status updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update goal status';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });
};

// Goal History
export const useGoalHistory = (
  page: number = 1,
  filters?: GoalHistoryFilters
): UseQueryResult<PaginatedResponse<UserMarketingGoalHistory>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['goal-history', page, filters],
    queryFn: async () => {
      try {
        const { data } = await axios.get<PaginatedResponse<UserMarketingGoalHistory>>(
          `${BASE_URL}/goal-history/`,
          {
            params: { page, ...filters },
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch goal history';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    }
  });
};

// Marketing Analytics Dashboard
export const useMarketingAnalyticsDashboard = (
  filters?: AnalyticsDashboardFilters
): UseQueryResult<AnalyticsDashboardResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['marketing-analytics-dashboard', filters],
    queryFn: async () => {
      try {
        const { data } = await axios.get<AnalyticsDashboardResponse>(
          `${BASE_URL}/analytics/dashboard/`,
          {
            params: filters,
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch marketing analytics';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false
  });
};

// User Analytics Filters interface
interface UserAnalyticsFilters {
  user_id: number;
  start_date?: string;
  end_date?: string;
}

// Marketing User Analytics
export const useMarketingUserAnalytics = (
  filters: UserAnalyticsFilters
): UseQueryResult<UserAnalyticsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['marketing-user-analytics', filters],
    queryFn: async () => {
      try {
        const { data } = await axios.get<UserAnalyticsResponse>(
          `${BASE_URL}/analytics/user_analytics/`,
          {
            params: filters,
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user analytics';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
        throw new Error(errorMessage);
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!filters.user_id // Only fetch when user_id is provided
  });
};