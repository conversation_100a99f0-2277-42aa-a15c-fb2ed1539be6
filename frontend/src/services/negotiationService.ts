import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    NegotiationRead,
    NegotiationWrite,
    NegotiationQueryParams,
    NegotiationUpdateRequest,
    NegotiationUIMRead,
    NegotiationUIMWrite,
    NegotiationUIMUpdateRequest
} from '@/type/negotiationTypes';
import { AdjusterContact } from '@/type/case-management/commonTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch negotiations list
 */
export const useNegotiationsQuery = (caseId: string, defendantId: number | null) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['negotiations', caseId, defendantId],
        queryFn: async () => {
            const params: NegotiationQueryParams = {};
            if (defendantId) {
                params.defendant = defendantId;
            }

            const { data } = await axios.get<NegotiationRead[]>(
                `${BASE_URL}/cases/${caseId}/negotiations/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create a negotiation
 */
export const useCreateNegotiationMutation = (caseId: string): UseMutationResult<
    NegotiationRead,
    Error,
    NegotiationWrite
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: NegotiationWrite) => {
            const response = await axios.post<NegotiationRead>(
                `${BASE_URL}/cases/${caseId}/negotiations/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['negotiations', caseId] });
            toast({
                title: "Success",
                description: "Negotiation created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch a single negotiation
 */
export const useNegotiationQuery = (
    caseId: string,
    negotiationId: string
): UseQueryResult<NegotiationRead> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['negotiation', caseId, negotiationId],
        queryFn: async () => {
            const { data } = await axios.get<NegotiationRead>(
                `${BASE_URL}/cases/${caseId}/negotiations/${negotiationId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId && !!negotiationId,
    });
};

/**
 * Custom hook to update a negotiation
 */
export const useUpdateNegotiationMutation = (
    caseId: string,
    negotiationId: number
): UseMutationResult<NegotiationRead, Error, NegotiationUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: NegotiationUpdateRequest) => {
            const response = await axios.patch<NegotiationRead>(
                `${BASE_URL}/cases/${caseId}/negotiations/${negotiationId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['negotiations', caseId] });
            toast({
                title: "Success",
                description: "Negotiation updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a negotiation
 */
export const useDeleteNegotiationMutation = (
    caseId: string,
    negotiationId: number
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/negotiations/${negotiationId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['negotiations', caseId] });
            toast({
                title: "Success",
                description: "Negotiation deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch defendant adjusters
 */
export const useDefendantAdjustersQuery = (
    caseId: string,
    defendantId?: string,
    assigned_only?: boolean,
    options?: { enabled?: boolean }
): UseQueryResult<AdjusterContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendantAdjusters', caseId, defendantId],
        queryFn: async () => {
            if (!defendantId) {
                throw new Error('Defendant ID is required');
            }
            
            const params: { assigned_only?: boolean } = {};
            if (assigned_only !== undefined) {
                params.assigned_only = assigned_only;
            }
            
            const { data } = await axios.get<AdjusterContact[]>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/list-all-adjusters/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false && !!caseId && !!defendantId,
    });
};

/**
 * Custom hook to fetch UIM negotiations list
 */
export const useUIMNegotiationsQuery = (caseId: string, clientInsuranceId: number | null) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['negotiations-uim', caseId, clientInsuranceId],
        queryFn: async () => {
            const params: NegotiationQueryParams = {};
            if (clientInsuranceId) {
                params.client_insurance = clientInsuranceId;
            }

            const { data } = await axios.get<NegotiationUIMRead[]>(
                `${BASE_URL}/cases/${caseId}/negotiations-uim/`,
                {
                    params,
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create a UIM negotiation
 */
export const useCreateUIMNegotiationMutation = (caseId: string): UseMutationResult<
    NegotiationUIMRead,
    Error,
    NegotiationUIMWrite
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: NegotiationUIMWrite) => {
            const response = await axios.post<NegotiationUIMRead>(
                `${BASE_URL}/cases/${caseId}/negotiations-uim/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['negotiations-uim', caseId] });
            toast({
                title: "Success",
                description: "UIM Negotiation created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a UIM negotiation
 */
export const useUpdateUIMNegotiationMutation = (
    caseId: string,
    negotiationId: number
): UseMutationResult<NegotiationUIMRead, Error, NegotiationUIMUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: NegotiationUIMUpdateRequest) => {
            const response = await axios.patch<NegotiationUIMRead>(
                `${BASE_URL}/cases/${caseId}/negotiations-uim/${negotiationId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['negotiations-uim', caseId] });
            toast({
                title: "Success",
                description: "UIM Negotiation updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a UIM negotiation
 */
export const useDeleteUIMNegotiationMutation = (
    caseId: string,
    negotiationId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/negotiations-uim/${negotiationId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['negotiations-uim', caseId] });
            toast({
                title: "Success",
                description: "UIM Negotiation deleted successfully",
                variant: "default"
            });
        }
    });
};
