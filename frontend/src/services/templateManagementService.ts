import { useMutation, UseMutationResult, useQueryClient, useQuery } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { setTemplatesLoading, setTemplatesError } from '@/store/slices/templatesSlice';
import React from 'react';
import { FormValues } from '@/app/organization/components/RenderTemplateModal';

// Types
export interface Template {
    id: number;
    name: string;
    description: string;
    file_content: string;
    file_name: string;
    file_size: number;
    context_type: string;
    created_at: string;
    updated_at: string;
    use_in_case: boolean;
    use_in_lead: boolean;
}

export interface TemplateFilters {
    context_type?: string;
    search?: string;
}

export interface ListResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
}

interface RequiredVariablesResponse {
    required_variables: string[];
    context_type: string;
    template_id: number;
}

interface RenderTemplateRequest {
    templateId: number;
    variables?: FormValues;
    name: string;
    case_id?: string;
    lead_id?: string;
    defendant_id?: string;
    defendant_insurance_id?: string;
    client_id?: string;
    client_health_insurance_id?: string;
    client_insurance_id?: string;
    other_party_id?: string;
    other_plaintiff_id?: string;
    witness_id?: string;
    expert_witness_id?: string;
    treatment_provider_id?: string;
}

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/organization-management/templates';

/**
 * Hook to fetch templates list with filters
 */
export const useTemplatesListQuery = (filters: TemplateFilters = {}) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    return useQuery<Template[], Error>({
        queryKey: ['templates', filters],
        queryFn: async () => {
            const response = await axios.get<Template[]>(`${BASE_URL}/`, {
                params: filters,
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        }
    });
};

/**
 * Hook to create a new template
 */
export const useCreateTemplateMutation = (): UseMutationResult<Template, Error, FormData> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (formData: FormData) => {
            const response = await axios.post<Template>(`${BASE_URL}/`, formData, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (!response.data || !response.data.id) {
                throw new Error('Invalid response from server');
            }

            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['templates'] });
            toast({
                title: "Success",
                description: `Template "${data.name}" created successfully`,
            });
        }
    });
};

/**
 * Hook to update a template
 */
export const useUpdateTemplateMutation = (): UseMutationResult<Template, Error, { id: number; formData: FormData }> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async ({ id, formData }) => {
            const response = await axios.put<Template>(`${BASE_URL}/${id}/`, formData, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    'Content-Type': 'multipart/form-data',
                }
            });
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['templates'] });
            toast({
                title: "Success",
                description: "Template updated successfully",
            });
        }
    });
};

/**
 * Hook to delete a template
 */
export const useDeleteTemplateMutation = (): UseMutationResult<void, Error, number> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (templateId: number) => {
            await axios.delete(`${BASE_URL}/${templateId}/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['templates'] });
            toast({
                title: "Success",
                description: "Template deleted successfully",
            });
        }
    });
};

/**
 * Hook to download a template
 */
export const useDownloadTemplateMutation = (): UseMutationResult<Blob, Error, number> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (templateId: number) => {
            const response = await axios.get(`${BASE_URL}/${templateId}/download/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                },
                responseType: 'blob'
            });
            return response.data;
        }
    });
};

/**
 * Hook to fetch required variables for a template
 */
export const useTemplateRequiredVarsMutation = (): UseMutationResult<RequiredVariablesResponse, Error, number> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (templateId: number) => {
            const response = await axios.get<RequiredVariablesResponse>(
                `${BASE_URL}/${templateId}/required_variables/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                    }
                }
            );
            return response.data;
        }
    });
};

/**
 * Hook to render and download template
 */
export const useRenderTemplateMutation = (): UseMutationResult<{data:Blob, file_name:string}, Error, RenderTemplateRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async ({ 
            templateId, 
            name, 
            case_id, 
            lead_id, 
            defendant_id,
            defendant_insurance_id, 
            client_id, 
            client_health_insurance_id, 
            client_insurance_id,
            other_party_id,
            other_plaintiff_id,
            witness_id,
            expert_witness_id,
            treatment_provider_id,
            variables
        }) => {
            const payload = {
                name,
                template_id: templateId,
                ...(case_id && {
                    case_id,
                    use_in_case: true
                }),
                ...(lead_id && {
                    lead_id,
                    use_in_lead: true
                }),
                ...(defendant_id && { defendant_id }),
                ...(defendant_insurance_id && { defendant_insurance_id }),
                ...(client_id && { client_id }),
                ...(client_health_insurance_id && { client_health_insurance_id }),
                ...(client_insurance_id && { client_insurance_id }),
                ...(other_party_id && { other_party_id }),
                ...(other_plaintiff_id && { other_plaintiff_id }),
                ...(witness_id && { witness_id }),
                ...(expert_witness_id && { expert_witness_id }),
                ...(treatment_provider_id && { treatment_provider_id }),
                ...(variables && { variables }),
            };

            const response = await axios.post(
                `${BASE_URL}/${templateId}/replace_template_variables/`,
                payload,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    },
                    responseType: 'blob'
                }
            );

            if (!(response.data instanceof Blob) || response.data.size === 0) {
                throw new Error('Invalid response format');
            }

            const file_name = response.headers['content-disposition'].split('filename=')[1];
            return {
                data: response.data,
                file_name: file_name
            };
        }
    });
};

/**
 * Custom hook that combines Redux state management with template list query
 */
export const useTemplatesWithRedux = (filters: TemplateFilters = {}) => {
    const dispatch = useDispatch();
    const query = useTemplatesListQuery(filters);

    React.useEffect(() => {
        dispatch(setTemplatesLoading(query.isLoading));

        if (query.error) {
            dispatch(setTemplatesError(query.error.message));
        } else {
            dispatch(setTemplatesError(null));
        }
    }, [query.data, query.error, query.isLoading, dispatch]);

    return query;
};
