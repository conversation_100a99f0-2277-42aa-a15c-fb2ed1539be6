import { useQuery, useMutation, useQueryClient, useInfiniteQuery, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
  Lead,
  Document,
  DocumentUpload,
  LeadActivitiesResponse,
  LeadEvaluation,
  InjuryAssessmentUpdate,
  IncidentInfoUpdate,
  LeadQueryParams
} from '@/type/lead/leadTypes';
import { LeadDocument } from '@/type/leadDocumentTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/lead-management';
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);

export const useLead = (leadId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['lead', leadId],
    queryFn: async () => {
      const { data } = await axios.get<Lead>(
        `${BASE_URL}/leads/${leadId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!leadId
  });
};

// Document Management Hooks
export const useUploadDocument = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: DocumentUpload) => {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('document_type', data.document_type);
      formData.append('lead', data.lead);
      if (data.notes) formData.append('notes', data.notes);

      const { data: response } = await axios.post<Document>(
        `${BASE_URL}/documents/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      toast({
        title: "Success",
        description: "Document uploaded successfully",
        variant: "default"
      });
    }
  });
};

export const useGetDocumentDownloadUrl = (documentId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['documentDownloadUrl', documentId],
    queryFn: async () => {
      const { data } = await axios.get<{ url: string }>(
        `${BASE_URL}/documents/${documentId}/download_url/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!documentId
  });
};

export const useLeadDocuments = (leadId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['lead-documents', leadId],
    queryFn: async () => {
      if (!leadId) throw new Error('Lead ID is required');
      const { data } = await axios.get<LeadDocument[]>(
        `${BASE_URL}/leads/${leadId}/documents/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!leadId,
  });
};

export const useLeadActivities = (leadId: string, searchQuery?: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useInfiniteQuery<LeadActivitiesResponse, Error>({
    queryKey: ['lead-activities', leadId, searchQuery],
    initialPageParam: 1,
    queryFn: async ({ pageParam = 1 }) => {
      if (!leadId) throw new Error('Lead ID is required');
      const { data } = await axios.get<LeadActivitiesResponse>(
        `${BASE_URL}/leads/${leadId}/activities/`,
        {
          params: { 
            page: pageParam,
            search_query: searchQuery
          },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    getNextPageParam: (lastPage: LeadActivitiesResponse) => {
      if (lastPage.next) {
        const nextUrl = new URL(lastPage.next);
        return nextUrl.searchParams.get('page');
      }
      return undefined;
    },
    enabled: !!leadId,
  });
};

export const useLeadEvaluation = (leadId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['lead-evaluation', leadId],
    queryFn: async () => {
      if (!leadId) throw new Error('Lead ID is required');
      const { data } = await axios.get<LeadEvaluation>(
        `${BASE_URL}/leads/${leadId}/evaluation_details/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!leadId,
  });
};

export const useUpdateEvaluation = (leadId: string) => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LeadEvaluation) => {
      const response = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/update_evaluation/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Evaluation information updated successfully",
        variant: "default"
      });
    }
  });
};

export const useUpdateInjuryAssessment = (leadId: string) => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: InjuryAssessmentUpdate) => {
      const response = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/injury_assessment/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Injury assessment updated successfully",
        variant: "default"
      });
    }
  });
};

export const useUpdateIncidentInformation = (leadId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: IncidentInfoUpdate) => {
      const response = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/incident_info/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    }
  });
};

// Add this interface near other interfaces
interface LeadActivityResponse {
  active_leads: {
    leads: Lead[];
    total: number;
  };
  low_activity_leads: {
    leads: Lead[];
    total: number;
  };
  no_activity_leads: {
    leads: Lead[];
    total: number;
  };
}

/**
 * Custom hook to fetch recent leads from the API
 * @param {LeadQueryParams} params - Query parameters for filtering leads
 * @returns {UseQueryResult<LeadActivityResponse>} Query result containing leads activity data
 */
export const useRecentLeadsQuery = (params: LeadQueryParams = {}, options?: { enabled?: boolean }): UseQueryResult<LeadActivityResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['recentLeads', params],
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const { data } = await axios.get<LeadActivityResponse>(
        `${BASE_URL}/leads/recent_leads/`,
        {
          params,
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled !== false && !!accessToken,
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
};
