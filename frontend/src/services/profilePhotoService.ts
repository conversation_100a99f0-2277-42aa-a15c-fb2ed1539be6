import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { AxiosError } from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/marketing-management';

// Response types
interface UploadProfilePhotoResponse {
  message: string;
  profile_photo: string;
  profile_photo_url: string;
}

interface DownloadProfilePhotoResponse {
  url: string;
}

/**
 * Hook for uploading a profile photo for a partner contact
 */
export const useUploadProfilePhoto = (partnerId: number) => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post<UploadProfilePhotoResponse>(
        `${BASE_URL}/partners/${partnerId}/upload_profile_photo/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate partner queries to refresh the UI with the new photo
      queryClient.invalidateQueries({ queryKey: ['partners'] });

      toast({
        title: 'Success',
        description: 'Profile photo uploaded successfully',
        variant: 'default',
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload profile photo';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for getting a download URL for a partner's profile photo
 */
export const useDownloadProfilePhoto = (partnerId: number, hasProfilePhoto: boolean, expiration: number = 3600) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['profile-photo', partnerId],
    queryFn: async () => {
      try {
        const { data } = await axios.get<DownloadProfilePhotoResponse>(
          `${BASE_URL}/partners/${partnerId}/download_profile_photo/`,
          {
            params: { expiration },
            headers: { Authorization: `Bearer ${accessToken}` },
          }
        );
        return data;
      } catch (error) {
        // Don't show toast for 404 errors as they're expected when a contact doesn't have a photo
        const axiosError = error as AxiosError;
        if (axiosError.response?.status !== 404) {
          const errorMessage = axiosError.message || 'Failed to get profile photo URL';
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
        }
        throw new Error('No profile photo available');
      }
    },
    // Only enable the query if the partner has a profile photo
    enabled: !!partnerId && hasProfilePhoto,
    staleTime: (expiration * 1000) / 2, // Set stale time to half the expiration time
    retry: false, // Don't retry failed requests
  });
};
