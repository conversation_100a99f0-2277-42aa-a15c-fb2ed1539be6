import axios from '@/lib/axios';
import { cookies } from 'next/headers';
import type { CaseFullDetails } from '@/type/dashboard';

interface serverAPIBody {
  case_id: string;
}

// Generic server-side API call function
async function serverApi<T>(
  endpoint: string,
  options?: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: serverAPIBody;
  }
) {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get('accessToken')?.value;

  if (!accessToken) {
    return null;
  }

  try {
    const { data } = await axios<T>({
      url: `${process.env.NEXT_PUBLIC_DJANGO_URL}${endpoint}`,
      method: options?.method || 'GET',
      data: options?.body,
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    return data;
  } catch (error) {
    console.error('Server API Error:', error);
    return null;
  }
}

// Case specific API calls
export async function getCaseDetails(caseId: string) {
  return serverApi<CaseFullDetails>(`/case-management/cases/${caseId}/full_details/`);
}

// Add other server-side API calls here
// Example:
// export async function getOrganizationDetails(orgId: string) {
//   return serverApi<OrganizationType>(`/organizations/${orgId}/`);
// } 