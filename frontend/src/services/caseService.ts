// src/services/caseService.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import {
  CaseResponse,
  CaseQueryParams,
  PaginatedResponse,
  CreateCaseFormData,
  CaseFullDetails,
  PlaintiffResponse,
  PlaintiffCreate,
  MetadataResponse,
  MetadataUpdate,
  MetadataCreate,
  PlaintiffUpdate,
  PolicyInfoCreate,
  PolicyInfoUpdate,
  PolicyInfoResponse,
  DefendantResponse,
  DefendantCreate,
  DefendantUpdate,
  RecipientCreate,
  RecipientResponse,
  RecipientUpdate,
  EconomicImpactCreate,
  EconomicImpactUpdate,
  EconomicImpactResponse,
  NonEconomicImpactCreate,
  NonEconomicImpactUpdate,
  NonEconomicImpactResponse,
  DiscoveryWorkflowRequest,
  DemandGenerationRequestResponse,
  ValidationWorkflowRequest,
  CaseStatisticsResponse,
  AIEditRequest,
  AIEditResponse,
  TransformDocDataResponse,
  CaseActivityResponse
} from '@/type/dashboard';
import { useDispatch, useSelector } from 'react-redux';
import { setCases, setGlobalSearchResults, setStatistics } from '@/store/slices/caseSlice';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/case-management';
const DOC_BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/document-management';
const API_URL = process.env.NEXT_PUBLIC_API_URL + '/api/chat_with_case/v1';
const TRANSFORM_DOC_DATA_URL = process.env.NEXT_PUBLIC_API_URL + '/api/validation_ai/v1/transform-doc-data';
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
interface DocumentMetadata {
  id: string;
  status: string;
}

// Get all cases
export const useCases = (params: CaseQueryParams = {}, isOrganizationCases: boolean = false) => {
  const dispatch = useDispatch();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  return useQuery({
    queryKey: ['cases', isOrganizationCases, params],
    queryFn: async () => {
      const endpoint = 'organization_cases';
      const { data } = await axios.get<PaginatedResponse<CaseResponse>>(
        `${BASE_URL}/cases/${endpoint}/`,
        {
          params,
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      dispatch(setCases(data.results));
      return data;
    }
  });
};


export const useCasesGlobal = (
  params: CaseQueryParams = {},
  isOrganizationCases: boolean = false,
  options?: { enabled?: boolean }
) => {
  const dispatch = useDispatch();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  return useQuery({
    queryKey: ['cases-global-search', isOrganizationCases, params],
    queryFn: async () => {
      const endpoint = 'organization_global_cases';
      const { data } = await axios.get<PaginatedResponse<CaseResponse>>(
        `${BASE_URL}/cases/${endpoint}/`,
        {
          params,
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      // Store results in a separate Redux state to avoid interfering with the cases page
      if (data.results) {
        dispatch(setGlobalSearchResults(data.results));
      }
      return data;
    },
    enabled: options?.enabled !== false && !!accessToken,
  });
};

export const useRecentCases = (params: CaseQueryParams = {}, options?: { enabled?: boolean }) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['recentCases', params],
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const { data } = await axios.get<CaseActivityResponse>(
        `${BASE_URL}/cases/recent_cases/`,
        {
          params,
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled !== false && !!accessToken,
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
};


// Get a single case by caseId
export const useCase = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  return useQuery({
    queryKey: ['case', caseId],
    queryFn: async () => {
      const { data } = await axios.get<CaseResponse>(
        `${BASE_URL}/cases/${caseId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId
  });
};

// Get case statistics
export const useCaseStatistics = () => {
  const dispatch = useDispatch();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  return useQuery({
    queryKey: ['caseStatistics'],
    queryFn: async () => {
      const { data } = await axios.get<CaseStatisticsResponse>(
        `${BASE_URL}/cases/user_case_statistics`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      dispatch(setStatistics(data));
      return data;
    }
  });
};

// Create a new case
export const useCreateCase = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (caseData: Partial<CreateCaseFormData>) => {
      const { data } = await axios.post<CaseResponse>(
        `${BASE_URL}/cases/`,
        caseData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cases'] });
      queryClient.invalidateQueries({ queryKey: ['caseStatistics'] });
    }
  });
};

// Update a case
export const useUpdateCase = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...caseData }: Partial<CaseResponse> & { id: string }) => {
      const { data } = await axios.put<CaseResponse>(
        `${BASE_URL}/cases/${id}/`,
        caseData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['cases'] });
      queryClient.invalidateQueries({ queryKey: ['case', data.id] });
    }
  });
};

// Delete a case
export const useDeleteCase = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (id: string) => {
      await axios.delete(
        `${BASE_URL}/cases/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cases'] });
      queryClient.invalidateQueries({ queryKey: ['caseStatistics'] });
    }
  });
};

// Get full case details
export const useFullCaseDetails = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  return useQuery({
    queryKey: ['case', caseId, 'fullDetails'],
    queryFn: async () => {
      const { data } = await axios.get<CaseFullDetails>(
        `${BASE_URL}/cases/${caseId}/full_details/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId
  });
};

// Create a plaintiff
export const useCreatePlaintiff = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: PlaintiffCreate) => {
      const { data: response } = await axios.post<PlaintiffResponse>(
        `${BASE_URL}/plaintiffs/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Update a plaintiff
export const useUpdatePlaintiff = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: PlaintiffUpdate & { id: number }) => {
      const { data: response } = await axios.patch<PlaintiffResponse>(
        `${BASE_URL}/plaintiffs/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Delete multiple plaintiffs
export const useDeletePlaintiffs = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, plaintiffIds }: { caseId: string; plaintiffIds: number[] }) => {
      await axios.delete(
        `${BASE_URL}/plaintiffs/bulk_delete/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          params: { case_id: caseId },
          data: { plaintiff_ids: plaintiffIds }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Add these mutations after the existing ones
export const useCreateMetadata = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: MetadataCreate) => {
      const { data: response } = await axios.post<MetadataResponse>(
        `${BASE_URL}/case-metadata/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useUpdateMetadata = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: MetadataUpdate & { id: number }) => {
      const { data: response } = await axios.patch<MetadataResponse>(
        `${BASE_URL}/case-metadata/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Create policy info
export const useCreatePolicyInfo = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: PolicyInfoCreate) => {
      const { data: response } = await axios.post<PolicyInfoResponse>(
        `${BASE_URL}/case-policy-info/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Update policy info
export const useUpdatePolicyInfo = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: PolicyInfoUpdate & { id: number }) => {
      const { data: response } = await axios.patch<PolicyInfoResponse>(
        `${BASE_URL}/case-policy-info/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Add these mutations after the existing ones
// Create a defendant
export const useCreateDefendant = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: DefendantCreate) => {
      const { data: response } = await axios.post<DefendantResponse>(
        `${BASE_URL}/case-defendants/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Update a defendant
export const useUpdateDefendant = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: DefendantUpdate & { id: string }) => {
      const { data: response } = await axios.patch<DefendantResponse>(
        `${BASE_URL}/case-defendants/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Delete a defendant
export const useDeleteDefendant = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (id: string) => {
      await axios.delete(
        `${BASE_URL}/case-defendants/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

// Add these mutations after the existing ones
export const useCreateRecipient = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: RecipientCreate) => {
      const { data: response } = await axios.post<RecipientResponse>(
        `${BASE_URL}/case-recipients/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useUpdateRecipient = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: RecipientUpdate & { id: number }) => {
      const { data: response } = await axios.patch<RecipientResponse>(
        `${BASE_URL}/case-recipients/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useDeleteRecipient = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (id: number) => {
      await axios.delete(
        `${BASE_URL}/case-recipients/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useCreateEconomicImpact = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: EconomicImpactCreate) => {
      const { data: response } = await axios.post<EconomicImpactResponse>(
        `${BASE_URL}/case-economic-impact/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useUpdateEconomicImpact = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: EconomicImpactUpdate & { id: number }) => {
      const { data: response } = await axios.patch<EconomicImpactResponse>(
        `${BASE_URL}/case-economic-impact/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useCreateNonEconomicImpact = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: NonEconomicImpactCreate) => {
      const { data: response } = await axios.post<NonEconomicImpactResponse>(
        `${BASE_URL}/case-non-economic-impact/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useUpdateNonEconomicImpact = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ id, ...data }: NonEconomicImpactUpdate & { id: number }) => {
      const { data: response } = await axios.patch<NonEconomicImpactResponse>(
        `${BASE_URL}/case-non-economic-impact/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['case'] });
    }
  });
};

export const useAntiColossusQuestions = () => {
  // const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: DiscoveryWorkflowRequest) => {
      const params = new URLSearchParams({
        case_id: data.case_id,
        demand_letter_filenames: data.demand_letter_filenames,
        user_prompt: data.user_prompt
      });

      const { data: response } = await axios.get(
        `${API_URL}/anti-colossus-workflow?${params.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ['antiColossus', variables.case_id] });
      toast({
        title: "Success",
        description: "Anti-Colossus questions generated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error in negotiations workflow:', error);
      toast({
        title: "Error",
        description: "Failed to generate discovery questions",
        variant: "destructive"
      });
    }
  });
};

export const useDemandGenerationRequest = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['demandGenerationRequest', caseId],
    queryFn: async () => {
      const { data } = await axios.get<DemandGenerationRequestResponse>(
        `${BASE_URL}/demand-generation-requests/get_by_case/`,
        {
          params: { case: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId
  });
};

export const useSubmitForDemandGeneration = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: number) => {
      const { data } = await axios.post<DemandGenerationRequestResponse>(
        `${BASE_URL}/demand-generation-requests/${id}/submit_for_demand_generation/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['demandGenerationRequest'] });
      toast({
        title: "Success",
        description: "Demand generation request submitted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error submitting demand generation request:', error);
      toast({
        title: "Error",
        description: "Failed to submit demand generation request",
        variant: "destructive"
      });
    }
  });
};

export const useSubmitForReview = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, reviewText }: { id: number; reviewText: string }) => {
      const { data } = await axios.post<DemandGenerationRequestResponse>(
        `${BASE_URL}/demand-generation-requests/${id}/submit_for_review/`,
        { review_text: reviewText },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['demandGenerationRequest'] });
      toast({
        title: "Success",
        description: "Review submitted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Error submitting review:', error);
      toast({
        title: "Error",
        description: "Failed to submit review",
        variant: "destructive"
      });
    }
  });
};

export const useDemandValidation = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: ValidationWorkflowRequest) => {
      const params = new URLSearchParams({
        case_id: data.case_id,
        demand_letter_filenames: data.demand_letter_filenames,
        validation_type: data.validation_type,
        user_prompt: data.user_prompt || ''
      });

      const { data: response } = await axios.get(
        `${API_URL}/validate-demand-letter?${params.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Demand letter validation completed",
        variant: "default"
      });
    }
  });
};

// Add this interface if not already defined elsewhere
interface IndexCaseInfo {
  case_id: string;
  case_with_chat_filenames: string[];
  case_name: string;
}

// Add this new hook
export const useIndexCaseFromS3 = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseInfo: IndexCaseInfo) => {
      const { data } = await axios.post(
        `${API_URL}/index-a-case-from-s3`,
        {
          case_id: caseInfo.case_id,
          case_with_chat_filenames: caseInfo.case_with_chat_filenames,
          case_name: caseInfo.case_name
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Case indexed successfully",
        variant: "default"
      });
    }
  });
};

// Add this new hook
export const useUpdateCaseWithChatProcessingStatus = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseId: string) => {
      const { data } = await axios.post<DocumentMetadata[]>(
        `${DOC_BASE_URL}/documents/update_case_with_chat_processing_status/`,
        { case_id: caseId },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return { data, caseId };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Case chat processing status updated successfully",
        variant: "default"
      });
    }
  });
};


// Add this new hook
export const useGenerateEdit = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (request: AIEditRequest) => {
      const { data } = await axios.post<AIEditResponse>(
        `${API_URL}/generate-edit`,
        {
          case_id: request.caseId,
          section_type: request.sectionType,
          section_id: request.sectionId,
          prompt: request.prompt,
          current_content: request.currentContent
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Edit generated successfully",
        variant: "default"
      });
    }
  });
};

export const useTransformDocDataIntroduction = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseId: string) => {
      const { data } = await axios.get<TransformDocDataResponse>(
        `${TRANSFORM_DOC_DATA_URL}/introduction`,
        {
          params: { case_id: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Document data transformed successfully",
        variant: "default"
      });
    }
  });
};


export const useTransformDocDataFactsAndLiability = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseId: string) => {
      const { data } = await axios.get<TransformDocDataResponse>(
        `${TRANSFORM_DOC_DATA_URL}/facts-and-liability`,
        {
          params: { case_id: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Document data transformed successfully",
        variant: "default"
      });
    }
  });
};

export const useTransformDocDataLossOfHouseholdServices = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseId: string) => {
      const { data } = await axios.get<TransformDocDataResponse>(
        `${TRANSFORM_DOC_DATA_URL}/loss-of-household-services`,
        {
          params: { case_id: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Document data transformed successfully",
        variant: "default"
      });
    }
  });
};


export const useTransformDocDataLossOfWages = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseId: string) => {
      const { data } = await axios.get<TransformDocDataResponse>(
        `${TRANSFORM_DOC_DATA_URL}/loss-of-wages`,
        {
          params: { case_id: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Document data transformed successfully",
        variant: "default"
      });
    }
  });
};


export const useTransformDocDataPainAndSuffering = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (caseId: string) => {
      const { data } = await axios.get<TransformDocDataResponse>(
        `${TRANSFORM_DOC_DATA_URL}/pain-and-suffering`,
        {
          params: { case_id: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Document data transformed successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Update Case Organization Status
 *
 * Updates the organization status of a case
 *
 * Endpoint: POST /case-management/cases/{case_id}/update_status/
 * Authentication: Required
 */
export const useUpdateOrganizationCaseStatus = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (params: { caseId: string; organizationStatusId: number }) => {
            const { data } = await axios.post(
                `${BASE_URL}/cases/${params.caseId}/update_status/`,
                { organization_status: params.organizationStatusId },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ['case', variables.caseId] });
            toast({
                title: "Success",
                description: "Case status updated successfully",
                variant: "default"
            });
        }
    });
};

interface CaseChecklist {
    id: number;
    item_name: string;
    description: string;
    is_required: boolean;
    is_completed: boolean;
    completed_at: string | null;
    completed_by: string | null;
    order: number;
}

interface CaseChecklistResponse {
    required: CaseChecklist[];
    optional: CaseChecklist[];
}


export const useUpdateCaseOrganizationStatus = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ caseId, organizationStatusId }: { caseId: string; organizationStatusId: number }) => {
            const { data } = await axios.post(
                `${BASE_URL}/cases/${caseId}/update_status/`,
                { organization_status: organizationStatusId },
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ['case', variables.caseId] });
            toast({
                title: "Success",
                description: "Case organization status updated successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to update case organization status',
                variant: "destructive"
            });
        }
    });
};

export const useCaseChecklists = (caseId: string) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['case-checklists', caseId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<CaseChecklistResponse>(
                    `${BASE_URL}/cases/${caseId}/checklists/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                toast({
                    title: "Error",
                    description: error instanceof Error ? error.message : 'Failed to fetch case checklists',
                    variant: "destructive"
                });
                throw error;
            }
        },
        enabled: !!accessToken,
        retry: false
    });
};

export const useUpdateCaseChecklistItem = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ caseId, checklistItemId, isCompleted }: { caseId: string; checklistItemId: number; isCompleted: boolean }) => {
            const { data } = await axios.post(
                `${BASE_URL}/cases/${caseId}/update_checklist_item/`,
                {
                    checklist_item_id: checklistItemId,
                    is_completed: isCompleted
                },
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ['case-checklists', variables.caseId] });
            toast({
                title: "Success",
                description: "Checklist item updated successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : 'Failed to update checklist item',
                variant: "destructive"
            });
        }
    });
};

export const useCaseQuery = (caseId: string) => {
  return useQuery({
    queryKey: ['case', caseId],
    queryFn: () => axios.get(`${BASE_URL}/cases/${caseId}/`).then(res => res.data),
    enabled: !!caseId
  });
};
