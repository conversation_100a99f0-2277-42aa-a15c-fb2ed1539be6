import { useQuery, UseQueryResult, useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { Task, ListResponse, LeadTaskCreate, LeadNote, LeadNoteCreate } from '@/type/lead/leadTaskTypes';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/lead-management/';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);

/**
 * Custom hook to fetch tasks for a lead
 * @param {string} leadId - The ID of the lead
 * @returns {UseQueryResult<ListResponse<Task>>} Query result containing paginated tasks data
 */
export const useLeadTasksQuery = (): UseQueryResult<ListResponse<Task>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['leadTasks'],
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const { data } = await axios.get<ListResponse<Task>>(
        `${BASE_URL}tasks/`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: (failureCount, error) => {
      if (error instanceof Error && error.message === 'Authentication failed') {
        return false;
      }
      return failureCount < RETRY_COUNT;
    },
    staleTime: STALE_TIME,
    enabled: !!accessToken,
  });
};

/**
 * Custom hook to create a new task for a lead
 * @param {string} leadId - The ID of the lead
 * @returns {UseMutationResult} Mutation result for creating a task
 */
export const useCreateLeadTaskMutation = (leadId: string): UseMutationResult<Task, Error, LeadTaskCreate> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  console.log('useCreateLeadTaskMutation', leadId);

  return useMutation({
    mutationFn: async (taskData: LeadTaskCreate) => {
      const { data } = await axios.post<Task>(
        `${BASE_URL}tasks/`,
        taskData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadTasks'] });
      toast({
        title: "Success",
        description: "Task created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a task
 * @param {string} leadId - The ID of the lead
 * @returns {UseMutationResult} Mutation result for deleting a task
 */
export const useDeleteLeadTaskMutation = (leadId: string): UseMutationResult<void, Error, string> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  console.log('useDeleteLeadTaskMutation', leadId);

  return useMutation({
    mutationFn: async (taskId: string) => {
      await axios.delete(
        `${BASE_URL}tasks/${taskId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadTasks'] });
      toast({
        title: "Success",
        description: "Task deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a task
 * @param {string} leadId - The ID of the lead
 * @returns {UseMutationResult} Mutation result for updating a task
 */
export const useUpdateLeadTaskMutation = (leadId: string): UseMutationResult<Task, Error, { taskId: string; data: LeadTaskCreate }> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  console.log('useUpdateLeadTaskMutation', leadId);

  return useMutation({
    mutationFn: async ({ taskId, data }: { taskId: string; data: LeadTaskCreate }) => {
      const { data: responseData } = await axios.patch<Task>(
        `${BASE_URL}tasks/${taskId}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return responseData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadTasks'] });
      toast({
        title: "Success",
        description: "Task updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch notes for a lead
 */
export const useLeadNotesQuery = (leadId: string): UseQueryResult<ListResponse<LeadNote>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['leadNotes', leadId],
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const { data } = await axios.get<ListResponse<LeadNote>>(
        `${BASE_URL}notes/`,
        {
          params: { lead: leadId },
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
    enabled: !!accessToken && !!leadId,
  });
};

/**
 * Custom hook to create a new note
 */
export const useCreateLeadNoteMutation = (leadId: string): UseMutationResult<LeadNote, Error, LeadNoteCreate> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (noteData: LeadNoteCreate) => {
      const { data } = await axios.post<LeadNote>(
        `${BASE_URL}notes/`,
        { ...noteData, lead: parseInt(leadId) },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadNotes', leadId] });
      toast({
        title: "Success",
        description: "Note created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a note
 */
export const useUpdateLeadNoteMutation = (leadId: string): UseMutationResult<LeadNote, Error, { noteId: number; data: Partial<LeadNoteCreate> }> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ noteId, data }) => {
      const { data: responseData } = await axios.patch<LeadNote>(
        `${BASE_URL}notes/${noteId}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return responseData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadNotes', leadId] });
      toast({
        title: "Success",
        description: "Note updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a note
 */
export const useDeleteLeadNoteMutation = (leadId: string): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (noteId: number) => {
      await axios.delete(
        `${BASE_URL}notes/${noteId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadNotes', leadId] });
      toast({
        title: "Success",
        description: "Note deleted successfully",
        variant: "default"
      });
    }
  });
};
