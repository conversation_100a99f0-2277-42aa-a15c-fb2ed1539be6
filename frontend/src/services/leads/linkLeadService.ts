import { useQuery, useMutation, useQuery<PERSON>lient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

// Define types for the linked leads functionality
export interface LinkedLead {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface LinkedLeadsResponse {
  linked_leads: LinkedLead[];
}

export interface CreateLinkedLeadRequest {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
}

export interface LinkLeadRequest {
  target_lead_id: string;
}

// Constants
const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/lead-management';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);

/**
 * Custom hook to fetch linked leads for a specific lead
 * @param {string} leadId - The ID of the lead
 * @returns {UseQueryResult<LinkedLead[]>} Query result containing linked leads data
 */
export const useLinkedLeadsQuery = (leadId: string): UseQueryResult<LinkedLead[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['linkedLeads', leadId],
    queryFn: async () => {
      if (!leadId) throw new Error('Lead ID is required');

      const { data } = await axios.get<LinkedLeadsResponse>(
        `${BASE_URL}/leads/${leadId}/linked_leads/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!leadId,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new linked lead
 * @param {string} leadId - The ID of the source lead
 * @returns {UseMutationResult<LinkedLead, Error, CreateLinkedLeadRequest>} Mutation result for creating a linked lead
 */
export const useCreateLinkedLeadMutation = (leadId: string): UseMutationResult<
  LinkedLead,
  Error,
  CreateLinkedLeadRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (leadData: CreateLinkedLeadRequest) => {
      if (!leadId) throw new Error('Lead ID is required');

      const { data } = await axios.post<LinkedLead>(
        `${BASE_URL}/leads/${leadId}/create_linked_lead/`,
        leadData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['linkedLeads', leadId] });
      toast({
        title: "Success",
        description: "Linked lead created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to link an existing lead
 * @param {string} leadId - The ID of the source lead
 * @returns {UseMutationResult<LinkedLead, Error, LinkLeadRequest>} Mutation result for linking an existing lead
 */
export const useLinkExistingLeadMutation = (leadId: string): UseMutationResult<
  LinkedLead,
  Error,
  LinkLeadRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (linkData: LinkLeadRequest) => {
      if (!leadId) throw new Error('Lead ID is required');

      const { data } = await axios.post<LinkedLead>(
        `${BASE_URL}/leads/${leadId}/link_lead/`,
        linkData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['linkedLeads', leadId] });
      toast({
        title: "Success",
        description: "Lead linked successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to remove a link between two leads
 * @param {string} leadId - The ID of the source lead
 * @returns {UseMutationResult<void, Error, string>} Mutation result for removing a lead link
 */
export const useRemoveLeadLinkMutation = (leadId: string): UseMutationResult<void, Error, string> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (targetLeadId: string) => {
      if (!leadId) throw new Error('Lead ID is required');
      if (!targetLeadId) throw new Error('Target lead ID is required');

      await axios.delete(
        `${BASE_URL}/leads/${leadId}/unlink_lead/`,
        {
          params: { target_lead_id: targetLeadId },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['linkedLeads', leadId] });
      toast({
        title: "Success",
        description: "Lead link removed successfully",
        variant: "default"
      });
    }
  });
}; 