import { useQ<PERSON>y, UseQueryResult, useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { 
  LeadStatistics, 
  LeadQueryParams, 
  Lead, 
  BasicInfoUpdate, 
  IncidentSummaryUpdate, 
  StatuteUpdate, 
  IncidentInfoUpdate, 
  InjuryAssessmentUpdate, 
  ConvertToCaseResponse, 
  LeadPartialUpdate,
  LeadFormData,
  LeadHealthInsuranceCreateRequest,
  LeadHealthInsuranceUpdateRequest,
  LeadHealthInsurance,
  LeadClientInsuranceCreateRequest,
  LeadClientInsuranceUpdateRequest,
  LeadClientInsurance
} from '@/type/lead/leadTypes';
import { PaginatedResponse } from '@/type/commont';
import { useToast } from '@/hooks/use-toast';
import { setSelectedLead } from '@/store/slices/lead/leadSlice';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/lead-management';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);
// const GC_TIME = Number(process.env.GC_TIME || 30 * 60 * 1000);

/**
 * Custom hook to fetch lead statistics from the API
 * @returns {UseQueryResult<LeadStatistics>} Query result containing lead statistics data
 * - data: Lead statistics when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useLeadStatisticsQuery = (): UseQueryResult<LeadStatistics> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['leadStatistics'],
    queryFn: async () => {
      const { data } = await axios.get<LeadStatistics>(
        `${BASE_URL}/leads/statistics/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch all leads from the API
 * @param {LeadQueryParams} params - Query parameters for filtering leads
 * @returns {UseQueryResult<PaginatedResponse<Lead>>} Query result containing paginated leads data
 * - data: Paginated leads when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useLeadsQuery = (params: LeadQueryParams = {}): UseQueryResult<PaginatedResponse<Lead>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['leads', params],
    queryFn: async () => {
      const { data } = await axios.get<PaginatedResponse<Lead>>(
        `${BASE_URL}/leads/`,
        {
          params,
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to update a lead's status
 * @returns {UseMutationResult<Lead, Error, { id: number; status: string }>} Mutation result containing:
 * - mutate: Function to trigger the status update, accepts object with:
 *   - id: The ID of the lead to update
 *   - status: The new status to set
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated lead data when successful
 * - other standard react-query properties
 */
export const useUpdateLeadStatusMutation = (): UseMutationResult<
  Lead,
  Error,
  { id: string; status: string }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, status }: { id: string; status: string }) => {
      const { data } = await axios.post<Lead>(
        `${BASE_URL}/leads/${id}/update_status/`,
        { status },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: async (_, variables) => {
      await queryClient.invalidateQueries({ queryKey: ['leads'] });
      await queryClient.invalidateQueries({ queryKey: ['lead', variables.id] });
      
      await queryClient.refetchQueries({ 
        queryKey: ['lead', variables.id],
        exact: true 
      });
      
      toast({
        title: "Success",
        description: "Lead status updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to create a new lead
 * @returns {UseMutationResult} Mutation result for creating a lead
 * - mutate: Function to trigger the lead creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - other standard react-query properties
 */
export const useCreateLeadMutation = (): UseMutationResult<Lead, Error, Partial<LeadFormData>> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (leadData: Partial<LeadFormData>) => {
      const { data } = await axios.post<Lead>(
        `${BASE_URL}/leads/`,
        leadData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      toast({
        title: "Success",
        description: "Lead created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch details of a specific lead
 * @param {string} leadId - The ID of the lead to fetch
 * @returns {UseQueryResult<Lead>} Query result containing lead details
 * - data: Lead details when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useLeadDetailsQuery = (leadId: string): UseQueryResult<Lead> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();

  return useQuery({
    queryKey: ['lead', leadId],
    queryFn: async () => {
      if (!leadId) throw new Error('Lead ID is required');
      const { data } = await axios.get<Lead>(
        `${BASE_URL}/leads/${leadId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      dispatch(setSelectedLead(data));
      return data;
    },
    enabled: !!leadId
  });
};

/**
 * Custom hook to update a lead's basic information
 * @param {string} leadId - The ID of the lead to update
 * @returns {UseMutationResult<Lead, Error, BasicInfoUpdate>} Mutation result containing:
 * - mutate: Function to trigger the basic info update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated lead data when successful
 * - other standard react-query properties
 */
export const useUpdateLeadBasicInfoMutation = (leadId: string): UseMutationResult<
  Lead,
  Error,
  BasicInfoUpdate
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (basicInfoData: BasicInfoUpdate) => {
      const { data } = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/basic_info/`,
        basicInfoData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedLead) => {
      dispatch(setSelectedLead(updatedLead));
      queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Basic information updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a lead's incident summary
 * @param {string} leadId - The ID of the lead to update
 * @returns {UseMutationResult<Lead, Error, IncidentSummaryUpdate>} Mutation result containing:
 * - mutate: Function to trigger the incident summary update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated lead data when successful
 * - other standard react-query properties
 */
export const useUpdateLeadIncidentSummaryMutation = (leadId: string): UseMutationResult<
  Lead,
  Error,
  IncidentSummaryUpdate
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (incidentData: IncidentSummaryUpdate) => {
      const { data } = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/incident_summary/`,
        incidentData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedLead) => {
      dispatch(setSelectedLead(updatedLead));
      queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Incident summary updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a lead's statute information
 * @param {string} leadId - The ID of the lead to update
 * @returns {UseMutationResult<Lead, Error, StatuteUpdate>} Mutation result containing:
 * - mutate: Function to trigger the statute update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated lead data when successful
 * - other standard react-query properties
 */
export const useUpdateLeadStatuteMutation = (leadId: string): UseMutationResult<
  Lead,
  Error,
  StatuteUpdate
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (statuteData: StatuteUpdate) => {
      const { data } = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/statute/`,
        statuteData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedLead) => {
      dispatch(setSelectedLead(updatedLead));
      queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Statute information updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a lead's incident information
 * @param {string} leadId - The ID of the lead to update
 * @returns {UseMutationResult<Lead, Error, IncidentInfoUpdate>} Mutation result containing:
 * - mutate: Function to trigger the incident info update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated lead data when successful
 * - other standard react-query properties
 */
export const useUpdateLeadIncidentInfoMutation = (leadId: string): UseMutationResult<
  Lead,
  Error,
  IncidentInfoUpdate
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (incidentInfoData: IncidentInfoUpdate) => {
      const { data } = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/incident_info/`,
        incidentInfoData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedLead) => {
      dispatch(setSelectedLead(updatedLead));
      queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Incident information updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a lead's injury assessment
 * @param {string} leadId - The ID of the lead to update
 * @returns {UseMutationResult<Lead, Error, InjuryAssessmentUpdate>} Mutation result containing:
 * - mutate: Function to trigger the injury assessment update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated lead data when successful
 * - other standard react-query properties
 */
export const useUpdateLeadInjuryAssessmentMutation = (leadId: string): UseMutationResult<
  Lead,
  Error,
  InjuryAssessmentUpdate
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (injuryAssessmentData: InjuryAssessmentUpdate) => {
      const { data } = await axios.patch<Lead>(
        `${BASE_URL}/leads/${leadId}/injury_assessment/`,
        injuryAssessmentData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedLead) => {
      dispatch(setSelectedLead(updatedLead));
      queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Injury assessment updated successfully",
        variant: "default"
      });
    }
  });
};

export const useConvertToCase = (leadId: string) => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      const { data } = await axios.post<ConvertToCaseResponse>(
        `${BASE_URL}/leads/${leadId}/convert_to_case/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return data;
    },
    onSuccess: async (data) => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['cases'] });
      
      toast({
        title: "Success",
        description: "Lead successfully converted to case",
        variant: "default"
      });

      return data.case_id;
    }
  });
};

export const useUpdateLeadMutation = (): UseMutationResult<Lead, Error, LeadPartialUpdate> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: LeadPartialUpdate) => {
      const { data } = await axios.patch<Lead>(
        `${BASE_URL}/leads/${id}/`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedLead) => {
      queryClient.invalidateQueries({ queryKey: ['lead', updatedLead.id] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      toast({
        title: "Success",
        description: "Lead updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch health insurance records for a lead
 * @param {string} leadId - The ID of the lead
 * @returns {UseQueryResult<LeadHealthInsurance[]>} Query result containing:
 * - data: List of health insurance records when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useLeadHealthInsuranceListQuery = (
  leadId: string,
  options?: { enabled?: boolean }
): UseQueryResult<LeadHealthInsurance[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['leadHealthInsurance', leadId],
    queryFn: async () => {
      const { data } = await axios.get<LeadHealthInsurance[]>(
        `${BASE_URL}/health-insurances/`,
        {
          params: { lead_id: leadId },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      console.log('Health Insurance API Response:', data);
      return data;
    },
    enabled: options?.enabled !== false && !!leadId,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a health insurance record
 */
export const useCreateLeadHealthInsuranceMutation = (leadId: string): UseMutationResult<
  LeadHealthInsurance,
  Error,
  LeadHealthInsuranceCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LeadHealthInsuranceCreateRequest) => {
      const response = await axios.post<LeadHealthInsurance>(
        `${BASE_URL}/health-insurances/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadHealthInsurance', leadId] });
      toast({
        title: "Success",
        description: "Health insurance record created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a health insurance record
 */
export const useUpdateLeadHealthInsuranceMutation = (
  leadId: string,
): UseMutationResult<void, Error, LeadHealthInsuranceUpdateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LeadHealthInsuranceUpdateRequest) => {
      await axios.patch<LeadHealthInsurance>(
        `${BASE_URL}/health-insurances/${data.id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadHealthInsurance', leadId] });
      toast({
        title: "Success",
        description: "Health insurance record updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a health insurance record
 */
export const useDeleteLeadHealthInsuranceMutation = (
  leadId: string,
): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (insuranceId: number) => {
      await axios.delete(
        `${BASE_URL}/health-insurances/${insuranceId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadHealthInsurance', leadId] });
      toast({
        title: "Success",
        description: "Health insurance record deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch client insurance records for a lead
 * @param {string} leadId - The ID of the lead
 * @returns {UseQueryResult<LeadClientInsuranceListResponse>} Query result containing:
 * - data: List of insurance records when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useLeadClientInsuranceListQuery = (
  leadId: string,
  options?: { enabled?: boolean }
): UseQueryResult<LeadClientInsurance[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['leadClientInsurance', leadId],
    queryFn: async () => {
      const { data } = await axios.get<LeadClientInsurance[]>(
        `${BASE_URL}/client-insurances/`,
        {
          params: { lead: leadId },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled !== false && !!leadId,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new client insurance record
 * @param {string} leadId - The ID of the lead
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the insurance creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created insurance data when successful
 */
export const useCreateLeadClientInsuranceMutation = (
  leadId: string
): UseMutationResult<LeadClientInsurance, Error, LeadClientInsuranceCreateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LeadClientInsuranceCreateRequest) => {
      const response = await axios.post<LeadClientInsurance>(
        `${BASE_URL}/client-insurances/`,
        { ...data, lead: Number(leadId) },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadClientInsurance', leadId] });
      toast({
        title: "Success",
        description: "Insurance record created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a client insurance record
 * @param {string} leadId - The ID of the lead
 * @param {number} insuranceId - The ID of the insurance record
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the insurance update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated insurance data when successful
 */
export const useUpdateLeadClientInsuranceMutation = (
  leadId: string,
): UseMutationResult<LeadClientInsurance, Error, LeadClientInsuranceUpdateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LeadClientInsuranceUpdateRequest) => {
      const response = await axios.put<LeadClientInsurance>(
        `${BASE_URL}/client-insurances/${data.id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadClientInsurance', leadId] });
      toast({
        title: "Success",
        description: "Insurance record updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a client insurance record
 * @param {string} leadId - The ID of the lead
 * @param {number} insuranceId - The ID of the insurance record to delete
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the insurance deletion
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 */
export const useDeleteLeadClientInsuranceMutation = (
  leadId: string,
): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (insuranceId: number) => {
      await axios.delete(
        `${BASE_URL}/client-insurances/${insuranceId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leadClientInsurance', leadId] });
      toast({
        title: "Success",
        description: "Insurance record deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to replace variables in a DOCX document for a lead
 * @param {string} leadId - The ID of the lead
 * @param {File} document - The DOCX document file to process
 * @returns {UseMutationResult} Mutation result for replacing document variables
 */
export const useReplaceDocxVariablesMutation = (leadId: string): UseMutationResult<
  Blob,
  Error,
  File
> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (document: File) => {
      const formData = new FormData();
      formData.append('document', document);
      formData.append('lead_id', leadId);

      const response = await axios.post(
        `${BASE_URL}/replace-docx-variables/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
          responseType: 'blob', // Important for receiving file response
        }
      );

      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Document processed successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to transfer documents for a lead
 * @param {string} leadId - The ID of the lead
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the document transfer
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - other standard react-query properties
 */
export const useTransferLeadDocumentsMutation = (leadId: string): UseMutationResult<
  void,
  Error,
  void
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.post(
        `${BASE_URL}/leads/${leadId}/transfer_documents/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      toast({
        title: "Success",
        description: "Documents transferred in progress, it may take 2 minutes",
        variant: "default"
      });
    }
  });
};