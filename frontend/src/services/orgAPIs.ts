import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { 
  InsuranceCompany, 
  InsuranceCompanyCreateRequest,
  LawFirmCreateRequest,
  LawFirmResponse,
  LawFirmUpdateRequest,
  AttorneyCreateRequest,
  AttorneyResponse,
  AttorneyUpdateRequest,
  AdjusterContact,
  AdjusterCreateRequest,
  AdjusterResponse,
  AdjusterDetailType,
  MedicalProvider,
  MedicalProviderCreateRequest,
  MedicalProviderContact,
  LienHolderCreateRequest,
  LienHolder,
  SubrogationContact,
  SubrogationContactCreateRequest,
  OrgCostContact,
  OrgCostContactCreateRequest,
  OrgCostContactUpdateRequest,
  ExpertWitnessContact,
  CaseCost,
  CaseCostCreateRequest,
  User,
  SubrogationCompany,
  SubrogationCompanyCreateRequest,
  KPIGoal,
  KPIGoalCreateRequest,
  KPIGoalUpdateRequest,
  KPIGoalBulkCreateRequest
} from '@/type/case-management/orgTypes';

import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);
// const GC_TIME = Number(process.env.GC_TIME || 30 * 60 * 1000);
const KPI_GOALS_BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/organization-management';

/**
 * Custom hook to fetch all insurance companies
 * @returns {UseQueryResult<InsuranceCompany[]>} Query result containing:
 * - data: Array of insurance companies when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useInsuranceCompaniesQuery = (): UseQueryResult<InsuranceCompany[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['insuranceCompanies'],
    queryFn: async () => {
      const { data } = await axios.get<InsuranceCompany[]>(
        `${BASE_URL}/insurance-companies/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Custom hook to create a new insurance company
 * @returns {UseMutationResult<InsuranceCompany, Error, Partial<InsuranceCompany>>} Mutation result containing:
 * - mutate: Function to trigger the insurance company creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created insurance company when successful
 * - other standard react-query properties
 */
export const useCreateInsuranceCompanyMutation = (): UseMutationResult<
  InsuranceCompany,
  Error,
  Partial<InsuranceCompanyCreateRequest>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (insuranceData: Partial<InsuranceCompanyCreateRequest>) => {
      const { data } = await axios.post<InsuranceCompany>(
        `${BASE_URL}/insurance-companies/`,
        insuranceData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['insuranceCompanies'] });
      toast({
        title: "Success",
        description: "Insurance company created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch a single insurance company
 * @param {string} id - The ID of the insurance company
 * @returns {UseQueryResult<InsuranceCompany>} Query result
 */
export const useInsuranceCompanyQuery = (id: string): UseQueryResult<InsuranceCompany> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['insuranceCompany', id],
    queryFn: async () => {
      if (!id) throw new Error('Insurance Company ID is required');
      
      const { data } = await axios.get<InsuranceCompany>(
        `${BASE_URL}/insurance-companies/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!id,
  });
};

/**
 * Custom hook to update an insurance company
 * @param {string} id - The ID of the insurance company
 * @returns {UseMutationResult<InsuranceCompany, Error, Partial<InsuranceCompany>>} Mutation result
 */
export const useUpdateInsuranceCompanyMutation = (id: string): UseMutationResult<
  InsuranceCompany,
  Error,
  Partial<InsuranceCompanyCreateRequest>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (insuranceData: Partial<InsuranceCompanyCreateRequest>) => {
      const { data } = await axios.put<InsuranceCompany>(
        `${BASE_URL}/insurance-companies/${id}/`,
        insuranceData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['insuranceCompanies'] });
      queryClient.invalidateQueries({ queryKey: ['insuranceCompany', id] });
      toast({
        title: "Success",
        description: "Insurance company updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete an insurance company
 * @param {string} id - The ID of the insurance company
 * @returns {UseMutationResult<void, Error, void>} Mutation result
 */
export const useDeleteInsuranceCompanyMutation = (id: string): UseMutationResult<
  void,
  Error,
  void
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.delete(
        `${BASE_URL}/insurance-companies/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['insuranceCompanies'] });
      toast({
        title: "Success",
        description: "Insurance company deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to create a new law firm
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the law firm creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created law firm data when successful
 */
export const useCreateLawFirmMutation = (): UseMutationResult<
  LawFirmResponse,
  Error,
  LawFirmCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LawFirmCreateRequest) => {
      const response = await axios.post<LawFirmResponse>(
        `${BASE_URL}/law-firms/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['lawFirms'] });
      toast({
        title: "Success",
        description: `Law firm "${data.office_name}" created successfully`,
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch all law firms
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<LawFirmResponse[]>} Query result containing:
 * - data: List of law firms when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useLawFirmsQuery = (
  options?: { enabled?: boolean }
): UseQueryResult<LawFirmResponse[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['lawFirms'],
    queryFn: async () => {
      const { data } = await axios.get<LawFirmResponse[]>(
        `${BASE_URL}/law-firms/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled !== false,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to update an existing law firm
 * @param {string} lawFirmId - The ID of the law firm to update
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the law firm update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated law firm data when successful
 */
export const useUpdateLawFirmMutation = (
  lawFirmId: string
): UseMutationResult<LawFirmResponse, Error, LawFirmUpdateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LawFirmUpdateRequest) => {
      const response = await axios.put<LawFirmResponse>(
        `${BASE_URL}/law-firms/${lawFirmId}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['lawFirms'] });
      toast({
        title: "Success",
        description: `Law firm "${data.office_name}" updated successfully`,
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to create a new attorney contact
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the attorney creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created attorney data when successful
 */
export const useCreateAttorneyMutation = (): UseMutationResult<
    AttorneyResponse,
    Error,
    AttorneyCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: AttorneyCreateRequest) => {
            const response = await axios.post<AttorneyResponse>(
                `${BASE_URL}/organization/attorneys/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['attorneys'] });
            toast({
                title: "Success",
                description: `Attorney "${data.first_name} ${data.last_name}" created successfully`,
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all attorneys
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<AttorneyResponse[]>} Query result containing:
 * - data: List of attorneys when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useAttorneysQuery = (
    options?: { enabled?: boolean }
): UseQueryResult<AttorneyResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['attorneys'],
        queryFn: async () => {
            const { data } = await axios.get<AttorneyResponse[]>(
                `${BASE_URL}/organization/attorneys/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false,
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to update an existing attorney
 * @param {string} attorneyId - The ID of the attorney to update
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the attorney update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated attorney data when successful
 */
export const useUpdateAttorneyMutation = (
    attorneyId: string
): UseMutationResult<AttorneyResponse, Error, AttorneyUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: AttorneyUpdateRequest) => {
            const response = await axios.put<AttorneyResponse>(
                `${BASE_URL}/organization/attorneys/${attorneyId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['attorneys'] });
            toast({
                title: "Success",
                description: `Attorney "${data.first_name} ${data.last_name}" updated successfully`,
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all adjuster contacts for a specific insurance company
 * @param {string} insuranceCompanyId - The ID of the insurance company
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<AdjusterContact[]>} Query result containing:
 * - data: List of adjuster contacts when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useAdjusterContactsQuery = (
  insuranceCompanyId: string,
  options?: { enabled?: boolean }
): UseQueryResult<AdjusterContact[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['adjusterContacts', insuranceCompanyId],
    queryFn: async () => {
      const { data } = await axios.get<AdjusterContact[]>(
        `${BASE_URL}/insurance-companies/${insuranceCompanyId}/adjusters/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled && !!insuranceCompanyId,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new adjuster contact
 * @param {string} insuranceCompanyId - The ID of the insurance company
 * @returns {UseMutationResult} Mutation result for creating an adjuster
 */
export const useCreateAdjusterMutation = (
  insuranceCompanyId: string
): UseMutationResult<AdjusterResponse, Error, AdjusterCreateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: AdjusterCreateRequest) => {
      const response = await axios.post<AdjusterResponse>(
        `${BASE_URL}/insurance-companies/${insuranceCompanyId}/adjusters/`,
        {
          ...data,
          insurance_company: Number(insuranceCompanyId)
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['adjusterContacts', insuranceCompanyId] 
      });
      toast({
        title: "Success",
        description: "Adjuster created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update an existing adjuster contact
 * @param {string} insuranceCompanyId - The ID of the insurance company
 * @param {string} adjusterId - The ID of the adjuster to update
 * @returns {UseMutationResult} Mutation result for updating an adjuster
 */
export const useUpdateAdjusterMutation = (
  insuranceCompanyId: string,
  adjusterId: string
): UseMutationResult<AdjusterResponse, Error, AdjusterCreateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: AdjusterCreateRequest) => {
      const response = await axios.put<AdjusterResponse>(
        `${BASE_URL}/insurance-companies/${insuranceCompanyId}/adjusters/${adjusterId}/`,
        {
          ...data,
          insurance_company: Number(insuranceCompanyId)
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['adjusterContacts', insuranceCompanyId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['adjusterDetails', insuranceCompanyId, adjusterId] 
      });
      toast({
        title: "Success",
        description: "Adjuster updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch adjuster details
 * @param {string} insuranceCompanyId - The ID of the insurance company
 * @param {string} adjusterId - The ID of the adjuster to fetch
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should run automatically
 * @returns {UseQueryResult<AdjusterDetailType>} Query result containing adjuster details
 */
export const useAdjusterDetailsQuery = (
  insuranceCompanyId: string,
  adjusterId: string,
  options?: { enabled?: boolean }
): UseQueryResult<AdjusterDetailType> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['adjusterDetails', insuranceCompanyId, adjusterId],
    queryFn: async () => {
      const { data } = await axios.get<AdjusterDetailType>(
        `${BASE_URL}/insurance-companies/${insuranceCompanyId}/adjusters/${adjusterId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled && !!insuranceCompanyId && !!adjusterId,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch all medical providers
 * @returns {UseQueryResult<MedicalProvider[]>} Query result containing:
 * - data: Array of medical providers when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useMedicalProvidersQuery = (searchParams?: {
  name?: string;
  specialty?: string;
  city?: string;
  page?: number;
  page_size?: number;
}): UseQueryResult<{
  count: number;
  results: MedicalProvider[];
}> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['medicalProviders', searchParams],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (searchParams?.name) params.append('company__icontains', encodeURIComponent(searchParams.name));
      if (searchParams?.specialty) params.append('specialties', encodeURIComponent(searchParams.specialty));
      if (searchParams?.city) params.append('contacts__city', encodeURIComponent(searchParams.city));
      if (searchParams?.page) params.append('page', searchParams.page.toString());
      if (searchParams?.page_size) params.append('page_size', searchParams.page_size.toString());

      const { data } = await axios.get<{
        count: number;
        results: MedicalProvider[];
      }>(
        `${BASE_URL}/medical-providers/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          params
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new medical provider
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the medical provider creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created medical provider when successful
 */
export const useCreateMedicalProviderMutation = (): UseMutationResult<
  MedicalProvider,
  Error,
  MedicalProviderCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: MedicalProviderCreateRequest) => {
      const response = await axios.post<MedicalProvider>(
        `${BASE_URL}/medical-providers/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['medicalProviders'] });
      toast({
        title: "Success",
        description: `Medical provider "${data.company}" created successfully`,
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch a single medical provider
 * @param {string} id - The ID of the medical provider
 * @returns {UseQueryResult<MedicalProvider>} Query result
 */
export const useMedicalProviderQuery = (id: string): UseQueryResult<MedicalProvider> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['medicalProvider', id],
    queryFn: async () => {
      if (!id) throw new Error('Medical Provider ID is required');
      
      const { data } = await axios.get<MedicalProvider>(
        `${BASE_URL}/medical-providers/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!id,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to update a medical provider
 * @param {string} id - The ID of the medical provider
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateMedicalProviderMutation = (id: string): UseMutationResult<
  MedicalProvider,
  Error,
  MedicalProviderCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: MedicalProviderCreateRequest) => {
      const response = await axios.put<MedicalProvider>(
        `${BASE_URL}/medical-providers/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['medicalProviders'] });
      queryClient.invalidateQueries({ queryKey: ['medicalProvider', id] });
      toast({
        title: "Success",
        description: `Medical provider "${data.company}" updated successfully`,
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a medical provider
 * @param {string} id - The ID of the medical provider
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteMedicalProviderMutation = (id: string): UseMutationResult<
  void,
  Error,
  void
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.delete(
        `${BASE_URL}/medical-providers/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['medicalProviders'] });
      toast({
        title: "Success",
        description: "Medical provider deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch all medical provider contacts
 * @param {string} medicalProviderId - The ID of the medical provider
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<MedicalProviderContact[]>} Query result
 */
export const useMedicalProviderContactsQuery = (providerId: string, options?: { enabled?: boolean }): UseQueryResult<MedicalProviderContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['medicalProviderContacts', providerId],
        queryFn: async () => {
            if (!providerId) throw new Error('Provider ID is required');
            
            const { data } = await axios.get<MedicalProviderContact[]>(
                `${BASE_URL}/medical-providers/${providerId}/contacts/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled && !!providerId,
    });
};

/**
 * Custom hook to create a medical provider contact
 * @param {string} medicalProviderId - The ID of the medical provider
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateMedicalProviderContactMutation = (providerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Omit<MedicalProviderContact, "id" | "created_at" | "updated_at">) => {
            const response = await axios.post<MedicalProviderContact>(
                `${BASE_URL}/medical-providers/${providerId}/contacts/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['medicalProviderContacts', providerId] });
            toast({
                title: "Success",
                description: "Contact created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a medical provider contact
 * @param {string} medicalProviderId - The ID of the medical provider
 * @param {string} contactId - The ID of the contact to update
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateMedicalProviderContactMutation = (providerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Omit<MedicalProviderContact, "created_at" | "updated_at">) => {
            const response = await axios.put<MedicalProviderContact>(
                `${BASE_URL}/medical-providers/${providerId}/contacts/${data.id}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['medicalProviderContacts', providerId] });
            toast({
                title: "Success",
                description: "Contact updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a medical provider contact
 * @param {string} medicalProviderId - The ID of the medical provider
 * @param {string} contactId - The ID of the contact to delete
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteMedicalProviderContactMutation = (
  medicalProviderId: string,
  contactId: string
): UseMutationResult<void, Error, void> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.delete(
        `${BASE_URL}/medical-provider-contacts/${contactId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['medicalProviderContacts', medicalProviderId] 
      });
      toast({
        title: "Success",
        description: "Medical provider contact deleted successfully",
        variant: "default"
      });
    }
  });
};


/**
 * Custom hook to fetch all lien holders
 * @returns {UseQueryResult<LienHolder[]>} Query result
 */
export const useLienHoldersQuery = (): UseQueryResult<LienHolder[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['lienHolders'],
        queryFn: async () => {
            const { data } = await axios.get<LienHolder[]>(
                `${BASE_URL}/lien-holders/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
    });
};

/**
 * Custom hook to fetch a single lien holder
 * @param {string} id - The ID of the lien holder
 * @returns {UseQueryResult<LienHolder>} Query result
 */
export const useLienHolderQuery = (id: string): UseQueryResult<LienHolder> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['lienHolder', id],
        queryFn: async () => {
            if (!id) throw new Error('Lien holder ID is required');

            const { data } = await axios.get<LienHolder>(
                `${BASE_URL}/lien-holders/${id}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!id,
    });
};

/**
 * Custom hook to create a lien holder
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateLienHolderMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LienHolderCreateRequest) => {
            const response = await axios.post<LienHolder>(
                `${BASE_URL}/lien-holders/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['lienHolders'] });
            toast({
                title: "Success",
                description: "Lien holder created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a lien holder
 * @param {string} id - The ID of the lien holder
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateLienHolderMutation = (id: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LienHolderCreateRequest) => {
            const response = await axios.put<LienHolder>(
                `${BASE_URL}/lien-holders/${id}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['lienHolders'] });
            queryClient.invalidateQueries({ queryKey: ['lienHolder', id] });
            toast({
                title: "Success",
                description: "Lien holder updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a lien holder
 * @param {string} id - The ID of the lien holder
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteLienHolderMutation = (id: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/lien-holders/${id}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['lienHolders'] });
            toast({
                title: "Success",
                description: "Lien holder deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all subrogation contacts
 * @returns {UseQueryResult<SubrogationContact[]>} Query result
 */
export const useSubrogationContactsQuery = (): UseQueryResult<SubrogationContact[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['subrogationContacts'],
    queryFn: async () => {
      const { data } = await axios.get<SubrogationContact[]>(
        `${BASE_URL}/subrogation-contacts/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    }
  });
};

/**
 * Custom hook to create a subrogation contact
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateSubrogationContactMutation = (): UseMutationResult<
  SubrogationContact,
  Error,
  SubrogationContactCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: SubrogationContactCreateRequest) => {
      const response = await axios.post<SubrogationContact>(
        `${BASE_URL}/subrogation-contacts/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subrogationContacts'] });
      toast({
        title: "Success",
        description: "Subrogation contact created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a subrogation contact
 * @param {string} id - The ID of the subrogation contact
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateSubrogationContactMutation = (id: string): UseMutationResult<
  SubrogationContact,
  Error,
  SubrogationContactCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: SubrogationContactCreateRequest) => {
      const response = await axios.put<SubrogationContact>(
        `${BASE_URL}/subrogation-contacts/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subrogationContacts'] });
      toast({
        title: "Success",
        description: "Subrogation contact updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a subrogation contact
 * @param {string} id - The ID of the subrogation contact
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteSubrogationContactMutation = (id: string): UseMutationResult<
  void,
  Error,
  void
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.delete(
        `${BASE_URL}/subrogation-contacts/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subrogationContacts'] });
      toast({
        title: "Success",
        description: "Subrogation contact deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch a single subrogation contact
 * @param {string} id - The ID of the subrogation contact
 * @returns {UseQueryResult<SubrogationContact>} Query result
 */
export const useSubrogationContactQuery = (id: string): UseQueryResult<SubrogationContact> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['subrogationContact', id],
    queryFn: async () => {
      if (!id) throw new Error('Subrogation Contact ID is required');
      
      const { data } = await axios.get<SubrogationContact>(
        `${BASE_URL}/subrogation-contacts/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!id,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch all cost contacts
 * @returns {UseQueryResult<{count: number, next: string | null, previous: string | null, results: OrgCostContact[]}>} Query result with paginated data
 */
export const useCostContactsQuery = (
  searchParams?: { 
    search?: string; 
    contact_type?: string; 
    page?: number; 
    page_size?: number; 
  }
): UseQueryResult<{
  count: number;
  next: string | null;
  previous: string | null;
  results: OrgCostContact[];
}> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['costContacts', searchParams],
        queryFn: async () => {
            let url = `${BASE_URL}/cost-contacts/`;
            
            if (searchParams) {
                const params = new URLSearchParams();
                if (searchParams.search) params.append('search', searchParams.search);
                if (searchParams.contact_type) params.append('contact_type', searchParams.contact_type);
                if (searchParams.page) params.append('page', searchParams.page.toString());
                if (searchParams.page_size) params.append('page_size', searchParams.page_size.toString());
                
                if (params.toString()) {
                    url += `?${params.toString()}`;
                }
            }
            
            const { data } = await axios.get(
                url,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to fetch a specific cost contact by ID
 * @param {string} id - The ID of the cost contact
 * @returns {UseQueryResult<OrgCostContact>} Query result
 */
export const useCostContactQuery = (id: string): UseQueryResult<OrgCostContact> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['costContact', id],
        queryFn: async () => {
            const { data } = await axios.get<OrgCostContact>(
                `${BASE_URL}/cost-contacts/${id}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!id,
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create a cost contact
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateCostContactMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: OrgCostContactCreateRequest) => {
            const response = await axios.post<OrgCostContact>(
                `${BASE_URL}/cost-contacts/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['costContacts'] });
            toast({
                title: "Success",
                description: "Cost contact created successfully",
                variant: "default",
            });
        },
    });
};

/**
 * Custom hook to update a cost contact
 * @param {string} id - The ID of the cost contact to update
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateCostContactMutation = (id: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: OrgCostContactUpdateRequest) => {
            const response = await axios.patch<OrgCostContact>(
                `${BASE_URL}/cost-contacts/${id}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['costContacts'] });
            queryClient.invalidateQueries({ queryKey: ['costContact', id] });
            toast({
                title: "Success",
                description: "Cost contact updated successfully",
                variant: "default",
            });
        },
    });
};

/**
 * Custom hook to delete a cost contact
 * @param {string} id - The ID of the cost contact to delete
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteCostContactMutation = (id: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cost-contacts/${id}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['costContacts'] });
            toast({
                title: "Success",
                description: "Cost contact deleted successfully",
                variant: "default",
            });
        },
    });
};

/**
 * Custom hook to fetch expert witnesses with optional filters
 * @param {Object} options - Query options
 * @param {string} options.search - Optional search term
 * @param {string} options.specialty - Optional specialty
 * @param {string} options.city - Optional city
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<ExpertWitnessContact[]>} Query result containing expert witness data
 */
export const useExpertWitnessesQuery = (
    options?: {
        search?: string;
        specialty?: string;
        city?: string;
        enabled?: boolean;
    }
): UseQueryResult<ExpertWitnessContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['expertWitnesses', options?.search, options?.specialty, options?.city],
        queryFn: async () => {
            const params = new URLSearchParams();
            if (options?.search) params.append('search', options.search);
            if (options?.specialty) params.append('specialty', options.specialty);
            if (options?.city) params.append('city', options.city);

            const { data } = await axios.get<ExpertWitnessContact[]>(
                `${BASE_URL}/expert-witnesses/${params.toString() ? `?${params.toString()}` : ''}`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false,
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to fetch case costs
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseCost[]>} Query result
 */
export const useCaseCostsQuery = (caseId: string): UseQueryResult<CaseCost[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['caseCosts', caseId],
        queryFn: async () => {
            const { data } = await axios.get<{ results: CaseCost[] }>(
                `${BASE_URL}/cases/${caseId}/costs/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data.results;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create a case cost
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateCaseCostMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseCostCreateRequest) => {
            const response = await axios.post<CaseCost>(
                `${BASE_URL}/cases/${caseId}/costs/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseCosts', caseId] });
            toast({
                title: "Success",
                description: "Case cost created successfully",
                variant: "default",
            });
        },
    });
};

/**
 * Custom hook to fetch health insurance liens
 * @returns {UseQueryResult<LienHolder[]>} Query result
 */
export const useHealthInsuranceLiensQuery = (): UseQueryResult<LienHolder[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['healthInsuranceLiens'],
        queryFn: async () => {
            const { data } = await axios.get<LienHolder[]>(
                `${BASE_URL}/lien-holders/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
    });
};

/**
 * Custom hook to create a health insurance lien
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateHealthInsuranceLienMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LienHolderCreateRequest) => {
            const response = await axios.post<LienHolder>(
                `${BASE_URL}/lien-holders/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['healthInsuranceLiens'] });
            toast({
                title: "Success",
                description: "Health insurance lien created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch organization users
 * @returns {UseQueryResult<User[]>} Query result
 */
export const useOrganizationUsersQuery = (): UseQueryResult<User[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['organizationUsers'],
        queryFn: async () => {
            const { data } = await axios.get<User[]>(
                `${BASE_URL}/organization/users/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to fetch all subrogation companies
 * @returns {UseQueryResult<SubrogationCompany[]>} Query result containing:
 * - data: Array of subrogation companies when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useSubrogationCompaniesQuery = (): UseQueryResult<SubrogationCompany[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['subrogationCompanies'],
    queryFn: async () => {
      const { data } = await axios.get<SubrogationCompany[]>(
        `${BASE_URL}/subrogation-companies/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch a single subrogation company
 * @param {string} id - The ID of the subrogation company
 * @returns {UseQueryResult<SubrogationCompany>} Query result
 */
export const useSubrogationCompanyQuery = (id: string): UseQueryResult<SubrogationCompany> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['subrogationCompany', id],
    queryFn: async () => {
      if (!id) throw new Error('Subrogation Company ID is required');
      
      const { data } = await axios.get<SubrogationCompany>(
        `${BASE_URL}/subrogation-companies/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!id,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new subrogation company
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the subrogation company creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created subrogation company when successful
 */
export const useCreateSubrogationCompanyMutation = (): UseMutationResult<
  SubrogationCompany,
  Error,
  SubrogationCompanyCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: SubrogationCompanyCreateRequest) => {
      const response = await axios.post<SubrogationCompany>(
        `${BASE_URL}/subrogation-companies/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['subrogationCompanies'] });
      toast({
        title: "Success",
        description: `Subrogation company "${data.company}" created successfully`,
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a subrogation company
 * @param {string} id - The ID of the subrogation company
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateSubrogationCompanyMutation = (id: string): UseMutationResult<
  SubrogationCompany,
  Error,
  SubrogationCompanyCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: SubrogationCompanyCreateRequest) => {
      const response = await axios.put<SubrogationCompany>(
        `${BASE_URL}/subrogation-companies/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['subrogationCompanies'] });
      queryClient.invalidateQueries({ queryKey: ['subrogationCompany', id] });
      toast({
        title: "Success",
        description: `Subrogation company "${data.company}" updated successfully`,
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a subrogation company
 * @param {string} id - The ID of the subrogation company
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteSubrogationCompanyMutation = (id: string): UseMutationResult<
  void,
  Error,
  void
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.delete(
        `${BASE_URL}/subrogation-companies/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subrogationCompanies'] });
      toast({
        title: "Success",
        description: "Subrogation company deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch all KPI goals
 * @returns {UseQueryResult<KPIGoal[]>} Query result containing:
 * - data: Array of KPI goals when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useKPIGoalsQuery = (): UseQueryResult<KPIGoal[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['kpiGoals'],
    queryFn: async () => {
      const { data } = await axios.get<KPIGoal[]>(
        `${BASE_URL}/kpi-goals/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch a single KPI goal
 * @param {string} id - The ID of the KPI goal
 * @returns {UseQueryResult<KPIGoal>} Query result
 */
export const useKPIGoalQuery = (id: string): UseQueryResult<KPIGoal> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['kpiGoal', id],
    queryFn: async () => {
      if (!id) throw new Error('KPI Goal ID is required');
      
      const { data } = await axios.get<KPIGoal>(
        `${BASE_URL}/kpi-goals/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!id,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new KPI goal
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the KPI goal creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created KPI goal when successful
 */
export const useCreateKPIGoalMutation = (): UseMutationResult<
  KPIGoal,
  Error,
  KPIGoalCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: KPIGoalCreateRequest) => {
      const response = await axios.post<KPIGoal>(
        `${BASE_URL}/kpi-goals/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kpiGoals'] });
      toast({
        title: "Success",
        description: "KPI goal created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update a KPI goal
 * @param {string} id - The ID of the KPI goal
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateKPIGoalMutation = (id: string): UseMutationResult<
  KPIGoal,
  Error,
  KPIGoalUpdateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: KPIGoalUpdateRequest) => {
      const response = await axios.put<KPIGoal>(
        `${BASE_URL}/kpi-goals/${id}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kpiGoals'] });
      queryClient.invalidateQueries({ queryKey: ['kpiGoal', id] });
      toast({
        title: "Success",
        description: "KPI goal updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to delete a KPI goal
 * @param {string} id - The ID of the KPI goal
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteKPIGoalMutation = (id: string): UseMutationResult<
  void,
  Error,
  void
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      await axios.delete(
        `${BASE_URL}/kpi-goals/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kpiGoals'] });
      toast({
        title: "Success",
        description: "KPI goal deleted successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to bulk create KPI goals
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the bulk creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created KPI goals when successful
 */
export const useBulkCreateKPIGoalsMutation = (): UseMutationResult<
  KPIGoal[],
  Error,
  KPIGoalBulkCreateRequest
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: KPIGoalBulkCreateRequest) => {
      const response = await axios.post<{ goals: KPIGoal[] }>(
        `${BASE_URL}/kpi-goals/bulk_create/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data.goals;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['kpiGoals'] });
      toast({
        title: "Success",
        description: `Successfully created ${data.length} KPI goals`,
        variant: "default"
      });
    }
  });
}; 


/**
 * Get Organization KPI Goals
 * 
 * Fetches all KPI goals for the current organization
 * 
 * Endpoint: GET /kpi-goals/
 * Authentication: Required
 */
export const useOrganizationKPIGoals = (): UseQueryResult<KPIGoal[], Error> => {
  const { accessToken } = useSelector((state: RootState) => state.auth);
  return useQuery({
    queryKey: ['organization-kpi-goals'],
    queryFn: async () => {
      const response = await axios.get(`${KPI_GOALS_BASE_URL}/kpi-goals/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    },
    enabled: !!accessToken,
  });
};

/**
* Create Organization KPI Goal
* 
* Creates a new KPI goal for the current organization
* 
* Endpoint: POST /kpi-goals/
* Authentication: Required
*/
export const useCreateOrganizationKPIGoal = (): UseMutationResult<KPIGoal, Error, KPIGoalCreateRequest> => {
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  return useMutation({
    mutationFn: async (data: KPIGoalCreateRequest) => {
      const response = await axios.post(`${KPI_GOALS_BASE_URL}/kpi-goals/`, data, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "KPI goal created successfully",
      });
    }
  });
};

/**
* Update Organization KPI Goal
* 
* Updates an existing KPI goal for the current organization
* 
* Endpoint: PUT /kpi-goals/{id}/
* Authentication: Required
*/
export const useUpdateOrganizationKPIGoal = (): UseMutationResult<KPIGoal, Error, KPIGoalUpdateRequest> => {
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  return useMutation({
    mutationFn: async ({ id, data }: KPIGoalUpdateRequest) => {
      const response = await axios.put(`${KPI_GOALS_BASE_URL}/kpi-goals/${id}/`, data, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "KPI goal updated successfully",
      });
    }
  });
};

/**
* Delete Organization KPI Goal
* 
* Deletes an existing KPI goal for the current organization
* 
* Endpoint: DELETE /kpi-goals/{id}/
* Authentication: Required
*/
export const useDeleteOrganizationKPIGoal = (): UseMutationResult<void, Error, string> => {
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  return useMutation({
    mutationFn: async (id: string) => {
      await axios.delete(`${KPI_GOALS_BASE_URL}/kpi-goals/${id}/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "KPI goal deleted successfully",
      });
    }
  });
};

/**
* Bulk Create Organization KPI Goals
* 
* Creates multiple KPI goals for the current organization
* 
* Endpoint: POST /kpi-goals/bulk_create/
* Authentication: Required
*/
export const useBulkCreateOrganizationKPIGoals = (): UseMutationResult<KPIGoal[], Error, KPIGoalBulkCreateRequest> => {
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  return useMutation({
    mutationFn: async (data: KPIGoalBulkCreateRequest) => {
      const response = await axios.post(`${KPI_GOALS_BASE_URL}/kpi-goals/bulk-create/`, data, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "KPI goals created successfully",
      });
    }
  });
};



