import { WebSocketMessage } from '@/types/websocket';

class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private subscribers: ((message: WebSocketMessage) => void)[] = [];
  private isConnecting = false;
  private currentToken: string | null = null;

  private constructor() {
    this.connect = this.connect.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.disconnect = this.disconnect.bind(this);
  }

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  public connect(token: string) {
    // If already connected with same token, don't reconnect
    if (this.ws?.readyState === WebSocket.OPEN && this.currentToken === token) {
      console.log('WebSocket already connected');
      return;
    }

    // If currently connecting, don't start another connection
    if (this.isConnecting) {
      console.log('WebSocket connection already in progress');
      return;
    }

    this.currentToken = token;
    this.isConnecting = true;

    if (this.ws) {
      console.log('Closing existing WebSocket connection');
      this.ws.close();
    }

    // Use the WebSocket URL from environment variables
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL;
    if (!wsUrl) {
      console.error('WebSocket URL not configured in environment variables');
      return;
    }
    console.log('Connecting to WebSocket:', wsUrl);

    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket connected successfully');
      this.isConnecting = false;
      // Send authentication message immediately after connection
      this.ws?.send(JSON.stringify({
        type: 'authentication',
        token: token // Changed from data: { token } to match the test implementation
      }));
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data) as WebSocketMessage;
        console.log('Received WebSocket message:', message);
        
        // Handle different message types
        switch (message.type) {
          case 'chat_message':
            // Determine message type (user or assistant)
            const messageType = message.data?.type === 'assistant_response' ? 'assistant' : 'user';
            
            // Try different possible locations for the message content
            let messageContent = null;
            
            // Check data.data.content first (for assistant responses)
            if (message.data?.content) {
              messageContent = message.data.content;
            }
            // Then check message.message
            else if (message.message) {
              messageContent = message.message;
            }
            // Finally check message.data.message
            else if (message.data?.message) {
              messageContent = message.data.message;
            }
            
            if (messageContent) {
              console.log(`Adding ${messageType} message:`, messageContent);
              this.subscribers.forEach(callback => callback({
                ...message,
                data: {
                  ...message.data,
                  content: messageContent,
                  type: messageType
                }
              }));
            }
            break;

          case 'authentication_successful':
            console.log('WebSocket authentication successful');
            break;

          default:
            // Handle other message types
            this.subscribers.forEach(callback => callback(message));
            break;
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.isConnecting = false;
      // Attempt to reconnect after error
      setTimeout(() => {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
          console.log('Attempting to reconnect WebSocket...');
          this.connect(token);
        }
      }, 5000);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.isConnecting = false;
      this.ws = null;
      // Attempt to reconnect after disconnection
      if (this.currentToken) {
        setTimeout(() => {
          console.log('Attempting to reconnect WebSocket...');
          this.connect(this.currentToken!);
        }, 5000);
      }
    };
  }

  public subscribe(callback: (message: WebSocketMessage) => void) {
    console.log('Adding new WebSocket subscriber');
    this.subscribers.push(callback);
    return () => {
      console.log('Removing WebSocket subscriber');
      this.subscribers = this.subscribers.filter(cb => cb !== callback);
    };
  }

  public disconnect() {
    console.log('Disconnecting WebSocket');
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.currentToken = null;
      this.isConnecting = false;
      this.subscribers = [];
    }
  }
}

// Export a singleton instance
export const websocketService = WebSocketService.getInstance(); 