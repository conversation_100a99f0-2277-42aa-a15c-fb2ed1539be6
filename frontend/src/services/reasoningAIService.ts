import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { DocumentData } from '@/type/documentData';
import { DocumentWithCategories } from '@/type/doocument';
import { ChronologicalData } from '@/type/documentData';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL + '/api/reasoning_ai/v1';
const CREATE_DOCUMENT_URL = process.env.NEXT_PUBLIC_API_URL + "/api/create_document/v1";
const REASONING_URL = process.env.NEXT_PUBLIC_API_URL + "/api/reasoning_ai/v1";

// Get document data sections
export const useDocDataSections = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useQuery({
    queryKey: ['docDataSections', caseId],
    queryFn: async () => {
      const { data } = await axios.get<DocumentData>(
        `${BASE_URL}/get-doc-data-sections`,
        {
          params: { case_name: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId
  });
};

// Update pain and suffering raw data
export const useUpdatePainSufferingData = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ 
      caseId, 
      painSufferingData 
    }: { 
      caseId: string; 
      painSufferingData: DocumentData 
    }) => {
      const { data } = await axios.post(
        `${BASE_URL}/update-pain-suffering-raw-data`,
        { pain_suffering_data: painSufferingData },
        {
          params: { case_name: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: ['docDataSections', variables.caseId] 
      });
    }
  });
};

export const useUpdateDocumentData = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ 
      caseId, 
      documentData 
    }: { 
      caseId: string; 
      documentData: DocumentData 
    }) => {
      const { data } = await axios.post(
        `${BASE_URL}/update-document-data`,
        { document_data: documentData },
        {
          params: { case_name: caseId },
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: ['docDataSections', variables.caseId] 
      });
    }
  });
};

// Create document
export const useCreateDocument = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ 
      caseId, 
      organization,
      fileName,
      documentWithCategories 
    }: { 
      caseId: string; 
      organization: string;
      fileName: string;
      documentWithCategories: DocumentWithCategories[];
    }) => {
      const response = await axios.post(
        `${CREATE_DOCUMENT_URL}/create-document`,
        { document_categories: documentWithCategories },
        {
          params: { 
            case_name: caseId,
            organization: encodeURIComponent(organization),
            file_name: fileName
          },
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          responseType: 'blob'
        }
      );
      return response.data;
    }
  });
};

// Save document to S3
export const useSaveDocInS3 = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ 
      caseId, 
      saveGeneratedDocInS3,
      fileName,
      organization,
      documentWithCategories
    }: { 
      caseId: string;
      saveGeneratedDocInS3: boolean;
      fileName: string;
      organization: string;
      documentWithCategories: DocumentWithCategories[];
    }) => {
      const { data } = await axios.post<string>(
        `${CREATE_DOCUMENT_URL}/create-document`,
        { document_categories: documentWithCategories },
        {
          params: { 
            case_name: caseId,
            save_generated_doc_in_s3: saveGeneratedDocInS3,
            file_name: fileName,
            organization: encodeURIComponent(organization)
          },
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          responseType: 'text'
        }
      );
      return { s3_key: data };
    }
  });
};


// Rerun Treatment Expense Analysis
export const useRerunTreatmentExpense = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, accidentDate }: { caseId: string; accidentDate: string }) => {
      const { data } = await axios.get(
        `${REASONING_URL}/rerun-treatment-expense`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};

// Rerun Loss Household Services
export const useRerunLossHouseholdServices = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, accidentDate }: { caseId: string; accidentDate: string }) => {
      const { data } = await axios.get(
        `${REASONING_URL}/rerun-loss-household-services`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};

// Rerun Loss Income Summary
export const useRerunLossIncomeSummary = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, accidentDate }: { caseId: string; accidentDate: string }) => {
      const { data } = await axios.get(
        `${REASONING_URL}/rerun-loss-income-summary`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};

// Rerun Loss Income Table
export const useRerunLossIncomeTable = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, accidentDate }: { caseId: string; accidentDate: string }) => {
      const { data } = await axios.get(
        `${REASONING_URL}/rerun-loss-income-table`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};

// Rerun Chronological Data
export const useRerunChronologicalData = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation<ChronologicalData, Error, { caseId: string; accidentDate: string }>({
    mutationFn: async ({ caseId, accidentDate }) => {
      const { data } = await axios.get<ChronologicalData>(
        `${REASONING_URL}/rerun-chronological-data`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};

// Rerun Future Treatments
export const useRerunFutureTreatments = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, accidentDate }: { caseId: string; accidentDate: string }) => {
      const { data } = await axios.get(
        `${REASONING_URL}/rerun-future-treatments`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};

// Rerun Disease Info
export const useRerunDiseaseInfo = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, accidentDate }: { caseId: string; accidentDate: string }) => {
      const { data } = await axios.get<{ data: { [key: string]: string } }>(
        `${REASONING_URL}/rerun-disease-info`,
        {
          params: { case_id: caseId, accident_date: accidentDate },
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );
      return data;
    }
  });
};



