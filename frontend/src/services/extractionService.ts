import axios from '@/lib/axios';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

interface AnalysisParams {
  caseId: string;
  fileNames: string[];
  accidentDate: string;
}

// interface UpdateAnalysisData {
//   caseId: string;
//   data: Record<string, any>;
// }

const BASE_URL = process.env.NEXT_PUBLIC_API_URL + '/api/extraction_ai/v1';

const useExtractionService = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  const headers = {
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  };

  // Helper function to create URL with params
  const createUrl = (endpoint: string, params: Record<string, string>) => {
    const url = new URL(`${BASE_URL}/${endpoint}/`);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });
    return url.toString();
  };

  // Queries
  const useMedicalAnalysisData = (caseId: string) => 
    useQuery({
      queryKey: ['medicalAnalysis', caseId],
      queryFn: async () => {
        const url = createUrl('get-medical-analysis-data', { case_name: caseId });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      enabled: Boolean(caseId),
    });

  const useTreatmentExpenseData = (caseId: string) => 
    useQuery({
      queryKey: ['treatmentExpense', caseId],
      queryFn: async () => {
        const url = createUrl('get-treatment-expense-analysis-data', { case_name: caseId });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      enabled: Boolean(caseId),
    });

  const useLossOfEarningsData = (caseId: string) => 
    useQuery({
      queryKey: ['lossOfEarnings', caseId],
      queryFn: async () => {
        const url = createUrl('get-loss-of-earnings-analysis-data', { case_name: caseId });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      enabled: Boolean(caseId),
    });

  const useIncidentDetailsData = (caseId: string) => 
    useQuery({
      queryKey: ['incidentDetails', caseId],
      queryFn: async () => {
        const url = createUrl('get-incident-details-analysis-data', { case_name: caseId });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      enabled: Boolean(caseId),
    });

  const useFutureTreatmentData = (caseId: string) => 
    useQuery({
      queryKey: ['futureTreatment', caseId],
      queryFn: async () => {
        const url = createUrl('get-future-treatment-analysis-data', { case_name: caseId });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      enabled: Boolean(caseId),
    });

  // Start Analysis Mutations
  const useStartMedicalAnalysis = () =>
    useMutation({
      mutationFn: async ({ caseId, fileNames, accidentDate }: AnalysisParams) => {
        const url = createUrl('start-medical-analysis', {
          case_name: caseId,
          file_names: fileNames.join(';'),
          accident_date: accidentDate,
        });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      onSuccess: (_, { caseId }) => {
        queryClient.invalidateQueries({ queryKey: ['medicalAnalysis', caseId] });
        toast({
          title: "Analysis Started",
          description: "Medical analysis has been initiated successfully.",
        });
      },
      onError: (error) => {
        console.error('Error starting medical analysis:', error);
        toast({
          title: "Error",
          description: "Failed to start medical analysis. Please try again.",
          variant: "destructive",
        });
      },
    });

  const useStartTreatmentExpenseAnalysis = () =>
    useMutation({
      mutationFn: async ({ caseId, fileNames, accidentDate }: AnalysisParams) => {
        const url = createUrl('start-treatment-expense-analysis', {
          case_name: caseId,
          file_names: fileNames.join(';'),
          accident_date: accidentDate,
        });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      onSuccess: (_, { caseId }) => {
        queryClient.invalidateQueries({ queryKey: ['treatmentExpense', caseId] });
        toast({
          title: "Analysis Started",
          description: "Treatment expense analysis has been initiated successfully.",
        });
      },
      onError: (error) => {
        console.error('Error starting treatment expense analysis:', error);
        toast({
          title: "Error",
          description: "Failed to start treatment expense analysis. Please try again.",
          variant: "destructive",
        });
      },
    });

  const useStartLossOfEarningsAnalysis = () =>
    useMutation({
      mutationFn: async ({ caseId, fileNames, accidentDate }: AnalysisParams) => {
        const url = createUrl('start-loss-of-earnings-analysis', {
          case_name: caseId,
          file_names: fileNames.join(';'),
          accident_date: accidentDate,
        });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      onSuccess: (_, { caseId }) => {
        queryClient.invalidateQueries({ queryKey: ['lossOfEarnings', caseId] });
        toast({
          title: "Analysis Started",
          description: "Loss of earnings analysis has been initiated successfully.",
        });
      },
    });

  const useStartIncidentDetailsAnalysis = () =>
    useMutation({
      mutationFn: async ({ caseId, fileNames, accidentDate }: AnalysisParams) => {
        const url = createUrl('start-incident-details-analysis', {
          case_name: caseId,
          file_names: fileNames.join(';'),
          accident_date: accidentDate,
        });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      onSuccess: (_, { caseId }) => {
        queryClient.invalidateQueries({ queryKey: ['incidentDetails', caseId] });
        toast({
          title: "Analysis Started",
          description: "Incident details analysis has been initiated successfully.",
        });
      },
    });

  const useStartFutureTreatmentAnalysis = () =>
    useMutation({
      mutationFn: async ({ caseId, fileNames, accidentDate }: AnalysisParams) => {
        const url = createUrl('start-future-treatment-analysis', {
          case_name: caseId,
          file_names: fileNames.join(';'),
          accident_date: accidentDate,
        });
        const { data } = await axios.get(url, { headers });
        return data;
      },
      onSuccess: (_, { caseId }) => {
        queryClient.invalidateQueries({ queryKey: ['futureTreatment', caseId] });
        toast({
          title: "Analysis Started",
          description: "Future treatment analysis has been initiated successfully.",
        });
      },
    });

  // // Update Analysis Mutations
  // const useUpdateMedicalAnalysisData = () =>
  //   useMutation({
  //     mutationFn: async ({ caseId, data }: UpdateAnalysisData) => {
  //       const url = createUrl('update-medical-analysis-data', { case_name: caseId });
  //       const { data: response } = await axios.post(url, { data }, { headers });
  //       return response;
  //     },
  //     onSuccess: (_, { caseId }) => {
  //       queryClient.invalidateQueries({ queryKey: ['medicalAnalysis', caseId] });
  //       toast({
  //         title: "Update Successful",
  //         description: "Medical analysis data has been updated.",
  //       });
  //     },
  //   });

  // const useUpdateTreatmentExpenseData = () =>
  //   useMutation({
  //     mutationFn: async ({ caseId, data }: UpdateAnalysisData) => {
  //       const url = createUrl('update-treatment-expense-analysis-data', { case_name: caseId });
  //       const { data: response } = await axios.post(url, { data }, { headers });
  //       return response;
  //     },
  //     onSuccess: (_, { caseId }) => {
  //       queryClient.invalidateQueries({ queryKey: ['treatmentExpense', caseId] });
  //       toast({
  //         title: "Update Successful",
  //         description: "Treatment expense data has been updated.",
  //       });
  //     },
  //   });

  return {
    // Queries
    useMedicalAnalysisData,
    useTreatmentExpenseData,
    useLossOfEarningsData,
    useIncidentDetailsData,
    useFutureTreatmentData,

    // Start Analysis Mutations
    useStartMedicalAnalysis,
    useStartTreatmentExpenseAnalysis,
    useStartLossOfEarningsAnalysis,
    useStartIncidentDetailsAnalysis,
    useStartFutureTreatmentAnalysis,

    // Update Mutations
    // useUpdateMedicalAnalysisData,
    // useUpdateTreatmentExpenseData,
  };
};

export default useExtractionService;