import { useQ<PERSON>y, useMutation, UseQueryResult, UseMutationResult, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/users';

/**
 * {
    "id": 1,
    "email": "<EMAIL>",
    "username": "pritish",
    "first_name": "Pritish",
    "last_name": "<PERSON>",
    "role": "admin",
    "verified": false,
    "date_joined": "2024-11-05T15:25:01Z",
    "last_login": "2024-12-09T08:29:29.108721Z",
    "organization": {
        "id": 1,
        "name": "Alpha Law"
    }
}
 */
// Types
export interface UserProfile {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    role: 'admin' | 'member';
    subscription_type: string;
    organization: {
        id: string;
        name: string;
    };
    verified: boolean;
    date_joined: string;
    last_login: string;
}

export interface UpdateProfileRequest {
    first_name: string;
    last_name: string;
}

export interface ChangePasswordRequest {
    old_password: string;
    new_password: string;
    confirm_password: string;
}

export interface UserTag {
    id: number;
    name: string;
    description: string;
    is_default: boolean;
    created_at: string;
    updated_at: string;
}

/**
 * Get User Profile
 * 
 * Retrieves the current user's profile details
 * 
 * Endpoint: GET /users/me/
 * Authentication: Required
 */
export const useUserProfile = (): UseQueryResult<UserProfile> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['user-profile'],
        queryFn: async () => {
            const { data } = await axios.get<UserProfile>(
                `${BASE_URL}/users/me/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Update User Profile
 * 
 * Updates the current user's profile details
 * 
 * Endpoint: PUT /users/me/
 * Authentication: Required
 */
export const useUpdateProfile = (): UseMutationResult<UserProfile, Error, UpdateProfileRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UpdateProfileRequest) => {
            const response = await axios.put<UserProfile>(
                `${BASE_URL}/users/me/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-profile'] });
            toast({
                title: "Success",
                description: "Profile updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Change Password
 * 
 * Changes the user's password
 * 
 * Endpoint: POST /change-password/
 * Authentication: Required
 */
export const useChangePassword = (): UseMutationResult<{ success: string }, Error, ChangePasswordRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ChangePasswordRequest) => {
            const response = await axios.post<{ success: string }>(
                `${BASE_URL}/change-password/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            toast({
                title: "Success",
                description: data.success || "Password changed successfully",
                variant: "default"
            });
        }
    });
};
