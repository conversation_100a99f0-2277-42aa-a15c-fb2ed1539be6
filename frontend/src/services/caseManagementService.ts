import axios from '@/lib/axios';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { store } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { 
  CaseWorker, 
  UpdateCaseWorkersData, 
  UserDetails 
} from '@/type/caseManagement';
import { AttorneyResponse } from '@/type/case-management/orgTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Helper to get token
const getToken = () => {
  const state = store.getState();
  return state.auth.accessToken;
};

const caseManagementService = {
  // Get workers for a specific case
  async getCaseWorkers(caseId: string): Promise<CaseWorker> {
    const token = getToken();
    const { data } = await axios.get(
      `${BASE_URL}/cases/${caseId}/workers/`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  // Get available users for assignment
  async getAvailableUsers(caseId: string, role?: string): Promise<UserDetails[]> {
    const token = getToken();
    const params = role ? { role } : {};
    const { data } = await axios.get(
      `${BASE_URL}/cases/${caseId}/workers/available-users/`,
      {
        headers: { Authorization: `Bearer ${token}` },
        params
      }
    );
    return data;
  },

  // Update case workers
  async updateCaseWorkers(caseId: string, workerData: UpdateCaseWorkersData): Promise<CaseWorker> {
    const token = getToken();
    const { data } = await axios.post(
      `${BASE_URL}/cases/${caseId}/workers/`,
      workerData,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  // React Query Hooks with proper configurations
  useCaseWorkers(caseId: string) {
    return useQuery({
      queryKey: ['case-workers', caseId],
      queryFn: () => this.getCaseWorkers(caseId),
      enabled: !!caseId,
      staleTime: 1000 * 60, // Cache for 1 minute
      retry: 2, // Retry failed requests twice
    });
  },

  useAvailableUsers(caseId: string, role?: string) {
    return useQuery({
      queryKey: ['available-users', caseId, role],
      queryFn: () => this.getAvailableUsers(caseId, role),
      enabled: !!caseId,
      staleTime: 1000 * 60 * 5, // Cache for 5 minutes
    });
  },

  useUpdateCaseWorkers() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: ({ caseId, data }: { caseId: string; data: UpdateCaseWorkersData }) =>
        this.updateCaseWorkers(caseId, data),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['case-workers', variables.caseId] });
        toast({
          title: "Success",
          description: "Case workers updated successfully",
          variant: "default",
        });
      }
    });
  },

  // New method to get case workers details from API endpoint
  useCaseWorkersDetails(caseId: string) {
    return useQuery<{ lead_attorney_details: AttorneyResponse }>({
      queryKey: ["case-workers-details", caseId],
      queryFn: async () => {
        const response = await fetch(`/api/case-management/cases/${caseId}/workers`);
        if (!response.ok) {
          throw new Error("Failed to fetch case workers");
        }
        return response.json();
      },
      enabled: Boolean(caseId),
    });
  },
};

export default caseManagementService;
