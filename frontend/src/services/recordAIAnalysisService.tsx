import { useMutation } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { CategorizeFilesRequest, DocumentWithCategories, CategorizeFilesBackgroundRequest } from '@/type/doocument';


const BASE_URL = process.env.NEXT_PUBLIC_API_URL + '/api/record_ai/v1';

export const useCategorizeFiles = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useMutation({
    mutationFn: async (data: CategorizeFilesRequest) => {
      const response = await axios({
        method: 'POST',
        url: `${BASE_URL}/categorize-files`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        data,
        responseType: 'stream'
      });

      const results: DocumentWithCategories[] = [];
      const decoder = new TextDecoder('utf-8');
      let buffer = '';

      for await (const chunk of response.data) {
        buffer += decoder.decode(chunk, { stream: true });
        const parts = buffer.split(/(?<=})\s*(?={)/);

        // Process complete JSON objects
        parts.slice(0, -1).forEach(part => {
          try {
            const data = JSON.parse(part);
            results.push(data);
          } catch (error) {
            console.error('Error parsing chunk:', error);
            throw error;
          }
        });

        buffer = parts[parts.length - 1];
      }

      return results;
    }
  });
};

export const useCategorizeFilesBackground = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (data: CategorizeFilesBackgroundRequest) => {
      const response = await axios({
        method: 'POST',
        url: `${BASE_URL}/categorize-files-background`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        data,
      });
      
      console.log('Background categorization started:', response.data);
      return response.data;
    },
    onError: (error) => {
      console.error('Background categorization error:', error);
      throw error;
    }
  });
};
