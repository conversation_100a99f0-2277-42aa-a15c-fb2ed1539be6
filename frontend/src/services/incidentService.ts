import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { isAxiosError } from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { CaseIncidentDetails, IncidentDetailsUpdateRequest } from '@/type/incidentTypes';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch incident details for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseIncidentDetails[]>} Query result containing incident details
 */
export const useIncidentDetailsQuery = (caseId: string): UseQueryResult<CaseIncidentDetails> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['incidentDetails', caseId],
        queryFn: async () => {
            if (!caseId) throw new Error('Case ID is required');

            const { data } = await axios.get<CaseIncidentDetails>(
                `${BASE_URL}/cases/${caseId}/incident-details/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create incident details
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for creating incident details
 */
export const useCreateIncidentDetailsMutation = (caseId: string): UseMutationResult<
    CaseIncidentDetails,
    Error,
    Partial<CaseIncidentDetails>
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Partial<CaseIncidentDetails>) => {
            const response = await axios.post<CaseIncidentDetails>(
                `${BASE_URL}/cases/${caseId}/incident-details/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['incidentDetails', caseId] });
            toast({
                title: "Success",
                description: "Incident details created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update incident details
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for updating incident details
 */
export const useUpdateIncidentDetailsMutation = (caseId: string): UseMutationResult<
    CaseIncidentDetails,
    Error,
    IncidentDetailsUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: IncidentDetailsUpdateRequest) => {
            try {
                // First try to update
                const response = await axios.put<CaseIncidentDetails>(
                    `${BASE_URL}/cases/${caseId}/incident-details/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                // If 404, create new incident details
                if (isAxiosError(error) && error.response?.status === 404) {
                    const createResponse = await axios.post<CaseIncidentDetails>(
                        `${BASE_URL}/cases/${caseId}/incident-details/`,
                        {
                            ...data,
                            case: caseId,
                            insurance_status: data.insurance_status ?? false,
                            conflicts_checked: data.conflicts_checked ?? false,
                            surgery_required: data.surgery_required ?? false,
                        },
                        {
                            headers: {
                                Authorization: `Bearer ${accessToken}`
                            }
                        }
                    );
                    return createResponse.data;
                }
                throw error;
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['incidentDetails', caseId] });
            toast({
                title: "Success",
                description: "Incident details saved successfully",
                variant: "default"
            });
        }
    });
};
