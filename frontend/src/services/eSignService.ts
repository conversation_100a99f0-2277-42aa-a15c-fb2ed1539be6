import { useQuery, useMutation, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { AxiosError } from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/esign';

// Document Types
export type DocumentStatus = 'draft' | 'pending' | 'completed' | 'voided';
export type RecipientStatus = 'pending' | 'signed' | 'declined';
export type FieldType = 
  | 'SIGNATURE' 
  | 'INITIALS' 
  | 'NAME' 
  | 'EMAIL' 
  | 'DATE'
  | 'TEXT' 
  | 'NUMBER' 
  | 'RADIO' 
  | 'CHECKBOX' 
  | 'DROPDOWN';

export interface Document {
  id: number;
  title: string;
  status: DocumentStatus;
  recipients?: Recipient[];
  fields?: Field[];
  created_at: string;
  updated_at: string;
}

export interface Recipient {
  id: number;
  email: string;
  name: string;
  signing_order: number;
  status: RecipientStatus;
}

export interface FieldPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FieldMeta {
  label?: string;
  required?: boolean;
  fontSize?: number;
  textAlign?: 'left' | 'center' | 'right';
}

export interface Field {
  id: number;
  type: FieldType;
  pageNumber: number;
  position: FieldPosition;
  meta?: FieldMeta;
}

// Request Types
export interface CreateDocumentRequest {
  file: File;
  title: string;
}

export interface CreateRecipientRequest {
  document: number;
  email: string;
  name: string;
  signing_order: number;
}

export interface UpdateRecipientRequest {
  name?: string;
  signing_order?: number;
}

export interface CreateFieldRequest {
  recipientId: number;
  type: FieldType;
  pageNumber: number;
  pageX: number;
  pageY: number;
  pageWidth: number;
  pageHeight: number;
  fieldMeta?: FieldMeta;
}

export interface UpdateFieldRequest {
  pageX?: number;
  pageY?: number;
  pageWidth?: number;
  pageHeight?: number;
  fieldMeta?: FieldMeta;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

/**
 * Create Document
 * 
 * Creates a new document for e-signing
 * Endpoint: POST /documents/
 * Authentication: Required
 */
export const useCreateDocument = (): UseMutationResult<Document, Error, CreateDocumentRequest> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateDocumentRequest) => {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('title', data.title);

      try {
        const { data: response } = await axios.post<Document>(
          `${BASE_URL}/documents/`,
          formData,
          {
            headers: { 
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'multipart/form-data'
            }
          }
        );
        return response;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to create document';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Get Document
 * 
 * Retrieves document details including recipients and fields
 * Endpoint: GET /documents/{id}/
 * Authentication: Required
 */
export const useDocument = (documentId: number): UseQueryResult<Document> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['document', documentId],
    queryFn: async () => {
      try {
        const { data } = await axios.get<Document>(
          `${BASE_URL}/documents/${documentId}/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to fetch document';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    },
    enabled: !!accessToken && !!documentId
  });
};

/**
 * Send Document
 * 
 * Sends document to recipients for signing
 * Endpoint: POST /documents/{id}/send/
 * Authentication: Required
 */
export const useSendDocument = (): UseMutationResult<{ status: string }, Error, number> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (documentId: number) => {
      try {
        const { data } = await axios.post<{ status: string }>(
          `${BASE_URL}/documents/${documentId}/send/`,
          {},
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return data;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to send document';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Resend Document
 * 
 * Resends document to specific recipients
 * Endpoint: POST /documents/{id}/resend/
 * Authentication: Required
 */
export const useResendDocument = (): UseMutationResult<void, Error, { documentId: number; recipientIds: number[] }> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ documentId, recipientIds }) => {
      try {
        await axios.post(
          `${BASE_URL}/documents/${documentId}/resend/`,
          { recipients: recipientIds },
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to resend document';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Create Recipient
 * 
 * Adds a recipient to a document
 * Endpoint: POST /recipients/
 * Authentication: Required
 */
export const useCreateRecipient = (): UseMutationResult<Recipient, Error, CreateRecipientRequest> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateRecipientRequest) => {
      try {
        const { data: response } = await axios.post<Recipient>(
          `${BASE_URL}/recipients/`,
          data,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return response;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to create recipient';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Update Recipient
 * 
 * Updates recipient details
 * Endpoint: PATCH /recipients/{id}/
 * Authentication: Required
 */
export const useUpdateRecipient = (): UseMutationResult<Recipient, Error, { id: number; data: UpdateRecipientRequest }> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, data }) => {
      try {
        const { data: response } = await axios.patch<Recipient>(
          `${BASE_URL}/recipients/${id}/`,
          data,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return response;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to update recipient';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Delete Recipient
 * 
 * Deletes a recipient from a document
 * Endpoint: DELETE /recipients/{id}/
 * Authentication: Required
 */
export const useDeleteRecipient = (): UseMutationResult<void, Error, number> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (recipientId: number) => {
      try {
        await axios.delete(
          `${BASE_URL}/recipients/${recipientId}/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to delete recipient';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Create Field
 * 
 * Adds a field to a document
 * Endpoint: POST /documents/{document_id}/fields/
 * Authentication: Required
 */
export const useCreateField = (): UseMutationResult<Field, Error, { documentId: number; data: CreateFieldRequest }> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ documentId, data }) => {
      try {
        const { data: response } = await axios.post<Field>(
          `${BASE_URL}/documents/${documentId}/fields/`,
          data,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return response;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to create field';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Update Field
 * 
 * Updates field details
 * Endpoint: PATCH /documents/{document_id}/fields/{id}/
 * Authentication: Required
 */
export const useUpdateField = (): UseMutationResult<Field, Error, { documentId: number; fieldId: number; data: UpdateFieldRequest }> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ documentId, fieldId, data }) => {
      try {
        const { data: response } = await axios.patch<Field>(
          `${BASE_URL}/documents/${documentId}/fields/${fieldId}/`,
          data,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
        return response;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to update field';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Delete Field
 * 
 * Deletes a field from a document
 * Endpoint: DELETE /documents/{document_id}/fields/{id}/
 * Authentication: Required
 */
export const useDeleteField = (): UseMutationResult<void, Error, { documentId: number; fieldId: number }> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ documentId, fieldId }) => {
      try {
        await axios.delete(
          `${BASE_URL}/documents/${documentId}/fields/${fieldId}/`,
          {
            headers: { Authorization: `Bearer ${accessToken}` }
          }
        );
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to delete field';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    }
  });
};

/**
 * Get Documents List
 * 
 * Retrieves paginated list of documents with optional status filter
 * Endpoint: GET /documents/
 * Authentication: Required
 */
export const useDocumentsQuery = (status?: DocumentStatus): UseQueryResult<PaginatedResponse<Document>> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useQuery({
    queryKey: ['documents', status],
    queryFn: async () => {
      try {
        const { data } = await axios.get<PaginatedResponse<Document>>(`${BASE_URL}/documents/`, {
          params: status ? { status } : undefined,
          headers: { Authorization: `Bearer ${accessToken}` }
        });
        return data;
      } catch (error) {
        const errorMessage = (error as AxiosError<{ detail: string }>).response?.data?.detail 
          || (error as Error).message 
          || 'Failed to fetch documents';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw error;
      }
    },
    enabled: !!accessToken
  });
}; 