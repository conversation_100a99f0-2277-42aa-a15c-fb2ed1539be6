import { useQuery, UseQueryResult, useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/alarm-service';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);

export type AlarmType = 'MINOR_FEE_AGREEMENT' | 'NEGOTIATION_DEADLINE' | 'UIM_NEGOTIATION_DEADLINE';

export type AlarmSeverity = 'HIGH' | 'MEDIUM' | 'LOW';
export type AlarmStatus = 'ACTIVE' | 'RESOLVED' | 'IGNORED';

export interface User {
  id: number;
  email: string;
  name: string;
  verified: boolean;
}

export interface ContentObject {
  id: number;
  title?: string;
  name?: string;
  [key: string]: unknown;
}

export interface Alarm {
  id: number;
  organization: number;
  alarm_type: AlarmType;
  severity: AlarmSeverity;
  status: AlarmStatus;
  description: string;
  metadata: Record<string, string | number | boolean | null>;
  created_at: string;
  updated_at: string;
  resolved_at: string | null;
  created_by: number;
  created_by_name: string;
  resolved_by: number | null;
  resolved_by_name: string | null;
  content_type: string;
  object_id: number;
  content_object: ContentObject;
  content_object_str: string;
  notify_users: User[];
}

export interface AlarmQueryParams {
  status?: AlarmStatus;
  severity?: AlarmSeverity;
  alarm_type?: AlarmType;
  notify_users?: number;
  notify_users__in?: number[];
  page?: number;
  page_size?: number;
}

export interface PaginatedAlarmResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Alarm[];
}

export interface NotifyUsersRequest {
  user_ids: number[];
}

/**
 * Custom hook to fetch alarms with pagination and filtering
 */
export const useAlarmsQuery = (params: AlarmQueryParams = {}): UseQueryResult<PaginatedAlarmResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['alarms', params],
    queryFn: async () => {
      const { data } = await axios.get<PaginatedAlarmResponse>(
        `${BASE_URL}/alarms/`,
        {
          params,
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to resolve an alarm
 */
export const useResolveAlarmMutation = (): UseMutationResult<Alarm, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (alarmId: number) => {
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/resolve/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Alarm resolved successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to ignore an alarm
 */
export const useIgnoreAlarmMutation = (): UseMutationResult<Alarm, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (alarmId: number) => {
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/ignore/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Alarm ignored successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to reactivate an alarm
 */
export const useReactivateAlarmMutation = (): UseMutationResult<Alarm, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (alarmId: number) => {
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/reactivate/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Alarm reactivated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to add notify users to an alarm
 */
export const useAddNotifyUsersMutation = (): UseMutationResult<Alarm, Error, { alarmId: number; userIds: number[] }> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ alarmId, userIds }: { alarmId: number; userIds: number[] }) => {
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/add_notify_users/`,
        { user_ids: userIds },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Notify users added successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to remove notify users from an alarm
 */
export const useRemoveNotifyUsersMutation = (): UseMutationResult<Alarm, Error, { alarmId: number; userIds: number[] }> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ alarmId, userIds }: { alarmId: number; userIds: number[] }) => {
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/remove_notify_users/`,
        { user_ids: userIds },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Notify users removed successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to clear all notify users from an alarm
 */
export const useClearNotifyUsersMutation = (): UseMutationResult<Alarm, Error, number> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (alarmId: number) => {
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/clear_notify_users/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Notify users cleared successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to replace all notify users for an alarm
 * Uses the backend endpoint that handles this in a single operation
 */
export const useReplaceNotifyUsersMutation = (): UseMutationResult<Alarm, Error, { alarmId: number; userIds: number[] }> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ alarmId, userIds }: { alarmId: number; userIds: number[] }) => {
      // Use the new replace_notify_users endpoint
      const { data } = await axios.post<Alarm>(
        `${BASE_URL}/alarms/${alarmId}/replace_notify_users/`,
        { user_ids: userIds },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarms'] });
      toast({
        title: "Success",
        description: "Users updated successfully",
        variant: "default"
      });
    }
  });
};