import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  DocumentMetadata,
  FolderDocumentResponse,
  FolderOperationResponse,
  DocumentWithCategories,
  DocumentCategoryResponse,
  DocumentUpdateResponse,
  BulkDocumentUpdateResponse
} from '@/type/doocument';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/document-management';

// List folder files
export const useFolderFiles = (caseId: string, category: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['folderFiles', caseId, category],
    queryFn: async () => {
      const { data } = await axios.get<FolderDocumentResponse[]>(
        `${BASE_URL}/folders/list_folder_files/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          params: {
            case_id: caseId,
            category: category
          }
        }
      );
      return data;
    },
    enabled: !!caseId && !!category
  });
};

// Upload to folder
export const useUploadToFolder = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, category, file }: {
      caseId: string;
      category: string;
      file: File;
    }) => {
      const formData = new FormData();
      formData.append('case_id', caseId);
      formData.append('category', category);
      formData.append('file', file);

      const { data } = await axios.post<FolderDocumentResponse>(
        `${BASE_URL}/folders/upload_to_folder/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['folderFiles', variables.caseId, variables.category]
      });
    }
  });
};

// Delete from folder
export const useDeleteFromFolder = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, category, filename }: {
      caseId: string;
      category: string;
      filename: string;
    }) => {
      const { data } = await axios.delete<FolderOperationResponse>(
        `${BASE_URL}/folders/delete_from_folder/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          data: {
            case_id: caseId,
            category: category,
            filename: filename
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['folderFiles', variables.caseId, variables.category]
      });
    }
  });
};

// Upload multiple files to folder
export const useUploadMultipleToFolder = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, category, files }: {
      caseId: string;
      category: string;
      files: File[];
    }) => {
      const formData = new FormData();
      formData.append('case_id', caseId);
      formData.append('category', category);
      files.forEach(file => formData.append('files', file));

      const { data } = await axios.post<FolderDocumentResponse[]>(
        `${BASE_URL}/folders/upload_multiple_to_folder/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['folderFiles', variables.caseId, variables.category]
      });
    }
  });
};

// Get documents for demand package
export const useDocumentsForDemandPackage = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['demandPackageDocuments', caseId],
    queryFn: async () => {
      const { data } = await axios.get<DocumentMetadata[]>(
        `${BASE_URL}/documents/metadata_demand_package/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          params: {
            case_id: caseId
          }
        }
      );
      return data;
    },
    enabled: !!caseId
  });
};

// List all files
export const useListFiles = (caseId: string | null) => {
  return useQuery({
    queryKey: ['filesList', caseId],
    queryFn: async () => {
      // Return mock successful response with documents array
      return {
        status: 200,
        message: "Files list retrieved successfully",
        data: [],
        documents: [
          
        ]
      };
    }
  });
};

// Get case documents with categories
export const useCaseDocumentsCategories = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['caseDocumentsCategories', caseId],
    queryFn: async () => {
      const { data } = await axios.get<DocumentWithCategories[]>(
        `${BASE_URL}/documents/case_documents_categories/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          params: {
            case_id: caseId
          }
        }
      );
      return data;
    },
    enabled: !!caseId
  });
};

// Add categories to document
export const useAddDocumentCategories = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, documentName, categories }: {
      caseId: string;
      documentName: string;
      categories: string[];
    }) => {
      const { data } = await axios.post<DocumentCategoryResponse>(
        `${BASE_URL}/documents/add_categories/`,
        {
          case_id: caseId,
          document_name: documentName,
          categories: categories
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['caseDocumentsCategories', variables.caseId]
      });
    }
  });
};

// Delete categories from document
export const useDeleteDocumentCategories = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, documentName, categories }: {
      caseId: string;
      documentName: string;
      categories?: string[];
    }) => {
      const { data } = await axios.delete<DocumentCategoryResponse>(
        `${BASE_URL}/documents/delete_categories/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          },
          data: {
            case_id: caseId,
            document_name: documentName,
            categories: categories
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['caseDocumentsCategories', variables.caseId]
      });
    }
  });
};

// Single document update for case chat
export const useUpdateCaseChatFile = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  const mutation = useMutation({
    mutationFn: async ({ caseId, documentName, isPartOfCaseWithChat }: {
      caseId: string;
      documentName: string;
      isPartOfCaseWithChat: boolean;
    }) => {
      const { data } = await axios.post<DocumentUpdateResponse>(
        `${BASE_URL}/documents/update_case_chat_file/`,
        {
          case_id: caseId,
          document_name: documentName,
          is_part_of_case_with_chat: isPartOfCaseWithChat
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    }
  });

  return {
    updateCaseChatFile: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error
  };
};

// Bulk update documents for case chat
export const useBulkUpdateCaseChatFiles = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useMutation({
    mutationFn: async ({ caseId, isPartOfCaseWithChat = true }: {
      caseId: string;
      isPartOfCaseWithChat?: boolean;
    }) => {
      const { data } = await axios.post<BulkDocumentUpdateResponse>(
        `${BASE_URL}/documents/bulk_update_case_chat_files/`,
        {
          case_id: caseId,
          is_part_of_case_with_chat: isPartOfCaseWithChat
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    }
  });
};


