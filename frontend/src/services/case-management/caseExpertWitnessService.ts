import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    CaseExpertWitnessRead,
    CaseExpertWitnessCreateRequest,
    CaseExpertWitnessUpdateRequest,
    CaseExpertWitnessListResponse
} from '@/type/case-management/caseExpertWitnessTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch expert witnesses list
 */
export const useExpertWitnessesQuery = (): UseQueryResult<CaseExpertWitnessListResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['expertWitnesses'],
        queryFn: async () => {
            const { data } = await axios.get<CaseExpertWitnessListResponse>(
                `${BASE_URL}/expert-witnesses/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        }
    });
};

/**
 * Custom hook to fetch case expert witnesses
 */
export const useCaseExpertWitnessesQuery = (caseId: string): UseQueryResult<CaseExpertWitnessRead[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['caseExpertWitnesses', caseId],
        queryFn: async () => {
            const { data } = await axios.get<CaseExpertWitnessRead[]>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

/**
 * Custom hook to create a case expert witness
 */
export const useCreateCaseExpertWitnessMutation = (caseId: string): UseMutationResult<
    CaseExpertWitnessRead,
    Error,
    CaseExpertWitnessCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseExpertWitnessCreateRequest) => {
            const response = await axios.post<CaseExpertWitnessRead>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseExpertWitnesses', caseId] });
            toast({
                title: "Success",
                description: "Expert witness added successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a case expert witness
 */
export const useUpdateCaseExpertWitnessMutation = (
    caseId: string,
    witnessId: string
): UseMutationResult<CaseExpertWitnessRead, Error, CaseExpertWitnessUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseExpertWitnessUpdateRequest) => {
            const response = await axios.put<CaseExpertWitnessRead>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/${witnessId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseExpertWitnesses', caseId] });
            toast({
                title: "Success",
                description: "Expert witness updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a case expert witness
 */
export const useDeleteCaseExpertWitnessMutation = (
    caseId: string,
    witnessId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/${witnessId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseExpertWitnesses', caseId] });
            toast({
                title: "Success",
                description: "Expert witness deleted successfully",
                variant: "default"
            });
        }
    });
};
