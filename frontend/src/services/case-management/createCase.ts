import { useMutation, useQueryClient } from '@tanstack/react-query';
import { store } from '@/store';
import { useToast } from '@/hooks/use-toast';
import axios from '@/lib/axios';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Types
export interface CreateCaseData {
  first_name: string;
  last_name: string;
  source_type: string;
  phone: string;
  email?: string;
  incident_date: string;
  previous_attorney?: string;
  incident_type: string;
  medical_status: string;
  state: string;
  has_insurance: boolean;
  street?: string;
  street2?: string;
  city?: string;
  zip?: string;
  organization_status?: number;
}

export interface CreatedCase {
  id: string;
  case_name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// Helper to get token
const getToken = () => {
  const state = store.getState();
  return state.auth.accessToken;
};

const caseCreationService = {
  // Create a new case
  async createCase(caseData: CreateCaseData): Promise<CreatedCase> {
    const token = getToken();
    const { data } = await axios.post(
      `${BASE_URL}/cases/create/`,
      {
        ...caseData,
        status: 'intake' // Default status for new cases, don't remove as for backward compatibility
      },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  // React Query Hook for case creation
  useCreateCase() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: (data: CreateCaseData) => this.createCase(data),
      onSuccess: (data) => {
        // Invalidate cases list query if it exists
        queryClient.invalidateQueries({ queryKey: ['cases'] });
        
        toast({
          title: "Success",
          description: `Case "${data.case_name}" created successfully`,
          variant: "default",
        });
      }
    });
  },
};

export default caseCreationService;