import { useQuery, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Define the types for the latest negotiations API response
export interface LatestNegotiationsResponse {
    third_party: ThirdPartyNegotiation[];
    uim: UIMNegotiation[];
}

export interface ThirdPartyNegotiation {
    company_name: string;
    defendant_name: string;
    defendant_id: number;
    insurance_company_id: number;
    amount: number;
    status: string;
    date: string;
    id: number;
}

export interface UIMNegotiation {
    company_name: string;
    client_insurance_id: number;
    insurance_company_id: number;
    amount: number;
    status: string;
    date: string;
    id: number;
}

/**
 * Custom hook to fetch latest negotiations data
 */
export const useLatestNegotiationsQuery = (caseId: string): UseQueryResult<LatestNegotiationsResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['latestNegotiations', caseId],
        queryFn: async () => {
            const { data } = await axios.get<LatestNegotiationsResponse>(
                `${BASE_URL}/cases/${caseId}/negotiations-api/latest/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};
