import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { CreateLinkRequest, LinkedCase, LinkedCasesResponse } from '@/type/linkCaseType';

// Constants
const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);

/**
 * Custom hook to fetch linked cases for a specific case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<LinkedCasesResponse>} Query result containing linked cases data
 */
export const useLinkedCasesQuery = (caseId: string): UseQueryResult<LinkedCasesResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['linkedCases', caseId],
        queryFn: async () => {
            if (!caseId) throw new Error('Case ID is required');

            const { data } = await axios.get<LinkedCasesResponse>(
                `${BASE_URL}/cases/${caseId}/linked_cases/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create a link between two cases
 * @param {string} caseId - The ID of the source case
 * @returns {UseMutationResult<LinkedCase, Error, CreateLinkRequest>} Mutation result for creating a case link
 */
export const useCreateCaseLinkMutation = (caseId: string): UseMutationResult<
    LinkedCase,
    Error,
    CreateLinkRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (linkData: CreateLinkRequest) => {
            if (!caseId) throw new Error('Case ID is required');

            const { data } = await axios.post<LinkedCase>(
                `${BASE_URL}/cases/${caseId}/link_case/`,
                linkData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['linkedCases', caseId] });
            toast({
                title: "Success",
                description: "Case link created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to remove a link between two cases
 * @param {string} caseId - The ID of the source case
 * @returns {UseMutationResult<void, Error, string>} Mutation result for removing a case link
 */
export const useRemoveCaseLinkMutation = (caseId: string): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (targetCaseId: string) => {
            if (!caseId) throw new Error('Case ID is required');
            if (!targetCaseId) throw new Error('Target case ID is required');

            await axios.delete(
                `${BASE_URL}/cases/${caseId}/unlink_case/`,
                {
                    params: { target_case_id: targetCaseId },
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['linkedCases', caseId] });
            toast({
                title: "Success",
                description: "Case link removed successfully",
                variant: "default"
            });
        }
    });
};
