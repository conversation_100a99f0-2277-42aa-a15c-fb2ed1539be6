import { useQuery, useMutation, useQ<PERSON>y<PERSON>lient, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    SystemCard,
    UserCard,
    CombinedCardsResponse,
    UserCardCreateRequest,
    CardVisibilityUpdate,
    CardOrderUpdate,
    BulkCardOrderUpdate,
    UserCardUpdate,
    UserCardSyncRequest,
    UserCardSyncResponse
} from '@/type/case-management/cardTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// System Cards Queries and Mutations
export const useSystemCardsQuery = (caseId: string): UseQueryResult<SystemCard[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['system-cards', caseId],
        queryFn: async () => {
            const { data } = await axios.get<SystemCard[]>(
                `${BASE_URL}/cases/${caseId}/system-cards/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

// Update System Card Visibility or Order
export const useUpdateSystemCardMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ cardId, data }: { cardId: number, data: CardVisibilityUpdate | CardOrderUpdate }) => {
            const response = await axios.patch<SystemCard>(
                `${BASE_URL}/cases/${caseId}/system-cards/${cardId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['system-cards', caseId] });
            queryClient.invalidateQueries({ queryKey: ['cards', caseId] });
            toast({
                title: "Success",
                description: "Card updated successfully",
                variant: "default"
            });
        }
    });
};

// Update User Card (Visibility, Order, Pin, Color)
export const useUpdateUserCardMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ cardId, data }: { cardId: number, data: Partial<UserCardUpdate> }) => {
            const response = await axios.patch<UserCard>(
                `${BASE_URL}/cases/${caseId}/user-cards/${cardId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-cards', caseId] });
            queryClient.invalidateQueries({ queryKey: ['cards', caseId] });
            toast({
                title: "Success",
                description: "Card updated successfully",
                variant: "default"
            });
        }
    });
};

// Bulk Update Card Orders
export const useBulkUpdateCardOrdersMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: BulkCardOrderUpdate) => {
            const endpoint = data.cardType === 'system' ? 'system-cards' : 'user-cards';
            await Promise.all(
                data.updates.map(({ id, order }) =>
                    axios.patch(
                        `${BASE_URL}/cases/${caseId}/${endpoint}/${id}/`,
                        { order },
                        {
                            headers: { Authorization: `Bearer ${accessToken}` }
                        }
                    )
                )
            );
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: [variables.cardType === 'system' ? 'system-cards' : 'user-cards', caseId] });
            queryClient.invalidateQueries({ queryKey: ['cards', caseId] });
            toast({
                title: "Success",
                description: "Card orders updated successfully",
                variant: "default"
            });
        }
    });
};

// User Cards Queries and Mutations
export const useUserCardsQuery = (caseId: string): UseQueryResult<UserCard[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['user-cards', caseId],
        queryFn: async () => {
            const { data } = await axios.get<UserCard[]>(
                `${BASE_URL}/cases/${caseId}/user-cards/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

export const useSyncUserCardMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UserCardSyncRequest) => {
            const response = await axios.post<UserCardSyncResponse>(
                `${BASE_URL}/cases/${caseId}/user-cards/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['user-cards'] });
            queryClient.invalidateQueries({ queryKey: ['cards'] });
            toast({
                title: "Success",
                description: data.message || "User card synced successfully",
                variant: "default"
            });
        }
    });
};

export const useCreateUserCardMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UserCardCreateRequest) => {
            const response = await axios.post<UserCard>(
                `${BASE_URL}/cases/${caseId}/user-cards/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-cards', caseId] });
            queryClient.invalidateQueries({ queryKey: ['cards', caseId] });
            toast({
                title: "Success",
                description: "User card created successfully",
                variant: "default"
            });
        }
    });
};

export const useDeleteUserCardMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (cardId: number) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/user-cards/${cardId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['user-cards', caseId] });
            queryClient.invalidateQueries({ queryKey: ['cards', caseId] });
            toast({
                title: "Success",
                description: "User card deleted successfully",
                variant: "default"
            });
        }
    });
};

// Combined Cards Query
export const useCombinedCardsQuery = (caseId: string): UseQueryResult<CombinedCardsResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['cards', caseId],
        queryFn: async () => {
            const { data } = await axios.get<CombinedCardsResponse>(
                `${BASE_URL}/cases/${caseId}/cards/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
}; 