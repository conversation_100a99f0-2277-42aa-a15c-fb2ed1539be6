import { useQuery, useMutation, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    CaseAttorneyLien,
    CaseAttorneyLienResponse,
    CaseAttorneyLienCreateRequest,
    CaseAttorneyLienUpdateRequest,
    CaseMiscellaneousLien,
    CaseMiscellaneousLienResponse,
    CaseMiscellaneousLienCreateRequest,
    CaseMiscellaneousLienUpdateRequest,
} from '@/type/case-management/lienTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Query keys
const ATTORNEY_LIENS_KEY = 'attorney-liens';
const MISC_LIENS_KEY = 'misc-liens';

/**
 * Custom hook to fetch all attorney liens for a case
 */
export const useAttorneyLiensQuery = (caseId: string): UseQueryResult<CaseAttorneyLienResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [ATTORNEY_LIENS_KEY, caseId],
        queryFn: async () => {
            const { data } = await axios.get<CaseAttorneyLienResponse[]>(
                `${BASE_URL}/cases/${caseId}/attorney-liens/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId,
        staleTime: 1000 * 60,
        retry: 2
    });
};

/**
 * Custom hook to create an attorney lien
 */
export const useCreateAttorneyLienMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseAttorneyLienCreateRequest) => {
            const response = await axios.post<CaseAttorneyLien>(
                `${BASE_URL}/cases/${caseId}/attorney-liens/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [ATTORNEY_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Attorney lien created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update an attorney lien
 */
export const useUpdateAttorneyLienMutation = (caseId: string, lienId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseAttorneyLienUpdateRequest) => {
            const response = await axios.patch<CaseAttorneyLien>(
                `${BASE_URL}/cases/${caseId}/attorney-liens/${lienId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [ATTORNEY_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Attorney lien updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update only the final status of an attorney lien
 */
export const useUpdateAttorneyLienFinalStatusMutation = (caseId: string, lienId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: { is_final: boolean, final_lien_date?: string, final_amount?: string }) => {
            const apiData: CaseAttorneyLienUpdateRequest = {
                final_lien: data.is_final,
                final_lien_date: data.is_final ? data.final_lien_date : undefined,
                final_amount: data.is_final ? data.final_amount : undefined
            };
            
            const response = await axios.patch<CaseAttorneyLien>(
                `${BASE_URL}/cases/${caseId}/attorney-liens/${lienId}/`,
                apiData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [ATTORNEY_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Lien status updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete an attorney lien
 */
export const useDeleteAttorneyLienMutation = (caseId: string, lienId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/attorney-liens/${lienId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [ATTORNEY_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Attorney lien deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all miscellaneous liens for a case
 */
export const useMiscLiensQuery = (caseId: string): UseQueryResult<CaseMiscellaneousLienResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [MISC_LIENS_KEY, caseId],
        queryFn: async () => {
            const { data } = await axios.get<CaseMiscellaneousLienResponse[]>(
                `${BASE_URL}/cases/${caseId}/miscellaneous-liens/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId,
        staleTime: 1000 * 60,
        retry: 2
    });
};

/**
 * Custom hook to create a miscellaneous lien
 */
export const useCreateMiscLienMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseMiscellaneousLienCreateRequest) => {
            const response = await axios.post<CaseMiscellaneousLien>(
                `${BASE_URL}/cases/${caseId}/miscellaneous-liens/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [MISC_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Miscellaneous lien created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a miscellaneous lien
 */
export const useUpdateMiscLienMutation = (caseId: string, lienId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseMiscellaneousLienUpdateRequest) => {
            const response = await axios.patch<CaseMiscellaneousLien>(
                `${BASE_URL}/cases/${caseId}/miscellaneous-liens/${lienId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [MISC_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Miscellaneous lien updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update only the final status of a miscellaneous lien
 */
export const useUpdateMiscLienFinalStatusMutation = (caseId: string, lienId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: { is_final: boolean, final_lien_date?: string, final_amount?: string }) => {
            const apiData: CaseMiscellaneousLienUpdateRequest = {
                final_lien: data.is_final,
                final_lien_date: data.is_final ? data.final_lien_date : undefined,
                final_amount: data.is_final ? data.final_amount : undefined
            };
            
            const response = await axios.patch<CaseMiscellaneousLien>(
                `${BASE_URL}/cases/${caseId}/miscellaneous-liens/${lienId}/`,
                apiData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [MISC_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Lien status updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a miscellaneous lien
 */
export const useDeleteMiscLienMutation = (caseId: string, lienId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/miscellaneous-liens/${lienId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [MISC_LIENS_KEY, caseId] });
            toast({
                title: "Success",
                description: "Miscellaneous lien deleted successfully",
                variant: "default"
            });
        }
    });
};