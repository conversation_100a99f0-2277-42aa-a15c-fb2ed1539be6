import { useQuery } from "@tanstack/react-query";
import axios from '@/lib/axios';
import { Contact } from "@/type/case-management/orgTypes";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2;

interface ContactListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Contact[];
}

export const useContactsQuery = (caseId: string, contactType: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ["contacts", caseId, contactType],
    queryFn: async () => {
      const { data } = await axios.get<ContactListResponse>(
        `${BASE_URL}/case-management/cases/${caseId}/contacts/?contact_type=${contactType}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId && !!contactType,
  });
};