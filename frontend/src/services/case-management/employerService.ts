import { useQuery, useMutation, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { 
    EmployerCreateRequest, 
    EmployerResponse,
    EmployerWorkersCompensationCreateRequest,
    EmployerWorkersCompensationResponse
} from '@/type/case-management/employerTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Employers
export const useEmployersQuery = (caseId: string): UseQueryResult<EmployerResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['employers', caseId],
        queryFn: async () => {
            const { data } = await axios.get<EmployerResponse[]>(
                `${BASE_URL}/cases/${caseId}/employers/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

export const useCreateEmployerMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: EmployerCreateRequest) => {
            const response = await axios.post(
                `${BASE_URL}/cases/${caseId}/employers/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['employers', caseId] });
            toast({
                title: "Success",
                description: "Employer added successfully",
                variant: "default"
            });
        }
    });
};

export const useUpdateEmployerMutation = (caseId: string, employerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: EmployerCreateRequest) => {
            const response = await axios.put(
                `${BASE_URL}/cases/${caseId}/employers/${employerId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['employers', caseId] });
            toast({
                title: "Success",
                description: "Employer updated successfully",
                variant: "default"
            });
        }
    });
};

export const useDeleteEmployerMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (employerId: string) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/employers/${employerId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['employers', caseId] });
            toast({
                title: "Success",
                description: "Employer deleted successfully",
                variant: "default"
            });
        }
    });
};

// Workers Compensation
export const useWorkersCompensationQuery = (caseId: string, employerId: string): UseQueryResult<EmployerWorkersCompensationResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['workers-compensation', caseId, employerId],
        queryFn: async () => {
            const { data } = await axios.get<EmployerWorkersCompensationResponse[]>(
                `${BASE_URL}/cases/${caseId}/employers/${employerId}/workers-compensation/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId && !!employerId
    });
};

export const useCreateWorkersCompensationMutation = (caseId: string, employerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: EmployerWorkersCompensationCreateRequest) => {
            const response = await axios.post(
                `${BASE_URL}/cases/${caseId}/employers/${employerId}/workers-compensation/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['workers-compensation', caseId, employerId] });
            toast({
                title: "Success",
                description: "Workers compensation added successfully",
                variant: "default"
            });
        }
    });
};

export const useUpdateWorkersCompensationMutation = (caseId: string, employerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: EmployerWorkersCompensationCreateRequest) => {
            const response = await axios.put(
                `${BASE_URL}/cases/${caseId}/employers/${employerId}/workers-compensation/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['workers-compensation', caseId, employerId] });
            toast({
                title: "Success",
                description: "Workers compensation updated successfully",
                variant: "default"
            });
        }
    });
}; 