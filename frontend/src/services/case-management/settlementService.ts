import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
    SettlementAdvance,
    SettlementAdvanceCreateRequest,
    SettlementAdvanceUpdateRequest,
    LoanCompany,
    LoanCompanyCreateRequest,
    LoanCompanyUpdateRequest,
    SettlementCalculation,
    SettlementCalculationUpdateRequest,
    ManualSettlementEntry,
    ManualSettlementEntryCreateRequest,
    ManualSettlementEntryUpdateRequest,
    ClientTrust,
    ClientTrustCreateRequest,
    ClientTrustUpdateRequest,
    ClientTrustSummary,
} from '@/type/case-management/settlementTypes';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch settlement advances
 */
export const useSettlementAdvancesQuery = (caseId: string): UseQueryResult<SettlementAdvance[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['settlementAdvances', caseId],
        queryFn: async () => {
            const { data } = await axios.get<SettlementAdvance[]>(
                `${BASE_URL}/cases/${caseId}/settlement-advances/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create settlement advance
 */
export const useCreateSettlementAdvanceMutation = (caseId: string): UseMutationResult<
    SettlementAdvance,
    Error,
    SettlementAdvanceCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: SettlementAdvanceCreateRequest) => {
            const response = await axios.post<SettlementAdvance>(
                `${BASE_URL}/cases/${caseId}/settlement-advances/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['settlementAdvances', caseId] });
            toast({
                title: "Success",
                description: "Settlement advance created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update settlement advance
 */
export const useUpdateSettlementAdvanceMutation = (
    caseId: string,
    advanceId: string
): UseMutationResult<SettlementAdvance, Error, SettlementAdvanceUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: SettlementAdvanceUpdateRequest) => {
            const response = await axios.put<SettlementAdvance>(
                `${BASE_URL}/cases/${caseId}/settlement-advances/${advanceId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['settlementAdvances', caseId] });
            toast({
                title: "Success",
                description: "Settlement advance updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete settlement advance
 */
export const useDeleteSettlementAdvanceMutation = (
    caseId: string,
    advanceId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/settlement-advances/${advanceId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['settlementAdvances', caseId] });
            toast({
                title: "Success",
                description: "Settlement advance deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch loan companies
 */
export const useLoanCompaniesQuery = (): UseQueryResult<LoanCompany[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['loanCompanies'],
        queryFn: async () => {
            const { data } = await axios.get<LoanCompany[]>(
                `${BASE_URL}/loan-companies/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
    });
};

/**
 * Custom hook to create a loan company
 */
export const useCreateLoanCompanyMutation = (): UseMutationResult<
    LoanCompany,
    Error,
    LoanCompanyCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LoanCompanyCreateRequest) => {
            const response = await axios.post<LoanCompany>(
                `${BASE_URL}/loan-companies/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['loanCompanies'] });
            toast({
                title: "Success",
                description: "Loan company created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a loan company
 */
export const useUpdateLoanCompanyMutation = (
    companyId: number
): UseMutationResult<LoanCompany, Error, LoanCompanyUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LoanCompanyUpdateRequest) => {
            const response = await axios.put<LoanCompany>(
                `${BASE_URL}/loan-companies/${companyId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['loanCompanies'] });
            toast({
                title: "Success",
                description: "Loan company updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch settlement calculations
 */
export const useSettlementCalculationQuery = (caseId: string): UseQueryResult<SettlementCalculation> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['settlementCalculation', caseId],
        queryFn: async () => {
            const { data } = await axios.get<SettlementCalculation>(
                `${BASE_URL}/cases/${caseId}/settlement-calculation/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to update settlement calculation
 */
export const useUpdateSettlementCalculationMutation = (
    caseId: string
): UseMutationResult<SettlementCalculation, Error, SettlementCalculationUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: SettlementCalculationUpdateRequest) => {
            const response = await axios.patch<SettlementCalculation>(
                `${BASE_URL}/cases/${caseId}/settlement-calculation/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['settlementCalculation', caseId] });
            // Also invalidate settlement memo query since it depends on settlement calculation
            queryClient.invalidateQueries({ queryKey: ['settlementMemo', caseId] });
            toast({
                title: "Success",
                description: "Settlement calculation updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch manual settlement entries
 */
export const useManualSettlementEntriesQuery = (
    caseId: string,
    forWhat: 'THIRD_PARTY_DEMAND' | 'UIM' = 'THIRD_PARTY_DEMAND'
): UseQueryResult<ManualSettlementEntry[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['manualSettlementEntries', caseId, forWhat],
        queryFn: async () => {
            const { data } = await axios.get<ManualSettlementEntry[]>(
                `${BASE_URL}/cases/${caseId}/manual-settlement-entries/?for_what=${forWhat}`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create manual settlement entry
 */
export const useCreateManualSettlementEntryMutation = (caseId: string): UseMutationResult<
    ManualSettlementEntry,
    Error,
    ManualSettlementEntryCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ManualSettlementEntryCreateRequest) => {
            const response = await axios.post<ManualSettlementEntry>(
                `${BASE_URL}/cases/${caseId}/manual-settlement-entries/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['manualSettlementEntries', caseId] });
            // Also invalidate settlement memo query since it depends on manual settlement entries
            queryClient.invalidateQueries({ queryKey: ['settlementMemo', caseId] });
            toast({
                title: "Success",
                description: "Manual settlement entry created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update manual settlement entry
 */
export const useUpdateManualSettlementEntryMutation = (
    caseId: string,
    entryId: string
): UseMutationResult<ManualSettlementEntry, Error, ManualSettlementEntryUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ManualSettlementEntryUpdateRequest) => {
            const response = await axios.put<ManualSettlementEntry>(
                `${BASE_URL}/cases/${caseId}/manual-settlement-entries/${entryId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['manualSettlementEntries', caseId] });
            toast({
                title: "Success",
                description: "Manual settlement entry updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete manual settlement entry
 */
export const useDeleteManualSettlementEntryMutation = (
    caseId: string
): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (entryId: string) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/manual-settlement-entries/${entryId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['manualSettlementEntries', caseId] });
            // Also invalidate settlement memo query since it depends on manual settlement entries
            queryClient.invalidateQueries({ queryKey: ['settlementMemo', caseId] });
            toast({
                title: "Success",
                description: "Manual settlement entry deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch client trust entries
 */
export const useClientTrustQuery = (caseId: string): UseQueryResult<ClientTrust[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['clientTrust', caseId],
        queryFn: async () => {
            const { data } = await axios.get<ClientTrust[]>(
                `${BASE_URL}/cases/${caseId}/client-trust/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

/**
 * Custom hook to create client trust entry
 */
export const useCreateClientTrustMutation = (caseId: string): UseMutationResult<
    ClientTrust,
    Error,
    ClientTrustCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ClientTrustCreateRequest) => {
            const response = await axios.post<ClientTrust>(
                `${BASE_URL}/cases/${caseId}/client-trust/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clientTrust', caseId] });
            toast({
                title: "Success",
                description: "Client trust entry created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update client trust entry
 */
export const useUpdateClientTrustMutation = (
    caseId: string,
    entryId: string
): UseMutationResult<ClientTrust, Error, ClientTrustUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ClientTrustUpdateRequest) => {
            const response = await axios.put<ClientTrust>(
                `${BASE_URL}/cases/${caseId}/client-trust/${entryId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clientTrust', caseId] });
            toast({
                title: "Success",
                description: "Client trust entry updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete client trust entry
 */
export const useDeleteClientTrustMutation = (
    caseId: string,
    entryId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/client-trust/${entryId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clientTrust', caseId] });
            toast({
                title: "Success",
                description: "Client trust entry deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch client trust summary
 */
export const useClientTrustSummaryQuery = (caseId: string): UseQueryResult<ClientTrustSummary> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['clientTrustSummary', caseId],
        queryFn: async () => {
            const { data } = await axios.get(
                `${BASE_URL}/cases/${caseId}/client-trust/summary/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

export const useThirdPartySettlementQuery = (caseId: string) => {
    return useQuery({
        queryKey: ['thirdPartySettlement', caseId],
        queryFn: async () => {
            const response = await axios.get(`/api/case-management/${caseId}/settlement/third-party`);
            return response.data;
        },
        enabled: !!caseId
    });
};
