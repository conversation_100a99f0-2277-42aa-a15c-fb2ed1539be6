import { useMutation, UseMutationResult, useQ<PERSON>y, UseQueryResult, useQueryClient, useInfiniteQuery, QueryFunctionContext } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { Task, TaskWrite, ListResponse, TaskFilters } from "@/type/case-management/noteTaskTypes";

const TASK_MANAGEMENT_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/task-management';

/**
 * Hook to fetch categorized tasks for the dashboard
 * @param viewType - The type of view (my_tasks, tagged, created, all)
 * @param filters - Additional filters to apply
 * @returns Query result containing categorized tasks
 */
export const useCategorizedTasksQuery = (
    viewType: 'my_tasks' | 'tagged' | 'created' | 'all' = 'my_tasks',
    filters: Partial<TaskFilters> = {}
): UseQueryResult<{
    overdue: Task[];
    today: Task[];
    this_week: Task[];
    later: Task[];
    no_due_date: Task[];
    summary: {
        total: number;
        pending: number;
        in_progress: number;
        completed: number;
        reopened: number;
    };
}> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['categorized-tasks', viewType, filters],
        queryFn: async () => {
            console.log('Fetching categorized tasks with params:', {
                view: viewType,
                ...filters
            });

            try {
                const response = await axios.get(`${TASK_MANAGEMENT_URL}/tasks/categorized_dashboard/`, {
                    params: {
                        view: viewType,
                        ...filters,
                        include_case_details: true,
                        include_full_case_details: true,
                        include_case_name: true
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                });

                // Process the response to ensure case_details is properly populated
                const processedData = {
                    ...response.data,
                };

                // Process each category of tasks
                ['overdue', 'today', 'this_week', 'later', 'no_due_date'].forEach(category => {
                    if (response.data[category] && Array.isArray(response.data[category])) {
                        processedData[category] = response.data[category].map((task: Task) => {
                            // If task has case_name directly but no case_details, create case_details from it
                            if (task.case && task.case_name && !task.case_details) {
                                return {
                                    ...task,
                                    case_details: {
                                        status: 'unknown',
                                        name: task.case_name,
                                        case_name: task.case_name
                                    }
                                };
                            }
                            // If task has a case but no case_details and no case_name, create a minimal case_details object
                            else if (task.case && !task.case_details) {
                                return {
                                    ...task,
                                    case_details: {
                                        status: 'unknown',
                                        name: `Case ${task.case}`,
                                        case_name: `Case ${task.case}`
                                    }
                                };
                            }
                            return task;
                        });
                    }
                });

                console.log('Processed categorized tasks response:', processedData);
                return processedData;
            } catch (error) {
                console.error('Error fetching categorized tasks:', error);
                throw error;
            }
        },
        staleTime: 30 * 1000, // 30 seconds
        enabled: !!accessToken
    });
};

/**
 * Hook to quickly update a task's status
 * @returns Mutation function to update a task's status
 */
export const useQuickStatusUpdateMutation = (): UseMutationResult<
    Task,
    Error,
    { taskId: number; status: string }
> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ taskId, status }) => {
            const response = await axios.patch(
                `${TASK_MANAGEMENT_URL}/tasks/${taskId}/quick_status/`,
                { status },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            // Invalidate all task-related queries to ensure UI updates
            queryClient.invalidateQueries({ queryKey: ['categorized-tasks'] });
            queryClient.invalidateQueries({ queryKey: ['tasks'] });
            queryClient.invalidateQueries({ queryKey: ['filtered-tasks'] });
            queryClient.invalidateQueries({ queryKey: ['tasks-filtered'] });
            queryClient.invalidateQueries({ queryKey: ['all-tasks'] });

            toast({
                title: "Success",
                description: "Task status updated successfully",
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: error.message || "Failed to update task status",
                variant: "destructive"
            });
        }
    });
};

/**
 * Hook to update multiple tasks at once
 * @returns Mutation function to update multiple tasks
 */
export const useBulkUpdateTasksMutation = (): UseMutationResult<
    { detail: string; updated_count: number; total_count: number },
    Error,
    { taskIds: number[]; updates: Partial<TaskWrite> }
> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ taskIds, updates }) => {
            const response = await axios.post(
                `${TASK_MANAGEMENT_URL}/tasks/bulk_update/`,
                {
                    task_ids: taskIds,
                    updates
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            // Invalidate all task-related queries to ensure UI updates
            queryClient.invalidateQueries({ queryKey: ['categorized-tasks'] });
            queryClient.invalidateQueries({ queryKey: ['tasks'] });
            queryClient.invalidateQueries({ queryKey: ['filtered-tasks'] });
            queryClient.invalidateQueries({ queryKey: ['tasks-filtered'] });
            queryClient.invalidateQueries({ queryKey: ['all-tasks'] });

            toast({
                title: "Success",
                description: data.detail || `Updated ${data.updated_count} of ${data.total_count} tasks`,
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: error.message || "Failed to update tasks",
                variant: "destructive"
            });
        }
    });
};
