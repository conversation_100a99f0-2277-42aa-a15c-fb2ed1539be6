import { useMutation, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

// Constants
const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Types
export interface ClientSearchParams {
  first_name: string;
  last_name: string;
  date_of_birth?: string | null;
  incident_date?: string | null;
}

export interface DefendantSearchParams {
  first_name: string;
  last_name: string;
  date_of_birth?: string | null;
}

export interface MatchingClientCase {
  case_id: string;
  case: string;
  client_name: string;
  date_of_birth: string | null;
}

export interface MatchingDefendantCase {
  case_id: string;
  case: string;
  defendant_name: string;
  date_of_birth: string | null;
}

/**
 * Custom hook to search for matching clients and defendants
 * @returns {UseMutationResult} Mutation result for searching clients and defendants
 */
export const useMatchingCasesMutation = (): UseMutationResult<
  { clients: MatchingClientCase[], defendants: MatchingDefendantCase[] },
  <PERSON><PERSON><PERSON>,
  ClientSearchParams
> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async (searchParams: ClientSearchParams) => {
      // Search for matching clients
      const clientResponse = await axios.post<MatchingClientCase[]>(
        `${BASE_URL}/matching-cases/client_search/`,
        searchParams,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      // Search for matching defendants
      const defendantResponse = await axios.post<MatchingDefendantCase[]>(
        `${BASE_URL}/matching-cases/defendant_search/`,
        {
          first_name: searchParams.first_name,
          last_name: searchParams.last_name,
          date_of_birth: searchParams.date_of_birth
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      return {
        clients: clientResponse.data,
        defendants: defendantResponse.data
      };
    }
  });
}; 