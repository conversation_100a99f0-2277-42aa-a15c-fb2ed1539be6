import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ImpactAssessment, ImpactAssessmentFormData } from "@/type/case-management/impactAssessmentTypes";
import axios from '@/lib/axios';

const IMPACT_ASSESSMENT_ENDPOINT = (caseId: string) => 
  `${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/impact-assessment/`;

export const useImpactAssessmentQuery = (caseId: string) => {
  return useQuery<ImpactAssessment>({
    queryKey: ["impactAssessment", caseId],
    queryFn: async () => {
      const response = await axios.get(IMPACT_ASSESSMENT_ENDPOINT(caseId));
      return response.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useCreateImpactAssessmentMutation = (caseId: string) => {
  const queryClient = useQueryClient();

  return useMutation<
    ImpactAssessment,
    Error,
    ImpactAssessmentFormData
  >({
    mutationFn: async (data: ImpactAssessmentFormData) => {
      const response = await axios.post(
        IMPACT_ASSESSMENT_ENDPOINT(caseId),
        data
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(["impactAssessment", caseId], data);
      queryClient.invalidateQueries({
        queryKey: ["impactAssessment", caseId],
      });
    },
  });
};

export const useUpdateImpactAssessmentMutation = (caseId: string) => {
  const queryClient = useQueryClient();

  return useMutation<
    ImpactAssessment,
    Error,
    ImpactAssessmentFormData
  >({
    mutationFn: async (data: ImpactAssessmentFormData) => {
      const response = await axios.put(
        IMPACT_ASSESSMENT_ENDPOINT(caseId),
        data
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(["impactAssessment", caseId], data);
      queryClient.invalidateQueries({
        queryKey: ["impactAssessment", caseId],
      });
    },
  });
};
