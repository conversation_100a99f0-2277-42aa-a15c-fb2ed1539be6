import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import {
    ClientBasicDetails,
    ClientContactDetails,
    ClientInsurance,
    ClientInsuranceCreateRequest,
    ClientInsuranceUpdateRequest,
    ClientPropertyDamageCreateRequest,
    ClientSyncRequest,
    ClientSyncResponse,
} from '@/type/case-management/clientDetailTypes';
import {
  ClientPropertyDamageUpdateRequest,
    InsuranceAdjusterDetails,
    InsuranceAdjusterRequest,
    InsuranceAdjusterResponse,
    InsuranceLegalRepresentationResponse,
    InsuranceLegalRepresentationUpdateRequest
} from '@/type/case-management/insuranceTypes';
import {
  setClientBasicDetails,
  setIsLoading,
  setError,
  updateClientBasicDetails,
  setClientContactDetails,
  setContactDetailsLoading,
  setContactDetailsError,
  updateClientContactDetails,
} from '@/store/slices/case-management/clientDetailSlice';
import { useToast } from '@/hooks/use-toast';
import {
    ClientPropertyDamage
} from '@/type/case-management/insuranceTypes';

// Note: For organization-wide APIs (Law Firms, Attorneys, Adjusters, Insurance Companies),
// import from '@/services/orgAPIs'

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);

/**
 * Custom hook to fetch client basic details
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<ClientBasicDetails>} Query result containing:
 * - data: Client basic details when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useClientBasicDetailsQuery = (caseId: string): UseQueryResult<ClientBasicDetails> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();

  return useQuery({
    queryKey: ['clientBasicDetails', caseId],
    queryFn: async () => {
      if (!caseId) throw new Error('Case ID is required');
      dispatch(setIsLoading(true));

      const { data } = await axios.get<ClientBasicDetails>(
        `${BASE_URL}/cases/${caseId}/client-basic-details/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      dispatch(setClientBasicDetails(data));
      dispatch(setError(null));
      return data;
    },
    enabled: !!caseId,
  });
};

/**
 * Custom hook to update client basic details
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult<ClientBasicDetails, Error, Partial<ClientBasicDetails>>} Mutation result containing:
 * - mutate: Function to trigger the client details update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated client details when successful
 * - other standard react-query properties
 */
export const useUpdateClientBasicDetailsMutation = (caseId: string): UseMutationResult<
  ClientBasicDetails,
  Error,
  Partial<ClientBasicDetails>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();
  const dispatch = useDispatch();

  return useMutation({
    mutationFn: async (clientData: Partial<ClientBasicDetails>) => {
      dispatch(setIsLoading(true));
      dispatch(setError(null));

      const { data } = await axios.put<ClientBasicDetails>(
        `${BASE_URL}/cases/${caseId}/client-basic-details/`,
        clientData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedClient) => {
      queryClient.invalidateQueries({ queryKey: ['clientBasicDetails', caseId] });
      dispatch(updateClientBasicDetails(updatedClient));
      toast({
        title: "Success",
        description: "Client details updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch client contact details
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<ClientContactDetails>} Query result containing:
 * - data: Client contact details when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useClientContactDetailsQuery = (caseId: string): UseQueryResult<ClientContactDetails> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();

  return useQuery({
    queryKey: ['clientContactDetails', caseId],
    queryFn: async () => {
      if (!caseId) throw new Error('Case ID is required');
      dispatch(setContactDetailsLoading(true));

      const { data } = await axios.get<ClientContactDetails>(
        `${BASE_URL}/cases/${caseId}/client-contact-details/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );

      dispatch(setClientContactDetails(data));
      dispatch(setContactDetailsError(null));
      return data;
    },
    enabled: !!caseId
  });
};

/**
 * Custom hook to update client contact details
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult<ClientContactDetails, Error, Partial<ClientContactDetails>>} Mutation result containing:
 * - mutate: Function to trigger the contact details update
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Updated contact details when successful
 * - other standard react-query properties
 */
// TODO: Review this mutation @azuzul
export const useUpdateClientContactDetailsMutation = (caseId: string): UseMutationResult<
  ClientContactDetails,
  Error,
  Partial<ClientContactDetails>
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();
  const dispatch = useDispatch();

  return useMutation({
    mutationFn: async (contactData: Partial<ClientContactDetails>) => {
      dispatch(setContactDetailsLoading(true));
      dispatch(setContactDetailsError(null));

      const { data } = await axios.put<ClientContactDetails>(
        `${BASE_URL}/cases/${caseId}/client-contact-details/`,
        contactData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (updatedContact) => {
      queryClient.invalidateQueries({ queryKey: ['clientContactDetails', caseId] });
      dispatch(updateClientContactDetails(updatedContact));
      toast({
        title: "Success",
        description: "Client contact details updated successfully",
        variant: "default"
      });
    },
    onSettled: () => {
      dispatch(setContactDetailsLoading(false));
    }
  });
};

/**
 * Custom hook to fetch all insurance records for a specific case
 * @param {string} caseId - The ID of the case to fetch insurance records for
 * @returns {UseQueryResult<ClientInsuranceListResponse>} Query result containing:
 * - data: List of insurance records when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useClientInsuranceListQuery = (
    caseId: string,
    options?: { enabled?: boolean }
): UseQueryResult<ClientInsurance[]> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['clientInsurance', caseId],
    queryFn: async () => {
      const { data } = await axios.get<ClientInsurance>(
        `${BASE_URL}/cases/${caseId}/insurances/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: options?.enabled !== false,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Custom hook to fetch a specific insurance record
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance record
 * @returns {UseQueryResult<ClientInsurance>} Query result containing:
 * - data: Insurance record details when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useClientInsuranceQuery = (
  caseId: string,
  insuranceId: string
): UseQueryResult<ClientInsurance> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['clientInsurance', caseId, insuranceId],
    queryFn: async () => {
      const { data } = await axios.get<ClientInsurance>(
        `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId && !!insuranceId,
  });
};

/**
 * Custom hook to create a new insurance record
 * @param {string} caseId - The ID of the case to create insurance for
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the insurance creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - other standard react-query properties
 */
export const useCreateClientInsuranceMutation = (
  caseId: string
): UseMutationResult<ClientInsurance, Error, ClientInsuranceCreateRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (insuranceData: ClientInsuranceCreateRequest) => {
      const { data } = await axios.post<ClientInsurance>(
        `${BASE_URL}/cases/${caseId}/insurances/`,
        insuranceData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clientInsurance', caseId] });
      toast({
        title: "Success",
        description: "Insurance record created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update client insurance details
 * @param caseId - The ID of the case
 * @param insuranceId - The ID of the insurance to update
 */
export const useUpdateClientInsuranceMutation = (
    caseId: string,
    insuranceId: string
): UseMutationResult<ClientInsurance, Error, ClientInsuranceUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (insuranceData: ClientInsuranceUpdateRequest) => {
            const { data } = await axios.put<ClientInsurance>(
                `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/`,
                insuranceData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['clientInsurance', caseId]
            });
            toast({
                title: "Success",
                description: "Insurance details updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete an insurance record
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the insurance deletion
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - other standard react-query properties
 */
export const useDeleteClientInsuranceMutation = (): UseMutationResult<
  void,
  Error,
  { caseId: string; insuranceId: string }
> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ caseId, insuranceId }) => {
      await axios.delete(
        `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: (_, { caseId }) => {
      queryClient.invalidateQueries({ queryKey: ['clientInsurance', caseId] });
    }
  });
};

/**
 * Custom hook to fetch insurance adjuster details
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseQueryResult<InsuranceAdjusterResponse>} Query result containing adjuster details
 */
export const useInsuranceAdjusterDetailsQuery = (
  caseId: string,
  insuranceId: string,
  options?: { enabled?: boolean }
): UseQueryResult<InsuranceAdjusterResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['insuranceAdjusterDetails', caseId, insuranceId],
    queryFn: async () => {
      // Check if insuranceId is empty or undefined
      if (!insuranceId) {
        throw new Error('Insurance ID is required');
      }

      const { data } = await axios.get<InsuranceAdjusterResponse>(
        `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/adjusters/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    // Only enable the query if caseId and insuranceId are both valid
    enabled: options?.enabled !== false && !!caseId && !!insuranceId && insuranceId.trim() !== '',
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create insurance adjuster details
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseMutationResult} Mutation result for creating adjuster details
 */
export const useCreateInsuranceAdjusterMutation = (
  caseId: string,
  insuranceId: string
): UseMutationResult<InsuranceAdjusterDetails, Error, InsuranceAdjusterRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: InsuranceAdjusterRequest) => {
      // Validate required parameters
      if (!caseId) {
        throw new Error('Case ID is required');
      }
      if (!insuranceId || insuranceId.trim() === '') {
        throw new Error('Insurance ID is required');
      }

      const response = await axios.post<InsuranceAdjusterDetails>(
        `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/adjusters/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['insuranceAdjusterDetails', caseId, insuranceId]
      });
      toast({
        title: "Success",
        description: "Insurance adjuster details created successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to update insurance adjuster details
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseMutationResult} Mutation result for updating adjuster details
 */
export const useUpdateInsuranceAdjusterMutation = (
  caseId: string,
  insuranceId: string
): UseMutationResult<InsuranceAdjusterDetails, Error, InsuranceAdjusterRequest> => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: InsuranceAdjusterRequest) => {
      // Validate required parameters
      if (!caseId) {
        throw new Error('Case ID is required');
      }
      if (!insuranceId || insuranceId.trim() === '') {
        throw new Error('Insurance ID is required');
      }

      const response = await axios.put<InsuranceAdjusterDetails>(
        `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/adjusters/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['insuranceAdjusterDetails', caseId, insuranceId]
      });
      toast({
        title: "Success",
        description: "Insurance adjuster details updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to fetch insurance legal representation details
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<InsuranceLegalRepresentationResponse>} Query result containing:
 * - data: Legal representation details when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useInsuranceLegalRepresentationQuery = (
    caseId: string,
    insuranceId: string,
    options?: { enabled?: boolean }
): UseQueryResult<InsuranceLegalRepresentationResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['insuranceLegalRepresentation', caseId, insuranceId],
        queryFn: async () => {
            const { data } = await axios.get<InsuranceLegalRepresentationResponse[]>(
                `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/legal-representation/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data[0] || null;
        },
        enabled: options?.enabled !== false && Boolean(caseId) && Boolean(insuranceId),
        retry: RETRY_COUNT,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create insurance legal representation
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseMutationResult<InsuranceLegalRepresentationResponse, Error, InsuranceLegalRepresentationUpdateRequest>} Mutation result containing:
 * - mutate: Function to trigger the creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created legal representation data when successful
 */
export const useCreateInsuranceLegalRepresentationMutation = (
    caseId: string,
    insuranceId: string
): UseMutationResult<
    InsuranceLegalRepresentationResponse,
    Error,
    InsuranceLegalRepresentationUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: InsuranceLegalRepresentationUpdateRequest) => {
            if (!caseId || !insuranceId) {
                throw new Error('Case ID and Insurance ID are required');
            }

            const response = await axios.post<InsuranceLegalRepresentationResponse>(
                `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/legal-representation/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['insuranceLegalRepresentation', caseId, insuranceId]
            });
            toast({
                title: "Legal Representation Created",
                description: "Legal representation has been successfully created",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch property damage details
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<ClientPropertyDamage[]>} Query result containing:
 * - data: List of property damage details when successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useClientPropertyDamageQuery = (caseId: string): UseQueryResult<ClientPropertyDamage> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['clientPropertyDamage', caseId],
    queryFn: async () => {
      const { data } = await axios.get<ClientPropertyDamage>(
        `${BASE_URL}/cases/${caseId}/client-property-damage/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to create a new property damage record
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result containing:
 * - mutate: Function to trigger the property damage creation
 * - isLoading: Boolean indicating if the mutation is in progress
 * - error: Error object if the mutation fails
 * - data: Created property damage data when successful
 */
export const useCreatePropertyDamageMutation = (caseId: string): UseMutationResult<
    ClientPropertyDamage,
    Error,
    ClientPropertyDamageCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ClientPropertyDamageCreateRequest) => {
            const response = await axios.post<ClientPropertyDamage>(
                `${BASE_URL}/cases/${caseId}/client-property-damage/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clientPropertyDamage', caseId] });
            toast({
                title: "Success",
                description: "Property damage record created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch insurance adjuster details for a specific insurance
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @param {Object} options - Query options
 * @param {boolean} options.enabled - Whether the query should automatically run
 * @returns {UseQueryResult<InsuranceAdjusterResponse>} Query result containing adjuster details
 */
export const useInsuranceAdjusterQuery = (
  caseId: string,
  insuranceId: string,
  options?: { enabled?: boolean }
): UseQueryResult<InsuranceAdjusterResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['insuranceAdjuster', caseId, insuranceId],
    queryFn: async () => {
      // Check if insuranceId is empty or undefined
      if (!insuranceId) {
        throw new Error('Insurance ID is required');
      }

      const { data } = await axios.get<InsuranceAdjusterResponse>(
        `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/adjusters/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    // Only enable the query if caseId and insuranceId are both valid
    enabled: options?.enabled !== false && !!caseId && !!insuranceId && insuranceId.trim() !== '',
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to update a property damage record
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for updating property damage
 */
export const useUpdatePropertyDamageMutation = (caseId: string): UseMutationResult<
    ClientPropertyDamage,
    Error,
    ClientPropertyDamageUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ClientPropertyDamageUpdateRequest) => {
            const response = await axios.put<ClientPropertyDamage>(
                `${BASE_URL}/cases/${caseId}/client-property-damage/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clientPropertyDamage', caseId] });
            toast({
                title: "Success",
                description: "Property damage record updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update insurance legal representation
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the insurance
 * @param {string} representationId - The ID of the legal representation
 * @returns {UseMutationResult} Mutation result for updating legal representation
 */
export const useUpdateInsuranceLegalRepresentationMutation = (
    caseId: string,
    insuranceId: string,
    representationId: string
): UseMutationResult<
    InsuranceLegalRepresentationResponse,
    Error,
    InsuranceLegalRepresentationUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: InsuranceLegalRepresentationUpdateRequest) => {
            const response = await axios.put<InsuranceLegalRepresentationResponse>(
                `${BASE_URL}/cases/${caseId}/insurances/${insuranceId}/legal-representation/${representationId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['insuranceLegalRepresentation', caseId, insuranceId]
            });
            toast({
                title: "Success",
                description: "Legal representation updated successfully",
                variant: "default"
            });
        }
    });
};

export const useUpdateClientEmploymentStatusMutation = (caseId: string) => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (employed: boolean) => {
      const { data } = await axios.patch<ClientBasicDetails>(
        `${BASE_URL}/cases/${caseId}/client-basic-details/`,
        { employed },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clientBasicDetails', caseId] });
      queryClient.invalidateQueries({ queryKey: ['cards', caseId] });
      toast({
        title: "Success",
        description: "Employment status updated successfully",
        variant: "default"
      });
    }
  });
};

/**
 * Custom hook to sync client data across cases
 */
export const useSyncClientMutation = (
    caseId: string
): UseMutationResult<ClientSyncResponse, Error, ClientSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: ClientSyncRequest) => {
            const response = await axios.post<ClientSyncResponse>(
                `${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['client-basic-details', caseId] });
            queryClient.invalidateQueries({ queryKey: ['client-contact-details', caseId] });
            queryClient.invalidateQueries({ queryKey: ['client-insurances', caseId] });
            queryClient.invalidateQueries({ queryKey: ['client-property-damage', caseId] });

            toast({
                title: "Success",
                description: "Client data synced successfully",
                variant: "default"
            });
        }
    });
};

export const useUpdateMinorFeeSignedMutation = (caseId: string) => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (minorFeeSigned: boolean) => {
      const { data } = await axios.patch<ClientBasicDetails>(
        `${BASE_URL}/cases/${caseId}/client-basic-details/`,
        { minor_fee_signed: minorFeeSigned },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clientBasicDetails', caseId] });
      toast({
        title: "Success",
        description: "Minor fee signed status updated successfully",
        variant: "default"
      });
    }
  });
};

