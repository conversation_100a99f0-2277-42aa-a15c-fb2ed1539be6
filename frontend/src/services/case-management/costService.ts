import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from '@/lib/axios';
import { CaseCost, CaseCostCreateRequest, CaseCostListResponse, CaseCostUpdateRequest, CostFor } from "@/type/case-management/costTypes";
import { useToast } from "@/hooks/use-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2;
const CASE_COSTS_KEY = "case-costs";

export const useCaseCostsQuery = (caseId: string, costFor: CostFor = CostFor.CASE_COST, searchParams?: { page: number; page_size: number; search?: string }) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [CASE_COSTS_KEY, caseId, costFor, searchParams],
        queryFn: async () => {
            const { data } = await axios.get<CaseCostListResponse>(
                `${BASE_URL}/case-management/cases/${caseId}/costs/`,
                {
                    params: {
                        cost_for: costFor,
                        page: searchParams?.page,
                        page_size: searchParams?.page_size,
                        search: searchParams?.search
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

export const useCaseCostQuery = (caseId: string, costId: string, costFor: CostFor = CostFor.CASE_COST) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [CASE_COSTS_KEY, caseId, costId, costFor],
        queryFn: async () => {
            const { data } = await axios.get<CaseCost>(
                `${BASE_URL}/case-management/cases/${caseId}/costs/${costId}/`,
                {
                    params: {
                        cost_for: costFor
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId && !!costId,
    });
};

export const useCreateCaseCostMutation = (caseId: string, costFor: CostFor = CostFor.CASE_COST) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (newCost: CaseCostCreateRequest) => {
            const { data } = await axios.post<CaseCost>(
                `${BASE_URL}/case-management/cases/${caseId}/costs/`,
                {
                    ...newCost,
                    priority: newCost.priority?.toUpperCase(),
                    status: newCost.status?.toUpperCase(),
                    cost_for: costFor
                },
                {
                    params: {
                        cost_for: costFor
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_COSTS_KEY, caseId, costFor],
            });
            toast({
                title: "Success",
                description: "Cost created successfully",
            });
        },
    });
};

export const useUpdateCaseCostMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ caseId, costId, data, costFor = CostFor.CASE_COST }: { 
            caseId: string; 
            costId: string; 
            data: CaseCostUpdateRequest;
            costFor?: CostFor;
        }) => {
            const response = await axios.patch<CaseCost>(
                `${BASE_URL}/case-management/cases/${caseId}/costs/${costId}/`,
                {
                    ...data,
                    priority: data.priority?.toUpperCase(),
                    status: data.status?.toUpperCase(),
                    cost_for: costFor
                },
                {
                    params: {
                        cost_for: costFor
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({
                queryKey: [CASE_COSTS_KEY, variables.caseId, variables.costFor],
            });
            toast({
                title: "Success",
                description: "Cost updated successfully",
            });
        },
    });
};

export const useDeleteCaseCostMutation = (caseId: string, costId: string, costFor: CostFor = CostFor.CASE_COST) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/case-management/cases/${caseId}/costs/${costId}/`,
                {
                    params: {
                        cost_for: costFor
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_COSTS_KEY, caseId, costFor],
            });
            toast({
                title: "Success",
                description: "Cost deleted successfully",
            });
        },
    });
}; 