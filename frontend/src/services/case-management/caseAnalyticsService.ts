import { useQuery, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  CaseReportFilters,
  CaseListResponse,
  CaseStatusResponse,
  CheckDepositsResponse,
  ClientsResponse,
  DefendantTypesResponse,
  HealthProvidersResponse,
  InsuranceAdjustersResponse,
  InsuranceCompaniesResponse,
  LienHoldersResponse,
  MedpayRequestsResponse,
  NotesResponse,
  SourcePerformanceResponse,
  MedicalRequestsResponse,
  StatuteResponse,
  DemandsResponse,
  SettlementResponse,
  SettlementManagementResponse,
  LitigationManagementResponse,
  ClientTrustResponse,
  CaseReviewResponse,
  StatisticsResponse,
  ClientTrustConsolidatedResponse,
  CaseCostResponse,
  ContactsResponse,
  CaseEventsCalendarResponse,
  EstimatedValueResponse,
  MedpayDepositsResponse,
} from '@/type/case-management/caseAnalyticsTypes';
// Constants
const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';
const RETRY_COUNT = Number(process.env.RETRY_COUNT || 2);
const STALE_TIME = Number(process.env.STALE_TIME || 5 * 60 * 1000);

export const getReportEndpoint = (reportType: string): string => {
  const endpoints: Record<string, string> = {
    caseList: 'case_list',
    caseListClosed: 'case_list_closed',
    caseListRejected: 'case_list_rejected',
    caseListDropped: 'case_list_dropped',
    caseStatus: 'case_status',
    statistics: 'statistics',
    checkDeposits: 'check_deposits',
    clients: 'clients',
    clientsClosed: 'closed_cases_for_clients',
    clientsRejected: 'rejected_cases_for_clients',
    clientsOpen: 'open_cases_for_clients',
    clientsReferredIn: 'referred_in_cases_for_clients',
    clientsSubbedOut: 'subbed_out_cases_for_clients',
    clientsIntakeReady: 'intake_ready_cases_for_clients',
    clientsRetained: 'client_retained_cases_for_clients',
    defendantTypes: 'defendant_types',
    healthProviders: 'health_providers',
    insuranceAdjusters: 'insurance_adjusters',
    insuranceCompanies: 'insurance_companies',
    lienHolders: 'lien_holders',
    medpayRequests: 'medpay_requests',
    medpayDeposits: 'medpay_deposits',
    notes: 'notes',
    sourcePerformance: 'source_performance',
    medicalRequests: 'medical_requests',
    statute: 'statute',
    demands: 'demands',
    settlement: 'settlement',
    settlementManagement: 'settlement_management',
    litigationManagement: 'litigation_management',
    clientTrust: 'client_trust',
    clientTrustAttorneyFees: 'client_trust_attorney_fees',
    clientTrustIssuedPayments: 'client_trust_issued_payments',
    clientTrustCheckDeposits: 'client_trust_check_deposits',
    clientTrustConsolidated: 'client_trust_consolidated',
    caseReview: 'case_review',
    caseCosts: 'case_costs',
    contacts: 'all_contacts',
    caseEventsCalendar: 'case_events_calendar',
    estimatedValue: 'estimated_value_report',
  };
  return endpoints[reportType] || reportType;
};

// Helper function to convert filters object to query string
const filtersToQueryString = (filters?: CaseReportFilters): string => {
  if (!filters) return '';

  const queryParams = new URLSearchParams();

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return queryParams.toString();
};

/**
 * Custom hook to fetch case list report
 */
export const useCaseListQuery = (filters?: CaseReportFilters) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseListResponse>({
    queryKey: ['caseList', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseList');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseListResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch closed cases list report
 */
export const useCaseListClosedQuery = (filters?: CaseReportFilters) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseListResponse>({
    queryKey: ['caseListClosed', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseListClosed');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseListResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch rejected cases list report
 */
export const useCaseListRejectedQuery = (filters?: CaseReportFilters) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseListResponse>({
    queryKey: ['caseListRejected', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseListRejected');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseListResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch dropped cases list report
 */
export const useCaseListDroppedQuery = (filters?: CaseReportFilters) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseListResponse>({
    queryKey: ['caseListDropped', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseListDropped');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseListResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch case status report
 */
export const useCaseStatusQuery = (filters?: CaseReportFilters) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseStatusResponse>({
    queryKey: ['caseStatus', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseStatus');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseStatusResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch case statistics report
 */
export const useCaseStatisticsQuery = (filters?: CaseReportFilters): UseQueryResult<StatisticsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<StatisticsResponse>({
    queryKey: ['caseStatistics', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('statistics');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<StatisticsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch check deposits report
 */
export const useCheckDepositsQuery = (filters?: CaseReportFilters): UseQueryResult<CheckDepositsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CheckDepositsResponse>({
    queryKey: ['checkDeposits', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('checkDeposits');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CheckDepositsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch clients report
 */
export const useClientsQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clients', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clients');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch closed cases for clients report
 */
export const useClientsClosedQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsClosed', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsClosed');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch rejected cases for clients report
 */
export const useClientsRejectedQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsRejected', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsRejected');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch open cases for clients report
 */
export const useClientsOpenQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsOpen', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsOpen');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch referred in cases for clients report
 */
export const useClientsReferredInQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsReferredIn', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsReferredIn');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch subbed out cases for clients report
 */
export const useClientsSubbedOutQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsSubbedOut', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsSubbedOut');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch intake ready cases for clients report
 */
export const useClientsIntakeReadyQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsIntakeReady', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsIntakeReady');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch retained cases for clients report
 */
export const useClientsRetainedQuery = (filters?: CaseReportFilters): UseQueryResult<ClientsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientsResponse>({
    queryKey: ['clientsRetained', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientsRetained');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch defendant types report
 */
export const useDefendantTypesQuery = (filters?: CaseReportFilters): UseQueryResult<DefendantTypesResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<DefendantTypesResponse>({
    queryKey: ['defendantTypes', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('defendantTypes');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<DefendantTypesResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch health providers report
 */
export const useHealthProvidersQuery = (filters?: CaseReportFilters): UseQueryResult<HealthProvidersResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<HealthProvidersResponse>({
    queryKey: ['healthProviders', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('healthProviders');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<HealthProvidersResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch insurance adjusters report
 */
export const useInsuranceAdjustersQuery = (filters?: CaseReportFilters): UseQueryResult<InsuranceAdjustersResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<InsuranceAdjustersResponse>({
    queryKey: ['insuranceAdjusters', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('insuranceAdjusters');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<InsuranceAdjustersResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch insurance companies report
 */
export const useInsuranceCompaniesQuery = (filters?: CaseReportFilters): UseQueryResult<InsuranceCompaniesResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<InsuranceCompaniesResponse>({
    queryKey: ['insuranceCompanies', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('insuranceCompanies');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<InsuranceCompaniesResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch lien holders report
 */
export const useLienHoldersQuery = (filters?: CaseReportFilters): UseQueryResult<LienHoldersResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<LienHoldersResponse>({
    queryKey: ['lienHolders', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('lienHolders');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<LienHoldersResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch medpay requests report
 */
export const useMedpayRequestsQuery = (filters?: CaseReportFilters): UseQueryResult<MedpayRequestsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<MedpayRequestsResponse>({
    queryKey: ['medpayRequests', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('medpayRequests');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<MedpayRequestsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch notes report
 */
export const useNotesQuery = (filters?: CaseReportFilters): UseQueryResult<NotesResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<NotesResponse>({
    queryKey: ['notes', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('notes');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<NotesResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch source performance report
 */
export const useSourcePerformanceQuery = (filters?: CaseReportFilters): UseQueryResult<SourcePerformanceResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<SourcePerformanceResponse>({
    queryKey: ['sourcePerformance', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('sourcePerformance');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<SourcePerformanceResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch medical requests report
 */
export const useMedicalRequestsQuery = (filters?: CaseReportFilters): UseQueryResult<MedicalRequestsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<MedicalRequestsResponse>({
    queryKey: ['medicalRequests', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('medicalRequests');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<MedicalRequestsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch statute report
 */
export const useStatuteQuery = (filters?: CaseReportFilters): UseQueryResult<StatuteResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<StatuteResponse>({
    queryKey: ['statute', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('statute');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<StatuteResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch demands report
 */
export const useDemandsQuery = (filters?: CaseReportFilters): UseQueryResult<DemandsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<DemandsResponse>({
    queryKey: ['demands', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('demands');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<DemandsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch settlement report
 */
export const useSettlementQuery = (filters?: CaseReportFilters): UseQueryResult<SettlementResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<SettlementResponse>({
    queryKey: ['settlement', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('settlement');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<SettlementResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch settlement management report
 */
export const useSettlementManagementQuery = (filters?: CaseReportFilters): UseQueryResult<SettlementManagementResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<SettlementManagementResponse>({
    queryKey: ['settlementManagement', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('settlementManagement');
      const url = `${BASE_URL}/reports/management-reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<SettlementManagementResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch litigation management report
 */
export const useLitigationManagementQuery = (filters?: CaseReportFilters): UseQueryResult<LitigationManagementResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<LitigationManagementResponse>({
    queryKey: ['litigationManagement', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('litigationManagement');
      const url = `${BASE_URL}/reports/management-reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<LitigationManagementResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch client trust report
 */
export const useClientTrustQuery = (filters?: CaseReportFilters): UseQueryResult<ClientTrustResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientTrustResponse>({
    queryKey: ['clientTrust', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientTrust');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientTrustResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch client trust attorney fees report
 */
export const useClientTrustAttorneyFeesQuery = (filters?: CaseReportFilters): UseQueryResult<ClientTrustResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientTrustResponse>({
    queryKey: ['clientTrustAttorneyFees', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientTrustAttorneyFees');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientTrustResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch client trust issued payments report
 */
export const useClientTrustIssuedPaymentsQuery = (filters?: CaseReportFilters): UseQueryResult<ClientTrustResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientTrustResponse>({
    queryKey: ['clientTrustIssuedPayments', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientTrustIssuedPayments');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientTrustResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch consolidated client trust report
 */
export const useClientTrustConsolidatedQuery = (filters?: CaseReportFilters): UseQueryResult<ClientTrustConsolidatedResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ClientTrustConsolidatedResponse>({
    queryKey: ['clientTrustConsolidated', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientTrustConsolidated');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<ClientTrustConsolidatedResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

export const useCaseReviewQuery = (filters?: CaseReportFilters): UseQueryResult<CaseReviewResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseReviewResponse>({
    queryKey: ['caseReview', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseReview');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseReviewResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch case costs report
 */
export const useCaseCostsQuery = (filters?: CaseReportFilters): UseQueryResult<CaseCostResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseCostResponse>({
    queryKey: ['caseCosts', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseCosts');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseCostResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch contacts report
 */
export const useContactsQuery = (filters?: CaseReportFilters): UseQueryResult<ContactsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<ContactsResponse>({
    queryKey: ['contacts', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const url = `${BASE_URL}/reports/contacts/all_contacts/?${queryString}`;

      const { data } = await axios.get<ContactsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch case events calendar report
 */
export const useCaseEventsCalendarQuery = (filters?: CaseReportFilters): UseQueryResult<CaseEventsCalendarResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<CaseEventsCalendarResponse>({
    queryKey: ['caseEventsCalendar', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('caseEventsCalendar');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<CaseEventsCalendarResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch client trust check deposits report
 */
export const useClientTrustCheckDepositsQuery = (filters?: CaseReportFilters): UseQueryResult<ClientTrustResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useQuery<ClientTrustResponse>({
    queryKey: ['clientTrustCheckDeposits', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('clientTrustCheckDeposits');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;
      
      const { data } = await axios.get<ClientTrustResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });
      
      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch estimated value report
 */
export const useEstimatedValueQuery = (filters?: CaseReportFilters): UseQueryResult<EstimatedValueResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useQuery<EstimatedValueResponse>({
    queryKey: ['estimatedValue', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('estimatedValue');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;
      
      const { data } = await axios.get<EstimatedValueResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });
      
      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};

/**
 * Custom hook to fetch medpay deposits report
 */
export const useMedpayDepositsQuery = (filters?: CaseReportFilters): UseQueryResult<MedpayDepositsResponse> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery<MedpayDepositsResponse>({
    queryKey: ['medpayDeposits', filters],
    queryFn: async () => {
      const queryString = filtersToQueryString(filters);
      const endpoint = getReportEndpoint('medpayDeposits');
      const url = `${BASE_URL}/reports/${endpoint}/?${queryString}`;

      const { data } = await axios.get<MedpayDepositsResponse>(url, {
        headers: { Authorization: `Bearer ${accessToken}` }
      });

      return data;
    },
    enabled: !!filters,
    retry: RETRY_COUNT,
    staleTime: STALE_TIME,
  });
};
