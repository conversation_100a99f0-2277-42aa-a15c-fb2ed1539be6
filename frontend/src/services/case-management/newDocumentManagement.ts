import { useQuery, useMutation, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/storage';



// Add these new types
export interface CaseRootFolder {
    id: string;
    name: string;
}

export interface CaseContentItem {
    id: string;
    name: string;
    type: 'folder' | 'file';
    size?: number;
    created_at: string | null;
    modified_at: string | null;
    etag?: string | null;
    storage_class?: string | null;
    version_id?: string | null;
}

export interface CaseContentsResponse {
    root_folder: CaseRootFolder;
    contents: CaseContentItem[];
}

// Add new interface for folder content item
export interface FolderContentItem {
    id: string;
    name: string;
    type: 'file' | 'folder';
    size?: number;
    modified?: string;
}

// Add the new query hook for folder contents
export const useFolderContentsQuery = (folderPath: string, options: { enabled: boolean }): UseQueryResult<FolderContentItem[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['folderContents', folderPath],
        queryFn: async () => {
            const encodedPath = encodeURIComponent(folderPath);
            const { data } = await axios.get<FolderContentItem[]>(
                `${BASE_URL}/folder/${encodedPath}/contents/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken && !!folderPath && options.enabled
    });
};

// Add the new query hook
export const useCaseFolderContentsQuery = (caseId: string): UseQueryResult<CaseContentsResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['caseContents', caseId],
        queryFn: async () => {
            const { data } = await axios.get<CaseContentsResponse>(
                `${BASE_URL}/case-contents/${caseId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken && !!caseId
    });
};

// Update the CreateFolderRequest interface
export interface CreateFolderRequest {
  folder_name: string;
  parent_id: string | null; // Optional since root folders don't have a parent
}

// Add the create folder mutation hook
export const useCreateFolderMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (request: CreateFolderRequest) => {
            const { data } = await axios.post(
                `${BASE_URL}/folder/`,
                request,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['folderContents', variables.parent_id]
            });
            
            toast({
                title: "Success",
                description: "Folder created successfully",
            });
        }
    });
};

// Add new interface for file upload request
export interface FileUploadRequest {
  folder_id: string;
  file: File;
}

// Add new interface for file upload response
export interface FileUploadResponse {
  id: string;
  name: string;
  path: string;
  // Add other response fields as needed
}

// Add the file upload mutation hook
export const useFileUploadMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ folder_id, file }: FileUploadRequest) => {
            const formData = new FormData();
            formData.append('folder_id', folder_id);
            formData.append('file', file);

            const { data } = await axios.post<FileUploadResponse>(
                `${BASE_URL}/upload/`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'multipart/form-data',
                    }
                }
            );
            return data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['folderContents', variables.folder_id]
            });
            
            toast({
                title: "Success",
                description: "File uploaded successfully",
            });
        }
    });
};

// Update interfaces for pin file functionality
export interface PinFileRequest {
  case_id: string;
  file_id?: string;
  folder_id?: string;  // Added folder_id for pinning
  file_name: string;
  unpin?: boolean;
}

export interface PinnedFile {
  id: string;
  name: string;
  pinned_at: string;
  pinned_by: string;
  view_url: string;
  download_url: string;
  size?: number;
  modified?: string;
}

export interface PinnedFileResponse {
  message?: string;
  pinned_files: PinnedFile[];
  expires_in_minutes: number;
}

// Update the pin file mutation hook
export const usePinFileMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (request: PinFileRequest) => {
            if (!request.unpin && request.file_id) {
                const fileName = request.file_name.toLowerCase();
                const isImage = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].some(ext => fileName.endsWith(ext));
                if (!isImage) {
                    throw new Error('Only image files can be pinned');
                }
            }

            const { data } = await axios.post<PinnedFileResponse>(
                `${BASE_URL}/pin-file/`,
                request,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['pinnedFile', variables.case_id]
            });
            
            toast({
                title: "Success",
                description: data.message || (variables.unpin ? "File unpinned successfully" : "File pinned successfully"),
            });
        }
    });
};

// Update the get pinned file query hook
export const usePinnedFileQuery = (caseId: string): UseQueryResult<PinnedFileResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['pinnedFile', caseId],
        queryFn: async () => {
            const { data } = await axios.get<PinnedFileResponse>(
                `${BASE_URL}/case/${caseId}/pinned-file/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken && !!caseId
    });
};

// Add new interfaces for file view/download
export interface FileViewLinkResponse {
  view_link: string;
  expires_in_minutes: number;
}

// Add the file download hook
export const useFileDownloadMutation = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken)

    return useMutation({
        mutationFn: async (fileId: string) => {
            console.log('Starting download for fileId:', fileId);
            const response = await axios.get(
                `${BASE_URL}/file/${fileId}/download/`,
                {
                    headers: { 
                        Authorization: `Bearer ${accessToken}`,
                        Accept: '*/*'
                    },
                    responseType: 'blob',
                    maxRedirects: 5,
                    validateStatus: function (status) {
                        return status >= 200 && status < 300;
                    }
                }
            );

            const contentType = response.headers['content-type'] || 'application/octet-stream';
            const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];
            const blob = new Blob([response.data], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            
            let fileName = null;

            if (contentDisposition) {
                const utf8FilenameMatch = /filename\*=UTF-8''([^;]+)/.exec(contentDisposition);
                if (utf8FilenameMatch && utf8FilenameMatch[1]) {
                    fileName = decodeURIComponent(utf8FilenameMatch[1]);
                } else {
                    const patterns = [
                        /filename="([^"]+)"/,
                        /filename=([^;]+)/,
                        /filename\*=([^;]+)/
                    ];
                    
                    for (const pattern of patterns) {
                        const match = pattern.exec(contentDisposition);
                        if (match && match[1]) {
                            fileName = match[1].replace(/["']/g, '').trim();
                            break;
                        }
                    }
                }
            }

            if (!fileName && response.data.name) {
                fileName = response.data.name;
            }

            if (!fileName) {
                const extension = contentType.split('/').pop()?.split(';')[0] || 'bin';
                if (extension === 'vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    fileName = 'document.docx';
                } else if (extension === 'vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                    fileName = 'spreadsheet.xlsx';
                } else {
                    fileName = `document.${extension}`;
                }
            }
                
            link.setAttribute('download', fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

            return { success: true };
        }
    });
};

// Add the get file view link hook
export const useFileViewLinkQuery = (fileId: string, expirationMinutes: number = 60, options: { enabled: boolean } = { enabled: true }) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['fileViewLink', fileId, expirationMinutes],
        queryFn: async () => {
            const { data } = await axios.get<FileViewLinkResponse>(
                `${BASE_URL}/file/${fileId}/view-link/`,
                {
                    params: { expiration_minutes: expirationMinutes },
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken && !!fileId && options.enabled
    });
};

// Add helper function to handle file downloads
export const downloadFile = async (fileId: string, accessToken: string) => {
  try {
    const response = await axios.get(
      `${BASE_URL}/file/${fileId}/download/`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
        responseType: 'blob'
      }
    );

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from Content-Disposition header or use fileId
    const contentDisposition = response.headers['content-disposition'];
    const fileName = contentDisposition
      ? contentDisposition.split('filename=')[1].replace(/"/g, '')
      : fileId;
      
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    return { success: true };
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

// Add helper function to open file in new tab for viewing
export const viewFile = async (fileId: string, accessToken: string, expirationMinutes: number = 60) => {
  try {
    const response = await axios.get<FileViewLinkResponse>(
      `${BASE_URL}/file/${fileId}/view-link/`,
      {
        params: { expiration_minutes: expirationMinutes },
        headers: { Authorization: `Bearer ${accessToken}` }
      }
    );
    
    if (response.data?.view_link) {
      window.open(response.data.view_link, '_blank');
    }
  } catch (error) {
    console.error('Error getting view link:', error);
    throw error;
  }
};

export interface RenameFileRequest {
  file_id: string;
  new_name: string;
}

// add rename file mutation
export const useRenameFileMutation = (file_id: string, accessToken: string) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (request: RenameFileRequest) => {
            const { data } = await axios.post(
                `${BASE_URL}/file/${file_id}/rename/`,
                request,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['folderContents', file_id]
            });
        }
    });
};

// Add new interface for URL upload request
export interface FileUploadFromUrlRequest {
  folder_id: string;
  file_url: string;
}

// Add the file upload from URL mutation hook
export const useFileUploadFromUrlMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (request: FileUploadFromUrlRequest) => {
            const { data } = await axios.post(
                `${BASE_URL}/upload-from-url/`,
                request,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    }
                }
            );
            return data;
        },
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['folderContents', variables.folder_id]
            });
            
            toast({
                title: "Success",
                description: "File uploaded successfully from URL",
            });
        }
    });
};

// Add delete file mutation
export const useDeleteFileMutation = (fileId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            if (!fileId) {
                throw new Error('File ID is required for deletion');
            }
            await axios.delete(
                `${BASE_URL}/file/${fileId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ 
                queryKey: ['folderContents']
            });
            toast({
                title: "Success",
                description: "File deleted successfully",
                variant: "default"
            });
        }
    });
};

// Add delete folder mutation
export const useDeleteFolderMutation = (folderId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            if (!folderId) {
                throw new Error('Folder ID is required for deletion');
            }
            await axios.delete(
                `${BASE_URL}/folder/${folderId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ 
                queryKey: ['folderContents']
            });
            toast({
                title: "Success",
                description: "Folder deleted successfully",
                variant: "default"
            });
        }
    });
};

// Define allowed section types
export enum CaseSection {
    // Basic Sections (No additional fields required)
    ACCIDENT_PHOTOS = "accident_photos",
    POLICE_REPORT = "police_report",
    DEMAND_QUESTIONNAIRE = "demand_questionnaire",
    SETTLEMENT_MEMO = "settlement_memo",
    OTHER = "other",

    // Medical Sections (Requires medical_treatment_id)
    MEDICAL_BILLS = "medical_bills",
    MEDICAL_RECORDS = "medical_records",

    // Employment Sections (Requires employment_record_id)
    LOSS_OF_INCOME = "loss_of_income",

    // Photos Section (Requires photos_id)
    PHOTOS = "photos"
}

// Helper function to check if a section requires medical_treatment_id
export const isMedicalSection = (section: CaseSection): boolean => {
    return section === CaseSection.MEDICAL_BILLS || section === CaseSection.MEDICAL_RECORDS;
};

// Helper function to check if a section requires employment_record_id
export const isEmploymentSection = (section: CaseSection): boolean => {
    return section === CaseSection.LOSS_OF_INCOME;
};

export interface UploadBySectionResponse {
    status: string;
    message: string;
    section: string;
    folder_name: string;
    file_details: {
        file_id: string;
        name: string;
        size: number;
        created_at: string;
        modified_at: string;
    };
    processing: {
        task_id: string;
        operation_id: string;
        status: string;
    };
}

export interface UploadBySectionRequest {
    case_id: string;
    section: CaseSection;  // Updated to use the enum
    file: File;
    medical_treatment_id?: string;
    employment_record_id?: string;
}

// Add the upload by section mutation hook
export const useUploadBySectionMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (request: UploadBySectionRequest) => {
            const formData = new FormData();
            formData.append('case_id', request.case_id);
            formData.append('section', request.section);
            formData.append('file', request.file);

            if (request.medical_treatment_id) {
                formData.append('medical_treatment_id', request.medical_treatment_id);
            }
            if (request.employment_record_id) {
                formData.append('employment_record_id', request.employment_record_id);
            }

            const { data } = await axios.post<UploadBySectionResponse>(
                `${BASE_URL}/upload-by-section/`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'multipart/form-data',
                    }
                }
            );
            return data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({
                queryKey: ['caseContents', data.file_details.file_id]
            });
            
            toast({
                title: "Success",
                description: data.message || "File uploaded successfully",
            });
        }
    });
}; 