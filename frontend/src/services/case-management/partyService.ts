import { useMutation, useQuery, useQuery<PERSON>lient } from "@tanstack/react-query";
import axios from '@/lib/axios';
import { 
    CaseParty, 
    CasePartyCreateRequest, 
    CasePartyUpdateRequest,
    CasePartyContact,
    CasePartyContactCreateRequest,
    CasePartyContactUpdateRequest,
    PartyType,
    CaseExpertWitness,
    CaseExpertWitnessCreateRequest
} from "@/type/case-management/partyTypes";
import { useToast } from "@/hooks/use-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

export const CASE_PARTIES_KEY = "case-parties";
export const CASE_PARTY_CONTACTS_KEY = "case-party-contacts";

// Case Party APIs
export const useCasePartiesQuery = (caseId: string, partyType?: PartyType) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [CASE_PARTIES_KEY, caseId, partyType],
        queryFn: async () => {
            const { data } = await axios.get<CaseParty[]>(
                `${BASE_URL}/cases/${caseId}/parties/`,
                {
                    params: partyType ? { party_type: partyType } : undefined,
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

export const useCasePartyQuery = (caseId: string, partyId: number, options?: { enabled?: boolean }) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [CASE_PARTIES_KEY, caseId, partyId],
        queryFn: async () => {
            const { data } = await axios.get<CaseParty>(
                `${BASE_URL}/cases/${caseId}/parties/${partyId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== undefined ? options.enabled : (!!caseId && !!partyId),
    });
};

export const useCreateCasePartyMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CasePartyCreateRequest) => {
            const response = await axios.post<CaseParty>(
                `${BASE_URL}/cases/${caseId}/parties/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_PARTIES_KEY, caseId],
            });
            toast({
                title: "Success",
                description: "Party created successfully",
            });
        },
    });
};

export const useUpdateCasePartyMutation = (caseId: string, partyId: number) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CasePartyUpdateRequest) => {
            const response = await axios.put<CaseParty>(
                `${BASE_URL}/cases/${caseId}/parties/${partyId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_PARTIES_KEY, caseId],
            });
            toast({
                title: "Success",
                description: "Party updated successfully",
            });
        },
    });
};

export const useDeleteCasePartyMutation = (caseId: string, partyId: number) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/parties/${partyId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_PARTIES_KEY, caseId],
            });
            toast({
                title: "Success",
                description: "Party deleted successfully",
            });
        },
    });
};

// Case Party Contact APIs
export const useCasePartyContactsQuery = (caseId: string, partyType?: PartyType) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [CASE_PARTY_CONTACTS_KEY, caseId, partyType],
        queryFn: async () => {
            const { data } = await axios.get<CasePartyContact[]>(
                `${BASE_URL}/cases/${caseId}/party-contacts/`,
                {
                    params: partyType ? { party_type: partyType } : undefined,
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

export const useCasePartyContactQuery = (caseId: string, contactId: number, options?: { enabled?: boolean }) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: [CASE_PARTY_CONTACTS_KEY, caseId, contactId],
        queryFn: async () => {
            const { data } = await axios.get<CasePartyContact>(
                `${BASE_URL}/cases/${caseId}/party-contacts/${contactId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== undefined ? options.enabled : (!!caseId && !!contactId),
    });
};

export const useCreateCasePartyContactMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CasePartyContactCreateRequest) => {
            const response = await axios.post<CasePartyContact>(
                `${BASE_URL}/cases/${caseId}/party-contacts/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_PARTY_CONTACTS_KEY, caseId],
            });
            toast({
                title: "Success",
                description: "Party contact created successfully",
            });
        },
    });
};

export const useUpdateCasePartyContactMutation = (caseId: string, contactId: number) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CasePartyContactUpdateRequest) => {
            const response = await axios.put<CasePartyContact>(
                `${BASE_URL}/cases/${caseId}/party-contacts/${contactId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_PARTY_CONTACTS_KEY, caseId],
            });
            toast({
                title: "Success",
                description: "Party contact updated successfully",
            });
        },
    });
};

export const useDeleteCasePartyContactMutation = (caseId: string, contactId: number) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/party-contacts/${contactId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [CASE_PARTY_CONTACTS_KEY, caseId],
            });
            toast({
                title: "Success",
                description: "Party contact deleted successfully",
            });
        },
    });
};

export const useCaseExpertWitnessesQuery = (caseId: string) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['caseExpertWitnesses', caseId],
        queryFn: async () => {
            const { data } = await axios.get<CaseExpertWitness[]>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};

export const useCreateCaseExpertWitnessMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseExpertWitnessCreateRequest) => {
            const response = await axios.post<CaseExpertWitness>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['caseExpertWitnesses', caseId],
            });
            toast({
                title: "Success",
                description: "Expert witness added successfully",
            });
        },
    });
};

export const useDeleteCaseExpertWitnessMutation = (caseId: string, expertWitnessId: number) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/${expertWitnessId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['caseExpertWitnesses', caseId],
            });
            toast({
                title: "Success",
                description: "Expert witness deleted successfully",
            });
        },
    });
}; 
