import { useQuery, useMutation, UseQueryResult, QueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { ClientTrust, ClientTrustSummary } from '@/type/case-management/clientTrustTypes';

// Query keys
export const CLIENT_TRUST_KEYS = {
  all: ['client-trust'] as const,
  lists: () => [...CLIENT_TRUST_KEYS.all, 'list'] as const,
  list: (caseId: string) => [...CLIENT_TRUST_KEYS.lists(), caseId] as const,
  summary: (caseId: string) => [...CLIENT_TRUST_KEYS.all, 'summary', caseId] as const,
  details: () => [...CLIENT_TRUST_KEYS.all, 'detail'] as const,
  detail: (caseId: string, id: number) => [...CLIENT_TRUST_KEYS.details(), caseId, id] as const,
};

// Get client trust entries for a case
export const useClientTrustQuery = (caseId: string): UseQueryResult<ClientTrust[]> => {
  return useQuery({
    queryKey: CLIENT_TRUST_KEYS.list(caseId),
    queryFn: async () => {
      const { data } = await axios.get(`${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client-trust/`);
      return data;
    },
    enabled: !!caseId,
  });
};

// Get client trust summary for a case
export const useClientTrustSummaryQuery = (caseId: string): UseQueryResult<ClientTrustSummary> => {
  return useQuery({
    queryKey: CLIENT_TRUST_KEYS.summary(caseId),
    queryFn: async () => {
      const { data } = await axios.get(`${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client-trust/summary/`);
      return data;
    },
    enabled: !!caseId,
  });
};

// Get a specific client trust entry
export const useClientTrustDetailQuery = (caseId: string, id: number): UseQueryResult<ClientTrust> => {
  return useQuery({
    queryKey: CLIENT_TRUST_KEYS.detail(caseId, id),
    queryFn: async () => {
      const { data } = await axios.get(`${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client-trust/${id}/`);
      return data;
    },
    enabled: !!caseId && !!id,
  });
};

// Create a client trust entry
export const useCreateClientTrustMutation = (queryClient: QueryClient) => {
  return useMutation({
    mutationFn: async ({ caseId, trustData }: { caseId: string; trustData: ClientTrust }) => {
      const { data } = await axios.post(`${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client-trust/`, trustData);
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(variables.caseId) });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.summary(variables.caseId) });
    },
  });
};

// Update a client trust entry
export const useUpdateClientTrustMutation = (queryClient: QueryClient) => {
  return useMutation({
    mutationFn: async ({ caseId, id, trustData }: { caseId: string; id: number; trustData: ClientTrust }) => {
      const { data } = await axios.patch(`${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client-trust/${id}/`, trustData);
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.detail(variables.caseId, variables.id) });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(variables.caseId) });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.summary(variables.caseId) });
    },
  });
};

// Delete a client trust entry
export const useDeleteClientTrustMutation = (queryClient: QueryClient) => {
  return useMutation({
    mutationFn: async ({ caseId, id }: { caseId: string; id: number }) => {
      await axios.delete(`${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/client-trust/${id}/`);
      return id;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.list(variables.caseId) });
      queryClient.invalidateQueries({ queryKey: CLIENT_TRUST_KEYS.summary(variables.caseId) });
    },
  });
};
