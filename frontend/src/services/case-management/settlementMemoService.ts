import { useQuery, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

// Define the types for the settlement memo API response
// Define the structured medpay type
export interface StructuredMedpay {
    third_party: MedpayRecovery[];
    client: MedpayRecovery[];
}

export interface SettlementMemoResponse {
    settlement_type: 'THIRD_PARTY_ONLY' | 'UIM_ONLY' | 'BOTH';
    summary: {
        total_settlement: number;
        net_to_client_third_party: number;
        net_to_client_uim: number;
        total_medpay: number;
        total_expenses: number;
        total_attorney_fees: number;
        total_attorney_liens?: number;
        total_medical_expenses: number;
        total_health_insurance_liens?: number;
        total_case_costs: number;
        total_miscellaneous_liens: number;
        total_adjustments: number;
        net_to_client: number;
        total_uim: number;
        total_third_party: number;
    };
    recovery: {
        third_party: ThirdPartyRecovery[];
        uim: UIMRecovery[];
        medpay: StructuredMedpay; // Always structured with third_party and client arrays
    };
    expenses: {
        third_party: {
            attorney_fees: AttorneyFee[];
            attorney_liens?: AttorneyLien[];
            medical_expenses: MedicalExpense[];
            health_insurance_liens?: HealthInsuranceLien[];
            case_costs: CaseCost[];
            miscellaneous_liens: MiscellaneousLien[];
            adjustments: Adjustment[];
        };
        uim: {
            attorney_fees: AttorneyFee[];
            attorney_liens?: AttorneyLien[];
            medical_expenses: MedicalExpense[];
            health_insurance_liens?: HealthInsuranceLien[];
            case_costs: CaseCost[];
            miscellaneous_liens: MiscellaneousLien[];
            adjustments: Adjustment[];
        };
    };
}

export interface ThirdPartyRecovery {
    company_name: string;
    amount: number;
    client_responsibility?: boolean;
    negotiations: {
        id: number;
        defendant_name: string;
        defendant_id?: number;
        amount: number;
        accepted_date: string;
        status?: string;
        type?: string;
    }[];
}

export interface UIMRecovery {
    company_name: string;
    amount: number;
    negotiations: {
        id: number;
        client_insurance_id: number;
        policy_number?: string;
        amount: number;
        accepted_date: string;
        status?: string;
        type?: string;
    }[];
}

export interface MedpayRecovery {
    id: number;
    insurance_type: string;
    insurance_name: string;
    provider_name: string;
    amount: number;
    check_number?: string;
    deposit_date?: string;
    client_responsibility?: boolean;
}

export interface AttorneyFee {
    id: number | null;
    law_firm: string;
    attorney: string;
    amount: number;
    note?: string;
    is_final: boolean;
}

export interface AttorneyLien {
    id: number;
    law_firm: string;
    attorney: string;
    amount: number;
    note?: string;
    is_final: boolean;
}

export interface MedicalExpense {
    id: number;
    provider_name: string;
    amount: number;
    treatment_status: string;
    account_number?: string;
    client_responsibility?: boolean;
    original_bill?: string;
}

export interface HealthInsuranceLien {
    id: number;
    insurance_name: string;
    amount: number;
    policy_number?: string;
    file_number?: string;
    is_final: boolean;
}

export interface CaseCost {
    id: number;
    contact_name: string;
    amount: number;
    memo?: string;
    payment_type: string;
    paid_date: string;
    cost_for: string;
}

export interface MiscellaneousLien {
    id: number;
    lien_holder: string;
    amount: number;
    description?: string;
    is_final: boolean;
}

export interface Adjustment {
    id: number;
    name: string;
    company: string;
    amount: number;
    description?: string;
    for_what: 'THIRD_PARTY_DEMAND' | 'UIM';
}

/**
 * Custom hook to fetch settlement memo data
 */
export const useSettlementMemoQuery = (caseId: string): UseQueryResult<SettlementMemoResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['settlementMemo', caseId],
        queryFn: async () => {
            const { data } = await axios.get<SettlementMemoResponse>(
                `${BASE_URL}/cases/${caseId}/settlement-memo/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!caseId,
    });
};
