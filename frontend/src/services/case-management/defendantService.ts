import { useMutation, UseMutationResult, useQ<PERSON>y, UseQueryResult, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    DefendantCreateRequest,
    DefendantResponse,
    DefendantInsuranceCreateRequest,
    DefendantInsuranceResponse,
    DefendantDetailResponse,
    DefendantInsuranceListItem,
    DefendantInsuranceAdjusterResponse,
    DefendantInsuranceLegalRepresentationResponse,
    DefendantInsuranceAdjusterRequest,
    DefendantInsuranceUpdateRequest,
    DefendantListItem,
    DefendantInsuranceLegalRepresentationRequest,
    DefendantPropertyDamage,
    DefendantPropertyDamageCreateRequest,
    DefendantPropertyDamageUpdateRequest,
    DefendantUpdateRequest,
    DefendantSyncRequest,
    DefendantSyncResponse
} from '@/type/case-management/defendantTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to create a defendant and its insurance
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for creating defendant and insurance
 */
export const useCreateDefendantWithInsuranceMutation = (
    caseId: string
): UseMutationResult<
    { defendant: DefendantResponse; insurance: DefendantInsuranceResponse | null },
    Error,
    { defendant: DefendantCreateRequest; insurance: Omit<DefendantInsuranceCreateRequest, 'defendant'> }
> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ defendant, insurance }) => {
            // First create the defendant
            const defendantResponse = await axios.post<DefendantResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/`,
                defendant,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );

            // Then create the insurance using the new defendant's ID
            if (insurance.no_insurance === false && insurance.insurance_company !== null) {
                const insuranceResponse = await axios.post<DefendantInsuranceResponse>(
                    `${BASE_URL}/cases/${caseId}/defendants/${defendantResponse.data.id}/insurances/`,
                    {
                        ...insurance,
                        defendant: defendantResponse.data.id
                    },
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return {
                    defendant: defendantResponse.data,
                    insurance: insuranceResponse.data
                };
            } else if (insurance.no_insurance === true) {
                const insuranceResponse = await axios.post<DefendantInsuranceResponse>(
                    `${BASE_URL}/cases/${caseId}/defendants/${defendantResponse.data.id}/insurances/`,
                    {
                        ...insurance,
                        defendant: defendantResponse.data.id
                    },
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return {
                    defendant: defendantResponse.data,
                    insurance: insuranceResponse.data
                };
            }

            return {
                defendant: defendantResponse.data,
                insurance: null
            };
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendants', caseId] });
            toast({
                title: "Success",
                description: "Defendant and insurance created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch list of defendants for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<DefendantListResponse>} Query result containing:
 * - data: List of defendants when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useDefendantsListQuery = (caseId: string): UseQueryResult<DefendantListItem[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendants', caseId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantListItem[]>(
                `${BASE_URL}/cases/${caseId}/defendants/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
    });
};

/**
 * Custom hook to fetch a defendant by ID
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @returns {UseQueryResult<DefendantDetailResponse>} Query result containing:
 * - data: Defendant details when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useDefendantDetailQuery = (
    caseId: string,
    defendantId: string
): UseQueryResult<DefendantDetailResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendant', caseId, defendantId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantDetailResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
    });
};

/**
 * Custom hook to fetch list of insurances for a defendant
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @returns {UseQueryResult<DefendantInsuranceListResponse>} Query result containing:
 * - data: List of insurances when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useDefendantInsuranceListQuery = (
    caseId: string,
    defendantId: string,
    options?: { enabled?: boolean }
): UseQueryResult<DefendantInsuranceListItem[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendant-insurances', caseId, defendantId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantInsuranceListItem[]>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurances/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
    });
};

/**
 * Custom hook to fetch adjuster details for a defendant's insurance
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseQueryResult<DefendantInsuranceAdjusterResponse>} Query result containing:
 * - data: Adjuster details when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useDefendantInsuranceAdjusterDetailsQuery = (
    caseId: string,
    defendantId: string,
    insuranceId: string,
    options?: { enabled?: boolean }
): UseQueryResult<DefendantInsuranceAdjusterResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendant-insurance-adjuster', caseId, defendantId, insuranceId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantInsuranceAdjusterResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurance/${insuranceId}/adjusters/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
    });
};

/**
 * Custom hook to fetch legal representation for a defendant's insurance
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseQueryResult<DefendantInsuranceLegalRepresentationResponse>} Query result containing:
 * - data: Legal representation details when the query is successful
 * - isLoading: Boolean indicating if the query is in progress
 * - error: Error object if the query fails
 * - other standard react-query properties
 */
export const useDefendantInsuranceLegalRepresentationQuery = (
    caseId: string,
    defendantId: string,
    insuranceId: string,
    options?: { enabled?: boolean }
): UseQueryResult<DefendantInsuranceLegalRepresentationResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendant-insurance-legal-representation', caseId, defendantId, insuranceId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantInsuranceLegalRepresentationResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/legal-representation/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled
    });
};

/**
 * Custom hook to create adjuster details for a defendant's insurance
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseMutationResult} Mutation result for creating adjuster details
 */
export const useCreateDefendantInsuranceAdjusterMutation = (
    caseId: string,
    defendantId: string,
    insuranceId: string
): UseMutationResult<DefendantInsuranceAdjusterResponse, Error, DefendantInsuranceAdjusterRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantInsuranceAdjusterRequest) => {
            const response = await axios.post<DefendantInsuranceAdjusterResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurance/${insuranceId}/adjusters/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendant-insurance-adjuster', caseId, defendantId, insuranceId] });
            toast({
                title: "Success",
                description: "Adjuster details created successfully",
            });
        }
    });
};

/**
 * Custom hook to update adjuster details for a defendant's insurance
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @param {string} insuranceId - The ID of the insurance
 * @returns {UseMutationResult} Mutation result for updating adjuster details
 */
export const useUpdateDefendantInsuranceAdjusterMutation = (
    caseId: string,
    defendantId: string,
    insuranceId: string
): UseMutationResult<DefendantInsuranceAdjusterResponse, Error, DefendantInsuranceAdjusterRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantInsuranceAdjusterRequest) => {
            const response = await axios.put<DefendantInsuranceAdjusterResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurance/${insuranceId}/adjusters/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendant-insurance-adjuster', caseId, defendantId, insuranceId] });
            toast({
                title: "Success",
                description: "Adjuster details updated successfully",
            });
        }
    });
};

/**
 * Custom hook to create insurance for a defendant
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @returns {UseMutationResult} Mutation result for creating defendant insurance
 */
export const useCreateDefendantInsuranceMutation = (
    caseId: string,
    defendantId: string
): UseMutationResult<DefendantInsuranceResponse, Error, DefendantInsuranceCreateRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantInsuranceCreateRequest) => {
            const response = await axios.post<DefendantInsuranceResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurances/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendant-insurances', caseId, defendantId] });
            toast({
                title: "Success",
                description: "Defendant insurance created successfully",
            });
        }
    });
};

/**
 * Custom hook to update insurance for a defendant
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @param {string} insuranceId - The ID of the insurance to update
 * @returns {UseMutationResult} Mutation result for updating defendant insurance
 */
export const useUpdateDefendantInsuranceMutation = (
    caseId: string,
    defendantId: string,
    insuranceId: string
): UseMutationResult<DefendantInsuranceResponse, Error, DefendantInsuranceUpdateRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantInsuranceUpdateRequest) => {
            const response = await axios.put<DefendantInsuranceResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurances/${insuranceId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendant-insurances', caseId, defendantId] });
            queryClient.invalidateQueries({ queryKey: ['defendant-insurance-adjuster', caseId, defendantId, insuranceId] });
            queryClient.invalidateQueries({ queryKey: ['defendant-insurance-legal-representation', caseId, defendantId, insuranceId] });
            toast({
                title: "Success",
                description: "Defendant insurance updated successfully",
            });
        }
    });
};

/**
 * Custom hook to create legal representation for defendant insurance
 */
export const useCreateDefendantInsuranceLegalRepresentationMutation = (
    caseId: string,
    defendantId: string,
    insuranceId: string
): UseMutationResult<DefendantInsuranceLegalRepresentationResponse, Error, DefendantInsuranceLegalRepresentationRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantInsuranceLegalRepresentationRequest) => {
            const response = await axios.post<DefendantInsuranceLegalRepresentationResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/legal-representation/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['defendant-insurance-legal-representation', caseId, defendantId, insuranceId]
            });
            toast({
                title: "Success",
                description: "Legal representation created successfully",
            });
        }
    });
};

/**
 * Custom hook to update legal representation for defendant insurance
 */
export const useUpdateDefendantInsuranceLegalRepresentationMutation = (
    caseId: string,
    defendantId: string,
    insuranceId: string,
    legalRepId: string
): UseMutationResult<DefendantInsuranceLegalRepresentationResponse, Error, DefendantInsuranceLegalRepresentationRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantInsuranceLegalRepresentationRequest) => {
            const response = await axios.put<DefendantInsuranceLegalRepresentationResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/legal-representation/${legalRepId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['defendant-insurance-legal-representation', caseId, defendantId, insuranceId]
            });
            toast({
                title: "Success",
                description: "Legal representation updated successfully",
            });
        }
    });
};

/**
 * Custom hook to fetch property damage details for a defendant
 */
export const useDefendantPropertyDamageQuery = (
    caseId: string,
    defendantId: string
): UseQueryResult<DefendantPropertyDamage> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendantPropertyDamage', caseId, defendantId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantPropertyDamage>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/property-damage/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        }
    });
};

/**
 * Custom hook to create property damage for a defendant
 */
export const useCreateDefendantPropertyDamageMutation = (
    caseId: string,
    defendantId: string
): UseMutationResult<DefendantPropertyDamage, Error, DefendantPropertyDamageCreateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: DefendantPropertyDamageCreateRequest) => {
            const response = await axios.post<DefendantPropertyDamage>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/property-damage/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['defendantPropertyDamageList', caseId, defendantId]
            });
            toast({
                title: "Success",
                description: "Property damage record created successfully",
            });
        }
    });
};

/**
 * Custom hook to update property damage for a defendant
 */
export const useUpdateDefendantPropertyDamageMutation = (
    caseId: string,
    defendantId: string
): UseMutationResult<DefendantPropertyDamage, Error, DefendantPropertyDamageUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: DefendantPropertyDamageUpdateRequest) => {
            const response = await axios.put<DefendantPropertyDamage>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/property-damage/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['defendantPropertyDamageList', caseId, defendantId]
            });
            toast({
                title: "Success",
                description: "Property damage record updated successfully",
            });
        }
    });
};

/**
 * Custom hook to fetch property damage list for a defendant
 */
export const useDefendantPropertyDamageListQuery = (
    caseId: string,
    defendantId: string,
    options?: { enabled?: boolean }
): UseQueryResult<DefendantPropertyDamage> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['defendantPropertyDamageList', caseId, defendantId],
        queryFn: async () => {
            const { data } = await axios.get<DefendantPropertyDamage>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/property-damage/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false && !!caseId && !!defendantId
    });
};

/**
 * Custom hook to update a defendant
 * @param {string} caseId - The ID of the case
 * @param {string} defendantId - The ID of the defendant
 * @returns {UseMutationResult} Mutation result for updating defendant
 */
export const useUpdateDefendantMutation = (
    caseId: string,
    defendantId: string
): UseMutationResult<DefendantResponse, Error, DefendantUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: DefendantUpdateRequest) => {
            const response = await axios.put<DefendantResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendant', caseId, defendantId] });
            queryClient.invalidateQueries({ queryKey: ['defendants', caseId] });
            toast({
                title: "Success",
                description: "Defendant updated successfully",
            });
        }
    });
};

/**
 * Custom hook to delete a defendant
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for deleting defendant
 */
export const useDeleteDefendantMutation = (
    caseId: string
): UseMutationResult<void, Error, string> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (defendantId: string) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendants', caseId] });
            toast({
                title: "Success",
                description: "Defendant deleted successfully",
            });
        }
    });
};

/**
 * Custom hook to delete insurance for a defendant
 */
export const useDeleteDefendantInsuranceMutation = (
    caseId: string,
    defendantId: string,
): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    return useMutation({
        mutationFn: async (insuranceId: string) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/insurances/${insuranceId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['defendant-insurances', caseId, defendantId] });
            toast({
                title: "Success",
                description: "Insurance deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to sync defendant data across cases
 */
export const useSyncDefendantMutation = (
    caseId: string
): UseMutationResult<DefendantSyncResponse, Error, DefendantSyncRequest> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: DefendantSyncRequest) => {
            const response = await axios.post<DefendantSyncResponse>(
                `${BASE_URL}/cases/${caseId}/defendants/sync/sync/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['defendants'] });
            toast({
                title: "Success",
                description: data.message || "Defendant data synced successfully",
            });
        }
    });
};
