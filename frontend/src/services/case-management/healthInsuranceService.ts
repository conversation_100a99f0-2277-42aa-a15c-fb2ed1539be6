import { useQuery, useMutation, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { 
    HealthInsurance,
    HealthInsuranceCreateRequest,
    HealthInsuranceResponse
} from '@/type/case-management/healthInsuranceTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch all health insurances for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<HealthInsuranceResponse[]>} Query result
 */
export const useHealthInsurancesQuery = (caseId: string): UseQueryResult<HealthInsuranceResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['healthInsurances', caseId],
        queryFn: async () => {
            const { data } = await axios.get<HealthInsuranceResponse[]>(
                `${BASE_URL}/cases/${caseId}/health-insurances/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

/**
 * Custom hook to create a health insurance
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateHealthInsuranceMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: HealthInsuranceCreateRequest) => {
            const response = await axios.post<HealthInsurance>(
                `${BASE_URL}/cases/${caseId}/health-insurances/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['healthInsurances', caseId] });
            toast({
                title: "Success",
                description: "Health insurance created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a health insurance
 * @param {string} caseId - The ID of the case
 * @param {string} insuranceId - The ID of the health insurance
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateHealthInsuranceMutation = (caseId: string, insuranceId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: HealthInsuranceCreateRequest) => {
            const response = await axios.put<HealthInsurance>(
                `${BASE_URL}/cases/${caseId}/health-insurances/${insuranceId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['healthInsurances', caseId] });
            toast({
                title: "Success",
                description: "Health insurance updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a health insurance
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteHealthInsuranceMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ caseId, insuranceId }: { caseId: string; insuranceId: string }) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/health-insurances/${insuranceId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: (_, { caseId }) => {
            queryClient.invalidateQueries({ queryKey: ['healthInsurances', caseId] });
            toast({
                title: "Success",
                description: "Health insurance deleted successfully",
                variant: "default"
            });
        }
    });
}; 