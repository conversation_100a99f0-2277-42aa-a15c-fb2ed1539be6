import { useQuery, useMutation, useQuery<PERSON>lient, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/document-management';

// Types
export interface S3File {
    id: number;
    organization: number;
    folder: number;
    case?: number;
    case_party?: number;
    name: string;
    s3_path: string;
    size: number;
    content_type: string;
    document_type: string;
    tags: string[];
    description?: string;
    created_by: number;
    created_at: string;
    updated_at: string;
    download_url: string;
    file_count?: number | undefined;
}

export interface S3Folder {
    id: number;
    organization: number;
    case?: number;
    parent?: number;
    name: string;
    s3_path: string;
    is_system_folder: boolean;
    system_folder_type?: string;
    created_by: number;
    created_at: string;
    updated_at: string;
    file_count: number;
    total_size: number;
}

export interface FileUploadRequest {
    file: File;
    name: string;
    case: string;
    document_type: string;
    folder?: string;
    case_party?: string;
    tags?: string[];
    description?: string;
}

// Document type categories
export const ORG_LEVEL_DOCUMENTS = [
    'LEGAL_DOCUMENT',
    'LEGAL_TEMPLATE',
    'LEGAL_REPORT',
    'OTHER'
] as const;

export const CASE_LEVEL_DOCUMENTS = [
    'CLIENT_DOCUMENT',
    'DEFENDANT_DOCUMENT',
    'INCIDENT_DOCUMENT',
    'ACCIDENT_IMAGE',
    'CASE_PARTY_DOCUMENT',
    'MEDICAL_BILL',
    'MEDICAL_RECORD',
    'EMPLOYMENT_RECORD',
    'SETTLEMENT_DOCUMENT',
    'LIENS_DOCUMENT',
    'CASE_COST_DOCUMENT',
    'DISCOVERY_DOCUMENT',
    'LITIGATION_DOCUMENT',
    'ANTI_COLOSSUS_DOCUMENT',
    'NEGOTIATION_DOCUMENT',
    'DEMAND_DOCUMENT',
    'OTHER',
    'LITIGATION_COST_DOCUMENT'
] as const;

export type OrgLevelDocumentType = typeof ORG_LEVEL_DOCUMENTS[number];
export type CaseLevelDocumentType = typeof CASE_LEVEL_DOCUMENTS[number];
export type DocumentType = OrgLevelDocumentType | CaseLevelDocumentType;

export interface AutoUploadRequest {
    file: File;
    document_type: DocumentType;
    level: 'org' | 'case';
    case_id?: string;
    tags?: string[];
    description?: string;
}

export interface PartyUploadRequest {
    file: File;
    case_id: string;
    case_party_id: string;
    metadata: {
        document_type: string;
        tags?: string[];
        description?: string;
    };
}

export interface SearchQuery {
    q?: string;
    document_type?: string;
    tags?: string[];
    case_id?: string;
    case_party_id?: string;
    folder_id?: string;
    created_after?: string;
    created_before?: string;
}

export interface AdvancedSearchQuery {
    text?: string;
    exact_match?: boolean;
    filters?: {
        document_types?: string[];
        tags?: {
            include_all?: string[];
            include_any?: string[];
            exclude?: string[];
        };
        date_range?: {
            start?: string;
            end?: string;
        };
        cases?: string[];
        case_parties?: string[];
        folders?: string[];
        created_by?: string[];
    };
}

// Update the interface for folder creation
export interface CreateFolderRequest {
  name: string;
  case_id: string;
  parent_id?: string;
}

// Update the response type for folder contents
export interface FolderContentsResponse {
    root_folder_id: string;
    folders: S3Folder[];
    files: S3File[];
}

// S3 Folders Hooks
export const useS3FoldersQuery = (caseId?: string, parentId?: string): UseQueryResult<S3Folder[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['s3Folders', caseId, parentId],
        queryFn: async () => {
            try {
                const params = new URLSearchParams();
                if (caseId) params.append('case_id', caseId);
                if (parentId) params.append('parent_id', parentId);

                const { data } = await axios.get<S3Folder[]>(
                    `${BASE_URL}/s3-folders/?${params.toString()}`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch folders';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken
    });
};

export const useCreateFolderMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CreateFolderRequest) => {
            const response = await axios.post<S3Folder>(
                `${BASE_URL}/s3-folders/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: (_, variables) => {
            // Invalidate the parent folder's contents
            queryClient.invalidateQueries({ 
                queryKey: ['s3FolderContents', variables.case_id, variables.parent_id] 
            });
            // Also invalidate root folder contents if we're creating at root
            if (!variables.parent_id) {
                queryClient.invalidateQueries({ 
                    queryKey: ['s3FolderContents', variables.case_id, undefined] 
                });
            }
            toast({
                title: "Success",
                description: "Folder created successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create folder';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
        }
    });
};

// S3 Files Hooks
export const useS3FilesQuery = (caseId?: string, folderId?: string): UseQueryResult<S3File[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['s3Files', caseId, folderId],
        queryFn: async () => {
            try {
                const params = new URLSearchParams();
                if (caseId) params.append('case_id', caseId);
                if (folderId) params.append('folder_id', folderId);

                const { data } = await axios.get<S3File[]>(
                    `${BASE_URL}/s3-files/?${params.toString()}`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch files';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken
    });
};

export const useUploadFileMutation = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: FileUploadRequest) => {
            const formData = new FormData();
            formData.append('file', data.file);
            formData.append('name', data.name);
            formData.append('case', data.case);
            formData.append('document_type', data.document_type);
            
            if (data.folder) {
                formData.append('folder', data.folder);
            }
            if (data.case_party) {
                formData.append('case_party', data.case_party);
            }
            if (data.tags) {
                data.tags.forEach(tag => formData.append('tags', tag));
            }
            if (data.description) {
                formData.append('description', data.description);
            }

            const response = await axios.post<S3File>(
                `${BASE_URL}/s3-files/`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            // Don't invalidate queries here, let the component handle it
            toast({
                title: "Success",
                description: "File uploaded successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            console.error('Upload error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
            throw error;
        }
    });
};

export const useAutoUploadMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: AutoUploadRequest) => {
            const formData = new FormData();
            formData.append('file', data.file);
            formData.append('document_type', data.document_type);
            formData.append('level', data.level);
            
            if (data.level === 'case' && data.case_id) {
                formData.append('case_id', data.case_id);
            }
            if (data.tags) {
                data.tags.forEach(tag => formData.append('tags', tag));
            }
            if (data.description) {
                formData.append('description', data.description);
            }

            const response = await axios.post<S3File>(
                `${BASE_URL}/s3-files/auto_upload/`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            return response.data;
        },
        onSuccess: (_, variables) => {
            // Invalidate queries based on level
            if (variables.level === 'case' && variables.case_id) {
                queryClient.invalidateQueries({ 
                    queryKey: ['s3FolderContents', variables.case_id]
                });
            } else {
                queryClient.invalidateQueries({ 
                    queryKey: ['s3FolderContents']
                });
            }
            toast({
                title: "Success",
                description: "File uploaded successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            console.error('Auto upload error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
            throw error;
        }
    });
};

export const usePartyUploadMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: PartyUploadRequest) => {
            const formData = new FormData();
            formData.append('file', data.file);
            formData.append('metadata', JSON.stringify(data.metadata));
            formData.append('case_id', data.case_id);
            formData.append('case_party_id', data.case_party_id);

            const response = await axios.post<S3File>(
                `${BASE_URL}/s3-files/party_upload/`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['s3Files'] });
            toast({
                title: "Success",
                description: "File uploaded successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
        }
    });
};

// Search Hooks
export const useBasicSearchQuery = (searchParams: SearchQuery): UseQueryResult<S3File[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['basicSearch', searchParams],
        queryFn: async () => {
            try {
                const params = new URLSearchParams();
                Object.entries(searchParams).forEach(([key, value]) => {
                    if (value) {
                        if (Array.isArray(value)) {
                            value.forEach(v => params.append(key, v));
                        } else {
                            params.append(key, value);
                        }
                    }
                });

                const { data } = await axios.get<S3File[]>(
                    `${BASE_URL}/search/basic/?${params.toString()}`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Search failed';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken
    });
};

export const useAdvancedSearchMutation = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (query: AdvancedSearchQuery) => {
            const response = await axios.post<S3File[]>(
                `${BASE_URL}/search/advanced/`,
                query,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Advanced search failed';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
        }
    });
};

// Additional S3 Folder Hooks
export const useS3FolderDetailsQuery = (folderId: string): UseQueryResult<S3Folder> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['s3Folder', folderId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<S3Folder>(
                    `${BASE_URL}/s3-folders/${folderId}/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch folder details';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!folderId
    });
};

export const useUpdateFolderMutation = (folderId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Partial<S3Folder>) => {
            const response = await axios.put<S3Folder>(
                `${BASE_URL}/s3-folders/${folderId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['s3Folders'] });
            queryClient.invalidateQueries({ queryKey: ['s3Folder', folderId] });
            toast({
                title: "Success",
                description: "Folder updated successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Failed to update folder';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
        }
    });
};

export const useDeleteFolderMutation = (folderId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            if (!folderId) {
                throw new Error('Folder ID is required for deletion');
            }
            await axios.delete(
                `${BASE_URL}/s3-folders/${folderId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            // Invalidate all folder contents queries to ensure proper refresh
            queryClient.invalidateQueries({ 
                queryKey: ['s3FolderContents']
            });
            toast({
                title: "Success",
                description: "Folder deleted successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Failed to delete folder';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
            throw error; // Re-throw to handle in component
        }
    });
};

// Additional S3 File Hooks
export const useS3FileDetailsQuery = (fileId: string): UseQueryResult<S3File> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['s3File', fileId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<S3File>(
                    `${BASE_URL}/s3-files/${fileId}/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch file details';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!fileId
    });
};

export const useUpdateFileMutation = (fileId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: Partial<S3File>) => {
            const response = await axios.put<S3File>(
                `${BASE_URL}/s3-files/${fileId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['s3Files'] });
            queryClient.invalidateQueries({ queryKey: ['s3File', fileId] });
            toast({
                title: "Success",
                description: "File updated successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Failed to update file';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
        }
    });
};

export const useDeleteFileMutation = (fileId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            if (!fileId) {
                throw new Error('File ID is required for deletion');
            }
            await axios.delete(
                `${BASE_URL}/s3-files/${fileId}/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
        },
        onSuccess: () => {
            // Invalidate folder contents to refresh the file list
            queryClient.invalidateQueries({ 
                queryKey: ['s3FolderContents']
            });
            toast({
                title: "Success",
                description: "File deleted successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            const errorMessage = error instanceof Error ? error.message : 'Failed to delete file';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
            throw error; // Re-throw to handle in component
        }
    });
};

export const useFileDownloadUrlQuery = (fileId: string): UseQueryResult<string> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['fileDownload', fileId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<{ download_url: string }>(
                    `${BASE_URL}/s3-files/${fileId}/download/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data.download_url;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to get download URL';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!fileId
    });
};

export const useFileViewUrlQuery = (fileId: string): UseQueryResult<string> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['fileView', fileId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<{ view_url: string }>(
                    `${BASE_URL}/s3-files/${fileId}/view/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data.view_url;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to get view URL';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!fileId
    });
};

// Update the folder query hook
export const useS3FolderContentsQuery = (caseId?: string, folderId?: string): UseQueryResult<FolderContentsResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['s3FolderContents', caseId, folderId],
        queryFn: async () => {
            try {
                const params = new URLSearchParams();
                if (caseId) params.append('case_id', caseId);
                if (folderId) params.append('folder_id', folderId);

                const { data } = await axios.get<FolderContentsResponse>(
                    `${BASE_URL}/s3-folders/folder_contents/?${params.toString()}`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch folder contents';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                throw error;
            }
        },
        enabled: !!accessToken
    });
};

// Add new document types
export const USER_DOCUMENT_TYPES = [
    'DRIVER_LICENSE',
    'CLIENT_PROFILE_PHOTO'
] as const;

export type UserDocumentType = typeof USER_DOCUMENT_TYPES[number];

// Add interface for user upload request
export interface UserUploadRequest {
    file: File;
    case_id: string;
    document_type: UserDocumentType;
    tags?: string[];
    description?: string;
}

export interface UserFolderContentsResponse {
    folder: {
        id: string;
        name: string;
        s3_path: string;
    };
    files: S3File[];
}

export const useUserFolderContentsQuery = (caseId?: string): UseQueryResult<UserFolderContentsResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['userFolderContents', caseId],
        queryFn: async () => {
            try {
                const params = new URLSearchParams();
                if (caseId) params.append('case_id', caseId);

                const { data } = await axios.get<UserFolderContentsResponse>(
                    `${BASE_URL}/s3-files/user_folder_contents/?${params.toString()}`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user folder contents';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive"
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!caseId
    });
};

export const useUserUploadMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: UserUploadRequest) => {
            const formData = new FormData();
            formData.append('file', data.file);
            formData.append('case_id', data.case_id);
            formData.append('document_type', data.document_type);
            
            if (data.tags) {
                data.tags.forEach(tag => formData.append('tags', tag));
            }
            if (data.description) {
                formData.append('description', data.description);
            }

            const response = await axios.post<S3File>(
                `${BASE_URL}/s3-files/user_upload/`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            return response.data;
        },
        onSuccess: (_, variables) => {
            // Invalidate both queries
            queryClient.invalidateQueries({ 
                queryKey: ['userFolderContents', variables.case_id]
            });
            toast({
                title: "Success",
                description: "File uploaded successfully",
                variant: "default"
            });
        },
        onError: (error) => {
            console.error('User upload error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive"
            });
            throw error;
        }
    });
}; 