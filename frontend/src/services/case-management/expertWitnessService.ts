import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    ExpertWitnessContact,
    ExpertWitnessCreateRequest,
    CaseExpertWitness,
    CaseExpertWitnessCreateRequest
} from '@/type/case-management/expertWitnesTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch expert witnesses list
 */
export const useExpertWitnessesQuery = (
    search?: string,
    ordering?: string
): UseQueryResult<ExpertWitnessContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['expertWitnesses', search, ordering],
        queryFn: async () => {
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (ordering) params.append('ordering', ordering);

            const { data } = await axios.get<ExpertWitnessContact[]>(
                `${BASE_URL}/expert-witnesses/?${params.toString()}`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        }
    });
};

/**
 * Custom hook to create an expert witness
 */
export const useCreateExpertWitnessMutation = (): UseMutationResult<
    ExpertWitnessContact,
    Error,
    ExpertWitnessCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ExpertWitnessCreateRequest) => {
            const response = await axios.post<ExpertWitnessContact>(
                `${BASE_URL}/expert-witnesses/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['expertWitnesses'] });
            toast({
                title: "Success",
                description: "Expert witness created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to create a case expert witness
 */
export const useCreateCaseExpertWitnessMutation = (caseId: string): UseMutationResult<
    CaseExpertWitness,
    Error,
    CaseExpertWitnessCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseExpertWitnessCreateRequest) => {
            const response = await axios.post<CaseExpertWitness>(
                `${BASE_URL}/cases/${caseId}/expert-witnesses/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseExpertWitnesses', caseId] });
            toast({
                title: "Success",
                description: "Case expert witness created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update an expert witness
 */
export const useUpdateExpertWitnessMutation = (expertWitnessId: string): UseMutationResult<
    ExpertWitnessCreateRequest,
    Error,
    ExpertWitnessCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ExpertWitnessCreateRequest) => {
            const response = await axios.put<ExpertWitnessCreateRequest>(
                `${BASE_URL}/expert-witnesses/${expertWitnessId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['expertWitnesses'] });
            toast({
                title: "Success",
                description: "Expert witness updated successfully",
                variant: "default"
            });
        }
    });
};
