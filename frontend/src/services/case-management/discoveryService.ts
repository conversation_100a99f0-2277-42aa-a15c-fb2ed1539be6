import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { CaseDiscoveryRead, CaseDiscoveryWrite, DiscoveryResponse, DiscoveryExtension } from '@/type/case-management/discoveryTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';

/**
 * Custom hook to fetch list of discoveries for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseDiscoveryRead[]>} Query result containing list of discoveries
 */
export const useDiscoveriesListQuery = (
    caseId: string,
    options?: { enabled?: boolean }
): UseQueryResult<CaseDiscoveryRead[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['discoveries', caseId],
        queryFn: async () => {
            const { data } = await axios.get<CaseDiscoveryRead[]>(
                `${BASE_URL}/cases/${caseId}/discoveries/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: options?.enabled !== false,
    });
};

/**
 * Custom hook to fetch a specific discovery by ID
 * @param {string} caseId - The ID of the case
 * @param {string} discoveryId - The ID of the discovery
 * @returns {UseQueryResult<CaseDiscoveryRead>} Query result containing discovery details
 */
export const useDiscoveryDetailsQuery = (
    caseId: string,
    discoveryId: string
): UseQueryResult<CaseDiscoveryRead> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['discovery', caseId, discoveryId],
        queryFn: async () => {
            const { data } = await axios.get<CaseDiscoveryRead>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return data;
        },
        enabled: !!discoveryId,
    });
};

/**
 * Custom hook to create a new discovery
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for creating discovery
 */
export const useCreateDiscoveryMutation = (
    caseId: string
): UseMutationResult<CaseDiscoveryRead, Error, CaseDiscoveryWrite> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseDiscoveryWrite) => {
            const response = await axios.post<CaseDiscoveryRead>(
                `${BASE_URL}/cases/${caseId}/discoveries/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveries', caseId] });
            toast({
                title: "Success",
                description: "Discovery created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a discovery
 * @param {string} caseId - The ID of the case
 * @param {string} discoveryId - The ID of the discovery to update
 * @returns {UseMutationResult} Mutation result for updating discovery
 */
export const useUpdateDiscoveryMutation = (
    caseId: string,
    discoveryId: string
): UseMutationResult<CaseDiscoveryRead, Error, CaseDiscoveryWrite> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseDiscoveryWrite) => {
            const response = await axios.put<CaseDiscoveryRead>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveries', caseId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Discovery updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a discovery
 * @param {string} caseId - The ID of the case
 * @param {string} discoveryId - The ID of the discovery to delete
 * @returns {UseMutationResult} Mutation result for deleting discovery
 */
export const useDeleteDiscoveryMutation = (
    caseId: string,
    discoveryId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveries', caseId] });
            toast({
                title: "Success",
                description: "Discovery deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch responses for a discovery
 */
export const useDiscoveryResponsesQuery = (
    caseId: string,
    discoveryId: string
): UseQueryResult<DiscoveryResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['discoveryResponses', caseId, discoveryId],
        queryFn: async () => {
            const { data } = await axios.get<DiscoveryResponse[]>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/responses/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!discoveryId,
    });
};

/**
 * Custom hook to create a discovery response
 */
export const useCreateDiscoveryResponseMutation = (
    caseId: string,
    discoveryId: string
): UseMutationResult<DiscoveryResponse, Error, Omit<DiscoveryResponse, 'id' | 'discovery' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({    
        mutationFn: async (data) => {
            const response = await axios.post<DiscoveryResponse>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/responses/`,
                data,
                { headers: { Authorization: `Bearer ${accessToken}` } }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveryResponses', caseId, discoveryId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Response created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a discovery response
 */
export const useUpdateDiscoveryResponseMutation = (
    caseId: string,
    discoveryId: string,
    responseId: string
): UseMutationResult<DiscoveryResponse, Error, Omit<DiscoveryResponse, 'id' | 'discovery' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data) => {
            const response = await axios.put<DiscoveryResponse>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/responses/${responseId}/`,
                data,
                { headers: { Authorization: `Bearer ${accessToken}` } }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveryResponses', caseId, discoveryId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Response updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a discovery response
 */
export const useDeleteDiscoveryResponseMutation = (
    caseId: string,
    discoveryId: string,
    responseId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/responses/${responseId}/`,
                { headers: { Authorization: `Bearer ${accessToken}` } }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveryResponses', caseId, discoveryId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Response deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch extensions for a discovery
 */
export const useDiscoveryExtensionsQuery = (
    caseId: string,
    discoveryId: string
): UseQueryResult<DiscoveryExtension[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['discoveryExtensions', caseId, discoveryId],
        queryFn: async () => {
            const { data } = await axios.get<DiscoveryExtension[]>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/extensions/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!discoveryId,
    });
};

/**
 * Custom hook to create a discovery extension
 */
export const useCreateDiscoveryExtensionMutation = (
    caseId: string,
    discoveryId: string
): UseMutationResult<DiscoveryExtension, Error, Omit<DiscoveryExtension, 'id' | 'discovery' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data) => {
            const response = await axios.post<DiscoveryExtension>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/extensions/`,
                data,
                { headers: { Authorization: `Bearer ${accessToken}` } }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveryExtensions', caseId, discoveryId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Extension created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a discovery extension
 */
export const useUpdateDiscoveryExtensionMutation = (
    caseId: string,
    discoveryId: string,
    extensionId: string
): UseMutationResult<DiscoveryExtension, Error, Omit<DiscoveryExtension, 'id' | 'discovery' | 'created_at' | 'updated_at'>> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data) => {
            const response = await axios.put<DiscoveryExtension>(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/extensions/${extensionId}/`,
                data,
                { headers: { Authorization: `Bearer ${accessToken}` } }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveryExtensions', caseId, discoveryId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Extension updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a discovery extension
 */
export const useDeleteDiscoveryExtensionMutation = (
    caseId: string,
    discoveryId: string,
    extensionId: string
): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async () => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/discoveries/${discoveryId}/extensions/${extensionId}/`,
                { headers: { Authorization: `Bearer ${accessToken}` } }
            );
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['discoveryExtensions', caseId, discoveryId] });
            queryClient.invalidateQueries({ queryKey: ['discovery', caseId, discoveryId] });
            toast({
                title: "Success",
                description: "Extension deleted successfully",
                variant: "default"
            });
        }
    });
}; 