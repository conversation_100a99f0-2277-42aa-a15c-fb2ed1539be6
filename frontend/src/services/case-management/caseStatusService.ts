import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from '@/lib/axios';
import { useToast } from "@/hooks/use-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  ChecklistResponse,
  StatusUpdateRequest,
  StatusUpdateResponse,
  ChecklistItemUpdateRequest,
  ChecklistItemUpdateResponse
} from "@/type/case-management/caseStatusTypes";

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';
const CASE_STATUS_KEY = "case-status";

// Get Case Checklist
export const useCaseChecklist = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: [CASE_STATUS_KEY, 'checklist', caseId],
    queryFn: async () => {
      const { data } = await axios.get<ChecklistResponse>(
        `${BASE_URL}/case-status/${caseId}/checklist/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!caseId && !!accessToken,
  });
};

// Update Case Status
export const useUpdateCaseStatus = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ caseId, status }: { caseId: string; status: string }) => {
      const { data } = await axios.post<StatusUpdateResponse>(
        `${BASE_URL}/case-status/${caseId}/update_status/`,
        { status } as StatusUpdateRequest,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [CASE_STATUS_KEY, 'checklist', variables.caseId] });
      toast({
        title: "Success",
        description: "Case status updated successfully",
        variant: "default",
      });
    }
  });
};

// Update Checklist Item
export const useUpdateChecklistItem = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ caseId, itemId, isCompleted }: { caseId: string; itemId: number; isCompleted: boolean }) => {
      const { data } = await axios.post<ChecklistItemUpdateResponse>(
        `${BASE_URL}/case-status/${caseId}/update_checklist_item/`,
        {
          item_id: itemId,
          is_completed: isCompleted
        } as ChecklistItemUpdateRequest,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [CASE_STATUS_KEY, 'checklist', variables.caseId] });
      toast({
        title: "Success",
        description: "Checklist item updated successfully",
        variant: "default",
      });
    }
  });
};
