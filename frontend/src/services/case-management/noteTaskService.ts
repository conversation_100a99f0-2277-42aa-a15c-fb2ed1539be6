import { useMutation, UseMutationResult, use<PERSON><PERSON><PERSON>, UseQueryResult, useQueryClient, useInfiniteQuery, QueryFunctionContext } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { CaseNote, CaseNoteWrite, Task, TaskWrite, ListResponse, TaskCreate, TaskFilters, NoteSyncRequest, NoteSyncResponse, TaskSyncRequest, TaskSyncResponse } from "@/type/case-management/noteTaskTypes";
import { NotesTasksSectionTypes } from "@/type/case-management/noteTaskTypes";

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/case-management';

// Add new types for case checklists
export interface CaseChecklist {
    id: number;
    case: number;
    organization_status: number;
    item_name: string;
    description?: string;
    is_required: boolean;
    is_completed: boolean;
    is_applicable?: boolean;
    kpi_type?: string;
    file_ids?: string[];
    created_at: string;
    updated_at: string;
}

// Add type for query parameters
interface NoteTaskQueryParams {
    page: number;
    note_for?: NotesTasksSectionTypes;
    task_for?: NotesTasksSectionTypes;
    defendant_id?: string;
}

export interface CaseChecklistCreate {
    organization_status: number;
    item_name: string;
    description?: string;
    is_required: boolean;
    kpi_type?: string;
}

export interface CaseChecklistUpdate {
    is_completed?: boolean;
    is_applicable?: boolean;
    kpi_type?: string;
    item_name?: string;
    description?: string;
    is_required?: boolean;
    organization_status?: number;
    file_ids?: string[];
}

/**
 * Notes API
 */

/**
 * Hook to fetch a list of notes for a case
 * @param caseId - The ID of the case
 * @param noteFor - The section for which the notes are intended
 * @param defendantId - The ID of the defendant (optional)
 * @returns Query result containing the list of notes
 */
export const useNotesListQuery = (
    caseId: string,
    noteFor: NotesTasksSectionTypes = 'Case',
    defendantId?: string
) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<CaseNote>, Error>({
        queryKey: ["notes", caseId, noteFor, defendantId],
        queryFn: async (context: QueryFunctionContext) => {
            const params: NoteTaskQueryParams = {
                page: Number(context.pageParam) || 1,
                note_for: noteFor
            };

            if (noteFor === 'Negotiation' && defendantId) {
                params.defendant_id = defendantId;
            }

            const response = await axios.get<ListResponse<CaseNote>>(`${BASE_URL}/cases/${caseId}/notes/`, {
                params,
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        getNextPageParam: (lastPage) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                return url.searchParams.get('page');
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to fetch details of a specific note
 * @param caseId - The ID of the case
 * @param noteId - The ID of the note
 * @param options - Additional query options
 * @returns Query result containing the note details
 */
export const useNoteDetailsQuery = (caseId: string, noteId: string, options = {}) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ["notes", caseId, noteId],
        queryFn: async () => {
            const response = await axios.get<CaseNote>(
                `${BASE_URL}/cases/${caseId}/notes/${noteId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        ...options
    });
};

/**
 * Hook to create a new note
 * @param caseId - The ID of the case
 * @param noteFor - The section for which the notes are intended
 * @returns Mutation function to create a note
 */
export const useCreateNoteMutation = (caseId: string, noteFor: NotesTasksSectionTypes = 'Case') => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: CaseNoteWrite) => {
            const response = await axios.post<CaseNote>(
                `${BASE_URL}/cases/${caseId}/notes/`,
                {
                    ...data,
                    note_for: noteFor
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notes", caseId, noteFor] });
            toast({
                title: "Success",
                description: "Note created successfully",
            });
        }
    });
};

/**
 * Hook to update an existing note
 * @param caseId - The ID of the case
 * @param noteId - The ID of the note to update
 * @param noteFor - The section for which the notes are intended
 * @returns Mutation function to update a note
 */
export const useUpdateNoteMutation = (caseId: string, noteId: string, noteFor: NotesTasksSectionTypes = 'Case'): UseMutationResult<CaseNote, Error, CaseNoteWrite> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: CaseNoteWrite) => {
            const response = await axios.put<CaseNote>(`${BASE_URL}/notes/${noteId}/`, data, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notes", caseId, noteFor] });
            toast({
                title: "Success",
                description: "Note updated successfully",
            });
        }
    });
};

/**
 * Hook to delete a note
 * @param caseId - The ID of the case
 * @param noteId - The ID of the note to delete
 * @returns Mutation function to delete a note
 */
export const useDeleteNoteMutation = (caseId: string, noteId: string): UseMutationResult<void, Error, void> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async () => {
            await axios.delete(`${BASE_URL}/notes/${noteId}/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notes", caseId] });
            toast({
                title: "Success",
                description: "Note deleted successfully",
            });
        }
    });
};

/**
 * Tasks API
 */

/**
 * Hook to fetch a list of tasks for a case
 * @param caseId - The ID of the case
 * @param taskFor - The section for which the tasks are intended
 * @param defendantId - The ID of the defendant (optional)
 * @returns Query result containing the list of tasks
 */
export const useTasksListQuery = (
    caseId: string,
    taskFor: NotesTasksSectionTypes = 'Case',
    defendantId?: string
) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<Task>, Error>({
        queryKey: ["tasks", caseId, taskFor, defendantId],
        queryFn: async (context: QueryFunctionContext) => {
            const params: NoteTaskQueryParams = {
                page: Number(context.pageParam) || 1,
                task_for: taskFor
            };

            if (taskFor === 'Negotiation' && defendantId) {
                params.defendant_id = defendantId;
            }

            const response = await axios.get<ListResponse<Task>>(
                `${BASE_URL}/cases/${caseId}/tasks/`,
                {
                    params,
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        getNextPageParam: (lastPage) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                return url.searchParams.get('page');
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to fetch details of a specific task
 * @param caseId - The ID of the case
 * @param taskId - The ID of the task
 * @param options - Additional query options
 * @returns Query result containing the task details
 */
export const useTaskDetailsQuery = (caseId: string, taskId: string, options = {}): UseQueryResult<Task> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ["tasks", caseId, taskId],
        queryFn: async () => {
            const response = await axios.get<Task>(`${BASE_URL}/tasks/${taskId}/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        ...options
    });
};

/**
 * Hook to create a new task
 * @param caseId - The ID of the case
 * @param taskFor - The section for which the tasks are intended
 * @returns Mutation function to create a task
 */
export const useCreateTaskMutation = (caseId: string, taskFor: NotesTasksSectionTypes = 'Case') => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: TaskCreate) => {
            const response = await axios.post<Task>(
                `${BASE_URL}/cases/${caseId}/tasks/`,
                {
                    ...data,
                    task_for: taskFor
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["tasks", caseId, taskFor] });
            toast({
                title: "Success",
                description: "Task created successfully",
            });
        }
    });
};

/**
 * Hook to delete a task
 * @param caseId - The ID of the case
 * @returns Mutation function to delete a task
 */
export const useDeleteTaskMutation = (caseId: string): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (taskId: string) => {
            await axios.delete(`${BASE_URL}/tasks/${taskId}/`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["tasks", caseId] });
            toast({
                title: "Success",
                description: "Task deleted successfully",
            });
        }
    });
};

/**
 * Hook to update a task
 * @param caseId - The ID of the case
 * @param taskFor - The section for which the tasks are intended
 * @returns Mutation function to update a task
 */
export const useUpdateTaskMutation = (caseId: string, taskFor: NotesTasksSectionTypes = 'Case'): UseMutationResult<Task, Error, { taskId: string, data: TaskWrite }> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async ({ taskId, data }: { taskId: string, data: TaskWrite }) => {
            const response = await axios.put<Task>(`${BASE_URL}/tasks/${taskId}/`, data, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        onSuccess: () => {
            // Invalidate all task-related queries to ensure UI updates
            queryClient.invalidateQueries({ queryKey: ["tasks"] });
            queryClient.invalidateQueries({ queryKey: ["tasks", caseId] });
            queryClient.invalidateQueries({ queryKey: ["tasks", caseId, taskFor] });
            queryClient.invalidateQueries({ queryKey: ["all-tasks"] });
            queryClient.invalidateQueries({ queryKey: ["filtered-tasks"] });

            // Dispatch a custom event to force UI updates
            window.dispatchEvent(new CustomEvent('task-updated'));

            toast({
                title: "Success",
                description: "Task updated successfully",
            });
        }
    });
};

/**
 * Hook to assign a user to a task
 * @param caseId - The ID of the case
 * @param taskId - The ID of the task
 * @returns Mutation function to assign a user to the task
 */
export const useAssignTaskMutation = (caseId: string, taskId: string): UseMutationResult<Task, Error, number> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (userId: number) => {
            const response = await axios.post<Task>(`${BASE_URL}/tasks/${taskId}/assign_user/`, {
                assigned_to_id: userId
            }, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["tasks", caseId] });
            toast({
                title: "Success",
                description: "Task assigned successfully",
            });
        }
    });
};

/**
 * Hook to update the due date of a task
 * @param caseId - The ID of the case
 * @param taskId - The ID of the task
 * @returns Mutation function to update the task's due date
 */
export const useUpdateTaskDueDateMutation = (caseId: string, taskId: string): UseMutationResult<Task, Error, string> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (dueDate: string) => {
            const response = await axios.post<Task>(`${BASE_URL}/tasks/${taskId}/update_due_date/`, {
                due_date: dueDate
            }, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["tasks", caseId] });
            toast({
                title: "Success",
                description: "Task due date updated successfully",
            });
        }
    });
};

/**
 * Hook to fetch all tasks for the current user
 * @returns Query result containing the list of all tasks
 */
export const useAllTasksListQuery = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<Task>, Error>({
        queryKey: ["all-tasks"],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<Task>>(`${BASE_URL}/tasks/`, {
                params: {
                    page: pageParam
                },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        getNextPageParam: (lastPage: ListResponse<Task>) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                const page = url.searchParams.get('page');
                return page ? parseInt(page) : undefined;
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to update a checklist item
 * @param caseId - The ID of the case
 * @param checklistItemId - The ID of the checklist item
 * @returns Mutation function to update a checklist item
 */
export const useUpdateChecklistItemMutation = (caseId: string, checklistItemId: number) => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: CaseChecklistUpdate) => {
            const response = await axios.post<CaseChecklist>(
                `${BASE_URL}/cases/${caseId}/update_checklist_item/`,
                {
                    checklist_item_id: checklistItemId,
                    is_completed: data.is_completed,
                    file_ids: data.file_ids
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["case", caseId] });
            toast({
                title: "Success",
                description: "Checklist item updated successfully",
            });
        }
    });
};

/**
 * Hook to fetch all notes for the current user
 * @returns Query result containing the list of all notes
 */
export const useAllNotesListQuery = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<CaseNote>, Error>({
        queryKey: ["all-notes"],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<CaseNote>>(`${BASE_URL}/notes/`, {
                params: {
                    page: pageParam
                },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        getNextPageParam: (lastPage: ListResponse<CaseNote>) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                const page = url.searchParams.get('page');
                return page ? parseInt(page) : undefined;
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to fetch filtered tasks
 * @param filters - Object containing filter parameters
 * @returns Query result containing filtered tasks
 */
export const useFilteredTasksQuery = (filters: {
    status?: string;
    assigned_to?: number;
    exclude_closed_cases?: boolean;
    for_dashboard?: boolean;
    page?: number;
    page_size?: number;
}) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<Task>, Error>({
        queryKey: ["filtered-tasks", filters],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<Task>>(`${BASE_URL}/tasks/`, {
                params: {
                    ...filters,
                    page: pageParam,
                    page_size: filters.page_size || 100, // Use provided page_size or default to 100
                    include_case_details: true,
                    include_full_case_details: true // Add this parameter to get full case details
                },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        getNextPageParam: (lastPage: ListResponse<Task>) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                const page = url.searchParams.get('page');
                return page ? parseInt(page) : undefined;
            }
            return undefined;
        },
        initialPageParam: 1,
        refetchOnWindowFocus: true,
        refetchOnMount: true,
        staleTime: 10 * 1000, // 10 seconds
    });
};

/**
 * Hook to fetch filtered tasks with all available filter options
 * @param filters - Object containing all possible filter parameters
 * @returns Query result containing filtered tasks
 */
export const useTasksListQueryWithFilters = (filters: TaskFilters) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useInfiniteQuery<ListResponse<Task>, Error>({
        queryKey: ["tasks-filtered", filters],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await axios.get<ListResponse<Task>>(`${BASE_URL}/tasks/`, {
                params: {
                    ...filters,
                    page: pageParam,
                    page_size: 100, // Fetch 100 results at once
                    include_case_details: true,
                    include_full_case_details: true
                },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });
            return response.data;
        },
        getNextPageParam: (lastPage) => {
            if (lastPage.next) {
                const url = new URL(lastPage.next);
                return url.searchParams.get('page');
            }
            return undefined;
        },
        initialPageParam: 1
    });
};

/**
 * Hook to create a task with all available fields
 * @returns Mutation function to create a task
 */
export const useCreateTaskMutationWithAllFields = () => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: TaskCreate) => {
            const response = await axios.post<Task>(
                `${BASE_URL}/tasks/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["tasks-filtered"] });
            toast({
                title: "Success",
                description: "Task created successfully",
            });
        }
    });
};

export const getTasks = async () => {
  const accessToken = localStorage.getItem('accessToken');
  const response = await axios.get(`${BASE_URL}/tasks/`, {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });
  return response.data;
};

export const createTask = async (taskData: Partial<TaskCreate>) => {
  const accessToken = localStorage.getItem('accessToken');
  const response = await axios.post(`${BASE_URL}/tasks/`, taskData, {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });
  return response.data;
};

export const updateTask = async (taskData: Task) => {
  const accessToken = localStorage.getItem('accessToken');
  const response = await axios.put(`${BASE_URL}/tasks/${taskData.id}/`, taskData, {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });
  return response.data;
};

export const deleteTask = async (taskId: string) => {
  const accessToken = localStorage.getItem('accessToken');
  const response = await axios.delete(`${BASE_URL}/tasks/${taskId}/`, {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });
  return response.data;
};

/**
 * Case Checklist API
 */

/**
 * Hook to create a new case checklist item
 * @param caseId - The ID of the case
 * @returns Mutation function to create a checklist item
 */
export const useCreateCaseChecklistMutation = (caseId: string): UseMutationResult<CaseChecklist, Error, CaseChecklistCreate> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: CaseChecklistCreate) => {
            const response = await axios.post<CaseChecklist>(
                `${BASE_URL}/cases/${caseId}/case-checklists/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["case-checklists", caseId] });
            toast({
                title: "Success",
                description: "Checklist item created successfully",
            });
        }
    });
};

/**
 * Hook to update a case checklist item
 * @param caseId - The ID of the case
 * @returns Mutation function to update a checklist item
 */
export const useUpdateCaseChecklistMutation = (caseId: string): UseMutationResult<CaseChecklist, Error, { checklistId: string, data: CaseChecklistUpdate }> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async ({ checklistId, data }: { checklistId: string, data: CaseChecklistUpdate }) => {
            const response = await axios.patch<CaseChecklist>(
                `${BASE_URL}/cases/${caseId}/case-checklists/${checklistId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["case-checklists", caseId] });
            toast({
                title: "Success",
                description: "Checklist item updated successfully",
            });
        }
    });
};

/**
 * Hook to bulk create case checklist items
 * @param caseId - The ID of the case
 * @returns Mutation function to bulk create checklist items
 */
export const useBulkCreateCaseChecklistMutation = (caseId: string): UseMutationResult<CaseChecklist[], Error, CaseChecklistCreate[]> => {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useMutation({
        mutationFn: async (data: CaseChecklistCreate[]) => {
            const response = await axios.post<CaseChecklist[]>(
                `${BASE_URL}/cases/${caseId}/case-checklists/bulk_create/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["case-checklists", caseId] });
            toast({
                title: "Success",
                description: "Checklist items created successfully",
            });
        }
    });
};

/**
 * Hook to fetch case checklist items
 * @param caseId - The ID of the case
 * @returns Query result containing the list of checklist items
 */
export const useCaseChecklistsQuery = (caseId: string): UseQueryResult<CaseChecklist[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ["case-checklists", caseId],
        queryFn: async () => {
            const response = await axios.get<CaseChecklist[]>(
                `${BASE_URL}/cases/${caseId}/case-checklists/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        }
    });
};

export const useSyncNoteMutation = (
  caseId: string
): UseMutationResult<NoteSyncResponse, Error, NoteSyncRequest> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: NoteSyncRequest) => {
      const response = await axios.post<NoteSyncResponse>(
        `${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/notes/sync/sync/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast({
        title: "Success",
        description: data.message || "Note synced successfully",
      });
    }
  });
};

export const useSyncTaskMutation = (
  caseId: string
): UseMutationResult<TaskSyncResponse, Error, TaskSyncRequest> => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: TaskSyncRequest) => {
      const response = await axios.post<TaskSyncResponse>(
        `${process.env.NEXT_PUBLIC_DJANGO_URL_V2}/case-management/cases/${caseId}/tasks/sync/sync/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      toast({
        title: "Success",
        description: data.message || "Task synced successfully",
      });
    }
  });
};