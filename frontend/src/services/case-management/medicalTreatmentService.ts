import { useQuery, useMutation, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { 
    TreatmentProvider, 
    TreatmentProviderCreateRequest,
    TreatmentProviderPatchRequest,
    MedicalProviderCasesResponse,
    MedPayDepositSummary, 
    MedPayDepositCreateRequest, 
    MedPayDepositUpdateRequest,
    MedPayDepositResponse,
} from '@/type/case-management/medicalTreatmentTypes';
import { MedicalProviderWithContactCreateRequest, MedicalProviderContactCreateRequest, MedicalProviderUpdateRequest } from '@/type/case-management/medicalTreatmentTypes';
import { MedicalProvider } from '@/type/case-management/orgTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';


/**
 * Custom hook to fetch all treatment providers for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<TreatmentProvider[]>} Query result
 */
export const useTreatmentProvidersQuery = (caseId: string): UseQueryResult<TreatmentProvider[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['treatmentProviders', caseId],
        queryFn: async () => {
            console.log('fetching treatment providers');
            const { data } = await axios.get<TreatmentProvider[]>(
                `${BASE_URL}/cases/${caseId}/treatment-providers/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

/**
 * Custom hook to create a treatment provider
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateTreatmentProviderMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: TreatmentProviderCreateRequest) => {
            const response = await axios.post<TreatmentProvider>(
                `${BASE_URL}/cases/${caseId}/treatment-providers/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['treatmentProviders', caseId] });
            toast({
                title: "Success",
                description: "Treatment provider created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a treatment provider
 * @param {string} caseId - The ID of the case
 * @param {string} providerId - The ID of the treatment provider
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateTreatmentProviderMutation = (caseId: string, providerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: TreatmentProviderCreateRequest) => {
            const response = await axios.patch<TreatmentProvider>(
                `${BASE_URL}/cases/${caseId}/treatment-providers/${providerId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['treatmentProviders', caseId] });
            toast({
                title: "Success",
                description: "Treatment provider updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a treatment provider
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteTreatmentProviderMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ caseId, providerId }: { caseId: string; providerId: string }) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/treatment-providers/${providerId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: (_, { caseId }) => {
            queryClient.invalidateQueries({ queryKey: ['treatmentProviders', caseId] });
            toast({
                title: "Success",
                description: "Treatment provider deleted successfully",
                variant: "default"
            });
        }
    });
};

type CreateMedicalProviderMutationResult = UseMutationResult<
  MedicalProvider,
  Error,
  MedicalProviderWithContactCreateRequest
>;

export const useCreateMedicalProviderWithContactMutation = (): CreateMedicalProviderMutationResult => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: MedicalProviderWithContactCreateRequest) => {
            // First create the medical provider
            const providerResponse = await axios.post<MedicalProvider>(
                `${BASE_URL}/medical-providers/`,
                data.provider,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );

            // If contact data exists, create the contact
            if (data.contact) {
                const contactData: MedicalProviderContactCreateRequest = {
                    ...data.contact,
                    medical_provider: providerResponse.data.id
                };

                await axios.post(
                    `${BASE_URL}/medical-providers/${providerResponse.data.id}/contacts/`,
                    contactData,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
            }

            return providerResponse.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['medicalProviders'] });
            toast({
                title: "Success",
                description: "Medical provider created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a medical provider with contact
 */
export const useUpdateMedicalProviderWithContactMutation = (providerId: string): UseMutationResult<
    MedicalProvider,
    Error,
    MedicalProviderUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: MedicalProviderUpdateRequest) => {
            const providerResponse = await axios.put<MedicalProvider>(
                `${BASE_URL}/medical-providers/${providerId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return providerResponse.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['medicalProviders'] });
            toast({
                title: "Success",
                description: "Medical provider updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to patch treatment provider status
 * @param {string} caseId - The ID of the case
 * @param {string} providerId - The ID of the treatment provider
 * @returns {UseMutationResult} Mutation result
 */
export const usePatchTreatmentProviderStatusMutation = (caseId: string, providerId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: TreatmentProviderPatchRequest) => {
            const response = await axios.patch<TreatmentProvider>(
                `${BASE_URL}/cases/${caseId}/treatment-providers/${providerId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['treatmentProviders', caseId] });
            toast({
                title: "Success",
                description: "Status updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all cases associated with a medical provider
 * @param {number} medicalProviderId - The ID of the medical provider
 * @param {string} caseId - Optional case ID to filter the results
 * @param {number} page - Current page number (defaults to 1)
 * @param {number} pageSize - Number of items per page (defaults to 10)
 * @returns {UseQueryResult} Query result with cases data
 */
export const useMedicalProviderCasesQuery = (
  medicalProviderId: number | undefined, 
  caseId?: string,
  page: number = 1,
  pageSize: number = 10
): UseQueryResult<MedicalProviderCasesResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['medicalProviderCases', medicalProviderId, caseId, page, pageSize],
        queryFn: async () => {
            const response = await axios.post<MedicalProviderCasesResponse>(
                `${BASE_URL}/medical-providers/fetch_all_cases_for_medical_provider/?page=${page}&page_size=${pageSize}`,
                { medical_provider_id: medicalProviderId, case_id: caseId },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        enabled: !!medicalProviderId
    });
};

/**
 * Custom hook to fetch all MedPay deposits for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<MedPayDepositListResponse>} Query result
 */
export const useMedPayDepositsQuery = (caseId: string): UseQueryResult<MedPayDepositResponse[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['medPayDeposits', caseId],
        queryFn: async () => {
            const { data } = await axios.get<MedPayDepositResponse[]>(
                `${BASE_URL}/cases/${caseId}/medpay-deposits/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

/**
 * Custom hook to fetch MedPay deposit summary for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<MedPayDepositSummary>} Query result
 */
export const useMedPayDepositSummaryQuery = (caseId: string): UseQueryResult<MedPayDepositSummary> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['medPayDepositSummary', caseId],
        queryFn: async () => {
            const { data } = await axios.get<MedPayDepositSummary>(
                `${BASE_URL}/cases/${caseId}/medpay-deposits/summary/`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!caseId
    });
};

/**
 * Custom hook to create a MedPay deposit
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result
 */
export const useCreateMedPayDepositMutation = (caseId: string) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: MedPayDepositCreateRequest) => {
            const response = await axios.post<MedPayDepositResponse>(
                `${BASE_URL}/cases/${caseId}/medpay-deposits/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['medPayDeposits', caseId] });
            queryClient.invalidateQueries({ queryKey: ['medPayDepositSummary', caseId] });
            toast({
                title: "Success",
                description: "MedPay deposit created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a MedPay deposit
 * @param {string} caseId - The ID of the case
 * @param {number} depositId - The ID of the MedPay deposit
 * @returns {UseMutationResult} Mutation result
 */
export const useUpdateMedPayDepositMutation = (caseId: string, depositId: number) => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: MedPayDepositUpdateRequest) => {
            const response = await axios.put<MedPayDepositResponse>(
                `${BASE_URL}/cases/${caseId}/medpay-deposits/${depositId}/`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['medPayDeposits', caseId] });
            queryClient.invalidateQueries({ queryKey: ['medPayDepositSummary', caseId] });
            toast({
                title: "Success",
                description: "MedPay deposit updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete a MedPay deposit
 * @returns {UseMutationResult} Mutation result
 */
export const useDeleteMedPayDepositMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async ({ caseId, depositId }: { caseId: string; depositId: number }) => {
            await axios.delete(
                `${BASE_URL}/cases/${caseId}/medpay-deposits/${depositId}/`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            );
        },
        onSuccess: (_, { caseId }) => {
            queryClient.invalidateQueries({ queryKey: ['medPayDeposits', caseId] });
            queryClient.invalidateQueries({ queryKey: ['medPayDepositSummary', caseId] });
            toast({
                title: "Success",
                description: "MedPay deposit deleted successfully",
                variant: "default"
            });
        }
    });
};
