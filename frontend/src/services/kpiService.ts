import { useQuery, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/kpi-and-reports';

// Types for KPI metrics
export interface KPIMetric {
    value: number;
    type: 'count' | 'monetary' | 'time' | 'percentage' | 'rate';
    unit: string;
}

export interface KPIMetrics {
    primary: KPIMetric;
    total_items: number;
    rate?: KPIMetric;
}

// Define specific types for additional metrics
export interface AgeBreakdown {
    years: number;
    months: number;
    days: number;
    human_readable: string;
}

export interface NotesMetrics {
    average_notes_per_case: number;
}

export interface CareCallsMetrics {
    status: 'not_implemented' | 'implemented';
}

export interface AdditionalMetrics {
    breakdown?: AgeBreakdown;
    average_notes_per_case?: number;
    status?: 'not_implemented' | 'implemented';
}

export interface KPI {
    kpi_type: string;
    scope: 'organization' | 'user';
    metrics: KPIMetrics;
    additional_metrics?: AdditionalMetrics;
}

export interface CaseKPIResponse {
    case_demand_ready: KPI;
    case_closed: KPI;
    cases_touched: KPI;
    treatment_period: KPI;
    avg_case_age: KPI;
    care_calls_percentage: KPI;
    notes_entered: KPI;
    three_plus_providers: KPI;
    cases_in_negotiation: KPI;
    total_attorney_fees: KPI;
    depositions_taken: KPI;
    mediations_arb_trials: KPI;
    hearings: KPI;
}

export interface LeadKPIResponse {
    lead_kpis: KPI[];
    last_updated: string;
}

interface KPIQueryParams {
    user_id?: string;
    org_wide?: boolean;
}

/**
 * Get Case KPIs
 * 
 * Retrieves case KPIs for the organization or specific user
 * Default behavior:
 * - No params: returns requesting user's KPIs
 * - user_id: returns specific user's KPIs
 * - org_wide=true: returns organization-wide KPIs
 * 
 * Endpoint: GET /kpi-and-reports/case-kpis/all_kpis/
 * Authentication: Required
 */
export const useCaseKPIsQuery = (params: KPIQueryParams = {}): UseQueryResult<CaseKPIResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['case-kpis', params],
        queryFn: async () => {
            const { data } = await axios.get<CaseKPIResponse>(
                `${BASE_URL}/case-kpis/all_kpis/`,
                {
                    params: {
                        user_id: params.user_id,
                        org_wide: params.org_wide
                    },
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
};

/**
 * Get Lead KPIs
 * 
 * Retrieves lead KPIs for the organization or specific user
 * Default behavior:
 * - No params: returns requesting user's KPIs
 * - user_id: returns specific user's KPIs
 * - org_wide=true: returns organization-wide KPIs
 * 
 * Endpoint: GET /kpi-and-reports/lead-kpis/all_kpis/
 * Authentication: Required
 */
export const useLeadKPIsQuery = (params: KPIQueryParams = {}): UseQueryResult<LeadKPIResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['lead-kpis', params],
        queryFn: async () => {
            const { data } = await axios.get<LeadKPIResponse>(
                `${BASE_URL}/lead-kpis/all_kpis/`,
                {
                    params: {
                        user_id: params.user_id,
                        org_wide: params.org_wide
                    },
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return data;
        },
        enabled: !!accessToken
    });
}; 