import { useQuery, useMutation, UseQueryResult } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/chat-with-case';

// Add interface for process file request/response
export interface ProcessFileRequest {
    file_id: string;
    case_id: string;
}

export interface ProcessFileResponse {
    status: string;
    task_id: string;
    operation_id: number;
    message: string;
}

// Interface for OCR status response
export interface OcrStatusResponse {
    status: string;
    data: {
        text: string;
        metadata: {
            model: string;
            pages_processed: number;
            doc_size_bytes: number;
        };
    };
}

// Add interface for processed files response
export interface ProcessedFile {
    file_id: string;
    file_name: string;
    status: string;
    created_at: string;
    updated_at: string;
    processing_time: number | null;
    pages_processed: number;
    doc_size_bytes: number;
    metadata: {
        document_type: string;
        source: string;
        original_filename: string;
    };
    operation_id: number;
    task_id: string;
    error_message: string | null;
    retry_count: number | null;
}

export interface ProcessedFilesResponse {
    status: string;
    message: string;
    data: {
        case_id: string;
        files: ProcessedFile[];
        total_count: number;
        status_counts: {
            [key: string]: number;
        };
    };
}

// Add interface for delete file response
export interface DeleteFileResponse {
    status: string;
    message: string;
}

// Query hook for getting OCR status
export const useOcrStatusQuery = (documentId: string): UseQueryResult<OcrStatusResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['ocrStatus', documentId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<OcrStatusResponse>(
                    `${BASE_URL}/ocr/status/${documentId}/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch OCR status';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive"
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!documentId
    });
};

// Add the process file mutation hook
export const useProcessFileMutation = () => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (request: ProcessFileRequest) => {
            try {
                const { data } = await axios.post<ProcessFileResponse>(
                    `${BASE_URL}/ocr/process-storage-file/`,
                    request,
                    {
                        headers: { 
                            Authorization: `Bearer ${accessToken}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to process file';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive"
                });
                throw error;
            }
        }
    });
};

// Add the query hook for getting processed files
export const useProcessedFilesQuery = (caseId: string) => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['processedFiles', caseId],
        queryFn: async () => {
            try {
                const { data } = await axios.get<ProcessedFilesResponse>(
                    `${BASE_URL}/ocr/files/${caseId}/`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch processed files';
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive"
                });
                throw error;
            }
        },
        enabled: !!accessToken && !!caseId
    });
};

// Add interface for chat message from API
export interface ApiChatMessage {
  role: string;
  content: string;
  created_at: string;
  user: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  } | null;
}

export interface ConversationResponse {
  id: string;
  title: string;
  created_at: string;
  messages: ApiChatMessage[];
  case_id: string;
}

export interface ConversationsResponse {
  status: string;
  message: string;
  data: ConversationResponse[];
}

export interface SendMessageResponse {
  status: string;
  message: string;
  data: ConversationResponse;
}

export interface ConversationUpdateResponse {
  status: string;
  message: string;
  data: ConversationResponse;
}

// Remove any other interfaces that might be conflicting
export const useConversationsQuery = (caseId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useQuery<ConversationsResponse>({
    queryKey: ['conversations', caseId],
    queryFn: async () => {
      const { data } = await axios.get<ConversationsResponse>(
        `${BASE_URL}/chat/?case_id=${caseId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    }
  });
};

// Add mutation hook for sending messages
export const useUpdateConversationTitleMutation = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();
  
  return useMutation<ConversationUpdateResponse, Error, { conversationId: string; title: string }>({
    mutationFn: async ({ conversationId, title }) => {
      const { data } = await axios.patch<ConversationUpdateResponse>(
        `${BASE_URL}/chat/conversation/${conversationId}/`,
        { title },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch conversations query
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      toast({
        title: "Success",
        description: "Conversation title updated successfully"
      });
    },
    onError: (error) => {
      console.error('Error updating conversation title:', error);
      toast({
        title: "Error",
        description: "Failed to update conversation title",
        variant: "destructive"
      });
    }
  });
};

export const useSendMessageMutation = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation<SendMessageResponse, Error, { conversationId: string; message: string; caseId: string }>({
    mutationFn: async ({ conversationId, message, caseId }) => {
      const payload = {
        role: 'user',
        content: message,
        case_id: caseId,
        ...(conversationId && { conversation_id: conversationId })
      };

      const { data } = await axios.post<SendMessageResponse>(
        `${BASE_URL}/chat/`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch conversations query
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
    onError: (error) => {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      });
    }
  }); 
};

// Add mutation hook for deleting processed files
export const useDeleteProcessedFileMutation = () => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation<DeleteFileResponse, Error, string>({
        mutationFn: async (fileId: string) => {
            try {
                const { data } = await axios.delete<DeleteFileResponse>(
                    `${BASE_URL}/ocr/process-storage-file/${fileId}/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete file';
                throw new Error(errorMessage);
            }
        },
        onSuccess: (data) => {
            // Invalidate and refetch processed files query
            queryClient.invalidateQueries({ queryKey: ['processedFiles'] });
            toast({
                title: "Success",
                description: data.message || "File deleted successfully"
            });
        },
        onError: (error) => {
            console.error('Error deleting file:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to delete file",
                variant: "destructive"
            });
        }
    });
}; 