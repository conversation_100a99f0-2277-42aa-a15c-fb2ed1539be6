// src/services/documentService.ts

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import { LeadDocumentUpload, LeadDocumentDownloadUrl, LeadDocument } from '@/type/leadDocumentTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/lead-management';

// Document Upload Hook
export const useUploadDocument = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: LeadDocumentUpload) => {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('document_type', data.document_type);
      formData.append('lead', data.lead);
      if (data.notes) formData.append('notes', data.notes);

      const response = await axios.post<Document>(
        `${BASE_URL}/documents/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      toast({
        title: "Success",
        description: "Document uploaded successfully",
        variant: "default"
      });
    }
  });
};

// Get Documents for a Lead
export const useLeadDocuments = (leadId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['lead-documents', leadId],
    queryFn: async () => {
      const { data } = await axios.get<LeadDocument[]>(
        `${BASE_URL}/leads/${leadId}/documents/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    enabled: !!leadId
  });
};

// Get Document Download URL
export const useGetDocumentDownloadUrl = (documentId: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  
  return useQuery({
    queryKey: ['documentDownloadUrl', documentId],
    queryFn: async () => {
      const { data } = await axios.get<LeadDocumentDownloadUrl>(
        `${BASE_URL}/documents/${documentId}/download_url/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      return data.url;
    },
    enabled: !!documentId
  });
};

// Delete Document
export const useDeleteDocument = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (documentId: string) => {
      await axios.delete(
        `${BASE_URL}/documents/${documentId}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      toast({
        title: "Success",
        description: "Document deleted successfully",
        variant: "default"
      });
    }
  });
};