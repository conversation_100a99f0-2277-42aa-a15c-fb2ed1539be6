import { useMutation } from '@tanstack/react-query';
import { useDispatch } from 'react-redux';
import axios from '@/lib/axios';
import { AxiosError } from 'axios';
import { setUser, setTokens } from '@/store/slices/authSlice';
import { useToast } from '@/hooks/use-toast';
import Cookies from 'js-cookie';

// Disable automatic background queries for organization cases
axios.interceptors.request.use((config) => {
  const isLoginPage = typeof window !== 'undefined' && window.location.pathname === '/login';
  const isOrganizationRequest = config.url?.includes('organization_cases') || 
                               config.url?.includes('organization-case-statuses') ||
                               config.url?.includes('organization-management');
  
  if (isLoginPage && isOrganizationRequest) {
    return Promise.reject('Request cancelled - unauthorized organization request on login page');
  }
  return config;
});

interface LoginResponse {
  name: string;
  access: string;
  refresh: string;
  email: string;
  user_id: string;
  role: string;
  subscription_type: string;
  organization: {
    id: string;
    name: string;
  };
}

interface ErrorResponse {
  detail: string;
}

// User login, forgot password, reset password, verify token
export function useAuth() {
  const dispatch = useDispatch();
  const { toast } = useToast();

  // User login
  const login = useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      try {
        const { data } = await axios.post<LoginResponse>(
          '/users/login/',
          { email, password }
        );

        dispatch(setUser({
          id: parseInt(data.user_id), // Convert user_id to id as a number
          name: data.name,
          email: data.email,
          user_id: data.user_id,
          role: data.role,
          subscription_type: data.subscription_type,
          organization: data.organization,
        }));

        dispatch(setTokens({
          accessToken: data.access,
          refreshToken: data.refresh,
        }));

        // Set both access and refresh tokens in cookies
        Cookies.set('accessToken', data.access, {
          secure: true,
          sameSite: 'strict',
          expires: 7
        });

        Cookies.set('refreshToken', data.refresh, {
          secure: true,
          sameSite: 'strict',
          expires: 30  // Longer expiry for refresh token
        });

        return data;
      } catch (error) {
        const axiosError = error as AxiosError<ErrorResponse>;
        const errorMessage = axiosError.response?.data?.detail || 'Login failed';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw new Error(errorMessage);
      }
    },
  });

  // User forgot password
  const forgotPassword = useMutation({
    mutationFn: async (email: string) => {
      try {
        const { data } = await axios.post(
          '/users/forgot-password/',
          { email }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Forgot password request failed';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Reset password link has been sent to your mail.",
        variant: 'default',
      });
    }
  });

  // User reset password
  const resetPassword = useMutation({
    mutationFn: async ({
      new_password,
      confirm_password,
      uid,
      token,
    }: {
      new_password: string;
      confirm_password: string;
      uid: string | null;
      token: string | null;
    }) => {
      try {
        const { data } = await axios.post(
          `/users/reset-password?uid=${uid}&token=${token}`,
          { new_password, confirm_password }
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw new Error(errorMessage);
      }
    }
  });

  // User verify token
  const verifyToken = useMutation({
    mutationFn: async ({ uid, token }: { uid: string; token: string }) => {
      try {
        const { data } = await axios.get(
          `/users/reset-password?uid=${uid}&token=${token}`
        );
        return data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Token verification failed.';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        throw new Error(errorMessage);
      }
    }
  });

  const logout = () => {
    dispatch(setUser(null));
    dispatch(setTokens({ accessToken: null, refreshToken: null }));

    // Remove both tokens from cookies
    Cookies.remove('accessToken');
    Cookies.remove('refreshToken');
  };

  return {
    login,
    logout,
    forgotPassword,
    resetPassword,
    verifyToken
  };
}