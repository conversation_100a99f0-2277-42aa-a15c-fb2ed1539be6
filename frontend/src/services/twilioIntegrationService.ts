import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { useToast } from '@/hooks/use-toast';
import { store } from '@/store';
import { TwilioMessage, TwilioMessageListResponse } from '@/type/twilio';
import { useState, useEffect } from 'react';
import { formatPhoneNumberForApi } from '@/components/ui/phone-link';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/users/twilio';

// Helper to get token from Redux store
const getToken = () => {
  const state = store.getState();
  return state.auth.accessToken;
};

interface ConversationSummary {
  last_message: string;
  last_message_date: string;
  message_type: 'incoming' | 'outgoing';
  total_messages: number;
}

interface MessageStatistics {
  total_messages: number;
  incoming_messages: number;
  outgoing_messages: number;
  unique_contacts: number;
  failed_messages: number;
}

interface UnreadMessage {
  id: number;
  message_sid: string;
  from_number: string;
  body: string;
  created_at: string;
}

interface ScheduledMessage {
  id: number;
  to_number: string;
  message_body: string;
  scheduled_time: string;
  status: 'pending' | 'sent' | 'cancelled';
  created_at: string;
  updated_at: string;
  case_id?: string;
}

// Add these new utility functions at the top of the file
const POLLING_INTERVALS = {
  ACTIVE: 60000,    // 60 seconds when user is active
  INACTIVE: 30000, // 30 seconds when tab is not focused
  BACKUP: 60000,   // 1 minute when no activity for a while
};

// Create a class to manage conversation state
class ConversationState {
  private static instance: ConversationState;
  private lastMessageTimestamp: string | null = null;
  private consecutiveEmptyPolls: number = 0;

  static getInstance() {
    if (!this.instance) {
      this.instance = new ConversationState();
    }
    return this.instance;
  }

  reset() {
    this.lastMessageTimestamp = null;
    this.consecutiveEmptyPolls = 0;
  }

  updateState(timestamp: string | null, emptyPolls: number) {
    this.lastMessageTimestamp = timestamp;
    this.consecutiveEmptyPolls = emptyPolls;
  }

  getLastMessageTimestamp() {
    return this.lastMessageTimestamp;
  }

  getConsecutiveEmptyPolls() {
    return this.consecutiveEmptyPolls;
  }
}

const twilioService = {
  // Check if Twilio is integrated
  async checkIntegration(): Promise<boolean> {
    const token = getToken();
    const { data } = await axios.get(`${BASE_URL}/status/`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return data.is_integrated;
  },

  // Initialize Twilio integration
  async initializeIntegration(accountSid: string, authToken: string): Promise<{ success: boolean; message: string }> {
    const token = getToken();
    const { data } = await axios.post(
      `${BASE_URL}/initialize/`,
      { account_sid: accountSid, auth_token: authToken },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  // Make a phone call
  async makeCall(to: string, message: string, twiml_url?: string): Promise<{ success: boolean; callSid?: string }> {
    const token = getToken();
    // Format the phone number to E.164 format for API calls
    const formattedPhoneNumber = formatPhoneNumberForApi(to);
    const { data } = await axios.post(
      `${BASE_URL}/call/make/`,
      { to: formattedPhoneNumber, message, twiml_url },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  // Send SMS
  async sendSMS(
    to_number: string,
    message: string,
    media_files?: File[],
    organization_id?: string,
    case_id?: string,
    lead_id?: string
  ): Promise<{ success: boolean; message_sid?: string }> {
    const token = getToken();
    const formData = new FormData();
    // Format the phone number to E.164 format for API calls
    const formattedPhoneNumber = formatPhoneNumberForApi(to_number);
    formData.append('to_number', formattedPhoneNumber);
    formData.append('message', message);
    if (organization_id) {
      formData.append('organization_id', organization_id);
    }
    if (media_files) {
      media_files.forEach((file) => {
        formData.append('media_files', file);
      });
    }

    if (case_id) {
      formData.append('case_id', case_id);
    }
    if (lead_id) {
      formData.append('lead_id', lead_id);
    }

    const { data } = await axios.post(
      `${BASE_URL}/sms/send/`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        }
      }
    );
    return data;
  },

  // New message methods
  async getMessageStatus(messageSid: string): Promise<TwilioMessage> {
    const token = getToken();
    const { data } = await axios.get(
      `${BASE_URL}/sms/${messageSid}/`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  async getMessages(page = 1, perPage = 10, organizationId?: string): Promise<TwilioMessageListResponse> {
    const token = getToken();
    const { data } = await axios.get(
      `${BASE_URL}/sms/messages/`,
      {
        params: { page, per_page: perPage, organization_id: organizationId },
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  // New conversation methods
  async getRecentConversations(days = 30): Promise<Record<string, ConversationSummary>> {
    const token = getToken();
    const { data } = await axios.get(`${BASE_URL}/sms/conversations/`, {
      params: { days },
      headers: { Authorization: `Bearer ${token}` }
    });
    return data;
  },

  async getConversationHistory(phoneNumber: string, params?: {
    lead_id?: string;
    case_id?: string;
  }): Promise<TwilioMessage[]> {
    const token = getToken();
    // Format the phone number to E.164 format for API calls
    const formattedPhoneNumber = formatPhoneNumberForApi(phoneNumber);

    const { data } = await axios.get(
      `${BASE_URL}/sms/conversations/${formattedPhoneNumber}/`,
      {
        headers: { Authorization: `Bearer ${token}` },
        params: {
          include_media: true,
          lead_id: params?.lead_id,
          case_id: params?.case_id
        }
      }
    );
    return data;
  },

  async getUnreadMessages(): Promise<UnreadMessage[]> {
    const token = getToken();
    const { data } = await axios.get(
      `${BASE_URL}/sms/messages/unread/`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  async markMessagesAsRead(messageIds: number[]): Promise<{ updated: number }> {
    const token = getToken();
    const { data } = await axios.post(
      `${BASE_URL}/sms/messages/mark-read/`,
      { message_ids: messageIds },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  async getMessageStatistics(days = 30): Promise<MessageStatistics> {
    const token = getToken();
    const { data } = await axios.get(
      `${BASE_URL}/sms/stats/`,
      {
        params: { days },
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  // Schedule Message Service
  async scheduleMessage(
    to_number: string,
    message_body: string,
    scheduled_time: string,
    case_id?: string,
    lead_id?: string
  ): Promise<{ success: boolean; message: string }> {
    const token = getToken();
    // Format the phone number to E.164 format for API calls
    const formattedPhoneNumber = formatPhoneNumberForApi(to_number);
    const { data } = await axios.post(
      `${BASE_URL}/scheduled-messages/`,
      {
        to_number: formattedPhoneNumber,
        message_body,
        scheduled_time,
        ...(case_id && { case_id }),
        ...(lead_id && { lead_id })
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  // Get Scheduled Messages
  async getScheduledMessages(caseId?: string): Promise<ScheduledMessage[]> {
    const token = getToken();
    const { data } = await axios.get(
      `${BASE_URL}/scheduled-messages/`,
      {
        headers: { Authorization: `Bearer ${token}` },
        params: { case_id: caseId }
      }
    );
    return data;
  },

  // Update Scheduled Message
  async updateScheduledMessage(
    messageId: number,
    updates: { message_body?: string; scheduled_time?: string }
  ): Promise<ScheduledMessage> {
    const token = getToken();
    const { data } = await axios.patch(
      `${BASE_URL}/scheduled-messages/${messageId}/`,
      updates,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  // Cancel Scheduled Message
  async cancelScheduledMessage(messageId: number): Promise<{ success: boolean }> {
    const token = getToken();
    const { data } = await axios.delete(
      `${BASE_URL}/scheduled-messages/${messageId}/`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return data;
  },

  // React Query Hooks
  useCheckIntegration() {
    return useQuery({
      queryKey: ['twilio-integration'],
      queryFn: () => this.checkIntegration(),
    });
  },

  useInitializeIntegration() {
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: ({ accountSid, authToken }: { accountSid: string; authToken: string }) =>
        this.initializeIntegration(accountSid, authToken),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['twilio-integration'] });
        toast({
          title: "Success",
          description: "Twilio integration completed successfully",
          variant: "default",
        });
      }
    });
  },

  useMakeCall() {
    const { toast } = useToast();

    return useMutation({
      mutationFn: ({ to, message, twiml_url }: { to: string; message: string; twiml_url?: string }) =>
        this.makeCall(to, message, twiml_url),
      onSuccess: () => {
        toast({
          title: "Success",
          description: "Call initiated successfully",
          variant: "default",
        });
      }
    });
  },

  // Updated and new React Query Hooks for messaging
  useSendSMS() {
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: ({
        to_number,
        message,
        media_files,
        organization_id,
        case_id,
        lead_id
      }: {
        to_number: string;
        message: string;
        media_files?: File[];
        organization_id?: string;
        case_id?: string;
        lead_id?: string;
      }) => this.sendSMS(to_number, message, media_files, organization_id, case_id, lead_id),
      onMutate: async ({ to_number, message, case_id, lead_id }) => {
        // Format the phone number for consistent query keys
        const formattedPhoneNumber = formatPhoneNumberForApi(to_number);

        // Cancel any outgoing refetches
        await queryClient.cancelQueries({
          queryKey: ['twilio-conversation', formattedPhoneNumber]
        });

        // Get the current conversation
        const previousMessages = queryClient.getQueryData<TwilioMessage[]>([
          'twilio-conversation',
          formattedPhoneNumber
        ]);

        // Create optimistic message
        const optimisticMessage: TwilioMessage = {
          id: `temp-${Date.now()}`,
          message_sid: `temp-${Date.now()}`,
          message_type: 'outgoing',
          from_number: to_number,
          to_number: to_number,
          body: message,
          status: 'sending',
          media_urls: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization_id: '',
          case_id: case_id,
          lead_id: lead_id
        };

        // Update the conversation with optimistic message
        queryClient.setQueryData<TwilioMessage[]>(
          ['twilio-conversation', formattedPhoneNumber],
          old => old ? [...old, optimisticMessage] : [optimisticMessage]
        );

        return { previousMessages, formattedPhoneNumber };
      },
      onError: (err, _variables, context?: { previousMessages?: TwilioMessage[], formattedPhoneNumber?: string }) => {
        // Revert to previous messages on error
        if (context?.previousMessages && context?.formattedPhoneNumber) {
          queryClient.setQueryData(
            ['twilio-conversation', context.formattedPhoneNumber],
            context.previousMessages
          );
        }
        toast({
          title: "Error",
          description: err instanceof Error ? err.message : "Failed to send message",
          variant: "destructive",
        });
      },
      onSuccess: (data, variables) => {
        // Format the phone number for consistent query keys
        const formattedPhoneNumber = formatPhoneNumberForApi(variables.to_number);
        // Invalidate and refetch
        queryClient.invalidateQueries({
          queryKey: ['twilio-conversation', formattedPhoneNumber]
        });
        toast({
          title: "Success",
          description: "Message sent successfully",
          variant: "default",
        });
      },
    });
  },

  useGetMessageStatus() {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: (messageSid: string) => this.getMessageStatus(messageSid),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['twilio-messages'] });
      }
    });
  },

  useGetMessages(page = 1, perPage = 10, organizationId?: string) {
    return useQuery({
      queryKey: ['twilio-messages', page, perPage, organizationId],
      queryFn: () => this.getMessages(page, perPage, organizationId),
    });
  },

  // New React Query Hooks
  useGetRecentConversations(days = 30) {
    return useQuery({
      queryKey: ['twilio-conversations', days],
      queryFn: () => this.getRecentConversations(days),
    });
  },

  resetConversationState() {
    ConversationState.getInstance().reset();
  },

  useGetConversationHistory(phoneNumber: string, param?: {
    lead_id?: string;
    case_id?: string;
  }) {
    const [pollingInterval, setPollingInterval] = useState(POLLING_INTERVALS.ACTIVE);
    const conversationState = ConversationState.getInstance();
    // Format the phone number consistently for query key
    const formattedPhoneNumber = phoneNumber ? formatPhoneNumberForApi(phoneNumber) : "";

    // Handle tab visibility
    useEffect(() => {
      const handleVisibilityChange = () => {
        setPollingInterval(document.hidden ? POLLING_INTERVALS.INACTIVE : POLLING_INTERVALS.ACTIVE);
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }, []);

    return useQuery({
      queryKey: ['twilio-conversation', formattedPhoneNumber],
      queryFn: async () => {
        if (!phoneNumber) return [];

        const params: Record<string, string> = {
          include_media: 'true',
          ...(param?.lead_id && { lead_id: param?.lead_id }),
          ...(param?.case_id && { case_id: param?.case_id })
        };

        // Add timestamp filter if we have a last message
        if (conversationState.getLastMessageTimestamp()) {
          params.after_timestamp = conversationState.getLastMessageTimestamp() || '';
        }

        const response = await this.getConversationHistory(phoneNumber, params);

        // Update polling strategy based on response
        if (response.length > 0) {
          conversationState.updateState(response[response.length - 1].created_at, 0);
          setPollingInterval(POLLING_INTERVALS.ACTIVE);
        } else {
          conversationState.updateState(null, conversationState.getConsecutiveEmptyPolls() + 1);
          if (conversationState.getConsecutiveEmptyPolls() > 5) {
            setPollingInterval(POLLING_INTERVALS.BACKUP);
          }
        }

        return response;
      },
      enabled: !!phoneNumber,
      refetchInterval: pollingInterval,
      // Add these options for better caching
      staleTime: 1000, // Consider data fresh for 1 second
      gcTime: 5 * 60 * 1000, // Keep unused data in cache for 5 minutes
      // Merge function to handle incremental updates
      select: (data) => {
        // Sort messages by timestamp
        return [...data].sort((a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      },
    });
  },

  useGetUnreadMessages() {
    return useQuery({
      queryKey: ['twilio-unread-messages'],
      queryFn: () => this.getUnreadMessages(),
    });
  },

  useMarkMessagesAsRead() {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: (messageIds: number[]) => this.markMessagesAsRead(messageIds),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['twilio-unread-messages'] });
        queryClient.invalidateQueries({ queryKey: ['twilio-conversations'] });
      },
    });
  },

  useGetMessageStatistics(days = 30) {
    return useQuery({
      queryKey: ['twilio-statistics', days],
      queryFn: () => this.getMessageStatistics(days),
    });
  },

  // Schedule Message Hook
  useScheduleMessage() {
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: ({
        to_number,
        message_body,
        scheduled_time,
        case_id,
        lead_id
      }: {
        to_number: string;
        message_body: string;
        scheduled_time: string;
        case_id?: string;
        lead_id?: string;
      }) => this.scheduleMessage(to_number, message_body, scheduled_time, case_id, lead_id),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['scheduled-messages'] });
        toast({
          title: "Success",
          description: "Message scheduled successfully",
          variant: "default",
        });
      }
    });
  },

  // Get Scheduled Messages Hook
  useGetScheduledMessages(caseId?: string) {
    return useQuery({
      queryKey: ['scheduled-messages', caseId],
      queryFn: () => this.getScheduledMessages(caseId),
    });
  },

  // Update Scheduled Message Hook
  useUpdateScheduledMessage() {
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: ({
        messageId,
        updates
      }: {
        messageId: number;
        updates: { message_body?: string; scheduled_time?: string }
      }) => this.updateScheduledMessage(messageId, updates),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['scheduled-messages'] });
        toast({
          title: "Success",
          description: "Scheduled message updated successfully",
          variant: "default",
        });
      }
    });
  },

  // Cancel Scheduled Message Hook
  useCancelScheduledMessage() {
    const { toast } = useToast();
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: (messageId: number) => this.cancelScheduledMessage(messageId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['scheduled-messages'] });
        toast({
          title: "Success",
          description: "Scheduled message cancelled successfully",
          variant: "default",
        });
      }
    });
  },
};

export default twilioService;
