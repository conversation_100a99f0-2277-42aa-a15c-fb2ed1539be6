import { useMutation, useQueryClient, UseMutationResult, UseQueryResult, useQuery } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { isAxiosError } from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
    CourtContact,
    CourtContactCreateRequest,
    CaseCourtDetailsRead,
    CaseCourtDetailsCreateRequest,
    CaseCourtDetailsUpdateRequest,
    JudgeContact,
    JudgeContactCreateRequest,
    ClerkContact,
    ClerkContactCreateRequest,
    CaseJudgeDetailsRead,
    CaseJudgeDetailsCreateRequest,
    CaseJudgeDetailsUpdateRequest,
    MediatorContact,
    MediatorContactCreateRequest,
    MediatorContactUpdateRequest,
    CaseMediatorDetailsRead,
    CaseMediatorDetailsCreateRequest,
    CaseMediatorDetailsUpdateRequest,
    CaseEventRead,
    CaseEventCreateRequest,
    CaseEventUpdateRequest,
    LitigationDatesResponse,
    LitigationDatesUpdateRequest
} from '@/type/letigation/litigationTypes';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL_V2 + '/case-management';
const STALE_TIME = 5 * 60 * 1000; // 5 minutes

/**
 * Custom hook to create a court contact
 * @returns {UseMutationResult} Mutation result for creating court contact
 */
export const useCreateCourtContactMutation = (): UseMutationResult<
    CourtContact,
    Error,
    CourtContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CourtContactCreateRequest) => {
            try {
                const response = await axios.post<CourtContact>(
                    `${BASE_URL}/court-contacts/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create court contact';

                toast({
                    title: "Error",
                    description: `Failed to create court contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['courtContacts'] });
            toast({
                title: "Success",
                description: "Court contact created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a court contact
 * @param {string} id - The ID of the court contact to update
 * @returns {UseMutationResult} Mutation result for updating court contact
 */
export const useUpdateCourtContactMutation = (id: string): UseMutationResult<
    CourtContact,
    Error,
    CourtContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CourtContactCreateRequest) => {
            try {
                const response = await axios.put<CourtContact>(
                    `${BASE_URL}/court-contacts/${id}/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update court contact';

                toast({
                    title: "Error",
                    description: `Failed to update court contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['courtContacts'] });
            toast({
                title: "Success",
                description: "Court contact updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all court contacts
 * @returns {UseQueryResult<CourtContact[]>} Query result for court contacts
 */
export const useCourtContactsQuery = (): UseQueryResult<CourtContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['courtContacts'],
        queryFn: async () => {
            try {
                const response = await axios.get<CourtContact[]>(
                    `${BASE_URL}/court-contacts/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch court contacts';

                toast({
                    title: "Error",
                    description: `Failed to fetch court contacts: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to fetch a single court contact
 * @param {string} id - The ID of the court contact
 * @returns {UseQueryResult<CourtContact>} Query result for court contact
 */
export const useCourtContactQuery = (id: string): UseQueryResult<CourtContact> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['courtContact', id],
        queryFn: async () => {
            try {
                const response = await axios.get<CourtContact>(
                    `${BASE_URL}/court-contacts/${id}/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch court contact';

                toast({
                    title: "Error",
                    description: `Failed to fetch court contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: !!id,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to fetch court details
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseCourtDetailsRead[]>} Query result for court details
 */
export const useCaseCourtDetailsQuery = (caseId: string): UseQueryResult<CaseCourtDetailsRead[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['caseCourtDetails', caseId],
        queryFn: async () => {
            try {
                const response = await axios.get<CaseCourtDetailsRead[]>(
                    `${BASE_URL}/cases/${caseId}/court-details/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch court details';

                toast({
                    title: "Error",
                    description: `Failed to fetch court details: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: !!caseId,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create court details for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for creating court details
 */
export const useCreateCaseCourtDetailsMutation = (caseId: string): UseMutationResult<
    CaseCourtDetailsRead,
    Error,
    CaseCourtDetailsCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseCourtDetailsCreateRequest) => {
            try {
                const response = await axios.post<CaseCourtDetailsRead>(
                    `${BASE_URL}/cases/${caseId}/court-details/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create court details';

                toast({
                    title: "Error",
                    description: `Failed to create court details: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseCourtDetails', caseId] });
            toast({
                title: "Success",
                description: "Court details created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update court details for a case
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for updating court details
 */
export const useUpdateCaseCourtDetailsMutation = (caseId: string, caseCourtContactId: string): UseMutationResult<
    CaseCourtDetailsRead,
    Error,
    CaseCourtDetailsUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseCourtDetailsUpdateRequest) => {
            try {
                const response = await axios.put<CaseCourtDetailsRead>(
                    `${BASE_URL}/cases/${caseId}/court-details/${caseCourtContactId}/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update court details';

                toast({
                    title: "Error",
                    description: `Failed to update court details: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseCourtDetails', caseId] });
            toast({
                title: "Success",
                description: "Court details updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to delete court details for a case
 * @param {string} caseId - The ID of the case
 * @param {string} courtDetailId - The ID of the court detail to delete
 * @returns {UseMutationResult} Mutation result for deleting court details
 */
export const useDeleteCaseCourtDetailsMutation = (
    caseId: string
): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (courtDetailId: string) => {
            try {
                await axios.delete(
                    `${BASE_URL}/cases/${caseId}/court-details/${courtDetailId}/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to delete court details';

                toast({
                    title: "Error",
                    description: `Failed to delete court details: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseCourtDetails', caseId] });
            toast({
                title: "Success",
                description: "Court details deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to create a judge contact
 * @returns {UseMutationResult} Mutation result for creating judge contact
 */
export const useCreateJudgeContactMutation = (): UseMutationResult<
    JudgeContact,
    Error,
    JudgeContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: JudgeContactCreateRequest) => {
            try {
                const response = await axios.post<JudgeContact>(
                    `${BASE_URL}/judge-contacts/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create judge contact';

                toast({
                    title: "Error",
                    description: `Failed to create judge contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['judgeContacts'] });
            toast({
                title: "Success",
                description: "Judge contact created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to create a clerk contact
 * @returns {UseMutationResult} Mutation result for creating clerk contact
 */
export const useCreateClerkContactMutation = (): UseMutationResult<
    ClerkContact,
    Error,
    ClerkContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ClerkContactCreateRequest) => {
            try {
                const response = await axios.post<ClerkContact>(
                    `${BASE_URL}/clerk-contacts/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create clerk contact';

                toast({
                    title: "Error",
                    description: `Failed to create clerk contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clerkContacts'] });
            toast({
                title: "Success",
                description: "Clerk contact created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all judge contacts
 * @param {object} options - Query options
 * @returns {UseQueryResult<JudgeContact[]>} Query result for judge contacts
 */
export const useJudgeContactsQuery = (
    options?: { enabled?: boolean }
): UseQueryResult<JudgeContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['judgeContacts'],
        queryFn: async () => {
            try {
                const response = await axios.get<JudgeContact[]>(
                    `${BASE_URL}/judge-contacts/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch judge contacts';

                toast({
                    title: "Error",
                    description: `Failed to fetch judge contacts: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: options?.enabled,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to fetch all clerk contacts
 * @param {object} options - Query options
 * @returns {UseQueryResult<ClerkContact[]>} Query result for clerk contacts
 */
export const useClerkContactsQuery = (
    options?: { enabled?: boolean }
): UseQueryResult<ClerkContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['clerkContacts'],
        queryFn: async () => {
            try {
                const response = await axios.get<ClerkContact[]>(
                    `${BASE_URL}/clerk-contacts/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch clerk contacts';

                toast({
                    title: "Error",
                    description: `Failed to fetch clerk contacts: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: options?.enabled,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to update a judge contact
 * @returns {UseMutationResult} Mutation result for updating judge contact
 */
export const useUpdateJudgeContactMutation = (judgeId: string): UseMutationResult<
    JudgeContact,
    Error,
    JudgeContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: JudgeContactCreateRequest) => {
            try {
                const response = await axios.put<JudgeContact>(
                    `${BASE_URL}/judge-contacts/${judgeId}/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update judge contact';

                toast({
                    title: "Error",
                    description: `Failed to update judge contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['judgeContacts'] });
            toast({
                title: "Success",
                description: "Judge contact updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a clerk contact
 * @returns {UseMutationResult} Mutation result for updating clerk contact
 */
export const useUpdateClerkContactMutation = (clerkId: string): UseMutationResult<
    ClerkContact,
    Error,
    ClerkContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: ClerkContactCreateRequest) => {
            try {
                const response = await axios.put<ClerkContact>(
                    `${BASE_URL}/clerk-contacts/${clerkId}/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update clerk contact';

                toast({
                    title: "Error",
                    description: `Failed to update clerk contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clerkContacts'] });
            toast({
                title: "Success",
                description: "Clerk contact updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch case judge details
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseJudgeDetailsRead>} Query result for judge details
 */
export const useCaseJudgeDetailsQuery = (caseId: string): UseQueryResult<CaseJudgeDetailsRead[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['caseJudgeDetails', caseId],
        queryFn: async () => {
            try {
                const response = await axios.get<CaseJudgeDetailsRead[]>(
                    `${BASE_URL}/cases/${caseId}/judge-details/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch judge details';

                toast({
                    title: "Error",
                    description: `Failed to fetch judge details: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: !!caseId,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create case judge details
 */
export const useCreateCaseJudgeDetailsMutation = (caseId: string): UseMutationResult<
    CaseJudgeDetailsRead,
    Error,
    CaseJudgeDetailsCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseJudgeDetailsCreateRequest) => {
            const response = await axios.post<CaseJudgeDetailsRead>(
                `${BASE_URL}/cases/${caseId}/judge-details/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseJudgeDetails', caseId] });
            toast({
                title: "Success",
                description: "Judge details created successfully",
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: `Failed to create judge details: ${error.message}`,
                variant: "destructive",
            });
        }
    });
};

/**
 * Custom hook to update case judge details
 */
export const useUpdateCaseJudgeDetailsMutation = (caseId: string, caseJudgeContactId: string): UseMutationResult<
    CaseJudgeDetailsRead,
    Error,
    CaseJudgeDetailsUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseJudgeDetailsUpdateRequest) => {
            const response = await axios.put<CaseJudgeDetailsRead>(
                `${BASE_URL}/cases/${caseId}/judge-details/${caseJudgeContactId}/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseJudgeDetails', caseId] });
            toast({
                title: "Success",
                description: "Judge details updated successfully",
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: `Failed to update judge details: ${error.message}`,
                variant: "destructive",
            });
        }
    });
};

/**
 * Custom hook to delete judge details for a case
 * @param {string} caseId - The ID of the case
 * @param {string} judgeDetailId - The ID of the judge detail to delete
 * @returns {UseMutationResult} Mutation result for deleting judge details
 */
export const useDeleteCaseJudgeDetailsMutation = (
    caseId: string,
): UseMutationResult<void, Error, string> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (judgeDetailId: string) => {
            try {
                await axios.delete(
                    `${BASE_URL}/cases/${caseId}/judge-details/${judgeDetailId}/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to delete judge details';

                toast({
                    title: "Error",
                    description: `Failed to delete judge details: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseJudgeDetails', caseId] });
            toast({
                title: "Success",
                description: "Judge details deleted successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to create a mediator contact
 * @returns {UseMutationResult} Mutation result for creating mediator contact
 */
export const useCreateMediatorContactMutation = (): UseMutationResult<
    MediatorContact,
    Error,
    MediatorContactCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: MediatorContactCreateRequest) => {
            try {
                const response = await axios.post<MediatorContact>(
                    `${BASE_URL}/mediator-contacts/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create mediator contact';

                toast({
                    title: "Error",
                    description: `Failed to create mediator contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['mediatorContacts'] });
            toast({
                title: "Success",
                description: "Mediator contact created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch all mediator contacts
 * @returns {UseQueryResult<MediatorContact[]>} Query result for mediator contacts
 */
export const useMediatorContactsQuery = (): UseQueryResult<MediatorContact[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['mediatorContacts'],
        queryFn: async () => {
            try {
                const response = await axios.get<MediatorContact[]>(
                    `${BASE_URL}/mediator-contacts/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch mediator contacts';

                toast({
                    title: "Error",
                    description: `Failed to fetch mediator contacts: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to update a mediator contact
 * @param {string} mediatorId - The ID of the mediator contact to update
 * @returns {UseMutationResult} Mutation result for updating mediator contact
 */
export const useUpdateMediatorContactMutation = (mediatorId: string): UseMutationResult<
    MediatorContact,
    Error,
    MediatorContactUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: MediatorContactUpdateRequest) => {
            try {
                const response = await axios.put<MediatorContact>(
                    `${BASE_URL}/mediator-contacts/${mediatorId}/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update mediator contact';

                toast({
                    title: "Error",
                    description: `Failed to update mediator contact: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['mediatorContacts'] });
            toast({
                title: "Success",
                description: "Mediator contact updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch case mediator details
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseMediatorDetailsRead | null>} Query result for mediator details
 */
export const useCaseMediatorDetailsQuery = (caseId: string): UseQueryResult<CaseMediatorDetailsRead | null> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);

    return useQuery({
        queryKey: ['caseMediatorDetails', caseId],
        queryFn: async () => {
            try {
                const response = await axios.get<CaseMediatorDetailsRead>(
                    `${BASE_URL}/cases/${caseId}/mediator-details/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error: unknown) {
                console.log(error);
                // If it's a 404 error, it means no mediator details exist yet, return null
                if (isAxiosError(error) && error.response?.status === 404) {
                    return null;
                }
                // For other errors, throw the error
                throw error;
            }
        },
        enabled: !!caseId,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create case mediator details
 */
export const useCreateCaseMediatorDetailsMutation = (caseId: string): UseMutationResult<
    CaseMediatorDetailsRead,
    Error,
    CaseMediatorDetailsCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseMediatorDetailsCreateRequest) => {
            const response = await axios.post<CaseMediatorDetailsRead>(
                `${BASE_URL}/cases/${caseId}/mediator-details/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseMediatorDetails', caseId] });
            toast({
                title: "Success",
                description: "Mediator details created successfully",
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: `Failed to create mediator details: ${error.message}`,
                variant: "destructive",
            });
        }
    });
};

/**
 * Custom hook to update case mediator details
 */
export const useUpdateCaseMediatorDetailsMutation = (caseId: string): UseMutationResult<
    CaseMediatorDetailsRead,
    Error,
    CaseMediatorDetailsUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseMediatorDetailsUpdateRequest) => {
            const response = await axios.put<CaseMediatorDetailsRead>(
                `${BASE_URL}/cases/${caseId}/mediator-details/`,
                data,
                {
                    headers: { Authorization: `Bearer ${accessToken}` }
                }
            );
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseMediatorDetails', caseId] });
            toast({
                title: "Success",
                description: "Mediator details updated successfully",
            });
        },
        onError: (error) => {
            toast({
                title: "Error",
                description: `Failed to update mediator details: ${error.message}`,
                variant: "destructive",
            });
        }
    });
};

/**
 * Custom hook to fetch case events
 * @param {string} caseId - The ID of the case
 * @returns {UseQueryResult<CaseEventRead[]>} Query result for case events
 */
export const useCaseEventsQuery = (caseId: string): UseQueryResult<CaseEventRead[]> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['caseEvents', caseId],
        queryFn: async () => {
            try {
                const response = await axios.get<CaseEventRead[]>(
                    `${BASE_URL}/cases/${caseId}/events/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch case events';

                toast({
                    title: "Error",
                    description: `Failed to fetch case events: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: !!caseId,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create a case event
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for creating an event
 */
export const useCreateCaseEventMutation = (caseId: string): UseMutationResult<
    CaseEventRead,
    Error,
    CaseEventCreateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseEventCreateRequest) => {
            try {
                const response = await axios.post<CaseEventRead>(
                    `${BASE_URL}/cases/${caseId}/events/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create event';

                toast({
                    title: "Error",
                    description: `Failed to create event: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseEvents', caseId] });
            toast({
                title: "Success",
                description: "Event created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update a case event
 * @param {string} caseId - The ID of the case
 * @returns {UseMutationResult} Mutation result for updating an event
 */
export const useUpdateCaseEventMutation = (caseId: string): UseMutationResult<
    CaseEventRead,
    Error,
    CaseEventUpdateRequest
> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: CaseEventUpdateRequest) => {
            try {
                const response = await axios.put<CaseEventRead>(
                    `${BASE_URL}/cases/${caseId}/events/${data.id}/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update event';

                toast({
                    title: "Error",
                    description: `Failed to update event: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['caseEvents', caseId] });
            toast({
                title: "Success",
                description: "Event updated successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to fetch litigation dates for a defendant
 * @param {string} caseId - The ID of the case
 * @param {number} defendantId - The ID of the defendant
 * @returns {UseQueryResult<LitigationDatesResponse>} Query result containing litigation dates
 */
export const useLitigationDatesQuery = (
    caseId: string,
    defendantId: number,
    options?: { enabled?: boolean }
): UseQueryResult<LitigationDatesResponse> => {
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useQuery({
        queryKey: ['litigationDates', caseId, defendantId],
        queryFn: async () => {
            try {
                const response = await axios.get<LitigationDatesResponse>(
                    `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/litigation-dates/`,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to fetch litigation dates';

                toast({
                    title: "Error",
                    description: `Failed to fetch litigation dates: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        enabled: options?.enabled !== false && !!caseId && !!defendantId,
        staleTime: STALE_TIME,
    });
};

/**
 * Custom hook to create litigation dates for a defendant
 * @param {string} caseId - The ID of the case
 * @param {number} defendantId - The ID of the defendant
 * @returns {UseMutationResult} Mutation result for creating litigation dates
 */
export const useCreateLitigationDatesMutation = (
    caseId: string,
    defendantId: number
): UseMutationResult<LitigationDatesResponse, Error, LitigationDatesUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LitigationDatesUpdateRequest) => {
            try {
                const response = await axios.post<LitigationDatesResponse>(
                    `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/litigation-dates/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to create litigation dates';

                toast({
                    title: "Error",
                    description: `Failed to create litigation dates: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['litigationDates', caseId, defendantId]
            });
            toast({
                title: "Success",
                description: "Litigation dates created successfully",
                variant: "default"
            });
        }
    });
};

/**
 * Custom hook to update litigation dates for a defendant
 * @param {string} caseId - The ID of the case
 * @param {number} defendantId - The ID of the defendant
 * @returns {UseMutationResult} Mutation result for updating litigation dates
 */
export const useUpdateLitigationDatesMutation = (
    caseId: string,
    defendantId: number
): UseMutationResult<LitigationDatesResponse, Error, LitigationDatesUpdateRequest> => {
    const queryClient = useQueryClient();
    const accessToken = useSelector((state: RootState) => state.auth.accessToken);
    const { toast } = useToast();

    return useMutation({
        mutationFn: async (data: LitigationDatesUpdateRequest) => {
            try {
                const response = await axios.patch<LitigationDatesResponse>(
                    `${BASE_URL}/cases/${caseId}/defendants/${defendantId}/litigation-dates/`,
                    data,
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    }
                );
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error
                    ? error.message
                    : 'Failed to update litigation dates';

                toast({
                    title: "Error",
                    description: `Failed to update litigation dates: ${errorMessage}`,
                    variant: "destructive",
                });
                throw new Error(errorMessage);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['litigationDates', caseId, defendantId]
            });
            toast({
                title: "Success",
                description: "Litigation dates updated successfully",
                variant: "default"
            });
        }
    });
};
    