import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useToast } from '@/hooks/use-toast';
import {
  NylasIntegration,
  NylasAuthResponse,
  NylasEvent,
  EmailListResponse,
  EmailMessage,
  SendEmailData,
  Label,
  EmailListParams,
  CreateLabelRequest,
  ApplyLabelResponse,
  AgendaResponse,
  EmailSignature,
} from '@/type/emailCalender';
import { store } from '@/store'; // Import the Redux store directly
import { format } from 'date-fns';

const BASE_URL = process.env.NEXT_PUBLIC_DJANGO_URL + '/users/nylas';

// Helper to get token from Redux store
const getToken = () => {
  const state = store.getState();
  return state.auth.accessToken;
};

// Add state management for selected calendar
let primaryCalendarId: string | null = null;

// Integration Status Hook
export const useCheckIntegration = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['nylas-integration'],
    queryFn: async () => {
      if (!accessToken) return null;

      try {
        const { data } = await axiosInstance.get<NylasIntegration>(
          `${BASE_URL}/current/`, {
          params: { token: accessToken },
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
        );
        return data;
      } catch (error) {
        console.error('Error fetching integration:', error);
        return null;
      }
    }
  });
};

// Add this after the useCheckIntegration hook
export async function removeNylasIntegration(): Promise<{ success: boolean; message: string }> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  const { data } = await axiosInstance.delete(
    `${BASE_URL}/remove/`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
  return data;
}

// Add this hook after useCheckIntegration
export const useRemoveIntegration = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: removeNylasIntegration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nylas-integration'] });
      toast({
        title: "Success",
        description: "Calendar integration removed successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove calendar integration",
        variant: "destructive"
      });
    }
  });
};

// Direct functions for the calendar page
export async function checkIntegrationStatus(): Promise<NylasIntegration | null> {
  const token = getToken();
  if (!token) return null;

  try {
    const { data } = await axiosInstance.get<NylasIntegration>(
      `${BASE_URL}/current/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
    );

    // Store the primary calendar ID
    if (data.calendars) {
      const primaryCalendar = data.calendars.find(cal => cal.is_primary);
      primaryCalendarId = primaryCalendar?.id || data.settings.default_calendar_id || null;
    }

    return data;
  } catch (error) {
    console.error('Error fetching integration:', error);
    return null;
  }
}

export async function connectNylasCalendar(): Promise<void> {
  const token = getToken();
  if (!token) {
    throw new Error('Please login to connect your calendar');
  }

  try {
    const returnUrl = encodeURIComponent(`${window.location.origin}/calendar`);
    window.location.href = `${BASE_URL}/auth?return_url=${returnUrl}&token=${token}`;
  } catch (error) {
    console.error('Failed to start integration:', error);
    throw new Error('Failed to start calendar integration');
  }
}

export async function exchangeAuthCode(code: string): Promise<NylasAuthResponse> {
  const token = getToken();
  if (!token) throw new Error('Please login to connect your calendar');

  const { data } = await axiosInstance.post<NylasAuthResponse>(
    `${BASE_URL}/oauth/exchange?token=${token}`,
    { code },
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
  return data;
}

// Helper function to extract links from description
const extractLinks = (description: string): string[] => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return description?.match(urlRegex) || [];
};

// Helper function to format date for API
const formatDateParam = (date: Date): string => {
  return format(date, 'yyyy-MM-dd');
};

// Helper to get month start and end dates
const getMonthDateRange = (date: Date) => {
  const start = new Date(date.getFullYear(), date.getMonth(), 1);
  const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  return { start, end };
};

export async function fetchNylasEvents(viewDate?: Date, view: string = 'month'): Promise<NylasEvent[]> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  const params: Record<string, string> = {};

  if (viewDate) {
    const currentDate = new Date(viewDate);

    switch (view) {
      case 'month': {
        // First day of the month
        const start = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        // Last day of the month
        const end = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
      case 'week': {
        // Start of week
        const start = new Date(currentDate);
        start.setDate(currentDate.getDate() - currentDate.getDay());
        // End of week
        const end = new Date(start);
        end.setDate(start.getDate() + 6);
        end.setHours(23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
      case 'day': {
        // Start and end of the same day
        const start = new Date(currentDate);
        start.setDate(start.getDate() - 1);
        start.setHours(0, 0, 0);
        const end = new Date(currentDate);
        end.setDate(end.getDate() + 1);
        end.setHours(23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
    }
  }



  const { data } = await axiosInstance.get<NylasEvent[]>(
    `${BASE_URL}/calendar/events/`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params
    }
  );

  return data.map(event => {
    let start, end;

    if (event.is_all_day && event.start_date) {
      // Handle all-day events
      start = new Date(event.start_date);
      // For all-day events, end date is exclusive in calendar libraries
      // If end_date is provided, use it, otherwise use start_date + 1 day
      if (event.end_date) {
        end = new Date(event.end_date);
      } else {
        end = new Date(start);
        end.setDate(end.getDate() + 1);
      }
    } else if (event.start_time && event.end_time) {
      // Handle regular events with timestamps
      start = new Date(event.start_time * 1000);
      end = new Date(event.end_time * 1000);
    } else {
      // Fallback for any unexpected event format
      console.warn('Event with unexpected format:', event);
      start = new Date();
      end = new Date();
      end.setHours(end.getHours() + 1);
    }

    return {
      ...event,
      links: extractLinks(event.description || ''),
      start,
      end
    };
  });
}

export async function fetchUserNylasEvents(userId: string, viewDate?: Date, view: string = 'month'): Promise<NylasEvent[]> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  const params: Record<string, string> = {};

  if (viewDate) {
    const currentDate = new Date(viewDate);

    switch (view) {
      case 'month': {
        // First day of the month
        const start = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        // Last day of the month
        const end = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
      case 'week': {
        // Start of week
        const start = new Date(currentDate);
        start.setDate(currentDate.getDate() - currentDate.getDay());
        // End of week
        const end = new Date(start);
        end.setDate(start.getDate() + 6);
        end.setHours(23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
      case 'day': {
        // Start and end of the same day
        const start = new Date(currentDate);
        start.setDate(start.getDate() - 1);
        start.setHours(0, 0, 0);
        const end = new Date(currentDate);
        end.setDate(end.getDate() + 1);
        end.setHours(23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
      case 'agenda': {
        // For agenda view, we'll fetch events for the next 14 days
        const start = new Date(currentDate);
        start.setHours(0, 0, 0);
        const end = new Date(currentDate);
        end.setDate(end.getDate() + 14); //
        end.setHours(23, 59, 59);
        params.start_date = formatDateParam(start);
        params.end_date = formatDateParam(end);
        break;
      }
    }
  }



  const { data } = await axiosInstance.get<NylasEvent[]>(
    `${BASE_URL}/organization/calendars/${userId}/events/`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params
    }
  );

  return data.map(event => {
    let start, end;

    // Check if this is an all-day event (either explicitly marked or has start_date/end_date)
    if ((event.is_all_day || event.when?.object === 'datespan') && (event.start_date || event.when?.start_date)) {
      // Handle all-day events
      const startDateStr = event.start_date || event.when?.start_date || '';
      start = new Date(startDateStr);

      // For all-day events, end date is exclusive in calendar libraries
      // If end_date is provided, use it, otherwise use start_date + 1 day
      const endDateStr = event.end_date || event.when?.end_date;
      if (endDateStr) {
        end = new Date(endDateStr);
      } else {
        end = new Date(start);
        end.setDate(end.getDate() + 1);
      }

      // Make sure is_all_day flag is set
      event.is_all_day = true;
    } else if (event.start_time && event.end_time) {
      // Handle regular events with timestamps
      start = new Date(event.start_time * 1000);
      end = new Date(event.end_time * 1000);
    } else {
      // Fallback for any unexpected event format
      console.warn('Event with unexpected format:', event);
      start = new Date();
      end = new Date();
      end.setHours(end.getHours() + 1);
    }

    return {
      ...event,
      links: extractLinks(event.description || ''),
      start,
      end
    };
  });
}

export async function createNylasEvent(event: Partial<NylasEvent>): Promise<NylasEvent> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');
  if (!primaryCalendarId) throw new Error('No calendar selected');

  const eventData = {
    ...event,
    calendar_id: primaryCalendarId // Add calendar_id to event data
  };

  const { data } = await axiosInstance.post<NylasEvent>(
    `${BASE_URL}/calendar/events/create/`,
    eventData,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
  return data;
}

// Add this new function after createNylasEvent function
export const createBulkEvents = async (formData: FormData) => {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  try {
    const response = await axiosInstance.post(
      `${BASE_URL}/calendar/events/bulk-create/`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(error.message || 'Failed to create events');
    }
    throw error;
  }
}

// Additional helper function to validate CSV format
export const validateCsvFormat = async (file: File) => {
  if (!file.name.endsWith('.csv')) {
    throw new Error('Please upload a CSV file');
  }

  const content = await file.text();
  const lines = content.split('\n');
  if (lines.length < 2) {
    throw new Error('CSV file is empty');
  }

  const headers = lines[0].toLowerCase().trim().split(',');
  const requiredColumns = ['title', 'description', 'event_type', 'time', 'location'];

  const missingColumns = requiredColumns.filter(col => !headers.includes(col));
  if (missingColumns.length > 0) {
    throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
  }

  return true;
}

// Calendar Events Hook
export const useCalendarEvents = (viewDate?: Date, view: string = 'month') => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['nylas-events', view, viewDate?.toISOString()],
    queryFn: async () => {
      if (!accessToken) return [];

      const params: Record<string, string> = {};

      if (viewDate) {
        switch (view) {
          case 'month': {
            const { start, end } = getMonthDateRange(viewDate);
            params.start_date = formatDateParam(start);
            params.end_date = formatDateParam(end);
            break;
          }
          case 'day': {
            const dayStart = new Date(viewDate);
            dayStart.setDate(dayStart.getDate() - 1);
            params.start_date = formatDateParam(dayStart);
            const dayEnd = new Date(viewDate);
            dayEnd.setDate(dayEnd.getDate() + 1);
            params.end_date = formatDateParam(dayEnd);
            break;
          }
          case 'week': {
            const start = viewDate;
            const end = new Date(viewDate);
            end.setDate(end.getDate() + 6);
            params.start_date = formatDateParam(start);
            params.end_date = formatDateParam(end);
            break;
          }
        }
      }

      const { data } = await axiosInstance.get<NylasEvent[]>(
        `${BASE_URL}/calendar/events/`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          },
          params
        }
      );

      return data.map(event => {
        let start, end;

        // Check if this is an all-day event (either explicitly marked or has start_date/end_date)
        if ((event.is_all_day || event.when?.object === 'datespan') && (event.start_date || event.when?.start_date)) {
          // Handle all-day events
          const startDateStr = event.start_date || event.when?.start_date || '';
          start = new Date(startDateStr);

          // For all-day events, we need to adjust the end date
          // In Nylas API, end_date is exclusive (the day after the event ends)
          // But we want to display it only on the start date
          const endDateStr = event.end_date || event.when?.end_date;

          if (endDateStr) {
            // Create a date from the end date string
            const rawEndDate = new Date(endDateStr);

            // Check if the end date is exactly one day after the start date
            // (which is the typical pattern for single-day events in Nylas)
            const startDateObj = new Date(start);
            const oneDayAfterStart = new Date(startDateObj);
            oneDayAfterStart.setDate(oneDayAfterStart.getDate() + 1);

            if (rawEndDate.toISOString().split('T')[0] === oneDayAfterStart.toISOString().split('T')[0]) {
              // This is a single-day event, so set end to be the same as start
              // but with a small time offset to make it display correctly
              end = new Date(start);
              end.setHours(23, 59, 59, 999);
            } else {
              // This is a multi-day event, so subtract one day from the end date
              // to account for the exclusive end date in Nylas API
              end = new Date(rawEndDate);
              end.setDate(end.getDate() - 1);
              end.setHours(23, 59, 59, 999);
            }
          } else {
            // No end date provided, default to same day as start
            end = new Date(start);
            end.setHours(23, 59, 59, 999);
          }

          // Make sure is_all_day flag is set
          event.is_all_day = true;
        } else if (event.start_time && event.end_time) {
          // Handle regular events with timestamps
          start = new Date(event.start_time * 1000);
          end = new Date(event.end_time * 1000);
        } else {
          // Fallback for any unexpected event format
          console.warn('Event with unexpected format:', event);
          start = new Date();
          end = new Date();
          end.setHours(end.getHours() + 1);
        }

        return {
          ...event,
          links: extractLinks(event.description || ''),
          start,
          end
        };
      });
    }
  });
};

// Create Event Hook
export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventData: Partial<NylasEvent>) => {
      if (!primaryCalendarId) {
        throw new Error('No calendar selected');
      }

      const { data } = await axiosInstance.post(
        `${BASE_URL}/calendar/events/create/`,
        {
          ...eventData,
          calendar_id: primaryCalendarId
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      return data;
    },
    onSuccess: async () => {
      // Invalidate and refetch
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['nylas-events'] }),
        queryClient.invalidateQueries({ queryKey: ['nylas-agenda'] })
      ]);
      // Force refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['nylas-events'] }),
        queryClient.refetchQueries({ queryKey: ['nylas-agenda'] })
      ]);
      toast({
        title: "Success",
        description: "Event created successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create event",
        variant: "destructive"
      });
    }
  });
};

export async function updateNylasEvent(event: Partial<NylasEvent> & { event_id: string }): Promise<NylasEvent> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  const { data } = await axiosInstance.put<NylasEvent>(
    `${BASE_URL}/calendar/events/update/`,
    event,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
  return data;
}

// Update Event Hook
export const useUpdateEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (eventData: Partial<NylasEvent> & { event_id: string }) => updateNylasEvent(eventData),
    onSuccess: async () => {
      // Invalidate and refetch
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['nylas-events'] }),
        queryClient.invalidateQueries({ queryKey: ['nylas-agenda'] })
      ]);
      // Force refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['nylas-events'] }),
        queryClient.refetchQueries({ queryKey: ['nylas-agenda'] })
      ]);
      toast({
        title: "Success",
        description: "Event updated successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update event",
        variant: "destructive"
      });
    }
  });
};

export async function deleteNylasEvent(params: {
  event_id: string;
  calendar_id?: string;
  case_id?: string;
}): Promise<{ success: boolean }> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  const { data } = await axiosInstance.delete(
    `${BASE_URL}/calendar/events/delete/`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: params
    }
  );
  return data;
}

// Delete Event Hook
export const useDeleteEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (params: { event_id: string; calendar_id?: string; case_id?: string }) =>
      deleteNylasEvent(params),
    onSuccess: async () => {
      // Invalidate and refetch
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['nylas-events'] }),
        queryClient.invalidateQueries({ queryKey: ['nylas-agenda'] })
      ]);
      // Force refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['nylas-events'] }),
        queryClient.refetchQueries({ queryKey: ['nylas-agenda'] })
      ]);
      toast({
        title: "Success",
        description: "Event deleted successfully",
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete event",
        variant: "destructive"
      });
    }
  });
};

const emailService = {
  async listEmails(params: EmailListParams = {}): Promise<EmailListResponse> {
    const token = getToken();
    const queryParams = new URLSearchParams();

    // Add all params to query string
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.folder) queryParams.append('folder', params.folder);
    if (params.label_id) queryParams.append('label_id', params.label_id);
    if (params.search) queryParams.append('search', params.search);
    if (params.unread !== undefined) queryParams.append('unread', params.unread.toString());
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);

    const { data } = await axiosInstance.get(
      `${BASE_URL}/emails/?${queryParams.toString()}`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  useEmails(params: EmailListParams = {}) {
    return useQuery({
      queryKey: ['emails', params],
      queryFn: () => this.listEmails(params),
      staleTime: 1000 * 60, // 1 minute
    });
  },

  useInbox(params: Omit<EmailListParams, 'folder'> = {}) {
    return this.useEmails({ ...params, folder: 'inbox' });
  },

  useSentEmails(params: Omit<EmailListParams, 'folder'> = {}) {
    return this.useEmails({ ...params, folder: 'sent' });
  },

  useStarredEmails(params: Omit<EmailListParams, 'folder'> = {}) {
    return this.useEmails({ ...params, folder: 'starred' });
  },

  // Get recent emails
  async getRecentEmails(): Promise<EmailMessage[]> {
    const token = getToken();
    const { data } = await axiosInstance.get(`${BASE_URL}/emails/recent/`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return data;
  },

  // Get single email
  async getEmail(messageId: string): Promise<EmailMessage> {
    const token = getToken();
    const { data } = await axiosInstance.get(`${BASE_URL}/emails/${messageId}/`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return data;
  },

  // Send email
  async sendEmail(emailData: SendEmailData): Promise<{ message_id: string }> {
    const token = getToken();

    // Handle attachments if any
    if (emailData.attachments?.length) {
      const formData = new FormData();

      // Add email data
      formData.append('subject', emailData.subject);
      formData.append('body', emailData.body);
      formData.append('to', JSON.stringify(emailData.to));
      if (emailData.cc) formData.append('cc', JSON.stringify(emailData.cc));
      if (emailData.bcc) formData.append('bcc', JSON.stringify(emailData.bcc));

      // Add attachments
      emailData.attachments.forEach(file => {
        formData.append('attachments', file);
      });

      const { data } = await axiosInstance.post(`${BASE_URL}/emails/send/`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      return data;
    }

    // No attachments, send as JSON
    const { data } = await axiosInstance.post(`${BASE_URL}/emails/send/`, emailData, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return data;
  },

  // Mark email as read/unread
  async markAsRead(messageId: string, unread: boolean = false): Promise<{ success: boolean }> {
    const token = getToken();
    const { data } = await axiosInstance.post(
      `${BASE_URL}/emails/${messageId}/read/`,
      { unread },
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return data;
  },

  // Label management
  // Label Management
  async listLabels(): Promise<Label[]> {
    const token = getToken();
    const { data } = await axiosInstance.get(`${BASE_URL}/emails/labels/`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return data;
  },

  async createLabel(labelData: CreateLabelRequest): Promise<Label> {
    const token = getToken();
    const { data } = await axiosInstance.post(
      `${BASE_URL}/emails/labels/create/`,
      labelData,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  async deleteLabel(labelId: string): Promise<{ success: boolean }> {
    const token = getToken();
    const { data } = await axiosInstance.delete(
      `${BASE_URL}/emails/labels/${labelId}/`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },


  async getLabelMessages(labelId: string, params: EmailListParams = {}): Promise<EmailListResponse> {
    const token = getToken();
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.unread !== undefined) queryParams.append('unread', params.unread.toString());
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);

    const { data } = await axiosInstance.get(
      `${BASE_URL}/emails/labels/${labelId}/messages/?${queryParams.toString()}`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  // React Query hook for label messages
  useLabelMessages(labelId: string, params: EmailListParams = {}) {
    return useQuery({
      queryKey: ['label-messages', labelId, params],
      queryFn: () => this.getLabelMessages(labelId, params),
      enabled: !!labelId,
    });
  },

  async applyLabel(messageId: string, labelId: string): Promise<ApplyLabelResponse> {
    const token = getToken();
    const { data } = await axiosInstance.post(
      `${BASE_URL}/emails/${messageId}/label/`,
      { label_id: labelId },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  useRecentEmails() {
    return useQuery({
      queryKey: ['emails', 'recent'],
      queryFn: () => this.getRecentEmails(),
    });
  },

  useEmail(messageId: string) {
    return useQuery({
      queryKey: ['email', messageId],
      queryFn: () => this.getEmail(messageId),
      enabled: !!messageId, // Only fetch when we have an ID
    });
  },

  useLabels() {
    return useQuery({
      queryKey: ['email-labels'],
      queryFn: () => this.listLabels(),
      staleTime: 1000 * 60 * 5, // Cache for 5 minutes
    });
  },

  useCreateLabel() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: (labelData: CreateLabelRequest) => this.createLabel(labelData),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['email-labels'] });
        toast({
          title: "Success",
          description: "Label created successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to create label",
          variant: "destructive",
        });
      },
    });
  },

  useDeleteLabel() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: (labelId: string) => this.deleteLabel(labelId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['email-labels'] });
        toast({
          title: "Success",
          description: "Label deleted successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to delete label",
          variant: "destructive",
        });
      },
    });
  },

  useApplyLabel() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: ({ messageId, labelId }: { messageId: string; labelId: string }) =>
        this.applyLabel(messageId, labelId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['emails'] });
        toast({
          title: "Success",
          description: "Label applied successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to apply label",
          variant: "destructive",
        });
      },
    });
  },

  useSendEmail() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: (emailData: SendEmailData) => this.sendEmail(emailData),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['emails'] });
        toast({
          title: "Success",
          description: "Email sent successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to send email",
          variant: "destructive",
        });
      },
    });
  },

  useMarkAsRead() {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: ({ messageId, unread }: { messageId: string; unread: boolean }) =>
        this.markAsRead(messageId, unread),
      onSuccess: (_, variables) => {
        // Update both list and detail views
        queryClient.invalidateQueries({ queryKey: ['emails'] });
        queryClient.invalidateQueries({ queryKey: ['email', variables.messageId] });
        queryClient.invalidateQueries({ queryKey: ['label-messages'] });
      }
    });
  },

  async attachEmailToCase(messageId: string, caseId: string, userEmail?: string): Promise<{ success: boolean }> {
    const token = getToken();
    const { data } = await axiosInstance.post(
      `${BASE_URL}/emails/${messageId}/attach-to-case/`,
      { case_id: caseId, user_email: userEmail },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  useAttachEmailToCase() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: ({ messageId, caseId, userEmail }: { messageId: string; caseId: string; userEmail?: string }) =>
        this.attachEmailToCase(messageId, caseId, userEmail),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['emails'] });
        queryClient.invalidateQueries({ queryKey: ['case-notes'] });
        toast({
          title: "Success",
          description: "Email attached to case successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to attach email to case",
          variant: "destructive",
        });
      },
    });
  },

  // Signature related services
  async listSignatures(): Promise<EmailSignature[]> {
    const token = getToken();
    const { data } = await axiosInstance.get(
      `${BASE_URL}/email-signatures/`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  async createSignature(signatureData: Omit<EmailSignature, 'id' | 'created_at' | 'updated_at'>): Promise<EmailSignature> {
    const token = getToken();
    const { data } = await axiosInstance.post(
      `${BASE_URL}/email-signatures/`,
      signatureData,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  async updateSignature(id: string, signatureData: Partial<EmailSignature>): Promise<EmailSignature> {
    const token = getToken();
    const { data } = await axiosInstance.put(
      `${BASE_URL}/email-signatures/${id}/`,
      signatureData,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  async deleteSignature(id: string): Promise<{ success: boolean }> {
    const token = getToken();
    const { data } = await axiosInstance.delete(
      `${BASE_URL}/email-signatures/${id}/`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return data;
  },

  useSignatures() {
    return useQuery({
      queryKey: ['email-signatures'],
      queryFn: () => this.listSignatures(),
    });
  },

  useCreateSignature() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: (signatureData: Omit<EmailSignature, 'id' | 'created_at' | 'updated_at'>) =>
        this.createSignature(signatureData),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['email-signatures'] });
        toast({
          title: "Success",
          description: "Signature created successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to create signature",
          variant: "destructive",
        });
      },
    });
  },

  useUpdateSignature() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: Partial<EmailSignature> }) =>
        this.updateSignature(id, data),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['email-signatures'] });
        toast({
          title: "Success",
          description: "Signature updated successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to update signature",
          variant: "destructive",
        });
      },
    });
  },

  useDeleteSignature() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation({
      mutationFn: (id: string) => this.deleteSignature(id),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['email-signatures'] });
        toast({
          title: "Success",
          description: "Signature deleted successfully",
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to delete signature",
          variant: "destructive",
        });
      },
    });
  },
};

async function getAgendaView(date?: string, range: string = '2weeks'): Promise<AgendaResponse> {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  const params = new URLSearchParams();
  if (date) {
    params.append('date', date);
  }
  params.append('range', range);

  try {
    const { data } = await axiosInstance.get<AgendaResponse>(
      `${BASE_URL}/calendar/agenda/${params.toString() ? `?${params.toString()}` : ''}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    // Check if integration is not connected
    if (data.integration_status === 'not_connected') {
      console.log('Calendar integration not connected:', data.message);
      return data; // Return the response as is, with empty events
    }

    // Process events to add links
    return {
      ...data,
      days: data.days.map(day => ({
        ...day,
        events: day.events.map(event => {
          let start, end;

          if (event.is_all_day && event.start_date) {
            // Handle all-day events
            start = new Date(event.start_date);
            // For all-day events, end date is exclusive in calendar libraries
            if (event.end_date) {
              end = new Date(event.end_date);
            } else {
              end = new Date(start);
              end.setDate(end.getDate() + 1);
            }
          } else if (event.start_time && event.end_time) {
            // Handle regular events with timestamps
            start = new Date(event.start_time * 1000);
            end = new Date(event.end_time * 1000);
          } else {
            // Fallback for any unexpected event format
            console.warn('Event with unexpected format in agenda view:', event);
            start = new Date();
            end = new Date();
            end.setHours(end.getHours() + 1);
          }

          return {
            ...event,
            links: extractLinks(event.description || ''),
            start,
            end
          };
        })
      }))
    };
  } catch (error) {
    console.error('Error fetching agenda view:', error);
    // Return a default empty response instead of throwing an error
    return {
      current_date: new Date().toISOString().split('T')[0],
      start_date: new Date().toISOString().split('T')[0],
      end_date: new Date().toISOString().split('T')[0],
      days: [],
      integration_status: 'error',
      message: 'Failed to fetch calendar events'
    };
  }
}

// React Query hook for agenda view
export const useAgendaView = (date?: string, range?: string) => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useQuery({
    queryKey: ['nylas-agenda', date, range],
    queryFn: () => getAgendaView(date, range),
    enabled: !!accessToken,
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
  });
};

export const previewBulkEvents = async (formData: FormData) => {
  const token = getToken();
  if (!token) throw new Error('No auth token found');

  try {
    const response = await axiosInstance.post(
      `${BASE_URL}/calendar/events/preview-bulk/`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(error.message || 'Failed to preview events');
    }
    throw error;
  }
};

export default emailService;