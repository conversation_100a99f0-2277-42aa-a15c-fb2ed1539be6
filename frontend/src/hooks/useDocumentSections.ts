// src/hooks/useDocumentSections.ts
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setDocumentData, setLoading, setError } from '@/store/slices/reasoningAISlice';
import { useDocDataSections } from '@/services/reasoningAIService';

export function useDocumentSections(caseId: string) {
    const dispatch = useDispatch();
    const { 
        data: docSections, 
        isLoading, 
        error,
        refetch 
    } = useDocDataSections(caseId);

    useEffect(() => {
        if (!caseId) return;
        dispatch(setLoading(isLoading));

        if (docSections) {
            dispatch(setDocumentData(docSections));
        }

        if (error) {
            dispatch(setError((error as Error).message));
            dispatch(setDocumentData(null));
        }
    }, [docSections, isLoading, error, dispatch]);

    return {
        docSections,
        isLoading,
        error,
        refetch
    };
}