import { useState } from 'react';
import { 
  useMatchingCasesMutation,
  MatchingClientCase,
  MatchingDefendantCase,
  ClientSearchParams
} from '@/services/case-management/matchingCasesService';

interface UseMatchingCasesResult {
  matchingClients: MatchingClientCase[];
  matchingDefendants: MatchingDefendantCase[];
  isLoading: boolean;
  isModalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  searchForMatchingCases: (params: ClientSearchParams) => Promise<void>;
  clearMatches: () => void;
}

export function useMatchingCases(): UseMatchingCasesResult {
  const [matchingClients, setMatchingClients] = useState<MatchingClientCase[]>([]);
  const [matchingDefendants, setMatchingDefendants] = useState<MatchingDefendantCase[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const { mutateAsync: searchCases, isPending: isSearching } = useMatchingCasesMutation();
  
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);
  
  const clearMatches = () => {
    setMatchingClients([]);
    setMatchingDefendants([]);
  };
  
  const searchForMatchingCases = async (params: ClientSearchParams) => {
    // Only search if we have enough data
    if (!params.first_name || !params.last_name) {
      return;
    }
    
    try {
      const results = await searchCases(params);
      setMatchingClients(results.clients);
      setMatchingDefendants(results.defendants);
      
      // Open modal if we found matches
      if (results.clients.length > 0 || results.defendants.length > 0) {
        openModal();
      }
    } catch (error) {
      console.error('Error searching for matching cases:', error);
    }
  };
  
  return {
    matchingClients,
    matchingDefendants,
    isLoading: isSearching,
    isModalOpen,
    openModal,
    closeModal,
    searchForMatchingCases,
    clearMatches
  };
} 