import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useNotificationsListQuery, NotificationFilters } from '@/services/notificationService';
import { setNotifications, setLoading, setError } from '@/store/slices/notificationSlice';

export const useNotifications = (filters: NotificationFilters = {}) => {
  const dispatch = useDispatch();
  
  // Get the notification state from Redux
  const { 
    notifications, 
    isLoading, 
    error 
  } = useSelector((state: RootState) => state.notification);

  // Use the existing notification query
  const notificationsQuery = useNotificationsListQuery(filters);

  // Update the Redux store when notifications change
  useEffect(() => {
    if (notificationsQuery.data?.pages && notificationsQuery.data.pages.length > 0) {
      // Extract all notifications from all pages
      const allNotifications = notificationsQuery.data.pages.flatMap(page => page.results);
      dispatch(setNotifications(allNotifications));
    }
    
    dispatch(setLoading(notificationsQuery.isLoading));
    
    if (notificationsQuery.error) {
      dispatch(setError(notificationsQuery.error.message));
    } else {
      dispatch(setError(null));
    }
  }, [notificationsQuery.data, notificationsQuery.isLoading, notificationsQuery.error, dispatch]);

  return {
    notifications,
    isLoading,
    error,
    refetch: notificationsQuery.refetch,
    fetchNextPage: notificationsQuery.fetchNextPage,
    hasNextPage: notificationsQuery.hasNextPage,
    isFetchingNextPage: notificationsQuery.isFetchingNextPage
  };
}; 