import { useListFiles } from "@/services/documentService";
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import { setAllDocuments } from '@/store/slices/caseView';
import { DocumentFile } from "@/type/doocument";

export const useCaseDocuments = (caseId: string) => {
    const dispatch = useDispatch();
    const {
        data: filesResponse,
        isLoading: isLoadingFiles,
        refetch: refetchFiles
    } = useListFiles(caseId);

    const updateDocumentState = async (refetch?: boolean) => {
        if (refetch) {
            await Promise.all([
                refetchFiles()
            ]);
        }
        if (filesResponse?.documents) {
            dispatch(setAllDocuments([...filesResponse.documents].sort((a: DocumentFile, b: DocumentFile) => 
                a.file_name.localeCompare(b.file_name)
            )));
        }
    };

    useEffect(() => {
        updateDocumentState();
    }, [filesResponse, caseId, dispatch]);

    return {
        filesResponse,
        isLoading: isLoadingFiles,
        updateDocumentState
    };
}; 