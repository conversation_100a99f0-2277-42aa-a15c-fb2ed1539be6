import { useMutation } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { ChatRequest } from '@/type/dashboard';
import { useDispatch, useSelector } from 'react-redux';
import { addMessage, updateCurrentMessage, setIsStreaming } from '@/store/slices/caseChatSlice';
import { RootState } from '@/store';

interface UseStreamChatOptions {
  onChunk?: (chunk: string) => void;
  onError?: (error: Error) => void;
}

interface StreamResponse {
  type: 'context_files' | 'message';
  data: string | string[];
}

export const useStreamChat = (options: UseStreamChatOptions = {}) => {
  const dispatch = useDispatch();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  return useMutation({
    mutationFn: async ({ chatRequest, caseId }: { chatRequest: ChatRequest; caseId: string }) => {
      dispatch(setIsStreaming(true));
      let contextFiles: string[] = [];

      // Add user message to store
      dispatch(addMessage({
        caseId,
        message: {
          role: 'user',
          content: chatRequest.messages[chatRequest.messages.length - 1].content
        }
      }));

      const url = `${process.env.NEXT_PUBLIC_API_URL}/api/chat_with_case/v1/chat?case_id=${encodeURIComponent(caseId)}`;
      
      const response = await axios({
        method: 'POST',
        url,
        data: chatRequest,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        responseType: 'stream',
        onDownloadProgress: (progressEvent) => {
          const data = progressEvent.event.target.response;
          if (!data) return;
          
          try {
            const response = JSON.parse(data) as StreamResponse;
            if (response.type === 'context_files') {
              contextFiles = response.data as string[];
            } else if (response.type === 'message') {
              dispatch(updateCurrentMessage(response.data as string));
              options.onChunk?.(response.data as string);
            }
          } catch {
            dispatch(updateCurrentMessage(data));
            options.onChunk?.(data);
          }
        },
      });

      const finalMessage = response.data;

      dispatch(addMessage({
        caseId,
        message: {
          role: 'assistant',
          content: finalMessage,
          contextFiles: contextFiles.length > 0 ? contextFiles : []
        }
      }));
      
      dispatch(setIsStreaming(false));
      dispatch(updateCurrentMessage(''));

      return finalMessage;
    },
    onError: (error) => {
      dispatch(setIsStreaming(false));
      dispatch(updateCurrentMessage(''));
      options.onError?.(error as Error);
    },
  });
}; 