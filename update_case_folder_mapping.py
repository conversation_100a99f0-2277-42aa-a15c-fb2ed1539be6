#!/usr/bin/env python
import os
import sys
import django
import json
from django.conf import settings

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "alphalaw.settings")
django.setup()

from storage_service.services.factory import StorageServiceFactory
from case_management.models import Case
from storage_service.models import CaseStorageMetadata, CaseFolderMapping
from organization_management.models import Organization, OrganizationFolder
from django.contrib.auth import get_user_model
from django.db import transaction
from crum import impersonate
from utils.common.case_enums import ROOT_FOLDER_KEY

User = get_user_model()

def get_onedrive_personal_service(org_id):
    """Get the OneDrive Personal service for the given organization"""
    try:
        # Get organization
        org = Organization.objects.get(id=org_id)
        
        # Get a user from the organization to impersonate
        user = User.objects.filter(organizations=org).first()
        if not user:
            print(f"No users found for organization {org_id}")
            return None, None
            
        # Impersonate the user to get the correct storage service
        with impersonate(user):
            # Get storage service
            storage_service = StorageServiceFactory.get_storage_service(org.storage_type)
            if not storage_service or not hasattr(storage_service, 'get_folder_contents'):
                print(f"Storage service for organization {org_id} does not support folder operations")
                return None, None
                
            return storage_service, org
    except Organization.DoesNotExist:
        print(f"Organization {org_id} not found")
        return None, None
    except Exception as e:
        print(f"Error getting OneDrive Personal service: {str(e)}")
        return None, None

def update_case_folder_mapping(case_id, new_folder_id, org_id):
    """Update the case folder mapping to use a new root folder"""
    try:
        # Get the case
        case = Case.objects.get(pk=case_id)
        print(f"Case: {case.case_name}")
        
        # Get the storage service
        storage_service, org = get_onedrive_personal_service(org_id)
        if not storage_service:
            return False
            
        # Get folder info to verify it exists
        try:
            folder_info = storage_service._make_request("GET", f"/me/drive/items/{new_folder_id}")
            print(f"Found folder: {folder_info.get('name')} (ID: {folder_info.get('id')})")
        except Exception as e:
            print(f"Error getting folder info: {str(e)}")
            return False
            
        # Update the case's root folder ID
        with transaction.atomic():
            old_folder_id = case.storage_root_folder_id
            case.storage_root_folder_id = new_folder_id
            case.save(update_fields=["storage_root_folder_id"])
            
            # Get or create storage metadata
            storage_metadata, created = CaseStorageMetadata.objects.get_or_create(case=case)
            
            # Get folder contents to find subfolders
            subfolders = storage_service.get_folder_contents(new_folder_id)
            subfolders = [f for f in subfolders if f.get("type") == "folder"]
            
            # Get organization folders
            org_folders = OrganizationFolder.objects.filter(organization_id=org_id, is_enabled=True)
            
            # Delete existing folder mappings
            CaseFolderMapping.objects.filter(case_metadata=storage_metadata).delete()
            
            # Create new folder mappings
            folder_mappings = []
            folder_map = {ROOT_FOLDER_KEY: folder_info}
            
            # Match subfolders to organization folders by name
            for org_folder in org_folders:
                matching_folders = [f for f in subfolders if f.get("name") == org_folder.name]
                if matching_folders:
                    subfolder = matching_folders[0]
                    # Create folder mapping
                    folder_mapping = CaseFolderMapping(
                        case_metadata=storage_metadata,
                        organization_folder=org_folder,
                        folder_id=subfolder["id"],
                        sections=org_folder.sections_connected,
                    )
                    folder_mappings.append(folder_mapping)
                    
                    # Add to folder map
                    if org_folder.sections_connected:
                        for section in org_folder.sections_connected:
                            folder_map[section] = subfolder
                    else:
                        folder_map[f"custom_{org_folder.name}"] = subfolder
            
            # Bulk create all folder mappings
            if folder_mappings:
                CaseFolderMapping.objects.bulk_create(folder_mappings)
                print(f"Created {len(folder_mappings)} folder mappings")
            
            print(f"Updated case {case_id} root folder from {old_folder_id} to {new_folder_id}")
            return True
    except Exception as e:
        print(f"Error updating case folder mapping: {str(e)}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Update case folder mapping")
    parser.add_argument("--org", type=int, required=True, help="Organization ID")
    parser.add_argument("--case", type=str, required=True, help="Case ID")
    parser.add_argument("--folder", type=str, required=True, help="New folder ID")
    
    args = parser.parse_args()
    
    if update_case_folder_mapping(args.case, args.folder, args.org):
        print("Case folder mapping updated successfully")
    else:
        print("Failed to update case folder mapping")
