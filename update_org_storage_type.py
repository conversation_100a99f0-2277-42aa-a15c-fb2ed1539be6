#!/usr/bin/env python
"""
Script to update an organization's storage type from OneDrive Personal to OneDrive Business.

Usage:
    python update_org_storage_type.py <organization_id>

Example:
    python update_org_storage_type.py 3
"""

import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from django.db import transaction
from users.models import Organization
from storage_service.services.factory import StorageType
from utils.utils import info_logger, error_logger

def update_organization_storage_type(org_id, from_type=StorageType.ONEDRIVE_PERSONAL, to_type=StorageType.ONEDRIVE):
    """
    Update an organization's storage type.
    
    Args:
        org_id (int): The ID of the organization to update
        from_type (str): The current storage type (default: onedrive_personal)
        to_type (str): The new storage type (default: onedrive)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the organization
        org = Organization.objects.get(id=org_id)
        
        # Check if the organization has the expected current storage type
        if org.storage_type != from_type:
            error_logger.error(
                f"Organization {org_id} has storage type '{org.storage_type}', not '{from_type}'. Aborting."
            )
            return False
        
        # Store the old values for logging
        old_storage_type = org.storage_type
        
        # Update the organization's storage type
        with transaction.atomic():
            # Update the storage type
            org.storage_type = to_type
            
            # If converting from OneDrive Personal to OneDrive Business, transfer tokens
            if from_type == StorageType.ONEDRIVE_PERSONAL and to_type == StorageType.ONEDRIVE:
                # Transfer tokens if they exist
                if org.onedrive_personal_access_token:
                    org.onedrive_access_token = org.onedrive_personal_access_token
                    org.onedrive_personal_access_token = None
                
                if org.onedrive_personal_refresh_token:
                    org.onedrive_refresh_token = org.onedrive_personal_refresh_token
                    org.onedrive_personal_refresh_token = None
                
                if org.onedrive_personal_token_expires_at:
                    org.onedrive_token_expires_at = org.onedrive_personal_token_expires_at
                    org.onedrive_personal_token_expires_at = None
            
            # Save the changes
            org.save()
        
        info_logger.info(
            f"Successfully updated organization {org_id} storage type from '{old_storage_type}' to '{org.storage_type}'"
        )
        
        return True
    
    except Organization.DoesNotExist:
        error_logger.error(f"Organization with ID {org_id} does not exist")
        return False
    
    except Exception as e:
        error_logger.error(f"Error updating organization {org_id} storage type: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python update_org_storage_type.py <organization_id>")
        sys.exit(1)
    
    try:
        org_id = int(sys.argv[1])
    except ValueError:
        print(f"Error: Organization ID must be an integer, got '{sys.argv[1]}'")
        sys.exit(1)
    
    success = update_organization_storage_type(org_id)
    
    if success:
        print(f"Successfully updated organization {org_id} storage type from OneDrive Personal to OneDrive Business")
        sys.exit(0)
    else:
        print(f"Failed to update organization {org_id} storage type. Check the logs for details.")
        sys.exit(1)
