#!/usr/bin/env python
"""
Script to transfer marketing data from organization 5 to organization 12.
Run this script in Django shell after analyzing the data with marketing_data_transfer.py:

python manage.py shell < marketing_data_transfer_execute.py

This script will perform the actual transfer of data between organizations.
"""

import sys

from django.db import transaction
from marketing_management.models import (
    Engagement,
    EngagementType,
    MarketingGoal,
    PartnerContact,
    UserMarketingGoal,
    UserMarketingGoalHistory,
)
from organization_management.models import SourceTag
from users.models import Organization, User

# Configuration
SOURCE_ORG_ID = 5
TARGET_ORG_ID = 12
DRY_RUN = True  # Set to False to perform the actual transfer

# Mapping dictionaries to track old IDs to new IDs
id_mappings = {
    "source_tags": {},
    "engagement_types": {},
    "marketing_goals": {},
    "partners": {},
    "engagements": {},
    "user_goals": {},
}


def print_header(text):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(f" {text} ".center(80, "="))
    print("=" * 80)


def get_organizations():
    """Get source and target organizations"""
    try:
        source_org = Organization.objects.get(id=SOURCE_ORG_ID)
        target_org = Organization.objects.get(id=TARGET_ORG_ID)
    except Organization.DoesNotExist:
        print(f"Error: One or both organizations (IDs: {SOURCE_ORG_ID}, {TARGET_ORG_ID}) do not exist.")
        sys.exit(1)

    print_header("ORGANIZATION INFORMATION")
    print(f"Source Organization: {source_org.name} (ID: {source_org.id})")
    print(f"Target Organization: {target_org.name} (ID: {target_org.id})")

    return source_org, target_org


def create_user_mapping(source_org, target_org):
    """Create mapping between source and target users"""
    source_users = source_org.users.all()
    target_users = target_org.users.all()

    user_mapping = {}
    for source_user in source_users:
        matching_target_users = [u for u in target_users if u.email.lower() == source_user.email.lower()]
        if matching_target_users:
            user_mapping[source_user.id] = matching_target_users[0]

    print(f"User mapping found for {len(user_mapping)}/{source_users.count()} users")
    for source_id, target_user in user_mapping.items():
        source_user = User.objects.get(id=source_id)
        print(f"  - {source_user.email} (ID: {source_id}) → {target_user.email} (ID: {target_user.id})")

    return user_mapping


def transfer_source_tags(source_org, target_org):
    """Transfer source tags from source to target organization"""
    print_header("TRANSFERRING SOURCE TAGS")

    source_tags = SourceTag.objects.filter(organization=source_org)
    print(f"Found {source_tags.count()} source tags to transfer")

    # Check for existing tags in target org to avoid duplicates
    existing_tags = {tag.name.lower(): tag for tag in SourceTag.objects.filter(organization=target_org)}

    transferred = 0
    for tag in source_tags:
        if tag.name.lower() in existing_tags:
            print(f"  - Skipping '{tag.name}' (already exists in target org)")
            id_mappings["source_tags"][tag.id] = existing_tags[tag.name.lower()].id
            continue

        if not DRY_RUN:
            new_tag = SourceTag.objects.create(organization=target_org, name=tag.name, description=tag.description)
            id_mappings["source_tags"][tag.id] = new_tag.id
        else:
            print(f"  - Would create: '{tag.name}'")

        transferred += 1

    print(f"Transferred {transferred} source tags")
    return transferred


def transfer_engagement_types(source_org, target_org, user_mapping):
    """Transfer engagement types from source to target organization"""
    print_header("TRANSFERRING ENGAGEMENT TYPES")

    engagement_types = EngagementType.objects.filter(organization=source_org)
    print(f"Found {engagement_types.count()} engagement types to transfer")

    # Check for existing types in target org to avoid duplicates
    existing_types = {et.name.lower(): et for et in EngagementType.objects.filter(organization=target_org)}

    transferred = 0
    for et in engagement_types:
        if et.name.lower() in existing_types:
            print(f"  - Skipping '{et.name}' (already exists in target org)")
            id_mappings["engagement_types"][et.id] = existing_types[et.name.lower()].id
            continue

        # Map created_by if possible
        created_by = None
        if et.created_by_id and et.created_by_id in user_mapping:
            created_by = user_mapping[et.created_by_id]

        if not DRY_RUN:
            new_et = EngagementType.objects.create(
                organization=target_org,
                name=et.name,
                description=et.description,
                category=et.category,
                default_duration=et.default_duration,
                is_active=et.is_active,
                requires_follow_up=et.requires_follow_up,
                typical_cost=et.typical_cost,
                success_metrics=et.success_metrics,
                created_by=created_by,
            )
            id_mappings["engagement_types"][et.id] = new_et.id
        else:
            print(f"  - Would create: '{et.name}'")

        transferred += 1

    print(f"Transferred {transferred} engagement types")
    return transferred


def transfer_marketing_goals(source_org, target_org, user_mapping):
    """Transfer marketing goals from source to target organization"""
    print_header("TRANSFERRING MARKETING GOALS")

    goals = MarketingGoal.objects.filter(organization=source_org)
    print(f"Found {goals.count()} marketing goals to transfer")

    # Check for existing goals in target org to avoid duplicates
    existing_goals = {goal.name.lower(): goal for goal in MarketingGoal.objects.filter(organization=target_org)}

    transferred = 0
    for goal in goals:
        if goal.name.lower() in existing_goals:
            print(f"  - Skipping '{goal.name}' (already exists in target org)")
            id_mappings["marketing_goals"][goal.id] = existing_goals[goal.name.lower()].id
            continue

        # Map created_by if possible
        created_by = None
        if goal.created_by_id and goal.created_by_id in user_mapping:
            created_by = user_mapping[goal.created_by_id]

        if not DRY_RUN:
            new_goal = MarketingGoal.objects.create(
                organization=target_org,
                name=goal.name,
                category=goal.category,
                recurrence=goal.recurrence,
                metric_type=goal.metric_type,
                default_target=goal.default_target,
                description=goal.description,
                is_active=goal.is_active,
                created_by=created_by,
            )
            id_mappings["marketing_goals"][goal.id] = new_goal.id
        else:
            print(f"  - Would create: '{goal.name}'")

        transferred += 1

    print(f"Transferred {transferred} marketing goals")
    return transferred


def transfer_partner_contacts(source_org, target_org, user_mapping):
    """Transfer partner contacts from source to target organization"""
    print_header("TRANSFERRING PARTNER CONTACTS")

    partners = PartnerContact.objects.filter(organization=source_org)
    print(f"Found {partners.count()} partner contacts to transfer")

    transferred = 0
    for partner in partners:
        # Map source_tag if it was transferred
        source_tag_id = None
        if partner.source_tag_id and partner.source_tag_id in id_mappings["source_tags"]:
            source_tag_id = id_mappings["source_tags"][partner.source_tag_id]

        # Map assigned_to if possible
        assigned_to = None
        if partner.assigned_to_id and partner.assigned_to_id in user_mapping:
            assigned_to = user_mapping[partner.assigned_to_id]

        # Map created_by if possible
        created_by = None
        if partner.created_by_id and partner.created_by_id in user_mapping:
            created_by = user_mapping[partner.created_by_id]

        if not DRY_RUN:
            new_partner = PartnerContact.objects.create(
                organization=target_org,
                source_tag_id=source_tag_id,
                first_name=partner.first_name,
                last_name=partner.last_name,
                email=partner.email,
                phone=partner.phone,
                phone_ext=partner.phone_ext,
                role=partner.role,
                business_name=partner.business_name,
                partner_type=partner.partner_type,
                relationship_status=partner.relationship_status,
                website=partner.website,
                instagram=partner.instagram,
                specialties=partner.specialties,
                street1=partner.street1,
                street2=partner.street2,
                city=partner.city,
                state=partner.state,
                zip_code=partner.zip_code,
                latitude=partner.latitude,
                longitude=partner.longitude,
                accepts_liens=partner.accepts_liens,
                no_records_service=partner.no_records_service,
                do_not_use=partner.do_not_use,
                notes=partner.notes,
                is_active=partner.is_active,
                profile_photo=partner.profile_photo,
                assigned_to=assigned_to,
                total_referrals=partner.total_referrals,
                successful_referrals=partner.successful_referrals,
                last_engagement_date=partner.last_engagement_date,
                next_engagement_date=partner.next_engagement_date,
                average_case_value=partner.average_case_value,
                total_revenue_generated=partner.total_revenue_generated,
                medical_provider=None,  # Don't transfer medical provider link
                created_by=created_by,
            )
            id_mappings["partners"][partner.id] = new_partner.id
        else:
            print(f"  - Would create: '{partner.first_name} {partner.last_name} - {partner.business_name}'")

        transferred += 1

    print(f"Transferred {transferred} partner contacts")
    return transferred


def transfer_engagements(source_org, target_org, user_mapping):
    """Transfer engagements from source to target organization"""
    print_header("TRANSFERRING ENGAGEMENTS")

    # Get engagements from source org partners
    engagements = Engagement.objects.filter(partner__organization=source_org)
    print(f"Found {engagements.count()} engagements to transfer")

    transferred = 0
    for engagement in engagements:
        # Skip if partner wasn't transferred
        if engagement.partner_id not in id_mappings["partners"]:
            print(f"  - Skipping engagement ID {engagement.id} (partner not transferred)")
            continue

        # Skip if engagement type wasn't transferred
        if engagement.engagement_type_id not in id_mappings["engagement_types"]:
            print(f"  - Skipping engagement ID {engagement.id} (engagement type not transferred)")
            continue

        # Map created_by if possible
        created_by = None
        if engagement.created_by_id and engagement.created_by_id in user_mapping:
            created_by = user_mapping[engagement.created_by_id]

        # Map attendees if possible
        attendees_ids = []
        for attendee in engagement.attendees.all():
            if attendee.id in user_mapping:
                attendees_ids.append(user_mapping[attendee.id].id)

        if not DRY_RUN:
            new_engagement = Engagement.objects.create(
                partner_id=id_mappings["partners"][engagement.partner_id],
                engagement_type_id=id_mappings["engagement_types"][engagement.engagement_type_id],
                status=engagement.status,
                date=engagement.date,
                duration=engagement.duration,
                follow_up_date=engagement.follow_up_date,
                reminder_sent=engagement.reminder_sent,
                reminder_sent_date=engagement.reminder_sent_date,
                location_type=engagement.location_type,
                location=engagement.location,
                virtual_meeting_link=engagement.virtual_meeting_link,
                virtual_meeting_platform=engagement.virtual_meeting_platform,
                agenda=engagement.agenda,
                notes=engagement.notes,
                follow_up_notes=engagement.follow_up_notes,
                outcomes=engagement.outcomes,
                next_steps=engagement.next_steps,
                created_by=created_by,
                budgeted_cost=engagement.budgeted_cost,
                actual_cost=engagement.actual_cost,
                cost_notes=engagement.cost_notes,
                success_rating=engagement.success_rating,
                metrics_notes=engagement.metrics_notes,
                expected_referrals=engagement.expected_referrals,
                actual_referrals=engagement.actual_referrals,
                expected_revenue=engagement.expected_revenue,
                actual_revenue=engagement.actual_revenue,
            )

            # Add attendees
            if attendees_ids:
                new_engagement.attendees.add(*attendees_ids)

            id_mappings["engagements"][engagement.id] = new_engagement.id
        else:
            print(f"  - Would create: Engagement with {engagement.partner.business_name} on {engagement.date}")

        transferred += 1

    print(f"Transferred {transferred} engagements")
    return transferred


def transfer_user_marketing_goals(source_org, target_org, user_mapping):
    """Transfer user marketing goals from source to target organization"""
    print_header("TRANSFERRING USER MARKETING GOALS")

    # Get user marketing goals for users in the source org
    user_goals = UserMarketingGoal.objects.filter(user__organizations=source_org)
    print(f"Found {user_goals.count()} user marketing goals to transfer")

    transferred = 0
    for goal in user_goals:
        # Skip if user wasn't mapped
        if goal.user_id not in user_mapping:
            print(f"  - Skipping user goal ID {goal.id} (user not mapped)")
            continue

        # Skip if marketing goal wasn't transferred (for USER_MARKETING_GOAL type)
        if goal.type_of_goal == "USER_MARKETING_GOAL" and goal.marketing_goal_id:
            if goal.marketing_goal_id not in id_mappings["marketing_goals"]:
                print(f"  - Skipping user goal ID {goal.id} (marketing goal not transferred)")
                continue

        # Skip if engagement goal wasn't transferred (for ENGAGEMENT_GOAL type)
        if goal.type_of_goal == "ENGAGEMENT_GOAL" and goal.engagement_goal_id:
            if goal.engagement_goal_id not in id_mappings["engagement_types"]:
                print(f"  - Skipping user goal ID {goal.id} (engagement goal not transferred)")
                continue

        # Map created_by if possible
        created_by = None
        if goal.created_by_id and goal.created_by_id in user_mapping:
            created_by = user_mapping[goal.created_by_id]

        # Map marketing_goal or engagement_goal if applicable
        marketing_goal_id = None
        if goal.marketing_goal_id and goal.marketing_goal_id in id_mappings["marketing_goals"]:
            marketing_goal_id = id_mappings["marketing_goals"][goal.marketing_goal_id]

        engagement_goal_id = None
        if goal.engagement_goal_id and goal.engagement_goal_id in id_mappings["engagement_types"]:
            engagement_goal_id = id_mappings["engagement_types"][goal.engagement_goal_id]

        if not DRY_RUN:
            new_goal = UserMarketingGoal.objects.create(
                user=user_mapping[goal.user_id],
                marketing_goal_id=marketing_goal_id,
                engagement_goal_id=engagement_goal_id,
                type_of_goal=goal.type_of_goal,
                target_value=goal.target_value,
                current_period_start=goal.current_period_start,
                current_period_end=goal.current_period_end,
                is_recurring=goal.is_recurring,
                recurrence_end_date=goal.recurrence_end_date,
                status=goal.status,
                current_progress=goal.current_progress,
                notes=goal.notes,
                created_by=created_by,
            )
            id_mappings["user_goals"][goal.id] = new_goal.id

            # Transfer goal history if needed
            for history in goal.history.all():
                UserMarketingGoalHistory.objects.create(
                    user_goal=new_goal,
                    period_start=history.period_start,
                    period_end=history.period_end,
                    target_value=history.target_value,
                    achieved_value=history.achieved_value,
                    completion_percentage=history.completion_percentage,
                    created_by=created_by,
                )
        else:
            goal_name = (
                goal.marketing_goal.name
                if goal.marketing_goal
                else (goal.engagement_goal.name if goal.engagement_goal else "Unknown")
            )
            print(f"  - Would create: {goal.user.get_full_name()}'s goal for {goal_name}")

        transferred += 1

    print(f"Transferred {transferred} user marketing goals")
    return transferred


def main():
    """Main function to transfer data"""
    print_header("MARKETING DATA TRANSFER")
    print(f"Source Organization ID: {SOURCE_ORG_ID}")
    print(f"Target Organization ID: {TARGET_ORG_ID}")
    print(f"Mode: {'Dry Run' if DRY_RUN else 'Actual Transfer'}")

    # Get organizations
    source_org, target_org = get_organizations()

    # Create user mapping
    user_mapping = create_user_mapping(source_org, target_org)

    if DRY_RUN:
        print("\nDRY RUN MODE: No actual changes will be made to the database.")

    try:
        with transaction.atomic():
            # Transfer data in the correct order to maintain relationships
            transfer_source_tags(source_org, target_org)
            transfer_engagement_types(source_org, target_org, user_mapping)
            transfer_marketing_goals(source_org, target_org, user_mapping)
            transfer_partner_contacts(source_org, target_org, user_mapping)
            transfer_engagements(source_org, target_org, user_mapping)
            transfer_user_marketing_goals(source_org, target_org, user_mapping)

            # If in dry run mode, roll back the transaction
            if DRY_RUN:
                print("\nDRY RUN COMPLETE: Rolling back transaction.")
                raise Exception("Dry run complete - rolling back")

            print("\nTRANSFER COMPLETE: All data has been transferred successfully.")
    except Exception as e:
        if DRY_RUN and str(e) == "Dry run complete - rolling back":
            print("Dry run completed successfully. Set DRY_RUN = False to perform the actual transfer.")
        else:
            print(f"Error during transfer: {str(e)}")


if __name__ == "__main__":
    main()
