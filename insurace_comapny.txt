python manage.py shell -c "
import csv
from case_management.v2.models import InsuranceCompany
from datetime import datetime

# Get all insurance companies for organization ID 1
insurance_companies = InsuranceCompany.objects.filter(organization_id=1)

# Create CSV filename with timestamp
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
filename = f'insurance_companies_org1_{timestamp}.csv'

# Write to CSV
with open(filename, 'w', newline='') as csvfile:
    writer = csv.writer(csvfile)
    
    # Write header
    writer.writerow([
        'ID', 'Name', 'Payee', 'Phone', 'Phone Extension', 'Cell', 'Fax',
        'Email', 'Website', 'Street 1', 'Street 2', 'City', 'State',
        'ZIP Code', 'Tax ID', 'Type', 'Note', 'Created At', 'Updated At'
    ])
    
    # Write data
    for company in insurance_companies:
        writer.writerow([
            company.id,
            company.name,
            company.payee,
            company.phone,
            company.phone_ext,
            company.cell,
            company.fax,
            company.email,
            company.website,
            company.street1,
            company.street2,
            company.city,
            company.state,
            company.zip_code,
            company.tax_id,
            company.type,
            company.note,
            company.created_at,
            company.updated_at
        ])

print(f'CSV file created: {filename}')
"