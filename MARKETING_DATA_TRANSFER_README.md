# Marketing Data Transfer Scripts

These scripts help analyze and transfer marketing data from one organization to another in AlphaLaw.

## Overview

The transfer process is divided into two main steps:
1. **Analysis**: Examine the data in the source organization and create a plan for transfer
2. **Execution**: Perform the actual data transfer while maintaining relationships

## Files

- `marketing_data_transfer.py` - Analysis script to understand what data will be transferred
- `marketing_data_transfer_execute.py` - Execution script to perform the actual transfer

## Data Being Transferred

The scripts transfer the following marketing data:
- Source Tags
- Engagement Types
- Marketing Goals
- Partner Contacts
- Engagements
- User Marketing Goals (including goal history)

## Prerequisites

- Both source and target organizations must exist in the database
- Users in the target organization should have the same email addresses as users in the source organization for proper mapping
- Django shell access to run the scripts

## Usage Instructions

### Step 1: Analysis

First, run the analysis script to understand what data will be transferred:

```bash
cd /path/to/django_backend_alphalaw/backend
python manage.py shell < /path/to/marketing_data_transfer.py
```

This will:
- Show information about both organizations
- Count and display samples of marketing data in the source organization
- Create a mapping between users in both organizations
- Generate a plan for transferring the data

### Step 2: Execution (Dry Run)

Next, run the execution script in dry run mode to simulate the transfer without making actual changes:

```bash
cd /path/to/django_backend_alphalaw/backend
python manage.py shell < /path/to/marketing_data_transfer_execute.py
```

By default, the script runs in `DRY_RUN` mode, which means:
- It will simulate all operations
- No actual changes will be made to the database
- You can see exactly what would be transferred

### Step 3: Execution (Actual Transfer)

Once you're satisfied with the dry run results, edit the `marketing_data_transfer_execute.py` file:

1. Change `DRY_RUN = True` to `DRY_RUN = False`
2. Run the script again:

```bash
cd /path/to/django_backend_alphalaw/backend
python manage.py shell < /path/to/marketing_data_transfer_execute.py
```

This will perform the actual transfer of data between organizations.

## Configuration

Both scripts have configuration variables at the top:

```python
# Configuration
SOURCE_ORG_ID = 5  # ID of the source organization
TARGET_ORG_ID = 12  # ID of the target organization
```

Modify these values if you need to transfer data between different organizations.

## Important Notes

1. **User Mapping**: The scripts map users between organizations based on email addresses. If a user exists in the source organization but not in the target organization, their data will not be transferred.

2. **Relationships**: The scripts maintain relationships between entities (e.g., engagements are linked to the correct partners and engagement types).

3. **Duplicates**: The scripts check for existing entities in the target organization to avoid duplicates. If an entity with the same name already exists, it will be reused rather than creating a duplicate.

4. **Medical Providers**: The link between Partner Contacts and Medical Providers is not transferred, as Medical Providers are organization-specific.

5. **Transaction Safety**: All operations are performed within a database transaction, ensuring that either all data is transferred successfully or no changes are made.

## Troubleshooting

If you encounter any issues:

1. **Check User Mapping**: Ensure that users in the target organization have the same email addresses as users in the source organization.

2. **Database Errors**: If you see database errors, check that all required fields are being properly set during transfer.

3. **Missing Data**: If some data is not transferred, check the console output for skipped items and their reasons.

4. **Rollback Issues**: If the transaction is rolled back, check the error message for details on what went wrong.

## Support

For additional help or to report issues, please contact the development team.
