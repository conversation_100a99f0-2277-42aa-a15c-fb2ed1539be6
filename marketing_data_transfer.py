#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to analyze and transfer marketing data from organization 5 to organization 12.
Run this script in Django shell:

python manage.py shell < marketing_data_transfer.py

First, it will analyze the data and print information about what will be transferred.
Then, it will ask for confirmation before performing the actual transfer.
"""

import sys
from django.db import transaction
from django.utils import timezone
from users.models import Organization, User
from marketing_management.models import (
    PartnerContact,
    EngagementType,
    Engagement,
    MarketingGoal,
    UserMarketingGoal,
    UserMarketingGoalHistory
)
from organization_management.models import SourceTag

# Configuration
SOURCE_ORG_ID = 5
TARGET_ORG_ID = 12
ANALYZE_ONLY = True  # Set to False to perform the actual transfer

def print_header(text):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(f" {text} ".center(80, "="))
    print("=" * 80)

def analyze_organizations():
    """Analyze source and target organizations"""
    try:
        source_org = Organization.objects.get(id=SOURCE_ORG_ID)
        target_org = Organization.objects.get(id=TARGET_ORG_ID)
    except Organization.DoesNotExist:
        print(f"Error: One or both organizations (IDs: {SOURCE_ORG_ID}, {TARGET_ORG_ID}) do not exist.")
        sys.exit(1)
    
    print_header("ORGANIZATION INFORMATION")
    print(f"Source Organization: {source_org.name} (ID: {source_org.id})")
    print(f"Target Organization: {target_org.name} (ID: {target_org.id})")
    
    # Analyze users
    source_users = source_org.users.all()
    target_users = target_org.users.all()
    
    print(f"\nSource Organization Users ({source_users.count()}):")
    for user in source_users:
        print(f"  - {user.get_full_name()} ({user.email}) [ID: {user.id}] - Role: {user.role}")
    
    print(f"\nTarget Organization Users ({target_users.count()}):")
    for user in target_users:
        print(f"  - {user.get_full_name()} ({user.email}) [ID: {user.id}] - Role: {user.role}")
    
    return source_org, target_org, source_users, target_users

def analyze_marketing_data(source_org):
    """Analyze marketing data in the source organization"""
    print_header("MARKETING DATA ANALYSIS")
    
    # Partner Contacts
    partners = PartnerContact.objects.filter(organization=source_org)
    print(f"Partner Contacts: {partners.count()}")
    if partners.count() > 0:
        print("\nSample Partner Contacts:")
        for partner in partners[:5]:
            print(f"  - {partner.first_name} {partner.last_name} - {partner.business_name} [ID: {partner.id}]")
            print(f"    Type: {partner.partner_type}, Status: {partner.relationship_status}")
    
    # Engagement Types
    engagement_types = EngagementType.objects.filter(organization=source_org)
    print(f"\nEngagement Types: {engagement_types.count()}")
    if engagement_types.count() > 0:
        print("\nAll Engagement Types:")
        for et in engagement_types:
            print(f"  - {et.name} [ID: {et.id}]")
            print(f"    Category: {et.category}, Duration: {et.default_duration} min")
    
    # Engagements
    engagements = Engagement.objects.filter(partner__organization=source_org)
    print(f"\nEngagements: {engagements.count()}")
    if engagements.count() > 0:
        print("\nSample Engagements:")
        for engagement in engagements[:5]:
            print(f"  - {engagement.engagement_type.name} with {engagement.partner.business_name} [ID: {engagement.id}]")
            print(f"    Status: {engagement.status}, Date: {engagement.date}")
    
    # Marketing Goals
    goals = MarketingGoal.objects.filter(organization=source_org)
    print(f"\nMarketing Goals: {goals.count()}")
    if goals.count() > 0:
        print("\nAll Marketing Goals:")
        for goal in goals:
            print(f"  - {goal.name} [ID: {goal.id}]")
            print(f"    Category: {goal.category}, Metric Type: {goal.metric_type}")
    
    # User Marketing Goals
    user_goals = UserMarketingGoal.objects.filter(user__organizations=source_org)
    print(f"\nUser Marketing Goals: {user_goals.count()}")
    if user_goals.count() > 0:
        print("\nSample User Marketing Goals:")
        for user_goal in user_goals[:5]:
            goal_name = user_goal.marketing_goal.name if user_goal.marketing_goal else "N/A"
            print(f"  - {user_goal.user.get_full_name()}: {goal_name} [ID: {user_goal.id}]")
            print(f"    Target: {user_goal.target_value}, Progress: {user_goal.current_progress}")
    
    # Source Tags
    source_tags = SourceTag.objects.filter(organization=source_org)
    print(f"\nSource Tags: {source_tags.count()}")
    if source_tags.count() > 0:
        print("\nSample Source Tags:")
        for tag in source_tags[:5]:
            print(f"  - {tag.name} [ID: {tag.id}]")
    
    return {
        'partners': partners,
        'engagement_types': engagement_types,
        'engagements': engagements,
        'goals': goals,
        'user_goals': user_goals,
        'source_tags': source_tags
    }

def create_transfer_plan(source_data, source_users, target_users):
    """Create a plan for transferring data"""
    print_header("TRANSFER PLAN")
    
    # Map source users to target users (by email)
    user_mapping = {}
    for source_user in source_users:
        matching_target_users = [u for u in target_users if u.email.lower() == source_user.email.lower()]
        if matching_target_users:
            user_mapping[source_user.id] = matching_target_users[0]
    
    print(f"User mapping found for {len(user_mapping)}/{source_users.count()} users")
    for source_id, target_user in user_mapping.items():
        source_user = User.objects.get(id=source_id)
        print(f"  - {source_user.email} (ID: {source_id}) → {target_user.email} (ID: {target_user.id})")
    
    # Check for missing users
    missing_users = [u for u in source_users if u.id not in user_mapping]
    if missing_users:
        print("\nWarning: The following users from source org don't have matching users in target org:")
        for user in missing_users:
            print(f"  - {user.get_full_name()} ({user.email}) [ID: {user.id}]")
    
    return user_mapping

def generate_transfer_commands(source_data, source_org, target_org, user_mapping):
    """Generate commands for transferring data"""
    print_header("TRANSFER COMMANDS")
    
    commands = []
    
    # 1. Transfer Source Tags
    commands.append("# 1. Transfer Source Tags")
    for tag in source_data['source_tags']:
        cmd = f"new_tag = SourceTag.objects.create(organization_id={target_org.id}, name='{tag.name}', description='{tag.description}')"
        commands.append(cmd)
    
    # 2. Transfer Engagement Types
    commands.append("\n# 2. Transfer Engagement Types")
    for et in source_data['engagement_types']:
        created_by_id = f"user_mapping.get({et.created_by_id}, None)" if et.created_by_id else "None"
        cmd = (f"EngagementType.objects.create(organization_id={target_org.id}, name='{et.name}', "
               f"description='{et.description or ''}', category='{et.category}', "
               f"default_duration={et.default_duration or 'None'}, is_active={et.is_active}, "
               f"requires_follow_up={et.requires_follow_up}, "
               f"typical_cost={et.typical_cost or 'None'}, "
               f"success_metrics='{et.success_metrics or ''}', "
               f"created_by_id={created_by_id})")
        commands.append(cmd)
    
    # 3. Transfer Marketing Goals
    commands.append("\n# 3. Transfer Marketing Goals")
    for goal in source_data['goals']:
        created_by_id = f"user_mapping.get({goal.created_by_id}, None)" if goal.created_by_id else "None"
        cmd = (f"MarketingGoal.objects.create(organization_id={target_org.id}, name='{goal.name}', "
               f"category='{goal.category}', recurrence='{goal.recurrence}', "
               f"metric_type='{goal.metric_type}', default_target={goal.default_target}, "
               f"description='{goal.description or ''}', is_active={goal.is_active}, "
               f"created_by_id={created_by_id})")
        commands.append(cmd)
    
    # 4. Transfer Partner Contacts (more complex due to relationships)
    commands.append("\n# 4. Transfer Partner Contacts")
    commands.append("# This will be handled in the transfer_data function due to complexity")
    
    return commands

def transfer_data(source_data, source_org, target_org, user_mapping):
    """Perform the actual data transfer"""
    if ANALYZE_ONLY:
        print("Analysis complete. Set ANALYZE_ONLY = False to perform the actual transfer.")
        return
    
    print_header("PERFORMING DATA TRANSFER")
    
    try:
        with transaction.atomic():
            # Transfer logic will go here
            print("Transfer completed successfully!")
    except Exception as e:
        print(f"Error during transfer: {str(e)}")

def main():
    """Main function to analyze and transfer data"""
    print_header("MARKETING DATA TRANSFER ANALYSIS")
    print(f"Source Organization ID: {SOURCE_ORG_ID}")
    print(f"Target Organization ID: {TARGET_ORG_ID}")
    print(f"Mode: {'Analysis Only' if ANALYZE_ONLY else 'Analysis and Transfer'}")
    
    # Step 1: Analyze organizations
    source_org, target_org, source_users, target_users = analyze_organizations()
    
    # Step 2: Analyze marketing data
    source_data = analyze_marketing_data(source_org)
    
    # Step 3: Create transfer plan
    user_mapping = create_transfer_plan(source_data, source_users, target_users)
    
    # Step 4: Generate transfer commands
    commands = generate_transfer_commands(source_data, source_org, target_org, user_mapping)
    print("\nGenerated Commands:")
    for cmd in commands:
        print(cmd)
    
    # Step 5: Perform transfer if not in analyze-only mode
    transfer_data(source_data, source_org, target_org, user_mapping)

if __name__ == "__main__":
    main()
