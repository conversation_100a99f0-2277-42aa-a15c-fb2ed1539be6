#!/usr/bin/env python
import os
import sys
import django
import json
from django.conf import settings

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "alphalaw.settings")
django.setup()

from storage_service.services.factory import StorageServiceFactory
from case_management.models import Case
from organization_management.models import Organization
from django.contrib.auth import get_user_model
from django.db import transaction
from crum import impersonate

User = get_user_model()

def get_onedrive_personal_service(org_id):
    """Get the OneDrive Personal service for the given organization"""
    try:
        # Get organization
        org = Organization.objects.get(id=org_id)
        
        # Get a user from the organization to impersonate
        user = User.objects.filter(organizations=org).first()
        if not user:
            print(f"No users found for organization {org_id}")
            return None
            
        # Impersonate the user to get the correct storage service
        with impersonate(user):
            # Get storage service
            storage_service = StorageServiceFactory.get_storage_service(org.storage_type)
            if not storage_service or not hasattr(storage_service, 'get_folder_contents'):
                print(f"Storage service for organization {org_id} does not support folder operations")
                return None
                
            return storage_service
    except Organization.DoesNotExist:
        print(f"Organization {org_id} not found")
        return None
    except Exception as e:
        print(f"Error getting OneDrive Personal service: {str(e)}")
        return None

def list_folders(storage_service, folder_id="root"):
    """List all folders in the given folder"""
    try:
        items = storage_service.get_folder_contents(folder_id)
        folders = [item for item in items if item.get("type") == "folder"]
        return folders
    except Exception as e:
        print(f"Error listing folders: {str(e)}")
        return []

def find_folder_by_name(storage_service, parent_folder_id, folder_name):
    """Find a folder by name in the given parent folder"""
    folders = list_folders(storage_service, parent_folder_id)
    matching_folders = [folder for folder in folders if folder.get("name") == folder_name]
    return matching_folders

def search_for_folder(storage_service, folder_name):
    """Search for a folder by name across OneDrive"""
    try:
        results = storage_service.search_items(folder_name)
        folders = [item for item in results if item.get("folder")]
        return folders
    except Exception as e:
        print(f"Error searching for folder: {str(e)}")
        return []

def get_case_folder_info(case_id):
    """Get folder information for a case"""
    try:
        case = Case.objects.get(pk=case_id)
        print(f"Case: {case.case_name}")
        print(f"Storage type: {case.storage_type}")
        print(f"Root folder ID: {case.storage_root_folder_id}")
        
        # Get folder mappings
        if hasattr(case, 'storage_metadata'):
            folder_mappings = case.storage_metadata.folder_mappings.all()
            print(f"Folder mappings:")
            for mapping in folder_mappings:
                print(f"  - {mapping.organization_folder.name if mapping.organization_folder else 'Custom'}: {mapping.folder_id}")
        else:
            print("No storage metadata found for case")
            
        return case
    except Case.DoesNotExist:
        print(f"Case {case_id} not found")
        return None
    except Exception as e:
        print(f"Error getting case folder info: {str(e)}")
        return None

def update_case_folder(case_id, new_folder_id):
    """Update the root folder ID for a case"""
    try:
        with transaction.atomic():
            case = Case.objects.get(pk=case_id)
            old_folder_id = case.storage_root_folder_id
            case.storage_root_folder_id = new_folder_id
            case.save(update_fields=["storage_root_folder_id"])
            print(f"Updated case {case_id} root folder from {old_folder_id} to {new_folder_id}")
            return True
    except Exception as e:
        print(f"Error updating case folder: {str(e)}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Query and manage OneDrive folders")
    parser.add_argument("--org", type=int, help="Organization ID")
    parser.add_argument("--case", type=str, help="Case ID")
    parser.add_argument("--list", type=str, help="List folders in the given folder ID")
    parser.add_argument("--find", type=str, help="Find folder by name")
    parser.add_argument("--parent", type=str, help="Parent folder ID for find operation")
    parser.add_argument("--search", type=str, help="Search for folder by name")
    parser.add_argument("--update-case", action="store_true", help="Update case folder ID")
    parser.add_argument("--new-folder-id", type=str, help="New folder ID for case")
    
    args = parser.parse_args()
    
    if not args.org:
        print("Organization ID is required")
        sys.exit(1)
        
    storage_service = get_onedrive_personal_service(args.org)
    if not storage_service:
        sys.exit(1)
        
    if args.case:
        case = get_case_folder_info(args.case)
        if not case:
            sys.exit(1)
            
        if args.update_case and args.new_folder_id:
            if update_case_folder(args.case, args.new_folder_id):
                print("Case folder updated successfully")
            else:
                print("Failed to update case folder")
                
    if args.list:
        folders = list_folders(storage_service, args.list)
        print(f"Found {len(folders)} folders in {args.list}:")
        for folder in folders:
            print(f"  - {folder.get('name')}: {folder.get('id')}")
            
    if args.find and args.parent:
        folders = find_folder_by_name(storage_service, args.parent, args.find)
        print(f"Found {len(folders)} folders named '{args.find}' in {args.parent}:")
        for folder in folders:
            print(f"  - {folder.get('name')}: {folder.get('id')}")
            
    if args.search:
        folders = search_for_folder(storage_service, args.search)
        print(f"Found {len(folders)} folders matching '{args.search}':")
        for folder in folders:
            print(f"  - {folder.get('name')}: {folder.get('id')}")
            parent_ref = folder.get('parentReference', {})
            parent_path = parent_ref.get('path', '')
            print(f"    Path: {parent_path}")
            print(f"    Parent ID: {parent_ref.get('id', '')}")
