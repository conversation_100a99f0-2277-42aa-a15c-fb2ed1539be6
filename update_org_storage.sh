#!/bin/bash
# Script to update an organization's storage type from OneDrive Personal to OneDrive Business

# Check if organization ID is provided
if [ -z "$1" ]; then
    echo "Error: Organization ID is required"
    echo "Usage: ./update_org_storage.sh <organization_id>"
    exit 1
fi

ORG_ID=$1

# Navigate to the Django project directory
cd "$(dirname "$0")"

# Run the Python script
echo "Updating organization $ORG_ID storage type from OneDrive Personal to OneDrive Business..."
python update_org_storage_type.py $ORG_ID

# Check the exit status
if [ $? -eq 0 ]; then
    echo "Storage type updated successfully!"
else
    echo "Failed to update storage type. Check the logs for details."
    exit 1
fi

echo "Done!"
