import os
import sys

import django

# Set up Django environment
sys.path.append("/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

# Import models
from case_management.models import Case
from django.db.models import Count
from users.models import Organization, User

# Print all organizations to debug
print("Available Organizations:")
for org in Organization.objects.all():
    print(f"ID: {org.id}, Name: {org.name}, User Count: {org.users.count()}")
print("-" * 50)

# List all users in the system
print("\nAll Users in the System:")
all_users = User.objects.all().order_by("email")
print(f"Total users in system: {all_users.count()}")

# Print the first 20 users
print("\nFirst 20 users:")
for user in all_users[:20]:
    orgs = user.organizations.all()
    org_names = [f"{org.id}:{org.name}" for org in orgs]
    print(
        f"{user.email} - Role: {user.role}, Last Login: {user.last_login}, Organizations: {', '.join(org_names) if org_names else 'None'}"
    )
print("-" * 50)

# Use all users for analysis
users = all_users

# Print header
print("User Email, Role, Date Joined, Last Login, Cases Created, Notes Created, Tasks Created")
print("-" * 100)

# Analyze each user
for user in users:
    # Count cases created
    case_count = Case.objects.filter(created_by=user).count()

    # Count notes created (if applicable)
    try:
        from case_management.models import Note

        note_count = Note.objects.filter(created_by=user).count()
    except (ImportError, django.db.utils.ProgrammingError):
        note_count = "N/A"

    # Count tasks created (if applicable)
    try:
        from case_management.models import Task

        task_count = Task.objects.filter(created_by=user).count()
    except (ImportError, django.db.utils.ProgrammingError):
        task_count = "N/A"

    # Print user data
    print(f"{user.email}, {user.role}, {user.date_joined}, {user.last_login}, {case_count}, {note_count}, {task_count}")

print("\n\nSummary Statistics:")
print("-" * 30)

# Count users by role
role_counts = users.values("role").annotate(count=Count("id")).order_by("-count")
print("\nUsers by Role:")
for role in role_counts:
    print(f"{role['role']}: {role['count']}")

# Count active vs inactive users
active_count = users.filter(is_active=True).count()
inactive_count = users.filter(is_active=False).count()
print(f"\nActive users: {active_count}")
print(f"Inactive users: {inactive_count}")

# Count users who have created at least one case
users_with_cases = User.objects.filter(organizations__id=11, created_cases__isnull=False).distinct().count()
total_users = users.count()
print(f"\nUsers who have created at least one case: {users_with_cases}")
if total_users > 0:
    print(f"Percentage of users who have created cases: {(users_with_cases / total_users) * 100:.2f}%")
else:
    print("No users found in organization 11")
