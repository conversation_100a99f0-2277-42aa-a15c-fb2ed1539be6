#!/usr/bin/env python
"""
Analysis script for Casepeer photo migration.
This script will analyze the current state and provide a migration plan.
"""

import os
import sys
import django
import zipfile
from pathlib import Path

# Setup Django
sys.path.append('/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_backend_alphalaw.settings')
django.setup()

from case_management.models import Case
from storage_service.models import CaseStorageMetadata

# Configuration
CASEPEER_PHOTOS_PATH = "/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/casepeer_photos/Casepeer Photos/Photo_Data_Brumely"

def analyze_zip_files():
    """Analyze the zip files in the casepeer photos directory"""
    print("=== Analyzing Zip Files ===")
    
    if not os.path.exists(CASEPEER_PHOTOS_PATH):
        print(f"✗ Path does not exist: {CASEPEER_PHOTOS_PATH}")
        return []
    
    zip_files = []
    total_files = 0
    total_size = 0
    
    for filename in os.listdir(CASEPEER_PHOTOS_PATH):
        if filename.endswith('_photos.zip'):
            zip_path = os.path.join(CASEPEER_PHOTOS_PATH, filename)
            
            # Extract case ID from filename
            parts = filename.split('-')
            case_id = f"CASE-{parts[2].split('_')[0]}" if len(parts) >= 3 else "Unknown"
            
            # Get zip file info
            file_size = os.path.getsize(zip_path)
            total_size += file_size
            
            # Count files in zip
            file_count = 0
            try:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    file_count = len([f for f in zip_ref.namelist() if not f.endswith('/')])
                    total_files += file_count
            except Exception as e:
                print(f"  Warning: Could not read {filename}: {str(e)}")
            
            zip_files.append({
                'filename': filename,
                'case_id': case_id,
                'file_count': file_count,
                'size_mb': file_size / (1024 * 1024),
                'full_path': zip_path
            })
    
    # Sort by case ID
    zip_files.sort(key=lambda x: x['case_id'])
    
    print(f"Found {len(zip_files)} zip files:")
    print(f"Total files to migrate: {total_files}")
    print(f"Total size: {total_size / (1024 * 1024):.1f} MB")
    print()
    
    for zf in zip_files:
        print(f"  {zf['filename']}")
        print(f"    Case ID: {zf['case_id']}")
        print(f"    Files: {zf['file_count']}")
        print(f"    Size: {zf['size_mb']:.1f} MB")
        print()
    
    return zip_files

def analyze_existing_cases():
    """Analyze existing cases in organization 1"""
    print("=== Analyzing Existing Cases ===")
    
    cases = Case.objects.filter(organization_id=1)
    print(f"Found {cases.count()} cases in organization 1:")
    
    cases_with_storage = 0
    cases_without_storage = 0
    
    for case in cases[:10]:  # Show first 10
        has_storage = bool(case.storage_root_folder_id)
        storage_status = "✓" if has_storage else "✗"
        
        print(f"  {storage_status} {case.id}: {case.name}")
        if has_storage:
            print(f"      Storage: {case.storage_root_folder_id} ({case.storage_type})")
            cases_with_storage += 1
        else:
            cases_without_storage += 1
    
    if cases.count() > 10:
        print(f"  ... and {cases.count() - 10} more cases")
    
    print(f"\nSummary:")
    print(f"  Cases with storage: {cases_with_storage}")
    print(f"  Cases without storage: {cases_without_storage}")
    print()

def check_case_id_conflicts(zip_files):
    """Check if any of the target case IDs already exist"""
    print("=== Checking Case ID Conflicts ===")
    
    target_case_ids = [zf['case_id'] for zf in zip_files]
    
    conflicts = []
    for case_id in target_case_ids:
        try:
            existing_case = Case.objects.get(id=case_id)
            conflicts.append({
                'case_id': case_id,
                'existing_case': existing_case
            })
        except Case.DoesNotExist:
            pass
    
    if conflicts:
        print(f"Found {len(conflicts)} case ID conflicts:")
        for conflict in conflicts:
            case = conflict['existing_case']
            print(f"  ✗ {conflict['case_id']}: Already exists - {case.name} (Org: {case.organization_id})")
    else:
        print("✓ No case ID conflicts found")
    
    print()
    return conflicts

def generate_migration_plan(zip_files):
    """Generate a migration plan"""
    print("=== Migration Plan ===")
    
    # Get existing cases that can be renamed
    existing_cases = list(Case.objects.filter(organization_id=1)[:len(zip_files)])
    
    if len(existing_cases) < len(zip_files):
        print(f"⚠️  Warning: Need {len(zip_files)} cases but only {len(existing_cases)} available")
        print("   You may need to create additional cases first")
    
    print("Proposed case renaming:")
    for i, zf in enumerate(zip_files):
        if i < len(existing_cases):
            old_case = existing_cases[i]
            print(f"  {old_case.id} -> {zf['case_id']}")
            print(f"    Current: {old_case.name}")
            print(f"    Files to migrate: {zf['file_count']}")
        else:
            print(f"  [NEED NEW CASE] -> {zf['case_id']}")
            print(f"    Files to migrate: {zf['file_count']}")
    
    print("\nMigration steps:")
    print("1. Run rename_cases_and_create_structures.py")
    print("   - Renames existing cases to match zip file case IDs")
    print("   - Creates storage structures for each case")
    print()
    print("2. Run upload_casepeer_photos.py")
    print("   - Creates casepeer_photos folder in each case")
    print("   - Extracts and uploads photos from zip files")
    print()
    print("3. Verify migration")
    print("   - Check that all photos were uploaded correctly")
    print("   - Verify folder structure in OneDrive")

def main():
    """Main analysis function"""
    print("=== Casepeer Migration Analysis ===")
    print()
    
    # Analyze zip files
    zip_files = analyze_zip_files()
    
    if not zip_files:
        print("No zip files found. Exiting.")
        return
    
    # Analyze existing cases
    analyze_existing_cases()
    
    # Check for conflicts
    conflicts = check_case_id_conflicts(zip_files)
    
    # Generate migration plan
    generate_migration_plan(zip_files)
    
    print("\n=== Analysis Complete ===")

if __name__ == "__main__":
    main()
