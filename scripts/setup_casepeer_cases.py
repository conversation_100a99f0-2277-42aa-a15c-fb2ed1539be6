#!/usr/bin/env python
"""
Script to setup cases for Casepeer photo migration.
This script will:
1. Rename existing cases in organization 1 to match the case IDs from zip files
2. Create storage structures for these cases
3. Create a casepeer_photos folder in each case
4. Extract and upload photos from zip files to the appropriate case folders
"""

import os
import sys
import django
import zipfile
import requests
import json
from pathlib import Path

# Setup Django
sys.path.append('/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_backend_alphalaw.settings')
django.setup()

from case_management.models import Case
from storage_service.models import CaseStorageMetadata
from storage_service.utils import get_storage_service
from django.contrib.auth import get_user_model

User = get_user_model()

# Configuration
CASEPEER_PHOTOS_PATH = "/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/casepeer_photos/Casepeer Photos/Photo_Data_Brumely"
API_BASE_URL = "http://127.0.0.1:8080/api/v1"
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rwuo95_nC40qQpaclzN_uw6LGfqwuUyDfLypJfXsxMY"

# Case ID mappings from zip files
ZIP_CASE_IDS = [
    'CASE-CE7010AA',  # 1188414-CASE-CE7010AA_photos.zip (empty)
    'CASE-7A46521E',  # 1189234-CASE-7A46521E_photos.zip
    'CASE-B7FEF775',  # 1189235-CASE-B7FEF775_photos.zip
    'CASE-37A99F0A',  # 1189242-CASE-37A99F0A_photos.zip
    'CASE-B5B9996B',  # 1189634-CASE-B5B9996B_photos.zip
    'CASE-C716B7C4',  # 1189637-CASE-C716B7C4_photos.zip
    'CASE-C95A10C1',  # 1189642-CASE-C95A10C1_photos.zip
]

def get_zip_files():
    """Get list of zip files and their corresponding case IDs"""
    zip_files = []
    for filename in os.listdir(CASEPEER_PHOTOS_PATH):
        if filename.endswith('_photos.zip'):
            # Extract case ID from filename
            # Format: 1188414-CASE-CE7010AA_photos.zip
            parts = filename.split('-')
            if len(parts) >= 3:
                case_id = f"CASE-{parts[2].split('_')[0]}"
                zip_files.append({
                    'filename': filename,
                    'case_id': case_id,
                    'full_path': os.path.join(CASEPEER_PHOTOS_PATH, filename)
                })
    return zip_files

def rename_existing_cases():
    """Rename existing cases in org 1 to match zip file case IDs"""
    print("=== Renaming existing cases ===")
    
    # Get existing cases in org 1
    existing_cases = list(Case.objects.filter(organization_id=1)[:len(ZIP_CASE_IDS)])
    
    if len(existing_cases) < len(ZIP_CASE_IDS):
        print(f"Warning: Only {len(existing_cases)} cases found, but need {len(ZIP_CASE_IDS)}")
        return False
    
    # Rename cases
    for i, new_case_id in enumerate(ZIP_CASE_IDS):
        old_case = existing_cases[i]
        old_id = old_case.id
        
        print(f"Renaming {old_id} -> {new_case_id}")
        
        # Update the case ID directly in the database
        old_case.id = new_case_id
        old_case.save()
        
        print(f"✓ Successfully renamed {old_id} to {new_case_id}")
    
    return True

def create_case_structure_via_api(case_id):
    """Create case structure using the API"""
    print(f"Creating storage structure for {case_id}")
    
    url = f"{API_BASE_URL}/storage/case-structure/"
    headers = {
        'Authorization': f'Bearer {AUTH_TOKEN}',
        'Content-Type': 'application/json'
    }
    data = {
        'case_id': case_id,
        'force_create': True
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            print(f"✓ Storage structure created for {case_id}")
            return response.json()
        else:
            print(f"✗ Failed to create storage structure for {case_id}: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"✗ Error creating storage structure for {case_id}: {str(e)}")
        return None

def create_casepeer_photos_folder(case_id, storage_service, case):
    """Create casepeer_photos folder in the case's root folder"""
    print(f"Creating casepeer_photos folder for {case_id}")
    
    if not case.storage_root_folder_id:
        print(f"✗ No storage root folder ID found for {case_id}")
        return None
    
    try:
        # Create casepeer_photos folder
        folder_result = storage_service.create_folder(
            parent_folder_id=case.storage_root_folder_id,
            folder_name="casepeer_photos"
        )
        print(f"✓ Created casepeer_photos folder for {case_id}: {folder_result.get('id')}")
        return folder_result
    except Exception as e:
        print(f"✗ Error creating casepeer_photos folder for {case_id}: {str(e)}")
        return None

def extract_and_upload_photos(zip_info, casepeer_folder_id, storage_service):
    """Extract photos from zip file and upload to casepeer_photos folder"""
    case_id = zip_info['case_id']
    zip_path = zip_info['full_path']
    
    print(f"Processing photos for {case_id} from {zip_info['filename']}")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            if not file_list:
                print(f"✓ Zip file {zip_info['filename']} is empty, skipping")
                return True
            
            print(f"Found {len(file_list)} files in {zip_info['filename']}")
            
            # Extract and upload each file
            for file_path in file_list:
                if file_path.endswith('/'):  # Skip directories
                    continue
                
                # Get just the filename
                filename = os.path.basename(file_path)
                if not filename:
                    continue
                
                print(f"  Uploading {filename}...")
                
                # Extract file to memory
                file_data = zip_ref.read(file_path)
                
                # Upload to storage service
                try:
                    from io import BytesIO
                    file_obj = BytesIO(file_data)
                    
                    upload_result = storage_service.upload_file(
                        folder_id=casepeer_folder_id,
                        file_name=filename,
                        file_content=file_obj
                    )
                    print(f"    ✓ Uploaded {filename}")
                    
                except Exception as upload_error:
                    print(f"    ✗ Failed to upload {filename}: {str(upload_error)}")
            
            print(f"✓ Completed processing photos for {case_id}")
            return True
            
    except Exception as e:
        print(f"✗ Error processing zip file for {case_id}: {str(e)}")
        return False

def main():
    """Main function to orchestrate the entire process"""
    print("=== Casepeer Photos Migration Script ===")
    
    # Step 1: Get zip files info
    zip_files = get_zip_files()
    print(f"Found {len(zip_files)} zip files:")
    for zf in zip_files:
        print(f"  {zf['filename']} -> {zf['case_id']}")
    
    # Step 2: Rename existing cases
    if not rename_existing_cases():
        print("Failed to rename cases. Exiting.")
        return
    
    # Step 3: Create storage structures and upload photos
    print("\n=== Creating storage structures and uploading photos ===")
    
    # Get storage service for organization 1
    storage_service = get_storage_service(org_id=1)
    
    for zip_info in zip_files:
        case_id = zip_info['case_id']
        print(f"\n--- Processing {case_id} ---")
        
        # Get the case
        try:
            case = Case.objects.get(id=case_id)
        except Case.DoesNotExist:
            print(f"✗ Case {case_id} not found")
            continue
        
        # Create storage structure via API
        structure_result = create_case_structure_via_api(case_id)
        if not structure_result:
            continue
        
        # Refresh case to get updated storage info
        case.refresh_from_db()
        
        # Create casepeer_photos folder
        casepeer_folder = create_casepeer_photos_folder(case_id, storage_service, case)
        if not casepeer_folder:
            continue
        
        # Extract and upload photos
        extract_and_upload_photos(zip_info, casepeer_folder['id'], storage_service)
    
    print("\n=== Migration completed ===")

if __name__ == "__main__":
    main()
