#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to upload Casepeer photos to case storage.
This script will:
1. Check that cases have storage structures
2. Create a casepeer_photos folder in each case
3. Extract and upload photos from zip files to the appropriate case folders
"""

import os
import sys
import zipfile
from io import BytesIO

import django

# Setup Django
sys.path.append("/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from case_management.models import Case
from storage_service.services.factory import StorageServiceFactory, StorageType

# Configuration
CASEPEER_PHOTOS_PATH = "/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/casepeer_photos/Casepeer Photos/Photo_Data_Brumely"

# Case ID mappings from zip files
ZIP_CASE_IDS = [
    "CASE-CE7010AA",  # 1188414-CASE-CE7010AA_photos.zip (empty)
    "CASE-7A46521E",  # 1189234-CASE-7A46521E_photos.zip
    "CASE-B7FEF775",  # 1189235-CASE-B7FEF775_photos.zip
    "CASE-37A99F0A",  # 1189242-CASE-37A99F0A_photos.zip
    "CASE-B5B9996B",  # 1189634-CASE-B5B9996B_photos.zip
    "CASE-C716B7C4",  # 1189637-CASE-C716B7C4_photos.zip
    "CASE-C95A10C1",  # 1189642-CASE-C95A10C1_photos.zip
]


def get_zip_files():
    """Get list of zip files and their corresponding case IDs"""
    zip_files = []
    for filename in os.listdir(CASEPEER_PHOTOS_PATH):
        if filename.endswith("_photos.zip"):
            # Extract case ID from filename
            # Format: 1188414-CASE-CE7010AA_photos.zip
            parts = filename.split("-")
            if len(parts) >= 3:
                case_id = f"CASE-{parts[2].split('_')[0]}"
                zip_files.append(
                    {
                        "filename": filename,
                        "case_id": case_id,
                        "full_path": os.path.join(CASEPEER_PHOTOS_PATH, filename),
                    }
                )
    return zip_files


def check_case_storage(case_id):
    """Check if case has storage structure set up"""
    try:
        case = Case.objects.get(id=case_id)
        if case.storage_root_folder_id:
            print(f"✓ Case {case_id} has storage structure: {case.storage_root_folder_id}")
            return case
        else:
            print(f"✗ Case {case_id} does not have storage structure")
            return None
    except Case.DoesNotExist:
        print(f"✗ Case {case_id} not found")
        return None


def create_casepeer_photos_folder(case_id, storage_service, case):
    """Create casepeer_photos folder in the case's root folder"""
    print(f"Creating casepeer_photos folder for {case_id}")

    try:
        # Check if folder already exists
        contents = storage_service.get_folder_contents(case.storage_root_folder_id)
        for item in contents:
            if item.get("name") == "casepeer_photos" and "folder" in item:
                print(f"✓ casepeer_photos folder already exists for {case_id}: {item.get('id')}")
                return item

        # Create casepeer_photos folder
        folder_result = storage_service.create_folder(
            parent_folder_id=case.storage_root_folder_id, folder_name="casepeer_photos"
        )
        print(f"✓ Created casepeer_photos folder for {case_id}: {folder_result.get('id')}")
        return folder_result
    except Exception as e:
        print(f"✗ Error creating casepeer_photos folder for {case_id}: {str(e)}")
        return None


def get_zip_file_info(zip_path):
    """Get information about files in the zip"""
    try:
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            file_list = [f for f in zip_ref.namelist() if not f.endswith("/")]
            return file_list
    except Exception as e:
        print(f"Error reading zip file {zip_path}: {str(e)}")
        return []


def upload_photos_from_zip(zip_info, casepeer_folder_id, storage_service):
    """Extract photos from zip file and upload to casepeer_photos folder"""
    case_id = zip_info["case_id"]
    zip_path = zip_info["full_path"]

    print(f"Processing photos for {case_id} from {zip_info['filename']}")

    # First, check what's in the zip
    file_list = get_zip_file_info(zip_path)

    if not file_list:
        print(f"✓ Zip file {zip_info['filename']} is empty or has no files, skipping")
        return True

    print(f"Found {len(file_list)} files in {zip_info['filename']}")

    # Show files that will be uploaded
    for file_path in file_list[:5]:  # Show first 5 files
        filename = os.path.basename(file_path)
        print(f"  - {filename}")
    if len(file_list) > 5:
        print(f"  ... and {len(file_list) - 5} more files")

    # Confirm upload
    response = input(f"\nUpload {len(file_list)} files for {case_id}? (y/N): ")
    if response.lower() != "y":
        print("Skipped.")
        return False

    try:
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            uploaded_count = 0
            failed_count = 0

            # Extract and upload each file
            for file_path in file_list:
                # Get just the filename
                filename = os.path.basename(file_path)
                if not filename:
                    continue

                print(f"  Uploading {filename}...", end=" ")

                try:
                    # Extract file to memory
                    file_data = zip_ref.read(file_path)
                    file_obj = BytesIO(file_data)

                    # Check file size (limit to 15MB as per memory)
                    file_size = len(file_data)
                    if file_size > 15 * 1024 * 1024:  # 15MB
                        print(f"SKIPPED (too large: {file_size / (1024 * 1024):.1f}MB)")
                        continue

                    # Upload to storage service
                    upload_result = storage_service.upload_file(
                        folder_id=casepeer_folder_id, file_name=filename, file_content=file_obj
                    )
                    print("✓")
                    uploaded_count += 1

                except Exception as upload_error:
                    print(f"✗ ({str(upload_error)})")
                    failed_count += 1

            print(f"✓ Completed processing photos for {case_id}: {uploaded_count} uploaded, {failed_count} failed")
            return True

    except Exception as e:
        print(f"✗ Error processing zip file for {case_id}: {str(e)}")
        return False


def main():
    """Main function to orchestrate the photo upload process"""
    print("=== Casepeer Photos Upload Script ===")

    # Step 1: Get zip files info
    zip_files = get_zip_files()
    print(f"Found {len(zip_files)} zip files:")
    for zf in zip_files:
        file_count = len(get_zip_file_info(zf["full_path"]))
        print(f"  {zf['filename']} -> {zf['case_id']} ({file_count} files)")

    # Step 2: Get storage service for organization 1
    print("\n=== Setting up storage service ===")
    try:
        # Use S3 storage service for organization 1
        storage_service = StorageServiceFactory.get_storage_service(StorageType.S3, org_id=1)
        print("✓ Storage service initialized")
    except Exception as e:
        print(f"✗ Failed to initialize storage service: {str(e)}")
        return

    # Step 3: Process each case
    print("\n=== Processing cases ===")

    for zip_info in zip_files:
        case_id = zip_info["case_id"]
        print(f"\n--- Processing {case_id} ---")

        # Check if case has storage structure
        case = check_case_storage(case_id)
        if not case:
            continue

        # Create casepeer_photos folder
        casepeer_folder = create_casepeer_photos_folder(case_id, storage_service, case)
        if not casepeer_folder:
            continue

        # Upload photos
        upload_photos_from_zip(zip_info, casepeer_folder["id"], storage_service)

    print("\n=== Photo upload completed ===")


if __name__ == "__main__":
    main()
