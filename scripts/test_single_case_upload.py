#!/usr/bin/env python
"""
Test script to upload photos for a single case (CASE-CE7010AA) to verify the process.
"""

import os
import sys
import zipfile
from io import BytesIO

import django

# Setup Django
sys.path.append("/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "django_backend_alphalaw.settings")
django.setup()

from case_management.models import Case
from storage_service.services.factory import StorageServiceFactory, StorageType

# Configuration
CASEPEER_PHOTOS_PATH = "/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/casepeer_photos/Casepeer Photos/Photo_Data_Brumely"
TEST_CASE_ID = "CASE-C716B7C4"


def test_single_case_upload():
    """Test uploading photos for a single case"""
    print(f"=== Testing Photo Upload for {TEST_CASE_ID} ===")

    # Step 1: Get the case
    try:
        case = Case.objects.get(id=TEST_CASE_ID)
        print(f"✓ Found case: {case.name}")
        print(f"  Storage Root: {case.storage_root_folder_id}")
        print(f"  Storage Type: {case.storage_type}")
    except Case.DoesNotExist:
        print(f"✗ Case {TEST_CASE_ID} not found")
        return

    if not case.storage_root_folder_id:
        print(f"✗ Case {TEST_CASE_ID} does not have storage structure")
        return

    # Step 2: Get storage service
    try:
        storage_service = StorageServiceFactory.get_storage_service(StorageType.S3, org_id=1)
        print("✓ Storage service initialized")
    except Exception as e:
        print(f"✗ Failed to initialize storage service: {str(e)}")
        return

    # Step 3: Check if casepeer_photos folder exists
    print("\n--- Checking casepeer_photos folder ---")
    try:
        contents = storage_service.get_folder_contents(case.storage_root_folder_id)
        casepeer_folder = None

        print("Root folder contents:")
        for item in contents:
            print(f"  - {item.get('name')} (type: {item.get('type', 'file')})")
            if item.get("name") == "casepeer_photos" and "folder" in item.get("type", ""):
                casepeer_folder = item

        if casepeer_folder:
            casepeer_folder_id = casepeer_folder["id"]
            print(f"✓ Found casepeer_photos folder: {casepeer_folder_id}")
        else:
            print("✗ casepeer_photos folder not found, creating it...")
            folder_result = storage_service.create_folder(
                parent_folder_id=case.storage_root_folder_id, folder_name="casepeer_photos"
            )
            casepeer_folder_id = folder_result["id"]
            print(f"✓ Created casepeer_photos folder: {casepeer_folder_id}")

    except Exception as e:
        print(f"✗ Error checking/creating casepeer_photos folder: {str(e)}")
        return

    # Step 4: Check current contents of casepeer_photos folder
    print("\n--- Checking casepeer_photos folder contents ---")
    try:
        casepeer_contents = storage_service.get_folder_contents(casepeer_folder_id)
        print(f"Current files in casepeer_photos folder: {len(casepeer_contents)}")
        for item in casepeer_contents:
            print(f"  - {item.get('name')} ({item.get('size', 0)} bytes)")
    except Exception as e:
        print(f"✗ Error checking casepeer_photos contents: {str(e)}")
        return

    # Step 5: Find and process the zip file
    zip_filename = f"1189637-{TEST_CASE_ID}_photos.zip"
    zip_path = os.path.join(CASEPEER_PHOTOS_PATH, zip_filename)

    print("\n--- Processing zip file ---")
    print(f"Looking for: {zip_path}")

    if not os.path.exists(zip_path):
        print(f"✗ Zip file not found: {zip_path}")
        return

    print(f"✓ Found zip file: {zip_filename}")

    # Check zip contents
    try:
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            file_list = [f for f in zip_ref.namelist() if not f.endswith("/")]
            print(f"Files in zip: {len(file_list)}")

            if not file_list:
                print("✓ Zip file is empty, nothing to upload")
                return

            for file_path in file_list:
                filename = os.path.basename(file_path)
                print(f"  - {filename}")

            # Upload files
            print("\n--- Uploading files ---")
            uploaded_count = 0
            failed_count = 0

            for file_path in file_list:
                filename = os.path.basename(file_path)
                if not filename:
                    continue

                print(f"Uploading {filename}...", end=" ")

                try:
                    # Extract file to memory
                    file_data = zip_ref.read(file_path)
                    file_obj = BytesIO(file_data)

                    # Check file size
                    file_size = len(file_data)
                    if file_size > 15 * 1024 * 1024:  # 15MB
                        print(f"SKIPPED (too large: {file_size / (1024 * 1024):.1f}MB)")
                        continue

                    # Upload to storage service
                    upload_result = storage_service.upload_file(
                        folder_id=casepeer_folder_id, file_name=filename, file_content=file_obj
                    )
                    print("✓")
                    uploaded_count += 1

                except Exception as upload_error:
                    print(f"✗ ({str(upload_error)})")
                    failed_count += 1

            print("\n--- Upload Summary ---")
            print(f"Uploaded: {uploaded_count}")
            print(f"Failed: {failed_count}")

    except Exception as e:
        print(f"✗ Error processing zip file: {str(e)}")
        return

    # Step 6: Verify upload by checking folder contents again
    print("\n--- Verifying upload ---")
    try:
        casepeer_contents_after = storage_service.get_folder_contents(casepeer_folder_id)
        print(f"Files in casepeer_photos folder after upload: {len(casepeer_contents_after)}")
        for item in casepeer_contents_after:
            print(f"  ✓ {item.get('name')} ({item.get('size', 0)} bytes)")
    except Exception as e:
        print(f"✗ Error verifying upload: {str(e)}")

    print(f"\n=== Test completed for {TEST_CASE_ID} ===")


if __name__ == "__main__":
    test_single_case_upload()
