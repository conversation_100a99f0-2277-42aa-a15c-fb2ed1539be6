#!/usr/bin/env python
"""
Simple script to rename cases and create storage structures.
This script will:
1. Rename existing cases in organization 1 to match the case IDs from zip files
2. Create storage structures for these cases using the API
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append('/Users/<USER>/Documents/ai_products/alpha-law/django_backend_alphalaw/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_backend_alphalaw.settings')
django.setup()

from case_management.models import Case

# Configuration
API_BASE_URL = "http://127.0.0.1:8080/api/v1"
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rwuo95_nC40qQpaclzN_uw6LGfqwuUyDfLypJfXsxMY"

# Case ID mappings from zip files
ZIP_CASE_IDS = [
    'CASE-CE7010AA',  # 1188414-CASE-CE7010AA_photos.zip (empty)
    'CASE-7A46521E',  # 1189234-CASE-7A46521E_photos.zip
    'CASE-B7FEF775',  # 1189235-CASE-B7FEF775_photos.zip
    'CASE-37A99F0A',  # 1189242-CASE-37A99F0A_photos.zip
    'CASE-B5B9996B',  # 1189634-CASE-B5B9996B_photos.zip
    'CASE-C716B7C4',  # 1189637-CASE-C716B7C4_photos.zip
    'CASE-C95A10C1',  # 1189642-CASE-C95A10C1_photos.zip
]

def rename_existing_cases():
    """Rename existing cases in org 1 to match zip file case IDs"""
    print("=== Renaming existing cases ===")
    
    # Get existing cases in org 1
    existing_cases = list(Case.objects.filter(organization_id=1)[:len(ZIP_CASE_IDS)])
    
    if len(existing_cases) < len(ZIP_CASE_IDS):
        print(f"Warning: Only {len(existing_cases)} cases found, but need {len(ZIP_CASE_IDS)}")
        return False
    
    # Store old IDs for reference
    old_ids = [case.id for case in existing_cases]
    
    print("Current cases:")
    for case in existing_cases:
        print(f"  {case.id}: {case.name}")
    
    print(f"\nWill rename to: {ZIP_CASE_IDS}")
    
    # Confirm before proceeding
    response = input("\nProceed with renaming? (y/N): ")
    if response.lower() != 'y':
        print("Aborted.")
        return False
    
    # Rename cases
    for i, new_case_id in enumerate(ZIP_CASE_IDS):
        old_case = existing_cases[i]
        old_id = old_case.id
        
        print(f"Renaming {old_id} -> {new_case_id}")
        
        # Update the case ID directly in the database
        old_case.id = new_case_id
        old_case.save()
        
        print(f"✓ Successfully renamed {old_id} to {new_case_id}")
    
    return True

def create_case_structure_via_api(case_id):
    """Create case structure using the API"""
    print(f"Creating storage structure for {case_id}")
    
    url = f"{API_BASE_URL}/storage/case-structure/"
    headers = {
        'Authorization': f'Bearer {AUTH_TOKEN}',
        'Content-Type': 'application/json'
    }
    data = {
        'case_id': case_id,
        'force_create': True
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Storage structure created for {case_id}")
            print(f"  Root folder ID: {result.get('root', {}).get('id', 'N/A')}")
            return result
        else:
            print(f"✗ Failed to create storage structure for {case_id}: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"✗ Error creating storage structure for {case_id}: {str(e)}")
        return None

def main():
    """Main function"""
    print("=== Case Renaming and Structure Creation Script ===")
    
    # Step 1: Rename existing cases
    if not rename_existing_cases():
        print("Failed to rename cases. Exiting.")
        return
    
    # Step 2: Create storage structures
    print("\n=== Creating storage structures ===")
    
    for case_id in ZIP_CASE_IDS:
        print(f"\n--- Processing {case_id} ---")
        
        # Verify case exists
        try:
            case = Case.objects.get(id=case_id)
            print(f"Found case: {case.name}")
        except Case.DoesNotExist:
            print(f"✗ Case {case_id} not found")
            continue
        
        # Create storage structure via API
        structure_result = create_case_structure_via_api(case_id)
        
        if structure_result:
            # Refresh case to see updated storage info
            case.refresh_from_db()
            print(f"  Case storage_root_folder_id: {case.storage_root_folder_id}")
            print(f"  Case storage_type: {case.storage_type}")
    
    print("\n=== Process completed ===")
    print("Next steps:")
    print("1. Verify the storage structures were created correctly")
    print("2. Run the photo upload script to migrate the actual photos")

if __name__ == "__main__":
    main()
